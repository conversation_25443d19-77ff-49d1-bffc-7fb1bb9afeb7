import { endpoint } from './endpoint'
import request from '@/utils/request'

//not in use
export function GetIncidentTopFiveList(param){
    var config = {
      headers: {'Authorization': 'Bearer ' + param['token']},
      data:param.data
    };
    // let res =  request.get(`${endpoint}/incident-handler/123123123`, config)
    let res = {
            "status": "success",
            "data": [
                {
                    "type": "Type 1",
                    "number": "110",
                },
                {
                    "type": "Type 2",
                    "number": "111",
                },
                {
                    "type": "Type 3",
                    "number": "112",
                },
                {
                    "type": "Type 4",
                    "number": "113",
                },
                {
                    "type": "Type 5",
                    "number": "114",
                }
            ]
        }

    return new Promise((resolve, reject) => {
        resolve(res)}
    )
  }

// not in use
  export function GetIncidentTopFiveModel(param){
    var config = {
      headers: {'Authorization': 'Bearer ' + param['token']},
      data:param.data
    };
    // let res =  request.get(`${endpoint}/incident-handler/123123123`, config)
    let res = {
            "status": "success",
            "data": [
                {
                    "type":"others",
                    "number": 13
                },
                {
                    "type": 'SiaB-CVM Reboot',
                    "number": 5,
                },
                {
                    "type": 'SiaB-Host Connection',
                    "number": 6,
                },
                {
                    "type":'SiaB-PE-PC Connection',
                    "number": 7,
                },
                {
                    "type": 'SiaB-Host NIC link down',
                    "number": 8,
                },
                {
                    "type": "Siab-HOSTHOSTHOSTHOSTHOST",
                    "number": 121,
                }
            ]
        }

    return new Promise((resolve, reject) => {
        resolve(res)}
    )
  }


  export function GetIncidentList(param){
    let res =  request.post(`${endpoint}/incident_sheet`, param)
    return res
    // let res = {
    //         "status": "success",
    //         "data": [
    //             {
    //               "assignee": "Ivan Fan",
    //               "creation_time": "2025-03-11 11:49:24",
    //               "duration_time": "12 Hours 33 Minutes",
    //               "inc_num": "INC1004467143",
    //               "index": 1,
    //               "priority": "3 - High",
    //               "resolved_time": "2025-03-12 00:23:02",
    //               "short_description": "RETNL088-NXC000#CVM ************** rebooted",
    //               "site": "RETNL088-NXC000"
    //             },
    //             {
    //               "assignee": "nan",
    //               "creation_time": "2025-03-11 11:49:25",
    //               "duration_time": "6 Hours 5 Minutes",
    //               "inc_num": "INC1004467145",
    //               "index": 2,
    //               "priority": "3 - High",
    //               "resolved_time": "2025-03-11 17:54:49",
    //               "short_description": "[Human-3/D/3/W]-  RETNL088-NXC000#CVM ************** rebooted",
    //               "site": "RETNL088-NXC000"
    //             },
    //             {
    //               "assignee": "Ivan Fan",
    //               "creation_time": "2025-03-11 15:34:42",
    //               "duration_time": "8 Hours 48 Minutes",
    //               "inc_num": "INC1004467870",
    //               "index": 3,
    //               "priority": "3 - High",
    //               "resolved_time": "2025-03-12 00:22:54",
    //               "short_description": "RETCN495-NXC000#CVM ************* rebooted",
    //               "site": "RETCN495-NXC000"
    //             },
    //             {
    //               "assignee": "Ivan Fan",
    //               "creation_time": "2025-03-11 16:49:35",
    //               "duration_time": "7 Hours 33 Minutes",
    //               "inc_num": "INC1004468071",
    //               "index": 4,
    //               "priority": "3 - High",
    //               "resolved_time": "2025-03-12 00:23:31",
    //               "short_description": "RETCN495-NXC000#CVM ************* rebooted",
    //               "site": "RETCN495-NXC000"
    //             },
    //             {
    //               "assignee": "Ivan Fan",
    //               "creation_time": "2025-03-10 03:34:24",
    //               "duration_time": "44 Minutes",
    //               "inc_num": "INC1004462865",
    //               "index": 5,
    //               "priority": "3 - High",
    //               "resolved_time": "2025-03-10 04:18:56",
    //               "short_description": "RETGB461-NXC000#CVM ************* rebooted",
    //               "site": "RETGB461-NXC000"
    //             },
    //             {
    //               "assignee": "Ivan Fan",
    //               "creation_time": "2025-03-10 03:49:20",
    //               "duration_time": "29 Minutes",
    //               "inc_num": "INC1004462884",
    //               "index": 6,
    //               "priority": "3 - High",
    //               "resolved_time": "2025-03-10 04:18:30",
    //               "short_description": "RETGB461-NXC000#CVM ************* rebooted",
    //               "site": "RETGB461-NXC000"
    //             },
    //             {
    //               "assignee": "Ephraim Jiang",
    //               "creation_time": "2025-03-11 01:19:28",
    //               "duration_time": "16 Minutes",
    //               "inc_num": "INC1004465686",
    //               "index": 7,
    //               "priority": "3 - High",
    //               "resolved_time": "2025-03-11 01:36:01",
    //               "short_description": "[Human-3/D/3/W]-  RETFR133-NXC000#CVM ************* rebooted",
    //               "site": "RETFR133-NXC000"
    //             },
    //             {
    //               "assignee": "nan",
    //               "creation_time": "2025-03-11 01:19:29",
    //               "duration_time": "16 Minutes",
    //               "inc_num": "INC1004465687",
    //               "index": 8,
    //               "priority": "3 - High",
    //               "resolved_time": "2025-03-11 01:36:01",
    //               "short_description": "[Human-3/D/3/W]-  RETFR133-NXC000#CVM ************* rebooted",
    //               "site": "RETFR133-NXC000"
    //             }
    //           ]
    //     }

    // return new Promise((resolve, reject) => {
    //     resolve(res)}
    // )
  }

  export function GetIncidentChartData(payload){
    console.log(payload)
    let res =  request.post(`${endpoint}/incident_analysis_custom`, payload)
    return res
    // let res =  {
    //     "date_column": [
    //       {
    //         "index": 1,
    //         "siab": 109,
    //         "sum": 128,
    //         "label": 'week 10',
    //         "wiab": 3,
    //         "wtp": 16
    //       },
    //       {
    //         "index": 2,
    //         "siab": 33,
    //         "sum": 35,
    //         "label": 'week 11',
    //         "wiab": 0,
    //         "wtp": 2
    //       },
    //       {
    //         "index": 3,
    //         "siab": 0,
    //         "sum": 0,
    //         "label": 'week 12',
    //         "wiab": 0,
    //         "wtp": 0
    //       },
    //       {
    //         "index": 4,
    //         "siab": 0,
    //         "sum": 0,
    //         "label": 'week 13',
    //         "wiab": 0,
    //         "wtp": 0
    //       },
    //       {
    //         "index": 5,
    //         "siab": 0,
    //         "sum": 0,
    //         "label": 'week 14',
    //         "wiab": 0,
    //         "wtp": 0
    //       }
    //     ],
    //     "top5_models": [
    //       {
    //         "count": 8,
    //         "model": "SiaB-CVM reboot"
    //       },
    //       {
    //         "count": 10,
    //         "model": "SiaB-Remote site connection"
    //       },
    //       {
    //         "count": 18,
    //         "model": "WTP-Host connection"
    //       },
    //       {
    //         "count": 19,
    //         "model": "SiaB-Cluster services restart"
    //       },
    //       {
    //         "count": 73,
    //         "model": "SiaB-PE-PC Connection"
    //       }
    //     ]
    //   }

    // return new Promise((resolve, reject) => {
    //     resolve(res)}
    // )
  }
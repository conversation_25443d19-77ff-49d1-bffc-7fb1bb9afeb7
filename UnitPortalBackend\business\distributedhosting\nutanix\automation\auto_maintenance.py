
import logging
import re
from datetime import datetime, timezone, timedelta

import sqlalchemy
import werkzeug.exceptions as flaskex

from business.distributedhosting.nutanix.automation.desired_state_config import DesiredStateConfig
from business.distributedhosting.nutanix.automation.password_relate import PasswordRotate
from business.distributedhosting.nutanix.automation.renew_certificates import RenewCertificates
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.distributedhosting.scheduler.atm_scheduler import auto_maintenance_scheduler
from business.distributedhosting.nutanix.task_status import TaskStatus
from models.atm_models import ModelNtxAutomationRotatePasswordTask, ModelNtxAutomationRenewCertificateTask,  ModelNtxAutomationDscTask, \
    ModelNutanixAutoMaintenance, ModelRetailNutanixAutoMaintenanceLock, ModelRetailNutanixAutoMaintenanceLockSchema,  \
    ModelNtxAutomationRotatePasswordTaskLog, ModelNtxAutomationRotatePasswordTaskLogSchema,  \
    ModelNtxAutomationRenewCertificateTaskLog,  ModelNtxAutomationRenewCertificateTaskLogSchema,  \
    ModelNtxAutomationDscTaskLog, ModelNtxAutomationDscTaskLogSchema
from models.ntx_models import ModelPrismElement
from models.ntx_models_wh import ModelWarehousePrismElement
from business.loggings.loggings import DBLogging
import static.SETTINGS as SETTING
from business.generic.commonfunc import terminate_process_by_id
from sqlalchemy import func
from models.database import db
from business.distributedhosting.nutanix.task_status import TaskStatus


class AutoMaintenance:
    KEY_PWD = "rotate_password"
    KEY_CERT = "renew_certificate"
    KEY_DSC = "desired_state_config"
    STEP_ORDER = [KEY_PWD, KEY_CERT, KEY_DSC]
    STEP_CLASS_MAPPING = {
        KEY_PWD: {
            "class"     : PasswordRotate,
            "model"     : ModelNtxAutomationRotatePasswordTask,
            "log"       : ModelNtxAutomationRotatePasswordTaskLog,
            "log_schema": ModelNtxAutomationRotatePasswordTaskLogSchema
        },
        KEY_CERT: {
            "class"     : RenewCertificates,
            "model"     : ModelNtxAutomationRenewCertificateTask,
            "log"       : ModelNtxAutomationRenewCertificateTaskLog,
            "log_schema": ModelNtxAutomationRenewCertificateTaskLogSchema
        },
        KEY_DSC: {
            "class"     : DesiredStateConfig,
            "model"     : ModelNtxAutomationDscTask,
            "log"       : ModelNtxAutomationDscTaskLog,
            "log_schema": ModelNtxAutomationDscTaskLogSchema
        }
    }

    def __init__(self, pe, steps, facility_type):
        logging.getLogger("requests").setLevel(logging.ERROR)
        logging.getLogger("urllib3").setLevel(logging.ERROR)
        self.pe_fqdn = pe
        self.steps = steps
        self.facility_type = facility_type

    def trigger_atm_task(self, init_app=False, use_sa=False):
        if init_app:
            BaseUpTask.init_flask_app()
        for step in self.STEP_ORDER:
            if not self.steps.get(step):
                continue
            pe = self.pe_fqdn
            step_mapping = self.STEP_CLASS_MAPPING[step]
            instance = step_mapping["class"](pe=pe, facility_type=self.facility_type)
            try:
                instance.run(use_sa)
                logging.info(f"'{step}' triggered.")
            except Exception as e:
                logging.error(f"Current step '{step}' failed. Continue to do next step...")
                logging.error(f"Current error: {e}")

    @classmethod
    def list_tasks(cls):
        ret_pe_list = db.session.query(ModelPrismElement).filter(ModelPrismElement.status != 'Decommissioned').all()
        wh_pe_list = db.session.query(ModelWarehousePrismElement).filter(ModelWarehousePrismElement.status != 'Decommissioned').all()
        sys_scheduler = auto_maintenance_scheduler.get_jobs(jobstore='job_store_atm')
        last_jobs = dict()
        result = list()
        for step in cls.STEP_ORDER:
            m = cls.STEP_CLASS_MAPPING[step]["model"]
            last_jobs[step] = db.session.query(m).filter(m.id.in_(
                [row[0] for row in 
                 db.session.query(
                     func.max(m.id)
                     ).group_by(m.pe).all()]
                )).all()#get the latest task of each PE
            # query result is [<ModelRetailNutanixAutomationRenewCertificateTask 2>, <ModelRetailNutanixAutomationRenewCertificateTask 3>...]
        #last_jobs is {"rotate_password":[<task1>,<task2>..],"renew_certificate":[<task1>,<task2>..]...}
        for facility, pe_list in {"retail": ret_pe_list, "warehouse": wh_pe_list}.items():
            for pe in pe_list:
                _r = dict()
                _r["pe"], _r["prism"] = pe.name, pe.prism
                for task_title, tasks in last_jobs.items():
                    matched_task = next(iter(sorted(
                                                ({"status": task.status,
                                                "create_date": task.create_date,
                                                "id": task.id,
                                                "log_path": task.detail_log_path}
                                                for task in tasks if re.match(pe.fqdn, task.pe, re.I)), #task.pe is fqdn
                                                key=lambda e: e["create_date"], 
                                                reverse=True)
                                            ),
                                          {"status": "N/A", "create_date": "N/A", "id": "N/A", "log_path": "N/A"})
                    _r[f"{task_title}_status"] = matched_task.get("status", "N/A")
                    _r[f"{task_title}_lastrun"] = matched_task.get("create_date", "N/A")
                    _r[f"{task_title}_id"] = matched_task.get("id", "N/A")
                    _r[f"{task_title}_logpath"] = matched_task.get("log_path", "N/A")
                    _r["facility_type"] = facility
                #check if it's locked 
                # _r['locked'] = not not _m.lock_id # disable lock for now. 2025-03-18
                for _scheduler in sys_scheduler:
                    if _scheduler.id.lower() == pe.fqdn.lower():
                        _r["next_run_time"] =  datetime.fromtimestamp(_scheduler.next_run_time.timestamp()).isoformat()
                result.append(_r)
        return result
    
    @staticmethod
    def scan_stuck_tasks():
        from sqlalchemy import or_
        for step in AutoMaintenance.STEP_ORDER:
            m = AutoMaintenance.STEP_CLASS_MAPPING[step]["model"]
            tasks = db.session.query(m).filter(or_(m.status == TaskStatus.NOT_STARTED, m.status == TaskStatus.IN_PROGRESS)).all()
            if not tasks:
                continue
            commit = False
            for task in tasks:
                # Get the current UTC time
                time_now = datetime.now(timezone.utc)
                # Convert task.create_date to a datetime object
                try:
                    task_start_time = datetime.strptime(task.create_date, '%Y-%m-%d,%H:%M:%S').replace(tzinfo=timezone.utc)
                except (ValueError, TypeError, AttributeError, OverflowError) as e:
                    logging.error(f"Failed to convert task.create_date to datetime object for task {task.id} in {step} because of: {e}. Setting create_data to current date. Evaluation will happen in next function call.")
                    task.create_date = time_now.strftime('%Y-%m-%d,%H:%M:%S')
                    continue
                time_diff = time_now - task_start_time
                      
                if time_diff >= timedelta(hours=24): # If the task has been running for more than 24 hours
                    commit = True
                    task.status = "Error"
                    logging.info(f"Task {task.id} is stuck for {step}, set status to 'Error'")
            if commit:
                db.session.commit()
        logging.info("Scan stuck tasks finished.")    
    

    @staticmethod
    def add_lock(**kwargs):
        lock = ModelRetailNutanixAutoMaintenanceLock(**kwargs)
        db.session.add(lock)
        db.session.commit()
        ModelNutanixAutoMaintenance.query.filter_by(pe_id=lock.pe_id).one().lock_id = lock.id
        db.session.commit()

    @staticmethod
    def get_lock(pe_id):
        atm = ModelNutanixAutoMaintenance.query.filter_by(pe_id=pe_id).one()
        if atm.lock_id:
            try:
                lock = ModelRetailNutanixAutoMaintenanceLock.query.filter_by(id=atm.lock_id).one()
                lock_schema = ModelRetailNutanixAutoMaintenanceLockSchema()
                return lock_schema.dump(lock)
            except sqlalchemy.exc.NoResultFound:
                raise flaskex.BadRequest(f"Can't find lock id {atm.lock_id} in unit portal database!")
            except sqlalchemy.exc.MultipleResultsFound:
                raise flaskex.BadRequest(f"Found multiple lock with id {atm.lock_id} in unit portal database!")
        else:
            return "Not locked now, please refresh the page"
        
    @staticmethod
    def remove_lock(id):
        atm = ModelNutanixAutoMaintenance.query.filter_by(id=id).one()
        atm.lock_id = 0
        db.session.commit()
        

    @classmethod
    def get_brief_log(cls, atm_type, id):
        if atm_type not in cls.STEP_ORDER:
            raise flaskex.BadRequest(f"We don't have this type of log : '{atm_type}'")
        _logs = cls.STEP_CLASS_MAPPING[atm_type]["log"].query.filter_by(
            task_id=id).order_by(
                cls.STEP_CLASS_MAPPING[atm_type]["log"].id.desc()).all()
        _schema = cls.STEP_CLASS_MAPPING[atm_type]["log_schema"](many=True)
        return _schema.dump(_logs)

    @classmethod
    def get_atm_history_tasks(cls, pe):
        tasks = list()
        _ikea_com = re.compile("\.ikea\.com", re.I)
        pe = _ikea_com.sub("", pe)
        #convert PE.ikea.com to PE, incase that user gives pe.ikea.com and we have pe in db
        for step in cls.STEP_ORDER:
            _m = cls.STEP_CLASS_MAPPING[step]["model"] #model 
            if _tasks := db.session.query(_m).filter(_m.pe.ilike(f"%{pe}%")).all():
                for _t in _tasks:
                    tasks.append({
                        "type": step,
                        "status": _t.status,
                        "create_date": _t.create_date,
                        "log_path": _t.detail_log_path,
                        "creater": _t.creater
                    })
        tasks.sort(key=lambda e: e["create_date"], reverse=True)
        #sort by date
        return {"tasks": tasks}
    

    @classmethod
    def abort_atm_task(cls, task_type, task_id, user):
        lg = DBLogging(logdir=SETTING.DSC_LOG_PATH, taskid=task_id, logtype="DSC", log_model=ModelNtxAutomationDscTaskLog)
        lg.write_log(loginfo= f"{user} is trying to abort the task.")
        _model = cls.STEP_CLASS_MAPPING[task_type]['model']
        _task = _model.query.filter_by(id=task_id).one()
        _pid = _task.pid
        res, mes = terminate_process_by_id(_pid)
        lg.write_log(
            loginfo = "Task has been aborted" if res else f"Task was ended, but an error orcurred when aborting the task {mes}"
        )
        _task.status = "Aborted"
        db.session.commit()
        return True
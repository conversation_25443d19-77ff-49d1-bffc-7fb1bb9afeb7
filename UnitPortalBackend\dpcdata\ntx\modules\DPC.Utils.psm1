function Get-FunctionName(){
    param(
        [int] $StackNumber = 1
    )
    return [string]$(Get-PSCallStack)[$StackNumber].FunctionName
}
function Write-Console-Logs(){
    param(
        [string]                                                 $FunctionName,
        [string]                                                 $Message,
        [string] [ValidateSet("INFO", "DEBUG", "WARN", "ERROR")] $Level,
        [string]                                                 $DumpFile = $Null
    )
    $LogMessage      = "[$Level] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $FunctionName $Message"
    $ForegroundColor = "White"
    Switch($Level){
        "DEBUG"{
            $ForegroundColor = "Blue"
        }
        "WARN"{
            $ForegroundColor = "Yellow"
        }
        "ERROR"{
            $ForegroundColor = "Red"
        }
    }
    Write-Host $LogMessage -ForegroundColor $ForegroundColor
    if ($DumpFile) {
        try {
            Add-Content -Path $DumpFile -Value $LogMessage
        }
        catch {
            Write-Host $_
        }
    }
}
function Load-Vars(){
    # Get base location
    # Read configuration and convert to PS object
    $Vars = $null
    if (Test-Path -Path "$PSScriptRoot\..\vars.json") {
        $Vars = Get-Content -Path "$PSScriptRoot\..\vars.json" | ConvertFrom-Json -Depth 9
    }else {
        Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "It's unable to load vars.json" -DumpFile $Global:DumpFile
    }
    return $Vars
}
function Get-Base64Auth(){
    param(
        [string] $Username,
        [string] $PWord
    )
    return "Basic $([System.Convert]::ToBase64String([System.Text.Encoding]::ASCII.GetBytes("$($Username):$Pword")))"
}
function Create-AesManagedObject(){
    param(
        [string] $Key,
        [string] $IV
    )
    $AesManaged           = New-Object "System.Security.Cryptography.AesManaged"
    $AesManaged.Mode      = [System.Security.Cryptography.CipherMode]::CBC
    $AesManaged.Padding   = [System.Security.Cryptography.PaddingMode]::pkcs7
    $AesManaged.BlockSize = 128
    $AesManaged.KeySize   = 256
    if ($IV) {
        if ($IV.getType().Name -eq "String") {
            $AesManaged.IV = [System.Convert]::FromBase64String($IV)
        }else {
            $AesManaged.IV = $IV
        }
    }
    if ($Key) {
        if ($Key.getType().Name -eq "String") {
            $AesManaged.Key = [System.Convert]::FromBase64String($Key)
        }else {
            $AesManaged.Key = $Key
        }
    }
    return $AesManaged
}
function Decrypt-String(){
    param(
        [string] $Key,
        [string] $IV,
        [string] $Encrypted
    )
    try {
        $Bytes       = [System.Convert]::FromBase64String($Encrypted)
        $AesManaged  = Create-AesManagedObject -Key $Key -IV $IV
        $Decryptor   = $AesManaged.CreateDecryptor()
        $Unencrypted = $Decryptor.TransformFinalBlock($Bytes, 0, $Bytes.Length)
        $Decrypted   = [System.Text.Encoding]::UTF8.GetString($Unencrypted).Trim([char]0)
        $AesManaged.Dispose()
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_
        return $null
    }
    return $Decrypted
}
function Resolve-CountryCode(){
    param (
        [string] $Object
    )
    $Site = $Object.Split("-")[0]
    if ($Site -match "\d{5}") {
        $CountryCode = $Site -replace "[a-zA-Z]([a-zA-Z]{2})\d{5}",'$1'
    }elseif ($Site.Length -eq 6) {
        $CountryCode = $Site.Substring(2,2)
    }elseif ($Site.Length -eq 7) {
        if ($Site.Substring(0,2) -in @("IT","VH","DS","IS")) {
            $CountryCode = $Site.Substring(2,2)
        }else {
            $CountryCode = $Site.Substring(3,2)
        }
    }elseif ($Site.length -eq 8) {
        $CountryCode = $Site.Substring(3,2)
    }else {
        return $null
    }
    return $CountryCode
}
function Resolve-SiteCode(){
    param (
        [string] $Object
    )
    $Site = $Object.Split("-")[0]
    if ($Site -match "\d{5}") {
        $SiteCode = $Site -replace "[a-zA-Z]([a-zA-Z]{2})(\d{5})",'$2'
    }elseif ($Site.Length -eq 6) {
        $SiteCode = $Site.Substring(4,2)
    }elseif ($Site.Length -eq 7) {
        if ($Site.Substring(0,2) -in @("IT","VH","DS","IS")) {
            $SiteCode = $Site.Substring(4,3)
        }else {
            $SiteCode = $Site.Substring(5,2)
        }
    }elseif ($Site.length -eq 8) {
        $SiteCode = $Site.Substring(5,3)
    }else {
        return $null
    }
    return $SiteCode
}
function Resolve-VmCategory(){
    param (
        [string] $Object
    )
    $Category = [PSCustomObject]@{
        'OS'   = "Others"
        'Type' = "Others"
    }
    if ($Object -match "(NTNX-)(.*)(-CVM)") {
        $Category.OS = "AOS"
        $Category.Type = "Nutanix Controller VM"
    }elseif ($Object -match "(.*)(-NXP)(\d{3})(-)(\d{1})") {
        $Category.OS   = "PC AOS"
        $Category.Type = "Prism Central VM"
    }else {
        $SubName = $Object.Split("-")[1]
        if ($SubName.Length -ne 6) {
            return $Category
        }
        if ($SubName.StartsWith("NT")) {
            $Category.OS = "Windows"
            if ($SubName -match "(NT0)(\d{3})") {
                $Category.Type = "FSOL"
            }elseif ($SubName -match "(NT1)(\d{3})") {
                $Category.Type = "Terminal"
            }elseif ($SubName -match "(NT2)(\d{3})") {
                $Category.Type = "Database"
            }elseif ($SubName -match "(NT3)(\d{3})") {
                $Category.Type = "Web"
            }elseif ($SubName -match "(NT4)(\d{3})") {
                $Category.Type = "Application"
            }elseif ($SubName -match "(NT5)(\d{3})") {
                $Category.Type = "Cluster"
            }elseif ($SubName -match "(NT6)(\d{3})") {
                $Category.Type = "Infrastructure"
            }elseif ($SubName -match "(NT7)(\d{3})") {
                $Category.Type = "Hypervisor"
            }elseif ($SubName -match "(NT8)(\d{3})") {
                $Category.Type = "Local Solution"
            }elseif ($SubName -match "(NT9)(\d{3})") {
                $Category.Type = "Development"
            }
        }elseif ($SubName.StartsWith("LX")) {
            $Category.OS = "Linux"
            if ($SubName -match "LX2000") {
                $Category.Type = "MHS DB"
            }elseif ($SubName -match "LX2010") {
                $Category.Type = "PIP DB"
            }elseif ($SubName -match "LX4000") {
                $Category.Type = "PBR"
            }elseif ($SubName -match "LX4010") {
                $Category.Type = "MHS"
            }elseif ($SubName -match "LX4030") {
                $Category.Type = "PIP"
            }elseif ($SubName -match "(LX600)([1-9])") {
                $Category.Type = "NSB"
            }elseif ($SubName -match "(LX40)([4-7])(0)") {
                $Category.Type = "LIP"
            }
        }elseif ($SubName.StartsWith("LC") -or $SubName.StartsWith("PL") -or $SubName.StartsWith("WL")) {
            $Category.OS = "Network Appliance"
            if ($SubName -match "(LC)\d{4}") {
                $Category.Type = "Lancom"
            }elseif ($SubName -match "(PL)\d{4}") {
                $Category.Type = "Palo Alto"
            }elseif ($SubName -match "(WL)\d{4}") {
                $Category.Type = "Wireless Controller"
            }
        }
    }
    return $Category
}
function Convert-NtxCertExpiryDate(){
    param(
        [string] $ExpiryDate
    )
    [array]$DateStrings = $ExpiryDate -split " "
    $Month  = $DateStrings[1]
    $Date   = $DateStrings[2]
    $Time   = $DateStrings[3]
    $Year   = $DateStrings[5]
    $Hour   = ($Time -split ":")[0] 
    $Minute = ($Time -split ":")[1] 
    $Second = ($Time -split ":")[2] 
    $Count  = 0
    $Exit   = 0
    do{
      $Count++
      $MonthCult = (Get-Culture).DateTimeFormat.GetAbbreviatedMonthName($Count)
      if ($MonthCult -eq $Month){
        $exit = 1
      }
    } until ($Exit -eq 1)
    return Get-Date -Month $Count -Day $Date -Year $Year -Hour $Hour -Minute $Minute -Second $Second -Format FileDate -AsUTC
}
Export-ModuleMember *
#cloud-config

# Set hostname and FQDN
hostname: @@{VM_Name}@@
fqdn: @@{VM_Fqdn}@@
manage_etc_hosts: false
ssh_pwauth: true
write_files:
  - path: /etc/sysconfig/network-scripts/ifcfg-ens3
    content: |
      # Static IP Configured
      DEVICE=ens3
      TYPE=Ethernet
      BOOTPROTO=static
      IPADDR=@@{VM_IP}@@
      NETMASK=@@{VM_SubnetMask}@@
      GATEWAY=@@{VM_GW}@@
      ONBOOT=yes
      DOMAIN=@@{VM_DnsDomain}@@
      DNS1=@@{VM_DNS1}@@
      DNS2=@@{VM_DNS2}@@
  - path: /etc/sysconfig/network
    content: |
      NETWORKING=yes
      NETWORKING_IPV6=no

runcmd:
  - ifdown ens3
  - ifup ens3
  - touch /etc/cloud/cloud-init.disabled

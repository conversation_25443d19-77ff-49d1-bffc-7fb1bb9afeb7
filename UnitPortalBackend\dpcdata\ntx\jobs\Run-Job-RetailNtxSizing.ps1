$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Launch-Job(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $T0               = Get-Date -AsUTC -Format FileDate
            $CollectionL      = Select-DhRetailNtxSizing -Vars $Vars
            $CollectionR      = @()
            $CollectionInsert = @()
            $CollectionUpdate = @()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        foreach ($PC in $DhPCs) {
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on $($PC.fqdn)" -DumpFile $DumpFile
            #Get the service account and authentication string for calling PC to get the list of control panels
            $SvcAccount = Select-DhServiceAccount -Vars $Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for $($PC.name)" -DumpFile $DumpFile
                continue
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username `
                                   -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for $($PC.name)" -DumpFile $DumpFile
                continue
            }
            $PrismCall1 = Rest-Prism-v1-List-Cluster -Fqdn $PC.Fqdn -Auth $Auth
            if (!$PrismCall1) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to list PEs" -DumpFile $DumpFile
                continue
            }
            Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "$($PrismCall1.entities.count) PEs profile are available"  -DumpFile $DumpFile
            #List all hosts under the PC
            $PrismCall2 = Rest-Prism-v1-List-Host -Fqdn $PC.Fqdn -Auth $Auth
            if (!$PrismCall2) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to list hosts" -DumpFile $DumpFile
            }else {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "$($Prismcall2.entities.count) Nodes profile are available" -DumpFile $DumpFile
            }
            $PrismCall3 = Rest-Prism-v1-List-Vm -Fqdn $PC.Fqdn -Auth $Auth
            if (!$PrismCall3) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to list VMs" -DumpFile $DumpFile
            }else {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "$($Prismcall3.entities.count) VMs profile are available" -DumpFile $DumpFile
            }
            $CollectionRetry = @()
            $PEs             = $PrismCall1.entities
            foreach ($PE in $PEs) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "Assembling the $($PE.name) sizing data object" -DumpFile $DumpFile
                $DhPE         = $DhPEs | Where-Object{$_.uuid -eq $PE.uuid}
                $PeNodes      = $Prismcall2.entities | Where-Object {$_.clusterUuid -eq $PE.uuid}
                $PeWorkloads  = $Prismcall3.entities | Where-Object {$_.clusterUuid -eq $PE.uuid}
                $UsedCpu      = "" + ($PeWorkloads.numVCpus | Measure-Object -Sum).Sum
                $TotalCpu     = "" + ($PeNodes.numCpuCores | Measure-Object -Sum).Sum
                $UsedMemory   = "" + [int][Math]::Ceiling(($PeWorkloads.memoryCapacityInBytes | Measure-Object -Sum).Sum / [Math]::Pow(1024,3)) + " GiB"
                $TotalMemory  = "" + [int][Math]::Ceiling(($PeNodes.memoryCapacityInBytes | Measure-Object -Sum).Sum / [Math]::Pow(1024,3)) + " GiB"
                $UsedStorage  = "" + [int][Math]::Ceiling([int64]($PE.usageStats.'storage.usage_bytes') / [Math]::Pow(1024,3)) + " GiB"
                $TotalStorage = "" + [int][Math]::Ceiling([int64]($PE.usageStats.'storage.capacity_bytes') / [Math]::Pow(1024,3)) + " GiB"
                $R = [PSCustomObject]@{
                    'pe_uuid'       = $PE.uuid
                    'pe_id'         = $DhPE.id
                    'pe_name'       = $DhPE.name
                    'pc_id'         = $DhPE.pc_id
                    'pc_name'       = $DhPE.prism
                    'date'          = $T0
                    'used_cpu'      = $UsedCpu
                    'total_cpu'     = $TotalCpu
                    'used_memory'   = $UsedMemory
                    'total_memory'  = $TotalMemory
                    'used_storage'  = $UsedStorage
                    'total_storage' = $TotalStorage
                }
                if ("0 GiB" -in @($R.used_storage, $R.total_storage)) {
                    Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "We've found invalid used/total storage '0 GiB' for the PE '$($R.pe_name)', will retry"
                    $CollectionRetry += $R
                    continue
                }
                $CollectionR += $R
            }
            if ($CollectionRetry.Count) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We need to retry for '$($CollectionRetry.Count)' PEs"
                foreach ($Retry in $CollectionRetry) {
                    $RetryPeFqdn = $Retry.pe_name + "." + $PC.domain
                    Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "Retry to fetch data from '$($RetryPeFqdn)'"
                    if ($PrismCall = Rest-Prism-v1-Get-Cluster -Fqdn $RetryPeFqdn -Auth $Auth) {
                        Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We've got data from '$($RetryPeFqdn)'"
                        $Retry.used_storage  = "" + [int][Math]::Ceiling([int64]($PrismCall.usageStats.'storage.usage_bytes') / [Math]::Pow(1024,3)) + " GiB"
                        $Retry.total_storage = "" + [int][Math]::Ceiling([int64]($PrismCall.usageStats.'storage.capacity_bytes') / [Math]::Pow(1024,3)) + " GiB"
                        continue
                    }
                    Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to fetch data from '$($RetryPeFqdn)', need to check the PE '$($Retry.pe_name)'"
                    $Retry.date = "Out-Of-Date"
                }
                $CollectionR += $CollectionRetry
            }
        }
        if (0 -eq $CollectionR.Count) {
            Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "There is no data need to deal with. Exit" -DumpFile $DumpFile
            Exit 0
        }
        #Add new data into the array CollectionInsert and exsiting data into the array CollectionUpdate
        $CollectionR | ForEach-Object {
            if ($_.pe_uuid -notin $CollectionL.pe_uuid) {
                $CollectionInsert += $_
            }else {
                $CollectionUpdate += $_
            }
        }
        #Add data which was exist in the table and needs to be assigned to 'Decommissioned' into the array CollectionUpdate
        $CollectionL | ForEach-Object {
            if ($_.pe_name -notin $CollectionR.pe_name) {
                $L = [PSCustomObject]@{
                    'pe_uuid'       = $_.pe_uuid
                    'pe_id'         = $_.id
                    'pe_name'       = $_.pe_name
                    'pc_id'         = $_.pc_id
                    'pc_name'       = $_.pc_name
                    'date'          = "Out-Of-Date"
                    'used_cpu'      = $_.used_cpu
                    'total_cpu'     = $_.total_cpu
                    'used_memory'   = $_.used_memory
                    'total_memory'  = $_.total_memory
                    'used_storage'  = $_.used_storage
                    'total_storage' = $_.total_storage
                }
                $CollectionUpdate += $L
            }
        }
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_sizing]" -DumpFile $DumpFile
        Insert-Table-DhRetailNtxSizing -Vars $Vars -Collection $CollectionInsert
        Update-Table-DhRetailNtxSizing-ByPeName -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job
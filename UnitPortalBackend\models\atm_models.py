import marshmallow
from marshmallow import post_dump
from sqlalchemy import ForeignKey

from business.distributedhosting.nutanix.base_up_task_property import BaseUpTaskProperty
from models.base_task_models import ModelBaseTaskLog, ModelBaseTask
from models.database import db, ma


class ModelRetailNutanixAutomationSPPLCMTask(db.Model):
    __tablename__      = 'dh_retail_ntx_automation_spp_lcm_task'
    id                 = db.Column(db.Integer , primary_key=True)
    pe                 = db.Column(db.String(100))
    pc                 = db.Column(db.String(100))
    create_date        = db.Column(db.String(100))
    status             = db.Column(db.String(100))
    target_version     = db.Column(db.String(100))
    next_jump_version  = db.Column(db.String(100))
    pid                = db.Column(db.Integer)
    ntx_task_id        = db.Column(db.String(100))
    creater            = db.Column(db.String(100))
    detail_log_path    = db.Column(db.String(255))
    facility_type      = db.Column(db.String(100))
    slcm_plan_id       = db.Column(db.Integer)


class ModelRetailNutanixAutomationSPPLCMTaskSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelRetailNutanixAutomationSPPLCMTask
        load_instance = True


class ModelRetailNutanixAutomationSPPLCMTaskLog(db.Model):
    __tablename__     = 'dh_retail_ntx_automation_spp_lcm_task_log'
    id = db.Column(db.Integer , primary_key=True)
    task_type = db.Column(db.String(50))
    log_date = db.Column(db.String(50))
    severity = db.Column(db.String(50))
    log_info = db.Column(db.String(8000))
    task_id = db.Column(db.Integer)


class ModelRetailNutanixAutomationSPPLCMTaskLogSchema(ma.Schema):
    class Meta:
        fields = ('id', 'task_type', 'log_date', 'severity', 'log_info', 'task_id' )


class ModelRetailNutanixAutomationAOSLCMTask(db.Model):
    __tablename__      = 'dh_retail_ntx_automation_aos_lcm_task'
    id                 = db.Column(db.Integer , primary_key=True)
    pe                 = db.Column(db.String(100))
    pc                 = db.Column(db.String(100))
    facility_type      = db.Column(db.String(100))
    create_date        = db.Column(db.String(100))
    status             = db.Column(db.String(100))
    target_aos_version = db.Column(db.String(100))
    target_ahv_version = db.Column(db.String(100))
    pid                = db.Column(db.Integer)
    ntx_task_id        = db.Column(db.String(100))
    creater            = db.Column(db.String(100))
    detail_log_path      = db.Column(db.String(255))
    slcm_plan_id       = db.Column(db.Integer)

    # logs = relationship("ModelRetailNutanixAutomationAOSLCMTaskLog", back_populates="task")


class ModelRetailNutanixAutomationAOSLCMTaskSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelRetailNutanixAutomationAOSLCMTask
        load_instance = True

    @post_dump
    def uppercase_fqdn(self, data, **kwargs):   # pylint: disable=unused-argument
        if 'pe' in data and isinstance(data['pe'], str):
            data['pe'] = data['pe'].upper()
        return data


class ModelRetailNutanixAutomationAOSLCMTaskLog(db.Model):
    __tablename__     = 'dh_retail_ntx_automation_aos_lcm_task_log'
    id = db.Column(db.Integer, primary_key=True)
    task_type = db.Column(db.String(50))
    log_date = db.Column(db.String(50))
    severity = db.Column(db.String(50))
    log_info = db.Column(db.String(8000))
    task_id = db.Column(db.Integer, ForeignKey('dh_retail_ntx_automation_aos_lcm_task.id'))

    # task = relationship("ModelRetailNutanixAutomationAOSLCMTask", back_populates="logs")


class ModelRetailNutanixAutomationAOSLCMTaskLogSchema(ma.Schema):
    class Meta:
        fields = ('id', 'task_type', 'log_date', 'severity', 'log_info', 'task_id' )


class ModelNtxAutomationRotatePasswordTask(db.Model):
    __tablename__       = 'dh_ntx_automation_rotate_password_task'
    id                  = db.Column(db.Integer, primary_key=True)
    pe                  = db.Column(db.String(50))
    status              = db.Column(db.String(50))
    creater             = db.Column(db.String(50))
    create_date         = db.Column(db.String(50))
    description         = db.Column(db.String(255))
    pid                 = db.Column(db.Integer)
    detail_log_path     = db.Column(db.String(255))
    facility_type       = db.Column(db.String(100))


class ModelNtxAutomationRotatePasswordTaskSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxAutomationRotatePasswordTask
        load_instance = True


class ModelNtxAutomationRotatePasswordTaskLog(db.Model):
    __tablename__       = 'dh_ntx_automation_rotate_password_log'
    id                  = db.Column(db.Integer, primary_key=True)
    task_type           = db.Column(db.String(50))
    logdate             = db.Column(db.String(50))
    severity            = db.Column(db.String(50))
    loginfo             = db.Column(db.String(8000))
    task_id             = db.Column(db.Integer)


class ModelNtxAutomationRotatePasswordTaskLogSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxAutomationRotatePasswordTaskLog
        load_instance = True


class ModelNtxAutomationDscTask(ModelBaseTask):
    __tablename__       = 'dh_ntx_automation_dsc_task'
    facility_type       = db.Column(db.String(100))
    pe                  = db.Column(db.String(100))


class ModelNtxAutomationDscTaskSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxAutomationDscTask
        load_instance = True


class ModelNtxAutomationDscTaskLog(ModelBaseTaskLog):
    __tablename__       = 'dh_ntx_automation_dsc_task_log'
    task_type           = db.Column(db.String(50))


class ModelNtxAutomationDscTaskLogSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxAutomationDscTaskLog
        load_instance = True


class ModelNtxAutomationRenewCertificateTask(db.Model):
    __tablename__   = 'dh_ntx_automation_renew_cert_task'
    id              = db.Column(db.Integer, primary_key=True)
    pid             = db.Column(db.Integer)
    pe              = db.Column(db.String(100))
    creater         = db.Column(db.String(100))
    create_date     = db.Column(db.String(100))
    status          = db.Column(db.String(100))
    detail_log_path = db.Column(db.String(255))
    facility_type   = db.Column(db.String(100))


class ModelNtxAutomationRenewCertificateTaskSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxAutomationRenewCertificateTask
        load_instance = True


class ModelNtxAutomationRenewCertificateTaskLog(ModelBaseTaskLog):
    __tablename__       = 'dh_ntx_automation_renew_cert_task_log'
    task_type           = db.Column(db.String(50))


class ModelNtxAutomationRenewCertificateTaskLogSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxAutomationRenewCertificateTaskLog
        load_instance = True


class ModelNutanixAutoMaintenance(db.Model):
    __tablename__ = 'dh_ntx_auto_maintenance'
    id            = db.Column(db.Integer, primary_key=True)
    pe_id         = db.Column(db.Integer)
    pe_name       = db.Column(db.String(50))
    pc            = db.Column(db.String(50))
    lock_id       = db.Column(db.Integer)
    last_runtime  = db.Column(db.Integer)
    next_runtime  = db.Column(db.Integer)


class ModelNutanixAutoMaintenanceSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNutanixAutoMaintenance
        load_instance = True



class ModelRetailNutanixAutoMaintenanceLock(db.Model): 
    __tablename__ = 'dh_retail_ntx_auto_maintenance_lock'
    id            = db.Column(db.Integer, primary_key=True)
    pe_id         = db.Column(db.Integer)
    start_time    = db.Column(db.Integer)
    end_time      = db.Column(db.Integer)
    creater       = db.Column(db.String(50))
    description   = db.Column(db.String(8000))


class ModelRetailNutanixAutoMaintenanceLockSchema(ma.Schema): 
    class Meta: 
        fields = ('id', 'pe_id', 'start_time', 'end_time', 'creater', 'description')


class ModelNutanixSeamlessLcmPlans(db.Model):
    __tablename__ = 'dh_ntx_seamless_lcm_plans'
    id                    = db.Column(db.Integer, primary_key=True)
    creater               = db.Column(db.String(50))
    create_date           = db.Column(db.String(50))
    daily_report          = db.Column(db.Boolean)
    interval              = db.Column(db.Integer)
    max_count             = db.Column(db.Integer)
    execution_sequence    = db.Column(db.String(50))
    desired_plan_date     = db.Column(db.Date)
    facility_type         = db.Column(db.String(50))
    lcm_type              = db.Column(db.String(50))
    status                = db.Column(db.String(255))


class ModelNutanixSeamlessLcmPlansSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNutanixSeamlessLcmPlans
        load_instance = True


class ModelNutanixSeamlessLcmPlannedPEs(db.Model):
    __tablename__       = 'dh_ntx_seamless_lcm_planned_pes'
    id                  = db.Column(db.Integer, primary_key=True)
    country             = db.Column(db.String(50))
    pc                  = db.Column(db.String(50))
    pe_fqdn             = db.Column(db.String(50))        # fqdn
    planned_date        = db.Column(db.DateTime)
    task_completed_at   = db.Column(db.DateTime)
    status              = db.Column(db.String(50))
    plan_id             = db.Column(db.Integer)
    facility_type       = db.Column(db.String(50))
    lcm_type            = db.Column(db.String(50))

    def __repr__(self):
        return f"<{__class__.__name__}(id={self.id}, country={self.country}, pc={self.pc}, pe_fqdn={self.pe_fqdn})>"


class ModelNutanixSeamlessLcmPlannedPEsSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNutanixSeamlessLcmPlannedPEs
        load_instance = True

    @marshmallow.post_dump
    def convert_pe_fqdn_to_pe(self, data, **kwargs):    # pylint: disable=unused-argument
        """Convert pe_fqdn to pe"""
        if BaseUpTaskProperty.PE_FQDN in data:
            data["pe"] = data.pop(BaseUpTaskProperty.PE_FQDN)
        return data

    # @marshmallow.post_dump
    # def convert_status(self, data, **kwargs):    # pylint: disable=unused-argument
    #     in_progress_statuses = [
    #         LcmTaskStatus.IN_PROGRESS,
    #         LcmTaskStatus.PRE_CHECKING,
    #         LcmTaskStatus.SPP_UPGRADING,
    #         LcmTaskStatus.AOS_UPGRADING,
    #         LcmTaskStatus.AHV_UPGRADING,
    #         LcmTaskStatus.UPGRADING,
    #         LcmTaskStatus.REPAIRING,
    #     ]
    #     if data.get(BaseUpTaskProperty.STATUS) in in_progress_statuses:
    #         data[BaseUpTaskProperty.STATUS] = LcmTaskStatus.IN_PROGRESS
    #     return data

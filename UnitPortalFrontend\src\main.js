import Vue from 'vue'
import { ref } from 'vue'
import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'
import enLang from 'element-ui/lib/locale/lang/en'// 如果使用中文语言包请默认支持，无需额外引入，请删除该依赖

import '@/styles/index.scss' // global css
import '@/styles/bootstrap.css'
import '@/styles/bootstrap.min.css'
import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}

Vue.use(Element, {
  size: Cookies.get('size') || 'medium', // set element-ui default size
  locale: enLang // 如果使用中文，无需设置，请删除
})
  
Vue.directive('collapsible', {
  bind: function (el, binding) {      
     el.transitionDuration = 350;
  },
  update: function (el, binding) {      
     if (binding.oldValue != binding.value){
         if (binding.value) {      
            setTimeout(function () {
                el.classList.remove("collapse");                    
                var height = window.getComputedStyle(el).height;                    
                el.classList.add('collapsing');
                el.offsetHeight;
                el.style.height = height;
                setTimeout(function() {                
                   el.classList.remove("collapsing");
                   el.classList.add('collapse');                   
                   el.style.height = null;                        
                   el.classList.add('show');                    
               }, el.transitionDuration)
            }, 0);
         } 
         else {                
            el.style.height = window.getComputedStyle(el).height;                
            el.classList.remove("collapse");
            el.classList.remove('show');
            el.offsetHeight;
            el.style.height = null;            
            el.classList.add('collapsing');
            setTimeout(function() {                
               el.classList.add('collapse');                
               el.classList.remove("collapsing");                                               
            }, el.transitionDuration)
         }
     }
  }
});
// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false
new Vue({
  el: '#app',
  router,
  store,
  render: q => q(App)
})

function Convert-BytesTo {
    param(
        [long]                               $Bytes = 0,
        [string] [ValidateSet('GiB', 'TiB')] $Unit = "GiB"
    )
    process {
        $GibiBytes = [Math]::Round($Bytes / 1GB, 2)
        $TebiBytes = [Math]::Round($Bytes / 1TB, 2)

        switch ($Unit) {
            'GiB' {
                return $GibiBytes
            }
            'TiB' {
                return $TebiBytes
            }
        }
    }
}
function ConvertTo-StandardPrism {
    <#
        .SYNOPSIS
        This function will transfer the input to standard prism name.

        .DESCRIPTION
        This function will transfer the input to standard prism name.
        Fetching cluster information for D2/DT/PPE cluster and 
        output an object with domain/tier/fqdn information.

        .PARAMETER Name
        Mandatory, only parameter to verify prism name.

        .EXAMPLE
        ConvertTo-StandardPrism -Name dsse998-nxc000
        ------------------Output------------------
        PrismName      Domain     Tier Fqdn
        ---------      ------     ---- ----
        DSSE998-NXC000 ikeadt.com DT   DSSE998-NXC000.ikeadt.com

        .EXAMPLE
        ConvertTo-StandardPrism retcn888
        ------------------Output------------------
        PrismName       Domain   Tier Fqdn
        ---------       ------   ---- ----
        RETCN888-NXC000 ikea.com PPE  RETCN888-NXC000.ikea.com

        .NOTES
        There are RETSEELM-NXC000 in both D2/DT, this one needs to be 
        specified.
    #>
    
    Param(
        [Parameter (Mandatory = $true)] [string] $Name
    )

    Begin {
        # begin code
        $Vars = Read-Var
        $Obj = [PSCustomObject]@{
            "PrismName" = ""
            "Domain"    = ""
            "Tier"      = ""
            "Fqdn"      = ""
        }
        $Name = $Name.Split(".")[0]
    }

    Process {
        # verify if the cluster is D2/DT/PPE cluster.
        
        if ($Name -like "*-nxc*") {
            # It's using Prism name
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "$Name is matching -nxc"
            $Name = $Name.ToUpper()

            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Tring to fetch $Name in D2/DT/PPE clusters"
            if ($Name -like "DS*") {
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "It's a DS cluster, start to fetching DS data"
                $Clusters = $Vars.Infrastructure.Warehouse.Nutanix.PrismElement
                if ($Name -in $Clusters.Cluster) {
                    $Cluster = $Clusters | Where-Object {$_.Cluster -eq $Name}
                    $Domain  = $Cluster.Domain
                    $Tier    = $Cluster.Tier
                    $Fqdn    = $Name + "." + $Domain
                } else {
                    $Domain  = "ikea.com"
                    $Tier    = "PROD"
                    $Fqdn    = $Name + "." + $Domain
                }
            } else {
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "It's a Retail cluster, start to fetching Retail data"
                $Clusters = $Vars.Infrastructure.Retail.Nutanix.PrismElement
                if ($Name -eq "RETSEELM-NXC000") {
                    do {
                        Get-Menu -Title "Tier" -Selections "DT", "D2"
                        $Selection = Read-Host "We have $Name in both DT & D2, please select your tier (Q to quit)"
                        if ($Selection -eq "Q") { return }
                    } while (
                        $Selection -lt 1
                    )

                    switch ($Selection) {
                        1 { 
                            $Cluster = $Clusters | Where-Object {$_.Cluster -eq $Name -and $_.Tier -eq "DT"}
                            $Domain  = $Cluster.Domain
                            $Tier    = $Cluster.Tier
                            $Fqdn    = $Name + "." + $Domain
                         }
                        2 { 
                            $Cluster = $Clusters | Where-Object {$_.Cluster -eq $Name -and $_.Tier -eq "D2"}
                            $Domain  = $Cluster.Domain
                            $Tier    = $Cluster.Tier
                            $Fqdn    = $Name + "." + $Domain
                         }
                    } 
                } elseif ($Name -in $Clusters.Cluster) {
                    $Cluster = $Clusters | Where-Object {$_.Cluster -eq $Name}
                    $Domain  = $Cluster.Domain
                    $Tier    = $Cluster.Tier
                    $Fqdn    = $Name + "." + $Domain
                } else {
                    $Domain  = "ikea.com"
                    $Tier    = "PROD"
                    $Fqdn    = $Name + "." + $Domain
                }
            }
        } elseif ($Name -like "ssp-*") {
            if ($Name -like "*-wiab-*") {
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "It's a DS self service endpoint, start to fetching DS data"
                $Clusters = $Vars.Infrastructure.Warehouse.PrismCentral
            } else {
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "It's a Retail self service endpoint, start to fetching Retail data"
                $Clusters = $Vars.Infrastructure.Retail.PrismCentral
            }
            if ($Name -in $Clusters.Name) {
                $Cluster = $Clusters | Where-Object {$_.Name -eq $Name}
                $Domain  = $Cluster.Domain
                $Tier    = $Cluster.Tier
                $Fqdn    = $Name + "." + $Domain
            } else {
                $Domain  = "ikea.com"
                $Tier    = "PROD"
                $Fqdn    = $Name + "." + $Domain
            }
        } else {
            # It's using site name, transfer from site to Prism
            # By default, using -NXC000
            $Name = $Name.ToUpper() + "-NXC000"
            
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "You're using site name, will transfer to -NXC000 cluster by default"
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Tring to fetch $Name in D2/DT/PPE clusters"
            if ($Name -like "DS*") {
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "It's a DS cluster, start to fetching DS data"
                $Clusters = $Vars.Infrastructure.Warehouse.Nutanix.PrismElement
                if ($Name -in $Clusters.Cluster) {
                    $Cluster = $Clusters | Where-Object {$_.Cluster -eq $Name}
                    $Domain  = $Cluster.Domain
                    $Tier    = $Cluster.Tier
                    $Fqdn    = $Name + "." + $Domain
                } else {
                    $Domain  = "ikea.com"
                    $Tier    = "PROD"
                    $Fqdn    = $Name + "." + $Domain
                }
            } else {
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "It's a Retail cluster, start to fetching Retail data"
                $Clusters = $Vars.Infrastructure.Retail.Nutanix.PrismElement
                if ($Name -eq "RETSEELM-NXC000") {
                    do {
                        Get-Menu -Title "Tier" -Selections "DT", "D2"
                        $Selection = Read-Host "We have $Name in both DT & D2, please select your tier (Q to quit)"
                        if ($Selection -eq "Q") { return }
                    } while (
                        $Selection -lt 1
                    )

                    switch ($Selection) {
                        1 { 
                            $Cluster = $Clusters | Where-Object {$_.Cluster -eq $Name -and $_.Tier -eq "DT"}
                            $Domain  = $Cluster.Domain
                            $Tier    = $Cluster.Tier
                            $Fqdn    = $Name + "." + $Domain
                         }
                        2 { 
                            $Cluster = $Clusters | Where-Object {$_.Cluster -eq $Name -and $_.Tier -eq "D2"}
                            $Domain  = $Cluster.Domain
                            $Tier    = $Cluster.Tier
                            $Fqdn    = $Name + "." + $Domain
                         }
                    } 
                } elseif ($Name -in $Clusters.Cluster) {
                    $Cluster = $Clusters | Where-Object {$_.Cluster -eq $Name}
                    $Domain  = $Cluster.Domain
                    $Tier    = $Cluster.Tier
                    $Fqdn    = $Name + "." + $Domain
                } else {
                    $Domain  = "ikea.com"
                    $Tier    = "PROD"
                    $Fqdn    = $Name + "." + $Domain
                }
            }
        }
        $Obj.PrismName = $Name
        $Obj.Domain    = $Domain
        $Obj.Tier      = $Tier
        $Obj.Fqdn      = $Fqdn
    }

    End {
        # end code
        return $Obj
    }
}
function Find-RetailNtxPc(){
    param(
        [string] $Object
    )
    $PC = [PSCustomObject]@{
        'Region' = $null
        'FQDN'   = $null
    }
    $DC          = ''
    $PCArray     = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral
    if ($Object -match 'CN888' -or $OBject -match 'SE999' -or $Object -match 'SE995') {
        $DC = 'DC7'
    } else {
        $CountryCode = Resolve-CountryCode -Object $Object
        if (($CountryCode -match 'CN')) {
            $DC = 'CN'
        } elseif (($CountryCode -match 'AU') -or ($CountryCode -match 'IN') -or ($CountryCode -match 'JP') -or ($CountryCode -match 'KR') -or`
                 ($CountryCode -match 'SG')) {
            $DC = 'AP'
        } elseif (($CountryCode -match 'CA') -or ($CountryCode -match 'US') -or ($CountryCode -match 'MX')) {
            $DC = 'NA'
        } elseif (($CountryCode -match 'AT') -or ($CountryCode -match 'BE') -or ($CountryCode -match 'CH') -or ($CountryCode -match 'CZ') -or`
                 ($CountryCode -match 'DE') -or ($CountryCode -match 'DK') -or ($CountryCode -match 'ES') -or ($CountryCode -match 'FI') -or`
                 ($CountryCode -match 'FR') -or ($CountryCode -match 'GB') -or ($CountryCode -match 'HR') -or ($CountryCode -match 'HU') -or`
                 ($CountryCode -match 'IT') -or ($CountryCode -match 'NL') -or ($CountryCode -match 'NO') -or ($CountryCode -match 'PL') -or`
                 ($CountryCode -match 'PT') -or ($CountryCode -match 'RO') -or ($CountryCode -match 'RS') -or ($CountryCode -match 'SE') -or`
                 ($CountryCode -match 'SI')) {
                 $DC = 'EU'
        } elseif (($CountryCode -match 'RU')) {
            $DC = 'RU'
        } else {
            return $null
        }
    }
    $PC.Region = $($PCArray | Where-Object {$_.DC -eq $DC}).Region
    $PC.Fqdn   = $($PCArray | Where-Object {$_.DC -eq $DC}).Name +`
                '.' +`
                 $($PCArray | Where-Object {$_.DC -eq $DC}).Domain
    return $PC
}
function Find-RetailVc(){
    param(
        [string] [Parameter(Mandatory = $true)] $Site
    )
    $VC = [PSCustomObject]@{
        'FQDN' = $null
    }
    $Site        = $Site.ToUpper()
    $VCUsername  = $(Read-Var).GstAccount.username
    $VCPassword  = $(Read-Var).GstAccount.password
    $CountryCode = Resolve-CountryCode -Object $Site
    if (!$CountryCode) {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "It's unable to resolve country code from site name"
        return $null
    }
    $VCArray = $(Read-Var).Infrastructure.Retail.SLI.VCenter.($CountryCode)
    if (!$VCArray) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The VC is not found for '$Site'"
        return $null
    }
    foreach ($V in $VCArray) {
        $VCall = Rest-VC-List-Cluster -VCAddress $($V.Name + "." + $V.Domain) -VCUsername $VCUsername -VCPassword $VCPassword -Names $Site
        if ($VCall.value) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The '$Site' is located in VC '$($V.Name)'"
            $VC.FQDN = $($V.Name + "." + $V.Domain)
            return $VC
        }
    }
    return $null
}
function Get-Base64Auth(){
    param(
        [string] $Username,
        [string] $PWord
    )
    #Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're using: $Username"
    return "Basic $([System.Convert]::ToBase64String([System.Text.Encoding]::ASCII.GetBytes("$($Username):$Pword")))"
}
function Get-FunctionName(){
    param(
        [int]$StackNumber = 1
    )
    return [string]$(Get-PSCallStack)[$StackNumber].FunctionName
}
function Get-NetworkRegionalSettings(){
    param(
        [string] [Parameter(Mandatory = $true)] $Object
    )
    $CountryCode = Resolve-CountryCode -Object $Object
    $Settings = [PSCustomObject]@{
        'DC_Pool'       = $null
        'Primary_DNS'   = $null
        'Secondary_DNS' = $null
        'Tertiary_DNS'  = $null
        'Primary_NTP'   = $null
        'Secondary_NTP' = $null
    }
    # IKEAD2
    if ($CountryCode -match 'D2') {
        $Settings.DC_Pool       = 'ikead2.com'
        $Settings.Primary_DNS   = '10.63.241.13'
        $Settings.Secondary_DNS = '10.230.197.9'
        $Settings.Tertiary_DNS  = '0.0.0.0'
        $Settings.Primary_NTP   = 'ntp1-eu.ikea.com'
        $Settings.Secondary_NTP = 'ntp2-eu.ikea.com'
    }
    # DC5 DNS Servers
    if (($CountryCode -match 'AU') -or ($CountryCode -match 'IN') -or ($CountryCode -match 'JP') -or ($CountryCode -match 'KR')) {
        $Settings.DC_Pool       = 'apdc.ikea.com'
        $Settings.Primary_DNS   = '10.34.69.59'
        $Settings.Secondary_DNS = '10.34.69.26'
        $Settings.Tertiary_DNS  = '10.34.69.27'
        $Settings.Primary_NTP   = 'ntp1-ap.ikea.com'
        $Settings.Secondary_NTP = 'ntp2-ap.ikea.com'
    }
    # DC6 DNS Servers
    if (($CountryCode -match 'CA') -or ($CountryCode -match 'US')) {
        $Settings.DC_Pool       = 'nadc.ikea.com'
        $Settings.Primary_DNS   = '10.27.2.69'
        $Settings.Secondary_DNS = '10.27.2.27'
        $Settings.Tertiary_DNS  = '10.27.2.28'
        $Settings.Primary_NTP   = 'ntp1-na.ikea.com'
        $Settings.Secondary_NTP = 'ntp2-na.ikea.com'
    }
    # DC78 DNS Servers
    if (($CountryCode -match 'AT') -or ($CountryCode -match 'BE') -or ($CountryCode -match 'CH') -or ($CountryCode -match 'CZ') -or ($CountryCode -match 'DE') -or ($CountryCode -match 'DK') -or ($CountryCode -match 'ES') -or`
        ($CountryCode -match 'FI') -or ($CountryCode -match 'FR') -or ($CountryCode -match 'GB') -or ($CountryCode -match 'HU') -or ($CountryCode -match 'IE') -or ($CountryCode -match 'IT') -or ($CountryCode -match 'NL') -or`
        ($CountryCode -match 'NO') -or ($CountryCode -match 'PL') -or ($CountryCode -match 'PT') -or ($CountryCode -match 'RU') -or ($CountryCode -match 'SE') -or ($CountryCode -match 'SK') -or ($CountryCode -match 'UA')) {
        $Settings.DC_Pool       = 'eudc.ikea.com'
        $Settings.Primary_DNS   = '10.59.253.2'
        $Settings.Secondary_DNS = '10.59.67.9'
        $Settings.Tertiary_DNS  = '10.59.68.9'
        $Settings.Primary_NTP   = 'ntp1-eu.ikea.com'
        $Settings.Secondary_NTP = 'ntp2-eu.ikea.com'
    }
    # DC9 DNS Servers
    if ($CountryCode -match 'CN') {
        $Settings.DC_Pool       = 'cndc.ikea.com'
        $Settings.Primary_DNS   = '***********'
        $Settings.Secondary_DNS = '************'
        $Settings.Tertiary_DNS  = '************'
        $Settings.Primary_NTP   = 'ntp1-cn.ikea.com'
        $Settings.Secondary_NTP = 'ntp2-ap.ikea.com'
    }

    return $Settings
}
function Get-NtTimeZone(){
    param(
        [string] $Object
    )
    $Object = $Object.Replace("ikea.com", "").Trim(".")
    try{
        # Function is slow due to multiple AD queries.
        $ADComputer = Get-ADComputer $Object
        if ($TimeZoneName = (Get-ADObject -SearchBase $($ADComputer.DistinguishedName.Replace("CN=$($ADComputer.Name),OU=Servers", "CN=Aliases")) `
                                          -Filter 'objectClass -eq "classRegistration"' `
                                          -Properties * | Where-Object -Property 'Name' -eq 'TimeZone').DisplayName) {
            # Timzone alias on a site level. Find site specific time zone first. A country can have many time zones.
            $TimeZone = $TimeZoneName.Split(')')[0].Substring(4)
        } elseif ($TimeZoneName = (Get-ADObject -SearchBase $('CN=Aliases,' + $ADComputer.DistinguishedName.Replace("CN=$($ADComputer.Name),OU=Servers,OU=", '').Substring(3)) `
                                                 -Filter 'objectClass -eq "classRegistration"' `
                                                 -Properties * | Where-Object -Property 'Name' -eq 'TimeZone').DisplayName) {
            # Timzone alias on a country level. Next find country wide time zone of no site time zone was defined.
            $TimeZone = $TimeZoneName.Split(')')[0].Substring(4)
        } elseif ($TimeZoneName = (Get-ADObject -SearchBase $('CN=Aliases,' + ($ADComputer.DistinguishedName.Split(',')[5,6,7] -join ',')) `
                                                -Filter 'objectClass -eq "classRegistration"' `
                                                -Properties * | Where-Object -Property 'Name' -eq 'TimeZone').DisplayName) {
            # Timzone alias on a regional support level. Should not have to look here.
            $TimeZone = $TimeZoneName.Split(')')[0].Substring(4)
        } else {
            # Default to UTC if no alias was found
            $TimeZone = '+00:00'
        }
        # Handle (GMT) from aliases and default value UTC
        if (!$TimeZone -and $TimeZoneName.StartsWith('(GMT) Greenwich')) {
            $TimeZone = '+00:00'
        }
        return $TimeZone
    }
    catch{
        return 0
    }
}
Function Get-Menu {
    <#
        .SYNOPSIS
        This function will create a menu for selections

        .DESCRIPTION
        This function will create a formated menu for selections.
        It can be used for interactive script that user can get a list of selections.

        .PARAMETER Selections
        Accept a list of selections in string.
        E.g.    "Site_Pe_Nutanix",
                "Site_Pe_Admin",
                "Site_Pe_Svc",    
                "Site_Ahv_Nutanix",
                "Site_Ahv_Root",
                "Site_Oob"
        
        .EXAMPLE
        Get-Menu -Title "Vault Options" -Selections "Site_Pe_Nutanix",
                                                    "Site_Pe_Admin",
                                                    "Site_Pe_Svc",    
                                                    "Site_Ahv_Nutanix",
                                                    "Site_Ahv_Root",
                                                    "Site_Oob"

        --------------------- output ---------------------
        ╔═════════════════════════╗
        ║      Vault Options      ║
        ╟─────────────────────────╢
        ║  1. Site_Pe_Nutanix     ║
        ║  2. Site_Pe_Admin       ║
        ║  3. Site_Pe_Svc         ║
        ║  4. Site_Ahv_Nutanix    ║
        ║  5. Site_Ahv_Root       ║
        ║  6. Site_Oob            ║
        ╚═════════════════════════╝
    #>
    param(
        [string]$Title = $null,
        [parameter(Mandatory = $true, ValueFromPipeline = $true)]
        [String[]]$Selections
    )

    $Width = if ($Title) {
        $Length = $Title.Length
        $Length2 = $Selections | ForEach-Object { $_.length } | Sort-Object -Descending | Select-Object -First 1
        $Length2, $Length | Sort-Object -Descending | Select-Object -First 1
    } else {
        $Selections | ForEach-Object { $_.length } | Sort-Object Descending | Select-Object -First 1
    }

    if ($Width -lt 6) { $Width = 6 }
    $Buffer = 2
    $MaxWidth = $Buffer * 3 + $Width + ($Selections.Count).Length
    $Menu = @()

    $Menu += "╔" + "═" * $MaxWidth + "╗"

    if ($Title) {
        $Menu += "║" + " " * [Math]::Floor(($MaxWidth - $Title.Length) / 2) + 
                 $Title + 
                 " " * [Math]::Ceiling(($MaxWidth - $Title.Length) / 2) + "║"
        $Menu += "╟" + "─" * $MaxWidth + "╢"
    }

    For ($i = 1; $i -le $Selections.Count; $i++) {
        $Item = "$i. "
        $Menu += "║" + " " * $Buffer + 
                 $Item + 
                 $Selections[$i - 1] + 
                 " " * ($MaxWidth - $Item.Length - $Selections[$i - 1].Length - $Buffer) + "║"
    }

    $Menu += "╚" + "═" * $MaxWidth + "╝"
    foreach ($Line in $Menu) { Write-Host $Line }
}
function Resolve-CountryCode {
    param (
        [string] $Object
    )
    $Site = $Object.Split("-")[0]
    if ($Site -match "\d{5}") {
        $CountryCode = $Site -replace "[a-zA-Z]([a-zA-Z]{2})\d{5}",'$1'
    }elseif ($Site.Length -eq 6) {
        $CountryCode = $Site.Substring(2,2)
    }elseif ($Site.Length -eq 7) {
        if ($Site.Substring(0,2) -in @("IT","VH","DS","IS")) {
            $CountryCode = $Site.Substring(2,2)
        }else {
            $CountryCode = $Site.Substring(3,2)
        }
    }elseif ($Site.length -eq 8) {
        $CountryCode = $Site.Substring(3,2)
    }else {
        return $null
    }
    return $CountryCode
}
function Resolve-VmCategory(){
    param (
        [string] $Object
    )
    $Category = [PSCustomObject]@{
        'OS'   = "Others"
        'Type' = "Others"
    }
    if ($Object -match "(NTNX-)(.*)(-CVM)") {
        $Category.OS = "AOS"
        $Category.Type = "Nutanix Controller VM"
    }elseif ($Object -match "(.*)(-NXP)(\d{3})(-)(\d{1})") {
        $Category.OS   = "PC AOS"
        $Category.Type = "Prism Central VM"
    }else {
        $SubName = $Object.ToUpper().Split(".")[0]
        $SubName = $SubName.Split("-")[1]
        if ($SubName.Length -ne 6) {
            return $Category
        }
        if ($SubName.StartsWith("NT")) {
            $Category.OS = "Windows"
            if ($SubName -match "(NT0)(\d{3})") {
                $Category.Type = "FSOL"
            }elseif ($SubName -match "(NT1)(\d{3})") {
                $Category.Type = "Terminal"
            }elseif ($SubName -match "(NT2)(\d{3})") {
                $Category.Type = "Database"
            }elseif ($SubName -match "(NT3)(\d{3})") {
                $Category.Type = "Web"
            }elseif ($SubName -match "(NT4)(\d{3})") {
                $Category.Type = "Application"
            }elseif ($SubName -match "(NT5)(\d{3})") {
                $Category.Type = "Cluster"
            }elseif ($SubName -match "(NT6)(\d{3})") {
                $Category.Type = "Infrastructure"
            }elseif ($SubName -match "(NT7)(\d{3})") {
                $Category.Type = "Hypervisor"
            }elseif ($SubName -match "(NT8)(\d{3})") {
                $Category.Type = "Local Solution"
            }elseif ($SubName -match "(NT9)(\d{3})") {
                $Category.Type = "Development"
            }
        }elseif ($SubName.StartsWith("LX")) {
            $Category.OS = "Linux"
            if ($SubName -match "LX2000") {
                $Category.Type = "MHS DB"
            }elseif ($SubName -match "LX2010") {
                $Category.Type = "PIP DB"
            }elseif ($SubName -match "LX4000") {
                $Category.Type = "PBR"
            }elseif ($SubName -match "LX4010") {
                $Category.Type = "MHS"
            }elseif ($SubName -match "LX4030") {
                $Category.Type = "PIP"
            }elseif ($SubName -match "(LX600)([1-9])") {
                $Category.Type = "NSB"
            }elseif ($SubName -match "(LX40)([4-7])(0)") {
                $Category.Type = "LIP"
            }
        }elseif ($SubName.StartsWith("LC") -or $SubName.StartsWith("PL") -or $SubName.StartsWith("WL")) {
            $Category.OS = "Network Appliance"
            if ($SubName -match "(LC)\d{4}") {
                $Category.Type = "Lancom"
            }elseif ($SubName -match "(PL)\d{4}") {
                $Category.Type = "Palo Alto"
            }elseif ($SubName -match "(WL)\d{4}") {
                $Category.Type = "Wireless Controller"
            }
        }
    }
    return $Category
}
function Write-ConsoleLog(){
    [CmdletBinding()]
    param(
        [string][Parameter(Mandatory = $true)]               $Message,
        [string][ValidateSet("INFO", "DEBUG", "WARN", 
                            "ERROR", "CHAPTER", "SXED")]     $Level = "INFO",
        [string]                                             $FunctionName
    )

    $Log = [PSCustomObject]@{
        Time         = Get-Date -f s
        Level        = $Level
        Message      = $Message
        FunctionName = $FunctionName
    }
    
    Switch($Level){
        "INFO" {
            if ($FunctionName) {
                Write-Host "$($Log.Time) [$Level] $FunctionName $Message" -ForegroundColor White
            } else {
                Write-Host "$($Log.Time) [$Level] $Message" -ForegroundColor White
            }
        }
        "WARN" {
            if ($FunctionName) {
                Write-Host "$($Log.Time) [$Level] $FunctionName $Message" -ForegroundColor Yellow
            } else {
                Write-Host "$($Log.Time) [$Level] $Message" -ForegroundColor Yellow
            }
        }
        "ERROR" {
            if ($FunctionName) {
                Write-Host "$($Log.Time) [$Level] $FunctionName $Message" -ForegroundColor Red
            } else {
                Write-Host "$($Log.Time) [$Level] $Message" -ForegroundColor Red
            }
        }
        "CHAPTER" {
            Write-Host "$($Log.Time) [$Level] " -ForegroundColor Cyan
            Write-Host "$($Log.Time) [$Level] ####################################################" -ForegroundColor Cyan
            Write-Host "$($Log.Time) [$Level] #     $Message" -ForegroundColor Cyan
            Write-Host "$($Log.Time) [$Level] ####################################################" -ForegroundColor Cyan
            Write-Host "$($Log.Time) [$Level] " -ForegroundColor Cyan
        }
        "SXED" {
            if ($FunctionName) {
                Write-Host "$($Log.Time) [$Level] $FunctionName $Message" -ForegroundColor Green
            } else {
                Write-Host "$($Log.Time) [$Level] $Message" -ForegroundColor Green
            }
        }
    }

    # Debug message will only show with -Debug function
    if ($Level -eq "DEBUG") {
        if ($FunctionName) {
            Write-Debug "$($Log.Time) $FunctionName $Message"
        } else {
            Write-Debug "$($Log.Time) $Message"
        }
    }
}
function Install-AndImportModule() {
    param (
        [string] $ModuleName
    )
    if (-not (Get-Module -ListAvailable -Name $ModuleName)) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The module $ModuleName does not exist. Installing now..."
        try {
            Install-Module -Name $ModuleName -Repository PSGallery -Scope CurrentUser -Force
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The module $ModuleName installed successfully."
        } catch {
            Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Failed to install $ModuleName. Error: $_"
        }
    }
    try {
        Import-Module $ModuleName
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "$ModuleName imported successfully."
    } catch {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Failed to import $ModuleName. Error: $_"
    }
}
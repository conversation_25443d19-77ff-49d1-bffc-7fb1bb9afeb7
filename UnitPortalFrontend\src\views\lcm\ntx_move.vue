<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="5" >
        <el-col :span="3" >
        <el-dropdown split-button type="primary" @click="handle_move_development">
          Actions
          <el-dropdown-menu slot="dropdown" placement="right">
            <el-dropdown-item @click.native="handle_move_development()">Move Dep</el-dropdown-item>
            <el-dropdown-item @click.native="handle_move_stage1()">Preparation </el-dropdown-item>
            <el-dropdown-item @click.native="handle_move_stage2()">Cutover</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        </el-col>
        <el-col :span="4"  :offset="11" >
          <el-select    size="large"
            v-model="filter.selected_vc" multiple collapse-tags placeholder="Filter the VC" style="width:100%;" >

            <el-option v-for="item in filter.vc_list" :key="item" :label="item.toLowerCase()" :value="item" style="font-size: large;"/>

          </el-select>
        </el-col>
        <el-col :span="4" >
          <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large"  @keyup.enter.native="filter_cluster_list"/>
        </el-col>
        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large"  @click="filter_cluster_list">
            Search
          </el-button>
        </el-col>
    </el-row>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="current_list" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange"  @row-click="handle_row_click">
      <el-table-column label="ID" prop="id" sortable="custom" align="center" min-width="2%" >
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="SLI" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="name">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.name.toUpperCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column label="PE" min-width="8%" align="center" sortable="custom" prop="pe">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.pe.toUpperCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column label="VC" min-width="6%" align="center" sortable="custom" prop="vc_fqdn">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.vc_fqdn}}</span>
        </template>
      </el-table-column>
      <el-table-column label="Move IP" min-width="6%" align="center" sortable="custom" prop="moveip">
        <template slot-scope="{row}">
          <span class="bigger_font"  v-if="row.moveip!=null" ><a :href="'https://' + row.moveip" target="_blank" >{{ row.moveip|moveip_filter }}</a></span><span class="bigger_font" v-else>{{ row.moveip|moveip_filter}}</span>
        </template>
      </el-table-column>

      <el-table-column label="Latest log" min-width="13%" align="center" sortable="custom" prop="logs">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.logs|log_filter }}</span><span class="link-type" v-if="row.logs!=null"  @click="show_brief_log(row)">  More</span>
        </template>
      </el-table-column>

      <el-table-column label="MoveStatus" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="movedevop" >
        <template slot-scope="{row}">
          <el-tag :type="row.movedevop | status_filter">
            {{ row.movedevop }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="Stage1_Pre" min-width="4%" align="center" sortable="custom" prop="stagepreparation" show-overflow-tooltip>
        <template slot-scope="{row}">
          <el-tag :type="row.stagepreparation | status_filter">
            {{ row.stagepreparation }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="Stage2_CutOver" min-width="5%" align="center" sortable="custom" prop="stagecutover">
        <template slot-scope="{row}">
          <el-tag :type="row.stagecutover | status_filter">
            {{ row.stagecutover }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Operations"  min-width="5%" align="center" sortable="custom">
        <template slot-scope="{row}">
          <el-dropdown split-button type="primary">
            Actions
            <el-dropdown-menu slot="dropdown" placement="right">
              <el-dropdown-item @click.native="handle_move_development(row)">Move Dep</el-dropdown-item>
              <el-dropdown-item @click.native="handle_move_stage1(row)">Preparation </el-dropdown-item>
              <el-dropdown-item @click.native="handle_move_stage2(row)">Cutover</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" /> 
      <el-dialog :visible.sync="dialogPvVisible" :title="'MOVE Log(brief)'" :close-on-click-modal="false">

        <el-table :data="logdata" border fit highlight-current-row style="width: 100%" max-height="500" >
          <el-table-column prop="key" label="log date"  min-width="25%" >
            <template slot-scope="{row}">
              <span>{{ row.logdate }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="pv" label="log info" min-width="55%"  >
            <template slot-scope="{row}">
              <span>{{ row.loginfo }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="pv" label="log severity"  min-width="10%"  >
            <template slot-scope="{row}">
              <span>{{ row.severity }}</span>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button type="warning" @click="download_log_file()">Download Detail Log</el-button>
          <el-button type="primary" @click="dialogPvVisible = false">OK</el-button>
          
        </span>
      </el-dialog>
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="mdev_dialogFormVisible" width="30%" @close="handledialogClose" :close-on-click-modal="false">
      <el-form ref="dataForm"  :model="temp" label-position="left" label-width="auto" style="width: 600px; margin-left:50px;padding-top: 2%;display: inline;">
        <el-form-item label="SLI Cluster" prop="sli_cluster" >
          <el-input v-model="selectedrow.name" readonly style="width:auto;margin: auto;"/>
        </el-form-item>
        <el-form-item label="NTX Cluster" prop="ntx_cluster" >
          <el-input v-model="selectedrow.pe" readonly style="width:auto;margin: auto"/>
        </el-form-item>
      </el-form>
          <span>Notes: <p style="font-size: 12px">MOVE VM builing; PowerOn; Change the PW.</p></span>
      <div slot="footer" class="dialog-footer">
        <el-button @click="mdev_dialogFormVisible = false">
          Cancel
        </el-button>
        <el-button type="primary" @click="new_movevm()">
          Confirm
        </el-button>
      </div>
    </el-dialog>
    <el-dialog :title="textMap[dialogStatus]" v-if="stage1_dialogFormVisible" :visible.sync="stage1_dialogFormVisible" @close="handledialogClose" :close-on-click-modal="false" width="40%" style="display:flexbox">
      <el-form ref="plandataForm" :model="temp" label-position="left" label-width="auto" style="width: 660px; margin-left:20px;padding-top: 2%;display: inline;">
        <el-row :gutter="2">
          <el-col :span="12">
            <el-form-item label="SLI Cluster" prop="sli_cluster" >
              <el-input v-model="selectedrow.name" readonly style="width:auto;margin: auto;"/>
            </el-form-item>
          </el-col>
          <el-col :span="12" >
            <el-form-item label="NTX Cluster" prop="ntx_cluster" >
              <el-input v-model="selectedrow.pe" readonly style="width:auto;margin: auto"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="2">
          <el-col :span="12">
            <el-form-item label="Username" prop="gst_username" >
              <el-input v-model="this.$store.getters.name" placeholder="Please input move username" style="width:auto;margin: auto"/>
            </el-form-item>
          </el-col>
          <el-col :span="12" >
            <el-form-item label="Password" prop="gst_password">
              <el-input v-model="temp.gst_password" type="password"  placeholder="Please input move password" style="width:auto;margin: auto"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="VM List :"  style="width:50%" > 
        </el-form-item>
          <el-transfer
            ref="plantransfer"
            filterable
            :right-default-checked="orgtransferdata"
            :titles="['Unselected', 'Selected']"
            filter-placeholder="Fuzzy search"
            v-model="lockrole"
            @change="handleChange"
            :data="transferdata">
          </el-transfer>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="stage1_dialogFormVisible = false">
          Cancel
        </el-button>
        <el-button type="primary" @click="create_migration_plan()">
          Confirm
        </el-button>
      </div>
    </el-dialog>
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="stage2_dialogFormVisible" width="30%" @close="handledialogClose" :close-on-click-modal="false">
      <el-form ref="dataForm"  :model="temp" label-position="left" label-width="auto" style="width: 600px; margin-left:50px;padding-top: 2%;display: inline;">
        <el-form-item label="SLI Cluster" prop="sli_cluster" >
          <el-input v-model="selectedrow.name" readonly style="width:auto;margin: auto;"/>
        </el-form-item>
        <el-form-item label="NTX Cluster" prop="ntx_cluster" >
          <el-input v-model="selectedrow.pe" readonly style="width:auto;margin: auto"/>
        </el-form-item>
      </el-form>
          <span>Notes: <p style="font-size: 12px"> Migrate VMs from SLI to NTX; Power off and Rename the old VM; Disconnect the NIC; Add VM to PD.</p></span>
      <div slot="footer" class="dialog-footer">
        <el-button @click="stage2_dialogFormVisible = false">
          Cancel
        </el-button>
        <el-button type="primary" @click="start_cutover()">
          Confirm
        </el-button>
      </div>
    </el-dialog>



  </div>
</template>

<script>
import { DownloadMoveDetailLog, Get_Clusterlist_Move, Get_SLI_NTVMs, Create_migration_plan, New_Move_VM, Start_Cutover } from '@/api/automation'
import { GetVCClusterCorrespondence} from '@/api/simplivity'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import { string } from 'clipboard'

export default {
  name: 'MOVETable',
  components: { Pagination },
  directives: { waves },
  filters: {
    move_status_filter(movedev){
      if(movedev=='Error'){
        return 'danger'
      }
      if(movedev=='Done'){
        return 'success'
      }
      if(movedev=='In Progress'){
        return 'warning'
      }
    },
    log_filter(log){
      if(log==null){
        return "No log yet"
      }
      else{
        return log[0]['loginfo']
      }
    },
    moveip_filter(moveip){
      if(moveip==null){
        return "No IP yet"
      }
      else {
        return moveip
      }
    },
    status_filter(status) {
      if(status==null){
        return 'primary'
      }else{
        if(status=='Done'){
          return 'success'
        }
        else if(status=='Error'){
          return 'danger'
        }
        else if (status=='In Progress'){
          return 'warning'
        }
        else {
          return status
        }
      }
    }
  },
  data() {
    const validateTime =(rule, value, callback)=>{
      if(this.temp.datatimepickerdisabled){
        callback()
      }
      let currentdate = new Date()
      let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
      if (value < utctime){
        callback(new Error('Schedule date must be later then now.'))
      }else{
        let currnettime = utctime.getTime()
        let scheduletime = value.getTime()
        let timediff = scheduletime-currnettime
        if(timediff/1000/60 < 5){
          callback(new Error('Schedule date is too close from now.'))
        }else{
          callback()
        }
      }
      callback()
    }
    return {
      tableKey: 0,
      all_sliclu_list: null,
      filtered_list: null,
      current_list: null,
      page_list: null,
      sli_ntvms_list:null,
      textMap: {
        plan: 'Stage1 - New Migration Plan ',
        move: 'NEW MOVE',
        cutover:'Stage2 - Cut Over'
      },
      filter:{
        vc_list:[],
        selected_vc:[],
        fuzzy_string:"",
      },
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: '',
        status: '',
        sort: '+id'
      },
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      // statusOptions: ['published', 'draft', 'deleted'],
      ShowCreationDate: false,
      temp: {
        id: '',
        timestamp: new Date(),
        cluster:'',
        status: '',
        startnow: 1 ,
        datatimepickerdisabled:false,
        description: '',
        gst_username: this.$store.getters.name,
        gst_password: ''
      },
      selectedrow:'',
      dialogFormVisible: false,
      mdev_dialogFormVisible: false,
      stage1_dialogFormVisible: false,
      stage2_dialogFormVisible: false,
      dialogStatus: '',
      dialogPvVisible: false,
      logdata: [],
      // rules: {
      //   sli_cluster: [{ required: true, message: 'sli_cluster is required', trigger: 'change' }],
      //   ntx_cluster: [{ required: true, message: 'ntx cluster is required', trigger: 'change' }],
      //   gst_password: [{ required: true, message: 'move_pw is required', trigger: 'change' }],
      //   gst_username: [{ required: true, message: 'move_user is required', trigger: 'change' }],
      //   timestamp: [{ type: 'date', required: true , trigger: 'change' , validator:validateTime}]
      // },
      transferdata: null,
      orgtransferdata: null,
      lockrole: [],
      vmdialog_right :[],
      vmdialog_left :[],
      selectedvm_list: [],
      intervaljob:''
    }
  },
  computed: {
    total() {
      if(this.filtered_list){
        return this.filtered_list.length
      }
      else{
          return 0
      }
    }
  },
  created() {
    this.get_filter_data()
    this.get_clusters_list()
    this.intervaljob = setInterval(()=>{
      this.get_clusters_list()
    },80000)
  },
  methods: {
    get_filter_data(){
      GetVCClusterCorrespondence(this.$store.getters.token).then(
        response =>{
          let vcoptions = response['data']
          this.VCTypeKeyValue = vcoptions.reduce((acc, cur) => {
          acc[cur.vc] = cur.cluster.sort()
          return acc
        }, {})
        this.filter.vc_list = Object.keys(this.VCTypeKeyValue)
        }
      )
    },
    get_clusters_list() {
      this.listLoading = true
      Get_Clusterlist_Move(this.$store.getters.token).then(response => {
        this.all_sliclu_list = response.data
        this.filtered_list = this.all_sliclu_list
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)
        this.listLoading = false
        let all_vc_list = this.all_sliclu_list.map((obj, index) => { return obj['vc_fqdn'] })
        this.filter.vc_list = (this.remove_duplicate(all_vc_list)).sort()
        this.filter_cluster_list()
      })
      
    },
    handle_move_development() {
      this.dialogStatus = 'move'
      this.mdev_dialogFormVisible = true
    },
    handle_move_stage1(row) {
      this.transferdata = []
      this.vmdialog_right = []
      this.lockrole = []
      this.dialogStatus = 'plan'
      this.stage1_dialogFormVisible = true
      this.get_sli_ntvms(row)
      this.$nextTick(() => {
            this.$refs['plandataForm'].clearValidate()
        })
    },
    handle_move_stage2() {
      this.dialogStatus = 'cutover'
      this.stage2_dialogFormVisible = true
      // this.$alert("No Hurry, Cutover part is not ready! ", "Alert", {
      //     confirmButtonText: "Confirm",
      //   });
      // this.dialogStatus = 'cutover'
      // this.stage2_dialogFormVisible = true
    },
    handleChange(value, direction, movedKeys) {
      // let vm_list = []
      let id
      if (direction === "right"){
        for (let indid in movedKeys) {
          this.vmdialog_left.splice(this.vmdialog_left.indexOf(String(movedKeys[indid]-1)), 1)
          this.vmdialog_right.push(this.sli_ntvms_list[movedKeys[indid] - 1])
        }
      };
      if (direction === "left"){
        for(let indid in movedKeys){
          this.vmdialog_right.splice(this.vmdialog_right.indexOf(movedKeys[indid]),1)
          this.vmdialog_left.push(this.sli_ntvms_list[movedKeys[indid]-1])
        }
      };
    },
    handledialogClose() {
      this.vmdialog_right=null,
      this.temp.gst_password = '',
      this.transferdata = []
    },
    handledialogbeforeClose(done) {
      this.$confirm('Are you sure to close ?')
        .then(_ => {
          done();
          // this.get_clusters_list()
        })
        .catch(_ => {});
    },
    handle_row_click(row,column,event){
      this.selectedrow = row
    },
    remove_duplicate(arr) {
      const newArr = []
      arr.forEach(item => {
        if (!newArr.includes(item)) {
          newArr.push(item)
        }
      })
      return newArr
    },
    set_page(){
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start, end)
    },
    filter_cluster_list(){
      //screen the table as per filters
      this.listQuery.page = 1
      let temp_list
      if (this.filter.selected_vc.length){
        //No filter, so select all
        temp_list = this.all_sliclu_list.filter((item)=>{
          return this.filter.selected_vc.includes(item['vc_fqdn'].toLowerCase())
        })
        this.filtered_list = temp_list
      }
      else{
        this.filtered_list = this.all_sliclu_list
      }
      if(this.filter.fuzzy_string.trim().length){
        let temp_list = this.filtered_list
        let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
        //remove space, and split into array by space 
        for(let fuzzy of fuzzy_list){
              fuzzy = fuzzy.toString().toLowerCase()
              temp_list = temp_list.filter((k)=>{
              if( k.vc_fqdn.toString().toLowerCase().search(fuzzy)!= -1
                  || k.name.toLowerCase().search(fuzzy) != -1
              ){
                return true
              }
            })
            }
        this.filtered_list = temp_list
      }
      this.set_page()
    },
    sortChange(data) {
      const { prop, order } = data
      if(order==null){
        this.sortChange({prop:'id',order:'ascending'})
        return 
      }
      let flag_num = order=="ascending" ? 1 : -1
      this.filtered_list.sort((item1,item2)=>{
        let prop1 = item1[prop]?item1[prop]:''
        let prop2 = item2[prop]?item2[prop]:''        
        return (prop1 > prop2) ? flag_num*1 : ((prop1 < prop2) ? flag_num*-1 : 0)
    })
      this.set_page()
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    get_sli_ntvms(row){
        //get the role list
      this.listLoading = true,
      this.temp.gst_password = ''
      
      
      // this.$refs.plantransfer.$refs.leftPanel.checked = [],
      // this.$refs.plantransfer.$refs.rightPanel.checked = []
      // this.transferdata = null
      let payload = {
        data:{        
          "vc": row.vc_fqdn,
          "sli_cluster": row.name
        },
        token: this.$store.getters.token
      }
          Get_SLI_NTVMs(payload).then(response => {
            this.sli_ntvms_list = response.data
            const data = []
            let i=1
            for (let id in this.sli_ntvms_list) {
              data.push({
                key: i++,
                label: this.sli_ntvms_list[id]
              });
              this.vmdialog_left.push(id);
            };
            this.transferdata = data
            // this.lockrole = []
            this.orgtransferdata = data
            this.dialogStatus = 'plan'
            this.stage1_dialogFormVisible = true
            this.listLoading = false
        //     this.$nextTick(() => {
        //     this.$refs['userdataForm'].clearValidate()
        // })
            // return data;
          })
    },
    new_movevm() {
      //get the role list
      this.mdev_dialogFormVisible = false
      // this.listLoading = true
      // let vm_list = []
      // for (vmid in this.vmdialog_right) {
      //   vm_list.push(this.vmdialog_right[vmid])
      // }
      let payload = {
        data: {
          "ntx_cluster": this.selectedrow.pe,
          "sli_cluster": this.selectedrow.name,
          "tasktype": "new_move"
        },
        token: this.$store.getters.token
      }
      New_Move_VM(payload).then(response => {
        if (response.data.result) {
          this.$notify({
            title: 'Success',
            message: `Successed to create New Move VM task.`,
            type: 'success',
            duration: 2000
          })
        }
        else{
          this.$notify({
              title: 'OOOOOps....',
              message: response.data.message,
              type: 'error',
              duration: 10000
          })
        }
          this.mdev_dialogFormVisible = false
        })
        .catch((error) => {
          this.mdev_dialogFormVisible = false
          this.$notify({
            title: 'Error',
            message: error.response.data.message,
            type: 'error',
            duration: 2000
          })
        })
      },
    create_migration_plan() {
      //get the role list
      this.stage1_dialogFormVisible = false
      let payload = {
        data: {
          "ntx_cluster": this.selectedrow.pe,
          "sli_cluster": this.selectedrow.name,
          "vc": this.selectedrow.vc_fqdn,
          "gst_username": this.temp.gst_username,
          "gst_password": this.temp.gst_password,
          "vm_list": this.vmdialog_right,
          "tasktype": "preparation"
        },
        token: this.$store.getters.token
      }
      if (this.vmdialog_right == null || this.vmdialog_right.length == 0) {
        this.$alert("Please Select the VMs that need to move ! ", "Alert", {
          confirmButtonText: "Confirm",
        });
      } else if (this.temp.gst_password == null || this.temp.gst_password.length == 0) {
        this.$alert(" GST Account missing, Please don't get distracted ! ", "Alert", {
          confirmButtonText: "Confirm",
        });
      }
      else {
        Create_migration_plan(payload)
          .then(() => {
            this.$notify({
              title: 'Success',
              message: `Successed to create the migration plan.`,
              type: 'success',
              duration: 2000
            })
            this.stage1_dialogFormVisible = false
          })
          .catch((error) => {
            this.stage1_dialogFormVisible = false
            this.$notify({
              title: 'Error',
              message: error.response.data.message,
              type: 'error',
              duration: 2000
            })
          })
      }
    },
    start_cutover() {
      //get the role list
      this.stage1_dialogFormVisible = false
      let payload = {
        data: {
          "ntx_cluster": this.selectedrow.pe,
          "sli_cluster": this.selectedrow.name,
          "vc": this.selectedrow.vc_fqdn,
          "tasktype": "cutover"
        },
        token: this.$store.getters.token
      }
      // if (this.vmdialog_right == null || this.vmdialog_right.length == 0) {
      //   this.$alert("Please Select the VMs that need to move ! ", "Alert", {
      //     confirmButtonText: "Confirm",
      //   });
      // } else if (this.temp.gst_password == null || this.temp.gst_password.length == 0) {
      //   this.$alert(" GST Account missing, Please don't get distracted ! ", "Alert", {
      //     confirmButtonText: "Confirm",
      //   });
      // }
      // else {
      Start_Cutover(payload)
        .then(() => {
          this.$notify({
            title: 'Success',
            message: `Successed to start cutover task.`,
            type: 'success',
            duration: 2000
          })
          this.stage2_dialogFormVisible = false
        })
        .catch((error) => {
          this.stage2_dialogFormVisible = false
          this.$notify({
            title: 'Error',
            message: error.response.data.message,
            type: 'error',
            duration: 2000
          })
        })
      // }
    },
    show_brief_log(row){
      this.selectedrow = row
      this.logdata = row['logs']
      this.dialogPvVisible = true
    },
    download_log_file(){
      let payload = {
        data:{  id:this.selectedrow.latest_taskid,
                filepath:this.selectedrow.detail_log_path},
        token: this.$store.getters.token
      }
      DownloadMoveDetailLog(payload)
      .then((response)=>{
        const href = URL.createObjectURL(response.data);
        // create "a" HTML element with href to file & click
        const link = document.createElement('a');
        link.href = href;
        link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
        document.body.appendChild(link);
        link.click();
        // clean up "a" element & remove ObjectURL
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      })
    }
  },
  beforeDestroy(){
    clearInterval( this.intervaljob )
  }
}
</script>
<style lang="scss" scoped>
    .bigger_font {
      font-size: 16px;
    }
    .el-transfer ::v-deep.el-transfer-panel {
    width: 240px;
    margin-left: 35px;
  }
  .el-transfer ::v-deep  .el-transfer__buttons{
    width: 25px;
    margin-left: -20px;
  }
  .el-transfer ::v-deep  .el-button+.el-button{
    margin-left: 0px;
  }
</style>  
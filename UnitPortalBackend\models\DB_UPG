-- ----------------------------------------
--                                       --
--          2025-04-02 ZOEXU6            --
--                                       --
-- ----------------------------------------
IF not EXISTS (
    SELECT * FROM sys.all_objects
    WHERE object_id = OBJECT_ID('dh_ntx_seamless_lcm_plans')
)
CREATE TABLE dh_ntx_seamless_lcm_plans (
    id int IDENTITY(1,1) NOT NULL,
    creater varchar(100) COLLATE Latin1_General_CI_AS NULL,
    create_date varchar(100) COLLATE Latin1_General_CI_AS NULL,
    daily_report bit NULL,
    [interval] int NULL,
    max_count int NULL,
    execution_sequence varchar(100) COLLATE Latin1_General_CI_AS NULL,
    desired_plan_date date NULL,
    lcm_type varchar(255) COLLATE Latin1_General_CI_AS NULL,
    facility_type varchar(255) COLLATE Latin1_General_CI_AS NULL,
    CONSTRAINT PK__dh_ntx_s__3213E83F7566AFC5 PRIMARY KEY (id)
);
IF not EXISTS (
    SELECT * FROM sys.all_objects
    WHERE object_id = OBJECT_ID('dh_ntx_seamless_lcm_planned_pes')
)
CREATE TABLE dh_ntx_seamless_lcm_planned_pes (
	id int IDENTITY(1,1) NOT NULL,
	country varchar(100) COLLATE Latin1_General_CI_AS NULL,
	pc varchar(100) COLLATE Latin1_General_CI_AS NULL,
	pe_fqdn varchar(100) COLLATE Latin1_General_CI_AS NULL,
	planned_date datetime NULL,
	task_completed_at datetime NULL,
	status varchar(100) COLLATE Latin1_General_CI_AS NULL,
	plan_id int NULL,
	lcm_type varchar(255) COLLATE Latin1_General_CI_AS NULL,
	facility_type varchar(255) COLLATE Latin1_General_CI_AS NULL,
	status varchar(255) COLLATE Latin1_General_CI_AS NULL,
	CONSTRAINT dh_ntx_seamless_lcm_planned_pes_pk PRIMARY KEY (id)
);
########################################################
###                                                  ###
###     	    2025-05-28 RAYGU1		             ###
###                                                  ###
########################################################
### change column name 'remote_site' to 'remote_site_dsc'
### change column name 'backup_bandwidth' to 'bandwidth_dsc'
### change column name 'default_bandwidth_limit_mbps' to 'bandwidth_runtime'
### change data type of 'bandwidth_dsc' to float

IF EXISTS (
	select * from syscolumns
	where id = object_id('dh_retail_ntx_pe')
	and name = 'remote_site'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_pe.remote_site', 'remote_site_dsc', 'COLUMN';
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and name = 'backup_bandwidth'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_pe.backup_bandwidth', 'bandwidth_dsc', 'COLUMN';
  END
GO

IF EXISTS (
    select * from syscolumns
    where id  = object_id('dh_retail_ntx_pe')
    and name = 'default_bandwidth_limit_mbps'
)
  BEGIN
      EXEC sp_rename 'dh_retail_ntx_pe.default_bandwidth_limit_mbps', 'bandwidth_runtime', 'COLUMN';
  END
GO

ALTER TABLE dh_retail_ntx_pe ALTER COLUMN bandwidth_dsc FLOAT;

########################################################
###                                                  ###
###     	    		2025-06-11 RAYGU1                  ###
###                                                  ###
########################################################
UPDATE dh_ntx_benchmark_group_mapping SET
    pe_site_admin_group = 'ug-wiabadmin'
WHERE
    index_label = 'Warehouse-default';

########################################################
###                                                  ###
###     	    		2025-06-19 RAYGU1                  ###
###                                                  ###
########################################################
UPDATE dh_retail_ntx_vm SET
    is_cvm = 'Y'
WHERE
    name LIKE '%NXP%'

UPDATE dh_wh_ntx_vm SET
    is_cvm = 'Y'
WHERE
    name LIKE '%NXP%'

-- ----------------------------------------
--                                       --
--          2025-06-17 ZOEXU6            --
--                                       --
-- ----------------------------------------
ALTER TABLE dh_retail_ntx_automation_aos_lcm_task ADD slcm_plan_id int NULL;
ALTER TABLE dh_retail_ntx_automation_spp_lcm_task ADD slcm_plan_id int NULL;

ALTER TABLE role_lcm ADD view_slcm varchar(MAX) NULL;
ALTER TABLE role_lcm ADD view_slcm_scope varchar(MAX) NULL;
ALTER TABLE role_lcm ADD manage_slcm varchar(MAX) NULL;
ALTER TABLE role_lcm ADD manage_slcm_scope varchar(MAX) NULL;



########################################################
###                                                  ###
###     	    2025-06-11 HUNHE      		             ###
###                                                  ###
########################################################
-- ----------------------------
-- Table structure for dh_toolbox_task
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_toolbox_task]') AND type IN ('U'))
    DROP TABLE [dbo].[dh_toolbox_task]
GO

CREATE TABLE [dbo].[dh_toolbox_task] (
  [id] int IDENTITY(1,1) NOT NULL,
  [pid] int NULL,
  [create_date] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [creater] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [detail_log_path] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [pe_name] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [task_type] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  CONSTRAINT [PK_dh_toolbox_task] PRIMARY KEY CLUSTERED ([id])
)
GO

ALTER TABLE [dbo].[dh_toolbox_task] SET (LOCK_ESCALATION = TABLE)
GO

-- ----------------------------
-- Table structure for dh_toolbox_task_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_toolbox_task_log]') AND type IN ('U'))
    DROP TABLE [dbo].[dh_toolbox_task_log]
GO

CREATE TABLE [dbo].[dh_toolbox_task_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [task_id] int NULL,
  [log_date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [severity] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [log_info] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  [task_type] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  CONSTRAINT [PK_dh_toolbox_task_log] PRIMARY KEY CLUSTERED ([id])
)
GO

ALTER TABLE [dbo].[dh_toolbox_task_log] SET (LOCK_ESCALATION = TABLE)
GO

ALTER TABLE [dbo].[dh_toolbox_task_log] ADD CONSTRAINT [FK_dh_toolbox_task_log_dh_toolbox_task] FOREIGN KEY ([task_id]) REFERENCES [dbo].[dh_toolbox_task] ([id]) ON DELETE CASCADE ON UPDATE CASCADE
GO

########################################################
###                                                  ###
###     	    2025-06-23 RAYGU1		             ###
###                                                  ###
########################################################
-- change column 'is_cvm' to Boolean type in table 'dh_retail_ntx_vm' and 'dh_wh_ntx_vm'
ALTER TABLE dh_retail_ntx_vm ADD new_is_cvm BIT;
GO
UPDATE dh_retail_ntx_vm
    SET new_is_cvm = CASE
        WHEN is_cvm = 'Y' THEN 1
        WHEN is_cvm = 'N' THEN 0
        ELSE 0
    END;
ALTER TABLE dh_retail_ntx_vm DROP COLUMN is_cvm;
GO
EXEC sp_rename 'dh_retail_ntx_vm.new_is_cvm', 'is_cvm', 'COLUMN';

ALTER TABLE dh_wh_ntx_vm ADD new_is_cvm BIT;
GO
UPDATE dh_wh_ntx_vm
    SET new_is_cvm = CASE
        WHEN is_cvm = 'Y' THEN 1
        WHEN is_cvm = 'N' THEN 0
        ELSE 0
    END;
ALTER TABLE dh_wh_ntx_vm DROP COLUMN is_cvm;
GO
EXEC sp_rename 'dh_wh_ntx_vm.new_is_cvm', 'is_cvm', 'COLUMN';


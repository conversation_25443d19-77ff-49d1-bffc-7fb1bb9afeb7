from werkzeug.exceptions import Conflict, InternalServerError
from business.generic.base_up_exception import BaseUpException


class GenerateLcmPlanFailed(InternalServerError):
    def __init__(self, remaining_clusters):
        super().__init__(
            description=f"Silly backend, unable to design a good plan... Remaining clusters: {remaining_clusters}")


class SeamlessLcmClusterConflict(Conflict):
    def __init__(self, conflict_clusters):
        self.conflict_clusters = conflict_clusters
        self.msg = "Following clusters are already scheduled. Can't plan a new one!"
        super().__init__(description=self.msg)


class SeamlessLcmClusterHasExistingPlanIn24Hrs(Conflict):
    def __init__(self, conflict_plans):
        self.conflict_plans = conflict_plans
        self.msg = "Following clusters are already scheduled within 24 hours. Can't plan a new one!"
        super().__init__(description=self.msg)


class AutomationException(BaseUpException):
    def __init__(self, msg):
        super().__init__(msg)


class LCMTimeIsNotGood(AutomationException):
    def __init__(self):
        super().__init__("We are only allowed to perform software upgrade when during 21:00-4:00 local time")

from business.distributedhosting.nutanix.nutanix import Prism_Element
import static.SETTINGS as SETTING
from business.distributedhosting.nutanix.workload.workload_exceptions import SizingFailed


class Sizing:
    def __init__(self, pe: Prism_Element, logger) -> None:
        self.pe = pe
        self.logger = logger
        self.pe_exceptional = SETTING.SIZING['EXCEPTION_LIST']
        self.cpu_ratio = SETTING.SIZING['CPU_RATIO']
        self.storage_resilience = SETTING.SIZING['STORAGE_RESILIENCE']
        self.resilience_max = SETTING.SIZING['RESILIENCE_MAX']

    def _convert_bytes_to(self, bytes=0, unit='GiB'):
        gibibytes = bytes / pow(1024, 3)
        tebibytes = bytes / pow(1024, 4)
        if unit == 'GiB':
            return round(gibibytes, 2)
        if unit == 'TiB':
            return round(tebibytes, 2)
        return 0

    def check_capacity(self, cpu=None, memory=None, disk=None):
        #check if we do not need to check capacity of the given pe
        if self.pe.fqdn.lower() in self.pe_exceptional:
            self.logger.info("The cluster exists in the exception list of capacity check")
            return True
        #getting hosts from pe
        hosts = self.pe.get_host().json()
        #getting cluster info from pe
        cluster = self.pe.get_cluster().json()
        #getting user vm from pe
        vms = self.pe.get_user_vm().json()
        sum_storage, sum_cores, sum_memory = 0, 0, 0
        highest_ram, highest_cores = 0, 0
        on_vms_count, on_vms_mem, on_vms_cores = 0, 0, 0
        #installed hardware capacity calculation
        total_entities = hosts.get('metadata').get('totalEntities')
        self.logger.info(f"The cluster is a {total_entities} node(s) cluster")
        for host in hosts.get('entities'):
            #storage
            node_storage = host.get('usageStats').get('storage.capacity_bytes')
            logical_node_storage = int(node_storage) / 2
            logical_node_storage_tib = self._convert_bytes_to(bytes=logical_node_storage, unit='TiB')
            sum_storage += logical_node_storage_tib
            #CPU
            node_cores = host.get('numCpuCores') if host.get('numCpuCores') else 0
            #get the highest cores
            if node_cores > highest_cores:
                highest_cores = node_cores
            sum_cores += node_cores
            #memory
            node_memory = host.get('memoryCapacityInBytes') if host.get('memoryCapacityInBytes') else 0
            #get the highest ram
            if node_memory > highest_ram:
                highest_ram = node_memory
            node_memory_gib = self._convert_bytes_to(bytes=node_memory, unit='GiB')
            sum_memory += node_memory_gib
            self.logger.info(f"Fetching capacity from node {host.get('name')}")
            self.logger.info(f"Storage of node {host.get('name')} is {logical_node_storage_tib} TiB")
            self.logger.info(f"Number of cores on node {host.get('name')} is {node_cores}")
            self.logger.info(f"Memory install on node {host.get('name')} is {node_memory_gib} GiB")
        #resilient storage calculation (assuming one node failure)
        resilient_storage = self.storage_resilience * sum_storage if total_entities == 1 else self.storage_resilience * (sum_storage - logical_node_storage_tib) #accounting for 1 node failure and single node cluster
        #resilient memory calculation
        highest_ram_gib = self._convert_bytes_to(bytes=highest_ram, unit='GiB')
        resilient_ram = sum_memory if total_entities == 1 else sum_memory - highest_ram_gib #account for 1 node failure and single node cluster
         #accounting for the CVMs which uses 32 GiB memory
        resilient_ram_cvm = 32 if total_entities == 1 else (total_entities - 1) * 32
        total_resilient_ram_cvm = resilient_ram - resilient_ram_cvm
        #resilient CPU calculation
        resilient_cores = sum_cores if total_entities == 1 else sum_cores - highest_cores
         #accounting for the CVMs which uses 3 physical cores
        cores_cvm = 3 if total_entities == 1 else (total_entities - 1) * 3
        physical_resilient_cores = resilient_cores - cores_cvm
        #pyhsical to virtual CPU mapping (cpu_ratio)
        resilient_vcpu = self.cpu_ratio * physical_resilient_cores
        #utilized capacity calculation
         #used storage
        used_cluster_storage = cluster.get('usageStats').get('storage.usage_bytes')
        logical_used_cluster_storage = int(used_cluster_storage) / 2
        logical_used_cluster_storage_tib = self._convert_bytes_to(bytes=logical_used_cluster_storage, unit='TiB')
         #used ram and cores
        for vm in vms.get('entities'):
            if vm.get('power_state') == "on":
                on_vms_count += 1
                on_vms_mem += vm.get('memory_mb')
                on_vms_cores += vm.get('num_vcpus') * vm.get('num_cores_per_vcpu')
        on_vms_mem_gib = on_vms_mem / 1024
        #available capacity calculation
         #storage
        available_storage = resilient_storage - logical_used_cluster_storage_tib
         #cpu
        available_cores = resilient_vcpu - on_vms_cores
         #memory
        available_memory = total_resilient_ram_cvm - on_vms_mem_gib
        self.logger.info(f"Number of nodes in this cluster is {total_entities}")
        self.logger.info(f"Total storage capacity on this cluster is {sum_storage} TiB")
        self.logger.info(f"total number of CPU cores on cluster is {sum_cores}")
        self.logger.info(f"Total memory capacity on this cluster is {sum_memory} GiB")

        self.logger.info(f"Resilient storage capacity on this cluster is {resilient_storage} TiB")
        self.logger.info(f"Number of resilient physical CPU cores is {resilient_cores}")
        self.logger.info(f"Number of resilient physical CPU cores accounting CVMs is {physical_resilient_cores}")
        self.logger.info(f"Number of resilient virtual CPU cores (assuming 1:{self.cpu_ratio} ratio) is {resilient_vcpu}")
        self.logger.info(f"Resilient memory capacity on this cluster is {resilient_ram} GiB")
        self.logger.info(f"Resilient memory capacity accounting CVMs on this cluster is {total_resilient_ram_cvm} GiB")
        self.logger.info(f"Utilized storage of cluster is {logical_used_cluster_storage_tib} TiB")
        self.logger.info(f"There are {vms.get('metadata').get('total_entities')} VMs on this cluster")
        self.logger.info(f"Number of virtual cores used by {on_vms_count} VMs that are powered on is {on_vms_cores}")
        self.logger.info(f"Memory used by {on_vms_count} VMs that are powered on is {on_vms_mem_gib} GiB")

        self.logger.info(f"Available storage for new VM provisioning is {available_storage} TiB")
        self.logger.info(f"Available vCPU cores for new VM provisioning is {available_cores}")
        self.logger.info(f"Available memory for new VM provisioning is {available_memory} GiB")

        #difference calculation
        lacking_storage, lacking_cpu, lacking_memory = False, False, False
         #disk
        disk_tib = int(disk) / 1024
        if (logical_used_cluster_storage_tib + disk_tib) > self.resilience_max * resilient_storage: #stopping at 90% of resilient storage
            self.logger.warning(f"Storage required: {disk_tib}, available: {self.resilience_max * resilient_storage - logical_used_cluster_storage_tib}. (stopping at 90% of resilient storage)")
            lacking_storage = True
         #cpu
        if int(cpu) > available_cores:
            self.logger.warning(f"CPU required: {cpu}, available: {available_cores}")
            lacking_cpu = True
         #memory
        if int(memory) > available_memory:
            self.logger.warning(f"Memory required: {memory}, available: {available_memory}")
            lacking_memory = True
        #display final Yes or No flag
        if lacking_storage or lacking_cpu or lacking_memory:
            raise SizingFailed(lacking_storage, lacking_cpu, lacking_memory)


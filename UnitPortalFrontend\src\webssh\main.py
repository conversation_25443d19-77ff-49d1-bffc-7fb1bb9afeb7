import ssl
import logging
import tornado.web
import tornado.ioloop

from tornado.options import options
from src.webssh import handler
from src.webssh.handler import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NotFoundHandler
from src.webssh.settings import (
    get_app_settings,  get_host_keys_settings, get_policy_setting,
    get_ssl_context, get_server_settings, check_encoding_setting
)

# # 配置日志
# log_file = "src/webssh/log/app.log"  # 日志文件
# logging.basicConfig(
#     level=logging.DEBUG,  # 设置日志输出等级
#     format='%(asctime)s - %(levelname)s - %(message)s',  # 日志格式
#     handlers=[
#         logging.FileHandler(log_file),  # 将日志输出到文件
#         logging.StreamHandler()  # 同时输出到控制台
#     ]
# )

def make_handlers(loop, options):
    host_keys_settings = get_host_keys_settings(options)
    policy = get_policy_setting(options, host_keys_settings)

    handlers = [
        (r'/', IndexHand<PERSON>, dict(loop=loop, policy=policy,
                                  host_keys_settings=host_keys_settings)),
        (r'/ws', <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dict(loop=loop))
    ]
    return handlers


def make_app(handlers, settings):
    settings.update(default_handler_class=NotFoundHandler)
    return tornado.web.Application(handlers, **settings)


def app_listen(app, port, address, server_settings):
    app.listen(port, address, **server_settings)
    if not server_settings.get('ssl_options'):
        server_type = 'http'
    else:
        server_type = 'https'
        handler.redirecting = True if options.redirect else False
    logging.info(
        'Listening on {}:{} ({})'.format(address, port, server_type)
    )

def main():
    options.parse_command_line()
    check_encoding_setting(options.encoding)
    loop = tornado.ioloop.IOLoop.current()
    app = make_app(make_handlers(loop, options), get_app_settings(options))
    # ssl_ctx = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
    # ssl_ctx=ssl_ctx.load_cert_chain(r"D:\UP-Shuai_WH\UnitPortalFrontend\src\webssh\retcnsos-nt5678ikeacom.crt", r"D:\UP-Shuai_WH\UnitPortalFrontend\src\webssh\retcnsos-nt5678ikeacom.key")
    ssl_ctx = get_ssl_context(options)
    print(ssl_ctx)
    server_settings = get_server_settings(options)
    app_listen(app, options.port, options.address, server_settings)
    if ssl_ctx:
        server_settings.update(ssl_options=ssl_ctx)
        app_listen(app, options.sslport, options.ssladdress, server_settings)
    loop.start()


if __name__ == '__main__':
    main()

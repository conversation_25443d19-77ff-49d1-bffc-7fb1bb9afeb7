from models.database import db, ma


class ModelUPFeedback(db.Model):
    __tablename__    = 'dh_up_feedback'
    id               = db.Column(db.Integer, primary_key=True)
    name             = db.Column(db.String(100))
    date             = db.Column(db.String(50))
    comment          = db.Column(db.String(8000))
    solution         = db.Column(db.String(8000))
    star             = db.Column(db.String(55))
    anonymous        = db.Column(db.<PERSON>)
 
    
class ModelUPFeedbackSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'name', 'date', 'star', 'comment', 'anonymous', 'solution'
        )
        
        
class ModelUPVisit(db.Model):
    __tablename__     = 'dh_up_visit'
    id                = db.Column(db.Integer, primary_key=True)
    page_name         = db.Column(db.String(100))
    date             = db.Column(db.String(50))

    
class ModelUPVisitSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'page_name', 'date'
        )
        
        
class ModelUPAPIUtilization(db.Model):
    __tablename__     = 'dh_up_api_utilization'
    id                = db.Column(db.Integer, primary_key=True)
    api_path         = db.Column(db.String(100))
    times             = db.Column(db.String(50))
 
    
class ModelUPAPIUtilizationSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'api_path', 'times'
        )


class ModelUPAPIUtilizationLog(db.Model):
    __tablename__    = 'dh_up_api_utilization_log'
    id               = db.Column(db.Integer, primary_key=True)
    api_path         = db.Column(db.String(100))
    date             = db.Column(db.String(50))
    caller           = db.Column(db.String(100))
 
    
class ModelUPAPIUtilizationLogSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'api_path', 'date', 'caller'
        )
function Invoke-UpApi(){
    param(
        [string]                                     $Endpoint,
        [string]                                     $RequestUri,
        [object]                                     $Headers,
        [object]                                     $Body,
        [object]                                     $Auth,
        [string] [ValidateSet("GET", "POST", "PUT")] $Method,
        [int]                                        $MaxTry = 3,
        [int]                                        $TimeoutSec = 30
    )
    if ($MaxTry) {
        $Headers = @{
            'Accept'        = 'application/json'
            'Authorization' = $Auth
            'Content-Type'  = 'application/json'
        }
        $Payload = @{
            'Uri'        = "https://$($Endpoint)/$($RequestUri)"
            'Method'     = $Method
            'Headers'    = $Headers
            'TimeoutSec' = $TimeoutSec
        }
        if($Body){
            $Payload['Body'] = $Body | ConvertTo-Json -Depth 20
        }
        try {
            return Invoke-RestMethod @Payload -SkipCertificateCheck
        } catch {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling $Endpoint for $RequestUri. Cause: $_ Retry in 5 seconds"
            Start-Sleep 5
            return Invoke-UpApi -Endpoint $Endpoint `
                                -RequestURI $RequestUri `
                                -Headers $Headers `
                                -Body $Body `
                                -Auth $Auth `
                                -Method $Method `
                                -MaxTry $($MaxTry - 1) `
                                -TimeoutSec $($TimeoutSec + 5)
        }
    } else {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling $Endpoint for $RequestUri."
        return $null
    }
}
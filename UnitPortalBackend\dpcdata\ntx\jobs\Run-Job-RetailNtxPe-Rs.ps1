$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Launch-Job(){
    Begin {
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Load data from the table dh_retail_ntx_pe
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhPEs.Count)' PEs need to update" -DumpFile $DumpFile
        $DhPEs | Foreach-Object -ThrottleLimit 50 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $PE         = $_
            $UpdateDict = $using:CollectionUpdate
            $PC         = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for $($PE.name)" -DumpFile $DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for $($PE.name)" -DumpFile $using:DumpFile
                return
            }
            #Construct a data model which stores data needs to be updated into the table
            $RsMap = [PSCustomObject]@{
                'fqdn'                 = $PE.fqdn
                'remote_site'          = "NA"
                'remote_backup_number' = 0
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Calling $($PE.fqdn) to get the protection domain" -DumpFile $using:DumpFile
            $PrismCall1 = $(Rest-Prism-v1-List-ProtectionDomain -Fqdn $PE.fqdn -Auth $Auth) | Where-Object {$_.name -match $PE.name}
            if ($PE.node_number -eq "1") {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "$($PE.name) is the single node cluster" -DumpFile $using:DumpFile
                $PrismCall1 = $PrismCall1 | Where-Object {$_.name -match 'Silver_CCG'} | Select-Object -First 1
            }else {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "$($PE.name) has $($PE.node_number) nodes" -DumpFile $using:DumpFile
                $PrismCall1 = $PrismCall1 | Where-Object {$_.name -match 'Gold_CCG'} | Select-Object -First 1
            }
            #Validate the cluster has the Gold protection domain and the PD contains remote site settings, otherwise, the cluster has no remote backup
            if (!$PrismCall1 -or !($PrismCall1.remoteSiteNames)) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "$($PE.name) has 0 remote site settings" -DumpFile $using:DumpFile
                $UpdateDict.Add($RsMap)
                return
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Remote site settings of $($PE.name) is available" -DumpFile $using:DumpFile
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Calling $($PE.fqdn) to get the remote snapshots" -DumpFile $using:DumpFile
            $PrismCall2 = Rest-Prism-v1-List-RsDrSnapShot -Fqdn $PE.fqdn -Auth $Auth
            $Rs = @()
            #Convert name of each remote sites (e.g. RETUS051-NXC000_Gold_CCG) to cluster name (e.g. RETUS051-NXC000)
            $PrismCall1.remoteSiteNames | ForEach-Object {
                $Rs += $_.Split('_')[1]
            }
            $RsMap.remote_site = $Rs
            $RsMap.remote_backup_number = ($PrismCall2.entities | Where-Object {$_.protectionDomainName -match $PE.name}).count
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "$($PE.name) has $($RsMap.remote_backup_number) remote snapshots" -DumpFile $using:DumpFile
            $UpdateDict.Add($RsMap)
        } -UseNewRunspace
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pe]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxPe-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job
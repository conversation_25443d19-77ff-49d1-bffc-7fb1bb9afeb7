import json

from business.authentication.authentication import Vault
from business.benchmark.benchmark import Benchmark
from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.distributedhosting.nutanix.cluster.cluster_specs import ClusterSpec
from business.distributedhosting.nutanix.foundation_central import FoundationCentral
from business.generic.ipam import Ipam
from models.cluster_models import ModelC<PERSON>Task, ModelClusterTaskSchema, ModelClusterTaskLog, \
    ModelClusterTaskLogSchema, ModelWhClusterTask, ModelWhClusterTaskSchema, ModelWhClusterTaskLog, \
    ModelWhClusterTaskLogSchema
from models.ntx_models import ModelPrismCentral, ModelPrismElement
from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
from static.SETTINGS import CLUSTER_LOG_PATH


class DeleteClusterTask(BaseUpTask):
    LOG_DIR = CLUSTER_LOG_PATH
    LOG_TYPE = "NTX_CLUSTER"
    TASK_TYPE = "DELETE_CLUSTER"
    EXISTING_TASK_ALLOWED = True

    def __init__(self, payload, facility_type):
        if facility_type == FacilityType.RETAIL:
            super().__init__(
                ModelClusterTask, ModelClusterTaskSchema, ModelClusterTaskLog, ModelClusterTaskLogSchema, payload)
            self.model_pc = ModelPrismCentral
            self.model_pe = ModelPrismElement
        elif facility_type == FacilityType.WAREHOUSE:
            super().__init__(
                ModelWhClusterTask, ModelWhClusterTaskSchema, ModelWhClusterTaskLog, ModelWhClusterTaskLogSchema, payload)
            self.model_pc = ModelWarehousePrismCentral
            self.model_pe = ModelWarehousePrismElement
        self.task_duplicated_kwargs = {
            ClusterSpec.TASK_ID: self.payload[ClusterSpec.TASK_ID],
        }
        self.facility_type = facility_type

    def task_process(self):
        self.ilg.write("Cleanup cluster", severity="title")
        self.clean_ipam()
        self.clean_foundation_central()

    def clean_ipam(self):
        self.ilg.write("Cleanup IPAM", severity="title")
        ahv_cvm_oob_mapping = json.loads(self.task.ahv_cvm_oob_mapping)
        domain = Benchmark().get_bmk_by_id(self.task.benchmark_id).get('systems').get('ldap_domain')
        fqdns_to_delete = [
            f"{value['hostname'].lower()}.{domain}"
            for x in ahv_cvm_oob_mapping
            for key, value in x['static'].items()
        ]
        fqdns_to_delete.append(f"{self.task.pe_name.lower()}.{domain}")
        number = self.task.pe_name.split('-')[1].split('.')[0].lower().split('nxc00')[1]  # nxc00?
        ds_fqdn = f"{self.task.pe_name.lower().split('-')[0]}-nxd00{number}.{domain}"
        fqdns_to_delete.append(ds_fqdn)
        self.ilg.write(f"Will delete following fqdns: {fqdns_to_delete}")
        for fqdn in fqdns_to_delete:
            Ipam(logger=self.logger).cleanup_fqdn(fqdn)

    def clean_foundation_central(self):
        self.ilg.write("Cleanup Foundation Central", severity="title")
        # TODO: use admin
        tier = self.model_pc.query.filter_by(fqdn=self.task.pc_fqdn).first().tier
        vault = Vault(tier=tier)
        central_pe = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('central_pe')
        _res, pc_data = vault.get_secret(f"{central_pe}/Site_Pc_Admin")  # for test
        rest_fc = FoundationCentral(
            endpoint=self.task.pc_fqdn, username=pc_data['username'], password=pc_data['secret'], logger=self.logger)
        rest_fc.archive_cluster(self.task.cluster_uuid)

import logging
import re

import sqlalchemy
import werkzeug.exceptions as flaskex
from flask import abort

from business.authentication.authentication import ServiceAccount, LdapAdmin, User
from models.auth_models import ModelGroup, ModelUserGroupRoleMapping, ModelGroupRoleMapping, ModelGroupSchema, ModelRole
from models.database import db

class Group:
    def __init__(self, group_id=None, group_name=None):
        sa = ServiceAccount(usage="nutanix_pm").get_service_account()
        self.ldap = LdapAdmin(sa["username"], sa["password"])
        if not self.ldap:
            raise Exception(f"User {sa['username']} failed to login ldap!")
        if group_id:
            try:
                self.group_id = group_id
                self.group_model = ModelGroup.query.filter_by(id=group_id).one()
                self.group_name = self.group_model.group_name
            except sqlalchemy.exc.NoResultFound as e:
                msg = f"Group with id {group_id} or name {group_name} doesn't exist!"
                logging.error(f"{msg}. {str(e)}.")
                abort(400, msg)
        elif group_name:
            self.group_name = group_name

    def get_group_info_from_db(self):
        grouplist = ModelGroupSchema(many=True).dump(ModelGroup.query.all())
        groups_report = []
        for group_detail in grouplist:
            role_list = []
            single_user_detailinfo = {}
            group_role = group_detail['roles']
            if re.match(r'\D', group_role):
                single_user_detailinfo['roles'] = group_role if group_role else "Null"
            else:
                for roleid in group_role.split(','):
                    grouprole = ModelRole.query.filter_by(id=roleid).all()
                    role_list.append(grouprole[0].name)
                single_user_detailinfo['roles'] = ';'.join(role_list) if role_list else "Null"
            # user_role = UserRole(user_id=user_detail['id'])
            # user_privileges = user_role.get_all_privileges()
            # print(user_privileges)
            # role_group_names = [role_group_name for role_group_name in user_privileges]
            # for role_group_name in role_group_names:
            #     privileges_list = privileges_list + [privilege_name for privilege_name in user_privileges[role_group_name] if user_privileges[role_group_name][privilege_name] =="full"]         # noqa
            single_user_detailinfo['groupname'] = group_detail['group_name']
            single_user_detailinfo['domain'] = "ikea.com"
            # single_user_detailinfo['lastlogondate'] =group_detail['lastlogondate'] if group_detail['lastlogondate'] else "Null"       # noqa
            # single_user_detailinfo['roles']    = ';'.join(privileges_list) if privileges_list else "Null"
            groups_report.append(single_user_detailinfo)
        return groups_report

    def create_new_group(self):
        if not self.ldap.is_group_existing_in_ad(self.group_name):
            raise flaskex.BadRequest(f"Group '{self.group_name}' doesn't exist!")
        return self._create_new_group()

    def _create_new_group(self):
        group_model = ModelGroup.query.filter_by(group_name=self.group_name).scalar()
        if not group_model:
            group_model = ModelGroup().create_group(self.group_name)
        self.group_id = group_model.id
        self.group_name = group_model.group_name
        self.group_model = group_model
        return group_model

    def create_group(self, group_name, role_ids):
        logging.info(f"Start to create new group '{group_name}...")
        new_group = ModelGroup().create_group(group_name, role_ids)
        logging.info("Group created.")
        logging.info("Start to create the users under group...")
        users_to_add = self.get_all_users_from_ad(self.group_name)
        logging.info(f"Users to add: {users_to_add}")
        user_ids = User.bulk_create_by_user_names(users_to_add)
        ModelGroupRoleMapping.add_mappings_for_group_users(new_group.id, user_ids, role_ids)
        db.session.commit()

    # def get_all_users_from_ad(self):
    #     return self.ldap.search_group(self.group_name)

    def get_all_users_from_ad(self, group_name):
        return self.ldap.search_group(group_name)

    def delete(self):
        try:
            ModelUserGroupRoleMapping.query.filter_by(group_id=self.group_id).delete()
            ModelGroupRoleMapping.query.filter_by(group_id=self.group_id).delete()
            ModelGroup.query.filter_by(id=self.group_id).delete()
            db.session.commit()
            return {"message": "success"}, 200
        except Exception as e:
            db.session.rollback()
            msg = f'Failed to delete group with id {self.group_id}.'
            logging.error(msg)
            return {"message": msg, "error_message": str(e)}, 500

    # @property
    # def get_group_name(self):
    #     return self.group_name

    @staticmethod
    def is_group_exists_in_db(group_name):
        return ModelGroup.query.filter_by(group_name=group_name).scalar()

$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
#Import required modules from the project folder
@(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1") | Foreach-Object {
    try {
        Import-Module -Name $_.VersionInfo.FileName `
                      -DisableNameChecking:$true `
                      -Force
    }
    catch {
        $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
        Write-Host $Message -ForegroundColor Red
        Add-Content -Path $DumpFile -Value $Message
        Exit 0
    }
}
Function Launch-Job(){
    Begin {
        try {
            $Vars = Load-Vars
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Backup-DpcDatabase -Vars $Vars
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Database backup is completed" -DumpFile $DumpFile
    }
}
Launch-Job
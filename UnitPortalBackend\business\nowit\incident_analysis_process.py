import time
import re
import os
import requests
import logging
from flask import Flask
from models.database import db
from sqlalchemy import cast, Date
from models.incident_models import IncidentHandlerAnalysis
from datetime import datetime
import socket
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
from business.authentication.authentication import ServiceAccount
from requests.auth import HTTPBasic<PERSON><PERSON>
from pandas import json_normalize
from business.nowit.incident_handler_config import IncHandlerCfg
from business.generic.commonfunc import DBConfig


class IncidentAnalysis:

    def __init__(self) -> None:
        self.token_request_payload = {}
        self.sa = ServiceAccount()
        self.nowit_account = self.sa.get_service_account(usage='nowit_api')
        self.nowit_username = self.nowit_account['username']
        self.nowit_password = self.nowit_account['password']
        self.token_request_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/login'
        self.nowit_auth = f"{self.nowit_username}:{self.nowit_password}"

    def scheduled_batch(self):
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)
        app.app_context().push()
        path = 'D:/incident_data'
        self.batch(path)
        return True

    def get_incident_handler_token2(self):
        max_tries = 5
        proxies = {
            "http": None,
            "https": None,
        }
        for _ in range(max_tries):
            try:
                response = requests.post(
                    self.token_request_url,
                    data=self.token_request_payload,
                    auth=HTTPBasicAuth(self.nowit_username, self.nowit_password),
                    verify=False,
                    proxies=proxies,
                    timeout=10
                )
                logging.info(f"response code:{response.status_code},{response.text}")
                if response.ok:
                    logging.info("token get success")
                    token = response.text
                    return token
                logging.error(f"NowIT token request failed:{response.status_code},{response.text}")
            except ValueError as e:
                logging.error(f"Get NowIT Token Failed. Error is {e}")
            time.sleep(5)
        return None

    def get_resolved_incidents_fetch(self, url, token):
        headers = {
            "x-access-token": token
        }
        logging.info("start get_resolved_incidents_fetch API...")
        for _ in range(3):
            get_resolved_data = requests.request('GET', headers=headers, url=url, verify=False)
            logging.info(f"return status code:{get_resolved_data.status_code}")
            # logging.info(get_resolved_data.content)
            if get_resolved_data.ok:
                logging.info("get_resolved_data success")
                data = get_resolved_data.json()
                return data
            time.sleep(3)
            logging.error(f"Request failed with status code: {get_resolved_data.status_code}")
        logging.error("call get_resolved_incidents_fetch API failed")
        return None

    def fetch_url(self, date_str=None, inc_num=None, offset=0):
        """
          assignment group = gdh              and
          create time > 2024/7/15 00:00:00    and
          state is resolved                 or
          state is closed                     and
          resolved by is 'sac AICFN'        or
          short description contains [
       """
        if date_str is None:
            date_stamp = ('2024-7-15', '00:00:00')
            date_string = str(date_stamp)
            auto_resolved_url = f'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/incident?sysparm_query=\
                           assignment_group%3D321458b5878c099071b9a8a90cbb3521^ORDERBYsys_created_on\
                           ^sys_created_on>javascript:gs.dateGenerate{date_string}\
                           ^resolved_by%3D2353ad3d1b527890157feb11b24bcb16%5EORshort_descriptionLIKE%5B\
                           ^state%3D6%5EORstate%3D7\
                           &sysparm_limit=500&sysparm_offset={offset}'
        else:
            date_string = str(date_str)
            auto_resolved_url = f'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/incident?sysparm_query=\
                           assignment_group%3D321458b5878c099071b9a8a90cbb3521^ORDERBYsys_created_on\
                           ^sys_created_on>=javascript:gs.dateGenerate{date_string}\
                           ^resolved_by%3D2353ad3d1b527890157feb11b24bcb16%5EORshort_descriptionLIKE%5B\
                           ^state%3D6%5EORstate%3D7\
                           ^numberNOT%20LIKE{inc_num}\
                           &sysparm_limit=500&sysparm_offset={offset}'
        return auto_resolved_url

    def fetch_url_patch(self, date_str, offset=0):  # fetch by one month inc data
        """
          assignment group = gdh              and
          create time >= y/mon/day 00:00:00    and
          create time <= y/mon/day 00:00:00    and
          state is resolved                 or
          state is closed                     and
          resolved by is 'sac AICFN'        or
          short description contains [
        """
        date_string = str(date_str)
        date_string = str(f"('{date_string}','00:00:00')")
        auto_resolved_url = f'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/incident?sysparm_query=\
                                   assignment_group%3D321458b5878c099071b9a8a90cbb3521^ORDERBYsys_created_on\
                                   ^sys_created_on>javascript:gs.dateGenerate{date_string}\
                                   ^sys_created_on<javascript:gs.beginningOfToday()\
                                   ^resolved_by%3D2353ad3d1b527890157feb11b24bcb16%5EORshort_descriptionLIKE%5B\
                                   ^state%3D6%5EORstate%3D7\
                                   &sysparm_limit=500&sysparm_offset={offset}'
        return auto_resolved_url

    def query_incs_by_dates(self, start_date, end_date):
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
        all_records = db.session.query(IncidentHandlerAnalysis.inc_num).filter(
            cast(IncidentHandlerAnalysis.creation_time, Date) >= start_datetime.date(),
            cast(IncidentHandlerAnalysis.creation_time, Date) <= end_datetime.date(),
            IncidentHandlerAnalysis.id != 1
        ).all()
        inc_list = [inc_num[0] for inc_num in all_records]
        return inc_list

    def is_csv_empty(self, file_path):
        try:
            df = pd.read_csv(file_path)
            if df.shape[0] == 0:  # empty data, no colum
                return True
            if df.shape[0] == 1:  # colum only, no data
                return True
            return df.empty  # if DataFrame null，return True
        except pd.errors.EmptyDataError:
            return True  # if file is empty, will throw this exception

    def get_inc_data(self, path, url, token):
        json_data = self.get_resolved_incidents_fetch(url=url, token=token)
        if not json_data:  # json None
            logging.info(f"{url}")
            raise Exception(f"json is None.Result:{json_data}")
        df = json_normalize(json_data['result'])
        df.to_csv(path, index=False)
        offset_len = len(json_data['result'])
        logging.info(f"offset={offset_len}")
        return offset_len

    def get_pe_name(self, short_dest):
        if 'null' in short_dest:
            string: str = 'NA'
            return string
        try:
            match = re.search(IncHandlerCfg.NX_GET_PE_NAME_PAT, short_dest).group(1)  # SiaB pattern get cluster name
            return match
        except AttributeError:
            match = 'null'
        if match == 'null':
            try:
                match = re.search(IncHandlerCfg.WIAB_PE_NAME_PAT2, short_dest).group(1)  # WiaB pattern
                return match
            except AttributeError:
                match = 'null'
        if match == 'null':
            try:
                match = re.search(IncHandlerCfg.wtp_power_supply_host, short_dest).group(1)  # WTP pattern
                return match
            except AttributeError:
                return match

    def determine_type(self, row):
        match True:
            case _ if 'SiaB' in row['model']:
                return 'SiaB'
            case _ if 'WiaB' in row['model']:
                return 'WiaB'
            case _ if 'WTP' in row['model']:
                return 'WTP'
            case _ if 'WTP' in row['short_description']:
                return 'WTP'
            case _ if 'WiaB' in row['short_description']:
                return 'WiaB'
            case _ if 'SiaB' in row['short_description']:
                return 'SiaB'
            case _:
                return 'SiaB'

    def determine_model(self, short_description):
        conditions = ["ret", "RET", "#", "SiaB", "-NXP00", "-nxp00", "-NXC00", "-nxc00"]
        if pd.isna(short_description):  # check if None
            return 'SiaB-Others'
        for pattern, model_name in IncHandlerCfg.MODEL_MAPPING.items():
            if re.search(pattern, short_description):
                return model_name
        if 'No Model' in short_description:
            if 'WiaB' in short_description or "**" in short_description:
                return 'WiaB-Others'
            if any(condition in short_description for condition in conditions):
                return 'SiaB-Others'
        return 'SiaB-Others'

    def date_to_timestamp(self, date_string):
        dt = pd.to_datetime(date_string, format='%Y-%m-%d %H:%M:%S').tz_localize('CET')
        # change to UTC time
        dt_utc = dt.tz_convert('UTC')
        timestamp = int(dt_utc.timestamp())
        return str(timestamp)

    def get_latest_info(self):
        try:
            first_data = IncidentHandlerAnalysis.query.filter_by(id=1).one()
            creation_time = first_data.creation_time
            inc_num = first_data.inc_num
            if creation_time:
                date_str, time_str = creation_time.split(' ')
                date_stamp = f"('{date_str}','{time_str}')"
                return date_stamp, inc_num
            return None, None
        except Exception:
            return None, None

    def delete_oldest_csv(self, directory):
        files = [f for f in os.listdir(directory) if f.startswith('backup_') and f.endswith('.csv')]
        if len(files) > 3:
            files.sort()
            os.remove(os.path.join(directory, files[0]))

    def nslookup_ip_func(self, description):
        """
            @fixed PE-PC Connection Failure model:
            get ip in description and nslookup to the hostname
        """
        retries = 3
        for attempt in range(retries):
            try:
                ip = re.findall(IncHandlerCfg.IP_PATTERN, description)[0]
                hostname, _, _ = socket.gethostbyaddr(ip)
                hostname = hostname.split('.')[0]
                return hostname
            except (socket.herror, IndexError):
                if attempt == retries - 1:
                    return 'null'

    # --------------------------batch------------------------------------------------
    def inc_data_fetch(self, original_data_path, filtered_data_path):
        """
            1.get inc json data to csv
            2.renamed column 'cmdb_ci' to 'site', 'sys_created_on' to 'creation_time', 'number' to 'inc_num'
            3.add column:'timestamp','type','model','human_check','auto_resolved','note'
            4.select some columns we need to new csv
        """
        df = pd.read_csv(original_data_path)
        df.rename(columns={'cmdb_ci': 'site'}, inplace=True)
        df.rename(columns={'sys_created_on': 'creation_time'}, inplace=True)
        df.rename(columns={'calendar_duration': 'duration_time'}, inplace=True)
        df.rename(columns={'resolved_at': 'resolved_time'}, inplace=True)
        df.rename(columns={'number': 'inc_num'}, inplace=True)
        df.rename(columns={'assigned_to': 'assigned'}, inplace=True)
        df['timestamp'] = ''
        df['type'] = ''
        df['model'] = ''
        df['human_check'] = ''
        df['auto_resolved'] = ''
        df['note'] = ''
        selected_columns = ['inc_num', 'short_description', 'assigned', 'site', 'priority', 'creation_time',
                            'resolved_time', 'duration_time', 'timestamp', 'type', 'model', 'human_check',
                            'auto_resolved', 'note', 'description']
        new_df = df[selected_columns]
        new_df.to_csv(filtered_data_path, index=False)

    def inc_data_handle(self, file_path):
        """
            1.fill in all the cluster site name if None
            2.fill in 'human_check' and 'auto_resolved' column
            3.fill in 'type' column
            4.fill in 'model' column
            5.fill in 'timestamp' column
        """
        print('start handle...')
        df = pd.read_csv(file_path)
        df.loc[df['site'].isna(), 'site'] = df.loc[df['site'].isna(), 'short_description'].apply(self.get_pe_name)
        df['assigned'] = df['assigned'].fillna('NA')
        df['human_check'] = df['short_description'].apply(lambda x: True if '[' in x else False)
        df['auto_resolved'] = df['human_check'].apply(lambda x: True if x is False else False)
        df['model'] = df['short_description'].apply(self.determine_model)
        df['type'] = df.apply(self.determine_type, axis=1)
        df['timestamp'] = df['creation_time'].apply(self.date_to_timestamp)
        df.to_csv(file_path, index=False)

    def update_site(self, file_path):
        df = pd.read_csv(file_path)
        with ThreadPoolExecutor(max_workers=5) as executor:
            model_filter = df[df['model'] == 'SiaB-PE-PC Connection']
            descriptions = model_filter['description'].tolist()
            results = list(executor.map(self.nslookup_ip_func, descriptions))
            df.loc[df['model'] == 'SiaB-PE-PC Connection', 'site'] = results
        df.to_csv(file_path, index=False)

    def insert_data(self, file_path):
        """
            1.read csv file
            2.get data input to payload
            3.init SQL
            3.insert data to SQL
            4.log the latest time to the first data
        """
        # df = pd.read_csv(file_path, nrows=10)
        print('start insert data')
        df = pd.read_csv(file_path)
        for _, row in df.iterrows():  # use _ instead of 'index'
            payload = {
                'inc_num': str(row['inc_num']),
                'type': str(row['type']),
                'site': str(row['site']),
                'model': str(row['model']),
                'assigned': str(row['assigned']),
                'creation_time': row['creation_time'],
                'resolved_time': row['resolved_time'],
                'duration_time': str(row['duration_time']),
                'timestamp': str(row['timestamp']),
                'priority': str(row['priority']),
                'human_check': str(row['human_check']),
                'auto_resolved': str(row['auto_resolved']),
                'short_description': str(row['short_description']),
                'note': str(row['note'])
            }
            sa = IncidentHandlerAnalysis(**payload)
            db.session.add(sa)
            db.session.commit()
        print('insert data success')
        last_creation_time = df['creation_time'].iloc[-1]
        last_timestamp = str(df['timestamp'].iloc[-1])
        last_inc_num = df['inc_num'].iloc[-1]
        firstdata = IncidentHandlerAnalysis.query.filter_by(id=1).one()
        firstdata.note = firstdata.creation_time  # log last creation time to 'note'
        firstdata.creation_time = last_creation_time  # log latest creation time to 'creation_time'
        firstdata.timestamp = last_timestamp
        firstdata.inc_num = last_inc_num
        db.session.commit()

    def format_csv(self, path, ori_data, filtered_data):
        """
            1.backup file
            2.format file
            3.delete the oldest backup file
        """
        current_time = datetime.now().strftime('%Y-%m-%d_%H%M%S')
        df2 = pd.read_csv(filtered_data)
        backup_filtered = f'{path}/backup_filtered_{current_time}.csv'
        df2.to_csv()
        df2.to_csv(backup_filtered, index=False)
        empty_df = pd.DataFrame()
        empty_df.to_csv(ori_data, index=False)
        empty_df.to_csv(filtered_data, index=False)
        self.delete_oldest_csv(path)

    def batch(self, path):
        """
            1.read first data,get creation time
            2.use api(with latest creation time) fetch the latest data to csv 'original_data'
            3.data process:
                a.inc_data_fetch
                b.inc_data_handle(fill all the column data)
                c.insert_data(to database)
                d.format_data(format original and filtered data for next time use)
        """
        original_data = f'{path}/ori_data.csv'
        filtered_data = f'{path}/new_data.csv'
        month_data = f'{path}/each_month_data.csv'
        sys_cre, num = self.get_latest_info()
        token = self.get_incident_handler_token2()
        offset_length = 0
        start_time = time.time()
        timeout = 600  # 10 mins
        today = datetime.now()
        formatted_today = today.strftime("%Y-%m-%d")
        if today.day == 28:
            new_offset = 0
            today_date_str = str(formatted_today)
            first_day_of_month = today.replace(day=1)
            first_date_str = str(first_day_of_month.strftime("%Y-%m-%d"))
            # get inc list from db
            db_inc_list = self.query_incs_by_dates(start_date=first_date_str, end_date=today_date_str)
            while True:
                running_time = time.time() - start_time
                if running_time > timeout:  # if running out of 10 mins, stop this func
                    logging.error("running time out")
                    return
                url = self.fetch_url_patch(first_date_str, new_offset)
                offset = self.get_inc_data(month_data, url, token)  # get the whole month data to csv
                new_offset += offset
                if new_offset >= 5000 or offset == 0:  # too much inc fetched or no more inc:
                    return True
                # 3.delete same inc in csv
                try:
                    df = pd.read_csv(month_data)
                except pd.errors.EmptyDataError:
                    return True
                df_filtered = df[~df['number'].isin(db_inc_list)]
                df_filtered.to_csv(month_data, index=False)
                if self.is_csv_empty(month_data):  # if csv file is empty, means no inc lost
                    logging.info("no new resolved incidents,finish the task")
                    continue
                self.data_handle_part(path, month_data, filtered_data)  # insert new inc data to db
                if offset < 500:
                    return True
        else:
            while True:
                running_time = time.time() - start_time
                if running_time > timeout:  # if running out of 10 mins, stop this func
                    logging.error("running time out")
                    return
                url = self.fetch_url(date_str=sys_cre, inc_num=num, offset=offset_length)
                offset = self.get_inc_data(original_data, url, token)
                offset_length += offset
                if offset_length >= 5000 or offset == 0:
                    return True
                if self.is_csv_empty(original_data):  # if csv file is empty, means no auto resolved case
                    logging.info("no new resolved incidents,finish the task")
                    return True
                self.data_handle_part(path, original_data, filtered_data)
                if offset < 500:
                    return True

    def data_handle_part(self, path, ori_csv, filtered_csv):
        self.inc_data_fetch(ori_csv, filtered_csv)
        time.sleep(1)
        self.inc_data_handle(filtered_csv)
        time.sleep(1)
        self.update_site(filtered_csv)
        self.insert_data(filtered_csv)
        self.format_csv(path, ori_csv, filtered_csv)
        time.sleep(3)
        return True

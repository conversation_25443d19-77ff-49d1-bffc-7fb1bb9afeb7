
# installed module
import logging
import re
# local file
from business.authentication.authentication import ServiceAccount, Vault
from models.ntx_models import ModelPrismCentral, ModelRetailNutanixVM, ModelPrismElement
from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
from business.generic.commonfunc import NutanixAPI, CommonRestCall
from business.distributedhosting.nutanix.nutanix import PrismCentral, PrismElement
import werkzeug.exceptions as flaskex
from business.benchmark.benchmark import Benchmark


class Automation():
    VM_ACP = {
        "linux": {
            "role_name": "IKEA-Linux-Operator",
            "acp_name": "IKEA-Linux-Operator-ACP-By-UP",
            "description": "IKEA-Linux-Operator-ACP-By-UP",
            "group": ["UG-GLINUX-CG@ELM-IT-SE-ELM".upper()],
            "vm_match": "-lx",
            "vm_not_match": None
        },
        "windows": {
            "role_name": "IKEA-Windows-Operator",
            "acp_name": "IKEA-Windows-Operator-ACP-By-UP",
            "description": "IKEA-Windows-Operator-ACP-By-UP",
            "group": ["UG-DH_TCSTeam-CG@INF-NTX-SE-ELM".upper()],
            "vm_match": "-nt",
            "vm_not_match": "NTXUSPHI-NT4017|RETRUSO-NT4017|NTXCNCHN-NT4017|NTXSGSNG-NT4017|NTXSEELM-NT4017"
        },
        "network": {
            "role_name": "IKEA-Networking-Operator",
            "acp_name": "IKEA-Networking-Operator-ACP-By-UP",
            "description": "IKEA-Networking-Operator-ACP-By-UP",
            "group": ["UG-Network_Admin_Employee-CG@INF-ACS-SE-ELM".upper(), "UG-Network_Admin_Consultant-CG@INF-ACS-SE-ELM".upper()],
            "vm_match": "-pl|-wl|-lc|-bb",
            "vm_not_match": None
        },
    }

    def __init__(self, pc=None, pe=None, sa=None, token=None, logger=logging) -> None:
        self.pc = pc
        self.tier = None
        self.domain = None
        if self.pc:
            _pc = ModelPrismCentral.query.filter_by(fqdn=self.pc).first()
            if _pc:
                self.tier = _pc.tier
                self.domain = _pc.domain
            else:
                _pc_wh = ModelWarehousePrismCentral.query.filter_by(fqdn=self.pc).first()
                if _pc_wh:
                    self.tier = _pc_wh.tier
                    self.domain = _pc_wh.domain
        self.pe = pe
        self.logger = logger
        self.token = token
        if sa:
            self.sa = sa
        else:
            _sa = ServiceAccount(usage="nutanix_pm")
            self.sa = _sa.get_service_account()
        self.ntx_api = NutanixAPI(pe=self.pe, username=self.sa['username'],
                                   password=self.sa['password'], logger=self.logger)
        self.rest = CommonRestCall(username=self.sa['username'], password=self.sa['password'], logger=self.logger)

    def reauth_pe_ad(self):
        self.logger.info("Start to renew authentication of PE.")
        vault = Vault(self.tier)
        bmk_id = (ModelPrismElement.query.filter_by(fqdn=self.pe).first() or ModelWarehousePrismElement.query.filter_by(fqdn=self.pe).first()).bmk_id #self.pe should be a fqdn like ikea.com
        upn_label = Benchmark().get_bmk_by_id(bmk_id=bmk_id).get('vault').get('dc_labels').get('ad_query_upn')
        res, sec = vault.get_secret(upn_label)
        if not res:
            raise Exception("Failed to get the username/password of A_Central-Production/AdQuery .")
        payload = {
            "name": "ikea",
            "domain": self.domain,
            "directoryUrl": f"ldaps://{self.domain}:636",
            "groupSearchType": "NON_RECURSIVE",
            "directoryType": "ACTIVE_DIRECTORY",
            "connectionType": "LDAP",
            "serviceAccountUsername": sec['username'],
            "serviceAccountPassword": sec['secret']
        }
        res, mes = self.ntx_api.call_pe_get(request_url='/authconfig/directories')
        if not mes:
            res, mes = self.ntx_api.call_pe_post(request_url='/authconfig/directories', payload=payload)
            return res, mes
        res, mes = self.ntx_api.call_pe_put(request_url='/authconfig/directories', payload=payload)
        return res, mes

    def dsc_acp(self, acp_scope: str = "all"):
        self.logger.info("Renewing access control policies now.")
        acp_scope = acp_scope.lower()
        if acp_scope == "all":
            self.logger.info(f"Renewing all the {','.join(self.VM_ACP.keys())} vms.")
            _acp_list = self.VM_ACP
        elif acp_scope in self.VM_ACP.keys():
            self.logger.info(f"Renewing all the {acp_scope} vms.")
            _acp_list = {acp_scope: self.VM_ACP[acp_scope]}
        else:
            self.logger.info(f"{acp_scope} is not in the scope of {','.join(self.VM_ACP.keys())}.")
            return
        _pc = PrismCentral(pc=self.pc, sa=self.sa, logger=self.logger)

        res, user_group_list = _pc.get_user_group_list()
        if not (res and user_group_list):
            return False, user_group_list

        res, vm_list = _pc.get_vm_list()
        if not (res and vm_list):
            return False, vm_list

        for _type, _scope in _acp_list.items():
            # 3 things need to be settle for this
            # 1st role : name/uuid
            # 2nd user group : name/uuid
            # 3rd acp  : name/version(can be whatever)
            res, role = _pc.get_role_list(filter=f"name=={_scope['role_name']}")
            # role come back as a list [{role1},{role2}]
            if not (res and role):
                self.logger.error("Can't get the role list or role list is empty, we will skip this ACP.")
                continue
            # get the group now
            self.logger.info(f"Filtering the group {_scope['group']} out of all groups.")

            ug = [{"name": _group["status"]["resources"]["display_name"],
                   "uuid": _group["metadata"]["uuid"]}
                  for _group in user_group_list if _group["status"]["resources"]["display_name"].upper()
                  in _scope["group"]]
            if not ug:
                self.logger.error("Can't find the related user group, we will skip this ACP.")
                continue

            self.logger.info(f"Collecting vm uuid from vm list for the ones that has the name matches {_scope['vm_match']}.")
            _vm_list = [{'name': vm['status']['name'], 'uuid': vm['metadata']['uuid']} for vm in vm_list if re.search(_scope['vm_match'], vm['status']['name'], re.I)]
            if _scope['vm_not_match'] and _vm_list:
                self.logger.info(f"Collected {len(_vm_list)} vm, now kick out the vms that matches {_scope['vm_not_match']}.")
                matched_vm_list = [_vm for _vm in _vm_list if not re.search(_scope['vm_not_match'], _vm['name'], re.I)]
            else:
                matched_vm_list = _vm_list
            self.logger.info(f"Collected {len(matched_vm_list)} vm, now creating ACP.")
            self.logger.info(f"Here is the vm list {matched_vm_list}.")

            res, acp = _pc.get_acp_list(filter=f"name=={_scope['acp_name']}")
            if not (res and acp):
                self.logger.info("Can't find the ACP, something must be wrong, let's try to create the ACP.")
                _pc.create_acp(name=_scope['acp_name'], description=_scope['description'], role=role[0], groups=ug, vm_uuid_list=[vm['uuid'] for vm in matched_vm_list])
            else:
                self.logger.info("Got the ACP, now let's do the update.")
                _pc.update_acp(name=_scope['acp_name'],
                               description=_scope['description'],
                               role=role[0], groups=ug,
                               vm_uuid_list=[vm['uuid'] for vm in matched_vm_list],
                               acp={"version": acp[0]["metadata"]["spec_version"], "uuid": acp[0]["metadata"]["uuid"]})

    @staticmethod
    def disable_hardware_virtualization(vmname, logger=logging):
        vm_detail = ModelRetailNutanixVM.query.filter_by(name=vmname).all()
        if not vm_detail:
            raise flaskex.InternalServerError(f"Cannot find VM{vmname} in DB.")
        vm_uuid = vm_detail[0].uuid
        pe = vm_detail[0].pe_name
        _pe = PrismElement(pe)
        logger.info(f"Let's power off {vmname} first")
        _pe.set_vm_power_state(vm_uuid, "off", vmname)
        payload = {
            "boot": {
                "uefi_boot": "true",
                "secure_boot": "false",
                "hardware_virtualization": "false"
            },
            "machine_type": "Q35"
        }
        logger.info("start disable virtualization!")
        task_uuid = _pe.update_vm(vm_uuid=vm_uuid, payload=payload)
        res = _pe.is_task_succeeded(task_uuid=task_uuid, retries=1)
        if not res:
            raise flaskex.InternalServerError("update failed")
        logger.info(f"Great, virtualization changed. Let's start VM {vmname} ")
        _pe.set_vm_power_state(vm_uuid, "on", vmname)
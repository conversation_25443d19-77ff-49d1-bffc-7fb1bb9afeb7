import request from '@/utils/request'
import axios from 'axios'
import { endpoint } from './endpoint'

export function GetMemoID(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/compen/get_memo_id`,config)
  return res
}
export function GetAvaliableCompen(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/compen/list_avaliable_compen`,config)
  return res
}
// list Gain Compen
export function ListGainCompen(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/compen/list_gain_compen`,config)
  return res
}
export function AddGainCompen(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/compen/add_gain_compen`,param.data,config)
  return res
}
export function UpdateGainCompen(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/compen/update_gain_compen`,param.data,config)
  return res
}
export function DeleteGainCompen(param) {
  var config = {
    headers: { 'Authorization': 'Bearer ' + param['token'] }
  };
  let res = request.post(`${endpoint}/compen/delete_gain_compen`, param.data, config)
  return res
}
// list Used Compen
export function ListUseCompen(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/compen/list_use_compen`,config)
  return res
}
export function AddUseCompen(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/compen/add_use_compen`,param.data,config)
  return res
}
export function UpdateUseCompen(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/compen/update_use_compen`,param.data,config)
  return res
}
export function DeleteUseCompen(param){
  var config = {
    headers: { 'Authorization': 'Bearer ' + param['token'] }
  };
  let res = request.post(`${endpoint}/compen/delete_use_compen`, param.data, config)
  return res
}



//users and group
export function EditUser(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res =  request.put(`${endpoint}/user`,param.data,config)
  return res
}

export function DeleteUser(param){

  let payload = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data   : param.data
  }
  let res =  request.delete(`${endpoint}/user`, payload)
  return res
}

// Get/Add/Edit/Delete Group

export function GetGroupList(token , download = false) {
  if(download){
    var config = {
      headers: {'Authorization': 'Bearer ' + token},
      responseType:'blob'
    };
    let res =  request.get(`${endpoint}/group?download=true`,config)
    return res
  }
  else{
    var config = {
      headers: {'Authorization': 'Bearer ' + token}
    };
    let res =  request.get(`${endpoint}/group`,config)
    return res
  }
}

export function AddGroup(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/group`,param.data,config)
  return res
}

export function EditGroup(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res =  request.put(`${endpoint}/group`,param.data,config)
  return res
}

export function DeleteGroup(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res =  request.delete(`${endpoint}/group`,{data:{payload:param.data,headers:config}})
  return res
}

export function GetUserRole(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res =  request.get(`${endpoint}/user/${param.user_id}`, config)
  return res
}

export function UpdateUserRole(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res =  request.put(`${endpoint}/user/role`, param.data, config)
  return res
}
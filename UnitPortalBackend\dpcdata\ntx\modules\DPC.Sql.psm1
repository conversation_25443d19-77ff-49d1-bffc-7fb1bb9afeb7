#Requires -Modules SqlServer
function Backup-DpcDatabase(){
    param(
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        [System.Security.SecureString] $SecPword = ConvertTo-SecureString -String $($Vars.DB.Pword) -AsPlainText -Force
        $Hide = Backup-SqlDatabase -ServerInstance $Vars.DB.INstance `
                                   -Database $Vars.DB.Name `
                                   -Credential $(New-Object -TypeName System.Management.Automation.PSCredential -ArgumentList @($Vars.DB.Username, $SecPword))
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Select-DhRetailNtxPc(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_retail_ntx_pc]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}

function Select-DhServiceAccount(){
    param (
        [Parameter(Mandatory = $true)] $Vars,
        [string]                       $Usage
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_service_account] $(if ($Usage) {
                               "WHERE usage = '$Usage'"})"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Select-DhOneView(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_retail_ntx_oneview]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Select-DhRetailNtxPe(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_retail_ntx_pe]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Select-DhRetailNtxVm(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_retail_ntx_vm]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Select-DhRetailNtxHost(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_retail_ntx_host]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Select-DhRetailNtxState(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_retail_ntx_state]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Select-DhRetailNtxSizing(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_retail_ntx_sizing]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Write-DhData(){
    param (
        [Parameter(Mandatory = $true)] $Vars,
                              [string] $Str
    )
    try {
        Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "Running SQL: $Str" -DumpFile $Global:DumpFile
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query $Str
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Update-Table-DhRetailNtxPc-ByFqdn(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_retail_ntx_pc] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "fqdn") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody = $StrBody.TrimEnd(", ")
        $StrEnd  = " WHERE [fqdn] = '$($Item.fqdn)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
        #$UpdateStr
    }
}
function Insert-Table-DhRetailNtxPe(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "INSERT INTO [dbo].[dh_retail_ntx_pe] ("
        $StrCols = ""
        $StrVals = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            $StrCols += "[" + $_ + "], "
            $StrVals += "'" + $Item.$_ + "', "
        }
        $StrCols   = $StrCols.TrimEnd(", ")
        $StrVals   = $StrVals.TrimEnd(", ")
        $InsertStr = $StrHead + $StrCols + ") VALUES (" + $StrVals + ");"
        Write-DhData -Vars $Vars -Str $InsertStr
        #$InsertStr
    }
}
function Update-Table-DhRetailNtxPe-ByFqdn(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_retail_ntx_pe] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "fqdn") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody   = $StrBody.TrimEnd(", ")
        $StrEnd    = " WHERE [fqdn] = '$($Item.fqdn)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
        #$UpdateStr
    }
}
function Insert-Table-DhRetailNtxHost(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "INSERT INTO [dbo].[dh_retail_ntx_host] ("
        $StrCols = ""
        $StrVals = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            $StrCols += "[" + $_ + "], "
            $StrVals += "'" + $Item.$_ + "', "
        }
        $StrCols   = $StrCols.TrimEnd(", ")
        $StrVals   = $StrVals.TrimEnd(", ")
        $InsertStr = $StrHead + $StrCols + ") VALUES (" + $StrVals + ");"
        Write-DhData -Vars $Vars -Str $InsertStr
        #$InsertStr
    }
}
function Update-Table-DhRetailNtxHost-ByName(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_retail_ntx_host] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "name") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody = $StrBody.TrimEnd(", ")
        $StrEnd  = " WHERE [name] = '$($Item.name)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
        #$UpdateStr
    }
}
function Insert-Table-DhRetailNtxVm(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "INSERT INTO [dbo].[dh_retail_ntx_vm] ("
        $StrCols = ""
        $StrVals = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            $StrCols += "[" + $_ + "], "
            $StrVals += "'" + $Item.$_ + "', "
        }
        $StrCols   = $StrCols.TrimEnd(", ")
        $StrVals   = $StrVals.TrimEnd(", ")
        $InsertStr = $StrHead + $StrCols + ") VALUES (" + $StrVals + ");"
        Write-DhData -Vars $Vars -Str $InsertStr
    }
}
function Update-Table-DhRetailNtxVm-ByName(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_retail_ntx_vm] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "name") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody = $StrBody.TrimEnd(", ")
        $StrEnd  = " WHERE [name] = '$($Item.name)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
    }
}
function Update-Table-DhRetailNtxVm-ByUuid(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_retail_ntx_vm] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "uuid") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody = $StrBody.TrimEnd(", ")
        $StrEnd  = " WHERE [uuid] = '$($Item.uuid)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
    }
}
function Insert-Table-DhRetailNtxState(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "INSERT INTO [dbo].[dh_retail_ntx_state] ("
        $StrCols = ""
        $StrVals = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            $StrCols += "[" + $_ + "], "
            $StrVals += "'" + $Item.$_ + "', "
        }
        $StrCols   = $StrCols.TrimEnd(", ")
        $StrVals   = $StrVals.TrimEnd(", ")
        $InsertStr = $StrHead + $StrCols + ") VALUES (" + $StrVals + ");"
        Write-DhData -Vars $Vars -Str $InsertStr
    }
}
function Update-Table-DhRetailNtxState-ByPeUuidAndDate(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_retail_ntx_state] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "pe_uuid" -and $_ -ne "date") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody   = $StrBody.TrimEnd(", ")
        $StrEnd    = " WHERE [pe_uuid] = '$($Item.pe_uuid)' AND [date] = '$($Item.date)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
    }
}
function Insert-Table-DhRetailNtxSizing(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "INSERT INTO [dbo].[dh_retail_ntx_sizing] ("
        $StrCols = ""
        $StrVals = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            $StrCols += "[" + $_ + "], "
            $StrVals += "'" + $Item.$_ + "', "
        }
        $StrCols   = $StrCols.TrimEnd(", ")
        $StrVals   = $StrVals.TrimEnd(", ")
        $InsertStr = $StrHead + $StrCols + ") VALUES (" + $StrVals + ");"
        Write-DhData -Vars $Vars -Str $InsertStr
    }
}
function Update-Table-DhRetailNtxSizing-ByPeUuid(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_retail_ntx_sizing] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "pe_uuid") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody   = $StrBody.TrimEnd(", ")
        $StrEnd    = " WHERE [pe_uuid] = '$($Item.pe_uuid)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
    }
}
function Update-Table-DhRetailNtxSizing-ByPeName(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_retail_ntx_sizing] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "pe_name") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody   = $StrBody.TrimEnd(", ")
        $StrEnd    = " WHERE [pe_name] = '$($Item.pe_name)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
    }
}

function Select-DhWhNtxPc(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_wh_ntx_pc]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}

function Update-Table-DhWhNtxPc-ByFqdn(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_wh_ntx_pc] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "fqdn") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody = $StrBody.TrimEnd(", ")
        $StrEnd  = " WHERE [fqdn] = '$($Item.fqdn)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
        #$UpdateStr
    }
}
function Select-DhWhNtxPe(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_wh_ntx_pe]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Insert-Table-DhWhNtxPe(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "INSERT INTO [dbo].[dh_wh_ntx_pe] ("
        $StrCols = ""
        $StrVals = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            $StrCols += "[" + $_ + "], "
            $StrVals += "'" + $Item.$_ + "', "
        }
        $StrCols   = $StrCols.TrimEnd(", ")
        $StrVals   = $StrVals.TrimEnd(", ")
        $InsertStr = $StrHead + $StrCols + ") VALUES (" + $StrVals + ");"
        Write-DhData -Vars $Vars -Str $InsertStr
        #$InsertStr
    }
}
function Update-Table-DhWhNtxPe-ByFqdn(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_wh_ntx_pe] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "fqdn") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody   = $StrBody.TrimEnd(", ")
        $StrEnd    = " WHERE [fqdn] = '$($Item.fqdn)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
        #$UpdateStr
    }
}
function Select-DhWhNtxVm(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_wh_ntx_vm]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Insert-Table-DhWhNtxVm(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "INSERT INTO [dbo].[dh_wh_ntx_vm] ("
        $StrCols = ""
        $StrVals = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            $StrCols += "[" + $_ + "], "
            $StrVals += "'" + $Item.$_ + "', "
        }
        $StrCols   = $StrCols.TrimEnd(", ")
        $StrVals   = $StrVals.TrimEnd(", ")
        $InsertStr = $StrHead + $StrCols + ") VALUES (" + $StrVals + ");"
        Write-DhData -Vars $Vars -Str $InsertStr
    }
}
function Update-Table-DhWhNtxVm-ByUuid(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_wh_ntx_vm] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "uuid") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody = $StrBody.TrimEnd(", ")
        $StrEnd  = " WHERE [uuid] = '$($Item.uuid)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
    }
}
function Select-DhWhNtxHost(){
    param (
        [Parameter(Mandatory = $true)] $Vars
    )
    try {
        $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                               -Database $Vars.DB.Name `
                               -Username $Vars.DB.Username `
                               -Password $Vars.DB.Pword `
                               -TrustServerCertificate:$true `
                               -Query "SELECT * FROM [dbo].[dh_wh_ntx_host]"
        return $Query
    }
    catch {
        Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message $_ -DumpFile $Global:DumpFile
        return $null
    }
    return $null
}
function Insert-Table-DhWhNtxHost(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "INSERT INTO [dbo].[dh_wh_ntx_host] ("
        $StrCols = ""
        $StrVals = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            $StrCols += "[" + $_ + "], "
            $StrVals += "'" + $Item.$_ + "', "
        }
        $StrCols   = $StrCols.TrimEnd(", ")
        $StrVals   = $StrVals.TrimEnd(", ")
        $InsertStr = $StrHead + $StrCols + ") VALUES (" + $StrVals + ");"
        Write-DhData -Vars $Vars -Str $InsertStr
        #$InsertStr
    }
}
function Update-Table-DhWhNtxHost-ByName(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_wh_ntx_host] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "name") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody = $StrBody.TrimEnd(", ")
        $StrEnd  = " WHERE [name] = '$($Item.name)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
        #$UpdateStr
    }
}

function Update-Table-DhRetailNtxHost-BySn(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_retail_ntx_host] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "sn") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody = $StrBody.TrimEnd(", ")
        $StrEnd  = " WHERE [sn] = '$($Item.sn)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
        #$UpdateStr
    }
}

function Update-Table-DhWhNtxHost-BySn(){
    param(
        $Vars,
        $Collection
    )
    foreach ($Item in $Collection) {
        $StrHead = "UPDATE [dbo].[dh_wh_ntx_host] SET "
        $StrBody = ""
        ($Item | Get-Member -MemberType NoteProperty).Name | ForEach-Object {
            if ($_ -ne "sn") {
                $Col = $_
                $Val = $Item.$_
                $StrBody += "[" + $Col + "] = '" + $Val + "', "
            }
        }
        $StrBody = $StrBody.TrimEnd(", ")
        $StrEnd  = " WHERE [sn] = '$($Item.sn)';"
        $UpdateStr = $StrHead + $StrBody + $StrEnd
        Write-DhData -Vars $Vars -Str $UpdateStr
        #$UpdateStr
    }
}
<template>
    <div class="app-container">
      <div class="filter-container">
        <el-popover placement="right-start"  width="200" trigger="click"  ref="pop_operations" @show="toggleOperationarrow" @hide="toggleOperationarrow">
          <div class="trends_btn_wrap">
              <el-checkbox-group v-model="checkedOptions" >
                <el-checkbox style="margin-top: 5px;"
                    v-for="item in headerOptions" :key="item.label" :label="item" :value="item.value" :checked="item.checked" class="checkbox-line" @change="handleChecked($event,item)"
                  >{{item.label}}</el-checkbox>
                </el-checkbox-group>
            </div>
        </el-popover>
        <el-row :gutter="5" >
          <el-col :span="3" style='float:left;'>
            <!-- <el-button v-popover:pop_column  style='float:left;width:35%'  size="small">Column<i class="el-icon-arrow-down el-icon--right"></i></el-button>     -->
            <!-- <el-button v-popover:pop_operations style='float:left;width:45%' size="small">Operations<i class="el-icon-arrow-down el-icon--right"></i></el-button> -->
            <el-button-group>
            <el-button v-popover:pop_operations  size="small" >Operations<i :class="handleoperarrow ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i></el-button>
            <!-- <el-button v-popover:pop_column size="small" >Column <i class="el-icon-arrow-down "></i></el-button> -->
          </el-button-group>
          </el-col>
          <el-col :span="4" :offset="9">
            <el-select    size="large"
              v-model="filter.selected_pc" multiple collapse-tags placeholder="Filter the PC" style="width:100%;" >
              <el-option v-for="item in filter.pc_list" :key="item" :label="item" :value="item" style="font-size: large;"/>
            </el-select>
          </el-col>
          <el-col :span="4" >
            <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " @keyup.enter.native="filter_vm_list" size="large"/>
          </el-col>
          <el-col :span="2" style='float:right;'>
            <el-button style='float:right;width:100%' class="filter-item"  type="success" size="large" @click="download_vm_list">
              Download
            </el-button>
          </el-col>
          <el-col :span="2" style='float:right;'>
            <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="filter_vm_list">
              Search
            </el-button>
          </el-col>
        </el-row>
      </div>
      <el-table :key="tableKey" 
        v-loading="listLoading" 
        :data="current_list" 
        border 
        fit 
        ref="vm_table"
        highlight-current-row 
        style="width: 100%;" 
        @sort-change="sortChange"  >

        <el-table-column label="Name" prop="name" align="center" min-width="10%" sortable="custom" >
          <template slot-scope="{row}">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>
  
        <el-table-column label="PC" class-name="status-col" min-width="10%" align="center" sortable="custom" prop="prism" >
          <template slot-scope="{row}">
            <span >{{ row.prism.toUpperCase() }}</span>
          </template>
        </el-table-column>
  
        <el-table-column label="PE" min-width="10%" align="center"  sortable="custom" prop="pe_name" >
          <template slot-scope="{row}">
            <span >{{ row.pe_name.toUpperCase() }}</span>
          </template>
        </el-table-column>
  
        <el-table-column label="RAM" min-width="4%" align="center" sortable="custom" prop="memory" >
          <template slot-scope="{row}">
            <span >{{ row.memory }}</span>
          </template>
        </el-table-column>
  
        <el-table-column label="CPU" align="center" min-width="4%" sortable="custom" prop="cpu_core" >
          <template slot-scope="{row}">
            <span >{{ row.cpu_core }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Server Type" min-width="8%" align="center"  sortable="custom" prop="type" >
          <template slot-scope="{row}">
            <span>{{ row.type }}</span>
          </template>
        </el-table-column>
        <el-table-column label="OS Type" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="os" >
          <template slot-scope="{row}">
            <span class="bigger_font">{{ row.os }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Power State" class-name="status-col" min-width="4%" align="center" >
          <template slot-scope="{row}">
            <svg-icon :icon-class=row|powerstate_filter class-name="card-panel-icon" style="font-size: 25px"/>
          </template>
        </el-table-column>
        <el-table-column label="Last Update" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="last_update" >
          <template slot-scope="{row}">
            <span class="bigger_font">{{ row.last_update }}</span>
          </template>
        </el-table-column>
      </el-table>
  
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" /> 
  
    </div>
  </template>
  
  <script>
  import {GetVMList} from '@/api/nutanix'
  import waves from '@/directive/waves' // waves directive
  import { parseTime } from '@/utils'
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination
  import {customized_search} from '@/utils/commonfunc'

  const prismoptions = [
    { key: 'EU', display_name: 'SSP-EU-NTX.IKEA.COM' },
    { key: 'CN', display_name: 'SSP-CHINA-NTX.IKEA.COM' },
    { key: 'APAC', display_name: 'SSP-APAC-NTX.IKEA.COM' },
    { key: 'NA', display_name: 'SSP-NA-NTX.IKEA.COM' },
    { key: 'RU', display_name: 'SSP-RUSSIA-NTX.IKEA.COM' },
    { key: 'DT', display_name: 'SSP-DT-NTX.IKEADT.COM' },
    { key: 'PPE', display_name: 'SSP-PPE-NTX.IKEA.COM' },
  ]
  const peoptions =[
    { pc: 'SSP-EU-NTX.IKEA.COM', pe:['RETDE068-NXC000.ikea.com','RETSEHBG-NXC000.ikea.com','RETDE124-NXC000.ikea.com','RETFR134-NXC000.ikea.com'] },
    { pc: 'SSP-CHINA-NTX.IKEA.COM', pe:['RETCN856-NXC000.ikea.com','RETCNCHN-NXC000.ikea.com','RETCN644-NXC000.ikea.com','RETCNSOS-NXC000.ikea.com'] },
    { pc: 'SSP-APAC-NTX.IKEA.COM', pe:['RETKR373-NXC000.ikea.com','RETJP509-NXC000.ikea.com','RETKR522-NXC000.ikea.com','RETKRSO-NXC000.ikea.com'] },
    { pc: 'SSP-NA-NTX.IKEA.COM', pe:['RETUS100-NXC000.ikea.com','RETCA040-NXC000.ikea.com','RETUS209-NXC000.ikea.com','RETUS374-NXC000.ikea.com'] },
    { pc: 'SSP-RUSSIA-NTX.IKEA.COM', pe:['RETRU401','RETRU403','RETRU551','RETRU513'] },
    { pc: 'SSP-DT-NTX.IKEADT.COM', pe:['RETSEELM-NXC000.ikea.com'] },
    { pc: 'SSP-PPE-NTX.IKEA.COM', pe:['RETSE999-NXC000.ikea.com','RETCN888-NXC000.ikea.com'] },
  ]
  
  
  const peTypeKeyValue = peoptions.reduce((acc, cur) => {
    acc[cur.pc] = cur.pe
    return acc
  }, {})
  export default {
    name: 'PETable',
    components: { Pagination },
    directives: { waves },
    filters: {
      powerstate_filter(row){
          if(row['power_state'] === "on"){
            return "poweron"
          }
          else if(row['power_state'] === "off"){
            return "poweroff"
          }
          else{
            return "powerunknow"
          }
        },
  
    },
    data() {
      const validateTime =(rule, value, callback)=>{
        if(this.temp.datatimepickerdisabled){
          callback()
        }
        let currentdate = new Date()
        let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
        if (value < utctime){
          callback(new Error('Schedule date must be later then now.'))
        }else{
          let currnettime = utctime.getTime()
          let scheduletime = value.getTime()
          let timediff = scheduletime-currnettime
          if(timediff/1000/60 < 5){
            callback(new Error('Schedule date is too close from now.'))
          }else{
            callback()
          }
        }
        callback()
      }
      return {
        tableKey: 0,
        all_vm_list: null,
        filtered_list: null,
        current_list: null,
        page_list: null,
        filter:{
          pc_list:[],
          selected_pc:[],
          fuzzy_string:"",
          hidecvm: false,
          showsiab: false,
          showwiab: false
        },
        listLoading: true,
        listQuery: {
          page: 1,
          limit: 20,
          cluster: '',
          prism: '',
          status: '',
          sort: '+id'
        },
        statusToShowEditButton:['Not Started'],
        statusToShowAbortButton:['In Progress'],
        statusToShowDeleteButton:['Not Started','Done','Error','Aborted'],
        statusOptions: ['Not Started','In Progress','Done','Error','Aborted'],
        prismoptions,
        peTypeKeyValue,
        sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
        // statusOptions: ['published', 'draft', 'deleted'],
        ShowCreationDate: false,
        temp: {
          id: '',
          timestamp: new Date(),
          cluster:'',
          prism: '',
          status: '',
          startnow: 1 ,
          datatimepickerdisabled:false,
          description: '',
          pmtype: 1
        },
        selectedrow:'',
        dialogFormVisible: false,
        dialogStatus: '',
        dialogPvVisible: false,
        logdata: [],
        rules: {
          prism: [{ required: true, message: 'prism is required', trigger: 'change' }],
          cluster: [{ required: true, message: 'cluster is required', trigger: 'change' }],
          timestamp: [{ type: 'date', required: true , trigger: 'change' , validator:validateTime}]
        },
        checkedOptions: [],
        headerOptions: [
          {
          label: 'Show Siab Only',
          value: 'showsiab',
          checked:false
          },{
          label: 'Show Wiab Only',
          value: 'showwiab',
          checked:false
          },{
          label: 'Hide CVM',
          value: 'hidecvm',
          checked:false
              }
        ],
        handleoperarrow: false,
      }
    },
    computed: {
      total() {
        if(this.filtered_list){
          return this.filtered_list.length
        }
        else{
            return 0
        }
      }
    },
    created() {
      this.get_vm_list()
    },
    methods: {
      togglecolumnarrow() {
        this.handlecolarrow = !this.handlecolarrow;
      },
      toggleOperationarrow() {
        this.handleoperarrow = !this.handleoperarrow;
      },
      handleClick() {
        this.$refs.pop_operations.doDestroy()
      },
      get_vm_list() {
        this.listLoading = true
        GetVMList(this.$store.getters.token).then(response => {
          this.all_vm_list = response.data
          this.filtered_list = this.all_vm_list
          let page = this.listQuery.page
          let limit = this.listQuery.limit
          let start , end
          if(page*limit>=this.total){
            start = (page-1)*limit
            end = this.total
          }
          else{
            start = (page-1)*limit
            end = page * limit
          }
          this.current_list = this.filtered_list.slice(start,end)
          this.listLoading = false
          let all_prism_list = this.all_vm_list.map((obj,index)=>{return obj['prism']})
          this.filter.pc_list = this.remove_duplicate(all_prism_list)
        })
        
      },

      remove_duplicate(arr) {
        //去除重复值
        const newArr = []
        arr.forEach(item => {
          if (!newArr.includes(item)) {
            newArr.push(item)
          }
        })
        return newArr
      },


      set_page(){
        // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total 
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)
      },
      handleChecked(val,item) {
        this.filter[item.value] = val
        this.filter_vm_list()
      },

      filter_vm_list(){
        //根据过滤条件筛选表格显示内容
        //screen the table as per filters
        this.listQuery.page = 1
        let temp_list
        //filter selected pc first.
        if (this.filter.selected_pc.length){
          //No filter, so select all
          temp_list = this.all_vm_list.filter((item)=>{
            return this.filter.selected_pc.includes(item['prism'].toLowerCase())
          })
          this.filtered_list = temp_list
        }
        else{
          this.filtered_list = this.all_vm_list
        }
        
        //filter if only cvm
        if(this.filter.hidecvm){
          temp_list = this.filtered_list.filter((item)=>{
              if (item['is_cvm'] != 'Y') {
              return true
            }
          })
          this.filtered_list = temp_list
        }
        if (this.filter.showwiab) {
            temp_list = this.filtered_list.filter((item)=>{
                if (item['name'].startsWith('DS')) {
                return true
              }
            })
            this.filtered_list = temp_list
        }
        if (this.filter.showsiab) {
            temp_list = this.filtered_list.filter((item)=>{
                if (!item['name'].startsWith('DS')) {
                return true
              }
            })
            this.filtered_list = temp_list
        }
        if(this.filter.fuzzy_string.trim().length){
          let temp_list = this.filtered_list
          let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
          for(let fuzzy of fuzzy_list){
            fuzzy = fuzzy.toString().toLowerCase()
            temp_list = temp_list.filter((k)=>{
              if( customized_search(k.name,fuzzy)||
                  customized_search(k.prism,fuzzy)||
                  customized_search(k.pe_name,fuzzy)||
                  customized_search(k.memory,fuzzy)||
                  customized_search(k.cpu_core,fuzzy)||
                  customized_search(k.type,fuzzy)||
                  customized_search(k.power_state,fuzzy)||
                  customized_search(k.last_update,fuzzy)
              ){
                return true
              }
            })
          }
          this.filtered_list = temp_list
        }
        this.set_page()
        this.$refs.vm_table.clearSort()
      },


      sortChange(data) {
        const { prop, order } = data
        if(order==null){
          this.sortChange({prop:'id',order:'ascending'})
          return 
        }
        let flag_num = order=="ascending" ? 1 : -1
        if(prop =='cpu_core'){ //it needs to be changed to intger if it's CPU
          this.filtered_list.sort((item1,item2)=>{
            let re = /^\d+$/
            let temp1 = item1[prop]
            let temp2 = item2[prop]
            if(re.test(temp1.trim()) && re.test(temp2.trim())){
              temp1 =parseInt( temp1.trim()) 
              temp2 =parseInt( temp2.trim()) 
            }
            return (temp1 > temp2) ? flag_num*1 : ((temp1 < temp2) ? flag_num*-1 : 0)
          })
        }
        else if(prop =='memory'){
          this.filtered_list.sort((item1,item2)=>{
            let re = /^(\d+)\D+GiB$/
            let temp1 = item1[prop]
            let temp2 = item2[prop]
            if(re.test(temp1.trim()) && re.test(temp2.trim())){
              temp1 = parseInt(temp1.trim().match(re)[1])
              temp2 = parseInt(temp2.trim().match(re)[1])
            }
            return (temp1 > temp2) ? flag_num*1 : ((temp1 < temp2) ? flag_num*-1 : 0)
          })
        }
        else{
          this.filtered_list.sort((item1,item2)=>(
            (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
          ))
        }

        this.set_page()
      },


      formatJson(filterVal) {
        return this.list.map(v => filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        }))
      },

    download_vm_list(){
      GetVMList(this.$store.getters.token,  true)
      .then((response)=>{
        const href = URL.createObjectURL(response.data);
        // create "a" HTML element with href to file & click
        const link = document.createElement('a');
        link.href = href;
        link.setAttribute('download', "vm_list"); //or any other extension
        document.body.appendChild(link);
        link.click();
        // clean up "a" element & remove ObjectURL
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      })
    },
    }
  }
  </script>
  <style lang="scss" scoped>
      .bigger_font {
        font-size: 16px;
      }
  </style>
$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Launch-Job(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Load data from the table dh_retail_ntx_pe to CollectionL
        #Create an empty array CollectionR which is used for store data from API
        #Create an empty array CollectionInsert which stores data needs to be insert into the table
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $CollectionL      = Select-DhRetailNtxPe -Vars $Vars
            $CollectionR      = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $CollectionInsert = @()
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $LastUpdate       = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        #Rolling call each PC
        $DhPCs | Foreach-Object -ThrottleLimit 10 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We're now working on '$($_.fqdn)'" -DumpFile $using:DumpFile
            $PC         = $_
            $DictR      = $using:CollectionR
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -PWord (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication" -DumpFile $using:DumpFile
                return
            }
            #List child PEs under the PC by calling PC
            if ($PrismCall1 = Rest-Prism-v1-List-Cluster -Fqdn $PC.Fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "$($PrismCall1.entities.count) PEs profile are available in '$($PC.Fqdn)'" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling '$($PC.Fqdn)' for list PEs" -DumpFile $using:DumpFile
                return
            }
            #List all hosts under the PC
            if ($PrismCall2 = Rest-Prism-v1-List-Host -Fqdn $PC.Fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "$($Prismcall2.entities.count) Nodes profile are available in '$($PC.Fqdn)'" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling '$($PC.Fqdn)' for list hosts" -DumpFile $using:DumpFile
            }
            #List all VMs under the PC
            if ($PrismCall3 = Rest-Prism-v1-List-Vm -Fqdn $PC.Fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "$($Prismcall3.entities.count) VMs profile are available" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to list VMs" -DumpFile $using:DumpFile
            }
            $CollectionRetry = @()
            $PEs             = $PrismCall1.entities
            foreach ($PE in $PEs) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "Assembling the $($PE.name) data object" -DumpFile $using:DumpFile
                $PeWorkloads  = $Prismcall3.entities | Where-Object {$_.clusterUuid -eq $PE.uuid}
                $PeVm         = $PeWorkloads | Where-Object {$_.vmName -notmatch '-CVM'}
                $PeCvm        = $PeWorkloads | Where-Object {$_.vmName -match '-CVM'}
                $PeNodes      = $Prismcall2.entities | Where-Object {$_.clusterUuid -eq $PE.uuid}
                $AhvVersion   = $PeNodes.hypervisorFullName | Get-Unique
                $TotalMemory  = [int][Math]::Ceiling(($PeNodes.memoryCapacityInBytes | Measure-Object -Sum).Sum / [Math]::Pow(1024,3))
                $TotalStorage = [int][Math]::Ceiling([int64]($PE.usageStats.'storage.capacity_bytes') / [Math]::Pow(1024,3))
                $TotalCpu     = ($PeNodes.numCpuCores | Measure-Object -Sum).Sum
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "$($PeVm.count) VMs is found on $($PE.name)" -DumpFile $using:DumpFile
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "$($PeCvm.count) CVMs is found on $($PE.name)" -DumpFile $using:DumpFile
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "AHV version $AhvVersion is found on $($PE.name)" -DumpFile $using:DumpFile
                $R = [PSCustomObject]@{
                    'name'          = $PE.name.ToUpper()
                    'fqdn'          = $PE.name.ToLower() + "." + $PC.domain
                    'country_code'  = Resolve-CountryCode -Object $PE.name
                    'site_code'     = Resolve-SiteCode -Object $PE.name
                    'node_number'   = $PE.numNodes
                    'vm_number'     = $PeVm.count
                    'cvm_number'    = $PeCvm.count
                    'aos_version'   = $PE.version
                    'ahv_version'   = $AhvVersion
                    'uuid'          = $PE.uuid
                    'status'        = 'Running'
                    'pc_id'         = $PC.id
                    'prism'         = $PC.fqdn
                    'total_memory'  = $TotalMemory
                    'total_storage' = $TotalStorage
                    'total_cpu'     = $TotalCpu
                    'ncc_version'   = $PE.nccVersion
                    'last_update'   = $using:LastUpdate
                }
                if (0 -eq $R.total_storage) {
                    Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "We've found invalid storage capacity '0 GiB' for the PE '$($R.name)', will retry" -DumpFile $using:DumpFile
                    $CollectionRetry += $R
                    continue
                }
                $DictR.Add($R)
            }
            if ($CollectionRetry.Count) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We need to retry for '$($CollectionRetry.Count)' PEs" -DumpFile $using:DumpFile
                foreach ($Retry in $CollectionRetry) {
                    Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "Retry to fetch data from '$($Retry.fqdn)'" -DumpFile $using:DumpFile
                    if ($PrismCall = Rest-Prism-v1-Get-Cluster -Fqdn $Retry.fqdn -Auth $Auth) {
                        Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We've got data from '$($Retry.fqdn)'"
                        $Retry.total_storage = [int][Math]::Ceiling([int64]($PrismCall.usageStats.'storage.capacity_bytes') / [Math]::Pow(1024,3))
                        continue
                    }
                    Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to fetch data from '$($Retry.fqdn)', need to check the PE '$($Retry.name)'"
                    $Retry.status = "Unreachable"
                }
                $CollectionRetry | ForEach-Object {
                    $DictR.Add($_)
                }
            }
        }
        # if (0 -eq $CollectionR.Count) {
        #     Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "There is no data need to deal with. Exit" -DumpFile $DumpFile
        #     Exit 0
        # }
        #Add new data into the array CollectionInsert and exsiting data into the array CollectionUpdate
        $CollectionR | ForEach-Object {
            if ($_.fqdn -notin $CollectionL.fqdn) {
                $CollectionInsert += $_
            }else {
                $CollectionUpdate.Add($_)
            }
        }
        #Parallely deal with the situation of the exiting reocrds are not found in the API return, may caused by the PC is temporarily unavailable, or the PE is temporarily unreachable
        #Why parallelizing? When the PC service is unavailable, there are dozens or hundreds of PEs need to be checked
        $CollectionL | ForEach-Object -ThrottleLimit 10 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $DictR           = $using:CollectionR
            $DictUpdate      = $using:CollectionUpdate
            $using:ModuleItems | Foreach-Object {
                Import-Module -Name $_.VersionInfo.FileName -DisableNameChecking:$true -Force
            }
            if ($_.fqdn -notin $DictR.fqdn -and $_.status -ne "Decommissioned") {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "The PE '$($_.fqdn)' is not found in API return, we will re-fetch data for itself" -DumpFile $using:DumpFile
                $L = [PSCustomObject]@{
                    'name'          = $_.name
                    'fqdn'          = $_.fqdn
                    'country_code'  = $_.country_code
                    'site_code'     = $_.site_code
                    'node_number'   = 0
                    'vm_number'     = 0
                    'cvm_number'    = 0
                    'aos_version'   = 'NA'
                    'ahv_version'   = 'NA'
                    'uuid'          = $_.uuid
                    'status'        = 'Unreachable'
                    'pc_id'         = $_.pc_id
                    'prism'         = $_.prism
                    'total_memory'  = $null
                    'total_storage' = $null
                    'total_cpu'     = $null
                    'ncc_version'   = 'NA'
                    'last_update'   = $using:LastUpdate
                }
                $PC         = $using:DhPCs | Where-Object {$_.id -eq $L.pc_id } | Select-Object -First 1
                $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
                $Auth       = Get-Base64Auth -Username $SvcAccount.username -PWord (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
                if(!(Test-NetConnection -ComputerName $L.fqdn -Port 9440).TcpTestSucceeded) {
                    Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "The PE '$($L.fqdn)' is unreachable" -DumpFile $using:DumpFile
                    $DictUpdate.Add($L)
                    return
                } else {
                    $L.status = "Running"
                }
                if ($PeCall1 = Rest-Prism-v1-Get-Cluster -Fqdn $L.fqdn -Auth $Auth) {
                    $L.node_number = $PeCall1.numNodes
                    $L.aos_version = $PeCall1.version
                    $L.ncc_version = $PeCall1.nccVersion
                } else {
                    Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to getting cluster profile of '$($L.fqdn)'"
                }
                if ($PeCall2 = Rest-Prism-v1-List-Host -Fqdn $L.fqdn -Auth $Auth) {
                    $PeNodes         = $PeCall2.entities
                    $L.ahv_version   = $PeNodes.hypervisorFullName | Sort-Object | Get-Unique
                    $L.total_memory  = [int][Math]::Ceiling(($PeNodes.memoryCapacityInBytes | Measure-Object -Sum).Sum / [Math]::Pow(1024,3))
                    $L.total_storage = [int][Math]::Ceiling([int64]($PE.usageStats.'storage.capacity_bytes') / [Math]::Pow(1024,3))
                    $L.total_cpu     = ($PeNodes.numCpuCores | Measure-Object -Sum).Sum
                } else {
                    Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to getting node profile of '$($L.fqdn)'"
                }
                if ($PeCall3 = Rest-Prism-v1-List-Vm -fqdn $L.fqdn -Auth $Auth) {
                    $PeWorkloads  = $PeCall3.entities
                    $PeVm         = $PeWorkloads | Where-Object {$_.vmName -notmatch '-CVM'}
                    $PeCvm        = $PeWorkloads | Where-Object {$_.vmName -match '-CVM'}
                    $L.vm_number  = $PeVm.Count
                    $L.cvm_number = $PeCvm.Count
                } else {
                    Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to getting VM profile of '$($L.fqdn)'"
                }
                $DictUpdate.Add($L)
            }
        }
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pe]" -DumpFile $DumpFile
        Insert-Table-DhRetailNtxPe -Vars $Vars -Collection $CollectionInsert
        Update-Table-DhRetailNtxPe-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job
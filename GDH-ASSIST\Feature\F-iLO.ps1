function Get-NtnxNodePowerState () {
    <#
    .SYNOPSIS
    Get node power state via iLO.
    
    .DESCRIPTION
    Get node power state via iLO, if iLO credentials are not provided, 
    using GST account.
    
    .PARAMETER Node
    Standard Nutanix node name, e.g. retcn888-nx7001. If provided,
    the function will transfer iLO address from the node name,
    e.g. retcn888-nx7001oob.ikea.com.
    
    .PARAMETER iLOAddress
    Fqdn or IP address for iLO is accepted. It's conflict with Node parameter.
    
    .PARAMETER iLOUsername
    Either local iLO username or GST account username. If using GST account,
    the function will switch UPN account to SAM account for better iLO authentication 
    performance. Tier parameter is required for GST account.
    
    .PARAMETER iLOPassword
    Password for iLO local account or GST account.
    
    .PARAMETER Tier
    Required for GST account, the tier of the account. Default is PROD.
    Others are D2, DT, PPE.
    
    .EXAMPLE
    Get-NtnxNodePowerState -iLOAddress retcn888-nx7001oob.ikea.com -iLOUsername "Administrator" -iLOPassword '********'
    ------------------------------------------------
    Using Fqdn to get node power state with iLO local account

    .EXAMPLE
    Get-NtnxNodePowerState -iLOAddress retcn888-nx7001oob.ikea.com -Tier PPE
    ------------------------------------------------
    Using Fqdn to get node power state with GST account

    .EXAMPLE
    Get-NtnxNodePowerState -Node retcn888-nx7001 -iLOUsername "Administrator" -iLOPassword '********'
    ------------------------------------------------
    Using node name to get node power state with iLO local account

    .EXAMPLE
    Get-NtnxNodePowerState -Node retcn888-nx7001 -Tier PPE
    ------------------------------------------------
    Using node name to get node power state with GST account
    
    .NOTES
    General notes
    #>
    param(
        [Parameter(Position = 0, Mandatory = $true, ParameterSetName = "Node")]
        [string] $Node,

        [Parameter(Position = 0, Mandatory = $true, ParameterSetName = "ILO")]
        [string] $iLOAddress,

        [string] $iLOUsername,
        [string] $iLOPassword,
        [string][ValidateSet("D2","DT","PPE","PROD")] $Tier = "PROD"
    )

    # Test if iLO credentials are provided, if not, using GST account
    if (!$iLOUsername -or !$iLOPassword) {
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "No iLO credentials provided, using GST account"
        $GstAccount = Read-GstAccount -Tier $Tier
        $User       = ($GstAccount.username -split "@")[0]
        # switch UPN account to SAM account for better iLO authentication performance
        switch ($Tier) {
            "D2" {
                $iLOUsername = "ikead2\$User"
            }
            "DT" {
                $iLOUsername = "ikeadt\$User"
            }
            Default {
                $iLOUsername = "ikea\$User"
            }
        }
        $iLOPassword = $GstAccount.password
    }

    # Switch tier to domain
    switch ($Tier) {
        "D2" { $Domain = "ikead2.com" }
        "DT" { $Domain = "ikeadt.com" }
        Default { $Domain = "ikea.com" }
    }
    
    # get standard iLO fqdn if node name provided
    if ($Node) {
        $iLOAddress = $Node + "oob." + $Domain
    }

    $Reply = Rest-iLO-Get-Powerstate -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    if ($Reply) {
        return $Reply.PowerState
    }
}

function Start-NtnxNodeViaIlo () {
    <#
    .SYNOPSIS
    Start node(s) via iLO.

    .DESCRIPTION
    Start node(s) via iLO, if iLO credentials are not provided,
    using GST account.

    .PARAMETER Node
    Array, more than one node name can be provided. If provided,
    the function will transfer to iLO fqdn from the node name, only 
    standard Nutanix node name is accepted, e.g. retcn888-nx7001.
    It will transfer to iLO fqdn, e.g. retcn888-nx7001oob.ikea.com.

    .PARAMETER iLOAddress
    Either Fqdn or IP address for iLO is accepted. It's conflict 
    with Node parameter.

    .PARAMETER iLOUsername
    Either local iLO username or GST account username. If using GST 
    account, the function will switch UPN account to SAM account for 
    better iLO authentication

    .PARAMETER iLOPassword
    Password for iLO local account or GST account.

    .PARAMETER Tier
    Required for GST account, the tier of the account. Default is PROD.
    Others are D2, DT, PPE.

    .EXAMPLE
    Start-NtnxNodeViaIlo -iLOAddress retcn888-nx7001oob.ikea.com -iLOUsername "Administrator" -iLOPassword '********'
    ------------------------------------------------
    Using Fqdn to start node with iLO local account

    .EXAMPLE
    Start-NtnxNodeViaIlo -iLOAddress retcn888-nx7001oob.ikea.com -Tier PPE
    ------------------------------------------------
    Using Fqdn to start node with GST account, default Tier is PROD

    .EXAMPLE
    Start-NtnxNodeViaIlo -iLOAddress retcn888-nx7001oob.ikea.com, retcn888-nx7002oob.ikea.com -iLOUsername "Administrator" -iLOPassword '********'
    ------------------------------------------------
    Using Fqdn to start multiple nodes with iLO local account

    .EXAMPLE
    Start-NtnxNodeViaIlo -Node retcn888-nx7001 -iLOUsername "Administrator" -iLOPassword '********'
    ------------------------------------------------
    Using node name to start node with iLO local account

    .EXAMPLE
    Start-NtnxNodeViaIlo -Node retcn888-nx7001 -Tier PPE
    ------------------------------------------------
    Using node name to start node with GST account, default Tier is PROD

    .EXAMPLE
    Start-NtnxNodeViaIlo -Node retcn888-nx7001,retcn888-nx7002 -iLOUsername "Administrator" -iLOPassword '********'
    ------------------------------------------------
    Using node name to start multiple nodes with iLO local account

    .NOTES
    General notes
    #>
    param(
        [Parameter(Position = 0, Mandatory = $true, ParameterSetName = "Node")]
        [array]$Node,

        [Parameter(Position = 0, Mandatory = $true, ParameterSetName = "ILO")]
        [array] $iLOAddress,

        [string] $iLOUsername,
        [string] $iLOPassword,
        [string][ValidateSet("D2","DT","PPE","PROD")] $Tier = "PROD"
    )

    # Test if iLO credentials are provided, if not, using GST account
    if (!$iLOUsername -or !$iLOPassword) {
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "No iLO credentials provided, using GST account"
        $GstAccount = Read-GstAccount -Tier $Tier
        $User       = ($GstAccount.username -split "@")[0]
        # switch UPN account to SAM account for better iLO authentication performance
        switch ($Tier) {
            "D2" {
                $iLOUsername = "ikead2\$User"
            }
            "DT" {
                $iLOUsername = "ikeadt\$User"
            }
            Default {
                $iLOUsername = "ikea\$User"
            }
        }
        $iLOPassword = $GstAccount.password
    }

    # Switch tier to domain
    switch ($Tier) {
        "D2" { $Domain = "ikead2.com" }
        "DT" { $Domain = "ikeadt.com" }
        Default { $Domain = "ikea.com" }
    }

    # if Node provided, stop the node(s)
    if ($Node) {
        foreach ($N in $Node) {
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Starting node $N via iLO"
            $Fqdn = $N + "oob." + $Domain
            $Reply = Rest-iLO-Reset-Power -iLOAddress $Fqdn -iLOUsername $iLOUsername -iLOPassword $iLOPassword -Type On

            # sleep 5 seconds to wait for the node to be stopped and verify the status
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Waiting for 5 seconds to verify the node status"
            Start-Sleep -Seconds 5
            $Status = Get-NtnxNodePowerState -iLOAddress $Address -iLOUsername $iLOUsername -iLOPassword $iLOPassword
            if ($Status -ne "On") {
                Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "Failed to start node $N, please check manually"
            } else {
                Write-ConsoleLog -Level SXED -FunctionName (Get-FunctionName) -Message "Node status ON, $N started successfully"
            }
        }
    }

    # if iLO provided, stop the node(s)
    if ($iLOAddress) {
        foreach ($iLO in $iLOAddress) {
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Starting node via iLO address $iLO"
            $Reply = Rest-iLO-Reset-Power -iLOAddress $iLO -iLOUsername $iLOUsername -iLOPassword $iLOPassword -Type On

            # sleep 5 seconds to wait for the node to be started and verify the status
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Waiting for 5 seconds to verify the node status"
            Start-Sleep -Seconds 5
            $Status = Get-NtnxNodePowerState -iLOAddress $iLO -iLOUsername $iLOUsername -iLOPassword $iLOPassword
            if ($Status -ne "On") {
                Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "Failed to start node $iLO, please check manually"
            } else {
                Write-ConsoleLog -Level SXED -FunctionName (Get-FunctionName) -Message "Node status ON, $iLO started successfully"
            }
        }
    }
}

function Stop-NtnxNodeViaIlo () {
    <#
    .SYNOPSIS
    Stop node(s) via iLO.
    
    .DESCRIPTION
    Stop node(s) via iLO, if iLO credentials are not provided,
    using GST account.
    
    .PARAMETER Node
    Array, more than one node name can be provided. If provided,
    the function will transfer to iLO fqdn from the node name, only 
    standard Nutanix node name is accepted, e.g. retcn888-nx7001.
    It will transfer to iLO fqdn, e.g. retcn888-nx7001oob.ikea.com.
    
    .PARAMETER iLOAddress
    Either Fqdn or IP address for iLO is accepted. It's conflict 
    with Node parameter.
    
    .PARAMETER iLOUsername
    Either local iLO username or GST account username. If using GST 
    account, the function will switch UPN account to SAM account for 
    better iLO authentication
    
    .PARAMETER iLOPassword
    Password for iLO local account or GST account.
    
    .PARAMETER Tier
    Required for GST account, the tier of the account. Default is PROD.
    Others are D2, DT, PPE.
    
    .EXAMPLE
    Stop-NtnxNodeViaIlo -iLOAddress retcn888-nx7001oob.ikea.com -iLOUsername "Administrator" -iLOPassword '********'
    ------------------------------------------------
    Using Fqdn to stop node with iLO local account

    .EXAMPLE
    Stop-NtnxNodeViaIlo -iLOAddress retcn888-nx7001oob.ikea.com -Tier PPE
    ------------------------------------------------
    Using Fqdn to stop node with GST account, default Tier is PROD

    .EXAMPLE
    Stop-NtnxNodeViaIlo -iLOAddress retcn888-nx7001oob.ikea.com, retcn888-nx7002oob.ikea.com -iLOUsername "Administrator" -iLOPassword '********'
    ------------------------------------------------
    Using Fqdn to stop multiple nodes with iLO local account

    .EXAMPLE
    Stop-NtnxNodeViaIlo -Node retcn888-nx7001 -iLOUsername "Administrator" -iLOPassword '********'
    ------------------------------------------------
    Using node name to stop node with iLO local account

    .EXAMPLE
    Stop-NtnxNodeViaIlo -Node retcn888-nx7001 -Tier PPE
    ------------------------------------------------
    Using node name to stop node with GST account, default Tier is PROD

    .EXAMPLE
    Stop-NtnxNodeViaIlo -Node retcn888-nx7001,retcn888-nx7002 -iLOUsername "Administrator" -iLOPassword '********'
    ------------------------------------------------
    Using node name to stop multiple nodes with iLO local account
    
    .NOTES
    General notes
    #>
    param(
        [Parameter(Position = 0, Mandatory = $true, ParameterSetName = "Node")]
        [array]$Node,

        [Parameter(Position = 0, Mandatory = $true, ParameterSetName = "ILO")]
        [array] $iLOAddress,

        [string] $iLOUsername,
        [string] $iLOPassword,
        [string][ValidateSet("D2","DT","PPE","PROD")] $Tier = "PROD"
    )

    # Test if iLO credentials are provided, if not, using GST account
    if (!$iLOUsername -or !$iLOPassword) {
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "No iLO credentials provided, using GST account"
        $GstAccount = Read-GstAccount -Tier $Tier
        $User       = ($GstAccount.username -split "@")[0]
        # switch UPN account to SAM account for better iLO authentication performance
        switch ($Tier) {
            "D2" {
                $iLOUsername = "ikead2\$User"
            }
            "DT" {
                $iLOUsername = "ikeadt\$User"
            }
            Default {
                $iLOUsername = "ikea\$User"
            }
        }
        $iLOPassword = $GstAccount.password
    }

    # Switch tier to domain
    switch ($Tier) {
        "D2" { $Domain = "ikead2.com" }
        "DT" { $Domain = "ikeadt.com" }
        Default { $Domain = "ikea.com" }
    }

    # if Node provided, stop the node(s)
    if ($Node) {
        foreach ($N in $Node) {
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Stopping node $N"
            $Fqdn = $N + "oob." + $Domain
            $Reply = Rest-iLO-Reset-Power -iLOAddress $Fqdn -iLOUsername $iLOUsername -iLOPassword $iLOPassword -Type Off

            # sleep 5 seconds to wait for the node to be stopped and verify the status
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Waiting for 5 seconds to verify the node status"
            Start-Sleep -Seconds 5
            $Status = Get-NtnxNodePowerState -iLOAddress $Fqdn -iLOUsername $iLOUsername -iLOPassword $iLOPassword
            if ($Status -ne "Off") {
                Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "Failed to stop node $N, please check manually"
            } else {
                Write-ConsoleLog -Level SXED -FunctionName (Get-FunctionName) -Message "Node status OFF, $N stopped successfully"
            }
        }
    }

    # if iLO provided, stop the node(s)
    if ($iLOAddress) {
        foreach ($iLO in $iLOAddress) {
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Stopping node via iLO address $iLO"
            $Reply = Rest-iLO-Reset-Power -iLOAddress $iLO -iLOUsername $iLOUsername -iLOPassword $iLOPassword -Type Off

            # sleep 5 seconds to wait for the node to be stopped and verify the status
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Waiting for 5 seconds to verify the node status"
            Start-Sleep -Seconds 5
            $Status = Get-NtnxNodePowerState -iLOAddress $iLO -iLOUsername $iLOUsername -iLOPassword $iLOPassword
            if ($Status -ne "Off") {
                Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "Failed to stop node $iLO, please check manually"
            } else {
                Write-ConsoleLog -Level SXED -FunctionName (Get-FunctionName) -Message "Node status OFF, $iLO stopped successfully"
            }
        }
    }
}

function Update-IloNtpServer() {
    <#
    .SYNOPSIS
    This function is use for updating NTP servers in iLO.

    .DESCRIPTION
    This function is use for updating NTP servers in iLO.
    According to iLO setup, we can only add 2 NTP servers.

    .PARAMETER iLOAddress
    FQDN or IP address of iLO.

    .PARAMETER iLOUsername
    User name of iLO admin, prefer local account.

    .PARAMETER iLOPassword
    Password for iLO admin user.

    .PARAMETER Region
    Region of server location, will pick up 2 NTP servers according to region.
    Default value is "EU"

    .EXAMPLE
    Update-iLONtpServer -iLOAddress 'retcn888-nx7001oob.ikea.com' -iLOUsername 'Administrator' -iLOPassword '************' -Region CN
    #>
    Param (
        [string][Parameter(Mandatory = $true)]                                   $iLOAddress,
        [string][Parameter(Mandatory = $true, ParameterSetName = 'Credential')]  $iLOUsername,
        [string][Parameter(Mandatory = $true, ParameterSetName = 'Credential')]  $iLOPassword,
        [string][ValidateSet("AP","CN","EU","NA")]                               $Region = "EU"
    )
    Begin{
        switch ($Region) {
            'AP' { $Body = @{'StaticNTPServers' = 'ntp1-ap.ikea.com','ntp1-eu.ikea.com'}}
            'CN' { $Body = @{'StaticNTPServers' = 'ntp1-cn.ikea.com','ntp1-ap.ikea.com'}}
            'EU' { $Body = @{'StaticNTPServers' = 'ntp1-eu.ikea.com','ntp2-eu.ikea.com'}}
            'NA' { $Body = @{'StaticNTPServers' = 'ntp1-na.ikea.com','ntp1-eu.ikea.com'}}
        }
        $Body = $Body | ConvertTo-Json
    }
    Process {
        Rest-iLO-UpdateNTPServer -iLOAddress $iLOAddress `
                                -iLOUsername $iLOUsername `
                                -iLOPassword $iLOPassword `
                                -Body $Body
    }
    End {
        #Nothing yet
    }
}
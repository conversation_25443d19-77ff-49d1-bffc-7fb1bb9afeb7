function Register-CertByCsr(){
    param(
        [string] $KeyFactor,
        [string] $CsrFile,
        [string] $Username,
        [string] $Pword
    )
    [string] $Csr = ""
    $(Get-Content -Path $CsrFile) | Foreach-Object {
        $Csr += $_
    }
    return Rest-KeyFactor-EnrollCertByCsr -Fqdn $KeyFactor `
                                          -Csr $Csr `
                                          -Template "IKEAWebServer" `
                                          -CertificateAuthoriy "IKEA Issuing CA V4" `
                                          -CertificateOwnerGroup "DPC_Siab" `
                                          -EmailContact "<EMAIL>" `
                                          -IncludeChain:$false `
                                          -Username $Username `
                                          -Pword $Pword
}

function Register-CertInPKi3(){
    param (
        [string] [validateSet("IKEAWebserver", "IKEADTWebserver", "IKEAD2Webserver")] $Template = "IKEAWebserver",
        [string] [ValidateSet("PFX", "CSR")]                                          $Type,
        [string] [Parameter(ParameterSetName = 'PFX')]                                $CN,
        [string] [Parameter(ParameterSetName = 'PFX')]                                $CertPass,
        [array]  [Parameter(ParameterSetName = 'PFX')]                                $DNSs,
        [array]  [Parameter(ParameterSetName = 'PFX')]                                $IPv4s,
        [string] [Parameter(ParameterSetName = 'CSR')]                                $CsrFile,
        [switch] [Parameter(ParameterSetName = 'CSR')]                                $IncludeChain = $false,
        [string] [Parameter(Mandatory = $true)]                                       $Username,
        [string] [Parameter(Mandatory = $true)]                                       $Pword,
        [string] [ValidateSet("PFX", "PEM", "DER")]                                   $CertFormat,
        [string]                                                                      $OutFile
    )
    Begin {
        $KeyfactorVars = $(Read-Var).KeyFactor
        $Auth          = Get-Base64Auth -Username $Username -PWord $PWord
        $Timestamp     = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
    }
    Process {
        if ((("PFX" -eq $Type) -and ($CertFormat -notin @("PFX", "PEM"))) -or `
            (("CSR" -eq $Type) -and ($CertFormat -notin @("PEM", "DER")))) {
            Write-ConsoleLog -Level "ERROR" -FunctionName $(Get-FunctionName) -Message "The CertFormat $($CertFormat) does not compatible with the Type $($Type)"
            return
        }
        $RequestURI = "Enrollment/$($Type)"
        $Headers    = @{
            'Authorization'              = $Auth
            'Content-Type'               = "application/json"
            'x-keyfactor-requested-with' = "APIClient"
            'x-keyfactor-api-version'    = "1"
            'x-certificateformat'        = $CertFormat
        }
        switch ($Type) {
            "CSR" {
                [string] $Csr = ""
                $(Get-Content -Path $CsrFile) | Foreach-Object {
                    $Csr += $_
                }
                $Body = @{
                    'CSR'                  = $Csr
                    'IncludeChain'         = [bool] $IncludeChain
                    'CertificateAuthority' = $KeyfactorVars.Template."$($Template)".CertificateAuthority
                    'Timestamp'            = $Timestamp
                    'Template'             = $Template
                    'Metadata'             = @{
                        'Certificate_Owner_Group' = $KeyfactorVars.CertOwnerGroup
                        'Email-Contact1'          = $KeyfactorVars.EmailContact
                    }
                }
            }
            "PFX" {
                $Body = @{
                    'CustomFriendlyName'   = $CN
                    'Password'             = $CertPass
                    'Subject'              = "CN=$($CN), $($KeyfactorVars.OuName)"
                    'CertificateAuthority' = $KeyfactorVars.Template."$($Template)".CertificateAuthority
                    'Template'             = $Template
                    'Timestamp'            = $Timestamp
                    'Metadata'             = @{
                        'Notes'                   = "Certificate used for DPC"
                        'Email-Contact1'          = $KeyfactorVars.EmailContact
                        'Certificate_Owner_Group' = $KeyfactorVars.CertOwnerGroup
                    }
                    'SANs' = @{
                        'dns' = $DNSs
                        'ip4' = $IPv4s
                    }
                }
            }
        }
        $result = Invoke-KeyFactor-API -Fqdn $KeyfactorVars.Endpoint -RequestURI $RequestURI -Method POST -Headers $Headers -Body $Body
        # return $Body
    }
    End{
        switch ($Type) {
            "CSR" {
                if ($result.CertificateInformation.Certificates) {
                    $result.CertificateInformation.Certificates | Out-File $OutFile
                }
            }
            "PFX" {
                if ($result.CertificateInformation.Pkcs12Blob) {
                    $PfxBytes = [System.Convert]::FromBase64String($result.CertificateInformation.Pkcs12Blob)
                    [System.IO.File]::WriteAllBytes($OutFile, $PfxBytes)
                }
            }
        }
        # return $result
    }
}
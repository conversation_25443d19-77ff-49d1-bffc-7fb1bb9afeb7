from marshmallow import Schema, fields, validate


class ListBenchmarkRequestSchema(Schema):
    id = fields.Int(validate=validate.Range(min=1))
    name = fields.Str()
    facility = fields.Str(validate=validate.OneOf([
        "retail",
        "warehouse"
    ]))
    arch = fields.Str(validate=validate.OneOf([
        "PC",
        "PE"
    ]))
    tier = fields.Str(validate=validate.OneOf([
        "Production",
        "PreProduction",
        "IKEADT",
        "IKEAD2"
    ]))


class BenchmarkLinkedClusterSchema(Schema):
    bmk_id = fields.Int(required=True)

<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { GetPCPECorrespondence_Lcm} from '@/api/nutanix'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    version: {
      type: String,
      default: '*******',
    },
  },
  data() {
    return {
      aosVersionList: [],
      chart: null,
      chart_data: [
              { value: 2500, name: 'Windows' },
              { value: 1200, name: 'Linux' },
              { value: 500, name: 'Network Appliance' },
              { value: 410, name :'others'}
        ]
    }
  },
  mounted() {
    this.getCorrespondence_Lcm()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    getCorrespondence_Lcm(){
      GetPCPECorrespondence_Lcm(this.$store.getters.token).then(response => {
        if (response.status == 200){
          this.pes_list = response.data.filter(item => item.pc !== "ssp-ppe-ntx.ikea.com" && item.pc !== "ssp-russia-ntx.ikea.com" && item.pc !== "ssp-dhd2-ntx.ikead2.com" && item.pc !== "ssp-dt-ntx.ikeadt.com")
          if (!Array.isArray(this.pes_list)) {
            console.error("The data type is not an array");
            return;
          }
          const versionCount = {};
          this.pes_list.forEach(pcEntry => {
            if (!pcEntry.pe || !Array.isArray(pcEntry.pe)) {
              console.warn("Warning: pcEntry is missing the pe property or it is not an array", pcEntry);
              return;
            }
            pcEntry.pe.forEach(countryEntry => {
              if (!countryEntry.name || !Array.isArray(countryEntry.name)) {
                console.warn("Warning: countryEntry is missing the name property or it is not an array", countryEntry);
                return;
              }
              countryEntry.name.forEach(server => {
                const version = server.aos_version;
                // Count the occurrences of each aos_version
                if (versionCount[version]) {
                  versionCount[version] += 1;
                } else {
                  versionCount[version] = 1;
                }
              });
            });
          });
          this.aosVersionList = Object.keys(versionCount).map(version => {
            return {
              name: version,
              value: versionCount[version]
            };
          });
          this.chart_data = this.aosVersionList;
          this.initChart()
        } else {
          console.error("Unexpected response status:", response.status);
        }
      }).catch(error => {
        console.error("Error fetching correspondence data:", error);
      });
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        // legend: {
        //   left: 'center',
        //   bottom: '10',
        //   data: ['Industries', 'Technology', 'Forex', 'Gold', 'Forecasts']
        // },
        series: [
          {
            name: 'AOS Version Distribution',
            type: 'pie',
            roseType: 'radius',
            radius: [15, 135],
            center: ['50%', '55%'],
            data: this.chart_data,
            animationEasing: 'cubicInOut',
            animationDuration: 2600,
            label: {
              formatter: '{b}: {c} ({d}%)'
            }
          }
        ]
      })
      // this.chart.resize()
    }
  }
}
</script>

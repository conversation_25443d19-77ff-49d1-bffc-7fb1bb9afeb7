$Global:DumpFile = New-Item -Type File `
                     -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\Run-Job-RetailNtxState-t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                     -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Get-NtxAvgMetric(){
    param(
        [string] $Fqdn,
        [Array]  $Uuid,
        [int64]  $T0,
        [int64]  $Days,
                 $Auth
    )
    $Body = [PSCustomObject]@{
        'entity_type'             = "cluster"
        'entity_ids'              = @($Uuid)
        'group_member_attributes' = @(
            @{
                attribute = "hypervisor_cpu_usage_ppm"
                operation = "AVG"
            }
            @{
                attribute = "aggregate_hypervisor_memory_usage_ppm"
                operation = "AVG"
            }
            @{
                attribute = "storage.usage_bytes"
                operation = "AVG"
            }
        )
        'interval_start_ms'     = $T0 * 1000 - ($Days + 1) * 24 * 60 * 60 * 1000
        'interval_end_ms'       = $T0 * 1000
        'downsampling_interval' = 300
        'query_name'            = "prism:CPStatsModel"
    }
    $PrismCall = Rest-Prism-v3-Get-Groups -Fqdn $Fqdn -Body $Body -Auth $Auth
    return $PrismCall.group_results.entity_results
}
function Launch-Job(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CollectionL      = Select-DhRetailNtxState -Vars $Vars
            $T0               = Get-Date -AsUTC -UFormat %s
            $SiteOpenHours    = 8..21
            $RawMetrics0      = @()
            $RawMetrics7      = @()
            $CollectionInsert = @()
            $CollectionUpdate = @()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        foreach ($PC in $DhPCs) {
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on $($PC.fqdn)" -DumpFile $DumpFile
            $SvcAccount = Select-DhServiceAccount -Vars $Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for $($PC.name)" -DumpFile $DumpFile
                continue
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for calling $($PC.name)" -DumpFile $DumpFile
                continue
            }
            $PEs    = $DhPEs | Where-Object {$_.pc_id -eq $PC.id}
            $Offset = 0
            $Length = 100
            while ($Offset -lt $PEs.length) {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Getting metrics data in 0 days of PEs from the $($Offset + 1) to the $(if ($($Offset + $Length) -lt $PEs.length) {$($Offset + $Length)}else {$PEs.length})" -DumpFile $DumpFile
                $RawMetrics0 += Get-NtxAvgMetric -Fqdn $PC.fqdn -Uuid $PEs[$Offset..($Offset + $Length - 1)].uuid -T0 $T0 -Days 0 -Auth $Auth
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Getting metrics data in 7 days of PEs from the $($Offset + 1) to the $(if ($($Offset + $Length) -lt $PEs.length) {$($Offset + $Length)}else {$PEs.length})" -DumpFile $DumpFile
                $RawMetrics7 += Get-NtxAvgMetric -Fqdn $PC.fqdn -Uuid $PEs[$Offset..($Offset + $Length - 1)].uuid -T0 $T0 -Days 7 -Auth $Auth
                $Offset      += $Length
            }
        }
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Now we have raw data, start to do the math" -DumpFile $DumpFile
        $States0 = @()
        foreach ($RawMetric in $RawMetrics0) {
            $PE            = $DhPEs | Where-Object {$_.uuid -eq $RawMetric.entity_id}
            $TimeOffsetSec = [int]$PE.timezone.Substring(3,3) * 60 * 60
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Dealing with 0 days raw data of $($PE.name)" -DumpFile $DumpFile
            $RawMetric | Add-Member -NotePropertyName 'fqdn' -NotePropertyValue $PE.fqdn
            $RawMetric | Add-Member -NotePropertyName 'time_offset_sec' -NotePropertyValue $TimeOffsetSec
            $State = [PSCustomObject]@{
                'pe_uuid'  = $PE.uuid
                'date'     = $(Get-Date -UnixTimeSeconds ([int]$T0 + $TimeOffsetSec) -AsUTC -Format FileDate)
                'cpu_avg'  = 0
                'cpu_max'  = 0
                'mem_avg'  = 0
                'mem_max'  = 0
                'disk_avg' = 0
                'disk_max' = 0
            }
            #foreach ($Value in $RawMetric.data[0..2].values) {
            #    $Value | Add-Member -NotePropertyName 'site_date' -NotePropertyValue $(Get-Date -UnixTimeSeconds ($value.time / 1000 / 1000 + $TimeOffsetSec) -AsUTC -Format FileDate)
            #    $Value | Add-Member -NotePropertyName 'site_time' -NotePropertyValue $(Get-Date -UnixTimeSeconds ($value.time / 1000 / 1000 + $TimeOffsetSec) -AsUTC -UFormat %R)
            #}
            foreach ($Data in $RawMetric.data[0..2]) {
                $Values   = $data.values | Where-Object {[int]($(Get-Date -UnixTimeSeconds ($_.time / 1000 / 1000 + $TimeOffsetSec) -AsUTC -UFormat %R).Split(":")[0]) -in $SiteOpenHours}
                $Measured = $Values.values | Measure-Object -Maximum -Average
                $AvgValue = $Measured.Average
                $MaxValue = $Measured.Maximum
                switch ($data.name) {
                    "hypervisor_cpu_usage_ppm" {
                        $State.cpu_avg = "" + [int][Math]::Ceiling($AvgValue / [Math]::Pow(10,4)) + "%"
                        $State.cpu_max = "" + [int][Math]::Ceiling($MaxValue / [Math]::Pow(10,4)) + "%"
                    }
                    "aggregate_hypervisor_memory_usage_ppm" {
                        $State.mem_avg = "" + [int][Math]::Ceiling($AvgValue / [Math]::Pow(10,4)) + "%"
                        $State.mem_max = "" + [int][Math]::Ceiling($MaxValue / [Math]::Pow(10,4)) + "%"
                    }
                    "storage.usage_bytes" {
                        $State.disk_avg = "" + [int][Math]::Ceiling($AvgValue / [Math]::Pow(1024,3)) + " GiB"
                        $State.disk_max = "" + [int][Math]::Ceiling($MaxValue / [Math]::Pow(1024,3)) + " GiB"
                    }
                }
            }
            $States0 += $State
        }
        $States7 = @()
        foreach ($RawMetric in $RawMetrics7) {
            $PE            = $DhPEs | Where-Object {$_.uuid -eq $RawMetric.entity_id}
            $TimeOffsetSec = [int]$PE.timezone.Substring(3,3) * 60 * 60
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Dealing with 7 days raw data of $($PE.name)" -DumpFile $DumpFile
            $RawMetric | Add-Member -NotePropertyName 'fqdn' -NotePropertyValue $PE.fqdn
            $RawMetric | Add-Member -NotePropertyName 'time_offset_sec' -NotePropertyValue $TimeOffsetSec
            $State = [PSCustomObject]@{
                'pe_uuid'  = $PE.uuid
                'cpu_avg'  = 0
                'cpu_max'  = 0
                'mem_avg'  = 0
                'mem_max'  = 0
                'disk_avg' = 0
                'disk_max' = 0
            }
            #    foreach ($Value in $RawMetric.data[0..2].values) {
            #        $Value | Add-Member -NotePropertyName 'site_date' -NotePropertyValue $(Get-Date -UnixTimeSeconds ($value.time / 1000 / 1000 + $TimeOffsetSec) -AsUTC -Format FileDate)
            #        $Value | Add-Member -NotePropertyName 'site_time' -NotePropertyValue $(Get-Date -UnixTimeSeconds ($value.time / 1000 / 1000 + $TimeOffsetSec) -AsUTC -UFormat %R)
            #    }
            foreach ($Data in $RawMetric.data[0..2]) {
                $Values   = $data.values | Where-Object {[int]($(Get-Date -UnixTimeSeconds ($_.time / 1000 / 1000 + $TimeOffsetSec) -AsUTC -UFormat %R).Split(":")[0]) -in $SiteOpenHours}
                $Measured = $Values.values | Measure-Object -Maximum -Average
                $AvgValue = $Measured.Average
                $MaxValue = $Measured.Maximum
                switch ($data.name) {
                    "hypervisor_cpu_usage_ppm" {
                        $State.cpu_avg = "" + [int][Math]::Ceiling($AvgValue / [Math]::Pow(10,4)) + "%"
                        $State.cpu_max = "" + [int][Math]::Ceiling($MaxValue / [Math]::Pow(10,4)) + "%"
                    }
                    "aggregate_hypervisor_memory_usage_ppm" {
                        $State.mem_avg = "" + [int][Math]::Ceiling($AvgValue / [Math]::Pow(10,4)) + "%"
                        $State.mem_max = "" + [int][Math]::Ceiling($MaxValue / [Math]::Pow(10,4)) + "%"
                    }
                    "storage.usage_bytes" {
                        $State.disk_avg = "" + [int][Math]::Ceiling($AvgValue / [Math]::Pow(1024,3)) + " GiB"
                        $State.disk_max = "" + [int][Math]::Ceiling($MaxValue / [Math]::Pow(1024,3)) + " GiB"
                    }
                }
            }
            $States7 += $State
        }
        foreach ($PE in $DhPEs) {
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Assembling the data set of $($PE.name)" -DumpFile $DumpFile
            $R = [PSCustomObject]@{
                'pe_uuid'         = $PE.uuid
                'pe_id'           = $PE.id
                'pe_name'         = $PE.name
                'pc_id'           = $PE.pc_id
                'pc_name'         = $PE.prism
                'date'            = if ($Date    = ($States0 | Where-Object {$_.pe_uuid -eq $PE.uuid}).date    ) {$Date   } else {"NA"}
                'cpu_avg'         = if ($CpuAvg  = ($States0 | Where-Object {$_.pe_uuid -eq $PE.uuid}).cpu_avg ) {$CpuAvg } else {"NA"}
                'cpu_avg_7_days'  = if ($CpuAvg  = ($States7 | Where-Object {$_.pe_uuid -eq $PE.uuid}).cpu_avg ) {$CpuAvg } else {"NA"}
                'cpu_max'         = if ($CpuMax  = ($States0 | Where-Object {$_.pe_uuid -eq $PE.uuid}).cpu_max ) {$CpuMax } else {"NA"}
                'cpu_max_7_days'  = if ($CpuMax  = ($States7 | Where-Object {$_.pe_uuid -eq $PE.uuid}).cpu_max ) {$CpuMax } else {"NA"}
                'mem_avg'         = if ($MemAvg  = ($States0 | Where-Object {$_.pe_uuid -eq $PE.uuid}).mem_avg ) {$MemAvg } else {"NA"}
                'mem_avg_7_days'  = if ($MemAvg  = ($States7 | Where-Object {$_.pe_uuid -eq $PE.uuid}).mem_avg ) {$MemAvg } else {"NA"}
                'mem_max'         = if ($MemMax  = ($States0 | Where-Object {$_.pe_uuid -eq $PE.uuid}).mem_max ) {$MemMax } else {"NA"}
                'mem_max_7_days'  = if ($MemMax  = ($States7 | Where-Object {$_.pe_uuid -eq $PE.uuid}).mem_max ) {$MemMax } else {"NA"}
                'disk_avg'        = if ($DiskAvg = ($States0 | Where-Object {$_.pe_uuid -eq $PE.uuid}).disk_avg) {$DiskAvg} else {"NA"}
                'disk_avg_7_days' = if ($DiskAvg = ($States7 | Where-Object {$_.pe_uuid -eq $PE.uuid}).disk_avg) {$DiskAvg} else {"NA"}
                'disk_max'        = if ($DiskMax = ($States0 | Where-Object {$_.pe_uuid -eq $PE.uuid}).disk_max) {$DiskMax} else {"NA"}
                'disk_max_7_days' = if ($DiskMax = ($States7 | Where-Object {$_.pe_uuid -eq $PE.uuid}).disk_max) {$DiskMax} else {"NA"}
            }
            if ($CollectionL | Where-Object {$_.pe_uuid -eq $R.pe_uuid -and $_.date -eq $R.date}) {
                $CollectionUpdate += $R
            }else {
                $CollectionInsert += $R
            }
        }
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_state]" -DumpFile $DumpFile
        Insert-Table-DhRetailNtxState -Vars $Vars -Collection $CollectionInsert
        Update-Table-DhRetailNtxState-ByPeUuidAndDate -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job
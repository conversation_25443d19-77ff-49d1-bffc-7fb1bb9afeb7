2025-07-30 13:17:24,257 INFO Start to run the task.
2025-07-30 13:17:28,004 INFO Checking Maintenance Mode
2025-07-30 13:17:28,004 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-07-30 13:17:28,004 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-07-30 13:17:30,568 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-07-30 13:17:30,568 INFO SSH Executing '/home/<USER>/prism/cli/ncli host list --json=pretty'.
2025-07-30 13:17:31,420 INFO Waiting for 5 seconds for the execution.
2025-07-30 13:17:36,431 INFO stdout: b'{\n  "data" : [ {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::4",\n    "uuid" : "8a276a5a-9cd0-4702-8ed9-5699d07c192e",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH967RD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::60",\n        "diskUuid" : "ba87f2bb-2824-44e3-8240-75c32c2c4e80",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH967RD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH99N2D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::51",\n        "diskUuid" : "35ea81b9-7cfb-4924-9b45-f07a4cd6fc3f",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N2D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH7B71D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::53",\n        "diskUuid" : "65a27c83-885e-4672-be27-05971598814b",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7B71D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9726D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::61",\n        "diskUuid" : "f4390867-7871-494b-8fd2-65dfa19de512",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9726D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH9B3ED",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::58",\n        "diskUuid" : "9979a224-9d9e-42bc-a282-b1af4e2f9acd",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B3ED",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9421D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::52",\n        "diskUuid" : "01e76618-04df-4259-a761-7e0bab50887b",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9421D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH8DKKD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::56",\n        "diskUuid" : "e31d9325-c534-4c9e-a67e-65081ebff024",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8DKKD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98J3D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::57",\n        "diskUuid" : "c61322d0-febf-45eb-8293-8bb50ef57e95",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98J3D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307893",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::55",\n        "diskUuid" : "d55987bc-a972-43e0-8c49-380c1fed8ff0",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307893",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307888",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::54",\n        "diskUuid" : "514be4f9-c6dc-4f16-805f-8372704d5f1d",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307888",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7001",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8S",\n    "blockSerial" : "CZ20240J8S",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753432724207435,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "20000000",\n      "controller_num_read_iops" : "3",\n      "read_io_ppm" : "0",\n      "controller_num_iops" : "132",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "39279",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "436561",\n      "controller_num_write_io" : "3887",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "3668646",\n      "controller_total_read_io_size_kbytes" : "752",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "23612",\n      "content_cache_num_lookups" : "1174",\n      "controller_total_io_size_kbytes" : "37808",\n      "content_cache_hit_ppm" : "649063",\n      "controller_num_io" : "3981",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "85",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "921",\n      "num_io" : "10",\n      "controller_num_read_io" : "94",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "25",\n      "controller_io_bandwidth_kBps" : "1260",\n      "hypervisor_num_received_bytes" : "1800918770761",\n      "hypervisor_timespan_usecs" : "29962839",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "0",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "180",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "1235",\n      "controller_write_io_ppm" : "976387",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "1335654341936",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "0",\n      "hypervisor_memory_usage_ppm" : "453672",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "129",\n      "total_io_time_usecs" : "1809",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "8",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "0",\n      "write_io_bandwidth_kBps" : "21",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "417",\n      "num_write_io" : "10",\n      "total_io_size_kbytes" : "432",\n      "io_bandwidth_kBps" : "21",\n      "content_cache_physical_memory_usage_bytes" : "2920716796",\n      "controller_timespan_usecs" : "30000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-425909508",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "1000000",\n      "controller_avg_write_io_latency_usecs" : "933",\n      "content_cache_logical_memory_usage_bytes" : "2494807288"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "6729723904",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "840256258048",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97647648097896",\n      "storage_tier.ssd.usage_bytes" : "************",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91529232184936",\n      "storage.usage_bytes" : "************",\n      "storage_tier.ssd.free_bytes" : "6118415912960"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::5",\n    "uuid" : "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH9453D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::63",\n        "diskUuid" : "55b9bef0-e64e-4b83-b56d-07424ac5a4fa",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9453D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH9B0JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::69",\n        "diskUuid" : "87299dda-79ad-4648-a18d-867837bdb061",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B0JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH947JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::66",\n        "diskUuid" : "21accc04-9438-4b1e-a735-121e6651e2c1",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH947JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9B2GD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::68",\n        "diskUuid" : "8fcec93c-f3a5-4a88-a18f-ad5a75a3d36e",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B2GD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH95WYD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::64",\n        "diskUuid" : "190548f4-f43c-40fd-8e0d-c805d972bf31",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH95WYD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9984D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::67",\n        "diskUuid" : "f73b0d65-6966-4e81-92fb-7815e0986aea",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9984D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH99N1D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::70",\n        "diskUuid" : "ebe3c979-d24f-46fb-9f2d-06b675e7f957",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N1D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98WLD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::65",\n        "diskUuid" : "3b10236d-18ef-48d1-b777-8f9f6b64ced8",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98WLD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N200095",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::72",\n        "diskUuid" : "4ce56fa0-31fa-45aa-9740-39f6561ab0e2",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N200095",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307878",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::71",\n        "diskUuid" : "064c52a5-ac8a-4f7f-ae98-967f225ddb32",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307878",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7002",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8R",\n    "blockSerial" : "CZ20240J8R",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753430337659671,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "30000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "250000",\n      "controller_num_iops" : "132",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "281740",\n      "controller_num_write_io" : "3986",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "3553673",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "1219",\n      "controller_total_io_size_kbytes" : "37358",\n      "content_cache_hit_ppm" : "656275",\n      "controller_num_io" : "3986",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "86",\n      "num_write_iops" : "1",\n      "controller_num_random_io" : "0",\n      "num_iops" : "1",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "891",\n      "num_io" : "44",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "1245",\n      "hypervisor_num_received_bytes" : "2101550419580",\n      "hypervisor_timespan_usecs" : "29970814",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "66",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "284",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "1245",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "1402121921855",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "2",\n      "hypervisor_memory_usage_ppm" : "384259",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "132",\n      "total_io_time_usecs" : "12514",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "11",\n      "write_io_bandwidth_kBps" : "29",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "33",\n      "total_io_size_kbytes" : "958",\n      "io_bandwidth_kBps" : "31",\n      "content_cache_physical_memory_usage_bytes" : "3703239924",\n      "controller_timespan_usecs" : "30000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-513240212",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "750000",\n      "controller_avg_write_io_latency_usecs" : "891",\n      "content_cache_logical_memory_usage_bytes" : "3189999712"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "6740851712",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "888556109824",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97596085387880",\n      "storage_tier.ssd.usage_bytes" : "************",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91529221057128",\n      "storage.usage_bytes" : "************",\n      "storage_tier.ssd.free_bytes" : "6066864330752"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::6",\n    "uuid" : "34ff0abd-9dee-4e7d-9481-4716d64569b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH96JDD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::41",\n        "diskUuid" : "2c172a78-3626-4861-bfff-98d6d79fef49",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH96JDD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH995TD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::43",\n        "diskUuid" : "94de93d2-5e8f-44d7-85d7-c414177670a7",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH995TD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH991DD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::46",\n        "diskUuid" : "4d3bffc9-fa0d-4ff3-bd95-436cf68ab516",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH991DD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9825D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::45",\n        "diskUuid" : "ea324991-7bb3-49a9-84f6-7c30e1c938a5",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9825D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH8XYHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::48",\n        "diskUuid" : "5f14642e-c45b-4efc-b5ee-95c532192c22",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8XYHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH99MHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::44",\n        "diskUuid" : "5758e233-4edc-4045-8446-0625e9ba05c0",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99MHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH9ADHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::47",\n        "diskUuid" : "dbeb0a0e-ff11-4b98-a2b7-a45bad3407fd",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9ADHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH7XGHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::42",\n        "diskUuid" : "08dba0c0-cd98-4d75-a131-a60323ba43b5",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7XGHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307864",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::50",\n        "diskUuid" : "39e3bde6-2802-4ad2-825c-a0d61ae9fb35",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307864",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307881",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::49",\n        "diskUuid" : "a032c48d-3866-4fd6-88af-1f6a25fe5ac3",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307881",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7003",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********31",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********31"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8Q",\n    "blockSerial" : "CZ20240J8Q",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 2,\n    "bootTimeInUsecs" : 1753435095314546,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "20000000",\n      "controller_num_read_iops" : "6",\n      "read_io_ppm" : "500000",\n      "controller_num_iops" : "117",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "69502",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "275028",\n      "controller_num_write_io" : "3336",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "4299352",\n      "controller_total_read_io_size_kbytes" : "1536",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "54421",\n      "content_cache_num_lookups" : "1886",\n      "controller_total_io_size_kbytes" : "32648",\n      "content_cache_hit_ppm" : "779957",\n      "controller_num_io" : "3528",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "85",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1218",\n      "num_io" : "6",\n      "controller_num_read_io" : "192",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "51",\n      "controller_io_bandwidth_kBps" : "1088",\n      "hypervisor_num_received_bytes" : "1333381803929",\n      "hypervisor_timespan_usecs" : "30751110",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "21",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "116",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "1037",\n      "controller_write_io_ppm" : "945578",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "2215197671352",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "1",\n      "hypervisor_memory_usage_ppm" : "401537",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "111",\n      "total_io_time_usecs" : "696",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "8",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "3",\n      "write_io_bandwidth_kBps" : "1",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "361",\n      "num_write_io" : "3",\n      "total_io_size_kbytes" : "57",\n      "io_bandwidth_kBps" : "2",\n      "content_cache_physical_memory_usage_bytes" : "2820468136",\n      "controller_timespan_usecs" : "30000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-421680656",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "500000",\n      "controller_avg_write_io_latency_usecs" : "1267",\n      "content_cache_logical_memory_usage_bytes" : "2398787480"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "5955416064",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "1034843095040",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97440051867240",\n      "storage_tier.ssd.usage_bytes" : "808672757760",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91530006492776",\n      "storage.usage_bytes" : "814628173824",\n      "storage_tier.ssd.free_bytes" : "5910045374464"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  } ],\n  "status" : 0\n}\n'
2025-07-30 13:17:36,476 INFO All good, no hosts are set as NCLI maintenance inside this cluster.
2025-07-30 13:17:36,476 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-07-30 13:17:36,476 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-07-30 13:17:39,064 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-07-30 13:17:39,065 INFO SSH Executing '/usr/local/nutanix/bin/acli -o json host.list'.
2025-07-30 13:17:39,900 INFO Waiting for 5 seconds for the execution.
2025-07-30 13:17:44,902 INFO stdout: b'{"data": [{"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "8a276a5a-9cd0-4702-8ed9-5699d07c192e", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "34ff0abd-9dee-4e7d-9481-4716d64569b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}], "error": null, "status": 0}\n'
2025-07-30 13:17:44,903 INFO This seems a very old AOS version...
2025-07-30 13:17:44,903 INFO This seems a very old AOS version...
2025-07-30 13:17:44,903 INFO This seems a very old AOS version...
2025-07-30 13:17:44,950 INFO All good, no hosts are set as ACLI maintenance inside this cluster.
2025-07-30 13:17:44,990 INFO Checking CVM status
2025-07-30 13:17:45,552 INFO Trying to SSH to the RETSEELM-NXC000.IKEAD2.COM.
2025-07-30 13:17:45,552 INFO First try with username/password.
2025-07-30 13:17:45,553 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-07-30 13:17:48,112 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-07-30 13:17:54,221 INFO Sending 'cluster status |grep -v UP' to the server.
2025-07-30 13:18:12,727 INFO CVM IP:*********** Status:Up
2025-07-30 13:18:12,727 INFO CVM IP:*********** Status:Up
2025-07-30 13:18:12,728 INFO CVM IP:*********** Status:Up
2025-07-30 13:18:12,728 INFO Great, all CVM status are Up
2025-07-30 13:18:12,817 INFO Calling restapi, URL: https://retseelm-nxc000.ikead2.com:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-07-30 13:18:12,817 INFO params: None
2025-07-30 13:18:12,818 INFO User: <EMAIL>
2025-07-30 13:18:12,818 INFO payload: None
2025-07-30 13:18:12,818 INFO files: None
2025-07-30 13:18:12,818 INFO timeout: 30
2025-07-30 13:18:14,107 INFO Getting host list from retseelm-nxc000.
2025-07-30 13:18:14,107 INFO Got the host list from retseelm-nxc000.
2025-07-30 13:18:14,107 INFO Got the host list.
2025-07-30 13:18:14,107 INFO Getting vault from IKEAD2.
2025-07-30 13:18:14,695 INFO Getting Site_Pe_Nutanix.
2025-07-30 13:18:15,164 INFO Got Site_Pe_Nutanix.
2025-07-30 13:18:15,164 INFO Getting Site_Pe_Admin.
2025-07-30 13:18:15,609 INFO Got Site_Pe_Admin.
2025-07-30 13:18:15,610 INFO Getting Site_Oob.
2025-07-30 13:18:16,056 INFO Got Site_Oob.
2025-07-30 13:18:16,057 INFO Getting Site_Ahv_Nutanix.
2025-07-30 13:18:16,549 INFO Got Site_Ahv_Nutanix.
2025-07-30 13:18:16,550 INFO Getting Site_Ahv_Root.
2025-07-30 13:18:17,012 INFO Got Site_Ahv_Root.
2025-07-30 13:18:17,013 INFO Getting Site_Gw_Priv_Key.
2025-07-30 13:18:17,465 INFO Got Site_Gw_Priv_Key.
2025-07-30 13:18:17,465 INFO Getting Site_Gw_Pub_Key.
2025-07-30 13:18:17,911 INFO Got Site_Gw_Pub_Key.
2025-07-30 13:18:17,911 INFO Getting Site_Pe_Svc.
2025-07-30 13:18:18,425 INFO Got Site_Pe_Svc.
2025-07-30 13:18:18,463 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-07-30 13:19:08,699 INFO Start reset ILO administrator password for RETSEELM-NXC000
2025-07-30 13:19:10,226 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-07-30 13:19:12,247 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM with SSHKEY.
2025-07-30 13:19:20,911 INFO Sending '*' to the server.
2025-07-30 13:20:05,921 INFO ILO password Update Success
2025-07-30 13:20:08,145 INFO Saving token to Vault... Username: administrator, label: RETSEELM-NXC000/Site_Oob
2025-07-30 13:20:08,818 INFO Saving token completed.
2025-07-30 13:20:21,515 INFO Start reset CVM Nutanix password
2025-07-30 13:20:21,515 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-07-30 13:20:24,086 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-07-30 13:20:25,235 INFO unlocking nutanix account
2025-07-30 13:20:25,235 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.
2025-07-30 13:20:38,249 INFO Sending '*' to the server.
2025-07-30 13:20:50,576 INFO nutanix Password Update Success
2025-07-30 13:20:52,152 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Pe_Nutanix
2025-07-30 13:20:52,717 INFO Saving token completed.
2025-07-30 13:20:57,723 INFO taking a 30S extra powernap
2025-07-30 13:23:20,572 INFO Start reset admin password
2025-07-30 13:23:22,908 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-07-30 13:23:25,445 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-07-30 13:23:29,166 INFO unlocking admin account
2025-07-30 13:23:30,204 INFO Sending 'allssh sudo faillock --user admin --reset' to the server.
2025-07-30 13:23:46,587 INFO Sending '*' to the server.
2025-07-30 13:24:06,776 INFO admin Password Update Success
2025-07-30 13:24:07,960 INFO Saving token to Vault... Username: admin, label: RETSEELM-NXC000/Site_Pe_Admin
2025-07-30 13:24:08,629 INFO Saving token completed.
2025-07-30 13:24:15,500 INFO This is a central PE, start to reset PCVM password...
2025-07-30 13:24:15,502 INFO Resetting password for Site_Pc_Nutanix...
2025-07-30 13:24:16,433 INFO taking a 30S extra powernap
2025-07-30 13:24:46,482 INFO Start reset PCVM Site_Pc_Nutanix password
2025-07-30 13:24:49,387 INFO SSH connecting to ***********2, this is the '1' try.
2025-07-30 13:24:51,938 INFO SSH connected to ***********2.
2025-07-30 13:24:53,080 INFO unlocking nutanix account
2025-07-30 13:24:53,080 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.
2025-07-30 13:25:04,082 INFO Sending '*' to the server.
2025-07-30 13:25:35,124 INFO nutanix Password Update Success
2025-07-30 13:25:35,742 INFO Resetting password for Site_Pc_Admin...
2025-07-30 13:25:36,733 INFO taking a 30S extra powernap
2025-07-30 13:26:06,773 INFO Start reset PCVM Site_Pc_Admin password
2025-07-30 13:26:09,535 INFO SSH connecting to ***********2, this is the '1' try.
2025-07-30 13:26:12,066 INFO SSH connected to ***********2.
2025-07-30 13:26:13,207 INFO unlocking admin account
2025-07-30 13:26:13,208 INFO Sending 'allssh sudo faillock --user admin --reset' to the server.
2025-07-30 13:26:24,210 INFO Sending '*' to the server.
2025-07-30 13:27:04,034 INFO admin Password Update Success
2025-07-30 13:27:04,562 INFO Start reset 1-click-nutanix password for PE RETSEELM-NXC000
2025-07-30 13:27:05,030 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-07-30 13:27:05,030 INFO params: None
2025-07-30 13:27:05,031 INFO User: admin
2025-07-30 13:27:05,031 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-07-30 13:27:05,031 INFO files: None
2025-07-30 13:27:05,031 INFO timeout: None
2025-07-30 13:27:07,163 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-07-30 13:27:07,163 INFO params: None
2025-07-30 13:27:07,163 INFO User: admin
2025-07-30 13:27:07,164 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-07-30 13:27:07,164 INFO files: None
2025-07-30 13:27:07,164 INFO timeout: None
2025-07-30 13:27:09,355 INFO This is centrol PE, Start reset 1-click-nutanix password for Pc ssp-dhd2-ntx.ikead2.com
2025-07-30 13:27:10,016 INFO Saving token to Vault... Username: 1-click-nutanix, label: RETSEELM-NXC000/Site_Pe_Svc
2025-07-30 13:27:10,650 INFO Saving token completed.
2025-07-30 13:27:10,716 INFO ****************************************************************************************************
2025-07-30 13:27:10,717 INFO *                                                                                                  *
2025-07-30 13:27:10,717 INFO *                                          Renew SSH key                                           *
2025-07-30 13:27:10,717 INFO *                                                                                                  *
2025-07-30 13:27:10,717 INFO ****************************************************************************************************
2025-07-30 13:27:11,709 INFO Check if ssh_key exist
2025-07-30 13:27:11,709 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: GET, headers: None
2025-07-30 13:27:11,709 INFO params: None
2025-07-30 13:27:11,709 INFO User: admin
2025-07-30 13:27:11,709 INFO payload: None
2025-07-30 13:27:11,709 INFO files: None
2025-07-30 13:27:11,709 INFO timeout: None
2025-07-30 13:27:13,569 INFO SSH key exist, we need replace it.
2025-07-30 13:27:13,569 INFO Deleting public key...
2025-07-30 13:27:13,569 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys/Gateway, method: DELETE, headers: None
2025-07-30 13:27:13,570 INFO params: None
2025-07-30 13:27:13,570 INFO User: admin
2025-07-30 13:27:13,570 INFO payload: None
2025-07-30 13:27:13,570 INFO files: None
2025-07-30 13:27:13,570 INFO timeout: None
2025-07-30 13:27:15,285 INFO Delete public key finished.
2025-07-30 13:27:15,286 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: GET, headers: None
2025-07-30 13:27:15,286 INFO params: None
2025-07-30 13:27:15,286 INFO User: admin
2025-07-30 13:27:15,286 INFO payload: None
2025-07-30 13:27:15,286 INFO files: None
2025-07-30 13:27:15,286 INFO timeout: None
2025-07-30 13:27:16,720 INFO SSH key exist, we need replace it.
2025-07-30 13:27:16,721 INFO Deleting public key...
2025-07-30 13:27:16,721 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys/Gateway, method: DELETE, headers: None
2025-07-30 13:27:16,721 INFO params: None
2025-07-30 13:27:16,721 INFO User: admin
2025-07-30 13:27:16,721 INFO payload: None
2025-07-30 13:27:16,721 INFO files: None
2025-07-30 13:27:16,721 INFO timeout: None
2025-07-30 13:27:18,288 INFO Delete public key finished.
2025-07-30 13:27:18,327 INFO Generating ssh_key
2025-07-30 13:27:18,328 INFO Generating SSH key: ssh-keygen -t rsa -b 2048 -f c:\Dev\UnitPortalBackend\tmp\sshkey\RETSEELM-NXC000_2025-07-30-05-27-11\prvkey -q -N "" -m PEM
2025-07-30 13:27:18,562 INFO Key pair generated: c:\Dev\UnitPortalBackend\tmp\sshkey\RETSEELM-NXC000_2025-07-30-05-27-11\prvkey
2025-07-30 13:27:18,582 INFO Installing public key...
2025-07-30 13:27:18,582 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: POST, headers: None
2025-07-30 13:27:18,582 INFO params: None
2025-07-30 13:27:18,582 INFO User: admin
2025-07-30 13:27:18,583 INFO payload: {'name': 'Gateway', 'key': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDBXn5ASx1CQSBcpyjvRciSeJsJ6U+dP4Jw/LMy4g6+9CoC4WhyVX6lDYabeNaoiv+bZsKwv7Hl6HXzfa4DJpGtmlqUvKIXrVQowXW/LGX7ypZocZEdaJWRsM2tCjcdb9vszyZWUnuUK4IMPBsEPnAiHzfWmqrfOyfCC39xWYM81M1P5nnJZwE4A5laUJq2PjF+RGCRv95RgH4CJJ3oggH5WpeHWAx938Dyct3TqC7pMFJYCTq6HaBvQIwUFttObt2QxCqZ9xAkb0i6VEN9MlEW4bZC/RWaN5DvGmqUzyuEFxAkSfbCP5l9vxvQn8owVkrD5zlcI1BDlmslL/lgNFD5 ikea\\hunhe@ITCNSHG-NB0436'}
2025-07-30 13:27:18,583 INFO files: None
2025-07-30 13:27:18,583 INFO timeout: None
2025-07-30 13:27:21,145 INFO Install public key finished.
2025-07-30 13:27:21,145 INFO Installing public key...
2025-07-30 13:27:21,145 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: POST, headers: None
2025-07-30 13:27:21,145 INFO params: None
2025-07-30 13:27:21,145 INFO User: admin
2025-07-30 13:27:21,146 INFO payload: {'name': 'Gateway', 'key': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDBXn5ASx1CQSBcpyjvRciSeJsJ6U+dP4Jw/LMy4g6+9CoC4WhyVX6lDYabeNaoiv+bZsKwv7Hl6HXzfa4DJpGtmlqUvKIXrVQowXW/LGX7ypZocZEdaJWRsM2tCjcdb9vszyZWUnuUK4IMPBsEPnAiHzfWmqrfOyfCC39xWYM81M1P5nnJZwE4A5laUJq2PjF+RGCRv95RgH4CJJ3oggH5WpeHWAx938Dyct3TqC7pMFJYCTq6HaBvQIwUFttObt2QxCqZ9xAkb0i6VEN9MlEW4bZC/RWaN5DvGmqUzyuEFxAkSfbCP5l9vxvQn8owVkrD5zlcI1BDlmslL/lgNFD5 ikea\\hunhe@ITCNSHG-NB0436'}
2025-07-30 13:27:21,146 INFO files: None
2025-07-30 13:27:21,146 INFO timeout: None
2025-07-30 13:27:22,758 INFO Install public key finished.
2025-07-30 13:27:22,800 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Gw_Priv_Key
2025-07-30 13:27:23,422 INFO Saving token completed.
2025-07-30 13:27:23,460 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Gw_Pub_Key
2025-07-30 13:27:24,088 INFO Saving token completed.
2025-07-30 13:27:24,238 INFO Task is in 'Done' status.

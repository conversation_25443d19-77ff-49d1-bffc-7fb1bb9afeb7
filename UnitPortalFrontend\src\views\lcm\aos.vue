<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="5" >
        <el-col :span="4"  :offset="12" >
          <el-select size="large"
            v-model="filter.selected_pc" multiple collapse-tags placeholder="Filter the PC" style="width:100%;" >
            <el-option v-for="item in filter.pc_list" :key="item" :label="item" :value="item" style="font-size: large;"/>
          </el-select>
        </el-col>
        <el-col :span="4" >
          <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large" @keyup.enter.native="filter_pe_list" />
        </el-col>
        <el-col :span="2" >
          <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="filter_pe_list">
            Search
          </el-button>
        </el-col>
        <el-col :span="2" >
          <el-tooltip class="custom-tooltip" effect="light" content="Download planned pe list" placement="top" >
            <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="download_plannedpe_list">
              Download
            </el-button>
          </el-tooltip>
        </el-col>
      </el-row>

    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="current_list" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column label="ID" prop="id" sortable="custom" align="center" min-width="2%" >
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="PC" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="prism" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.prism.toUpperCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column label="PE" min-width="9%" align="center" sortable="custom" prop="fqdn" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.fqdn.toUpperCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Host" min-width="3%" align="center" sortable="custom" prop="node_number">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.node_number}}</span>
        </template>
      </el-table-column>
      <el-table-column label="Latest log" min-width="13%" align="center" prop="latest_log" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.latest_log|log_filter }}</span><span class="link-type" v-if="row.latest_log!=null"  @click="show_brief_log(row)">     More</span>
        </template>
      </el-table-column>

      <el-table-column label="Planned at" min-width="7%" align="center" sortable="custom" prop="scheduled_execution" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.scheduled_execution|plan_filter}}</span>
        </template>
      </el-table-column>

      <el-table-column label="AOS" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="aos_version" >
        <template slot-scope="{row}">
          <el-tag :type="row.aos_version | aos_table_color_maker" class="bigger_font">
            {{ row.aos_version }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="AHV" min-width="8%" align="center" sortable="custom" prop="ahv_version">
        <template slot-scope="{row}">
          <el-tag :type="row.ahv_version | ahv_table_color_maker" class="bigger_font">
            {{ row.ahv_version }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- <el-table-column label="SPP" min-width="6%" align="center" sortable="custom" prop="spp_version">
        <template slot-scope="{row}">
          <el-tag :type="row.spp_version | spp_status_filter" class="bigger_font">
            {{ row.spp_version }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="Status" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="latest_task_status" >
        <template slot-scope="{row}">
          <el-tag :type="row.latest_task_status | task_status_filter" class="bigger_font">
            {{ row.latest_task_status==null ? 'No task yet' : row.latest_task_status}}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Operations"  min-width="6%" align="center" sortable="custom">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="upgrade_aos(row)">
            Upgrade AOS
          </el-button>
        </template>
      </el-table-column>


    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" />

    <el-dialog :visible.sync="dialogPvVisible" :title="'PM Log(brief)'" >

      <el-table :data="logdata" border fit highlight-current-row style="width: 100%" max-height="500" >
        <el-table-column prop="key" label="log date"  min-width="25%" >
          <template slot-scope="{row}">
            <span>{{ row.log_date }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pv" label="log info" min-width="55%"  >
          <template slot-scope="{row}">
            <span>{{ row.log_info }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pv" label="log severity"  min-width="10%"  >
          <template slot-scope="{row}">
            <span>{{ row.severity }}</span>
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button type="warning" @click="download_log_file()">Download Detail Log</el-button>
        <el-button type="primary" @click="dialogPvVisible = false">OK</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import {UpgradeAOS, GetAOSTaskList, DownloadLCMUpgradeLog, DownloadSLCMPlannedPE} from  '@/api/automation'
import { GetPCPECorrespondence_Lcm} from '@/api/nutanix'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  name: 'SPPTable',
  components: { Pagination },
  directives: { waves },
  filters: {
    aos_table_color_maker(version){
      return version!='*******'?'danger':'success'
    },
    ahv_table_color_maker(version){
      return version!='Nutanix 20220304.462'?'danger':'success'
    },
    log_filter(log){
      if(log==null){
        return "No log yet"
      }
      else{
        return log['log_info']
      }
    },
    plan_filter(plan){
      if(plan==null){
        return "Not Planned"
      }
      else{
        return plan
      }
    },
    task_status_filter(status){
      if(status==null){
        return 'primary'
      }else{
        if(status=='Done'){
          return 'success'
        }
        else if(status=='Error'){
          return 'danger'
        }
        else if (status=='In Progress'){
          return 'primary'
        }
      }
    }
  },
  data() {
    const validateTime =(rule, value, callback)=>{
      if(this.temp.datatimepickerdisabled){
        callback()
      }
      let currentdate = new Date()
      let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
      if (value < utctime){
        callback(new Error('Schedule date must be later then now.'))
      }else{
        let currnettime = utctime.getTime()
        let scheduletime = value.getTime()
        let timediff = scheduletime-currnettime
        if(timediff/1000/60 < 5){
          callback(new Error('Schedule date is too close from now.'))
        }else{
          callback()
        }
      }
      callback()
    }
    return {
      tableKey: 0,
      all_pe_list: null,
      filtered_list: null,
      current_list: null,
      filter:{
        pc_list:[],
        selected_pc:[],
        fuzzy_string:"",
      },
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: '',
        prism: '',
        status: '',
        sort: '+id'
      },
      statusToShowEditButton:['Not Started'],
      statusToShowAbortButton:['In Progress'],
      statusToShowDeleteButton:['Not Started','Done','Error','Aborted'],
      statusOptions: ['Not Started','In Progress','Done','Error','Aborted'],
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      // statusOptions: ['published', 'draft', 'deleted'],
      ShowCreationDate: false,
      temp: {
        id: '',
        timestamp: new Date(),
        cluster:'',
        prism: '',
        status: '',
        datatimepickerdisabled:false,
        description: '',
      },
      selectedrow:'',
      dialogFormVisible: false,
      dialogStatus: '',
      dialogPvVisible: false,
      dialogPlanVisible: false,
      logdata: [],
      rules: {
        prism: [{ required: true, message: 'prism is required', trigger: 'change' }],
        cluster: [{ required: true, message: 'cluster is required', trigger: 'change' }],
        timestamp: [{ type: 'date', required: true , trigger: 'change' , validator:validateTime}]
      },
    }
  },
  computed: {
    total() {
      if(this.filtered_list){
        return this.filtered_list.length
      }
      else{
          return 0
      }
    }
  },
  created() {
    this.get_task_list()
  },
  methods: {
    get_task_list() {
      this.listLoading = true
      GetAOSTaskList(this.$store.getters.token, "retail").then(response => {
        this.all_pe_list = response.data
        this.filtered_list = this.all_pe_list
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)
        this.listLoading = false
        let all_prism_list = this.all_pe_list.map((obj,index)=>{return obj['prism']})
        this.filter.pc_list = this.remove_duplicate(all_prism_list)
      })
    },
    download_plannedpe_list(){
      // alert('Download planned PE list functionality is not yet implemented.');
      DownloadSLCMPlannedPE(this.$store.getters.token).then((response)=>{
        if (response.status === 200) {
          const blob = new Blob([response.data], { type: 'text/csv' });
          const href = URL.createObjectURL(blob); 
          const link = document.createElement('a');
          link.href = href;
          link.setAttribute('download', 'planned_pes.csv');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(href);
        } else {
          console.error('Failed to download:', response.statusText);
        }
      })
    },
    remove_duplicate(arr) {
      //去除重复值
      const newArr = []
      arr.forEach(item => {
        if (!newArr.includes(item)) {
          newArr.push(item)
        }
      })
      return newArr
    },
    set_page(){
      // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start,end)
    },
    filter_pe_list(){
      this.listQuery.page = 1
      let temp_list
      if (this.filter.selected_pc.length){
        temp_list = this.all_pe_list.filter((item)=>{
          return this.filter.selected_pc.includes(item['prism'].toLowerCase())
        })
        this.filtered_list = temp_list
      }
      else{
        this.filtered_list = this.all_pe_list
      }
      if(this.filter.fuzzy_string.trim().length){
        let temp_list = this.filtered_list
        let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
        for(let fuzzy of fuzzy_list){
          fuzzy = fuzzy.toString().toLowerCase()
          temp_list = temp_list.filter((k)=>{
            let combined_string = (k.id?k.id.toString().toLowerCase():'') + (k.prism?k.prism.toString().toLowerCase():'')+
                                  (k.fqdn?k.fqdn.toString().toLowerCase():'')+
                                  (k.foundation_version?k.foundation_version.toString().toLowerCase():'')+
                                  (k.lcm_version?k.lcm_version.toString().toLowerCase():'')+
                                  (k.spp_version?k.spp_version.toString().toLowerCase():'')+
                                  (k.latest_task_status?k.latest_task_status.toString().toLowerCase():'')
            if( combined_string.search(fuzzy)!= -1){
              return true
            }
          })
        }

        this.filtered_list = temp_list
      }


      this.set_page()
    },
    handleFilter() {
      this.listQuery.page = 1
    },
    sortChange(data) {
      const { prop, order } = data
      console.log(prop,order)
      console.log(this.current_list)
      if(order==null){
        this.sortChange({prop:'id',order:'ascending'})
        return 
      }
      let flag_num = order=="ascending" ? 1 : -1
      console.log(flag_num)
      this.filtered_list.sort((item1,item2)=>{
        let prop1 = item1[prop]?item1[prop]:''
        let prop2 = item2[prop]?item2[prop]:''        
        return (prop1 > prop2) ? flag_num*1 : ((prop1 < prop2) ? flag_num*-1 : 0)
    })
      this.set_page()
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    upgrade_aos(row){
      let payload = {
        token: this.$store.getters.token,
        data : [{
          facility_type: "retail", 
          pc : row.prism,
          pe : row.fqdn,
          target_aos_version : "*******", 
          target_ahv_version : "20220304.462"
        }]
      }
      UpgradeAOS(payload).then(response => {
        response.data.forEach(res =>{
          if(res.success){
            this.$notify({
                title: 'Success',
                message: 'Task has been created '+ res.cluster,
                type: 'success',
                duration: 10000
            })
          }
          else{
           this.$notify({
               title: 'OOOOOps....',
               message: 'Error happened when start AOS upgrade, ' + res.message,
               type: 'error',
               duration: 10000
           })
          }
        })
      })      
      .catch((error)=>{
        this.$notify({
              title: 'Error',
              message: error.response.data.message,
              type: 'error',
              duration: 10000
          })
      })
    },
    show_brief_log(row){
      this.selectedrow = row
      this.logdata = row['latest_task']['logs'].sort((a,b)=>{
        if (a.id > b.id) {
          return 1;
        }
        if (a.id < b.id) {
          return -1;
        }
        return 0;
      })
      this.dialogPvVisible = true
    },
    download_log_file(){
      console.log(this.selectedrow.latest_task)
      let payload = {
        data:{  id:this.selectedrow.latest_task.id,
                filepath:this.selectedrow.latest_task.detail_log_path},
        token: this.$store.getters.token
      }
      DownloadLCMUpgradeLog(payload)
      .then((response)=>{
        const href = URL.createObjectURL(response.data);
        const link = document.createElement('a');
        link.href = href;
        link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      })
    }
  }
}
</script>
<style lang="scss" scoped>
    .bigger_font {
      font-size: 13px;
    }
    .container {
      display: flex;
      // justify-content: space-around;
    }
</style>

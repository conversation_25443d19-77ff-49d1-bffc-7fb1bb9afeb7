import { login, logout, getInfo } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router, { asyncRoutes, constantRoutes, resetRouter } from '@/router'

function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  } else {
    return true
  }
}
function _hasPermission_(_privilege, route) {
  //首先privilege 里要有这个 这个route的对应的key

  if (route.meta && route.meta.privilege) {
    if(_privilege[route.meta.privilege] && _privilege[route.meta.privilege]!='empty'){
      return true
    }
    return false
  } else {
    return false
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
function generateRoutes(roles) {
    let accessedRoutes
    if (roles.includes('admin')) {
      accessedRoutes = asyncRoutes || []
    } else {
      accessedRoutes = filterAsyncRoutes(asyncRoutes, ['pmuser'])
    }
    return accessedRoutes
}

function _generateRoutes_(privilege) {
  let accessedRoutes
  accessedRoutes = _filterAsyncRoutes_(asyncRoutes, privilege)
  return accessedRoutes
}

export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

export function _filterAsyncRoutes_(routes, privilege) {
  const res = []
  routes.forEach(route => {
    const tmp = { ...route }
    if (_hasPermission_(privilege, tmp)) {
      if (tmp.children) {
        tmp.children = _filterAsyncRoutes_Level2_(tmp.children, privilege[tmp.meta.privilege])
      }
      res.push(tmp)
    }
  })
  return res
}

export function _filterAsyncRoutes_Level2_(routes, privilege) {
  const res = []
  routes.forEach(route => {
    const tmp = { ...route }
    if (_hasPermission_(privilege, tmp)) {
      if (tmp.children) {
        tmp.children = _filterAsyncRoutes_Level3_(tmp.children, privilege)
      }
      res.push(tmp)
    }
  })
  return res
}
export function _filterAsyncRoutes_Level3_(routes, privilege) {
  const res = []
  routes.forEach(route => {
    const tmp = { ...route }
    if (_hasPermission_(privilege, tmp)) {
      res.push(tmp)
    }
  })
  return res
}

const state = {
  token: getToken(),
  name: '',
  avatar: '',
  introduction: '',
  roles: [],
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PRIV: (state, priv) => {
    state.privilege = priv
  },
  SET_ALL_PRIV: (state, all_priv) => {
    state.all_privilege = all_priv
  },
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes 
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    const pro = new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password }).then(response => {
        const { data } = response
        let accessedRoutes
        commit('SET_TOKEN', data.token)
        setToken(data.token)
        commit('SET_ROLES', data.roles)
        commit('SET_NAME', data.name)
        commit('SET_INTRODUCTION', ' ')
        commit('SET_PRIV', data.privilege)
        commit('SET_ALL_PRIV', data.all_privilege)
        accessedRoutes = _generateRoutes_(data.privilege)
        commit('SET_ROUTES', accessedRoutes)
        router.addRoutes(accessedRoutes)
        resolve(accessedRoutes)
        // resolve()
      }).catch(error => {
        reject(error)
      })
    })
    return pro
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(state.token).then(response => {
        const { data } = response
        if (!data) {
          reject('Verification failed, please Login again.')
        }
        const { roles, name, privilege, avatar, introduction } = data
        // roles must be a non-empty array
        if (!roles || roles.length <= 0) {
          reject('getInfo: roles must be a non-null array!')
        }
        commit('SET_ROLES', roles)
        commit('SET_NAME', name)
        commit('SET_INTRODUCTION', ' ')
        commit('SET_PRIV', data.privilege)
        commit('SET_ALL_PRIV', data.all_privilege)
        const  accessedRoutes = _generateRoutes_(privilege)
        commit('SET_ROUTES', accessedRoutes)
        resolve(accessedRoutes)
      }).catch(error => {
        console.log(error)
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        removeToken()
        resetRouter()
        // reset visited views and cached views
        // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
        dispatch('tagsView/delAllViews', null, { root: true })
        resolve()
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

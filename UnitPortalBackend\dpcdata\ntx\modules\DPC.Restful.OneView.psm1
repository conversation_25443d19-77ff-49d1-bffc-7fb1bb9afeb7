function Invoke-OneView-Api(){
    param(
        [string]                                                        $Fqdn,
        [string]                                                        $RequestUri,
        [string] [ValidateSet("GET", "POST", "PUT", "PATCH", "DELETE")] $Method,
                                                                        $Headers,
                                                                        $Body,
        [int]                                                           $MaxTry     = 5,
        [int]                                                           $TimeoutSec = 15
    )
    if ($MaxTry) {
        $Payload = @{
            'Uri'        = "https://$($Fqdn)/rest/$($RequestUri)"
            'Method'     = $Method
            'TimeoutSec' = $TimeoutSec
        }
        if ($Headers) {
            $Payload['Headers'] = $Headers
        }
        if ($Body) {
            $Payload['Body'] = $Body | ConvertTo-Json
        }
        try {
            return Invoke-RestMethod @Payload -SkipCertificateCheck:$true
        }
        catch {
            Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling $Fqdn, $RequestUri. Cause: $_ Retry in 5 seconds" -DumpFile $Global:DumpFile
            Start-Sleep 5
            return Invoke-OneView-Api -Fqdn $Fqdn `
                                      -RequestUri $RequestUri `
                                      -Method $Method `
                                      -Headers $Headers `
                                      -Body $Body `
                                      -MaxTry $($MaxTry - 1) `
                                      -TimeoutSec $($TimeoutSec + 5)
        }
    }else {
        Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling $Fqdn" -DumpFile $Global:DumpFile
        return $null
    }
}
function Rest-OneView-Get-Auth(){
    <#
    .SYNOPSIS
    The login API returns a session token on successful authentication
    
    .DESCRIPTION
    Authenticate user with specified credentials. User name, password and an optional directory are specified as input in the request body. X-Api-Version to be provided in the header.
    
    .PARAMETER Fqdn
    The endpoint of requested OneView address
    
    .PARAMETER Username
    The authenticated user for calling API
    
    .PARAMETER PWord
    The authenticated user for calling API
    
    .PARAMETER Domain
    The optional directory are specified as input, LOCAL as default assignee
    
    .EXAMPLE
    Rest-OneView-Get-Auth -Fqdn oneviewap.ikea.com -Username gstjohndoe -PWord abcd.1234 -Domain 'ikea.com'
    
    .NOTES
    Sample of function returns in JSON:
        {
          "sessionID": "LTIzMzkzMjkyMTk3FdUOAOLIyvahR2kHd5xO7qutnipKsKqC",
          "partnerData": {}
        }
    #>
    param(
        [string] $Fqdn,
        [string] $Username,
        [string] $PWord,
        [string] $Domain = 'LOCAL'
    )
    $Headers = @{
        'X-Api-Version' = '1200'
        'Content-Type'  = 'application/json'
    }
    $Body = @{
        'userName'        = $Username
        'password'        = $PWord
        'authLoginDomain' = $Domain
    }
    return Invoke-OneView-Api -Fqdn $Fqdn `
                              -RequestUri 'login-sessions' `
                              -Method POST `
                              -Headers $Headers `
                              -Body $Body
}
function Rest-OneView-List-Server(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Credential')] $Domain = 'LOCAL',
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = (Rest-OneView-Get-Auth -Fqdn $Fqdn -Username $Username -PWord $PWord -Domain $Domain).sessionID
    }
    $Page    = 1
    $Start   = 0
    $Count   = 30
    $Members = @()
    $Headers = @{
        'Auth'          = $Auth
        'X-Api-Version' = '1200'
    }
    do {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page" -DumpFile $Global:DumpFile
        $RestCall = Invoke-OneView-Api -Fqdn $Fqdn `
                                       -RequestUri "server-hardware?start=$($Start)&count=$($Count)&expand=all" `
                                       -Method GET `
                                       -Headers $Headers
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.Count) objects on page $Page" -DumpFile $Global:DumpFile
        $Members += $RestCall.members
        $Page    ++
        $Start   += $Count
    } until (
        $RestCall.count -lt $Count
    )
    return $Members
}

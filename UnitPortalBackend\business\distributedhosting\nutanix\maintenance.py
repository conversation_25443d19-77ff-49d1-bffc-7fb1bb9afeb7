import datetime
from datetime import datetime
import uuid
import re
import paramiko
import time
import json
import werkzeug.exceptions as flaskex
from business.distributedhosting.nutanix.nutanix import NutanixOperation, PrismCentral
import static.SETTINGS as SETTING
from business.generic.commonfunc import CommonRestCall
from business.authentication.authentication import ServiceAccount, Vault
from business.generic.commonfunc import SSHConnect, setup_common_logger, create_file
from business.authentication.authentication import Vault
from io import StringIO
import time
import re
import random


class Maintenance():
    def __init__(self, pe, logger = None, sa=None) -> None:
        self.pe_fqdn = pe
        # Try to get PC from retail first
        self.pe_info = NutanixOperation.get_pe_by_fqdn_from_db(fqdn=self.pe_fqdn)
        self.tier = PrismCentral.get_prism_by_name_from_db(fqdn=self.pe_info.prism)['tier']
        self.pe = self.pe_fqdn.split('.')[0]
        self.vault = Vault(self.tier)
        _res, key_string = self.vault.get_secret(f"{self.pe.upper()}/Site_Gw_Priv_Key")
        self.public_key = None
        if isinstance(key_string, dict) and key_string.get('secret', '').startswith('-----BEGIN RSA PRIVATE KEY-----'):
            self.public_key = paramiko.RSAKey(file_obj=StringIO(key_string['secret']))
        logger = setup_common_logger(str(uuid.uuid4()), log_file= create_file(
                filepath=SETTING.MAINTENANCE_LOG_PATH,
                filename=f'{self.pe}_{datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}'
            ))
        self.logger = logger
        _sa = sa if sa else ServiceAccount(usage=ServiceAccount.NUTANIX_PM).get_service_account()
        self.rest = CommonRestCall(username=_sa['username'], password=_sa['password'], logger=self.logger) 
    
    def Ncli_host_maintenance(self, target_type, target_ip): # pylint: disable=invalid-name
        res, ssh_pass = self.vault.get_secret(f"{self.pe.upper()}/Site_Pe_Nutanix")
        #***************Do not use target CVM to ssh************
        hosts_info = self.get_hosts_info()
        all_cvm_ips = []
        target_cvm_ip = None
        for entity in hosts_info['entities']:
            if entity['hypervisor_address'] == target_ip:
                target_cvm_ip = entity['controller_vm_backplane_ip']
            else:
                all_cvm_ips.append(entity['controller_vm_backplane_ip'])
        if target_cvm_ip is None:
            raise ValueError(f"Target IP {target_ip} not found in the hosts_info.")
        ssh_ip = random.choice(all_cvm_ips)      
        ssh = SSHConnect(ssh_ip, ssh_pass['username'], ssh_pass['secret'], self.public_key)
        res, _ = ssh.connect()
        if not res:
            res_sshkey, _ = ssh.connect_sshkey()
            if not res_sshkey:
                raise flaskex.InternalServerError(f"Failed to connect to {self.pe}!")
        #************Use API to get CVM UUID*************
        for entity in hosts_info['entities']:
            if entity['hypervisor_address'] == target_ip:
                cvm_uuid = entity['service_vmid']
         
        if target_type == "enter":
            command = f"/home/<USER>/prism/cli/ncli host edit id={cvm_uuid} enable-maintenance-mode=true --json=pretty"
            stdout = ssh.exec_command(command, wait=30)
            stdout = json.loads(stdout)
            if not stdout['data']['hostInMaintenanceMode']:
                raise flaskex.InternalServerError('Put CVM to maintenance mode failed')
            self.logger.info('Put CVM to maintenance mode completed')
        else:
            command = f"/home/<USER>/prism/cli/ncli host edit id={cvm_uuid} enable-maintenance-mode=false --json=pretty"
            stdout = ssh.exec_command(command, wait=10)
            stdout = json.loads(stdout)
            in_maintenance_mode = stdout['data']['hostInMaintenanceMode']
            message = ('Hey, I am still in the maintenance mode, what are you doing!!!'
                    if in_maintenance_mode
                    else 'Exit CVM to maintenance mode completed')
            self.logger.info(message)
        

    def Acli_host_maintenance(self, target_type, target_ip = None): # pylint: disable=invalid-name
        res, ssh_pass = self.vault.get_secret(f"{self.pe.upper()}/Site_Pe_Nutanix")
        ssh = SSHConnect(self.pe_fqdn, ssh_pass['username'], ssh_pass['secret'], self.public_key)
        res, _ = ssh.connect()
        if not res:
            res_sshkey, _ = ssh.connect_sshkey()
            if not res_sshkey:
                raise flaskex.InternalServerError(f"Failed to connect to {self.pe}!")
        ssh.invoke_shell()
        if target_type == "enter":
            command = f"/usr/local/nutanix/bin/acli -o json host.enter_maintenance_mode {target_ip}"
        else:
            command = f"/usr/local/nutanix/bin/acli -o json host.exit_maintenance_mode {target_ip}"
        ssh.send_command(command)
        count = 0
        while count < 5:
            time.sleep(30)        
            command_output = ssh.receive_output()
            pattern = r"complete"
            if not re.search(pattern, command_output):
                count += 1
            else:
                self.logger.info(f'Good, Successfully {target_type} host maintenance mode')
                return
        raise flaskex.InternalServerError('failed')
    
    def get_hosts_info(self):
        url = f"https://{self.pe_fqdn}:9440/PrismGateway/services/rest/v2.0/hosts/"
        res = self.rest.call_restapi(url, method="GET")
        return res.json()
.xterm{
    font-feature-settings:"liga" 0;
    position:relative;
    user-select:text;
    -ms-user-select:none;
    -webkit-user-select:none
}
.xterm.focus,.xterm:focus{
    outline:none
}
    
.xterm .xterm-helpers{
    position:absolute;
    top:0;
    z-index:5
}
.xterm .xterm-helper-textarea{
    position:absolute;
    opacity:0;
    left:-9999em;
    top:0;
    width:0;
    height:0;
    z-index:-5;
    white-space:nowrap;
    overflow:hidden;
    resize:none
}
.xterm .composition-view{
    background:#000;
    color:#FFF;
    display:none;
    position:absolute;
    white-space:nowrap;
    z-index:1
}
.xterm .composition-view.active{
    display:block
}
.xterm .xterm-viewport{
    background-color:#000;
    overflow-y:scroll;
    cursor:default;
    position:absolute;
    right:0;
    left:0;
    top:0;
    bottom:0
}
.xterm .xterm-screen{
    position:relative
}
.xterm .xterm-screen canvas{
    position:absolute;
    left:0;
    top:0;
    height: calc(100% - 29px);
}
.xterm .xterm-scroll-area{
    visibility:hidden
}
.xterm-char-measure-element{
    display:inline-block;
    visibility:hidden;
    position:absolute;
    top:0;
    left:-9999em;
    line-height:normal
}
.xterm{
    cursor:text
}
.xterm.enable-mouse-events{
    cursor:default
}
.xterm.xterm-cursor-pointer{
    cursor:pointer
}
.xterm.column-select.focus{
    cursor:crosshair
}
.xterm .xterm-accessibility,.xterm .xterm-message{
    position:absolute;
    left:0;
    top:0;
    bottom:30px;
    right:0;
    z-index:10;
    color:transparent
}
.xterm .live-region{
    position:absolute;
    left:-9999px;
    width:1px;
    height:1px;
    overflow:hidden
}
.xterm-dim{
    opacity:0.5
}
.xterm-underline{
    text-decoration:underline
}
#bottomdiv {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: rgb(50, 50, 50);
    border-color: white;
    border-style: solid none none none;
    border-width: 1px;
    z-index: 99;
    height: 19px;
}

#menu {
    display: inline-block;
    font-size: 16px;
    color: rgb(255, 255, 255);
    padding-left: 16px;
    top: 0px;
    z-index: 100;
}
#menu:hover .dropup-content {
    display: block;
}
#logBtn, #credentialsBtn, #reauthBtn {
    color: #000;
}
#terminal .terminal {
    background-color: #000000;
    color: #fafafa;
    padding: 2px;
    height: calc(100% - 29px);
  }
  #terminal {
    display: block;
    width: calc(100% - 20px);
    margin: 0 auto;
    padding: 2px;
    top: 0px;
    height: calc(100% - 29px);
  }
.dropup {
    position: relative;
    display: inline-block;
    cursor: pointer;
}
.dropup-content {
  display: none;
  position: absolute;
  background-color: #f1f1f1;
  font-size: 16px;
  min-width: 160px;
  bottom: 18px;
  z-index: 101;
}
.dropup-content a {
    color: #777;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}
.dropup-content a:hover {
    background-color: #ccc
}
.dropup:hover .dropup-content {
    display: block;
}
.dropup:active .dropup-content {
    display: block;
}
.dropup:hover .dropbtn {
    background-color: #3e8e41;
}
.body {
    display: flex;
    flex-direction: column;
    height: 100vh; /* 确保全屏 */
    margin: 0; /* 清除默认 margin */
}

 
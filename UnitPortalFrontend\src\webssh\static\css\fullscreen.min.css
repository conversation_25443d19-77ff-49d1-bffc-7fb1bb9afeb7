.xterm.fullscreen{position:fixed;top:0;bottom: 0x;;left:0;right:0;width:auto;height:auto;z-index:255}
/*# sourceMappingURL=fullscreen.min.css.map */
#bottomdiv {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: rgb(50, 50, 50);
    border-color: white;
    border-style: solid none none none;
    border-width: 1px;
    z-index: 99;
    height: 39px;
}

#menu {
    display: inline-block;
    font-size: 16px;
    color: rgb(255, 255, 255);
    padding-left: 10px;
    z-index: 100;
}
#menu:hover .dropup-content {
    display: block;
}
#logBtn, #credentialsBtn, #reauthBtn {
    color: #000;
}
/* #terminal-container {
    display: block;
    width: calc(100% - 1px);
    margin: 0 auto;
    padding: 2px;
    height: calc(100vh - 40px);
    flex-grow: 1; 
    overflow: hidden; 
}
#terminal-container .terminal {
    background-color: #000000;
    color: #fafafa;
    padding: 6px;
    height: calc(100% - 39px);
}
#terminal-container .terminal:focus .terminal-cursor {
    background-color: #fafafa;
} */
/* #tS */
.body {
    display: flex;
    flex-direction: column;
    height: 100vh; /* 确保全屏 */
    margin: 0; /* 清除默认 margin */
}
.dropup {
    position: relative;
    display: inline-block;
    cursor: pointer;
}
.dropup-content {
  display: none;
  position: absolute;
  background-color: #f1f1f1;
  font-size: 16px;
  min-width: 160px;
  bottom: 18px;
  z-index: 101;
}
.dropup-content a {
    color: #777;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}
.dropup-content a:hover {
    background-color: #ccc
}
.dropup:hover .dropup-content {
    display: block;
}
.dropup:active .dropup-content {
    display: block;
}
.dropup:hover .dropbtn {
    background-color: #3e8e41;
}
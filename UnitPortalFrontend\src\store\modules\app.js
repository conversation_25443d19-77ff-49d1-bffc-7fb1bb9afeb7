import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  size: Cookies.get('size') || 'medium',
  role_map:[
          {
            id: "1",
            label: 'Dashboard',
            disabled:true,
            scope:"all",
            key:"view", // the key here is corresponding with the 'key' of the variable 'role_map' in role.vue,
            root_key:"role_dashboard",
          },
          {
            id: "2",
            label: 'Nutanix',
            scope:"all",
            key:"role_ntx",
            children: [
              {
                id: "2.1",
                label: 'Prism Central',
                scope:"all",
                key:"prism",
                children:[
                  {
                    id: "2.1.1",
                    label: 'View',
                    scope:"all",
                    root_key:"role_ntx",
                    key:"view_pc",
                  },
                  {
                    id: "2.1.2",
                    label: 'Add',
                    scope:"all",
                    root_key:"role_ntx",
                    key:"add_pc",
                  },
                  {
                    id: "2.1.3",
                    label: 'Remove',
                    scope:"all",
                    root_key:"role_ntx",
                    key:"remove_pc",
                  },
                ]
              },
              {
                id: "2.2",
                label: 'View AHV node',
                scope:"all",
                root_key:"role_ntx",
                key:"view_ahv"
              },
              {
                id: "2.3",
                label: 'View Prism Element',
                scope:"all",
                root_key:"role_ntx",
                key:"view_pe"
              },
              {
                id: "2.4",
                label: 'View Virtual Machine',
                scope:"all",
                root_key:"role_ntx",
                key:"view_vm"
              },
            ],
          }, 
          {
            id: "3",
            label: 'SimpliVity',
            scope: "all",
            key:"role_sli",
            children: [
              {
                id: "3.1",
                label: 'View vCenter',
                scope:"all",
                root_key:"role_sli",
                key:"view_vc"
              },
              {
                id: "3.2",
                label: 'View Cluster',
                scope:"all",
                root_key:"role_sli",
                key:"view_cluster"
              },
              {
                id: "3.3",
                label: 'View Host',
                scope:"all",
                root_key:"role_sli",
                key:"view_host"
              },
              {
                id: "3.4",
                label: 'View Virtual Machine',
                scope:"all",
                root_key:"role_sli",
                key:"view_vm"
              },
            ],
          },
          {
            id: "4",
            label: 'Power Maintenance',
            scope:"all",
            key:"role_pm",
            children: [
              {
                id: "4.1",
                label: 'Nutanix',
                scope:"all",
                children:[
                  {
                    id: "4.1.1",
                    label:"View",
                    scope:"all",
                    root_key:"role_pm",
                    key:"view_ntx_pm"
                  },
                  {
                    id: "4.1.2",
                    label: "Create",
                    scope:"create_ntx_pm",
                    root_key:"role_pm",
                    key:"create_ntx_pm"
                  },
                  {
                    id: "4.1.3",
                    label: "Abort",
                    scope:"abort_ntx_pm",
                    root_key:"role_pm",
                    key:"abort_ntx_pm"
                  },
                  {
                    id: "4.1.4",
                    label: "Delete",
                    scope:"delete_ntx_pm",
                    root_key:"role_pm",
                    key:"delete_ntx_pm"
                  }
                ]
              },
              {
                id: "4.2",
                label: 'Simplivity',
                scope:"all",
                children:[
                  {
                    id: "4.2.1",
                    label:"View",
                    scope:"all",
                    root_key:"role_pm",
                    key:"view_sli_pm"
                  },
                  {
                    id: "4.2.2",
                    label: "Create",
                    scope:"cluster",
                    root_key:"role_pm",
                    key:"create_sli_pm"
                  },
                  {
                    id: "4.2.3",
                    label: "Abort",
                    scope:"cluster",
                    root_key:"role_pm",
                    key:"abort_sli_pm"
                  },
                  {
                    id: "4.2.4",
                    label: "Delete",
                    scope:"cluster",
                    root_key:"role_pm",
                    key:"delete_sli_pm"
                  }
                ]
              },
            ],
          },          
          {
            id: "5",
            label: 'Marketplace',
            scope:"all",
            key:"role_mkt",
            children: [
              {
                id: "5.1",
                label: 'Template',
                scope:"all",
                children:[
                  {
                    id: "5.1.1",
                    label:"View",
                    scope:"all",
                    root_key:"role_mkt",
                    key:"view_template"
                  },
                  {
                    id: "5.1.2",
                    label: "Create",
                    scope:"all",
                    root_key:"role_mkt",
                    key:"create_template"
                  },
                  {
                    id: "5.1.3",
                    label: "Edit",
                    scope:"all",
                    root_key:"role_mkt",
                    key:"edit_template",
                  },
                  {
                    id: "5.1.4",
                    label: "Delete",
                    scope:"all",
                    root_key:"role_mkt",
                    key:"remove_template",
                  }
                ]
              },
              {
                id: "5.2",
                label: 'Workload',
                scope:"all",
                children:[
                  {
                    id: "5.2.1",
                    label:"View",
                    scope:"all",
                    root_key:"role_mkt",
                    key:"view_wl",
                  },
                  {
                    id: "5.2.2",
                    label: "Create",
                    scope:"create_wl",
                    root_key:"role_mkt",
                    key:"create_wl",
                  },
                  {
                    id: "5.2.3",
                    label: "Abort Task",
                    scope:"all",
                    root_key:"role_mkt",
                    key:"abort_wl",
                  },
                  {
                    id: "5.2.4",
                    label: "Delete Task",
                    scope:"all",
                    root_key:"role_mkt",
                    key:"remove_wl",
                  }
                ]
              },
            ],
          },{
            id: "6",
            label: 'Administration',
            scope: "all",
            key:"role_administration",
            children: [
              {
                id: "6.1",
                label: 'User',
                scope:"all",
                root_key:"role_administration",
                key:"view_user"
              },
              {
                id: "6.2",
                label: 'Role',
                scope:"all",
                root_key:"role_administration",
                key:"view_role"
              }
            ],
          },          
          {
            id: "7",
            label: 'Automation',
            scope: "all",
            key:"role_lcm",
            children: [
              {
                id: "7.1",
                label: 'View SPP LCM',
                scope:"all",
                root_key:"role_lcm",
                key:"view_spp"
              },
              {
                id: "7.2",
                label: 'View AOS LCM',
                scope:"all",
                root_key:"role_lcm",
                key:"view_aos"
              },
              {
                id: "7.3",
                label: 'View Move',
                scope:"all",
                root_key:"role_lcm",
                key:"view_move"
              },
              {
                id: "7.4",
                label: 'View Auto maintenance',
                scope:"all",
                root_key:"role_lcm",
                key:"view_atm"
              }
            ],
          },
        ]
}

const mutations = {
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_SIZE: (state, size) => {
    state.size = size
    Cookies.set('size', size)
  }
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

<template>
	<div class="root" @click="changeStatus"
		:style="{ width: rootWidth + 'em', height: height + 'em',borderRadius:radius+'em' }">
		<div class="strip" :style="{ width: stripWidth + 'em', height: height + 'em',borderRadius:radius+'em' }">
			<div class="node" :style="{ width: nodeWidth + 'em', height: height + 'em' }">
			</div>
		</div>
	</div>
</template>

<script>
export default {
    name:"switchView",
		props: {
			size: {
				type:String,
				default:1
			},
			check: {
				type:Number,
				default:-1
			}
		},
		data() {
			return {
				ismid: true,
				isright: false,
				rootWidth: 3,
				height: 1,
				stripWidth: 1,
				nodeWidth: 1,
				radius: 1,
				direct: 1,
				checkValue: -1
			}
		},
		methods: {
			changeStatus() {
				if(this.checkValue===1)this.direct=0
				else if(this.checkValue===-1)this.direct=1
				this.checkValue=this.direct===1?this.checkValue+1:this.checkValue-1
				this.stripWidth = (2+this.checkValue) * this.size
				this.$emit("change", this.checkValue)
			}
		},
		model: {
			prop: 'check',
			event: 'change'
		},
		created() {
			if(this.check==1)this.direct = 0
			this.checkValue=this.check
			this.stripWidth = (2+this.checkValue) * this.size
			this.rootWidth *= this.size
			this.height *= this.size
			this.nodeWidth *= this.size
			this.radius *= this.size
		}
	}
</script>

<style scoped lang="scss">
	.root {
		position: relative;
		display: inline-block;
		box-sizing: content-box;
		font-size: 30px;
		background-color: white;
		border: 1px solid rgba(221, 221, 221, 1.0);
		cursor: pointer;
	}

	.node {
		position: absolute;
		top: 0;
		right: 0;
		background-color: white;
		border-radius: 100%;
		box-shadow: 0 3px 1px 0 rgba(0, 0, 0, 0.05),
			0 2px 2px 0 rgba(0, 0, 0, 0.1), 0 3px 3px 0 rgba(0, 0, 0, 0.05);
		transition: transform 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05);
	}

	.strip {
		position: absolute;
		top: 0;
		left: 0;
		background: rgba(95, 184, 120, 1);
		transition: width 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05);
	}
</style>

2025-08-01 11:03:12,771 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:03:13,484 INFO Getting the cluster list from PC.
2025-08-01 11:03:14,157 INFO Getting cluster list from ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:03:14,158 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/api/nutanix/v3/clusters/list, method: POST, headers: None
2025-08-01 11:03:14,158 INFO params: None
2025-08-01 11:03:14,158 INFO User: <EMAIL>
2025-08-01 11:03:14,158 INFO payload: {'kind': 'cluster'}
2025-08-01 11:03:14,168 INFO files: None
2025-08-01 11:03:14,168 INFO timeout: None
2025-08-01 11:03:47,679 INFO Got the cluster list from PC, searching the target cluster RETSEELM-NXC000 in the list.
2025-08-01 11:04:19,712 INFO RETSEELM-NXC000 exists in ssp-dhd2-ntx.ikead2.com, continue.
2025-08-01 11:04:51,522 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 11:04:51,522 INFO params: None
2025-08-01 11:04:51,523 INFO User: <EMAIL>
2025-08-01 11:04:51,523 INFO payload: None
2025-08-01 11:04:51,523 INFO files: None
2025-08-01 11:04:51,523 INFO timeout: 30
2025-08-01 11:04:52,809 INFO Getting host list from RETSEELM-NXC000.
2025-08-01 11:04:52,809 INFO Got the host list from RETSEELM-NXC000.
2025-08-01 11:04:52,809 INFO Got the host list.
2025-08-01 11:04:52,809 INFO Getting vault from IKEAD2.
2025-08-01 11:04:53,389 INFO Getting Site_Pe_Nutanix.
2025-08-01 11:04:53,855 INFO Got Site_Pe_Nutanix.
2025-08-01 11:04:53,855 INFO Getting Site_Pe_Admin.
2025-08-01 11:04:54,332 INFO Got Site_Pe_Admin.
2025-08-01 11:04:54,332 INFO Getting Site_Oob.
2025-08-01 11:04:54,820 INFO Got Site_Oob.
2025-08-01 11:04:54,820 INFO Getting Site_Ahv_Nutanix.
2025-08-01 11:04:55,300 INFO Got Site_Ahv_Nutanix.
2025-08-01 11:04:55,300 INFO Getting Site_Ahv_Root.
2025-08-01 11:04:55,789 INFO Got Site_Ahv_Root.
2025-08-01 11:04:55,789 INFO Getting Site_Gw_Priv_Key.
2025-08-01 11:04:56,304 INFO Got Site_Gw_Priv_Key.
2025-08-01 11:04:56,304 INFO Getting Site_Gw_Pub_Key.
2025-08-01 11:04:56,848 INFO Got Site_Gw_Pub_Key.
2025-08-01 11:04:56,848 INFO Getting Site_Pe_Svc.
2025-08-01 11:04:57,333 INFO Got Site_Pe_Svc.
2025-08-01 11:05:05,332 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 11:05:05,332 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-01 11:05:05,332 INFO params: None
2025-08-01 11:05:05,332 INFO User: <EMAIL>
2025-08-01 11:05:05,332 INFO payload: None
2025-08-01 11:05:05,332 INFO files: None
2025-08-01 11:05:05,332 INFO timeout: 30
2025-08-01 11:05:06,658 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-08-01 11:05:06,658 WARNING API response is not ok, going to do the 2 retry...
2025-08-01 11:05:07,878 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-08-01 11:05:07,879 WARNING API response is not ok, going to do the 3 retry...
2025-08-01 11:05:09,110 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-08-01 11:05:09,110 WARNING API response is not ok, going to do the 4 retry...
2025-08-01 11:05:10,364 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-08-01 11:05:10,364 WARNING API response is not ok, going to do the 5 retry...
2025-08-01 11:05:11,592 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-08-01 11:05:11,592 ERROR Failed to get the VM list from RETSEELM-NXC000. Error message:502 Bad Gateway: Out of retry times when calling https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms. Response: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'

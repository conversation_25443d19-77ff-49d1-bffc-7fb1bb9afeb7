#C{$ICC_NetworkAvailable}
#'***********************************************************************************************
#'*     ____         _______          _______
#'*    |\   \       /\      \        /\      \
#'*    | \___\     |\ \______\      |\ \______\
#'*    | |   |     | \/       \     | \/       \
#'*    | |   |     | |   _____/     | |   _____/
#'*    | |   |     | |  |           | |  |
#'*    | |   |     | |  |           | |  |
#'*    | |   |     | |  |___        | |  |___
#'*    | |   |     | |  |   \       | |  |   \
#'*    | |   |     | |  |____\      | |  |____\
#'*    \ |   |      \|        \      \|        \
#'*     \|___|KEA    \________/ommon  \________/lient
#'*
#'*                     Small step for man, giant leap for mankind!
<#'***********************************************************************************************
.Synopsis

    Author:     Shuai Tao 
    Date:       2024-04-02
    TargetOS:   ESXI 7.0.3
    Revision:   2024-04-02). Initial version of the script. SHTAO8
                

.Description

    This script will gather various VMs detail infos and update the DB in DHUP .

.Example

    1. .\Update-VMsInfos_daily.ps1
    2. Supply a vCenter UserName/Password
    

#>
# Include Begin code
    # Include ICCScriptResource.ps1 if the scripts is not executed from runscript
    $Error.Clear()
    $ErrorACTIONPreference = "Ignore"

    # Check if the -Debug parameter is specified
    $boldebug = $false
    if ($args.count -gt 0) {
        if ($args[-1] -eq "-DEBUG") {
            $boldebug = $true
        }
    }
    if (!($ICC_ExecutedFromRunscript)) {
        . "C:\Program Files\IKEA\Bin\ICCScriptResource.ps1" "Startup"
    }
   
    # Log total script time
    ICC_ScriptLogger ("Script Execution Time: " + (([math]::Round(((measure-command {
# End Begin Include code

#**************************
# Script code STARTS here *
#**************************

# SSL skip check, stolen straight out of Interwebz
if (-not("dummy" -as [type])) {
    add-type -TypeDefinition @"
    using System;
    using System.Net;
    using System.Net.Security;
    using System.Security.Cryptography.X509Certificates;

    public static class Dummy {
        public static bool ReturnTrue(object sender,
            X509Certificate certificate,
            X509Chain chain,
            SslPolicyErrors sslPolicyErrors) { return true; }

        public static RemoteCertificateValidationCallback GetDelegate() {
            return new RemoteCertificateValidationCallback(Dummy.ReturnTrue);
        }
    }
"@
}

[System.Net.ServicePointManager]::ServerCertificateValidationCallback = [dummy]::GetDelegate()
 function Create-AesManagedObject(){
        param(
            [string] $Key,
            [string] $IV
        )
        $AesManaged           = New-Object "System.Security.Cryptography.AesManaged"
        $AesManaged.Mode      = [System.Security.Cryptography.CipherMode]::CBC
        $AesManaged.Padding   = [System.Security.Cryptography.PaddingMode]::pkcs7
        $AesManaged.BlockSize = 128
        $AesManaged.KeySize   = 256
        if ($IV) {
            if ($IV.getType().Name -eq "String") {
                $AesManaged.IV = [System.Convert]::FromBase64String($IV)
            }else {
                $AesManaged.IV = $IV
            }
        }
        if ($Key) {
            if ($Key.getType().Name -eq "String") {
                $AesManaged.Key = [System.Convert]::FromBase64String($Key)
            }else {
                $AesManaged.Key = $Key
            }
        }
        return $AesManaged
    }
    function Load-Vars(){
        # Get base location
        # Read configuration and convert to PS object
    
        if (Test-Path -Path "$PSScriptRoot\..\vars.json") {
            $Vars = Get-Content -Path "$PSScriptRoot\..\vars.json" | ConvertFrom-Json -Depth 6
        }else {
            Write-Host "Json file not existed."
        }
        return $Vars
    }
    function Decrypt-String(){
        param(
            [string] $Key,
            [string] $IV,
            [string] $Encrypted
        )
        try {
            $Bytes       = [System.Convert]::FromBase64String($Encrypted)
            $AesManaged  = Create-AesManagedObject -Key $Key -IV $IV
            $Decryptor   = $AesManaged.CreateDecryptor()
            $Unencrypted = $Decryptor.TransformFinalBlock($Bytes, 0, $Bytes.Length)
            $Decrypted   = [System.Text.Encoding]::UTF8.GetString($Unencrypted).Trim([char]0)
            $AesManaged.Dispose()
        }
        catch {
            Write-Host "Failed to convert the Decrypt."
            return $null
        }
        return $Decrypted
}
    function Select-DhServiceAccount(){
        param (
            [Parameter(Mandatory = $true)] $Vars,
            [string]                       $Usage
        )
        try {
            $Query = Invoke-Sqlcmd -ServerInstance $Vars.DB.Instance `
                                -Database $Vars.DB.Name `
                                -Username $Vars.DB.Username `
                                -Password $Vars.DB.Pword `
                                -TrustServerCertificate:$true `
                                -Query "SELECT * FROM [dbo].[dh_service_account] $(if ($Usage) {
                                "WHERE usage = '$Usage'"})"
            return $Query
        }
        catch {
            Write-Host "Failed to get the service account."
            return $null
        }
        return $null
    }

# vCenter Federation list.
    $VCSAs = @(
        #Begin - DC5/APAC
        'vc-aufed01.ikea.com'
        'vc-infed01.ikea.com',`
        'vc-jpfed01.ikea.com',`
        'vc-krfed01.ikea.com',`
        # Begin - DC6/NA #>
        'vc-cafed01.ikea.com'
        'vc-usfed01.ikea.com',`
        'vc-usfed02.ikea.com',`
        # Begin - DC78/EU
        'vc-atfed01.ikea.com'
        'vc-befed01.ikea.com',`
        'vc-chfed01.ikea.com',`
        'vc-czfed01.ikea.com',`
        'vc-defed01.ikea.com',`
        'vc-defed02.ikea.com',`
        'vc-dkfed01.ikea.com',`
        'vc-esfed01.ikea.com',`
        'vc-fifed01.ikea.com',`
        'vc-frfed01.ikea.com',`
        'vc-gbfed01.ikea.com',`
        'vc-hufed01.ikea.com',`
        'vc-itfed01.ikea.com',`
        'vc-nlfed01.ikea.com',`
        'vc-nofed01.ikea.com',`
        'vc-plfed01.ikea.com',`
        'vc-ptfed01.ikea.com',`
        # 'vc-rufed01.ikea.com',`
        'vc-sefed01.ikea.com'
        # 'vc-uafed01.ikea.com',`
        # # Begin- DC9/CN
        'vc-cnfed01.ikea.com'
    )
    ICC_ScriptLogger '' 'N'

# VCSA Credential
    ICC_ScriptLogger "ACTION: loading the DB vars..." 'w'
    $Vars = Load-Vars
    ICC_ScriptLogger "STATUS: Successfully to get the SA infos" 'i'
    Write-Host $Vars.DB
    ICC_ScriptLogger "ACTION: Fetching the SA in the DB ..." 'w'
    $SvcAccount = Select-DhServiceAccount -Vars $Vars -Usage "simplivity_pm"|Select-Object -First 1
    $Password = Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted
    $VCSA_Password = ConvertTo-SecureString -String $Password -AsPlainText -Force
    Write-Host $VCSA_Password,$SvcAccount.username
    $VCSA_Credential = New-Object System.Management.Automation.PSCredential -ArgumentList $SvcAccount.username, $VCSA_Password
    ICC_ScriptLogger "STATUS: Successfully to get the account infos" 'i'
    ICC_ScriptLogger '' 'N'

# Variables
    $j = 1
    $Failed_VC=@()
    $Output = @{'Failed_VC'=@();'DecommissionVM_List'=@();'NewVM_List'=@()}
    $sqlQuery = [System.Text.StringBuilder]::new()

# Get all hosts in each federation
    foreach ($VCSA in $VCSAs) {
        try {
            ICC_ScriptLogger "ACTION: Initializing SQLQuery..." 'w'
            $sqlQuery.Clear()
            ICC_ScriptLogger "ACTION: Connecting to vCenter '$VCSA'..." 'w'
            if (Connect-VIServer -Server $VCSA -Credential $VCSA_Credential) {
                ICC_ScriptLogger "STATUS: Successfully connected to '$VCSA'" 'i'
                $Current_DBdata = invoke-sqlcmd -Username $Vars.DB.Username -Password $Vars.DB.Pword -Database $Vars.DB.Name  -Query "select * from dh_retail_sli_vms WHERE vc_fqdn='$VCSA'" -ServerInstance $Vars.DB.Instance -TrustServerCertificate
                Write-Host $Current_DBdata.count
                $VMs= Get-VM|Select-Object HardwareVersion,Name,FolderId,Folder,ResourcePoolId,PersistentId,UsedSpaceGB,ProvisionedSpaceGB,MemoryGB,HARestartPriority,NumCpu,CoresPerSocket,HAIsolationResponse,GuestId,VMHostId,VMHost,CreateDate,PowerState,ExtensionData,Id
                $New_VMs = $VMs|Where-Object{$_.id -notin $Current_DBdata.vm_id}
                Write-Host $VMs.count
                $Decommissioned_VMs = $Current_DBdata|Where-Object{$_.vm_id -notin $VMs.id}
                Write-Host $Decommissioned_VMs.count
                $Existed_VMs = $VMs|Where-Object{$_.id -in $Current_DBdata.vm_id}
                Write-Host $Existed_VMs.count
                if ($New_VMs){
                    Write-Host "newe"
                    $i = 0 
                    #$sqlQuery.AppendLine("SET IDENTITY_INSERT dh_retail_sli_vms ON ")
                    $sqlQuery.AppendLine("INSERT INTO dh_retail_sli_vms ")
                    $sqlQuery.AppendLine("(id,version,name,folder_id,folder,resourcepool_id,persistent_id,used_space_gb,provisioned_space_gb,memory_total_gb,harestart_priority,num_cpu,cores_persocket,haisolation_response,guest_id,host_id,vmhost_name,create_date,power_state,cluster_id,guest_name,vm_id,status,vc_fqdn) ")
                    $sqlQuery.AppendLine("VALUES ")
                    $VMs_total = invoke-sqlcmd -Username $Vars.DB.Username -Password $Vars.DB.Pword -Database $Vars.DB.Name  -Query "select * from dh_retail_sli_vms " -ServerInstance $Vars.DB.Instance -TrustServerCertificate
                    $j=$VMs_total.count+1
                    while($i -lt $New_VMs.count){
                        $Output.NewVM_List += @{"name"= $VM.name;"message"="New VM"}
                        write-host $New_VMs[$i].name
                        $VMStatus = "Y"
                        $UsedSpaceGB = [Math]::Round($New_VMs[$i].UsedSpaceGB)
                        $ProvisionedSpaceGB = [Math]::Round($New_VMs[$i].ProvisionedSpaceGB)
                        $Guest_name=$New_VMs[$i].ExtensionData.Guest.HostName
                        $Cluster_Id = (invoke-sqlcmd -Username $Vars.DB.Username -Password $Vars.DB.Pword -Database $Vars.DB.Name  -Query "select * from dh_retail_sli_host where name='$($New_VMs[$i].VMHost)'" -ServerInstance $Vars.DB.Instance -TrustServerCertificate).cluster_id
                        write-host $MemoryTotalGB,$Cluster_Id
                        $sqlQuery.AppendLine("('"+$j +"','"+$($New_VMs[$i].version) +"','"+$($New_VMs[$i].Name) +"','"+$($New_VMs[$i].FolderId) +"','" `
                        +$($New_VMs[$i].Folder) +"','"+$($New_VMs[$i].ResourcePoolId) +"','"+$($New_VMs[$i].PersistentId) +"','"+$($UsedSpaceGB) +"','" `
                        +$($ProvisionedSpaceGB) +"','"+$($New_VMs[$i].MemoryGB) +"','"+$($New_VMs[$i].HARestartPriority) +"','"+$($New_VMs[$i].NumCpu) +"','" `
                        +$($New_VMs[$i].CoresPerSocket) +"','"+$($New_VMs[$i].HAIsolationResponse) +"','" +$($New_VMs[$i].GuestId) +"','"+$($New_VMs[$i].VMHostId) +"','" `
                        +$($New_VMs[$i].VMHost) +"','"+$($New_VMs[$i].CreateDate) +"','"+$($New_VMs[$i].PowerState) +"','"+$($Cluster_Id)+"','"+$($Guest_name)+"','"+$($New_VMs[$i].Id)+"','"+$($VMStatus)+"','"+$($VCSA)  +"'),")
                        $i++
                        $j++
                        write-host $i
                        if($i -eq $New_VMs.count-1){
                            $sqlQuery.AppendLine("('"+$j +"','"+$($New_VMs[$i].version) +"','"+$($New_VMs[$i].Name) +"','"+$($New_VMs[$i].FolderId) +"','" `
                            +$($New_VMs[$i].Folder) +"','"+$($New_VMs[$i].ResourcePoolId) +"','"+$($New_VMs[$i].PersistentId) +"','"+$($UsedSpaceGB) +"','" `
                            +$($ProvisionedSpaceGB) +"','"+$($New_VMs[$i].MemoryGB) +"','"+$($New_VMs[$i].HARestartPriority) +"','"+$($New_VMs[$i].NumCpu) +"','" `
                            +$($New_VMs[$i].CoresPerSocket) +"','"+$($New_VMs[$i].HAIsolationResponse) +"','" +$($New_VMs[$i].GuestId) +"','"+$($New_VMs[$i].VMHostId) +"','" `
                            +$($New_VMs[$i].VMHost) +"','"+$($New_VMs[$i].CreateDate) +"','"+$($New_VMs[$i].PowerState) +"','"+$($Cluster_Id)+"','"+$($Guest_name)+"','" `
                            +$($New_VMs[$i].Id)+"','"+$($VMStatus)+"','"+$($VCSA) +"')")
                            $j++
                            $Output.NewVM_List += @{"name"= $New_VMs[$i].Name;"message"="New VM "}
                            break
                        }
                    }
                    Write-Host $sqlQuery.ToString()
                    ICC_ScriptLogger "ACTION: Updating the details of VMs in '$VCSA' into the DB..." 'w'
                    Invoke-sqlcmd -Username $Vars.DB.Username -Password $Vars.DB.Pword -Database $Vars.DB.Name  -Query $sqlQuery.ToString() -ServerInstance $Vars.DB.Instance -TrustServerCertificate
                    ICC_ScriptLogger "STATUS: Successfully Updating the details into the DB." 'i'
                    ICC_ScriptLogger "ACTION: Initializing SQLQuery..." 'w'
                    $sqlQuery.Clear()
                    ICC_ScriptLogger '' 'N' 
                }
                if($Decommissioned_VMs){
                    Write-Host "d"
                    foreach($VM in $Decommissioned_VMs){
                        #$sqlQuery.AppendLine("SET IDENTITY_INSERT dh_retail_sli_vms ON ")
                        $sqlQuery.AppendLine("UPDATE dh_retail_sli_vms ")
                        $sqlQuery.AppendLine("SET status ='N' ")
                        $sqlQuery.AppendLine("WHERE id ='$($VM.id)'")
                        Write-Host $sqlQuery.ToString()
                        ICC_ScriptLogger "ACTION: Updating the details of Decommissioned VMs in '$VCSA' into the DB..." 'w'
                        Invoke-sqlcmd -Username $Vars.DB.Username -Password $Vars.DB.Pword -Database $Vars.DB.Name  -Query $sqlQuery.ToString() -ServerInstance $Vars.DB.Instance -TrustServerCertificate
                        ICC_ScriptLogger "STATUS: Successfully Updating the details into the DB." 'i'
                        ICC_ScriptLogger "ACTION: Initializing SQLQuery..." 'w'
                        $sqlQuery.Clear()
                        ICC_ScriptLogger '' 'N'
                        $Output.DecommissionVM_List += @{"name"= $VM.name;"message"="VM decommissioned"}
                    }
                }
                foreach($VM in $Existed_VMs){
                    Write-Host "upsate"
                    $sqlQuery.Clear()
                    $UsedSpaceGB = [Math]::Round($VM.UsedSpaceGB)
                    $ProvisionedSpaceGB = [Math]::Round($VM.ProvisionedSpaceGB)
                    $Guest_name=$VM.ExtensionData.Guest.HostName
                    $Cluster_Id = (invoke-sqlcmd -Username $Vars.DB.Username -Password $Vars.DB.Pword -Database $Vars.DB.Name  -Query "select * from dh_retail_sli_host where name='$($VM.VMHost)'" -ServerInstance $Vars.DB.Instance -TrustServerCertificate).cluster_id
                    #$sqlQuery.AppendLine("SET IDENTITY_INSERT dh_retail_sli_vms ON ")
                    $sqlQuery.AppendLine("UPDATE dh_retail_sli_vms ")
                    $update_query = "SET name='$($VM.Name)', used_space_gb='$($UsedSpaceGB)',provisioned_space_gb='$($ProvisionedSpaceGB)',cluster_id='$($Cluster_Id)',guest_id='$($VM.GuestId)',power_state='$($VM.PowerState)', num_cpu='$($VM.NumCpu)',host_id='$($VM.VMHostId)',Guest_name='$($Guest_name)'"
                    $sqlQuery.AppendLine($update_query)
                    $sqlQuery.AppendLine("WHERE vm_id ='$($VM.id)' and host_id='$($vm.VMHostId)'")
                    Write-Host $sqlQuery.ToString()
                    ICC_ScriptLogger "ACTION: Updating the details of new VMs into the DB..." 'w'
                    Invoke-sqlcmd -Username $Vars.DB.Username -Password $Vars.DB.Pword -Database $Vars.DB.Name  -Query $sqlQuery.ToString() -ServerInstance $Vars.DB.Instance -TrustServerCertificate
                    ICC_ScriptLogger "STATUS: Successfully Updating the details into the DB." 'i'
                    ICC_ScriptLogger "ACTION: Initializing SQLQuery..." 'w'
                    $sqlQuery.Clear()
                    ICC_ScriptLogger '' 'N'
                }
            }
            else{
                $Failed_VC += $VCSA
                $Output.Failed_VC += @{"name"=$VCSA;"message"="Failed to logon the VC $($VCSA)"}
                ICC_ScriptLogger "ERROR: Unable to connect to '$VCSA_'!" 'E'
            }
            ICC_ScriptLogger "ACTION: Disconnecting to vCenter '$VCSA'..." 'w'
            Disconnect-VIServer -Server $VCSA -Confirm:$false
        
        }
        catch {
            $Failed_VC += $VCSA
            $Output.Failed_VC += @{"name"=$VCSA;"message"="Failed to logon the VC $($VCSA)"}
            ICC_ScriptLogger "ERROR: Failed to generated the data from '$VCSA_'!" 'E'
            <#Do this if a terminating exception happens#>
        }
        # finally {
        #     Disconnect-VIServer -Server * -Confirm:$false
        #     <#Do this after the try block regardless of whether an exception occurred or not#>
        # }
    }

    if ($Failed_VC) {
        foreach ($_ in $Failed_VC | Sort-Object) {
            ICC_ScriptLogger "ERROR: Unable to connect to '$_'!" 'E'
        }
    }
#generate an mail with the report
    $MailBody = "Hi, here is the report of SLI VMs Update script $(Get-date -Format "yyyy-MM-dd HH:ss")`n"
    $MailBody += "`tFailed VC: `n"
    if($Output.Failed_VC){
        foreach($vc in $($Output.Failed_VC)){
        $MailBody += "`t`t$($vc.name):$($vc.message)`n"
        }
    }else{
        $MailBody += "`t`tNone.`n"
    }
    $MailBody += "`tVM Decommissioned: `n"
    if($Output.DecommissionVM_List){
        foreach($vm in $($Output.DecommissionVM_List)){
        $MailBody += "`t`t$($vm.name):$($vm.message)`n"
        }
    }else{
        $MailBody += "`t`tNone.`n"
    }
    $MailBody += "`tNew VMs: `n"
    if($Output.NewVM_List){
        foreach($vm in $($Output.NewVM_List)){
        $MailBody += "`t`t$($vm.name):$($vm.message)`n"
        }
    }else{
        $MailBody += "`t`tNone.`n"
    }

    write-host $MailBody
    Send-MailMessage -From "<EMAIL>" -To "<EMAIL>" -Subject "Running Result Of SLI VMs Update Daily" -SmtpServer "smtp-gw.ikea.com" -Body $MailBody

#************************
# Script code ENDS here *
#************************

# Include Endcode
    if ($Error.Count -ne 0) {
        ICC_ScriptLogger "The following errors happend during execution:" "e"
        ICC_ScriptLogger $Error "e"
        $Error.Clear()
    }}).TotalMilliSeconds),0)).tostring()+" milliseconds")) "L"

    if ($ICC_MyScriptDate -eq $null) {
        $ICC_MyScriptDateInfo = (get-item $myinvocation.invocationname)
        [datetime]$ICC_MyScriptDate = $ICC_MyScriptDateInfo.lastWriteTimeUTC
    }
    ICC_ScriptLogger ("----- $ICC_UserName - $ICC_Computername - " + ($myinvocation.MyCommand) + " - [Version: " + ([datetime]$ICC_MyScriptDate).tostring("yyyyMMdd HHmmss") + "] -----") "L"
    ICC_ScriptLogger " " "N"

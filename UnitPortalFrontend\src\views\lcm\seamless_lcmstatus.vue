<template>
  <div class="dashboard-editor-container" style="height:100%">
    <el-card  class="box-card" body-style="{ padding: '20px' }">
      <div slot="header">
        <div style="font-size:25px">
          Siab
        </div>
      </div>
      <el-row>
        <el-col :span="12">
          <div class="chart-wrapper">
            <PieVMOS/>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-wrapper">
            <BarChart :version="siab_aos_targetversion"/>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card  class="box-card">
      <div slot="header">
        <div style="font-size:25px">
          Wiab
        </div>
      </div>
      <el-row>
        <el-col :span="12">
          <div class="chart-wrapper">
            <PieVMOS/>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-wrapper">
            <BarChart :version="siab_aos_targetversion"/>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import PieVMOS from './scomponents/PieSlcm_Siab'
import BarChart from './scomponents/BarChartSlcm_Siab'
import PieVMOS_Wiab from './scomponents/PieSlcm_Wiab'
import BarChart_Wiab from './scomponents/BarChartSlcm_Wiab'
import { GetTargetVersion} from  '@/api/automation'

const lineChartData = {
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145]
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130]
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130]
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130]
  }
}

export default {
  name: 'DashboardAdmin',
  components: {
    PieVMOS,
    BarChart,
    PieVMOS_Wiab,
    BarChart_Wiab
  },
  data() {
    return {
      siab_aos_targetversion: null,
      wiab_aos_targetversion: null,
      lineChartData: lineChartData.newVisitis,
    }
  },
  created() {
    this.get_targetversion()
  },
  methods: {
    handleSetLineChartData(type) {
      this.lineChartData = lineChartData[type]
    },
    get_targetversion() {
      GetTargetVersion(this.$store.getters.token).then(response => {
        
          // this.temp.targetversion = response.data
          // console.log(this.temp.targetversion)
          response.data.forEach((item)=>{
            if(item.index_label=='default'){
              if(item.aos_version){
                this.siab_aos_targetversion = item.aos_version
              } else {
                alert("No Siab AOS target version found, please check the target version configuration.")
              }
            }
            if(item.index_label=='Warehouse'){
              if(item.aos_version){
                this.wiab_aos_targetversion = item.aos_version
              }else{
                alert("No Wiab AOS target version found, please check the target version configuration.")
              }
            }
          })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.box-card {
  width: 100%;
  max-width: 100%;
  margin: 10px auto;
  border-radius: 6px;
}
.dashboard-editor-container {
  padding: 2px 22px 2px 22px;
  background-color: rgb(240, 242, 245);
  // background-color: #353536;
  position: relative;

  .github-corner {
    position: absolute;
    top: 0px;
    border: 0;
    right: 0;
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>

# installed modules
import uuid
import json
import logging
import multiprocessing
from flask_restful import Resource
from flask import abort, request, Response, send_file, jsonify
from flask_apispec import MethodResource, doc, use_kwargs, marshal_with
# local files
from models.database import db
import static.SETTINGS as SETTING
from models.pm_models import ModelNTXPMTask
from business.loggings.loggings import DBLogging
from business.distributedhosting.pmtask import Task
from business.authentication.authentication import User
from business.distributedhosting.nutanix.pm.nutanix_pm import start_ntx_pm
from business.authentication.tokenvalidation import pmtokencheck, PrivilegeValidation
from business.generic.commonfunc import terminate_process_by_id, get_request_token, setup_logger
from swagger.ntx_pm_schema import CreateNtxPmRequestSchema, CreateNtxPmResponseSchema, AbortNtxPmRequestSchema, \
    AbortNtxPmResponseSchema, GetPmTaskInfoResponseSchema
from .route import route


@route('/api/v1/pmtasklist')
class RestfulPMTask(Resource):
    @PrivilegeValidation(privilege={"role_pm": "view_ntx_pm"})
    def get(self):
        try:
            page = request.args.get('page', type=int)
            limit = request.args.get('limit', type=int)
            _task = Task()
            task_list = _task.get_pm_task(getlog=True, page=page, limit=limit)
            return task_list
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/pm/task/log')
class RestfulPMTaskLog(Resource):
    @PrivilegeValidation(privilege={"role_pm": "view_ntx_pm"})
    def post(self):
        param = request.get_json(force=True)
        _task = Task()
        return _task.get_pm_tasklog_by_id(param["task_id"]), 200        


@route('/api/v1/pmtasks')
class RestfulNTXPMTasks(Resource):
    @PrivilegeValidation(privilege={"role_pm": "view_ntx_pm"})
    def get(self):
        try:
            _task = Task()
            task_list = _task.get_pm_task(getlog=False)
            return task_list
        except Exception:
            abort(500, "Internal error")

            


@route('/api/v1/ntx/pm/create')
class RestfulCreateNTXPM(MethodResource, Resource):
    # this is the function for creating a nutanix PM
    @doc(description="Create Nutanix PM", tags=['Power Maintenance'])
    @use_kwargs(CreateNtxPmRequestSchema, location='json', apply=False)
    @marshal_with(CreateNtxPmResponseSchema, code=200)
    @PrivilegeValidation(privilege={"role_pm": "create_ntx_pm"})
    def post(self):
        try:
            token = get_request_token()
            if param := request.get_json(force=True):
                task = Task(token=token)
                res = task.create_ntx_pm_task(param)
                if param['startnow']:
                    logging.info(f'Got one "startnow" NTX PM task, task_id:{res["id"]}, starting it !')
                    p = multiprocessing.Process(target=start_ntx_pm, args=[res['id']])
                    p.start()
                    _task = ModelNTXPMTask.query.filter_by(id=res['id']).first()
                    _task.pid = p.pid
                    db.session.commit()
                return jsonify(res)
            raise Exception({'code': 400, 'message': 'Missing fields'})  # raise if pm details not given
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/ntx/pm/update')
class RestfulUpdateNTXPM(MethodResource, Resource):  # need to optimize.
    # @use_kwargs(UpdateNtxPmRequestSchema, location='json', apply=False)
    # @marshal_with(UpdateNtxPmResponseSchema)
    @pmtokencheck
    @PrivilegeValidation(privilege={"role_pm": "create_ntx_pm"})
    def post(self):
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res, role = usr.get_role_by_token(token)
            if res:
                username = role.username  # ignore
            else:
                # if cann't get the username, then quit
                raise Exception({'code': 401, 'message': 'Username not found by the token.'})
            param = request.get_json(force=True)
            task_id = param['id']
            if not task_id:
                raise Exception({'code': 400, 'message': 'Cannot update the PM task due to lack of task id.'})
            task = Task(id=task_id, token=token)
            print('checking privilege.')
            if not task.if_operation_is_allowed():
                raise Exception({'code': 400,
                                 'message': 'Operation is not allowed. This mainly because user doesn''t have enough privilege.'})  # noqa
            res, msg = task.update_ntx_pm_task(**param)
            if res:
                logging.critical(f"PM task {task_id} updated by [{username}].")
                return Response(json.dumps(msg), status=200, mimetype='application/json')
            print(res, msg)
            raise Exception({'code': 500, 'message': msg})
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/ntx/pm/abort')
class RestfulAbortNTXPM(MethodResource, Resource):  # need to optimize.
    @doc(description="Abort Nutanix PM", tags=['Power Maintenance'])
    @use_kwargs(AbortNtxPmRequestSchema, location='json', apply=False)
    @marshal_with(AbortNtxPmResponseSchema, code=200)
    @PrivilegeValidation(privilege={"role_pm": "abort_ntx_pm"})
    def post(self):      # noqa
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res, role = usr.get_role_by_token(token)
            if res:
                username = role.username  # ignore
            else:
                # if cann't get the username, then quit
                raise Exception({'code': 401, 'message': 'Username not found by the token.'})
            param = request.get_json(force=True)
            pid, logfile, task_id = int(param['pid']), param['detaillogpath'], param['id']
            if not (pid and task_id):
                raise Exception({'code': 400, 'message': 'Cannot abort the PM task due to lack of PID or task id.'})
            if not logfile:
                raise Exception({'code': 400, 'message': 'Cannot abort the PM task due to lack of log file.'})
            # create a new logger to write log for aborting into the same log file as the PM procedure
            logger = setup_logger(str(uuid.uuid4()), logfile)
            logger.critical(f"User [{username}] is aborting this PM task.")
            res, msg = terminate_process_by_id(pid)  # kill the PM task process.
            if res:
                logging.critical(f"PM task {task_id} aborted by [{username}].")  # write logs in 2 place for aborting
                logger.critical(f"PM task {task_id} aborted by [{username}].")
                lg = DBLogging(logdir=SETTING.NTX_LOG_PATH, logtype="NTX_PM")
                lg.write_pm_log(loginfo=f'PM was aborted by {username}', logseverity='info', taskid=task_id)
                task = Task(task_id)
                res, msg = task.abort_ntx_pm_task()
                if res:
                    return Response(json.dumps(msg), status=200, mimetype='application/json')
                raise Exception({'code': 500,
                                     'message': f'Task was aborted, but failed at update the status in DB. error message:{msg}'})  # noqa
            logger.error(f"Failed to abort the PM task.message :{msg}")
            raise Exception({'code': 500, 'message': msg})
        except ValueError:
            abort(400, 'Cannot abort the PM task, make sure PID is integer and correct.')
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/ntx/pm/delete')
class RestfulDeleteNTXPM(MethodResource, Resource):  # need to optimize.
    # @use_kwargs(DeleteNtxPmRequestSchema, location='json', apply=False)
    # @marshal_with(DeleteNtxPmResponseSchema)
    @PrivilegeValidation(privilege={"role_pm": "delete_ntx_pm"})
    def post(self):
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res, role = usr.get_role_by_token(token)
            if res:
                username = role.username  # ignore
            else:
                raise Exception({'code': 401, 'message': role})  # if cann't get the username, then quit
            param = request.get_json(force=True)
            task_id = param['id']
            if not task_id:
                raise Exception({'code': 400, 'message': 'Cannot delete the PM task due to lack of task id.'})
            task = Task(task_id)
            res, msg = task.delete_ntx_pm_task()
            if res:
                logging.critical(f"PM task deleted by [{username}].")
                return Response(json.dumps(msg), status=200, mimetype='application/json')
            abort(500, f'Failed to delete the PM task, error message: {msg}')
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/pm/ntx/log/download')
class RestfulDownloadNTXPMLog(Resource):  # need to optimize.
    def post(self):
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res, role = usr.get_role_by_token(token)
            if res:
                username = role.username  # noqa    # pylint: disable=W0612
            else:
                # if cann't get the username, then quit
                raise Exception({'code': 401, 'message': 'Username not found by the token.'})
            param = request.get_json(force=True)
            filepath = param['filepath']
            if not filepath:
                raise Exception({'code': 400, 'message': 'Cannot download the log file due to lack of file path.'})
            return send_file(filepath)
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/ntx/pm/info/<int:pm_id>')
class RestfulGetNTXPMInfo(Resource, MethodResource):
    # @webargs.flaskparser.use_args({'pm_id': fields.Int(required=True, description="the pm task id")}, location='view_args')
    @doc(description="Get Nutanix PM task info", tags=['Power Maintenance'])
    @PrivilegeValidation(privilege={"role_pm": "view_ntx_pm"})
    @marshal_with(GetPmTaskInfoResponseSchema, code=200)
    def get(self, pm_id):
        task = Task(id=pm_id)
        pmtask = task.get_pm_task_by_id()
        print(pmtask)
        return Response(json.dumps(pmtask), status=200, mimetype='application/json')

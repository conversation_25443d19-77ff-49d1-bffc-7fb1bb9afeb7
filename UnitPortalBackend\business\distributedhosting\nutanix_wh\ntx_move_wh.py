# installed module
import logging
import re
import os
import json
import time
import datetime
import multiprocessing
import uuid

from flask import Flask
import traceback
import sys
# local file
import static.SETTINGS as SETTING
from base_path import application_path
from business.loggings.loggings import DBLogging
from business.authentication.authentication import ServiceAccount
from business.distributedhosting.nutanix.nutanix import Ntx_Move, PrismElement
from business.generic.commonfunc import create_file, setup_common_logger, test_pinging
from models.database import db
from models.auth_models import ModelUser
from models.ntx_models_wh import  ModelWHMOVETask, ModelWHMOVETaskSchema, \
    ModelWHNTXMOVE, ModelWarehousePrismElement
from business.generic.commonfunc import DBConfig


class MoveWh():
    def __init__(self, site=None, ntx_cluster=None, vm_list=None, token=None, plantype=None, tasktype=None, gst_username=None, gst_password=None) -> None:
        self.vc = "itseelm-bb4441.ikea.com"
        self.cluster = site
        self.token = token
        self.task = dict()
        self.logger = None
        self.pe = ntx_cluster
        self.vm_list = vm_list
        self.tasktype = tasktype
        self.plantype = plantype
        self.gst_username = gst_username
        self.gst_password = gst_password
        # rep = re.compile(re.escape(".IKEA.COM"), re.IGNORECASE)
        # if re.search(rep, ntx_cluster):
        #     self.pe_fqdn = ntx_cluster
        #     self.pe = rep.sub("", self.pe_fqdn)
        _move_sa = ServiceAccount(usage="ntx_move")
        _wh_sa = ServiceAccount(usage="wh_vcenter")
        self.whsa = _wh_sa.get_service_account()
        # _wh_sa = ServiceAccount(usage="wh")
        # self.whsa = _wh_sa.get_service_account()
        self.movesa = _move_sa.get_service_account()
        _move_defsa = ServiceAccount(usage="ntx_move_default")
        self.move_defsa = _move_defsa.get_service_account()
        self.lg = DBLogging(logdir=SETTING.MOVE_LOG_PATH, logtype="NTX_WHMOVE")
        
    def create_task(self, task_id=None):
        try:
            task = dict()
            user = ModelUser.query.filter_by(token=self.token).first()
            if not user:
                raise Exception("Cann't find a user by this token, try re-login maybe ?")
            ongoing_tasks = ModelWHMOVETask().check_ifmovetask_existence(tasktype=self.tasktype, pe=self.pe)
            if ongoing_tasks:
                raise Exception("Task is In Progress, Skipped.")
            if not task_id:
                # we need to create a task first.
                task["creater"] = user.username
                task["pe"] = self.pe 
                task["cluster"] = self.cluster
                task["status"] = "Not Started"
                task["tasktype"] = self.tasktype
                task["createdate"] = datetime.datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
                _move_task = ModelWHMOVETask(**task)
                db.session.add(_move_task)
                db.session.commit()
            else:
                # task is already there, we detected a finished middle task, and proceed.
                pass
            _move_task_schema = ModelWHMOVETaskSchema()
            self.task = _move_task_schema.dump(_move_task)
            self.start_subprocess_for_move_preparation()
            return True, ''
        except Exception as e:
            logging.error(str(e))
            return False, str(e)

    def start_subprocess_for_move_preparation(self):
        if self.task['tasktype'] == "new_move":
            logging.info("Starting move_development process ")
            p = multiprocessing.Process(target=self.create_move_vm)
            p.start()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.pid = p.pid
            db.session.commit()
        elif self.task['tasktype'] == "preparation":
            logging.info("Starting move_preparation process ")
            p = multiprocessing.Process(target=self.ntx_move_preparation)
            p.start()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.pid = p.pid
            db.session.commit()
        elif self.task['tasktype'] == "cutover":
            logging.info("Starting move_cutover process ")
            p = multiprocessing.Process(target=self.start_move_cutover)
            p.start()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.pid = p.pid
            db.session.commit()
            
    def ntx_move_preparation(self):
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)  # db is in models.py
        app.app_context().push()  # context definition
        # self.logger=logging
        self.pe = self.pe.upper()
        self.cluster = self.cluster.upper()
        plan_uuid = []

        try:
            if local_log_path := create_file(filepath=SETTING.MOVE_LOG_PATH,filename=f'Migration_Plan_{self.plantype}_{self.pe}_{datetime.datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}'):  # noqa
                logging.info(f"Local log file created, path: {local_log_path}.")
                _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
                _task.detaillogpath = local_log_path
                db.session.commit()
            else:
                raise Exception('Failed to create local log file, PM will be ended now.')
            self.logger = setup_common_logger(str(uuid.uuid4()), local_log_path)
            self.logger.info("Checking if the task existence...")
            # task = ModelSLICluster.query.filter_by(name=self.cluster).first()
            # if re.match(SETTING.STATUS['INPROGRESS'], task.stagepreparation, re.I):
            #     logging.info(f"Preparation task already started.")
            #     self.logger.info(f"Preparation task already there.")
            #     return
            # self.logger.info(f"Preparation task not existed, will new one.")
            # task.stagepreparation = "In Progress"
            # db.session.commit() 
            # _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            # _task.status = "In Progress"
            # db.session.commit()
            self.logger.info("Checking if the MOVE Creation task existence.")
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            if not task:
                self.logger.info("Not a planned cluster.")
                self.lg.write_move_log(loginfo='Not a planned cluster.', taskid=self.task['id'], logseverity='warning')
                return
            # if re.match(SETTING.STATUS['INPROGRESS'], task.stagepreparation, re.I):
            #     self.logger.info("Another Creation task is running there.")
            #     self.lg.write_move_log(loginfo=f'Another MOVE Creation task for {self.pe} is running there. Skipped.', taskid=self.task['id'], logseverity='error')            
            #     return
            if task.planuuid:
                plan_uuid.append(task.planuuid)
            task.stagepreparation = "In Progress"
            db.session.commit() 
            self.logger.info(f"No running MOVE Preparation task for {self.pe}.")
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.status = "In Progress"
            db.session.commit()
            self.logger.info(f"Trying to start the Preparation task for {self.pe} Move.")
            self.lg.write_move_log(loginfo=f'Trying to start the Preparation task for {self.pe} Move.', taskid=self.task['id'], logseverity='info')
            logging.info("Move task started.")
            provider_list = []
            # self.logger.info("Checking if ESXI cluster existed.")
            # self.lg.write_move_log(loginfo='Checking if ESXI cluster existed.', taskid=self.task['id'], logseverity='info')
            # logging.info("Check if ESXI cluster existed")
            # _cluster = ModelWHNTXMOVE().check_ifcluster_existence(cluster_name=self.cluster)
            # logging.info(f"{_cluster}")
            # if not _cluster:
            #     self.logger.info("ESXI cluster not existed. please check")
            #     self.lg.write_move_log(loginfo='ESXI cluster not existed. please check.', taskid=self.task['id'], logseverity='error')
            #     raise Exception('The cluster is not existing on the SLI.')
            # self.logger.info("Successed to get ESXI cluster.")
            # self.lg.write_move_log(loginfo='Successed to get ESXI cluster.', taskid=self.task['id'], logseverity='success')
            # vcpc.append(_cluster.vc_fqdn)
            self.logger.info("Checking if NTX cluster existed...")
            self.lg.write_move_log(loginfo='Checking if NTX cluster existed...', taskid=self.task['id'], logseverity='info')
            logging.info("Check if NTX cluster existed")
            new_ntx_cluster = ((self.pe).split("."))[0]
            _ntxcluster = ModelWarehousePrismElement().check_ifntx_existence_db(cluster_name=new_ntx_cluster)
            if not _ntxcluster:
                self.logger.info("NTX cluster not existed. please check.")
                self.lg.write_move_log(loginfo='NTX cluster not existed. please check.', taskid=self.task['id'], logseverity='error')
                raise Exception('The cluster is not existing on the NTX.')
            self.logger.info("Successed to get NTX cluster.")
            self.lg.write_move_log(loginfo='Successed to get NTX cluster.', taskid=self.task['id'], logseverity='success')
            # vcpc.append(_ntxcluster.fqdn)
            logging.info("Init the Ntx_Move.")
            ntx_move = Ntx_Move(pe=self.pe, sli_sa=self.whsa, logger=self.logger)
            # ntx_move = Ntx_Move(pe=self.pe,  logger=self.logger)
            _pe = PrismElement(pe=self.pe, logger=self.logger)
            self.logger.info(f'Logging on {self.pe} with "admin"...')
            # res = ntx_move.check_vmlist_connection()
            # if not res:
            #     pre_status = False
            #     self.logger.info(f'Failed to log on {self.pe} with "admin".')
            #     self.lg.write_move_log(loginfo=f'Failed to log on {self.pe} with "admin".', taskid=self.task['id'], logseverity='error')
            #     raise Exception(f'Failed to log on {self.pe} with "admin".')
            self.logger.info(f'Successed to log on {self.pe} with "admin"...')
            move_vm_name = self.cluster + "-MOVE_5.4_A" if "NXC000" in self.pe else self.cluster + "-MOVE_5.4_B"
            res, move_ip = ntx_move.get_move_ip(move_name=move_vm_name)
            if not res:
                self.logger.info('Failed to get the MOVE IP.')
                raise Exception('Failed to get the MOVE IP.')
            self.logger.info('Logging on MOVE ...')
            self.lg.write_move_log(loginfo='Logging on MOVE Application ...', taskid=self.task['id'], logseverity='info')
            logging.info("Logging on move....")
            token_res, move_token = ntx_move.login_move(self.movesa['username'], self.movesa['password'], move_ip)
            if not token_res:
                self.lg.write_move_log(loginfo='Failed to log on MOVE Application.', taskid=self.task['id'], logseverity='error')
                self.logger.info('Failed to log on the MOVE.')
                raise Exception('Failed to log on the MOVE.')
            self.logger.info('Successed to log on MOVE Application.')
            self.lg.write_move_log(loginfo='Successed to log on MOVE Application.', taskid=self.task['id'], logseverity='succes')
            logging.info("Successed to log on move....")
            logging.info(f"{move_token}")
            self.logger.info('Trying to check the providers...')
            self.lg.write_move_log(loginfo='Trying to check the providers...', taskid=self.task['id'], logseverity='info')
            res, mes, provider_data = ntx_move.list_providers(move_token, move_ip)
            if provider_data["Entities"]:
                for provider in provider_data["Entities"]:
                    if provider["Spec"]["Type"] == "VMWARE_ESXI_VCENTER":
                        provider_list.append({"ESXI": {"IPorFQDN": provider["Spec"]["ESXAccessInfo"]["IPorFQDN"].lower(), "UUID": provider["MetaData"]["UUID"]}})
                    else:
                        provider_list.append({"AOS": {"IPorFQDN": provider["Spec"]["AOSAccessInfo"]["IPorFQDN"].lower(), "UUID": provider["MetaData"]["UUID"]}})
            if_aospro_existence = [i[key]['IPorFQDN'] for i in provider_list for key in i.keys() if key == "AOS"]
            if_esxipro_existence = [i[key]['IPorFQDN'] for i in provider_list for key in i.keys() if key == "ESXI"]
                # existed_provider = [k for k in vcpc if k.lower() in iporfqdn_list]   
            self.logger.info('Successed to get the provider detail.')
            if if_esxipro_existence:
                self.logger.info(f'ESXI provider {self.cluster} exited.Skipped.')
                self.lg.write_move_log(loginfo=f'ESXI provider {self.cluster} exited. Skipped.', taskid=self.task['id'], logseverity='info')
                esxipro_uuid = [i[key]['UUID'] for i in provider_list for key in i.keys() if key == "ESXI"][0]
            else:
                self.logger.info('ESXI provider not exited. Creating...')   
                self.lg.write_move_log(loginfo='ESXI provider not exited. Creating...', taskid=self.task['id'], logseverity='info') 
                logging.info("creating the ESXI provider")
                sli_provider_res, sli_provider, sli_provider_data = ntx_move.create_provider(move_token, provider_type="ESXI", move_ip=move_ip, vc=self.vc)
                if not sli_provider_res:
                    self.logger.info(f'Failed to create the provider for the SLI, reason: {sli_provider}')
                    self.lg.write_move_log(loginfo='Failed to create the provider for the SLI', taskid=self.task['id'], logseverity='error') 
                    raise Exception('Failed to create the provider for the SLI.')
                self.logger.info('Successed to create the provider for the SLI')
                esxipro_uuid = sli_provider_data["MetaData"]["UUID"]
            self.logger.info('Checking the VDDK...')
            self.lg.write_move_log(loginfo='Checking the VDDK...', taskid=self.task['id'], logseverity='info')
            get_vddk_status, mes = ntx_move.get_vddk_status(move_token, move_ip)
            if not get_vddk_status:
                self.logger.info(f'{mes}')
                self.lg.write_move_log(loginfo=f'{mes}', taskid=self.task['id'], logseverity='info')
                self.logger.info('Uploading the VDDK...')
                self.lg.write_move_log(loginfo='Uploading the VDDK...', taskid=self.task['id'], logseverity='info')
                ntx_move.upload_vddk(move_token, move_ip)
                self.logger.info('Successfully uploaded VDDK.')
                self.lg.write_move_log(loginfo='Successfully uploaded VDDK.', taskid=self.task['id'], logseverity='info')
            else:
                self.logger.info('VDDK is already uploaded.')
                self.lg.write_move_log(loginfo='VDDK is already uploaded.', taskid=self.task['id'], logseverity='info')
            
            if if_aospro_existence and (self.pe.lower() in if_aospro_existence):
                self.logger.info(f'AOS provider {self.pe} exited.Skipped.')
                self.lg.write_move_log(loginfo=f'AOS provider {self.pe} exited.', taskid=self.task['id'], logseverity='info')
                aospro_uuid = [i[key]['UUID'] for i in provider_list for key in i.keys() if key == "AOS" and i[key]['IPorFQDN'] == self.pe.lower()][0]
                self.logger.info('Validating the providers...')
                self.lg.write_move_log(loginfo='Validating the providers...', taskid=self.task['id'], logseverity='info') 
                val_res, val_mes, val_data = ntx_move.validate_provider(move_token, move_ip, aospro_uuid)
                if not val_res and val_data.status_code == 404:
                    self.logger.info(f'{val_mes}.')
                    self.lg.write_move_log(loginfo=f'{val_mes}.', taskid=self.task['id'], logseverity='info')
                    self.logger.info('Trying to update the admin pw in AOS provider.')
                    self.lg.write_move_log(loginfo='Trying to update the admin pw in AOS provider.', taskid=self.task['id'], logseverity='info')
                    res, mes = ntx_move.update_provider(move_token, move_ip, aospro_uuid)
                    if not res:
                        self.lg.write_move_log(loginfo='Failed to update the admin pw in AOS provider,Try to fixed manually.', taskid=self.task['id'], logseverity='info')
                        raise Exception('Failed to update the admin pw in AOS provider.')
                    self.lg.write_move_log(loginfo='Succeed to update the admin pw in AOS provider.', taskid=self.task['id'], logseverity='info')
                self.logger.info('The AOS providers Validated.')
                self.lg.write_move_log(loginfo='The AOS providers Validated.', taskid=self.task['id'], logseverity='info') 
                self.logger.info(f'Trying to fetch AOS provider {self.pe} ...')
                self.lg.write_move_log(loginfo=f'Trying to fetch AOS provider {self.pe} ...', taskid=self.task['id'], logseverity='info')
                res, mes, aos_vm_list = ntx_move.fetch_provider_vminfos(move_token, aospro_uuid, move_ip)
                if not res:
                    self.logger.info('Failed to fetch the target VMs infos')
                    self.lg.write_move_log(loginfo='Failed to fetch the target VMs infos', taskid=self.task['id'], logseverity='error') 
                    raise Exception('Failed to fetch the target VMs infos.')
                self.logger.info(f'Successed to fetch AOS provider {self.pe}: {aos_vm_list} .')
                self.lg.write_move_log(loginfo=f'Successed to fetch AOS provider {self.pe}.', taskid=self.task['id'], logseverity='success')
            else: 
                self.logger.info(f'AOS provider {self.pe} not exited. Creating...')
                self.lg.write_move_log(loginfo=f'AOS provider {self.pe} not exited. Creating...', taskid=self.task['id'], logseverity='info') 
                logging.info("creating the AOS provider")  
                iporfqdn_list = [self.cluster+"-NXC000.IKEA.COM", self.cluster+"-NXC001.IKEA.COM"]
                for iporfqdn in iporfqdn_list:
                    ntx_move = Ntx_Move(pe=iporfqdn, sli_sa=self.whsa, logger=self.logger)
                    ntx_provider_res, ntx_provider, ntx_provider_data = ntx_move.create_provider(move_token, provider_type="AOS", move_ip=move_ip)
                    if not ntx_provider_res:
                        self.logger.info(f'Failed to create the provider {iporfqdn}, reason: {ntx_provider}')
                        self.lg.write_move_log(loginfo=f'Failed to create the provider {iporfqdn}', taskid=self.task['id'], logseverity='error') 
                        raise Exception(f'Failed to create the provider for the NTX: {iporfqdn}.')
                    self.logger.info(f'Successed to create the provider for the NTX: {iporfqdn}')
                    self.lg.write_move_log(loginfo=f'Successed to create the provider for the NTX: {iporfqdn}', taskid=self.task['id'], logseverity='success') 
                    aospro_uuid = ntx_provider_data["MetaData"]["UUID"]
            self.logger.info('Sleep 30s for the data generating...')
            self.lg.write_move_log(loginfo='Sleep for 30 sec to data generating...', taskid=self.task['id'], logseverity='info')  
            time.sleep(30)
            
            # self.lg.write_move_log(loginfo=f'Sleep for 30 sec to validate the providers...', taskid=self.task['id'], logseverity='info') 
            # self.logger.info('Fetching the Source VMs infos..')
            # self.lg.write_move_log(loginfo='Fetching the Source VMs infos...', taskid=self.task['id'], logseverity='info') 
            # res, mes, source_vm_list = ntx_move.fetch_provider_vminfos(move_token, esxipro_uuid, move_ip)
            # if not res:
            #     self.logger.info('Failed to fetch the Source VMs infos')
            #     self.lg.write_move_log(loginfo='Failed to fetch the Source VMs infos', taskid=self.task['id'], logseverity='error') 
            #     raise Exception('Failed to fetch the Source VMs infos')
            # vmlist = [vm_name.split(".")[0].lower() for vm_name in self.vm_list]
            # self.logger.info('Successed to fetch the Source VMs infos')
            # vmuuid_list = []
            # vm_message = []
            # for vm_name in vmlist:
            #     for vm_detail in source_vm_list["Entities"]:
            #         if vm_detail['VMName'].split(".")[0].lower() == vm_name:
            #             vmuuid_list.append({'VMUuid': vm_detail['VMUuid'], 'Network_Name': vm_detail['Networks'][0]['Name']})
            #             vm_message.append({'VM': vm_detail['VMName'], 'message': vm_detail['WarningMessages']})
            # # vmuuid_list = [[{'VMUuid':vm_detail['VMUuid'],'Network_Name':vm_detail['Networks'][0]['Name']} for vm_detail in source_vm_list["Entities"] if vm_detail['VMName'].lower()==vm_name] for vm_name in vmlist][0] 
            # if vm_message:
            #     self.logger.info(f'VMs status not avaliable to move, details:{vm_message}')
            #     self.lg.write_move_log(loginfo=f'VMs status not avaliable to move, details:{vm_message}.', taskid=self.task['id'], logseverity='error') 
            #     # raise Exception(f'VMs status not avaliable to move, see detail logs.')
            # self.logger.info(f'Trying to get the network portgroups from {self.cluster}')
            # self.lg.write_move_log(loginfo=f'Trying to get the network portgroups from {self.cluster}.', taskid=self.task['id'], logseverity='info')
            # portgroups = ntx_move.get_sli_networ_portgroups(self.vc, self.cluster)
            # if not portgroups:
            #     self.lg.write_move_log(loginfo=f'Failed to get the network portgroups from {self.cluster}', taskid=self.task['id'], logseverity='error')
            #     self.logger.info(f'Failed to get the network portgroups from {self.cluster}')
            #     raise Exception(f'Failed to get the portgroups from {self.cluster}.')
            # for portgroup in portgroups:
            #     [[b.update({"vlanid": a.spec.vlanId}) for a in portgroup if b["Network_Name"] == a.spec.name] for b in vmuuid_list if "vlanid" not in b.keys()]
            # if not vmuuid_list[0]["vlanid"]:
            #     raise Exception('Failed to fetch the vlanId.')
            # self.logger.info(f'Successed to get the network portgroups from {self.cluster}')
            # self.lg.write_move_log(loginfo=f'Successed to get the network portgroups from {self.cluster}.', taskid=self.task['id'], logseverity='info')
            # for vlan in vmuuid_list:
            #     self.logger.info(f'Trying to check if {vlan["vlanid"]} existing on {self.pe}')
            #     data = _pe.get_network_by_vlan_id(vlan_id=vlan["vlanid"])
            #     if data:
            #         self.logger.info(f'Vlan {vlan["vlanid"]} existed, generating the detail infos...')
            #         self.lg.write_move_log(loginfo=f'Vlan {vlan["vlanid"]} existed, generating the detail infos...', taskid=self.task['id'], logseverity='info')
            #         self.logger.info(f"The network is found, the UUID is {data['uuid']}")
            #         network_uuid = data['uuid']
            #         vlan.update({"target_network_uuid": network_uuid})
            #     else:
            #         self.logger.info(f'Vlan {vlan["vlanid"]} not existed, creating...')
            #         network_pe_name = self.cluster + "-" + str(vlan["vlanid"]) + "-Servers"
            #         self.lg.write_move_log(loginfo=f'Vlan {vlan["vlanid"]} not existed, trying to create with the name {network_pe_name}...', taskid=self.task['id'], logseverity='info')
            #         self.logger.info(f"The network does not exist, try to create with the name {network_pe_name}")
            #         res, data = _pe.add_network(name=network_pe_name, vlan_id=vlan["vlanid"])
            #         if not res:
            #             self.logger.info(f'Failed to create the network {network_pe_name},error:{data}')
            #             raise Exception(f"Failed to create the network {network_pe_name},error:{data}")
            #         self.logger.info(f'Successsed to create the network {network_pe_name}')
            #         network_uuid = data['network_uuid']
            #         vlan.update({"target_network_uuid": network_uuid})
            #     self.logger.info(f"The {vlan['vlanid']} network uuid :{network_uuid}.")
            # self.lg.write_move_log(loginfo='Successed to fetch the target VMs infos totally.', taskid=self.task['id'], logseverity='success')
            # logging.info(f"{vmuuid_list}")
            # logging.info(f"{vmuuid_list[0]['target_network_uuid']}")
            # # if_ntplan_existing,rep=ntx_move.if_ntplan_existing(move_token, move_ip)
            # # if if_ntplan_existing:
            # #     plan_name = self.cluster+"_LX_MOVE"
            # #     plan_uuid.append(rep)
            # # else:
            # plan_name = self.plantype + "_MovePlan"
            # res, mes, data = ntx_move.list_migration_plans(move_token, move_ip)
            # if data["Entities"]:
            #     self.logger.error(f"Failed to list migration plans. Reason: {data}")   
            #     self.lg.write_move_log(loginfo=f'Failed to list migration plans. Reason: {data}', taskid=self.task['id'], logseverity='error')
            #     # plan_uuids_list =plan_uuid_list
            #     plan_uuids_list = [migration_plan["MetaData"]["UUID"] for migration_plan in data["Entities"]]
            #     existed_planlist = [migration_plan["MetaData"]["Name"] for migration_plan in data["Entities"]]
            #     self.logger.info(f'{plan_uuids_list}')
            #     if plan_name in existed_planlist:
            #         self.logger.info(f'Plan {plan_name} has been existed, please verify !')
            #         self.lg.write_move_log(loginfo=f'Plan {plan_name} has been existed, please verify !', taskid=self.task['id'], logseverity='error') 
            #         raise Exception(f'Plan {plan_name} has been existed, please verify !')
            # self.logger.info(f"Trying to create the migration plan {plan_name}...")
            # self.lg.write_move_log(loginfo=f'Trying to create the migration plan {plan_name}...', taskid=self.task['id'], logseverity='info')
            # res, mes, data = ntx_move.create_migration_plan(move_token, vmuuid_list, plan_name, move_ip)
            # if not res:
            #     self.logger.info(f"Failed to create the migration plan {plan_name}.")
            #     self.lg.write_move_log(loginfo=f'Failed to create the migration plan {plan_name}.', taskid=self.task['id'], logseverity='error')
            #     raise Exception(f'Failed to create the migration plan {plan_name}.')
            # logging.info(f"{data['Spec']['UUID']}")
            # migration_plan_uuid = data['Spec']['UUID']
            # plan_uuid.append(migration_plan_uuid)
            # self.logger.info(f"Successed to create the migration plan {plan_name}.")
            # self.lg.write_move_log(loginfo=f'Successed to create the migration plan {plan_name}', taskid=self.task['id'], logseverity='success')
            # self.lg.write_move_log(loginfo='Trying to check the plan readiness...', taskid=self.task['id'], logseverity='info')
            # self.logger.info("Trying to check the plan readiness...")
            # res, mes = ntx_move.check_plan_readiness(move_token, migration_plan_uuid, move_ip)
            # if not res:
            #     self.logger.info("Failed to check the plan readiness...")
            #     self.lg.write_move_log(loginfo='Failed to check the plan readiness', taskid=self.task['id'], logseverity='error')
            #     raise Exception(f'{mes}')
            # logging.info("Migration plan readiness is ok")
            # self.logger.info("Migration plan readiness is ready to start")
            # self.lg.write_move_log(loginfo='Migration plan readiness is ready to start', taskid=self.task['id'], logseverity='info')
            # self.lg.write_move_log(loginfo='Trying to prepare migration plan ...', taskid=self.task['id'], logseverity='info')
            # self.logger.info("Trying to prepare migration plan ...")
            # res, mes = ntx_move.prepare_source_environment(move_token, migration_plan_uuid, vmuuid_list, move_ip, self.gst_username, self.gst_password)
            # if not res:
            #     self.logger.info("Failed to complete the source VMs preparation.")
            #     self.lg.write_move_log(loginfo='Failed to complete the source VMs preparation', taskid=self.task['id'], logseverity='error')
            #     raise Exception('Failed to complete the source VMs preparation.')
            # logging.info("complete the source VMs preparation")
            # self.logger.info("Successed to complete the source VMs preparation.")
            # self.logger.info("Trying to start the migration plan...")
            # self.lg.write_move_log(loginfo='Trying to start the migration plan...', taskid=self.task['id'], logseverity='info')
            # res, mes = ntx_move.start_migration_plan(move_token, migration_plan_uuid, move_ip)
            # if not res:
            #     self.logger.info("Failed to start the migration plan.")
            #     self.lg.write_move_log(loginfo='Failed to start the migration plan.', taskid=self.task['id'], logseverity='error')
            #     raise Exception('Failed to complete the source VMs preparation.')
            # logging.info("migration plan is started")
            # self.logger.info("Successed to start the migration plan.")
            # self.lg.write_move_log(loginfo='Successed to start the migration plan', taskid=self.task['id'], logseverity='success')
            # logging.info(f"{plan_uuid}")
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            task.stagepreparation = "Done"
            task.moveip = move_ip
            # if task.planuuid:
            #     task.planuuid = task.planuuid+";"+migration_plan_uuid
            # else:
            task.planuuid = ",".join(plan_uuid)
            task.syncstatus = "Done"
            db.session.commit()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.status = "Done"
            db.session.commit()
        except Exception:
            error_info = str(repr(traceback.format_exception(sys.exception())))
            self.logger.error(error_info)
            self.lg.write_move_log(loginfo=f'{error_info}', taskid=self.task['id'], logseverity='error')
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            task.stagepreparation = "Error"
            db.session.commit()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.status = "Error"
            db.session.commit()
        # if not pre_status:
        #     task = ModelSLICluster.query.filter_by(name=self.cluster).first()
        #     task.stagepreparation = "Error"
        #     db.session.commit()
        #     _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
        #     _task.status = "Error"
        #     db.session.commit()
        # else:
        #     task = ModelSLICluster.query.filter_by(name=self.cluster).first()
        #     task.stagepreparation = "Syncing Data"
        #     db.session.commit()
        #     _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
        #     _task.status = "Done"
        #     db.session.commit()

    def create_move_vm(self):
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)  # db is in models.py
        app.app_context().push()  # context definition
        self.task['id']
        self.pe = self.pe.upper()
        self.cluster = self.cluster.upper()
        try:
            if local_log_path := create_file(filepath=SETTING.MOVE_LOG_PATH,filename=f'Move_development_{self.cluster}_{datetime.datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}'):  # noqa
                logging.info(f"Local log file created, path: {local_log_path}.")
                _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
                _task.detaillogpath = local_log_path
                db.session.commit()
            else:
                raise Exception('Failed to create local log file, PM will be ended now.')
            self.logger = setup_common_logger(str(uuid.uuid4()), local_log_path)
            creation_status = True
            self.logger.info(f"Trying to start the MOVE Creation task for {self.cluster}.")
            # slicluster_id = (ModelSLICluster.query.filter_by(name=self.cluster).all())[0].id
            # self.lg = DBLogging(logdir=SETTING.MOVE_LOG_PATH, logtype="NTX_MOVE")
            # lg = DBLogging(logdir=SETTING.MOVE_LOG_PATH, clusterid=slicluster_id, taskid=task_id, logtype="NTX_MOVE")
            self.logger.info("Checking if the MOVE Creation task existence.")
            
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            # if re.match(SETTING.STATUS['INPROGRESS'], task.movedevop, re.I):
            #     self.logger.info("Creation task already started there.")
            #     self.lg.write_move_log(loginfo=f'MOVE Creation task for {self.cluster} already started. Skiped.', taskid=self.task['id'], logseverity='error')            
            #     return
            _task.status = "In Progress"
            db.session.commit()
            self.logger.info(f"No running MOVE Creation task for {self.cluster}.")
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            task.movedevop = "In Progress"
            db.session.commit() 
            self.lg.write_move_log(loginfo=f'Trying to start the MOVE Creation task for {self.cluster}.', taskid=self.task['id'], logseverity='info')
            ntx_cluster = self.pe.lower()
            image_pe_name = self.cluster + "_MOVE_5.4.1"
            move_vm_name = self.cluster + "-MOVE_5.4_A" if "NXC000" in self.pe else self.cluster + "-MOVE_5.4_B"
            network_pe_name = self.cluster + "-104-Servers"
            # pe_uuid = NutanixOperation.get_pe_by_fqdn_from_db(ntx_cluster).uuid
            # new service account transfer to the PrismElement
            _pe = PrismElement(pe=ntx_cluster, logger=self.logger)
            ntx_move = Ntx_Move(pe=ntx_cluster, logger=self.logger)
            self.lg.write_move_log(loginfo=f'Checking if {move_vm_name} existed....', taskid=self.task['id'], logseverity='info')
            self.logger.info(f"Checking the if vm {move_vm_name} existence.")
            vres, vmes = _pe.if_vm_exist(vm=move_vm_name)
            if not vres:
                creation_status = False
                self.logger.info(f"MOVE {move_vm_name} existed.Skiped")
                self.lg.write_move_log(loginfo=f'MOVE {move_vm_name} existed.Skiped.', taskid=self.task['id'], logseverity='error')
                raise Exception(
                    f"Looks like we have a VM named '{move_vm_name}' exists in the cluster '{self.pe}'."
                )
            self.lg.write_move_log(loginfo=f'MOVE {move_vm_name} not existed in the cluster.', taskid=self.task['id'], logseverity='info')
            self.logger.info(f"VM named '{move_vm_name}' not existed in the cluster {self.pe}")
            self.logger.info(f"{vmes}")
            self.lg.write_move_log(loginfo='Fetching the container "SelfServiceContainer" infos...', taskid=self.task['id'], logseverity='info')
            self.logger.info("Checking the if container 'SelfServiceContainer' existence.")
            res, container = _pe.get_container_by_name(name='SelfServiceContainer')  # Get the 'SelfServiceContainer' from PE, the container UUID is required for image upload action
            if not res:
                creation_status = False
                self.lg.write_move_log(loginfo='Failed to fetch the container "SelfServiceContainer" infos.', taskid=self.task['id'], logseverity='error')
                self.logger.error(f"The container 'SelfServiceContainer' does not exist in {_pe.pe}")
                raise Exception(f"The container 'SelfServiceContainer' does not exist in {_pe.pe}") 
            self.lg.write_move_log(loginfo=f"Successsed to get the container uuid :{container['container_uuid']}", taskid=self.task['id'], logseverity='success') 
            self.logger.info(f"Successsed to get the container uuid :{container['container_uuid']}.")
            self.lg.write_move_log(loginfo=f'Checking the if image {image_pe_name} existence...', taskid=self.task['id'], logseverity='info')
            self.logger.info(f"Checking the if image {image_pe_name} existence.")
            ires, data = _pe.get_image_by_name(name=image_pe_name)
            if not ires:  # Exists in PC confirmed
                moveurl_path = os.path.join(application_path, "static", "move.txt")  
                with open(moveurl_path, "r") as m:
                    d = json.load(m)
                moveurl = d["move_url"]
                self.logger.info(f"Image '{image_pe_name}' not existed in the cluster")
                self.lg.write_move_log(loginfo=f'Image {image_pe_name} not existed in the cluster', taskid=self.task['id'], logseverity='info')
                self.lg.write_move_log(loginfo=f'Uploading the Image {image_pe_name}, few mins needed.', taskid=self.task['id'], logseverity='info')
                self.logger.info(f"Uploading the Image '{image_pe_name}'")
                res, data = _pe.add_image(name=image_pe_name, annotation=image_pe_name, \
                                    image_type ="DISK_IMAGE", container_uuid=container['container_uuid'], \
                                    image_url = moveurl)
                                    # image_url ="https://download.nutanix.com/downloads/NutanixMove/5.4.1/move-5.4.1.qcow2?Expires=1745328410&Key-Pair-Id=APKAJTTNCWPEI42QKMSA&Signature=P9o7glVCekcTnE-s0mkQlzOGgM360ZE0XySKiJxYclVSPqCoQnV5Q6J8nwo8tq0H24hiVIovHfw61vipFHPpBuvjdNbRlu2unrkXr0IkQKvHH9tw3NcIDRZA4t9s6s9qoGMJdgf4yw7vDCV32811ToMsAT7je9A2pEbXtG6IzF6Fcz6cY41XkJeNbW-XDvDXHSZ~-EBuGLR45qa68LD3w5A~pEhcwAWih2Q27C5VE1QMUWnW0IyELxsYJ3JS9NY2L44dMW-SZm3xqgJ5AUUQh8IVk0itr29WsP2FLtIRT458~raiRcjLcKYKVJaP2wltLIAp9usk9S6EZP-Ly7jJ0A__")  # Once the PE create a task for image uploading, it returns a task ID for track
                # https://download.nutanix.com/downloads/NutanixMove/5.1.1/move-5.1.1.qcow2?Expires=1715172445504&Key-Pair-Id=APKAJTTNCWPEI42QKMSA&Signature=BZi8av2iGhvvT0WEfSTMTYsiOyo~ChhZ-2JQYrRiOe8EFZJ0ontNdbs4lty-GglAhuuT-fogAUx3B0In0~CC9ZjkOzMeDlHA~~veEG0a9YyOBXS9oUlfi53FmsTI7sIBS1kZpNEdvlpyK~qxB9TAnGgk8JEGrk09W0DrIZIHY1ZdJoxg2ePzg~yf~fl8~ooV2ujCN6xgHONx8OrD27ioS-ZxkFMJHs3RXnQjhrOCDjXaJTSfjdbHAW631Wsc5gTr5-zCYXOMdgBHn~pBHr0Qt5QveYnK4e837CHcBtXxfbwWwoIeitzQBcLS61htvRcJtoUkimXFQh4HdwzdbYJ4dw__
                if not res:
                    creation_status = False
                    self.lg.write_move_log(loginfo=f'Failed to upload the Image {image_pe_name}', taskid=self.task['id'], logseverity='error')
                    raise Exception(f"Failed to upload image {image_pe_name}, reason is {data}")
                self.logger.info(f"It's uploading the image, the task id is {data['taskUuid']}")
                res = _pe.is_task_succeeded(task_uuid=data['taskUuid'], retries=60, retry_interval=60)  # Check if the task completed
                if not res:
                    self.lg.write_move_log(loginfo='Upload image task failed! Please check detailed log', taskid=self.task['id'], logseverity='error')
                    raise Exception("Upload image task failed! Please check detailed log.")
                self.logger.info("Image upload is successfully completed")
                self.lg.write_move_log(loginfo='Image upload is successfully completed', taskid=self.task['id'], logseverity='info')
                # time.sleep(500)
                ires, data = _pe.get_image_by_name(name=image_pe_name)
                # image_uuid = data['uuid']
                vmdisk_uuid = data['vm_disk_id']
            else:
                self.lg.write_move_log(loginfo=f'Image {image_pe_name} existed.', taskid=self.task['id'], logseverity='info')
                vmdisk_uuid = data['vm_disk_id']
                # image_uuid = data['uuid']
            self.logger.info(f"Successsed to get the vmdisk_uuid :{vmdisk_uuid}.")
            # self.logger.info(f"successed to upload the image {image_pe_name} to the PE {self.pe}")
            self.logger.info("Checking the if vlan 104 existence.")
            self.lg.write_move_log(loginfo='Trying to check if Vlan 104 existed...', taskid=self.task['id'], logseverity='info')
            data = _pe.get_network_by_vlan_id(vlan_id=104)
            if data:
                self.lg.write_move_log(loginfo='Vlan 104 existed, generating the detail infos...', taskid=self.task['id'], logseverity='info')
                self.logger.info(f"The network is found, the UUID is {data['uuid']}")
                network_uuid = data['uuid']
            else:
                self.lg.write_move_log(loginfo=f'Vlan 104 not existed, trying to create with the name {network_pe_name}...', taskid=self.task['id'], logseverity='info')
                self.logger.info(f"The network does not exist, try to create with the name {network_pe_name}")
                res, data = _pe.add_network(name=network_pe_name, vlan_id="104")
                if not res:
                    raise Exception(f"Failed to create the network {network_pe_name},error:{data}")
                network_uuid = data['network_uuid']
            self.logger.info(f"Successsed to get the 104 network uuid :{network_uuid}.")
            
            #Create VM
            self.lg.write_move_log(loginfo=f'Trying to creating {move_vm_name}...', taskid=self.task['id'], logseverity='info')
            self.logger.info(f"Creating {move_vm_name}...")
            newmove_taskuuid = ntx_move.new_movevm(move_vm_name, network_uuid, vmdisk_uuid)
            time.sleep(50)
            self.lg.write_move_log(loginfo='Sleep 50 sec to wait the task completed...', taskid=self.task['id'], logseverity='info')
            self.lg.write_move_log(loginfo=f"Checking the status of the task : {newmove_taskuuid['task_uuid']}...", taskid=self.task['id'], logseverity='info')
            self.logger.info(f"Checking the status of the task : {newmove_taskuuid['task_uuid']}.")
            movevm_uuid = ntx_move.get_exiting_task(newmove_taskuuid['task_uuid'])
            if movevm_uuid:
                self.lg.write_move_log(loginfo='task completed.', taskid=self.task['id'], logseverity='info')
            else:
                self.lg.write_move_log(loginfo='Failed to new move VM.', logseverity='error')
                self.logger.info("Failed to new move VM")
                raise Exception('Failed to new move V.')
            self.lg.write_move_log(loginfo='Powering on the VM...', taskid=self.task['id'], logseverity='info')
            self.logger.info(f"Powering on {move_vm_name}...")
            _pe.set_vm_power_state(vm_uuid=movevm_uuid, transition='ON', vm_name=move_vm_name)
            time.sleep(200)
            # logging.info(f"Init the Ntx_Move.")
            self.logger.info("Trying to get the IP for the move...")
            self.lg.write_move_log(loginfo='Trying to get the IP for the move...', taskid=self.task['id'], logseverity='info')
            res, move_ip = ntx_move.get_move_ip(move_name=move_vm_name)
            if not res:
                creation_status = False
                self.lg.write_move_log(loginfo='Failed to get the IP for the move.', logseverity='error')
                self.logger.info("Failed to get the IP for the move.")
                raise Exception('Failed to get the MOVE IP.')
            # self.lg.write_move_log(loginfo=f'Logging on MOVE .....', taskid=self.task['id'], logseverity='info')
            self.logger.info("Logging on move....")
            self.lg.write_move_log(loginfo='Trying to logon the move with default PW...', taskid=self.task['id'], logseverity='info')
            token_res, move_token = ntx_move.login_move(move_username=self.move_defsa['username'], move_password=self.move_defsa['password'], move_ip=move_ip)
            if not token_res:
                creation_status = False
                self.lg.write_move_log(loginfo='Failed to logon the move with default PW.', taskid=self.task['id'], logseverity='error')
                raise Exception('Failed to log on the MOVE with default PW.')
            self.lg.write_move_log(loginfo='Trying to change PW for Move .', taskid=self.task['id'], logseverity='info')
            self.logger.info("Trying to change PW for Move....")
            pwres, pwmes = ntx_move.change_mov_password(move_token, move_ip)
            if not pwres:
                creation_status = False
                self.lg.write_move_log(loginfo=f'Failed to change PW for Move. {move_vm_name} created successfully with default PW', taskid=self.task['id'], logseverity='error')
                self.logger.info(f"{pwmes} : {move_vm_name} created successfully with default PW")
                raise Exception('Failed to change the PW for the MOVE.')
            self.lg.write_move_log(loginfo=f'{move_vm_name} created successfully with new PW .', taskid=self.task['id'], logseverity='info')
            self.logger.info(f"MOVE VM : {move_vm_name} created successfully with ON status")
            self.lg.write_move_log(loginfo=f'MOVE VM : {move_vm_name} creation task completed successfully.', taskid=self.task['id'], logseverity='info')
            moveipcol = "moveipa" if "-MOVE_5.4_A"  in move_vm_name else "moveipb"
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            task.movedevop = "Done"
            setattr(task, moveipcol, move_ip) 
            db.session.commit()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.status = "Done"
            db.session.commit()
            
        except Exception:
            error_info = str(repr(traceback.format_exception(sys.exception())))
            self.logger.error(error_info)
            self.lg.write_move_log(loginfo=f'{error_info}', taskid=self.task['id'], logseverity='error')
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            task.movedevop = "Error"
            db.session.commit()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.status = "Error"
            db.session.commit()
        # finally:
        if not creation_status:
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            task.movedevop = "Error"
            db.session.commit()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.status = "Error"
            db.session.commit()
        else:
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            task.movedevop = "Done"
            db.session.commit()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.status = "Done"
            db.session.commit()
            
    def check_task_status(self):
        _cluster = ModelWHNTXMOVE().get_syncing_task_move()
        logging.info(f"Detected {len(_cluster)} Move data sync tasks, start to spawn sub process.")
        for _task in _cluster:
            self.logger = ''
            p = multiprocessing.Process(target=self.check_single_task, args=[_task])
            p.start()

    def check_single_task(self, sync_task):
        app = Flask(__name__)
        app.config[
            'SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)  # db is in models.py
        app.app_context().push()  # context definition
        try:
            self.move_ip = sync_task.moveip
            self.plan_uuid_list = (sync_task.planuuid).split(",")
            self.cluster = sync_task.cluster
            plan_status = []
            for plan_uuid in self.plan_uuid_list:
                if local_log_path := create_file(filepath=SETTING.MOVE_LOG_PATH,filename=f'Scheduled_task_check_{self.cluster}_{plan_uuid}_{datetime.datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}'):  # noqa
                    logging.info(f"Local log file created, path: {local_log_path}.")
                else:
                    raise Exception('Failed to create local log file, PM will be ended now.')
            # for function/class using, logging into the specific file
                single_planstatus = {}
                vmwd_status = []
                self.logger = setup_common_logger(str(uuid.uuid4()), local_log_path)
                self.logger.info(f"Starting {plan_uuid} for Move data sync task check.")
                self.logger.info("Initializing the MOVE Module...")
                ntx_move = Ntx_Move(pe=self.pe, logger=self.logger)
                self.logger.info("Trying to log on the MOVE ...")
                token_res, move_token = ntx_move.login_move(self.movesa['username'], self.movesa['password'], self.move_ip)
                if not token_res:
                    # pre_status = False
                    self.logger.error("Failed to log on the MOVE.Please check the new password.")
                    raise Exception('Failed to log on the MOVE.')
                self.logger.info(f"Trying to list MOVE worklods on {self.move_ip} ...")
                wdres, wdmes, wddata = ntx_move.list_workloads(move_token, plan_uuid, self.move_ip)
                if not wdres:
                    # pre_status = False
                    self.logger.error(f"{wdmes}")
                    raise Exception('Failed to log on the MOVE.')
                self.logger.info(f"Sucessed to list MOVE worklods on {self.move_ip} .")
                sync_status = [vmswd for vmswd in wddata["Status"]["VMStatus"] if vmswd["ProgressPercentage"] != 100 ]
                # vmwd_status = []
                # if sync_status:    
                single_planstatus['name'] = wddata["Status"]["Name"]
                single_planstatus['status'] = wddata["Status"]["StateString"].split('State')[1]  
                # if wddata["Status"]["Status"]:
                for vmwd in wddata["Status"]["VMStatus"]:
                    # if vmwd["Status"]:
                    vminfos = {}
                    self.logger.info(f'vm_name: {vmwd["Name"]} sync_status:{str(vmwd["ProgressPercentage"])} .')
                    # vmwd_status.append({"vm_name":vmwd["Name"],"sync_status":vmwd["Progress"]})
                    vminfos['vmname'] = vmwd["Name"]
                    vminfos['status'] = vmwd["StateString"].split('State')[1]
                    vminfos['percentage'] = str(vmwd["ProgressPercentage"])
                    vmwd_status.append(vminfos)
                single_planstatus['vm'] = vmwd_status
                plan_status.append(single_planstatus)
            _task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            _task.datasyncstatus = str(plan_status)
            db.session.commit()
            if not sync_status: 
                _task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
                _task.stagepreparation = 'Done'
                _task.syncstatus = 'Done'
                db.session.commit()
        except Exception as e:
            logging.error(str(e))
            error_info = str(repr(traceback.format_exception(sys.exception())))
            self.logger.error(error_info)
            
    def start_move_cutover(self):
        app = Flask(__name__)
        app.config[
            'SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)  # db is in models.py
        app.app_context().push()  # context definition
        move_vm_name = self.cluster + "-MOVE_5.1"
        self.task['id']
        provider_list = []
        self.pe = self.pe.upper()
        self.cluster = self.cluster.upper()
        pbr_vm = self.cluster.lower() + "-lx4000.ikea.com"
        try:
            if local_log_path := create_file(filepath=SETTING.MOVE_LOG_PATH,filename=f'Move_Cutover_{self.cluster}_{datetime.datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}'):  # noqa
                logging.info(f"Local log file created, path: {local_log_path}.")
                _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
                _task.detaillogpath = local_log_path
                db.session.commit()
            else:
                raise Exception('Failed to create local log file, PM will be ended now.')
            self.logger = setup_common_logger(str(uuid.uuid4()), local_log_path)
            self.logger.info("Checking if the task existence...")
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            if re.match(SETTING.STATUS['INPROGRESS'], task.stagecutover, re.I):
                logging.info("Preparation task already started.")
                self.logger.info("Preparation task already there.")
                return
            self.logger.info("Preparation task not existed, will new one.")
            task.stagecutover = "In Progress"
            db.session.commit() 
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.status = "In Progress"
            db.session.commit()
            self.logger.info(f"Trying to start the MOVE Cutover task for {self.cluster}.")
            
            #main
            _pe = PrismElement(pe=self.pe, logger=self.logger)
            ntx_move = Ntx_Move(pe=self.pe, logger=self.logger)
            cluster = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            move_ip = cluster.moveip
            # plan_uuid_list=(cluster.planuuid).split(",")
            # self.logger.info(f'{plan_uuid_list}')
            if cluster.stagecutover == "Done":
                self.lg.write_move_log(loginfo=f'{self.cluster} Cutover status is Done, Skipped.', taskid=self.task['id'], logseverity='error')
                raise Exception('Cutover status is Done, Skipped.')
            res = ntx_move.check_vmlist_connection()
            if not res:
                self.logger.info(f'Failed to log on {self.pe} with "admin".')
                self.lg.write_move_log(loginfo=f'Failed to log on {self.pe} with "admin".', taskid=self.task['id'], logseverity='error')
                raise Exception(f'Failed to log on {self.pe} with "admin".')
            self.logger.info(f'Successed to log on {self.pe} with "admin"...')
            if not test_pinging(move_ip):
                self.logger.info('Move IP changed, generating the ip from PE again...')
                self.lg.write_move_log(loginfo='Move IP changed, generating the ip from PE again...', taskid=self.task['id'], logseverity='info')
                res, move_ip = ntx_move.get_move_ip(move_name=move_vm_name)
                if not res:
                    self.logger.info('Failed to get the MOVE IP.')
                    raise Exception('Failed to get the MOVE IP.')
                self.logger.info('Successed to generate the ip from PE .')
                self.lg.write_move_log(loginfo='FSuccessed to generate the ip from PE.', taskid=self.task['id'], logseverity='success')
                self.logger.info('Updating the ip to DB...')
                task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
                task.stagecutover = move_ip
                db.session.commit()
                move_ip = move_ip
                self.logger.info('Updated new Move IP to DB.')
            self.lg.write_move_log(loginfo='Trying to logon the Move app...', taskid=self.task['id'], logseverity='info')
            token_res, move_token = ntx_move.login_move(self.movesa['username'], self.movesa['password'], move_ip)
            if not token_res:
                self.lg.write_move_log(loginfo='Failed to log on the Move app...', taskid=self.task['id'], logseverity='error')
                self.logger.info('Failed to log on the MOVE.')
                raise Exception('Failed to log on the MOVE.')
            self.logger.info('Successed to log on MOVE Application.')
            self.lg.write_move_log(loginfo='Successed to log on MOVE Application.', taskid=self.task['id'], logseverity='success')
            self.logger.info('Trying to check the providers...')
            self.lg.write_move_log(loginfo='Trying to check the providers...', taskid=self.task['id'], logseverity='info')
            res, mes, provider_data = ntx_move.list_providers(move_token, move_ip)
            if provider_data["Entities"]:
                for provider in provider_data["Entities"]:
                    if provider["Spec"]["Type"] == "VMWARE_ESXI_VCENTER":
                        provider_list.append({"ESXI": {"IPorFQDN": provider["Spec"]["ESXAccessInfo"]["IPorFQDN"].lower(), "UUID": provider["MetaData"]["UUID"]}})
                    else:
                        provider_list.append({"AOS": {"IPorFQDN": provider["Spec"]["AOSAccessInfo"]["IPorFQDN"].lower(), "UUID": provider["MetaData"]["UUID"]}})
            if_aospro_existence = [i[key]['IPorFQDN'] for i in provider_list for key in i.keys() if key == "AOS"]
            if_esxipro_existence = [i[key]['IPorFQDN'] for i in provider_list for key in i.keys() if key == "ESXI"]
                # existed_provider = [k for k in vcpc if k.lower() in iporfqdn_list]   
            self.logger.info('Successed to get the provider detail.')
            if not if_esxipro_existence:
                self.logger.info(f'ESXI provider {self.pe} not exited. please check.')
                self.lg.write_move_log(loginfo=f'ESXI provider {self.pe} not exited. please check.', taskid=self.task['id'], logseverity='error')
                raise Exception(f'ESXI provider {self.pe} not exited. please check.')
            if not if_aospro_existence:
                self.logger.info(f'AOS provider {self.pe} not exited. please check.')
                self.lg.write_move_log(loginfo=f'AOS provider {self.pe} not exited. please check.', taskid=self.task['id'], logseverity='error')
                raise Exception(f'AOS provider {self.pe} not exited. please check.')
            aospro_uuid = [i[key]['UUID'] for i in provider_list for key in i.keys() if key == "AOS"][0]
            val_res, val_mes, val_data = ntx_move.validate_provider(move_token, move_ip, aospro_uuid)
            if not val_res and val_data.status_code == 404:
                self.logger.info(f'{val_mes}.')
                self.lg.write_move_log(loginfo=f'{val_mes}.', taskid=self.task['id'], logseverity='info')
                self.logger.info('Trying to update the admin pw in AOS provider.')
                self.lg.write_move_log(loginfo='Trying to update the admin pw in AOS provider.', taskid=self.task['id'], logseverity='info')
                res, mes = ntx_move.update_provider(move_token, move_ip, aospro_uuid)
                if not res:
                    self.lg.write_move_log(loginfo='Failed to update the admin pw in AOS provider,Try to fixed manually.', taskid=self.task['id'], logseverity='info')
                    raise Exception('Failed to update the admin pw in AOS provider.')
                self.lg.write_move_log(loginfo='Succeed to update the admin pw in AOS provider.', taskid=self.task['id'], logseverity='info')
            self.logger.info(f'Trying to fetch AOS provider {self.pe} ...')
            self.lg.write_move_log(loginfo=f'Trying to fetch AOS provider {self.pe} ...', taskid=self.task['id'], logseverity='info')
            esxipro_uuid = [i[key]['UUID'] for i in provider_list for key in i.keys() if key == "ESXI"][0]
            res, mes, aos_vm_list = ntx_move.fetch_provider_vminfos(move_token, esxipro_uuid, move_ip)
            if not res:
                self.logger.info('Failed to fetch the target VMs infos from SLI')
                self.lg.write_move_log(loginfo='Failed to fetch the target VMs infos from SLI', taskid=self.task['id'], logseverity='error')
            self.logger.info(f'{aos_vm_list}')   
            aospro_uuid = [i[key]['UUID'] for i in provider_list for key in i.keys() if key == "AOS"][0]
            res, mes, aos_vm_list = ntx_move.fetch_provider_vminfos(move_token, aospro_uuid, move_ip)
            if not res:
                self.logger.info('Failed to fetch the target VMs infos')
                self.lg.write_move_log(loginfo='Failed to fetch the target VMs infos', taskid=self.task['id'], logseverity='error')
                self.logger.info(f'Trying to Edit the AOS provider {self.pe}')
                self.lg.write_move_log(loginfo=f'Trying to Edit the AOS provider {self.pe}', taskid=self.task['id'], logseverity='info')
                res, mes = ntx_move.update_provider(move_token, move_ip, aospro_uuid)
                if not res:
                    self.logger.info(f'Failed to Edit the AOS provider {self.pe}')
                    self.lg.write_move_log(loginfo=f'Failed to Edit the AOS provider {self.pe}', taskid=self.task['id'], logseverity='error')
                    raise Exception('Failed to update AOS provider.')
                self.logger.info(f'Trying to fetch AOS provider {self.pe} again ...')
                feres, mes, aos_vm_list = ntx_move.fetch_provider_vminfos(move_token, aospro_uuid, move_ip)
            self.logger.info(f'{feres}:{aos_vm_list}') 
            self.logger.info(f'Successed to fetch AOS provider {self.pe} .')
            self.lg.write_move_log(loginfo=f'Successed to fetch AOS provider {self.pe}.', taskid=self.task['id'], logseverity='success')
            self.logger.info('Sleep 30s for the data generating...')
            self.lg.write_move_log(loginfo='Sleep for 30 sec to validate the providers...', taskid=self.task['id'], logseverity='info')  
            time.sleep(30)
            res, mes, data = ntx_move.list_migration_plans(move_token, move_ip)
            if not res:
                self.logger.error(f"Failed to list migration plans. Reason: {data}")  
                self.lg.write_move_log(loginfo=f'Failed to list migration plans. Reason: {data}', taskid=self.task['id'], logseverity='error')
                # plan_uuids_list =plan_uuid_list
            plan_uuids_list = [migration_plan["MetaData"]["UUID"] for migration_plan in data["Entities"] if migration_plan["MetaData"]["StateString"] == "MigrationPlanStateInProgress"]
            self.logger.info(f'{plan_uuids_list}')
            migrated_vm_list = []
            for plan_uuid in plan_uuids_list:
                self.logger.info(f"Trying to list MOVE worklods on {plan_uuid} ...")
                self.lg.write_move_log(loginfo=f'Trying to list MOVE worklods on {move_ip}...', taskid=self.task['id'], logseverity='info')
                wdres, wdmes, wddata = ntx_move.list_workloads(move_token, plan_uuid, move_ip)
                if not wdres:
                    self.lg.write_move_log(loginfo=f'Failed to list MOVE worklods on {plan_uuid}.', taskid=self.task['id'], logseverity='error')
                    self.logger.error(f'Failed to list MOVE worklods on {plan_uuid}.')
                    raise Exception(f'Failed to list MOVE worklods on {plan_uuid}.')
                self.logger.info(f"{wdmes}")
                self.lg.write_move_log(loginfo=f'Sucessed to list MOVE worklods on {plan_uuid}.', taskid=self.task['id'], logseverity='success')
                if wddata["Status"]["StateString"] != "MigrationPlanStateInProgress":
                    pname = wddata["Status"]["Name"]
                    self.logger.info(f"Migration plan {pname} has been completed, skipped.")
                    self.lg.write_move_log(loginfo=f'Migration plan {pname} has been completed, skipped.', taskid=self.task['id'], logseverity='success')
                    break
                new_migrated_vms = [vmswd["Name"] for vmswd in wddata["Status"]["VMStatus"] if vmswd["StatusString"] != "StatusCompleted"]
                migrated_vm_list = migrated_vm_list + new_migrated_vms
                workload_uuid = [workload["UUID"] for workload in wddata["Status"]["VMStatus"] if workload["StatusString"] != "StatusCompleted"]
                self.lg.write_move_log(loginfo='Trying to check the migration status for all VMs...', taskid=self.task['id'], logseverity='info')
                sync_status = [vmswd for vmswd in wddata["Status"]["VMStatus"] if vmswd["StateString"] != "VMMigrationStateReadyToCutover" and  vmswd["StatusString"] != "StatusCompleted"]
                if sync_status:
                    self.lg.write_move_log(loginfo='Not all VMs are ready for maigrate, please check.', taskid=self.task['id'], logseverity='error')
                    raise Exception('Not all the VMs are ready for maigrate, please check.')
                self.lg.write_move_log(loginfo='All VMs are ready for maigrate. Continue...', taskid=self.task['id'], logseverity='success')
                if pbr_vm in new_migrated_vms:
                    self.lg.write_move_log(loginfo='Trying to stop the service PBR Client...', taskid=self.task['id'], logseverity='info')
                    stop_pbr, mes = ntx_move.stop_pbrservice_status(host=pbr_vm)
                    if not stop_pbr:
                        self.lg.write_move_log(loginfo=f'{mes}', taskid=self.task['id'], logseverity='error')
                        # raise Exception(f'{mes}')
                    else:
                        self.lg.write_move_log(loginfo=f'{mes}', taskid=self.task['id'], logseverity='success')
                self.lg.write_move_log(loginfo='Trying to create the CUTOVER task ...', taskid=self.task['id'], logseverity='info')
                action_res, action_mes = ntx_move.perform_mulworkload_action(move_token, workload_uuid, plan_uuid, 'cutover', move_ip) 
                if not action_res:
                    self.lg.write_move_log(loginfo='Failed to start cutover task, Please check detailed log.', taskid=self.task['id'], logseverity='error')
                    raise Exception("Failed to start cutover task, Please check detailed log.")
            self.logger.info(f"{action_mes}")
            self.lg.write_move_log(loginfo='Cutover task is started successfully.', taskid=self.task['id'], logseverity='success')
            self.lg.write_move_log(loginfo='Cutover task will take about 10 mins to completed, be patient...', taskid=self.task['id'], logseverity='info')
            res = ntx_move.if_cuttask_succeeded(move_token=move_token, move_ip=move_ip, retries=50, retry_interval=60)  # Check if the task completed
            if not res:
                self.lg.write_move_log(loginfo='Cutover task failed! Please check detailed log', taskid=self.task['id'], logseverity='error')
                raise Exception("Cutover task failed! Please check detailed log.")
            self.logger.info("Cutover is successfully completed")
            self.lg.write_move_log(loginfo='Cutover is successfully completed, rest steps will continue', taskid=self.task['id'], logseverity='info')
            self.lg.write_move_log(loginfo='Trying to list the VMs in PE...', taskid=self.task['id'], logseverity='info')
            res, vm_list = _pe.get_vm_list()
            # self.lg.write_move_log(loginfo=f'Getting VM list from {self.cluster}.')
            if not res:
                self.lg.write_move_log(loginfo='Failed to list the VMs in PE.', taskid=self.task['id'], logseverity='error')
                raise Exception('Failed to get the VM list.')
            self.lg.write_move_log(loginfo='Successed to list the VMs in PE.', taskid=self.task['id'], logseverity='success')
            offvm_list = [vm for vm in vm_list if (not vm["is_controller_vm"] and vm['state'] != 'on')]
            if offvm_list:
                failedon_vmlist = [vm for vm in migrated_vm_list if vm in offvm_list]
                if failedon_vmlist:
                    self.lg.write_move_log(loginfo=f'VM : {failedon_vmlist[0]} Failed to power on. please check, task continue...', taskid=self.task['id'], logseverity='info')
                else:
                    self.lg.write_move_log(loginfo='All migrated VMs in PE seem ok. Continue...', taskid=self.task['id'], logseverity='success')
            else:
                self.lg.write_move_log(loginfo='All migrated VMs in PE seem ok. Continue...', taskid=self.task['id'], logseverity='success')
            if pbr_vm in migrated_vm_list:
                self.lg.write_move_log(loginfo='Trying to start the service PBR Client...', taskid=self.task['id'], logseverity='info')
                time.sleep(400)
                ntx_move.start_pbrservice_status(host=pbr_vm)
                # if not start_pbr:
                    # self.lg.write_move_log(loginfo=f'{mes}', taskid=self.task['id'], logseverity='error')
                    # raise Exception(f'{mes}')
            self.lg.write_move_log(loginfo='Trying to rename old VMs on SLI...', taskid=self.task['id'], logseverity='info')
            reres, remes = ntx_move.rename_slivms(vc=self.vc, cluster_name=self.cluster, vm_list=migrated_vm_list)
            if not reres:
                self.lg.write_move_log(loginfo=f'Error : {remes} ', taskid=self.task['id'], logseverity='info')
                raise Exception('Failed to rename VMs.')
            self.lg.write_move_log(loginfo=f' {remes} ', taskid=self.task['id'], logseverity='info')
            self.logger.info("Adding VM to PD...")
            self.lg.write_move_log(loginfo='Trying to add VMs to PD..', taskid=self.task['id'], logseverity='info')
            allvm_list = [vm for vm in vm_list if (not vm["is_controller_vm"])]
            allvm_namelist = [vm['name'] for vm in allvm_list]
            failed_migrated_vms = [vm for vm in migrated_vm_list if vm not in allvm_namelist]
            if failed_migrated_vms:
                self.lg.write_move_log(loginfo=f'Error : {failed_migrated_vms[0]} not exist on NTX, please check.', taskid=self.task['id'], logseverity='error')
            migratedvm_details = [vm for vm in allvm_list if (vm['name'] in migrated_vm_list)]
            for migratedvm_detail in migratedvm_details:
                ntx_move._add_vm_to_ntx_pd_process(migratedvm_detail['uuid'])
            self.lg.write_move_log(loginfo='Successed to add VMs to PD.', taskid=self.task['id'], logseverity='success')
            self.logger.info("Trying to update ACP...")
            self.lg.write_move_log(loginfo='Trying to update ACP...', taskid=self.task['id'], logseverity='info')
            ntx_move.update_acp()
            self.logger.info("Succeed to update ACP...")
            self.lg.write_move_log(loginfo='Succeed to update ACP.', taskid=self.task['id'], logseverity='success')
            self.lg.write_move_log(loginfo='Cutover for {self.cluster} completed successfully.', taskid=self.task['id'], logseverity='success')
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            task.stagecutover = "Done"
            db.session.commit()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.status = "Done"
            db.session.commit()
            
        except Exception:
            error_info = str(repr(traceback.format_exception(sys.exception())))
            self.logger.error(error_info)
            self.lg.write_move_log(loginfo=f'{error_info}', taskid=self.task['id'], logseverity='error')
            task = ModelWHNTXMOVE.query.filter_by(cluster=self.cluster).first()
            task.stagecutover = "Error"
            db.session.commit()
            _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
            _task.status = "Error"
            db.session.commit()
        # finally:
        # if not cutover_status :
        #     task = ModelSLICluster.query.filter_by(name=self.cluster).first()
        #     task.stagecutover = "Error"
        #     db.session.commit()
        #     _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
        #     _task.status = "Error"
        #     db.session.commit()
        # else:
        #     task = ModelSLICluster.query.filter_by(name=self.cluster).first()
        #     task.stagecutover = "Done"
        #     db.session.commit()
        #     _task = ModelWHMOVETask.query.filter_by(id=self.task['id']).first()
        #     _task.status = "Done"
        #     db.session.commit()

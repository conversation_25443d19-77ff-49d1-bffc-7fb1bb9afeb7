import re
from flask import request
from marshmallow import Schema, fields, validates_schema, EXCLUDE, ValidationError, validate

from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.cluster.cluster_specs import ClusterSpec, ClusterSpecWh
from business.distributedhosting.nutanix.cluster.stage_scope import stage_scope
from business.distributedhosting.nutanix.task_status import TaskStatus, NewClusterTaskStatus
from models.cluster_models import ModelClusterTask, ModelWhClusterTask


class CreateClusterSchema(Schema):

    class Meta:
        unknown = EXCLUDE

    stage = fields.Int(required=True)

    # stage 2
    pc_fqdn = fields.Str()
    ahv_subnet = fields.Str()
    benchmark_id = fields.Int()
    pe_name = fields.Str()  # TODO: validate=
    # warehouse
    pe_name_a = fields.Str()
    pe_name_b = fields.Str()

    # stage 3
    selected_oob = fields.List(fields.Str())

    # for resume
    resume = fields.Boolean(default=False)
    task_id = fields.Int()

    # warehouse specific
    metro_task_id = fields.Int()
    room_type = fields.Str(validate=validate.OneOf(["A", "B"]))

    @validates_schema
    def validate_stage_specific_args(self, data, **kwargs):     # pylint: disable=unused-argument
        self.facility_type = request.view_args.get(ClusterSpec.FACILITY_TYPE)
        self.validate_initialize_from_right_stage(data)
        self.validate_start_stage_in_scope(data)
        if data.get(ClusterSpec.TASK_ID) and data.get(ClusterSpec.RESUME):
            self.validate_resume_task_existence(data)
        match data[ClusterSpec.STAGE]:
            case 2:
                self.validate_stage_2(data)
            case 3:
                self.validate_stage_3(data)

    def validate_initialize_from_right_stage(self, data):
        if self.facility_type == FacilityType.RETAIL:
            initialize_from = [2, 3]
        elif self.facility_type == FacilityType.WAREHOUSE:
            initialize_from = [2, 3]
        if not data.get(ClusterSpec.RESUME) and data[ClusterSpec.STAGE] not in initialize_from:
            raise ValidationError(f"For {self.facility_type} clusters, stage can only initialize from {initialize_from}.")

    def validate_start_stage_in_scope(self, data):
        start_stage = data.get(ClusterSpec.STAGE)
        if start_stage not in stage_scope.keys():
            raise ValidationError(f"Stage {start_stage} is not in scope! Valid scope: {stage_scope}")

    def validate_resume_task_existence(self, data):
        task_id = data.get(ClusterSpec.TASK_ID)
        if self.facility_type == FacilityType.RETAIL:
            task = ModelClusterTask.query.filter_by(id=task_id).first()
        elif self.facility_type == FacilityType.WAREHOUSE:
            task = ModelWhClusterTask.query.filter_by(id=task_id).first()
        if not task:
            raise ValidationError(f"Can't find task_id {task_id} to resume!")
        if task.status in [TaskStatus.DONE, TaskStatus.CLEANUP_DONE]:
            raise ValidationError(f"Current task is in '{task.status}' status, unable to resume!")

    def validate_stage_2(self, data):
        required_params = [ClusterSpec.PC_FQDN, ClusterSpec.PE_NAME]
        if self.facility_type == FacilityType.RETAIL:
            required_params += [ClusterSpec.AHV_SUBNET, ClusterSpec.BENCHMARK_ID]
        elif self.facility_type == FacilityType.WAREHOUSE:
            required_params += [ClusterSpecWh.PE_NAME_A, ClusterSpecWh.PE_NAME_B, ClusterSpec.AHV_SUBNET]
        for p in required_params:
            if not data.get(p):
                raise ValidationError(f"In stage 2, following parameters are required: {required_params}")

    def validate_stage_3(self, data):
        required_params = [ClusterSpec.SELECTED_OOB]
        for p in required_params:
            if not data.get(p):
                raise ValidationError(f"In stage 3, following parameters are required: {required_params}")
        ip_pattern = r"((25[0-5]|(2[0-4]|1\d|[1-9]|)\d)\.?\b){4}"
        for ip in data[ClusterSpec.SELECTED_OOB]:
            if not re.search(ip_pattern, ip):
                raise ValidationError(f"{ClusterSpec.SELECTED_OOB}: Please provide valid IP!")
        if self.facility_type == FacilityType.WAREHOUSE:
            self.validate_stage3_wh(data)

    def validate_stage3_wh(self, data):
        """
        For Wiab in stage 3, two sub-tasks will be triggered simultaneously,
        and should provide metro task id and room type
        """
        # TODO
        metro_task_id = data.get(ClusterSpecWh.METRO_TASK_ID)
        room_type = data.get(ClusterSpecWh.ROOM_TYPE)
        # Validates metro task id provided in payload
        if not metro_task_id:
            raise ValidationError(f"{ClusterSpecWh.METRO_TASK_ID} is required when triggering a sub-task!")
        # Validates room type provided in payload
        if not room_type:
            raise ValidationError(f"{ClusterSpecWh.ROOM_TYPE} is required when triggering a sub-task!")
        # Validates metro task exists in DB
        metro_task = ModelWhClusterTask.query.filter_by(id=metro_task_id).first()
        if not metro_task:
            raise ValidationError(f"{ClusterSpecWh.METRO_TASK_ID} '{metro_task_id}' doesn't exist!")
        # Validates metro task's status is in metro_task_valid_statuses
        metro_task_valid_statuses = [NewClusterTaskStatus.WAITING_USER_INPUT, NewClusterTaskStatus.SUB_TASKS_TRIGGERED, NewClusterTaskStatus.SUB_TASKS_ERROR]
        if metro_task.status not in metro_task_valid_statuses:
            raise ValidationError(
                f"{ClusterSpecWh.METRO_TASK_ID} '{metro_task_id}' should be in statuses {metro_task_valid_statuses}, "
                f"current status: '{metro_task.status}'")
        # if stage=3 and resume=True and task_id provided, validates it's not a metro task
        # if data.get(ClusterSpec.RESUME) and data.get(ClusterSpec.TASK_ID):
        #     task = ModelWhClusterTask.query.filter_by(id=data.get(ClusterSpec.TASK_ID)).first()
        #     if not task.is_metro_task:
        #         raise ValidationError("Can't resume a non-metro task in stage 3!")


class DeleteClusterSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    task_id = fields.Int(required=True)

    @validates_schema
    def validate(self, data, **kwargs):          # pylint: disable=unused-argument
        self.validate_task_exists_and_status(data)

    def validate_task_exists_and_status(self, data):
        task_id = data.get(ClusterSpec.TASK_ID)
        facility_type = request.view_args.get(ClusterSpec.FACILITY_TYPE)
        if facility_type == FacilityType.RETAIL:
            task = ModelClusterTask.query.filter_by(id=task_id).first()
        elif facility_type == FacilityType.WAREHOUSE:
            task = ModelWhClusterTask.query.filter_by(id=task_id).first()
        if not task:
            raise ValidationError(f"Can't find task_id {task_id} for '{facility_type}' to cleanup!")
        if task.status in [TaskStatus.DONE, TaskStatus.CLEANUP_DONE]:
            raise ValidationError(
                f"Current task is in '{task.status}' status, unable to trigger CLEAN_UP action!")

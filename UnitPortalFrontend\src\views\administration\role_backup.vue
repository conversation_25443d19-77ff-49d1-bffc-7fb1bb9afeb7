<template>
    <div class="app-container">
      <div class="filter-container">
        <el-row :gutter="5" >
          <el-col :span="3" >
            <el-button class="filter-item"  type="primary" @click="handle_create">
                Create a new role
            </el-button>
          </el-col>
          <el-col :span="4" :offset="17" >
            <el-button style='float:right' class="filter-item"  type="danger" @click="handle_delete">
              Delete
            </el-button>
          </el-col>

      </el-row>
      </div>

      <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="rolelist"
        border
        fit
        highlight-current-row
        style="width: 100%;"
        @sort-change="sortChange"
        @row-click="handle_row_click"
        class='role-table'
        ref='roletable'
      >
  
        <el-table-column label="ID" min-width="10%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Name" min-width="16%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Dashboard " align="center" min-width="5%">
          <template slot-scope="{row}">
            <span>{{ row.role_dashboard }}</span>
          </template>
        </el-table-column>
        <el-table-column label="PC Version" min-width="10%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.pc_version }}</span>
          </template>
        </el-table-column>
        <el-table-column label="NCC Version" class-name="status-col" min-width="6%" align="center" >
          <template slot-scope="{row}">
            <span>
              {{ row.ncc_version }}</span>
          </template>
        </el-table-column>
        <el-table-column label="LCM Version" class-name="status-col" min-width="5%" align="center" >
          <template slot-scope="{row}">
            <span>
              {{ row.lcm_version }}
            </span>
              
          </template>
        </el-table-column>
        <el-table-column label="IP" min-width="10%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.ip }}</span>
          </template>
        </el-table-column>
        <el-table-column  label="Tier" min-width="6%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.tier}}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="get_prism_list" />
  
      <el-dialog :title="'Create a new role'" :visible.sync="dialogFormVisible" top='2%' width="80%">
        <el-form ref="dataForm" :rules="rules" :model="addprismform" label-position="left" label-width="90px" style="width: 90%; margin-left:50px;">
          <el-form-item label="Name" prop="name" >
            <el-input  v-model=addprismform.fqdn style="width:100%;border-bottom: 1px solid black;">
  
            </el-input>
          </el-form-item>
          <el-form-item label="Description" prop="description" >
            <el-input  v-model=addprismform.fqdn style="width:100%;border-bottom: 1px solid black;">
  
            </el-input>
          </el-form-item>
          <el-form-item label="Select privileges"  label-width="190px" >
          </el-form-item>
          <div class="role_div">
            <div class="role_panel">
              <div class="role_panel_header">
                <el-button  class="filter-item role_panel_header_button"  @click="expand_all">
                  Expand All
                </el-button>
                <el-button class="filter-item role_panel_header_button" @click="collapse_all">
                  Collapse All
                </el-button>
                <el-button class="filter-item role_panel_header_button" @click="select_all">
                  Select All
                </el-button>
                <el-button class="filter-item role_panel_header_button" @click="unselect_all">
                  Unselect All
                </el-button>
              </div>
              <div class="role_panel_tree" style="margin-top:1%">
                <el-tree 
                  ref="treeRef"
                  :data="data_tree" 
                  node-key="id"
                  :props="defaultProps" 
                  show-checkbox 
                  :default-checked-keys="[1]"
                  highlight-current>
                  <template #default="{ node, data }">
                    <span @click="change_scope_view(node)" class="custom-tree-node">
                      <span >{{ node.label }}</span>
                    </span>
                  </template>
                </el-tree>
              </div>

            </div>

            <div class="role_scope">
                <div class="role_scope_header" >
                  <div v-if="view_scope.pe">
                    <label class="role_scope_header_label">
                    Show :
                    </label>
                    <el-select @change="change_checkbox_view" v-model="scope_select" style="width:15%;margin:4px 0 0 8px">
                    <el-option key="all" label="ALL" value="all" />
                    <el-option key="selected" label="Selected" value="selected" />
                    <el-option key="unselected" label="Unselected" value="unselected" />
                    </el-select>
                    <el-checkbox style="margin-left:20px" v-model="checkAll" @change="check_all">Check all PE(hidden ones included)</el-checkbox>
                    <div style="float:right;width:40%;margin-right:1%;">
                    <el-input v-model="fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large" style="width:64%;margin-right:3%;margin-top:3px"/>
                    <el-button  class="filter-item"  type="primary"  @click="filter_checkbox">
                      Search
                    </el-button>
                    </div>
                  </div>
                </div>
                <div class="role_scope_body" >
                  <div v-if="view_scope.cluster">
                    cluster select scope
                  </div>
                  <div v-if="view_scope.pe" style="height:100%">
                      <el-tabs tab-position="right"  class="filter-tabs" v-model="pc_tab">
                        <el-tab-pane v-for="pair in checkedpes"  :name="pair.pc" style="height:100%;">

                          <span slot="label"><el-checkbox style="margin-right:6px" :key="pair.pc" :ref="pair.pc" v-model="pair.checkall" :indeterminate="pair.indeterminate"></el-checkbox>{{ pair.pc }}</span>
                          <div style="overflow-y:auto;height:480px;">
                            <el-button style="width:30%" @click = "check_all_visible(true)" >
                              check all
                            </el-button>
                            <el-button style="width:30%" @click = "check_all_visible(false)" >
                              uncheck all
                            </el-button>
                            <el-checkbox-group v-model="pair.check_group" @change="check_group_change">
                            <el-checkbox 
                              style="width:40%" 
                              v-for   = "_pe in (pair.pe)" 
                              :key    = "_pe.name" 
                              :label  = "_pe.name" 
                              v-if    = "_pe.show"
                              @change = "change_checkbox()"
                              disabled
                            >
                            {{_pe.name}}
                            </el-checkbox>
                          </el-checkbox-group>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                  </div>
                  <div v-if="view_scope.all">
                    No need to select any scope, just YES or NO, thank you Sir / Ma'am.
                  </div>
                </div>
                <div style="clear: both;"></div>
            </div>
          </div>  
          <div style="clear:both"></div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="create_role()">
            Confirm
          </el-button>
        </div>
      </el-dialog>

    </div>
  </template>
  
  <script>
  import {GetPrismList, GetPEList, GetPCPECorrespondence} from '@/api/nutanix'
  import {GetRoleList, CreateRole} from '@/api/common'
  import {UpdateNTXPM,AbortNTXPM,DeleteNTXPM,DownloadNTXPMLog} from '@/api/nutanixpm'
  import waves from '@/directive/waves' // waves directive
  import { parseTime } from '@/utils'
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import { validEmail } from '@/utils/validate'
  const prismoptions = [
    { key: 'EU', display_name: 'SSP-EU-NTX.IKEA.COM' },
    { key: 'CN', display_name: 'SSP-CHINA-NTX.IKEA.COM' },
    { key: 'APAC', display_name: 'SSP-APAC-NTX.IKEA.COM' },
    { key: 'NA', display_name: 'SSP-NA-NTX.IKEA.COM' },
    { key: 'RU', display_name: 'SSP-RUSSIA-NTX.IKEA.COM' },
    { key: 'DT', display_name: 'SSP-DT-NTX.IKEADT.COM' },
    { key: 'PPE', display_name: 'SSP-PPE-NTX.IKEA.COM' },
  ]

  // arr to obj, such as { CN : "China", US : "USA" }
  const calendarTypeKeyValue = prismoptions.reduce((acc, cur) => {
    acc[cur.key] = cur.display_name
    return acc
  }, {})
  

  export default {
    name: 'PrismTable',
    components: { Pagination },
    directives: { waves },
    filters: {
      statusFilter(status) {
        let st = status.toLowerCase();
        const statusMap = {
          'not started': 'info',
          'done': 'success',
          'error': 'danger',
          'in progress':'primary'
        }
        return statusMap[st]
      },
      typeFilter(type) {
        return calendarTypeKeyValue[type]
      }
    },
    data() {
      const validateTime =(rule, value, callback)=>{
        if(this.temp.datatimepickerdisabled){
          callback()
        }
        let currentdate = new Date()
        let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
        if (value < utctime){
          callback(new Error('Schedule date must be later then now.'))
        }else{
          let currnettime = utctime.getTime()
          let scheduletime = value.getTime()
          let timediff = scheduletime-currnettime
          if(timediff/1000/60 < 5){
            callback(new Error('Schedule date is too close from now.'))
          }else{
            callback()
          }
        }
        callback()
      }
      return {
        pc_tab:"",
        fuzzy_string:'',
        checkedpes:[],
        checkAll:'',
        check_all_group:{"SSP-APAC-NTX.IKEA.COM":[]},
        addprismform:{
          fqdn: '',
          serviceaccountname:'',
          serviceaccountpassword:'',
          tier:'',
          existingserviceaccount:'',
          newserviceaccount:'1',
          passwordType:'password',
        },
        serviceaccountlist:[],
        tableKey: 0,
        view_scope:{
          all:true,
          pe:false,
          cluster:false,
          template:false,
        },
        tree:{
          current_selected_node_id:'',
          last_selected_node_id:''
        },
        list: null,
        prismlist: null,
        rolelist : null,
        pelist:null,
        pepccorrespondencelist:null,
        total: 0,
        listLoading: true,
        listQuery: {
          page: 1,
          limit: 20,
          cluster: '',
          prism: '',
          status: '',
          sort: '+id'
        },
        prismoptions,
        cities : ['Shanghai', 'Beijing', 'Guangzhou', 'Shenzhen','1','2','3','4'],
        selectedrow:'',
        dialogFormVisible: false,
        dialogPvVisible: false,
        logdata: [],
        rules: {
          fqdn: { required: true, message: 'FQDN is required', trigger: 'change' },
          tier: { required: true, message: 'Tier is required', trigger: 'change' },
        },
        defaultProps : {
          children: 'children',
          label: 'label',
          disabled: 'disabled',
        },
        data_tree : this.$store.getters.role_map,
        scope_select:"all",
        role_map:{
          dashboard:{
            view:true
          },
          nutanix:{
            prism:{
              view:false,
              create:false,
              delete:false
            },
            cluster:{
              view:false
            },
            vm:{
              view:false
            }
          },
          simplivity:{
            vcenter:{
              view:false
            },
            cluster:{
              view:false
            },
            vm:{
              view:false
            }
          },
          pm:{
            nutanix:{
              view:false,
              create:false,
              create_scope:{
                allowed_cluster:[]
              },
              abort:false,
              abort_scope:{
                allowed_cluster:[],
                self_only:false
              },
              delete:false,
              delete_scope:{
                allowed_cluster:[],
                self_only:false
              }
            },
            simplivity:{
              view:false,
              create:false,
              create_scope:{
                allowed_cluster:[],
                self_only:false,
              },
              abort:false,
              abort_scope:{
                allowed_cluster:[],
                self_only:false,
              },
              delete:false,
              delete_scope:{
                allowed_cluster:[],
                self_only:false
              }
            }
          },
          marketplace:{
            template:{
              view:false,
              create:false,
              delete:false
            },
            workload:{
              view:false,
              create:{
                template:false,
                template_scope:{
                  allowed_cluster:[],
                  allowed_naming:""
                },
                custom:false,
                custom_scope:{
                  allowed_cluster:[],
                  allowed_naming:""
                }
              },
              abort:false,
              abort_scope:{
                allowed_cluster:[],
                self_only:false
              },
              delete:false,
              delete_scope:{
                allowed_cluster:[],
                self_only:false
              }
            }
          }
        }
      }
    },
    created() {
      this.get_role_list()
      this.get_prism_list()
      this.get_pe_list() 
      this.init_scope_list()
    },
    methods: {    
      get_pe_list() {
        GetPEList(this.$store.getters.token).then(response => {
          console.log('response pe..',response)
          this.pelist = response.data
        })
      },
      get_role_list(){
        this.listLoading = true
        GetRoleList(this.$store.getters.token).then(response => {
          this.rolelist = response.data
          this.total = response.data.length
          this.listLoading = false
        })
      },
      get_prism_list(){
        this.listLoading = true
        GetPrismList(this.$store.getters.token).then(response => {
          this.prismlist = response.data
          this.total = response.data.length
          this.listLoading = false
        })
      },
      init_scope_list(){
        // generate checkedpes list ,check_all_group 
        GetPCPECorrespondence(this.$store.getters.token).then(response => {
          this.pepccorrespondencelist = response['data']
          if(this.pc_tab == ''){
            this.pc_tab = this.pepccorrespondencelist[0].pc
          }
          for(let pair of this.pepccorrespondencelist){
            this.check_all_group[pair.pc]=[]
            let _temp = {'pc':pair.pc,'pe':[],'checkall':false,'check_group':[],'pe_list':[],'indeterminate':false}
            // this.checkedpes[pair.pc] = []
            for(let _pe of pair.pe){
              _temp['pe'].push(
                {name:_pe,show:true}
              )
              _temp['pe_list'].push(
                _pe
              )
            }
            this.checkedpes.push(_temp)
          }
        })
      },
      handleFilter() {
        this.listQuery.page = 1
      },
      sortChange(data) {
        const { prop, order } = data
        if (prop === 'id') {
          this.sortByID(order)
        }
      },
      sortByID(order) {
        if (order === 'ascending') {
          this.listQuery.sort = '+id'
        } else {
          this.listQuery.sort = '-id'
        }
        this.handleFilter()
      },
      resetTemp() {
        let localtime = new Date()
        let utctime =new Date( localtime.getTime() + 60*1000*localtime.getTimezoneOffset())
        this.temp = {
          id: undefined,
          timestamp: utctime,
          status: 'published',
          type: '',
          prism: '',
          startnow: "1" ,
          pmtype: "1" ,
          newsa: "1",
          datatimepickerdisabled: false
        }
      },
      handle_create() {
        this.resetTemp()
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      handle_delete(){
        console.log( this.selectedrow)
         this.$confirm('You sure??', '?!', {
            confirmButtonText: 'YES',
            cancelButtonText: 'NO',
            type: 'danger'
          }).then(() => {
            this.$message({
              type: 'success',
              message: 'I am not gonna let you do this....'
            });
          }).catch(() => {
            this.$message({
              type: 'info',
              message: 'I am not gonna let you do this....'
            });          
          });
      },
      handle_row_click(row,column,event){
        this.selectedrow = row
      },
      handleAbort(row){
        let payload = {
          data:row,
          token: this.$store.getters.token
        }
        AbortNTXPM(payload).then(() => {
              this.$notify({
                title: 'Success',
                message: 'PM aborted',
                type: 'info',
                duration: 2000
              })
            }
        )
        .catch((error)=>{
          this.$notify({
                title: 'Error',
                message: error.response.data.message,
                type: 'error',
                duration: 2000
            })
        })
      },
      handleDelete(row){
        let payload = {
          data:{id:row.id},
          token: this.$store.getters.token
        }
        DeleteNTXPM(payload).then(()=>{
          this.$notify({
                title: 'Success',
                message: 'PM task was deleted.',
                type: 'success',
                duration: 2000
              })
        })
        .catch((error)=>{
          this.$notify({
                title: 'Failed',
                message: error.response.data.message,
                type: 'error',
                duration: 2000
              })
        })
      },
      createPM() {
        let dt = this.temp.timestamp
        let scheduledate =new Date( dt.getTime() - 60*1000*dt.getTimezoneOffset()) // add timezone math 
        let payload = {
          data:{        
            "prism": this.temp.prism,
            "cluster": this.temp.cluster,
            "startnow": this.temp.datatimepickerdisabled,
            "scheduledate": scheduledate,
            "pmtype": this.temp.pmtype==1?"poweroff":"poweron",
            "description": this.temp.description ?this.temp.description:"webpage entrance"
          },
          token: this.$store.getters.token
        }
        return 0
      },
      handleUpdate(row) {
        this.temp.prism = row.prism
        this.temp.id = row.id
        this.temp.cluster = row.cluster
        this.temp.description =  row.description
        console.log(row.pmtype=='poweroff')
        this.temp.pmtype =  row.pmtype=='poweroff'?'1':'2'
        if (row.startdate ==='startnow'){
          this.temp.timestamp  = new Date()
          this.temp.datatimepickerdisabled = true
          this.temp.startnow = '2'
        }
        else{
          let dt = new Date(row.startdate)
          this.temp.timestamp = new Date(dt.getTime() + 60*1000*dt.getTimezoneOffset())
          this.temp.datatimepickerdisabled = false
          this.temp.startnow = '1'
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      updatePM() {
        let dt = this.temp.timestamp
        let scheduledate =new Date( dt.getTime() - 60*1000*dt.getTimezoneOffset()) // add timezone math 
        let payload = {
          data:{        
            "id": this.temp.id,
            "prism": this.temp.prism,
            "cluster": this.temp.cluster,
            "startdate":this.temp.datatimepickerdisabled?'startnow':scheduledate,
            "pmtype": this.temp.pmtype==1?"poweroff":"poweron",
            "description": this.temp.description ?this.temp.description:"webpage entrance"
          },
          token: this.$store.getters.token
        }
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            UpdateNTXPM(payload)
            .then(() => {
              this.$notify({
                title: 'Success',
                message: ' PM Updated Successfully',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
            })
            .catch((error) => {
              console.log(error)
              console.log(error.response)
              this.dialogFormVisible = false
              this.$notify({
                title: 'Error',
                message: error.response.data.message,
                type: 'error',
                duration: 2000
              })
            })
          }
        })
      },
      downloadLogFile(){
        if (!this.selectedrow.detaillogpath){
          this.$notify({
                title: 'Ooooops',
                message: 'No log yet!',
                type: 'info',
                duration: 2000
              })
        }
        let payload = {
          data:{  id:this.selectedrow.id,
                  filepath:this.selectedrow.detaillogpath},
          token: this.$store.getters.token
        }
        DownloadNTXPMLog(payload)
        .then((response)=>{
          const href = URL.createObjectURL(response.data);
          // create "a" HTML element with href to file & click
          const link = document.createElement('a');
          link.href = href;
          link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
          document.body.appendChild(link);
          link.click();
          // clean up "a" element & remove ObjectURL
          document.body.removeChild(link);
          URL.revokeObjectURL(href);
        })
      },
      handleFetchBriefLog(row) {
        this.selectedrow = row
        this.logdata = row.logs
        this.dialogPvVisible = true
      },
      formatJson(filterVal) {
        return this.list.map(v => filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        }))
      },
      getSortClass: function(key) {
        const sort = this.listQuery.sort
        return sort === `+${key}` ? 'ascending' : 'descending'
      },
      showPwd() {
        if (this.addprismform.passwordType === 'password') {
          this.addprismform.passwordType = ''
        } else {
          this.addprismform.passwordType = 'password'
        }
        this.$nextTick(() => {
          this.$refs.addprismform.serviceaccountpassword.focus()
        })
      },
      create_role(){
        let payload = {
          data:{
            "name":'Test',
            "dashboard":{
              "view":'full'
            }
          },
          token: this.$store.getters.token
        }
        CreateRole(payload)
            .then(() => {
              this.$notify({
                title: 'Success',
                message: 'Role added.',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.get_role_list()
            })
            .catch((error) => {
              this.dialogFormVisible  = false
              this.$notify({
                title: 'Error',
                message: 'Failed to add Prism.',
                type: 'error',
                duration: 2000
              })
            })
      },
      change_scope_view(node){
        this.tree.last_selected_node_id = this.tree.current_selected_node_id
        if(node.isLeaf){
          let scope = node.data.scope
          if (scope == undefined){
            this.change_scope_view_data('all')
          }
          else{
            this.change_scope_view_data(scope)
          }
        }
        else{
          this.change_scope_view_data('all')
        }
        this.tree.current_selected_node_id = node.data.id
      },
      change_scope_view_data(key){
        Object.keys(this.view_scope).forEach(
          (_key)=>{
            this.view_scope[_key] = (_key ==  key)//把相应目录对应的 v-if 改成对应的 true/false, 只显示当前点击的tree node对应的scope view
          }
        )
      },
      expand_all(){
        for(let _node of this.$refs['treeRef'].$children){
          this.change_node_status(_node.node,true)
        }
      },
      collapse_all(){
        for(let _node of this.$refs['treeRef'].$children){
          this.change_node_status(_node.node,false)
        }
      },
      change_node_status(node,status=false){
        node.expanded = status
        for(let _node of node.childNodes){
          this.change_node_status(_node,status)
        }
      },
      select_all(){
        this.$refs.treeRef.setCheckedNodes(
          this.$refs['treeRef'].$children.map(
            (item)=>{
              return item.node.data
            }
          ),false)
      },
      unselect_all(){
        this.$refs.treeRef.setCheckedNodes(
          this.$refs['treeRef'].$children.map(
            (item)=>{
              if(item.node.data.disabled == true){
                return item.node.data
              }
            }
          ),false)
      },
      change_checkbox_view(){
        if(this.scope_select =='all'){
          if(this.view_scope.pe){
            let pc = this.pc_tab
            this.checkedpes.forEach(
              (_map)=>{
                if(_map.pc == pc){
                  _map.pe.forEach(
                    (_pe)=>{
                      _pe.show = true
                    }
                  )
                }
              }
            )
          }
          return 
        }

        let _flag
        if(this.scope_select == 'selected'){
          _flag = true
        }
        else{
          _flag = false
        }
        if(this.view_scope.pe){
            let pc = this.pc_tab
            this.checkedpes.forEach(
              (_map)=>{
                if(_map.pc == pc){
                  _map.pe.forEach(
                    (_pe)=>{
                      _pe.show = ( 
                        _flag == 
                        ( _map.check_group.some(item => item ===_pe.name) )   
                      ) 
                    }
                  )
                }
              }
            )
          }
      },//选择下拉框调整函数
      change_checkbox(){
        if(this.scope_select!='all'){
          this.change_checkbox_view()
        }
        this.change_tab_checkbox_view()

      },//单个checkbox选中之后 处罚的函数
      check_all_visible(flag){
        // check all the checkbox in tab-pane
        if(this.view_scope.pe){
          let pc = this.pc_tab
          this.checkedpes.forEach(
            (_map)=>{
              if(_map.pc == pc){
                _map.pe.forEach(
                  (_pe)=>{
                    if(_pe.show){
                      if(flag){
                        if(!(_map.check_group.some(item=>item==_pe.name))){
                          _map.check_group.push(_pe.name)
                        }
                      }
                      else{
                        _map.check_group = _map.check_group.filter(function(item) {
                              return item != _pe.name;
                          })
                      }
                    }  
                  }
                )
              }
            }
          )
        }
        if(this.scope_select!='all'){
          this.change_checkbox_view()
        }
        this.change_tab_checkbox_view()
      },//（取消）选中所有可见的
      check_all(){
        console.log(this.$refs.treeRef.currentNode)
        this.$refs.treeRef.currentNode.node.indeterminate=true
      },  
      filter_checkbox(){
        console.log(111111)
        let fuzzy_list = this.fuzzy_string.trim().split(/\s+/)
        console.log(fuzzy_list)
        if(fuzzy_list.length == 0){
          this.change_checkbox_view()
          return
        }else if(fuzzy_list.length == 1){
          if(fuzzy_list[0]==''){
            this.change_checkbox_view()
            return
          }
        }
        if(this.view_scope.pe){//oprate the nutanix view.
          this.checkedpes.forEach(
            (_map)=>{
              if(_map.pc == this.pc_tab){// get the right pc
                _map.pe.forEach(
                  (_pe)=>{
                    if(_pe.show){
                      let flag = false
                      fuzzy_list.forEach(
                        (fuzzy)=>{
                          if(_pe.name.toString().toLowerCase().search(fuzzy.toLowerCase()) != -1){
                            flag = true
                            
                          }
                      })
                      if(!flag){
                        _pe.show=false
                      }
                    }  
                  }
                )
              }
            }
          )
        }
      },
      check_group_change(){
        // this.$refs[this.pc_tab].indeterminate=true
      },
      change_tab_checkbox_view(){
        this.checkedpes.forEach(
          (_map)=>{
            if(_map.pc == this.pc_tab){//找到对应的pc
              if(_map.check_group.length==0){ //nothing is checked.
                _map.indeterminate = false
                _map.checkall = false
              }
              else{
                if(_map.check_group.length == _map.pe_list.length){
                  _map.indeterminate = false
                  _map.checkall = true
                }
                else{
                  _map.indeterminate = true
                }
              }
            }
          }
        )
      }
    },
    beforeDestroy(){
      //
    }
  }
  </script>

  <style scoped>
   .role-table span{
       font-size: 17px
    }

    .custom-tree-node {
     flex: 1;
     display: flex;
     align-items: center;
     justify-content: space-between;
     font-size: 18px;
     padding-right: 8px;
    }


  </style>
  <style>
    .el-dialog__wrapper{
      width: 100%;
    }
    .role_div{
      border:1px black solid;
      height:600px; 
      margin-top: -1%;
    }
    .role_panel{
      width:40%;
      border-right:1px black solid;
      float:left;
      height:100%;
      
    }
    .role_panel_header{
      height:8%;
      border-bottom:1px solid black;
      width:98%;
    }
    .role_panel_header_button{
      height:30px;
      line-height:1px;
      margin:1.5% 0px 1% 15px;
      background-color:rgb(245, 240, 240)
    }
    .role_panel_tree{
      overflow-y:auto;
      height:91%;
      margin-top:1%
    }
    .role_scope{
      width:60%;
      float:right;
      height:100%
    }
    .role_scope_header{
      height:8%;
      border-bottom:1px solid black;
      width:98%;
      margin-left:2%
    }
    .role_scope_header_label{
      margin:3px;
    }
    .role_scope_body{
      overflow-y:auto;
      height:91%;
      margin-top:1%
    }
    .filter-tabs > .el-tabs__content {
      padding: 12px;
      color: #6b778c;
      font-size: 32px;
      font-weight: 600;
    }

    </style>
import axios from 'axios'
import { endpoint } from './endpoint'
import request from '@/utils/request'

export function CreateSLCMPlans(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param.token}
  };
  let res =  request.post(`${endpoint}/ntx/automation/lcm/seamless_lcm/plans`, param.data, config)
  return res
}
export function UpdateSLCMPlans(param, planId) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param.token}
  };
  let res =  request.put(`${endpoint}/ntx/automation/lcm/seamless_lcm/plans/${planId}`, param.data, config)
  return res
}
export function CancelSLCMPlan(token, planId) {
  let payload = {
    headers: {'Authorization': 'Bearer ' + token}
  }
  let res =  request.delete(`${endpoint}/ntx/automation/lcm/seamless_lcm/plans/${planId}`, payload)
  return res
}
export function GetSLCMPlans(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/automation/lcm/seamless_lcm/plans`, config)
  return res
}
export function CancelSLCMPEs(param) {
  let payload = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data   : param.data
  }
  let res =  request.delete(`${endpoint}/ntx/automation/lcm/seamless_lcm/planned_pe`, payload)
  return res
}
export function UpdateSLCMPEs(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.put(`${endpoint}/ntx/automation/lcm/seamless_lcm/planned_pe`, param.data, config)
  return res
}
export function GetTargetVersion(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.get(`${endpoint}/ntx/automation/lcm/seamless_lcm/target_version`, param.data, config)
  return res
}
export function UpgradeSPP(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/automation/lcm/spp/upgrade`, param.data, config)
  return res
}

export function DownloadSLCMPlannedPE(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.get(`${endpoint}/ntx/automation/lcm/seamless_lcm/planned_pe?download=True`,config)
  return res
}

export function GetPEWithSPPTask(token, facilityType) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res = request.get(`${endpoint}/ntx/automation/lcm/spp/task/list/${facilityType}`, config);
  return res;
}
export function GetSPPTasks(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/automation/lcm/spp/tasks`,config)
  return res
}

export function GetSPPTaskStatus(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/automation/lcm/spp/task/status`, param.data, config)
  return res
}

export function DownloadLCMUpgradeLog(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token},
    responseType:'blob'
  }
  let res = request.post(`${endpoint}/download`,param.data,config)
  return res
}
export function Get_DSC_Tasks(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/dsctasks`,config)
  return res
}
export function Get_PWRotate_Tasks(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/pwerotate/tasks`,config)
  return res
}
export function Get_CER_Tasks(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/certasks`,config)
  return res
}
export function GetMaintenanceTaskList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  return request.get(`${endpoint}/ntx/automation/auto_maintenance`,config)
}

export function SetMaintenanceLock(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };

//   return new Promise((resolve, reject) => {
//     let data = {
//      success:true
//    }
//    resolve(data);
//  });
  return request.post(`${endpoint}/ntx/automation/auto_maintenance_lock`, param.data, config)
}

export function GetMaintenanceLock(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };

  return request.get(`${endpoint}/ntx/automation/auto_maintenance_lock/${param['pe_id']}`, config)
}

export function RemoveMaintenanceLock(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };

  return request.delete(`${endpoint}/ntx/automation/auto_maintenance_lock/${param['id']}`, config)
}

export function LaunchAutoMaintenance(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };

  return request.post(`${endpoint}/ntx/automation/auto_maintenance`, param.data, config)
}

export function AbortAutoMaintenance(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };

  return request.post(`${endpoint}/ntx/automation/auto_maintenance/abort`, param.data, config)
}

export function GetMaintenanceLog(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  return request.post(`${endpoint}/ntx/automation/auto_maintenance_log`, param.data, config)
}

export function DownloadATMDetailLog(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token},
    responseType:'blob'
  }
  let res = request.post(`${endpoint}/download`,param.data,config)
  return res
}

export function GetMaintenanceHistory(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  return request.post(`${endpoint}/ntx/automation/auto_maintenance_history`, param.data, config)
}

export function GetAOSTaskList(param, facility_type) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.get(`${endpoint}/ntx/automation/lcm/aos/task/list/${facility_type}`,config)
  return res
}
export function GetAOSLCMTasks(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.get(`${endpoint}/ntx/automation/lcm/aos/tasks`,config)
  return res
}

export function UpgradeAOS(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/automation/lcm/aos/upgrade`, param.data, config)
  return res
}
// Nutanix MOve part
export function Get_Tasklist_Move(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/move_tasks`,config)
  return res
}
export function Get_Clusterlist_Move(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/move_clusterlist`,config)
  return res
}

export function Get_SLI_NTVMs(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/sli/vms/nt`,param.data,config)
  return res
}

export function Create_migration_plan(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/move_pre`,param.data,config)
  return res
}

export function New_Move_VM(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/move_creation`,param.data,config)
  return res
}

export function Start_Cutover(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/move_cutover`,param.data,config)
  return res
}

export function DownloadMoveDetailLog(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token},
    responseType:'blob'
  }
  let res = request.post(`${endpoint}/download`,param.data,config)
  return res
}

// Warehouse Nutanix Move part
export function Get_WHClusterlist_Move(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/move_whclusterlist`,config)
  return res
}
export function New_WHMove_VM(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/move_creation_wh`,param.data,config)
  return res
}
export function Get_WH_VMs(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/wh/vms/list`,param.data,config)
  return res
}
export function Create_Migration_Plan_WH(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/wh/ntx/move_pre`,param.data,config)
  return res
}
export function GetWHMoveBriefLog(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  return request.post(`${endpoint}/wh/ntx/move_brief_log`, param.data, config)
}
export function Get_SinglePE_CVMs(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/pe/cvmlist`,param.data,config)
  return res
}

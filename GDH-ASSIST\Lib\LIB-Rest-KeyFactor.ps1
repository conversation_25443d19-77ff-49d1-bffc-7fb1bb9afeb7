function Invoke-KeyFactor-API(){
    param (
        [string]                                               $Fqdn,
        [string]                                               $RequestURI,
        [string] [ValidateSet("GET", "POST", "PUT", "DELETE")] $Method,
        [object]                                               $Headers,
        [object]                                               $Body,
        [int]                                                  $MaxTry = 5,
        [int]                                                  $TimeoutSec = 15
    )
    if ($MaxTry) {
        $Payload = @{
            'Method'     = $Method
            'Headers'    = $Headers
            'TimeoutSec' = $TimeoutSec
        }
        if ($Body) {
            $Payload['Body'] = $Body | ConvertTo-Json
        }
        $Payload['Uri'] = "https://$($Fqdn)/KeyfactorApi/$($RequestURI)"
        try {
            return Invoke-RestMethod @Payload -SkipCertificateCheck:$true
        }
        catch {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling $Fqdn for $RequestURI. Cause: $_ Retry in 5 seconds"
            Start-Sleep 5
            return Invoke-KeyFactor-Api -Fqdn $Fqdn `
                                        -RequestURI $RequestURI `
                                        -Method $Method `
                                        -Auth $Auth `
                                        -Body $Body `
                                        -MaxTry $($MaxTry - 1) `
                                        -TimeoutSec $($TimeoutSec + 5)
        }
    } else {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling $Fqdn for $RequestURI."
        return $null
    }
}
# function Rest-KeyFactor-EnrollCertByCsr(){
#     param (
#         [string]                                              $Fqdn,
#         [string]                                              $Csr,
#         [string]                                              $Template,
#         [string]                                              $CertificateAuthority,
#         [String]                                              $CertificateOwnerGroup,
#         [String]                                              $EmailContact,
#         [switch]                                              $IncludeChain = $false,
#         [string] [Parameter(ParameterSetName = 'Credential')] $Username,
#         [string] [Parameter(ParameterSetName = 'Credential')] $Pword,
#                     [Parameter(ParameterSetName = 'Session')] $Auth
#     )
#     if (!$Auth) {
#         $Auth = Get-Base64Auth -Username $Username -PWord $PWord
#     }
#     $Timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
#     $Body      = @{
#         'CSR'                  = $Csr
#         'IncludeChain'         = [bool] $IncludeChain
#         'CertificateAuthority' = $CertificateAuthority
#         'Timestamp'            = $TimeStamp
#         'Template'             = $Template
#         'Metadata'             = @{
#             'Certificate_Owner_Group' = $CertificateOwnerGroup
#             'Email-Contact1'          = $EmailContact
#         }
#     }
#     return Invoke-KeyFactor-API -Fqdn $Fqdn `
#                                 -Auth $Auth `
#                                 -RequestURI "KeyfactorApi/Enrollment/CSR" `
#                                 -Method POST `
#                                 -Body $Body
# }
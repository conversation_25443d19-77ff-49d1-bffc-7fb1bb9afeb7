from business.distributedhosting.nutanix.task_status import TaskStatus
from business.generic.base_up_exception import BaseUpException


class WorkloadException(BaseUpException):
    def __init__(self, msg, status=TaskStatus.ERROR):
        super().__init__(msg, status)


class SizingFailed(WorkloadException):
    def __init__(self, lacking_storage, lacking_cpu, lacking_memory):
        error_msg = ""
        if lacking_storage:
            error_msg += "Storage is NOT sufficient."
        if lacking_cpu:
            error_msg += " CPU is NOT sufficient."
        if lacking_memory:
            error_msg += " Memory is NOT sufficient."
        error_msg += " For sizing calculating method, please check detailed log."
        super().__init__(error_msg)


class WorkloadExisting(WorkloadException):
    def __init__(self, name, where):
        super().__init__(f"Workload '{name}' already existing in {where}")


class ThorsHammerException(WorkloadException):
    def __init__(self, original_err, msg=""):
        if msg == "":
            msg = "Thor's Hammer API returned error."
        super().__init__(
            f"{msg} Error message from <PERSON>'s <PERSON>: {original_err}"
        )


class ThorsAddProfileFailed(ThorsHammerException):
    def __init__(self, machine_name, profile_name, original_err):
        msg = f"Add profile failed! Machine name: {machine_name}, profile: {profile_name}. "
        super().__init__(original_err, msg)


class ThorsExpControlAgentStatusFailed(ThorsHammerException):
    def __init__(self, original_err):
        msg = "Failed to control agent status."
        super().__init__(original_err, msg)


class ThorsDeleteComputerNotFound(WorkloadException):
    def __init__(self, name):
        super().__init__(f"Can't find computer '{name}' to delete in Thor's hammer!")


class ThorsRegisterMachineFailed(ThorsHammerException):
    def __init__(self, name, original_err):
        msg = f"Register machine {name} in Thor's Hammer failed!"
        super().__init__(original_err, msg)


class ThorsUpdateActivationCodeFailed(ThorsHammerException):
    def __init__(self, machine_name, vm_uuid, original_err):
        msg = f"Update activation code failed! Machine name: {machine_name}, vm uuid: {vm_uuid}. "
        super().__init__(original_err, msg)


class TowerJobLaunchFailed(WorkloadException):
    def __init__(self, tower_err_msg):
        super().__init__(f"Failed to launch tower template. Error: {tower_err_msg}")


class TowerJobFailed(WorkloadException):
    def __init__(self, template_id, job_id, msg=""):
        job_info = f"Template id: {template_id}. Job link: https://tower.ikea.com/#/jobs/workflow/{job_id}/output"
        if not msg:
            msg = "Tower job failed. Please check the job link for details."
        super().__init__(f"{msg} {job_info}", TaskStatus.DONE_WITH_ERROR)
        self.template_id = template_id
        self.job_id = job_id


class TowerJobFailedDb(TowerJobFailed):
    def __init__(self, job_id, msg=""):
        if not msg:
            msg = "VM is created successfully but Tower job failed. Please try again or contact DB team."
        super().__init__("8715", job_id, msg)


class TowerJobFailedPipDbPG14(TowerJobFailedDb):
    def __init__(self, job_id):
        msg = "PIP DB is created successfully but Tower job failed. Will send mail to DB team."
        super().__init__(job_id, msg)


class TowerTemplateNotFound(WorkloadException):
    def __init__(self, template_id):
        super().__init__(f"Ooooppppss, template: {template_id} not found in tower.")


class ImageUploadFailed(WorkloadException):
    def __init__(self, init, in_progress, image_name, original_err):
        if init:
            super().__init__(f"Failed to init image uploading! Image name: {image_name}, error message: {original_err}")
        if in_progress:
            super().__init__(f"Image uploading task failed! Image name: {image_name}, error message: {original_err}")


class ImageUploadOverTime(WorkloadException):
    def __init__(self, image_name, completed_at):
        super().__init__(f"Image upload over time! Image name: {image_name}, Completed at: {completed_at}. We abort, please retry few hours later.")

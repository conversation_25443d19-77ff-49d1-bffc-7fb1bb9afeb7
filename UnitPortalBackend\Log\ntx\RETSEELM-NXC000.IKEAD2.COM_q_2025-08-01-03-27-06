2025-08-01 11:27:11,727 INFO Getting cluster list from ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:27:11,727 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/api/nutanix/v3/clusters/list, method: POST, headers: None
2025-08-01 11:27:11,727 INFO params: None
2025-08-01 11:27:11,727 INFO User: <EMAIL>
2025-08-01 11:27:11,727 INFO payload: {'kind': 'cluster'}
2025-08-01 11:27:11,729 INFO files: None
2025-08-01 11:27:11,729 INFO timeout: None
2025-08-01 11:27:32,784 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000023F1AEA9CD0>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:27:32,794 WARNING Call api failed, going to do the 2 retry...
2025-08-01 11:27:53,814 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000023F1B760CD0>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:27:53,814 WARNING Call api failed, going to do the 3 retry...
2025-08-01 11:28:14,843 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x0000023F1B762C90>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:28:14,843 WARNING Call api failed, going to do the 4 retry...

import ntnx_volumes_py_client
import uuid
import datetime

from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
from business.authentication.authentication import Vault
from business.generic.base_up_exception import VaultGetSecretFailed

from business.generic.commonfunc import setup_common_logger, create_file
from static.SETTINGS import METRO_VG_LOG_PATH


class WiabMetroListVGs():
    def __init__(self, payload):
        self.payload = payload
        self.pe_prefix = payload.get("pe").upper().split('-')[0]
        self.pc_svc_account = self.init_svc_account()
        self.log_path = create_file(filepath=METRO_VG_LOG_PATH, filename=f'LIST_VGS_{payload.get("pe")}_{datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d_%H:%M:%S")}')
        self.logger = setup_common_logger(str(uuid.uuid4()), self.log_path)
        self.volumes_client = None

    def init_svc_account(self):
        def get_central_pe_name():
            prism = ModelWarehousePrismElement.query.filter_by(name=self.payload.get("pe")).first().prism
            central_pe_fqdn = ModelWarehousePrismCentral.query.filter_by(fqdn=prism).first().central_pe_fqdn
            central_pe_name = ModelWarehousePrismElement.query.filter_by(fqdn=central_pe_fqdn).first().name
            return prism, central_pe_name
        prism, central_pe_name = get_central_pe_name()
        vault = Vault(tier=ModelWarehousePrismCentral.query.filter_by(fqdn=prism).first().tier)
        pc_svc_label = f"{central_pe_name.upper()}/Site_Pc_Svc"
        res, data = vault.get_secret(pc_svc_label)
        if not res:
            raise VaultGetSecretFailed(pc_svc_label)
        pc_svc_account = {"username": data["username"], "password": data["secret"]}
        return pc_svc_account

    def init_api_client_config(self, config):
        config.host = ModelWarehousePrismElement.query.filter_by(name=self.payload.get("pe")).first().prism
        config.port = 9440
        config.username = self.pc_svc_account['username']
        config.password = self.pc_svc_account['password']
        config.verify_ssl = True                            # Set to False in Test env
        return config

    def init_ntx_clients(self):
        self.logger.info("Initializing API client(s)...")
        self.volumes_client = ntnx_volumes_py_client.ApiClient(self.init_api_client_config(ntnx_volumes_py_client.Configuration()))

    def list_volume_group(self, filter=None):
        self.logger.info(f"Listing volume groups for site: {filter}")
        volume_groups_api = ntnx_volumes_py_client.VolumeGroupsApi(api_client=self.volumes_client)

        filter = f"startswith(name, '{filter}')"
        response = {}
        api_response = None

        try:
            api_response = volume_groups_api.list_volume_groups(_filter=filter)
        except ntnx_volumes_py_client.rest.ApiException as e:
            if e.status == 401:
                self.logger.error("Unauthorized: Invalid credentials", exc_info=True)
                return {"error:" "Unauthorized: Invalid credentials"}, 401
            if e.status == 404:
                self.logger.error(f"Resource not found: {e.body}", exc_info=True)
                return {"error:" "Requested resource not found"}, 404

            self.logger.error(f'API Exception: {e}', exc_info=True)
            return {"error:" "An error occurred while fetching volume groups"}, 500

        if api_response:
            for k in range(api_response.metadata.total_available_results):
                key = api_response.data[k].name
                val = api_response.data[k].ext_id
                response[key] = val
            self.logger.info(f"Found {api_response.metadata.total_available_results} volume groups: {response}")
        else:
            self.logger.error("Failed to retrieve volume groups.")

        return response

    def run(self):
        self.init_ntx_clients()
        return self.list_volume_group(self.pe_prefix)
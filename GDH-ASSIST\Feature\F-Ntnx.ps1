class RetailPc : System.Management.Automation.IValidateSetValuesGenerator {
    [string[]] GetValidValues() {
        $Global:RetailPc = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name
        return $Global:RetailPc
    }
}
function Confirm-RetailNtnxBios(){
    <#
    .SYNOPSIS
    The function returns a list of Bios version of Nutanix nodes within the cluster which is under the given Prism Central
    
    .DESCRIPTION
    Each items are group by cluster name
    
    .PARAMETER Pc
    The name of Prism Central you want to call, only the PC name is acceptable
    
    .EXAMPLE
    Confirm-RetailNtnxBios -Pc ssp-eu-ntx
    
    .NOTES
    General notes
    #>
    param (
        [Parameter(Mandatory = $true)] [ValidateSet([RetailPc], ErrorMessage="[ERR]Invalid Parameters")] [string] $Pc
    )
    Begin {
        $BMCs = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new() # A thread safe collection that stores result set
    }
    Process {
        [Array] $Prisms = if ($RestCall = Get-NtnxCluster -Prism $Pc) { # Get a list of cluster name under the Prism Central
            $RestCall.Prism
        }
        $Prisms | ForEach-Object -Parallel { # Calling each clusters to get nodes in parallel mode
            Import-Module GDH-ASSIST
            $Prism    = $_
            $DictBMCs = $using:BMCs
            if ($BiosCall = Get-NtnxNodeBios -Prism $Prism) { # Get a set of nodes of current cluster
                $BiosCall | ForEach-Object { # Iterat each nodes and assemble the result object
                    $BMC = [PSCustomObject]@{
                        'Cluster' = $Prism
                        'Node'    = $_.Name
                        'Model'   = $_.Model
                        'BiosVer' = $_.BiosVer
                    }
                    $DictBMCs.Add($BMC) # Add the object into the result set
                }
            }
        } -ThrottleLimit 200 # The limitation of running threads simultaneously
    }
    End {
        return $BMCs
    }
}
function Confirm-NtnxReplicationChain(){
    <#
    .SYNOPSIS
    The function returns a list of replication chain of Nutanix clusters within the given Prism Central.
    
    .DESCRIPTION
    Each items are group by cluster name
    
    .PARAMETER Pc
    Only the Prism Central name is acceptable.
    Only accept retail PC name.
    
    .EXAMPLE
    Confirm-NtnxReplicationChain -Pc ssp-eu-ntx
    
    .NOTES
    General notes
    #>
    param (
        [Parameter(Mandatory = $true)] [ValidateSet([RetailPc], ErrorMessage="[ERR]Invalid Parameters")] [string] $Pc
    )
    $Results   = @{}
    $Validated = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Skipped   = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    [Array] $Prisms = if ($RestCall = Get-NtnxClusterList -Prism $Pc) { # Get a list of cluster name under the Prism Central
        $RestCall.Prism
    }
    $Prisms | ForEach-Object -Parallel { # Calling each clusters to get its replication chain in parallel mode
        $Prism         = $_
        $DictValidated = $using:Validated
        $DictSkipped   = $using:Skipped
        if ($ChainCall = Get-NtnxReplicationChain -Prism $Prism) {
            $Chain = [PSCustomObject]@{
                'Prism'       = $ChainCall[0].Prism
                'Outgoing'    = $ChainCall[0].Outgoing
                'RemoteSite'  = $ChainCall[0].RemoteSite
                'SameCountry' = if ($(Resolve-CountryCode -Object $ChainCall[0].Prism) -eq $(Resolve-CountryCode -Object $ChainCall[0].RemoteSite)) {
                    "Y"
                } else {
                    "N"
                }
                'Incoming'    = $ChainCall[0].Incoming
                'Receives'    = $ChainCall[0].Incoming.count
            }
            $DictValidated.Add($Chain)
        } else {
            $DictSkipped.Add($Prism)
        }
    } -ThrottleLimit 200 # The limitation of running threads simultaneously
    $Results.Validated = $Validated
    $Results.Skipped   = $Skipped
    return $Results
}
function Get-NtnxNodeBios(){
    <#
    .SYNOPSIS
    The function returns a set of Bios version of Nutanix nodes within the given Prism
    
    .DESCRIPTION
    Long description
    
    .PARAMETER Prism
    The name of Prism you want to call, only the Prism Element is acceptable
    It accepts value given by pipeline
    
    .EXAMPLE
    Get-NtnxNodeBios -Prism retjpso-nxc000
    Get-NtnxCluster -Prism ssp-china-ntx | Get-NtnxNodeBios
    
    .NOTES
    General notes
    #>
    param (
        [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] [string] $Prism
    )
    Begin {
        $PCs  = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
        $BMCs = @() # An array that stores result set
    }
    Process {
        if ($Prism -in $PCs) { # If the given Prism is a PC, then return NULL
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The Prism Central is not supported by this function"
            return $null
        }
        $Nodes    = Get-NtnxNode -Prism $Prism # Query the nodes within the Prism
        $OobPword = if ($VaultCall = Get-SecretForSiaB -Prism $Prism -Secret Site_Oob -Display:$true) { # Query the password of iLO from Vault
            $VaultCall.Password
        }
        if (!$Nodes -or !$OobPword) { # Neither the nodes nor password exists, return NULL
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Cannot get required data from'$Prism'"
            return $null
        }
        $Nodes | ForEach-Object { # Iterat each nodes, assemble the node object, then calling Redfish Api to get its Bios version
            $BMC = [PSCustomObject]@{
                'Name'  = $_.Name + 'OOB'
                'Sn'    = $_.Sn
                'Model' = $_.Model
            }
            if ($iLOCall = Rest-iLO-Get-Systems -iLOAddress $_.IpmiIp `
                                                -iLOUsername 'administrator' `
                                                -iLOPassword $OobPword) { # Add Bios version to node object only if the Redfish returns
                $BMC | Add-Member -NotePropertyName 'BiosVer' -NotePropertyValue $iLOCall.BiosVersion
            }
            $BMCs += $BMC # Add assembled object to result set
        }
    }
    End {
        return $BMCs
    }
}
function Get-NtnxNodeFirmware(){
    <#
    .SYNOPSIS
    The function returns Nutanix node firmware information.
    
    .DESCRIPTION
    This function returns a set of firmware information of Nutanix node, or 
    firmware information of each node in the cluster.
    
    .PARAMETER Prism
    The name of Prism you want to call, only the Prism Element is acceptable
    It accepts value given by pipeline, e.g. retcn888-nxc000

    .PARAMETER Node
    The name of the nutanix node, e.g. retcn888-nx7001
    
    .EXAMPLE
    Get-NtnxNodeFirmwares -Prism retcn888-nxc000
    Get-NtnxNodeFirmwares -Node retcn888-nx7001
    
    .NOTES
    General notes
    #>
    param (
        [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true, ParameterSetName = "Prism")] [string] $Prism,
        [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true, ParameterSetName = "Node")] [string] $Node
    )
    Begin {
        $PCs  = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
        $BMCs = @() # An array that stores result set
    }
    Process {
        if ($Prism -in $PCs) { # If the given Prism is a PC, then return NULL
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The Prism Central is not supported by this function"
            return $null
        }
        if ($Node){
            # Get Prism name using the node name
            $Nodes = $Node
            $Site = $Node.Split("-")[0]
            $NxcNumber = $Node.Split("-")[1][3]
            $Prism = $Site + "-NXC00" + $NxcNumber
        } else {
            $Nodes = Get-NtnxNode -Prism $Prism | Select-Object -ExpandProperty Name | Sort-Object
        }

        # Get password for Retail or Warehouse OOB
        
        if ($Prism -like "DS*" -or $Prism -like "MOD*" ) {
            $OobPword = if ($VaultCall = Get-SecretForWiaB -Prism $Prism -Secret Site_Oob -Display:$true) { # Query the password of iLO from Vault
                $VaultCall.Password
            }
        } else {
            $OobPword = if ($VaultCall = Get-SecretForSiaB -Prism $Prism -Secret Site_Oob -Display:$true) { # Query the password of iLO from Vault
                $VaultCall.Password
            }
        }

        if (!$Nodes -or !$OobPword) { # Neither the nodes nor password exists, return NULL
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Cannot get required data from'$Prism'"
            return $null
        }
        $Nodes | ForEach-Object { # Iterat each nodes, assemble the node object, then calling Redfish Api to get its Bios version
            $BMC = [pscustomobject]@{}
            $iLOAddress = $_ + "oob.ikea.com"
            if ($iLOCall = Rest-iLO-5-GetFirmwareInventoryAll -iLOAddress $iLOAddress -iLOUsername 'Administrator' -iLOPassword $OobPword) {
                $BMC = $iLOCall
            }
            $BMCs += $BMC # Add assembled object to result set
        }
    }
    End {
        return $BMCs
    }
}
function Update-PaloVmMemory(){
    <#
    .SYNOPSIS
    The function used to increase memory reservation of Palo Alto VMs reside in the cluster
    
    .DESCRIPTION
    The function can only used to increase memory capacity, decreasing is not allowed
    
    .PARAMETER Prism
    The Prism name of the cluster you want to work with
    It accepts value given by pipeline
    
    .PARAMETER MemoryInGb
    The desired memory settings, default is 12
    
    .EXAMPLE
    Update-PaloVmMemory -Prism retdkso-nxc000 -MemoryInGb 16
    Get-NtnxCluster -Prism ssp-apac-ntx | Update-PaloVmMemory -MemoryInGb 16
    
    .NOTES
    General notes
    #>
    param (
        [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] [string] $Prism,
        [ValidateSet(8, 12, 16, 24, 32, 64)] [int]                                                                  $MemoryInGb = 12
    )
    Begin {
        $PCs = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
    }
    Process {
        if ($Prism -in $PCs) { # If the given Prism is a PC, then return NULL
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The Prism Central is not supported by this function"
            return $null
        }
        if (!$PSBoundParameters.ContainsKey('MemoryInGb')) { # If MemoryInGb is not given, then inform that the function will use default is 12
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're using the default setting of MemoryInGb is 12"
        }
        $PLs = $(Get-NtnxVm -Prism $Prism) | Where-Object {($_.Name -match '-PL000') -and ($_.MemoryInGb -lt $MemoryInGb)} # Get PL VMs from the cluster
        if (!$PLs) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "0 PA VMs matched in $Prism, now we abort"
            return $null
        }
        $PePword = if ($VaultCall = Get-SecretForSiaB -Prism $Prism -Secret Site_Pe_Nutanix -Display:$true) { # Get the password that is enabled to login the CVM
            $VaultCall.Password
        } else {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "It's unable to fetch PE password, now we abort"
            return $null
        }
        $PLs | ForEach-Object { # Increase the memory settings of the VM by running shell command in CVM
            Ssh-Prism-UpdateVmMemory -Fqdn $($Prism + ".ikea.com") `
                                     -Vm $_.Name `
                                     -MemoryInGb $MemoryInGb `
                                     -Username 'nutanix' `
                                     -PWord $PePword
        }
    }
    End {
        return $null
    }
}
function Restart-EachNtnxNodeBmc(){
    <#
    .SYNOPSIS
    This function will reset all BMC (ILO) within a cluster.

    .DESCRIPTION
    This function will reset all BMC (ILO) within a cluster.
    Same as login to BMC (ILO) and press reset button to reset BMC.

    .PARAMETER Prism
    Mandaroty, should be full name of a Prism Element.

    .EXAMPLE
    Restart-EachNtnxNodeBmc -Prism retcn888-nxc000

    .NOTES
    #>
    param (
        [Parameter(Mandatory = $true)] [string] $Prism
    )
    $Nodes    = Get-NtnxNode -Prism $Prism
    $OobPword = if ($VaultCall = Get-SecretForSiaB -Prism $Prism -Secret Site_Oob -Display:$true) {
        $VaultCall.Password
    }
    $BMCs = @()
    if (!$Nodes -or !$OobPword) {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Cannot get required data from'$Prism'"
        return $null
    }
    $Nodes | ForEach-Object {
        $BMC = [pscustomobject]@{
            'Name'  = $_.Name + 'OOB'
            'Sn'    = $_.Sn
            'Model' = $_.Model
        }
        Ssh-iLO-Reset-BMC -iLOAddress $_.IpmiIp `
                          -iLOUsername 'administrator' `
                          -iLOPassword $OobPword
        $BMCs += $BMC
    }
    return $BMCs
}
function Restart-EachNtnxNode(){
    param (
        [Parameter(Mandatory = $true)] [string] $Prism
    )

    # Get nodes from Prism
    $Nodes = Get-NtnxNode -Prism $Prism
    if (!$Nodes) {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Find 0 nodes in '$Prism', let's quit"
        return $null
    }

    # Add Opt property to each node and sort by Opt
    $Nodes | ForEach-Object {
        $_ | Add-Member -NotePropertyName 'Opt' -NotePropertyValue $($_.Name).SubString($($_.Name).Length - 1, 1)
    }
    $Nodes | Select-Object Opt, Name, CvmIp, AhvIp, IpmiIp | Sort-Object Opt | Format-Table
    $SelOpt = Read-Host "Select the node(s) need reboot by input Opt number, like 1 or 1,2,3"
    $Opts   = $SelOpt.Split(",") | Sort-Object -Unique

    # Check if the input is valid
    if ($Opts.Length -gt $Nodes.length) {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Invalid selection, selected are over node number, let's quit"
        return $null
    }
    $Opts | ForEach-Object {
        if ($_ -notin $Nodes.Opt) {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Invalid selection, selected does not in the list, let's quit"
            break
        }
    }
    $Nodes = $Nodes | Where-Object {$_.Opt -in $Opts}
    $Nodes | Select-Object Opt, Name, CvmIp, AhvIp, IpmiIp | Sort-Object Opt | ft
    $FinalGo = Read-Host "Final check, is it correct y/n?"
    if ($FinalGo -eq 'y') {
        $Reboot = Restart-NtnxNode -Prism $Prism -CvmIps $Nodes.CvmIp
    } else {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "User abort the task"
        return $null
    }
    if (!$(($Reboot.value | ConvertFrom-Json).'.return')) {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The reboot task is failed to create"
        return $null
    }
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Monitoring the progress"
    do {
        $TaskCall = Get-NtnxTask -Prism $Prism -Uuid $(($Reboot.value | ConvertFrom-Json).'.return')
        if (!$TaskCall) {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "It's unable to trace the task status"
            break
        }
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The task '$($TaskCall.OperationType)' is running on $($TaskCall.'%')%, we can abort script if you want"
        Start-Sleep 15
    } until (
        ($TaskCall.'%' -eq "100") -or `
        ($TaskCall.Status -ne "Running")
    )
    return $TaskCall
}
function Start-NtnxVM(){
    param (
        [Parameter(Mandatory = $true)] [string] $Prism
    )
    $UVMs       = Get-NtnxVM -Prism $Prism | Where-Object {$_.IsCvm -eq $false}
    $PoweredOff = $UVMs | Where-Object {$_.PowerState -eq "off"}
    $UVMs | Select-Object Name, Uuid, PowerState, Os, Type, IPAddresses, CpuNum, MemoryInGb, StorageInGb | Format-Table
    if ($PoweredOff) {
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We have $($PoweredOff.Length) / $($UVMs.Length) VMs need to be powered on"
        foreach ($Vm in $PoweredOff) {
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Starting up the VM $($Vm.Name)"
            $Hide = Set-NtnxVmPowerState -Prism $Prism -Uuid $Vm.Uuid -State ON
            Start-Sleep 1
        }
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Sleeping 1 minutes after mass startup command sent, then re-check"
        Start-Sleep 60
        Start-NtnxVM -Prism $Prism
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "The progress of power on VMs is done"
    } else {
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We have $($PoweredOff.Length) / $($UVMs.Length) VMs need to be powered on"
    }
}
function Reset-NtnxProtectionDomain(){
    <#
        .DESCRIPTION
        This function will reset the protection domain of cluster, remove all VMs from PD
        And then re-add them.
        It will fix the issue if someone forget to remove VM from protection domain during
        VM decommission process.

        .PARAMETER Prism
        Prism Element name is mandatory for this function.

        .EXAMPLE
        Reset-NtnxProtectionDomain -Prism RETCN888-NXC000
    #>

    param (
        [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] [string] $Prism
    )
    Begin {
        $PCs = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
    }
    Process {
        if ($Prism -in $PCs) { # If the given Prism is a PC, then return NULL
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The Prism Central is not supported by this function"
            return $null
        }
        #Get the _Gold_CCG protection domain of the cluster
        $ProtectionDomain = Get-NtnxProtectionDomain -Prism $Prism
        if (!$ProtectionDomain) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The '$Prism' has no protection domain, lets quit"
            return $null
        }
        #Only if the protection domain exists, get the PD name the protected/unprotected VMs
        $PdName      = $ProtectionDomain.Name
        $Protected   = $ProtectionDomain.Protected
        $Unprotected = $ProtectionDomain.Unprotected
        if ($Protected) {
            #When the protected VMs list is not null, remove all to unprotected and re-get the unprotected VMs list
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Remove '$($Protected.count)' VMs from the protection domain '$PdName'"
            $Hide             = Remove-NtnxVmFromProtectionDomain -Prism $Prism -PdName $PdName -VmIds $Protected.vmId
            $ProtectionDomain = Get-NtnxProtectionDomain -Prism $Prism
            $Unprotected      = $ProtectionDomain.Unprotected
        }
        if ($Unprotected) {
            #Re-add all unprotected VMs to the protection domain
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Add '$($Unprotected.count)' VMs to the protection domain '$PdName'"
            $Hide = Add-NtnxVmToProtectionDomain -Prism $Prism -PdName $PdName -VmIds $Unprotected.uuid
        }
    }
    End {
        return $Hide
    }
}
function Reset-NtnxPcTrust(){
    param (
        [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] [string] $Prism
    )
    Begin {
        $PCs = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
    }
    Process {
        if ($Prism -in $PCs) { # If the given Prism is a PC, then return NULL
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The Prism Central is not supported by this function"
            return $null
        }
        $PePword = if ($VaultCall = Get-SecretForSiaB -Prism $Prism -Secret Site_Pe_Nutanix -Display:$true) { # Get the password that is enabled to login the CVM
            $VaultCall.Password
        }else {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "It's unable to fetch PE password, now we abort"
            return $null
        }
        $PeAdminPass = if ($VaultCall = Get-SecretForSiaB -Prism $Prism -Secret Site_Pe_Admin -Display:$true) { # Get the password that is enabled to login the CVM
            $VaultCall.Password
        }else {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "It's unable to fetch PE Admin password, now we abort"
            return $null
        }
        Ssh-Prism-ResetPcTrust -Fqdn $($Prism + ".ikea.com") -Username 'nutanix' -PWord $PePword -PeAdminPass $PeAdminPass
    }
    End {
        return $null
    }
}
function Update-ReplicationBandwidth(){
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [int64]                                                                                                     $DefBandwidth
    )
    Begin {}
    Process {
        if (($PrismCall = Get-NtnxReplicationChain -Prism $Prism | Select-Object RemoteSite) -and `
            ($TargetRsObj = Get-NtnxRemoteSite -Prism $Prism -RemoteSite "RS_$($PrismCall[0].RemoteSite)") -and `
            ($SourceRsObj = Get-NtnxRemoteSite -Prism $PrismCall[0].RemoteSite -RemoteSite "RS_$($Prism.ToUpper())")
        ) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The remote site is available for the source and target"
            if ($DefBandwidth) {
                Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Update default bandwidth for '$($TargetRsObj.name)' and '$($SourceRsObj.name)'"
                $TargetRsObj.bandwidthPolicy.defaultBandwidthLimit = $DefBandwidth * 1000000
                $SourceRsObj.bandwidthPolicy.defaultBandwidthLimit = $DefBandwidth * 1000000
            }
            Update-NtnxRemoteSite -Prism $Prism -RsObj $TargetRsObj -IsUpdateBandwidthPolicy $false -IsUpdateCompressionEnable $false -IsUpdateVStorenameMap $false
            Update-NtnxRemoteSite -Prism $PrismCall[0].RemoteSite -RsObj $SourceRsObj -IsUpdateBandwidthPolicy $false -IsUpdateCompressionEnable $false -IsUpdateVStorenameMap $false
        }
    }
    End {}
}
function Measure-NtnxCpuUsageRatio(){
    <#
        .SYNOPSIS
        Measure the virtual CPU ratio.

        .DESCRIPTION
        Measure the virtual CPU ratio of cluster. Maximum is 4.

        .PARAMETER Prism
        Prism Element name.

        .EXAMPLE
        Measure-NtnxCpuUsageRatio -Prism retcn856-nxc000
    #>
    param (
        [string] [Parameter(Mandatory = $true)] $Prism
    )
    Begin {
        $Ratios = @()
    }
    Process {
        if (($PrismCall  = Get-NtnxCluster -Prism $Prism | Select-Object Prism, Uuid) -and `
            ($PrismCall1 = Get-NtnxNode -Prism $Prism | Select-Object ClusterUuid, CpuCoreNum) -and `
            ($PrismCall2 = Get-NtnxVM -Prism $Prism | Select-Object ClusterUuid, CpuNum)
        ) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We've fetched all data, jump to math work"
            foreach ($Cluster in $PrismCall) {
                $Ratio = [PSCustomObject]@{
                    'Prism' = $Cluster.Prism
                    'CpuCoreNum' = "NA"
                    'VmNum' = "NA"
                    'VmCpuNum' = "NA"
                    'UsageRatio' = "NA"
                }
                $Nodes = $PrismCall1 | Where-Object {$_.ClusterUuid -eq $Cluster.Uuid}
                $Vms = $PrismCall2 | Where-Object {$_.ClusterUuid -eq $Cluster.Uuid}
                $Ratio.CpuCoreNum = [int]($Nodes | Measure-Object -Property CpuCoreNum -Sum).Sum
                $Ratio.VmNum = $Vms.count
                $Ratio.VmCpuNum = [int]($Vms | Measure-Object -Property CpuNum -Sum).Sum
                $Ratio.UsageRatio = '{0:n2}' -f $($Ratio.VmCpuNum / $Ratio.CpuCoreNum)
                $Ratios += $Ratio
            }
        }else {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to fetch data for ratio calculating"
            return $null
        }
    }
    End {
        return $Ratios
    }
}
function Update-NsbNameInNtnx(){
    <#
        .SYNOPSIS
        Update the virtual NSB VM name.

        .DESCRIPTION
        Update the virtual NSB VM name from LX6001 to BB6001.

        .PARAMETER Prism
        Prism Element or Prism Central.

        .EXAMPLE
        Update-NsbNameInNtnx -Prism retcn856-nxc000
        Update-NsbNameInNtnx -Prism ssp-china-ntx

        .NOTES
        This script only works for replacing VM name from LX6001 to BB6001.
        If there is another requirements, need to modify the specific name.
    #>
    param (
        [string] [Parameter(Mandatory = $true)] $Prism
    )
    Begin {
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $Results    = @()
    }
    Process {
        ## Pull LX6001 VM details from Prism
        Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Pulling LX6001 VM details from $($Prism)"
        $Vms = Rest-Prism-v3-Get-VM -Fqdn ($Prism + '.ikea.com') -Auth $Auth
        $Vms = $Vms | Where-Object {$_.spec.name -match "lx6001"}
        Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "We've got $($Vms.Count) NSBs from $($Prism)"
        ## Update the VM payload and send the VM payload back to Prism
        foreach ($Vm in $Vms) {
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Preparing the payload for the VM $($Vm.spec.name)"
            $Vm.spec.name = $Vm.spec.name -replace "LX6001", "BB6001"
            $Vm.PSObject.Properties.Remove('status')
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Updating the VM $($Vm.spec.name)"
            $Update = Rest-Prism-v3-Update-Vm -Fqdn ($Prism + '.ikea.com') -Uuid $Vm.metadata.uuid -Vm $Vm -Auth $Auth
            $Results += $Update
        }
    }
    End {
        return $Results
    }
}
function Measure-NtnxCapacity(){
    <#
        .SYNOPSIS
        Used for calculating the capacity of the cluster for new VM provisioning

        .DESCRIPTION
        With the given parameters, we can calculate the capacity of the cluster for new VM provisioning, 
        the function can accept Prism from the pipeline

        .PARAMETER Prism
        The Prism name of cluster, like 'RETCA040-NXC000'

        .PARAMETER CPU
        The product of CPU count and cores number of the new VM 

        .PARAMETER MemoryInGiB
        The memory size of the new VM

        .PARAMETER DiskInGiB
        The sum of disk size of the new VM

        .PARAMETER VmQuantity
        The total number of the VMs with the same configuration

        .PARAMETER IsDebug
        Summrized or detailed of the output

        .EXAMPLE
        Measure-NtnxCapacity -Prism 'RETCA040-NXC000' -CPU 4 -MemoryInGiB 8 -DiskInGiB 100 -VmQuantity 2
        Get-NtnxCluster -Prism 'ssp-eu-ntx' | Evaluate-NtnxCapacity -CPU 4 -MemoryInGiB 8 -DiskInGiB 100 -VmQuantity 2
        
    #>

    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [int]                                                                                                       $CPU,
        [int]                                                                                                       $MemoryInGiB,
        [int]                                                                                                       $DiskInGiB,
        [int]                                                                                                       $VmQuantity = 1,
        [switch]                                                                                                    $IsDebug
    )
    Begin {
        $GstAccount        = Read-GstAccount # Load GST account for calling API
        $Auth              = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $CpuRatio          = $(Read-Var).Infrastructure.Retail.Nutanix.Sizing.CpuRatio
        $StorageResilience = $(Read-Var).Infrastructure.Retail.Nutanix.Sizing.StorageResilience
        $ResilienceMax     = $(Read-Var).Infrastructure.Retail.Nutanix.Sizing.ResilienceMax
        $Results           = @()
    }
    Process {
        Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Getting metric data of the cluster $($Prism.ToUpper())"
        $Cluster = Rest-Prism-v1-Get-Cluster -Fqdn ($Prism + '.ikea.com') -Auth $Auth
        Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Getting nodes of the cluster $($Prism.ToUpper())"
        $Nodes = Rest-Prism-v1-Get-Host -Fqdn ($Prism + '.ikea.com') -Auth $Auth
        Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Getting user VMs of the cluster $($Prism.ToUpper())"
        $Vms = Rest-Prism-v2-Get-VM -Fqdn ($Prism + '.ikea.com') -Auth $Auth
        $SumStorage   = 0
        $SumCores     = 0
        $SumMemroy    = 0
        $HighestRam   = 0
        $HighestCores = 0
        $OnVmsCount   = 0
        $OnVmsMemory  = 0
        $OnVmsCores   = 0
        $TotalEntities = $Nodes.metadata.totalEntities
        Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "It's a $($TotalEntities) node(s) cluster"
        foreach ($Node in $Nodes.entities) {
            #Storage
            $NodeStorage = $Node.usageStats.'storage.capacity_bytes'
            $LogicalNodeStorage = $NodeStorage / 2
            $LogicalNodeStorageTib = Convert-BytesTo -Bytes $LogicalNodeStorage -Unit "TiB"
            $SumStorage += $LogicalNodeStorageTib
            #CPU
            $NodeCores = if ($Node.numCpuCores) {
                $Node.numCpuCores
            } else {0}
            $HighestCores = if ($NodeCores -gt $HighestCores) {
                $NodeCores
            }
            $SumCores += $NodeCores
            #Memory
            $NodeMemory = if ($Node.memoryCapacityInBytes) {
                $Node.memoryCapacityInBytes
            } else {0}
            $HighestRam = if ($NodeMemory -gt $HighestRam) {
                $NodeMemory
            }
            $NodeMemoryGiB = Convert-BytesTo -Bytes $NodeMemory -Unit "GiB"
            $SumMemroy += $NodeMemoryGiB
            if ($IsDebug) {
                Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Fetching capacity from node $($Node.name)"
                Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Storage of node $($Node.name) is $($LogicalNodeStorageTib) TiB"
                Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Number of cores on node $($Node.name) is $($NodeCores)"
                Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Memory installed on node $($Node.name) is $($NodeMemoryGiB) GiB"
            }
        }
        #Resilient storage calculation (asuuming one node failure)
        $ResilientStorage = if ($TotalEntities -eq 1) {
            $StorageResilience  * $SumStorage
        } else {
            $StorageResilience  * ($SumStorage - $LogicalNodeStorageTib)
        }
        #Resilient memory calculation
        $HighestRamGiB = Convert-BytesTo -Bytes $HighestRam -Unit "GiB"
        $ResilientRam = if ($TotalEntities -eq 1) {
            $SumMemroy
        } else {
            $SumMemroy - $HighestRamGiB
        }
        #Accounting for the CVMs which use 32 GiB memory
        $ResilientRamCVM = if ($TotalEntities -eq 1) {
            32
        } else {
            ($TotalEntities - 1) * 32
        }
        $TotalResilientRamCVM = $ResilientRam - $ResilientRamCVM
        #Resilient CPU calculation
        $ResilientCores = if ($TotalEntities -eq 1) {
            $SumCores
        } else {
            $SumCores - $HighestCores
        }
        #Accounting for the CVMs which use 3 physical cores
        $CoresCVM = if ($TotalEntities -eq 1) {
            3
        } else {
            ($TotalEntities - 1) * 3
        }
        $PhysicalResilientCores = $ResilientCores - $CoresCVM
        #Pyhsical to virtual CPU mapping (cpu_ratio)
        $ResilientvCPU = $CpuRatio * $PhysicalResilientCores
        #Utilized capacity calculation
        #Used storage
        $UsedClusterStorage = $Cluster.usageStats.'storage.usage_bytes'
        $LogicalUsedClusterStorage = [int64]$UsedClusterStorage / 2
        $LogicalusedCllusterStorageTib = Convert-BytesTo -Bytes $LogicalUsedClusterStorage -Unit "TiB"
        #Used RAM and cores
        foreach ($Vm in $Vms.entities) {
            if ($Vm.power_state -eq "on") {
                $OnVmsCount += 1
                $OnVmsMemory += $Vm.memory_mb
                $OnVmsCores += $Vm.num_vcpus * $Vm.num_cores_per_vcpu
            }
        }
        $OnVmsMemoryGiB = $OnVmsMemory / 1024
        #Avaialbe capacity calculation
        #Storage
        $AvailableStorage = $ResilientStorage - $LogicalusedCllusterStorageTib
        #CPU
        $AvailableCores = $ResilientvCPU - $OnVmsCores
        #Memory
        $AvailableMemory = $TotalResilientRamCVM - $OnVmsMemoryGiB
        if ($IsDebug) {
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Number of nodes in this cluster is $($TotalEntities)"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Total storage capacity on this cluster is $($SumStorage) TiB"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "total number of CPU cores on cluster is $($SumCores)"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Total memory capacity on this cluster is $($SumMemroy) GiB"

            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Resilient storage capacity on this cluster is $($ResilientStorage) TiB"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Number of resilient physical CPU cores is $($ResilientCores)"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Number of resilient physical CPU cores accounting CVMs is $($PhysicalResilientCores)"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Number of resilient virtual CPU cores (assuming 1:$($CpuRatio) ratio) is $($ResilientvCPU)"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Resilient memory capacity on this cluster is $($ResilientRam) GiB"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Resilient memory capacity accounting CVMs on this cluster is $($TotalResilientRamCVM) GiB"

            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Utilized storage of cluster is $($LogicalusedCllusterStorageTib) TiB"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "There are $($Vms.metadata.total_entities) VMs on this cluster"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Number of virtual cores used by $($OnVmsCount) VMs that are powered on is $($OnVmsCores)"
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Memory used by $($OnVmsCount) VMs that are powered on is $($OnVmsMemoryGiB) GiB"
        }
        Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Available storage for new VM provisioning is $($AvailableStorage) TiB"
        Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Available vCPU cores for new VM provisioning is $($AvailableCores)"
        Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level INFO -Message "Available memory for new VM provisioning is $($AvailableMemory) GiB"

        $Result = [PSCustomObject]@{
            'Prism'  = $Prism
            'CPU'    = "PASS"
            'Memory' = "PASS"
            'Disk'   = "PASS"
            'Final'  = "PASS"
        }
        #CPU
        if (($CPU * $VmQuantity) -gt $AvailableCores) {
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level WARN -Message "CPU is NOT sufficient for new VM provisioning requires $($CPU * $VmQuantity) cores"
            $Result.CPU = "FAIL"
        }
        #Memory
        if (($MemoryInGiB * $VmQuantity) -gt $AvailableMemory) {
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level WARN -Message "Memory is NOT sufficient for new VM provisioning requires $($MemoryInGiB * $VmQuantity) GiB of memory"
            $Result.Memory = "FAIL"
        }
        #Disk
        $DiskTiB = $DiskInGiB * $VmQuantity / 1024
        if (($LogicalusedCllusterStorageTib + $DiskTiB) -gt ($ResilienceMax * $ResilientStorage)) {
            Write-ConsoleLog -FunctionName $(Get-FunctionName) -Level WARN -Message "Disk is NOT sufficient for new VM provisioning requires $($DiskTiB) TiB of storage"
            $Result.Disk = "FAIL"
        }
        #Final yes or no flag
        if ("FAIL" -in @($Result.CPU, $Result.Memory, $Result.Disk)) {
            $Result.Final = "FAIL"
        }
        $Results += $Result
    }
    End {
        return $Results
    }
}
function Get-NtnxVmDisk() {
    <#
        .SYNOPSIS
        This function will get the scsi disk information for VMs.

        .DESCRIPTION
        The function will get basic disk information, and works for individual VM 
        or all VMs in a cluster.

        .PARAMETER Prism
        Mandatory
        You must specify the Prism name where the VM located.
        It can accept site ID and will add "-NXC000" by default.
        If you need Prism with NXC001 then you need full prism name.(without .ikea.com)

        .PARAMETER VM
        Optional
        You can specify a VM's name, or a condition to fetch VMs match the condition.
        E.g. if you use "0001", if will fetch all VMs which contains "0001" in VM's name.

        .PARAMETER Uuid
        Optional
        You can use this parameter to just fetch information for specified VM.

        .PARAMETER Tier
        Optional
        The default tier is PROD, you need to specify it for D2/DT/PPE.

        .EXAMPLE
        Get-NtnxVmDisk retcn888

        ---------------------output---------------------
        VmName                          VmUuid                               VmDisks
        ------                          ------                               -------
        RETCN888-NT1001                 f91196c3-9804-4f73-a13d-f90c4aa35760 {@{DiskLable=scsi.0; DiskUuid=...... 
        test                            fadccdc6-155f-428f-91a4-7d337ee75745 {@{DiskLable=scsi.0; DiskUuid=......
        RETCN888-MOVE_5.0               ff49ce5e-eebe-4a18-ad2c-5f2bb1f1b683 {@{DiskLable=scsi.0; DiskUuid=......

        .EXAMPLE
        Get-NtnxVmDisk retcn888 -Vm 0001

        -----------------selection list-----------------
        ╔════════════════════════╗
        ║          Vms           ║
        ╟────────────────────────╢
        ║    1. RETCN888-NT0001  ║
        ║    2. RETCN888-PL0001  ║
        ║    3. RETCN888-WL0001  ║
        ╚════════════════════════╝

        ---------------------output---------------------
        VmName          VmUuid                               VmDisks
        ------          ------                               -------
        RETCN888-WL0001 390ef7b5-9d95-457a-9ea9-c1892f6d0eb1 {@{DiskLable=scsi.0; DiskUuid=......

        .EXAMPLE
        Get-NtnxVmDisks -Prism retcn888 -Uuid 11e13e2a-3a5f-4789-8290-5cea09818f2e
        
        ---------------------output---------------------
        VmName          VmUuid                               VmDisks
        ------          ------                               -------
        RETCN888-NT0001 11e13e2a-3a5f-4789-8290-5cea09818f2e {@{DiskLable=scsi.0; DiskUuid=......

        .NOTES
    #>

    Param(
        [string] [Parameter( Mandatory = $true )]         $Prism,
        [string]                                          $Vm,
        [string]                                          $Uuid,
        [string] [ValidateSet("D2", "DT", "PPE", "PROD")] $Tier = "PROD"
    )

    Begin {
        # Switch tier to get domain name.
        switch ($Tier) {
            "DT" { $Domain = "ikeadt.com" }
            "D2" { $Domain = "ikead2.com" }
            Default { $Domain = "ikea.com" }
        }

        # Validate the prism
        if ($Prism) {
            $Prism = $Prism.ToUpper()
            if ($Prism -like "*-NXC00*") {
                $Fqdn = $Prism + "." + $Domain
                if (Test-Connection -ResolveDestination $Fqdn -Count 1) {
                    Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "$Fqdn is valid and reachable" # resolve DNS to check if cluster exist
                } else {
                    Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "$Fqdn can't be resolved, please make sure you have correct name"
                    break
                }
            } else {
                $Prism = $Prism + "-NXC000"
                $Fqdn = $Prism + "." + $Domain
                if (Test-Connection -ResolveDestination $Fqdn -Count 1) {
                    Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "$Fqdn is valid with DNS"
                } else {
                    Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "$Fqdn can't be resolved, please make sure you have correct name"
                    break
                }
            }
        } else {
            Write-ConsoleLog -Level WARN -FunctionName (Get-FunctionName) -Message "We are missing mandatory parameter(s)"
        }

        # prepare objects to store disk information
        $VmDisks = [PSCustomObject]@{
            # 'VmName'  = ""
            # 'VmUuid'  = ""
            # 'VmDisks' = ""
        }

        $Disks = @()

        # prepare authentication for prism api call, use account from vault
        if($Prism -like "DS*" -or $Prism -like "MOD*") {
            $Account = Get-SecretForWiaB -Prism $Prism -Tier $Tier -Secret Site_Pe_Admin -Display
        } else {
            $Account = Get-SecretForSiaB -Prism $Prism -Tier $Tier -Secret Site_Pe_Admin -Display
        }
        $Auth = Get-Base64Auth -Username $($Account.Username) -PWord $($Account.Password)
    }

    Process {
        # main code
        if ($Vm) {
            # get disks for VM through VM name
            $Vms = Get-NtnxVM -Prism $Prism -Tier $Tier | Where-Object {$_.Name -like "*$Vm*"}

            if ($Vms.Length -eq 1) {
                # append basic data
                $VmName = $Vms.Name
                $VmUuid = $Vms.Uuid
                $VmDisks | Add-Member -NotePropertyName "VmName" -NotePropertyValue $VmName
                $VmDisks | Add-Member -NotePropertyName "VmUuid" -NotePropertyValue $VmUuid
                
                # start to fetch scsi disk information
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "VM $VmName match condition, continue to get the disks info."
                try {
                    $PrismCall = Rest-PrismElement-v2-Get-VM -Fqdn $Fqdn -Uuid $VmUuid -Auth $Auth
                    $DiskInfo = $PrismCall.vm_disk_info
                    Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Got disks info from $VmName"
                    foreach ($Item in $DiskInfo) {
                        $DiskLabel = $Item.disk_address.disk_label
                        if ($DiskLabel -like "scsi*") {
                            $Disk = [PSCustomObject]@{
                                'DiskLable'     = $DiskLabel
                                'DiskIndex'     = $Item.disk_address.device_index
                                'DiskUuid'      = $Item.disk_address.vmdisk_uuid
                                'DiskSize(GB)'  = $Item.size / 1GB
                                'DeviceUuid'    = $Item.disk_address.device_uuid
                            }
                            $Disks += $Disk
                        }
                    }
                    $VmDisks | Add-Member -NotePropertyName "VmDisks" -NotePropertyValue $Disks
                } catch {
                    Write-ConsoleLog ERROR -FunctionName (Get-FunctionName) -Message "Failed to get Disks of $VmName, could be something wrong with API call."
                }
            } elseif ($Vms.Length -gt 1) {
                <# Action when this condition is true #>
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "There are few Vms match the condition, please select the one that you want"
                
                $VmsName = $Vms.Name

                do {
                    Get-Menu -Title "Vms" -Selections $VmsName
                    $Selection = Read-Host "Please enter your selection (Q to quit)"
                    if ($Selection -eq "Q") { return }
                } while (
                    $Selection -lt 1 -or $Selection -gt $Vms.Length
                )

                $Selection -= 1
                $VmName = $Vms[$Selection].Name
                $VmUuid = $Vms[$Selection].Uuid
                $VmDisks | Add-Member -NotePropertyName "VmName" -NotePropertyValue $VmName
                $VmDisks | Add-Member -NotePropertyName "VmUuid" -NotePropertyValue $VmUuid

                # start to fetch scsi disk information
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "VM $VmName selected, continue to get the disks info"
                try {
                    $PrismCall = Rest-PrismElement-v2-Get-VM -Fqdn $Fqdn -Uuid $VmUuid -Auth $Auth
                    $DiskInfo = $PrismCall.vm_disk_info
                    Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Getting disks info from $VmName"
                    foreach ($Item in $DiskInfo) {
                        $DiskLabel = $Item.disk_address.disk_label
                        if ($DiskLabel -like "scsi*") {
                            $Disk = [PSCustomObject]@{
                                'DiskLable'     = $DiskLabel
                                'DiskIndex'     = $Item.disk_address.device_index
                                'DiskUuid'      = $Item.disk_address.vmdisk_uuid
                                'DiskSize(GB)'  = $Item.size / 1GB
                                'DeviceUuid'    = $Item.disk_address.device_uuid
                            }
                            $Disks += $Disk
                        }
                    }
                    $Disks = $Disks | Sort-Object DiskLable
                    $VmDisks | Add-Member -NotePropertyName "VmDisks" -NotePropertyValue $Disks
                } catch {
                    Write-ConsoleLog ERROR -FunctionName (Get-FunctionName) -Message "Failed to get Disks of $VmName, could be something wrong with API call"
                }
            } else {
                Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "Not able to find a VM like/contain $Vm, please make sure you have correct input"
            }
        } elseif ($Uuid) {
            # get disks for VM through VM Uuid
            try {
                $PrismCall = Rest-PrismElement-v2-Get-VM -Fqdn $Fqdn -Uuid $Uuid -Auth $Auth

                $DiskInfo = $PrismCall.vm_disk_info
                $VmName = $PrismCall.name
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Found $VmName matching $Uuid"
                $VmDisks | Add-Member -NotePropertyName "VmName" -NotePropertyValue $VmName
                $VmDisks | Add-Member -NotePropertyName "VmUuid" -NotePropertyValue $Uuid

                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Getting disks info from $VmName"
                foreach ($Item in $DiskInfo) {
                    $DiskLabel = $Item.disk_address.disk_label
                    if ($DiskLabel -like "scsi*") {
                        $Disk = [PSCustomObject]@{
                            'DiskLable'     = $DiskLabel
                            'DiskIndex'     = $Item.disk_address.device_index
                            'DiskUuid'      = $Item.disk_address.vmdisk_uuid
                            'DiskSize(GB)'  = $Item.size / 1GB
                            'DeviceUuid'    = $Item.disk_address.device_uuid
                        }
                        $Disks += $Disk
                    }
                }
                $Disks = $Disks | Sort-Object DiskLable
                $VmDisks | Add-Member -NotePropertyName "VmDisks" -NotePropertyValue $Disks
            } catch {
                Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "Not able to find $Uuid in $Prism, please make sure you have correct input"
            }
        } else {
            # get disks for all VMs in this cluster
            $VmDisks = @()

            try {
                $PrismCall = Rest-PrismElement-v2-Get-VM -Fqdn $Fqdn -Auth $Auth
                $Entities = $PrismCall.entities
                
                foreach ($Entity in $Entities) {

                    $DiskInfo = $Entity.vm_disk_info
                    $VmDisk = [PSCustomObject]@{
                        'VmName' = $Entity.name
                        'VmUuid' = $Entity.uuid
                    }
                    $Disks = @()
                    
                    Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Getting disks info from $($Entity.name)"

                    foreach ($Item in $DiskInfo) {
                        $DiskLabel = $Item.disk_address.disk_label
                        if ($DiskLabel -like "scsi*") {
                            $DiskUuid = $Item.disk_address.vmdisk_uuid
                            $DiskSize = $Item.size / 1GB
                            $Disk = [PSCustomObject]@{
                                'DiskLable'     = $DiskLabel
                                'DiskIndex'     = $Item.disk_address.device_index
                                'DiskUuid'      = $Item.disk_address.vmdisk_uuid
                                'DiskSize(GB)'  = $Item.size / 1GB
                                'DeviceUuid'    = $Item.disk_address.device_uuid
                            }
                            $Disks += $Disk
                        }
                    }
                    
                    $Disks = $Disks | Sort-Object DiskLable
                    $VmDisk | Add-Member -NotePropertyName "VmDisks" -NotePropertyValue $Disks
                    $VmDisks += $VmDisk
                }
            } catch {
                Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "Not able to fetch data from $Prism, error message: $_"
            }
        }
    }

    End {
        # return Disks
        return $VmDisks
    }
}
function Get-NtnxClusterList() {
    <#
    .SYNOPSIS
    The function returns a list of clusters from a given Prism endpoint a particular cluster by given uuid
    
    .DESCRIPTION
    With the given Prism value is a PC endpoint, it returns a list of clusters in this PC, 
    once the uuid is given, it filters and returns a cluster from the Prism by uuid
    Besides, the Prism can accept PE, it will only return cluster itself
    
    .PARAMETER Prism
    The name of the Prism you want to call, can either Prism Central PC cluster name, such as retcnchn-nxp001
    It accepts value from pipeline
    
    .PARAMETER Uuid
    The uuid of the cluster
    
    .EXAMPLE
    Get-NtnxClusterList -Prism ssp-china-ntx

    Prism           Uuid                                 Nodes
    -----           ----                                 -----
    RETCNC03-NXC000 0005dcfe-7cd9-5a58-0269-d4f5ef575560     4
    RETCNC02-NXC000 0005c0f0-2d67-d6e8-4793-d4f5ef1e8730     4
    RETCN485-NXC000 0005f1d3-4f0a-4c27-3afd-84160c3974be     3
    RETCN673-NXC000 0005f181-4698-4bc5-5215-84160c39c198     3
    RETCN667-NXC000 00060b6f-425f-17bf-6b5e-1423f27cd310     3
    RETCN164-NXC000 0005f53a-9076-3c3b-26c3-84160c498d10     3
    RETCN330-NXC000 0005f26e-9609-d2ef-6a28-84160c49e9f2     3


    Get-NtnxClusterList -Prism ssp-apac-ntx -Uuid 0005af44-2ff9-c259-246c-48df37e18a60

    Prism           Uuid                                 Nodes
    -----           ----                                 -----
    RETJP647-NXC000 0005af44-2ff9-c259-246c-48df37e18a60     3

    .NOTES
    General notes
    #>
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [string]                                                                                                    $Uuid
    )
    Begin {
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $Clusters   = @() # The list for store result set
    }
    Process {
        if ($Uuid) {
            $PrismCall = Rest-Prism-v3-Get-Cluster -Fqdn ($Prism + '.ikea.com') -Uuid $Uuid -Auth $Auth
        } else {
            $PrismCall = Rest-Prism-v3-List-Cluster -Fqdn ($Prism + '.ikea.com') -Auth $Auth
        }
        if ($PrismCall) {
            $PrismCall | ForEach-Object {
                if ($_.status.name -notlike "*nxp*") {
                    $Cluster = [PSCustomObject]@{
                        'Prism' = $_.status.name
                        'Uuid'  = $_.metadata.uuid
                        'Nodes' = $_.status.resources.nodes.hypervisor_server_list.length - 1
                    }
                    $Clusters += $Cluster
                } else {
                    # Skip nxp from cluster list
                }             
            }
        }
    }
    End {
        $Clusters = $Clusters | Sort-Object Prism
        return $Clusters
    }
}
function Get-NtnxCluster() {
    <#
    .SYNOPSIS
    The function returns a list of clusters under the PC or details of a particular cluster by given parameter
    
    .DESCRIPTION
    If $Prism equals a PC address, it returns a list of clusters under the PC
    If $Prism equals a PC and $Uuid equals the uuid of a cluster, it returns details of a particular cluster which has given uuid
    If $Prism equals a cluster address, it returns details of given cluster, in this case the given uuid will be ignore
    
    .PARAMETER Prism
    The name of the Prism you want to call, can either Prism Central or Element or PC cluster name, such as retcnchn-nxp001
    It accepts value from pipeline
    
    .PARAMETER FetchLcm
    Enable this option to fetch AHV/SPP related LCM information.

    .PARAMETER Certificate
    Enable this option to fetch certificate information.

    .PARAMETER Vm
    Enable this option to fetch VM list with power states.
    
    .EXAMPLE
    Get-NtnxCluster -Prism retcn856-nxc000
    Get-NtnxCluster -Prism retcn856-nxc000 -FetchLcm
    Get-NtnxCluster -Prism retcn856-nxc000 -FetchLcm -Certificate -Vm

    .NOTES
    General notes
    #>
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [switch]                                                                                                    $FetchLcm,
        [switch]                                                                                                    $Certificate,
        [switch]                                                                                                    $Vm
    )
    Begin {
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $Clusters   = @() # The list for store result set
    }
    Process {
        if ($PrismCall = Rest-Prism-v1-Get-Cluster -Fqdn ($Prism + '.ikea.com') -Auth $Auth) {
            $Cluster = [pscustomobject]@{
                'Prism'     = $PrismCall.name
                'Uuid'      = $PrismCall.uuid
                'ClusterIp' = $PrismCall.clusterExternalIPAddress
                'NodeNum'   = $PrismCall.numNodes - 1
                'NodeModel' = $PrismCall.rackableUnits.modelName | Sort-Object -Unique
                'AosVer'    = $PrismCall.version
                'NccVer'    = $PrismCall.nccVersion
            }
            if ($FetchLcm -and ($LcmFwCall = Rest-Genesis-Get-LcmFw -Fqdn ($Prism + '.ikea.com') -Auth $Auth)) {
                $Cluster | Add-Member -NotePropertyName "Lcm" -NotePropertyValue ($LcmFwCall.semantic_version)

                $AhvVers = @()
                $SppVers = @()
                
                # Retrieve the node name and serial number with uuid
                $Nodes = @{}
                (Rest-Prism-v1-Get-Host -Fqdn ($Prism + '.ikea.com') -Auth $Auth).entities | ForEach-Object {
                    $Nodes.Add($_.uuid, @{ 'NodeName' = $_.name; 'Serial' = $_.serial })
                }

                # Start to get versions for each node
                if ($LcmCall = Rest-Lcm-v4-Get-LcmEntity -Fqdn ($Prism + '.ikea.com') -Auth $Auth) {
                    switch ($LcmCall) {
                        { $_.entityClass -eq 'Hypervisor' } {
                           # Write-Host "AHV $($_.version)"
                           $Uuid = ($_.locationId).split(":")[1]
                           $AhvVer = [PSCustomObject]@{
                               'Name'       = $Nodes[$Uuid].NodeName
                               'Serial'     = $Nodes[$Uuid].Serial
                               'Uuid'       = $Uuid
                               'Version'    = $_.version
                               'LastUpdate' = $_.lastUpdatedTime
                           }
                           $AhvVers += $AhvVer
                        }
                        { $_.entityClass -eq 'SPP' } {
                           # Write-Host "SPP $($_.version)"
                           $Uuid = ($_.locationId).split(":")[1]
                           $SppVer = [PSCustomObject]@{
                               'Name'       = $Nodes[$Uuid].NodeName
                               'Serial'     = $Nodes[$Uuid].Serial
                               'Uuid'       = $Uuid
                               'Version'    = $_.version
                               'LastUpdate' = $_.lastUpdatedTime
                           }
                           $SppVers += $SppVer
                        }
                        { $_.entityModel -eq 'Foundation'} {
                            $Cluster | Add-Member -NotePropertyName "FoundationVer" -NotePropertyValue $_.version
                        }
                    }
                $Cluster | Add-Member -NotePropertyName "AhvVers" -NotePropertyValue $AhvVers
                $Cluster | Add-Member -NotePropertyName "SppVers" -NotePropertyValue $SppVers
                }
            }
            if ($Certificate -and ($CertCall = Rest-Prism-v1-Get-Keys -Fqdn ($Prism + '.ikea.com') -Auth $Auth)) {
                $Cluster | Add-Member -NotePropertyName "CertOrganizationName" -NotePropertyValue ($CertCall.organizationName)
                $Cluster | Add-Member -NotePropertyName "CertExpiryDate"       -NotePropertyValue ($CertCall.expiryDate)
                $Cluster | Add-Member -NotePropertyName "CertKeyType"          -NotePropertyValue ($CertCall.keyType)
                $Cluster | Add-Member -NotePropertyName "CertSignAlgoName"     -NotePropertyValue ($CertCall.signAlgoName)
            }
            if ($Vm -and ($VmCall = Rest-Prism-v1-Get-VM -Fqdn ($Prism + '.ikea.com') -Auth $Auth)) {
                $VMs = @()
                $VmCall.entities | ForEach-Object {
                    $Workload = [PSCustomObject]@{
                        'Name'       = $_.vmName
                        'PowerState' = $_.powerState
                    }
                    $VMs += $Workload
                }
                $Cluster | Add-Member -NotePropertyName "Vm" -NotePropertyValue $VMs
            }
        $Clusters += $Cluster
        }
    }
    End {
        return $Clusters
    }
}
function Get-NtnxVg() {
    <#
        .SYNOPSIS
        Get volume group information for Nutanix cluster.

        .DESCRIPTION
        Get volume group information for Nutanix cluster.

        .PARAMETER Prism
        Standard cluster name preferred. It will return volume group 
        from a single Nutanix cluster. 
        It can accept central Pc cluster and it will be same as 
        parameter Pc.

        .PARAMETER Pc
        Either Pc alias name or Pc cluster name is accepted.

        .PARAMETER Auth
        The script will use GST acount as default account to call API.
        It's also possible to use other account with $Auth to call API.
        Use Get-Base64Auth to generate the authentication string.

        .PARAMETER FilterCriteria
        Filter the volume group information with given criteria.
        Then you can specify the volume group name or other criteria to filter the result.

        .EXAMPLE
        Get-NtnxVg -Prism retcn888
        ------------------Output------------------
        VgName       : TS
        NfsDiskUuid  : 1770313a-d090-4f81-be04-d32def1bdff9
        AttachType   : kExternalAttach
        Capacity(GB) : 20
        SyncStatus   : 

        VgName       : WinClusterQuorum
        NfsDiskUuid  : df0d6398-3af7-4f06-80f8-7841ac670513
        AttachType   : kDirectAttach
        Capacity(GB) : 5
        SyncStatus   : 

        .EXAMPLE
        Get-NtnxVg -Pc dsseelm-nxc000
        Get-NtnxVg -Pc ssp-eu-wiab-ntx
        ------------------Output------------------
        VgName       : DSBE359_LX4210_LX4211_VG1
        NfsDiskUuid  : 
        AttachType   : kExternalAttach
        Capacity(GB) : 0
        SyncStatus   : SYNCED

        VgName       : DSBE359_NT5000_NT5001_VG1
        NfsDiskUuid  : 
        AttachType   : kExternalAttach
        Capacity(GB) : 0
        SyncStatus   : SYNCED

        VgName       : DSDE022_LX4210_LX4211_VG1
        NfsDiskUuid  : b6a83fe0-5d3b-402b-9918-0edfc0c1a67d
        AttachType   : kExternalAttach
        Capacity(GB) : 300
        SyncStatus   : SYNCED
        ......

        .EXAMPLE
        Get-NtnxVg -Pc dsseelm-nxc000 -FilterCriteria "name==DSDE022_NT5000_NT5001_VG1"
        ------------------Output------------------
        VgName       : DSDE022_NT5000_NT5001_VG1
        NfsDiskUuid  : {62acbe5f-2e56-4daf-9e58-a8ab7aaf5606, 4a418423-ec46-40d2-881…}
        AttachType   : kExternalAttach
        Capacity(GB) : 2335
        SyncStatus   : SYNCED

        .NOTES
        #2024-10-28 Adding capability to fetch VG information from Pc and return sync
                    Status
    #>
    [CmdletBinding(DefaultParameterSetName = 'Prism')]
    
    Param(
        [Parameter (ParameterSetName = 'Prism', Position = 0)] [string] $Prism,
        [Parameter (ParameterSetName = 'Pc', Position = 0)] [string]    $Pc,
                                                            [string]    $Auth,
                                                            [string]    $FilterCriteria
    )

    Begin {
        # Verify input, if it's empty, will set Prism as default parameter
        if (-not ($Prism -or $Pc)) {
            Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "You must input Prism or Pc name to continue"
            break
        }

        # Get central Pc for SiaB and WiaB
        $Vars           = Read-Var
        $SiabPcs        = $Vars.Infrastructure.Retail.Nutanix.PrismCentral.Name
        $SiabPcClusters = $Vars.Infrastructure.Retail.Nutanix.PrismCentral.Cluster
        $WiabPcs        = $Vars.Infrastructure.Warehouse.Nutanix.PrismCentral.Name
        $WiabPcClusters = $Vars.Infrastructure.Warehouse.Nutanix.PrismCentral.Cluster

        if ($Pc) {
            if ($Pc -like "*NXC*" -and $Pc -in $SiabPcClusters) {
                $Obj = $Vars.Infrastructure.Retail.Nutanix.PrismCentral | Where-Object {$_.Cluster -like $Pc}
                $Pc      = $Obj.Name
                $Prism   = $Obj.Cluster
                $Domain  = $Obj.Domain
                $Tier    = $Obj.Tier
                $Area    = $Obj.Area
                $Cluster = $Obj.Cluster
                $Fqdn    = $Pc + "." + $Domain

                # $Account = Get-SecretForSiaB -Prism $Prism -Tier $Tier -Secret Site_Pc_Admin -Display
            } elseif ($Pc -like "*NXC*" -and $Pc -in $WiabPcClusters) {
                $Obj     = $Vars.Infrastructure.Warehouse.Nutanix.PrismCentral | Where-Object {$_.Cluster -like $Pc}
                $Pc      = $Obj.Name
                $Prism   = $Obj.Cluster
                $Domain  = $Obj.Domain
                $Tier    = $Obj.Tier
                $Area    = $Obj.Area
                $Cluster = $Obj.Cluster
                $Fqdn    = $Pc + "." + $Domain

                # $Account = Get-SecretForWiaB -Prism $Prism -Tier $Tier -Secret Site_Pc_Admin -Display
            } elseif ($Pc -in $SiabPcs) {
                $Obj = $Vars.Infrastructure.Retail.Nutanix.PrismCentral | Where-Object {$_.Name -like $Pc}
                $Pc      = $Obj.Name
                $Prism   = $Obj.Cluster
                $Domain  = $Obj.Domain
                $Tier    = $Obj.Tier
                $Area    = $Obj.Area
                $Cluster = $Obj.Cluster
                $Fqdn    = $Pc + "." + $Domain

                # $Account = Get-SecretForSiaB -Prism $Prism -Tier $Tier -Secret Site_Pc_Admin -Display
            } elseif ($Pc -in $WiabPcs) {
                $Obj     = $Vars.Infrastructure.Warehouse.Nutanix.PrismCentral | Where-Object {$_.Name -like $Pc}
                $Pc      = $Obj.Name
                $Prism   = $Obj.Cluster
                $Domain  = $Obj.Domain
                $Tier    = $Obj.Tier
                $Area    = $Obj.Area
                $Cluster = $Obj.Cluster
                $Fqdn    = $Pc + "." + $Domain

                # $Account = Get-SecretForWiaB -Prism $Prism -Tier $Tier -Secret Site_Pc_Admin -Display
            } else {
                Write-ConsoleLog -Level WARN -FunctionName (Get-FunctionName) -Message "You've input a wrong Pc name/alias or it's not in config.json, please verify"
                break
            }
        } else {
            $Obj    = ConvertTo-StandardPrism -Name $Prism
            $Prism  = $Obj.PrismName
            $Domain = $Obj.Domain
            $Tier   = $Obj.Tier
            $Fqdn   = $Obj.Fqdn
        }
        
        if (!$Auth) {
            Write-ConsoleLog -FunctionName (Get-FunctionName) -Message "No auth provided, will try to get gst account from registry"
            $Account = Read-GstAccount -Tier $Tier
            $Auth = Get-Base64Auth -Username $($Account.Username) -PWord $($Account.Password)
        }
        
        # Test Prism connection
        try {
            $Hide = Test-NetConnection -ComputerName $Fqdn -InformationLevel Quiet

            if ($Hide) {
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "$Fqdn is online, continue"
            } else {
                Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "$Fqdn is offline, please check"
                return
            }
        } catch {
            Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "Test network connection failed on $Fqdn, please verify if the cluster in online"
        }

        $Body       = [PSCustomObject]@{
            'entity_type'                  = "volume_group_config"
            'group_member_sort_attribute'  = "name"
            'group_member_sort_order'      = "ASCENDING"
            'group_member_count'           = 100
            'group_member_offset'          = 0
            'filter_criteria'              = "(is_hidden==false,is_hidden==[no_val])"
            'group_member_attributes' = @(
                @{ attribute = "name" }
                @{ attribute = "controller_user_bytes" }
                @{ attribute = "nutanix_nfs_based_virtual_disk_uuids" }
                @{ attribute = "client_uuids" }
                @{ attribute = "controller_num_iops" }
                @{ attribute = "controller_io_bandwidth_kBps" }
                @{ attribute = "controller_avg_io_latency_usecs" }
                @{ attribute = "cluster_name" }
                @{ attribute = "synchronous_replication_status"}
                @{ attribute = "_master_cluster_uuid_" }
                @{ attribute = "owner_reference" }
                @{ attribute = "volume_group_attachment_type" }
                @{ attribute = "capacity_bytes" }
                @{ attribute = "vm_uuids" }
                @{ attribute = "protection_rule_uuid" }
                @{ attribute = "protection_rule_name" }
                @{ attribute = "categories" }
                @{ attribute = "is_hidden" }
                @{ attribute = "usage_type" }
            )
        }

        if ($FilterCriteria) {
            $Body.filter_criteria = $FilterCriteria
        }
    }

    Process {
        # Fetching data for volume groups
        Write-ConsoleLog INFO -FunctionName (Get-FunctionName) -Message "Start to call rest api for volume group"
        $PrismCall = Rest-Prism-v3-Get-Groups -Fqdn $Fqdn -Body $Body -Auth $Auth
        $Entities  = $PrismCall.group_results.entity_results
        $VgList    = @()
        
        foreach ($Entity in $Entities) {
            $VgName = ($Entity.data | Where-Object {$_.name -eq "name"}).values.values
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Start to fetch data for VG $VgName"
            $Vg = [PSCustomObject]@{
                'VgName'       = $VgName
                'NfsDiskUuid'  = ($Entity.data | Where-Object {$_.name -eq 'nutanix_nfs_based_virtual_disk_uuids'}).values.values
                'AttachType'   = ($Entity.data | Where-Object {$_.name -eq "volume_group_attachment_type"}).values.values
                'Capacity(GB)' = ($Entity.data | Where-Object {$_.name -eq "capacity_bytes"}).values.values / 1GB
                'SyncStatus'   = ($Entity.data | Where-Object {$_.name -eq "synchronous_replication_status"}).values.values
            }
            $VgList += $Vg
        }
    }

    End {
        # end code
        return $VgList
    }
}

function Get-NtnxVm() {
    <#
    .SYNOPSIS
    The function returns a list of VMs from Prism Central or Element
    
    .DESCRIPTION
    Long description
    
    .PARAMETER Prism
    The Prism name, e.g. 'RETCN856-NXC000' or 'SSP-EU-NTX'

    .PARAMETER Tier
    Optional, default tier is PROD, you need to specify for D2/DT/PPE.
    
    .EXAMPLE
    Get-NtnxVM -Prism RETUS156-NXC000 | Format-Table
    Name                     Uuid                                 PowerState Os                Type                  IsCvm
    ----                     ----                                 ---------- --                ----                  -----
    RETUS156-BB6001          04088ca8-889b-421c-86ba-e0b096428a8b on         Others            Others                False
    RETUS156-NT1000          0d3330de-d6ef-4543-a90b-afd4acc6d727 on         Windows           Terminal              False
    RETUS156-LX4000          16bd41a3-d0c7-4106-a44f-6c32eb075cc2 on         Linux             PBR                   False
    RETUS156-MOVE_5.0        175bf121-64f9-41a5-bbfc-136ecd1036c9 off        Others            Others                False
    POSUS156-NT4030          285460e3-029a-493c-bc1e-dc5f6517c83e on         Windows           Application           False
    RETUS156-LX4020          38bf9479-a989-4ea9-8efb-5c7b32eebd53 on         Linux             Others                False
    RETUS156-LX4060          3b290424-59df-40dc-956d-738263cd65f5 on         Linux             LIP                   False
    RETUS156-NT1001          43190be9-0aa6-4abf-b27a-83d017069eb9 on         Windows           Terminal              False
    NTNX-RETUS156-NX7003-CVM 306fd5cb-d7eb-4563-8b28-2c5c4218470f on         AOS               Nutanix Controller VM  True
    RETUS156-NT0001          537f93f7-61c2-4c7e-a8b2-bb36b434bf80 on         Windows           FSOL                  False
    RETUS156-PL0002          5db7bdaf-8016-4c08-b71f-bfc01d9024b4 on         Network Appliance Palo Alto             False
    NTNX-RETUS156-NX7002-CVM 2624bb66-cbdd-421b-8a6d-aa459ba2039f on         AOS               Nutanix Controller VM  True
    RETUS156-PL0001          93e02c17-87a7-4c14-8fd1-0eee1a72adde on         Network Appliance Palo Alto             False
    RETUS156-LX4100          98ee2d5c-66f5-4d47-b4d8-51fbe18f7e51 on         Linux             Others                False
    RETUS156-LX2030          afd5349d-a320-4b5b-adb4-41dfcb84e623 on         Linux             Others                False
    RETUS156-LX4070          bc98b2be-625d-4d88-94e3-3d091229b9b9 on         Linux             LIP                   False
    NTNX-RETUS156-NX7001-CVM 38697be6-0f1a-42ad-8703-90e087b34138 on         AOS               Nutanix Controller VM  True
    RETUS156-LX2020          efc2e81c-517b-4247-bc98-7d2a04529235 on         Linux             Others                False
    
    .NOTES
    General notes
    #>
    ##The parameter Prism can either Element or Central
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [string] [ValidateSet("D2", "DT", "PPE", "PROD")]                                                           $Tier = "PROD"               
    )
    Begin {
        # Transfer input to standard Prism name
        $Obj    = ConvertTo-StandardPrism -Name $Prism
        $Prism  = $Obj.PrismName
        $Domain = $Obj.Domain
        $Tier   = $Obj.Tier
        $Fqdn   = $Obj.Fqdn

        # Get authentication for rest call
        # Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Get authentication for rest call"
        # if($Prism -like "DS*" -or $Prism -like "MOD*") {
        #     $Account = Get-SecretForWiaB -Prism $Prism -Tier $Tier -Secret Site_Pe_Admin -Display
        # } else {
        #     $Account = Get-SecretForSiaB -Prism $Prism -Tier $Tier -Secret Site_Pe_Admin -Display
        # }
        # $Auth = Get-Base64Auth -Username $($Account.Username) -PWord $($Account.Password)
        $Account = Read-GstAccount -Tier $Tier
        $Auth = Get-Base64Auth -Username $($Account.Username) -PWord $($Account.Password)
        $VMs  = @()
    }
    Process {
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're now getting VMs from $Prism"
        if ($PrismCall = Rest-Prism-v1-Get-VM -Fqdn $Fqdn -Auth $Auth) {
            $PrismCall.entities | ForEach-Object {
                $Category = Resolve-VmCategory -Object $_.vmName
                $VM = [PSCustomObject]@{
                    'Name'        = $_.vmName
                    'Uuid'        = $_.uuid
                    'PowerState'  = $_.powerState
                    'Os'          = $Category.OS
                    'Type'        = $Category.Type
                    'IsCvm'       = $_.controllerVm
                    'IPAddresses' = $_.ipAddresses
                    'HostName'    = $_.hostName
                    'NicNum'      = $_.virtualNicUuids.count
                    'MemoryInGb'  = $([int64]$_.memoryCapacityInBytes)/1024/1024/1024
                    'CpuNum'      = $_.numVCpus
                    'DiskNum'     = $_.vdiskNames.count
                    'StorageInGb' = [int64]($([int64]$_.diskCapacityInBytes)/1024/1024/1024)
                    'ClusterUuid' = $_.clusterUuid
                }
                $VMs += $VM
            }
        }
    }
    End {
        return $VMs
    }
}
function Get-NtnxNode() {
    <#
    .SYNOPSIS
    The function returns a list of nodes from the given Prism
    
    .DESCRIPTION
    The Prism can either Prism Central or Element
    
    .PARAMETER Prism
    The name of the Prism you want to call, either the Central or Element are acceptable, the disk number will not be returned when the Prism equals to the Central
    It accepts given by pipeline

    .PARAMETER Nic
    Switch parameter to return the NIC information
    
    .EXAMPLE
    Get-NtnxNode -Prism retjpso-nxc000
    Get-NtnxNode -Prism ssp-eu-ntx
    Get-NtnxCluster -Prism ssp-china-ntx | Get-NtnxNode
    Get-NtnxNode -prism retinso-nxc000 -Nic:$true
    
    .NOTES
    General notes
    #>
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [string] [Parameter(ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)]                    $Auth,
        [switch]                                                                                                    $Nic
    )
    Begin {
        if (!$Auth) {
            $GstAccount = Read-GstAccount # Load GST account for calling API
            $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate a Base64 formatted authentication string
        }
        $Nodes      = @() # A list that stores result
    }
    Process {
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're now getting nodes from $Prism"
        if ($PrismCall  = Rest-Prism-v1-Get-Host -Fqdn $($Prism + '.ikea.com') -Auth $Auth) { 
            # Calling API towards to the Prism, if it gets result then assemble the node object
            $PrismCall.entities | ForEach-Object {
                $Node = [PSCustomObject]@{
                    'Name'        = $_.name
                    'Uuid'        = $_.uuid
                    'DiskNum'     = if ($_.diskHardwareConfigs) {$($_.diskHardwareConfigs | Get-Member -MemberType NoteProperty).length}else {"NA"}
                    'Sn'          = $_.serial
                    'Model'       = $_.blockModelName
                    'AHV'         = $_.hypervisorFullName
                    'Memory'      = $_.memoryCapacityInBytes
                    'AhvIp'       = $_.hypervisorAddress
                    'CvmIp'       = $_.serviceVMExternalIP
                    'CpuModel'    = $_.cpuModel
                    'CpuCoreNum'  = $_.numCpuCores
                    'IpmiIp'      = $_.ipmiAddress
                    'Maintenance' = $_.hostInMaintenanceMode
                    'ClusterUuid' = $_.clusterUuid
                }
                if ($Nic -and ($NicCall = Rest-Prism-v1-Get-HostNic -Fqdn $($Prism + '.ikea.com') -Uuid $_.uuid -Auth $Auth)) {
                    Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're now getting the NIC details of '$($_.name)'"
                    $NicIndex = 1
                    foreach($N in $NicCall) {
                        $Node | Add-Member -NotePropertyName $("Nic" + $NicIndex + "Name")     -NotePropertyValue $N.name
                        $Node | Add-Member -NotePropertyName $("Nic" + $NicIndex + "Uuid")     -NotePropertyValue $N.uuid
                        $Node | Add-Member -NotePropertyName $("Nic" + $NicIndex + "Mac")      -NotePropertyValue $N.macAddress
                        $Node | Add-Member -NotePropertyName $("Nic" + $NicIndex + "Speed")    -NotePropertyValue "" + ([int]$N.linkSpeedInKbps / [Math]::Pow(1000, 2)) + " Gbps"
                        $Node | Add-Member -NotePropertyName $("Nic" + $NicIndex + "Mtu")      -NotePropertyValue $N.mtuInBytes
                        $Node | Add-Member -NotePropertyName $("Nic" + $NicIndex + "SwDevice") -NotePropertyValue $N.switchDeviceId
                        $Node | Add-Member -NotePropertyName $("Nic" + $NicIndex + "SwPort")   -NotePropertyValue $N.switchPortId
                        $Node | Add-Member -NotePropertyName $("Nic" + $NicIndex + "SwVender") -NotePropertyValue $N.switchVendorInfo
                        $Node | Add-Member -NotePropertyName $("Nic" + $NicIndex + "SwVlan")   -NotePropertyValue $N.switchVlanId
                        $NicIndex ++
                    }
                }
                $Nodes += $Node
            }
        }
    }
    End {
        return $Nodes
    }
}
function Get-NtnxLicense() {
    <#
    .SYNOPSIS
        Get license information for Nutanix cluster(s).

    .DESCRIPTION
        Get license information for individual Nutanix cluster or all 
        clusters within a PC.

    .PARAMETER Prism
        Prism Element name, same as the cluster name in Vault.
        Or you can use PC alias name, e.g. ssp-china-ntx
    
    .NOTES
        Author:         <JAWAN36>
        Creation Date:  <2024-06-17>
        Purpose/Change: Update for new functions
    
    .EXAMPLE
        Get-NtnxLicense -Prism RETCN247-NXC000

        Description
        -----------
        Get the license inforamtion for cluster RETCN247-NXC000.
    #>
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism
    )
    Begin {
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $PCs        = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
        $Licenses   = @() # The list for store result set
    }
    Process {
        if ($Prism -in $PCs) {
            $PrismCall = Rest-Prism-v1-List-Cluster -Fqdn ($Prism + '.ikea.com') -Auth $Auth
            if (!$PrismCall) {
                Write-ConsoleLog -Level WARN -Message "Failed to get cluster list from '$Prism', quit"
                return $null
            }
            $PrismCall = $PrismCall.entities
            $PrismCall | ForEach-Object {
                Write-ConsoleLog -Level INFO -Message "We're now collecting license info on cluster '$($_.name)'"
                if ($LicenseCall = Rest-Prism-v1-Get-License -Fqdn ($_.name + '.ikea.com') -Auth $Auth) {
                    if ($LicenseCall.licenseInfoDTO.pcDetailsDTO.licenses) {
                        # Nutanix has changed license information to licenseInfoDTO
                        $LicenseInfoDTO = $LicenseCall.licenseInfoDTO.pcDetailsDTO.licenses
                        $Categories = $LicenseInfoDTO.category | Sort-Object | Get-Unique
                        if($Categories -notcontains "PRO"){
                            $LicensedCores            = ($LicenseInfoDTO | Where-Object {$_.meter -eq "CORES"} | Measure-Object -Property quantity -Sum).Sum
                            $LicensedCoresExpiryDate  = ($LicenseInfoDTO | Where-Object {$_.meter -eq "CORES"} | Measure-Object -Property expiryDate -Minimum).Minimum
                            $LicensedFlash            = ($LicenseInfoDTO | Where-Object {$_.meter -eq "FLASH"} | Measure-Object -Property quantity -Sum).Sum
                            $LicensedFlashExpiryDate  = ($LicenseInfoDTO | Where-Object {$_.meter -eq "FLASH"} | Measure-Object -Property expiryDate -Minimum).Minimum
                            $LicensedCategory         = $LicenseInfoDTO.category | Get-Unique
                        } else {
                            $LicensedCores            = ($LicenseInfoDTO | Where-Object {$_.meter -eq "CORES" -and $_.name -eq "Pro"} | Measure-Object -Property quantity -Sum).Sum
                            $LicensedCoresExpiryDate  = ($LicenseInfoDTO | Where-Object {$_.meter -eq "CORES" -and $_.name -eq "Pro"} | Measure-Object -Property expiryDate -Minimum).Minimum
                            $LicensedFlash            = ($LicenseInfoDTO | Where-Object {$_.meter -eq "FLASH" -and $_.name -eq "Pro"} | Measure-Object -Property quantity -Sum).Sum
                            $LicensedFlashExpiryDate  = ($LicenseInfoDTO | Where-Object {$_.meter -eq "FLASH" -and $_.name -eq "Pro"} | Measure-Object -Property expiryDate -Minimum).Minimum
                            $LicensedCategory         = $LicenseInfoDTO.category | Sort-Object | Get-Unique
                        }
                        # create license object
                        $License = [PSCustomObject]@{
                            'Prism'          =      $($_.name).ToUpper()
                            'Category'       =      $LicensedCategory
                            'Licensed_Cores' = "" + $LicensedCores
                            'CoreExpDate'    = "" + $LicensedCoresExpiryDate
                            'Licensed_Flash' = "" + $LicensedFlash + " Tib"
                            'FlashExpDate'   = "" + $LicensedFlashExpiryDate
                        }
                        Write-Host $License
                    }else {
                        $License = [PSCustomObject]@{
                            'Prism'          = $($_.name).ToUpper()
                            'Category'       = $PrismCall.licenseInfoDTO.pcDetailsDTO.clusters.licenseDetails.category
                            'Licensed_Cores' = "Can not get license information"
                            'Licensed_Flash' = "Can not get license information"
                        }
                    }
                    $Licenses += $License
                }
            }
        } else {
            if ($LicenseCall = Rest-Prism-v1-Get-License -Fqdn ($Prism + '.ikea.com') -Auth $Auth) {
                if ($LicenseCall.licenseInfoDTO.pcDetailsDTO.licenses) {
                    # Nutanix has changed license information to licenseInfoDTO
                    $LicenseInfoDTO = $LicenseCall.licenseInfoDTO.pcDetailsDTO.licenses
                    $Categories = $LicenseInfoDTO.category | Sort-Object | Get-Unique
                    if($Categories -notcontains "PRO"){
                        $LicensedCores            = ($LicenseInfoDTO | Where-Object {$_.meter -eq "CORES"} | Measure-Object -Property quantity -Sum).Sum
                        $LicensedCoresExpiryDate  = ($LicenseInfoDTO | Where-Object {$_.meter -eq "CORES"} | Measure-Object -Property expiryDate -Minimum).Minimum
                        $LicensedFlash            = ($LicenseInfoDTO | Where-Object {$_.meter -eq "FLASH"} | Measure-Object -Property quantity -Sum).Sum
                        $LicensedFlashExpiryDate  = ($LicenseInfoDTO | Where-Object {$_.meter -eq "FLASH"} | Measure-Object -Property expiryDate -Minimum).Minimum
                        $LicensedCategory         = $LicenseInfoDTO.category | Get-Unique
                    } else {
                        $LicensedCores            = ($LicenseInfoDTO | Where-Object {$_.meter -eq "CORES" -and $_.name -eq "Pro"} | Measure-Object -Property quantity -Sum).Sum
                        $LicensedCoresExpiryDate  = ($LicenseInfoDTO | Where-Object {$_.meter -eq "CORES" -and $_.name -eq "Pro"} | Measure-Object -Property expiryDate -Minimum).Minimum
                        $LicensedFlash            = ($LicenseInfoDTO | Where-Object {$_.meter -eq "FLASH" -and $_.name -eq "Pro"} | Measure-Object -Property quantity -Sum).Sum
                        $LicensedFlashExpiryDate  = ($LicenseInfoDTO | Where-Object {$_.meter -eq "FLASH" -and $_.name -eq "Pro"} | Measure-Object -Property expiryDate -Minimum).Minimum
                        $LicensedCategory         = $LicenseInfoDTO.category | Sort-Object | Get-Unique
                    }
                    # create license object
                    $License = [PSCustomObject]@{
                        'Prism'          =      $Prism.ToUpper()
                        'Category'       =      $LicensedCategory
                        'Licensed_Cores' = "" + $LicensedCores
                        'CoreExpDate'    = "" + $LicensedCoresExpiryDate
                        'Licensed_Flash' = "" + $LicensedFlash + " Tib"
                        'FlashExpDate'   = "" + $LicensedFlashExpiryDate
                    }
                }else {
                    $License = [PSCustomObject]@{
                        'Prism'          = $Prism.ToUpper()
                        'Category'       = $PrismCall.licenseInfoDTO.pcDetailsDTO.clusters.licenseDetails.category
                        'Licensed_Cores' = "Can not get license information"
                        'Licensed_Flash' = "Can not get license information"
                    }
                }
                $Licenses += $License
            }
        }
    }
    End {
        return $Licenses
    }
}
function Get-NtnxNetwork() {
    <#
    .SYNOPSIS
        Get virtual network for Nutanix cluster.

    .DESCRIPTION
        Get virtual network information for Nutanix cluster.
        Including vSwitch, Uuid, VlanId.

    .PARAMETER Prism
        Prism Element name, same as the cluster name in Vault.

    .PARAMETER Uuid
        Uuid of virtual network.
    
    .NOTES
        Author:         <RAYGU1>
        Creation Date:  <2023-04-17>
        Purpose/Change: Update for new functions
    
    .EXAMPLE
        Get-NtnxNetwork -Prism RETCN856-NXC000
        Get-NtnxNetwork -Prism c9ac6b2d-74c7-42b4-80d7-a444f65ec21b
    #>
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [string]                                                                                                    $Uuid
    )
    Begin {
        $GstAccount = Read-GstAccount
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password
        $Networks   = @()
    }
    Process {
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're now getting subnets from $Prism"
        if ($Uuid) {
            if ($PrismCall  = Rest-Prism-v3-Get-Network -Fqdn ($Prism + '.ikea.com') -Uuid $Uuid -Auth $Auth) {
                $Network = [PSCustomObject]@{
                    'Name'       = $PrismCall.spec.name
                    'Uuid'       = $PrismCall.metadata.uuid
                    'Prism'      = $PrismCall.spec.cluster_reference.name
                    'VSwitch'    = $PrismCall.spec.resources.vswitch_name
                    'VlanId'     = $PrismCall.spec.resources.vlan_id
                    'State'      = $PrismCall.status.state
                    'SubnetType' = $PrismCall.spec.resources.subnet_type
                }
                $Networks += $Network
            }
        }else {
            if ($PrismCall  = Rest-Prism-v3-List-Network -Fqdn ($Prism + '.ikea.com') -Auth $Auth) {
                $PrismCall | ForEach-Object {
                    $Network = [PSCustomObject]@{
                        'Name'    = $_.spec.name
                        'Uuid'    = $_.metadata.uuid
                        'Prism'   = $_.spec.cluster_reference.name
                        'VSwitch' = $_.spec.resources.vswitch_name
                        'VlanId'  = $_.spec.resources.vlan_id
                    }
                    $Networks += $Network
                }
            }
        }
    }
    End {
        return $Networks
    }
}
function Get-NtnxSppInfo() {
    # The parameter Prism only accept Prism Element
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism
    )
    Begin {
        $SPPs = @()

        if ($Prism -like "DS*" -or $Prism -like "MOD*") {
            $PWord = (Get-SecretForWiaB -Prism $Prism -Secret Site_Pe_Admin -Display).Password
            $Auth  = Get-Base64Auth -Username 'admin'  -PWord $PWord
        } else {
            $PWord = (Get-SecretForSiaB -Prism $Prism -Secret Site_Pe_Admin -Display).Password
            $Auth  = Get-Base64Auth -Username 'admin'  -PWord $PWord
        }
    }
    Process {
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're now collecting SPP info from $Prism"
        $Nodes = Get-NtnxNode -Prism $Prism -Auth $Auth

        if ($PrismCall = Rest-Lcm-v4-Get-LcmEntity -Fqdn ($Prism + '.ikea.com') -Auth $Auth) {
            $PrismCall | Where-Object {$_.entityClass -eq "SPP"} | ForEach-Object {
                $Uuid = ($_.locationId).split(":")[1]
                $NodeName = ($Nodes | Where-Object {$_.uuid -eq $Uuid}).Name
                
                #Assemble properties for SPP
                $SPP = [PSCustomObject]@{
                    'Node'           = $NodeName
                    'Uuid'           = $Uuid
                    'Model'          = $_.entityModel
                    'Class'          = $_.entityClass
                    'Version'        = $_.Version
                    'LastUpdateTime' = $_.lastUpdatedTime
                }
                $SPPs += $SPP
            }
        }
    }
    End {
        $SPPs = $SPPs | Sort-Object Node
        return $SPPs
    }
}
function Create-NtnxNetwork() {
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [string]                                                                                                    $Name,
        [string]                                                                                                    $VlanId,
        [string]                                                                                                    $VSwitchName
    )
    Begin {
        $GstAccount = Read-GstAccount
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password
        $PCs        = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
        $Networks   = @()
    }
    Process {
        if ($Prism -in $PCs) {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The Prism Central is not supported by this function"
            return $null
        }
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're creating network $($Name) for $($Prism)"
        if ($PrismCall1 = Rest-Prism-v2-Create-Network -Fqdn ($Prism + ".ikea.com") -Name $Name -VlanId $VlanId -VSwitchName $VSwitchName -Auth $Auth) {
            if ($PrismCall2 = Rest-Prism-v2-Get-Network -Fqdn  ($Prism + ".ikea.com") -Uuid $($PrismCall1.network_uuid) -Auth $Auth) {
                $Network = [PSCustomObject]@{
                    'Name'    = $PrismCall2.name
                    'Uuid'    = $PrismCall2.uuid
                    'Prism'   = $Prism.ToUpper()
                    'VSwtich' = $PrismCall2.vswitch_name
                    'VlanId'  = $PrismCall2.vlan_id
                }
                $Networks += $Network
            }else {
                Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "It's unable to fetch data of network $($Name)"
                return $null
            }
        }else {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "It's unable to create network $($Name)"
            return $null
        }
    }
    End {
        return $Networks
    }
}
function Get-NtnxControlPanel() {
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [string] [Parameter(ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)]                    $Uuid
    )
    Begin {
        $GstAccount    = Read-GstAccount # Load GST account for calling API
        $Auth          = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $PCs           = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
        $ControlPanels = @() # The list for store result set
    }
    Process {
        if ($Prism -in $PCs) {
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're getting control panel from $Prism"
            if ($Uuid) {
                if ($PrismCall = Rest-Prism-v3-Get-App -Fqdn ($Prism + '.ikea.com') -Uuid $Uuid -Auth $Auth) {
                    $ControlPanel = [PSCustomObject]@{
                        'Name'              = $PrismCall.status.name
                        'State'             = $PrismCall.status.state
                        'Uuid'              = $PrismCall.status.uuid
                        'Cluster'           = $PrismCall.metadata.project_reference.name
                        'SiteNameCode'      = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AB_SiteNameCode"}).value
                        'AhvSubnet'         = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AD_SiteSubnetId"}).value
                        'SiteType'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AG_SiteProfile"}).value
                        'RemoteSite'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "ZZZ_REPLTarget"}).value
                        'BackupBandwidth'   = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AH_SiteBackupBandwidth"}).value
                        'DarksiteBandwidth' = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AH_SiteDarkSiteBandwidth"}).value
                        'Timezone'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AJ_SiteTimeZone"}).value
                        'Latitude'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AK_SiteLatitude"}).value
                        'Longtitude'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AK_SiteLongitude"}).value
                        'AosVersion'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AN_SiteAosVersion"}).value
                        'ConfigVersion'     = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "Z_ReleaseCustomer"}).value
                        'CoreFwVersion'     = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "Z_ReleaseFramework"}).value
                    }
                    $ControlPanels += $ControlPanel
                }
            }else {
                if ($PrismCall = Rest-Prism-v3-List-App -Fqdn ($Prism + '.ikea.com') -Filter "name==.*CPL.*;_state!=deleted" -Auth $Auth) {
                    $PrismCall | % {
                        $ControlPanel = [PSCustomObject]@{
                            'Name'     = $_.status.name
                            'State'    = $_.status.state
                            'Uuid'     = $_.status.uuid
                            'Prism'    = $_.metadata.project_reference.name
                            'Versions' = if ($_.status.description) {
                                $_.status.description.Split(".<br>")[0] + ", " + $_.status.description.Split(".<br>")[1]
                            } else {
                                "NA"
                            }
                        }
                        $ControlPanels += $ControlPanel
                    }
                }
            }
        }else {
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're getting control panel for $Prism"
            $Pc = Find-RetailNtxPc -Object $Prism
            if ($Uuid) {
                #if ($PrismCall = Rest-Prism-Get-App -Fqdn ($Pc.Fqdn) -Uuid $Uuid -Auth $Auth) {
                #    $ControlPanel = [PSCustomObject]@{
                #        'Name'              = $PrismCall.status.name
                #        'State'             = $PrismCall.status.state
                #        'Uuid'              = $PrismCall.status.uuid
                #        'Cluster'           = $PrismCall.metadata.project_reference.name
                #        'SiteNameCode'      = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AB_SiteNameCode"}).value
                #        'AhvSubnet'         = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AD_SiteSubnetId"}).value
                #        'SiteType'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AG_SiteProfile"}).value
                #        'RemoteSite'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "ZZZ_REPLTarget"}).value
                #        'BackupBandwidth'   = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AH_SiteBackupBandwidth"}).value
                #        'DarksiteBandwidth' = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AH_SiteDarkSiteBandwidth"}).value
                #        'Timezone'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AJ_SiteTimeZone"}).value
                #        'Latitude'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AK_SiteLatitude"}).value
                #        'Longtitude'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AK_SiteLongitude"}).value
                #        'AosVersion'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AN_SiteAosVersion"}).value
                #    }
                #    $ControlPanels += $ControlPanel
				#}
                if ($ControlPanel = (Get-NtnxControlPanel -Prism ($Pc.Fqdn.Split(".")[0]) -Uuid $Uuid)[0]) {
                    $ControlPanels += $ControlPanel
                }
            }else {
                if ($PrismCall = Rest-Prism-v3-List-App -Fqdn ($Pc.Fqdn) -Filter "name==.*$($Prism.ToUpper()).*CPL.*;_state!=deleted" -Auth $Auth) {
                    #if ($PrismCall = Rest-Prism-v3-Get-App -Fqdn ($Pc.Fqdn) -Uuid ($PrismCall.status.uuid) -Auth $Auth) {
                    #    $ControlPanel = [PSCustomObject]@{
                    #        'Name'              = $PrismCall.status.name
                    #        'State'             = $PrismCall.status.state
                    #        'Uuid'              = $PrismCall.status.uuid
                    #        'Cluster'           = $PrismCall.metadata.project_reference.name
                    #        'SiteNameCode'      = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AB_SiteNameCode"}).value
                    #        'AhvSubnet'         = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AD_SiteSubnetId"}).value
                    #        'SiteType'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AG_SiteProfile"}).value
                    #        'RemoteSite'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "ZZZ_REPLTarget"}).value
                    #        'BackupBandwidth'   = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AH_SiteBackupBandwidth"}).value
                    #        'DarksiteBandwidth' = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AH_SiteDarkSiteBandwidth"}).value
                    #        'Timezone'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AJ_SiteTimeZone"}).value
                    #        'Latitude'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AK_SiteLatitude"}).value
                    #        'Longtitude'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AK_SiteLongitude"}).value
                    #        'AosVersion'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AN_SiteAosVersion"}).value
                    #        'ConfigVersion'     = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "Z_ReleaseCustomer"}).value
                    #        'CoreFwVersion'     = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "Z_ReleaseFramework"}).value
                    #    }
                    #    $ControlPanels += $ControlPanel
                    #}
                    if ($ControlPanel = (Get-NtnxControlPanel -Prism ($Pc.Fqdn.Split(".")[0]) -Uuid ($PrismCall.status.uuid))[0]) {
                        $ControlPanels += $ControlPanel
                    }
                }
            }
        }  
    }
    End {
        return $ControlPanels
    }
}
function Remove-NtnxVmDisk {
    <#
    .SYNOPSIS
    This function will remove scsi disks for Nutanix VM.

    .DESCRIPTION
    There is a requirement for WiaB migration to remove all scsi 
    disks (except scsi.0) of Windows/Linux cluster VM after those 
    disks copied to volume group.

    .PARAMETER Prism
    Mandatory, specify the nutanix cluster name. If you use site 
    name, it will transfer to <site>-NXC000 by default.

    .PARAMETER VmName
    Mandatory, you must specify the vm name that you want to remove 
    scsi disks.

    .PARAMETER VmDisk
    Optional, it can receive a VmDisk object as parameter, and it 
    will compare the VmName for both parameter VmName and 
    VmDisk.Vmname.

    .EXAMPLE
    Remove-NtnxVmDisk -Prism retcn888-nxc000 -VmName retcn888-nt0003

    .EXAMPLE
    Remove-NtnxVmDisk -Prism retcn888-nxc000 -VmName retcn888-nt0003 -VmDisk $VmDisk

    --------------$VmDisk Object--------------
    VmName          VmUuid                               VmDisks
    ------          ------                               -------
    RETCN888-NT0003 0b309e0d-8166-47a2-9313-840a74ecf135 {@{DiskLable=scsi.0; DiskInd...

    .NOTES
    #>
    [CmdletBinding()]

    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [string] [Parameter(Mandatory = $true)]                                                                     $VmName,
        [Object]                                                                                                    $VmDisk
    )

    Begin {
        # Convert to standard Prism object
        $Obj = ConvertTo-StandardPrism -Name $Prism
        $Prism  = $Obj.PrismName
        $Domain = $Obj.Domain
        $Tier   = $Obj.Tier
        $Fqdn   = $Obj.Fqdn

        # Prepare authentication for prism api call, use account from vault
        if($Prism -like "DS*" -or $Prism -like "MOD*") {
            $Account = Get-SecretForWiaB -Prism $Prism -Tier $Tier -Secret Site_Pe_Admin -Display
        } else {
            $Account = Get-SecretForSiaB -Prism $Prism -Tier $Tier -Secret Site_Pe_Admin -Display
        }
        $Auth = Get-Base64Auth -Username $($Account.Username) -PWord $($Account.Password)
    }

    Process {
        # Verify the VmDisk object
        if ($VmDisk) {
            # Compare object and VmName
            if ($VmName -eq $VmDisk.VmName) {
                Write-ConsoleLog -Level DEBUG -FunctionName (Get-FunctionName) -Message "Vm name is same in both parameters"
            } else {
                Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "Vm name is different in parameter VmName and VmDisk, please verify"
                return
            }
        } else {
            # Get VM disk information
            Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Start to query VM disk information from $VmName"
            $VmDisk = Get-NtnxVmDisk -Prism $Prism -Vm $VmName -Tier $Tier
        }

        # Remove scsi disks except scsi.0
        foreach ($Disk in $VmDisk.VmDisks) {
            if ($Disk.DiskLable -ne "scsi.0") {
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Start to remove disk $($Disk.DiskLable)"
                $Result = Rest-Prism-v2-Detach-disk -Fqdn $Fqdn -Auth $Auth `
                                                    -VmName $VmDisk.VmName `
                                                    -VmUuid $VmDisk.VmUuid `
                                                    -Index $Disk.DiskIndex `
                                                    -VmDiskUuid $Disk.DiskUuid `
                                                    -DeviceUuid $Disk.DeviceUuid
            } else {
                Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "Skip disk scsi.0"
            }
        }
    }

    End {
        return $Result
    }
}
function Restart-NtnxNode() {
    param (
        [string] [Parameter(Mandatory = $true)] $Prism,
        [Array]                                 $CvmIps
    )
    $GstAccount  = Read-GstAccount
    $Auth        = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password
    $HashPayload = @{
        ".oid"    = "ClusterManager"
        ".method" = "host_rolling_reboot"
        ".kwargs" = @{
            svm_ips = @($CvmIps)
        }
    }
    return Invoke-Genesis-Api -Fqdn ($Prism + '.ikea.com') `
                              -Method POST `
                              -Auth $Auth `
                              -HashPayload $HashPayload
}
function Get-NtnxTask() {
    param (
        [string] [Parameter(Mandatory = $true)] $Prism,
        [string]                                $Uuid
    )
    $GstAccount = Read-GstAccount
    $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password
    $PrismCall  = Rest-Prism-v2-Get-Task -Fqdn ($Prism + '.ikea.com') -Uuid $Uuid -Auth $auth
    if ($PrismCall) {
        $Task = [PSCustomObject]@{
            'Uuid'          = $PrismCall.uuid
            'OperationType' = $PrismCall.operation_type
            'Status'        = $PrismCall.progress_status
            '%'             = $PrismCall.percentage_complete
        }
        return $Task
    }
    return $null
}
function Set-NtnxVmPowerState() {
    param (
        [string] [Parameter(Mandatory = $true)]                                              $Prism,
        [string] [Parameter(Mandatory = $true)]                                              $Uuid,
        [string] [Parameter(Mandatory = $true)] [ValidateSet("ON", "OFF", `
                                                            "POWERCYCLE", "REST", `
                                                            "PAUSE", "SUSPEND", `
                                                            "RESUME", "SAVE", `
                                                            "ACPI_SHUTDOWN", "ACPI_REBOOT")] $State
    )

    # Transfer input to standard Prism name
    $Obj    = ConvertTo-StandardPrism -Name $Prism
    $Prism  = $Obj.PrismName
    $Domain = $Obj.Domain
    $Tier   = $Obj.Tier
    $Fqdn   = $Obj.Fqdn

    # prepare authentication for prism api call, use account from vault
    if($Prism -like "DS*" -or $Prism -like "MOD*") {
        $Account = Get-SecretForWiaB -Prism $Prism -Tier $Tier -Secret Site_Pe_Admin -Display
    } else {
        $Account = Get-SecretForSiaB -Prism $Prism -Tier $Tier -Secret Site_Pe_Admin -Display
    }
    $Auth = Get-Base64Auth -Username $($Account.Username) -PWord $($Account.Password)

    $PrismCall  = Rest-Prism-v2-Set-VM-PowerState -Fqdn $Fqdn -Uuid $Uuid -State $State -Auth $Auth
    return $PrismCall
}
function Get-NtnxProtectionDomain() {
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism
    )
    Begin {
        $GstAccount        = Read-GstAccount # Load GST account for calling API
        $Auth              = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $PCs               = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
        $ProtectionDomains = @()
    }
    Process {
        if ($Prism -in $PCs) {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The Prism Central is not supported by this function"
            return $null
        }
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're getting protection domains from $Prism"
        $PrismCall  = Rest-Prism-v1-List-ProtectionDomain -Fqdn ($Prism + '.ikea.com') -Auth $Auth
        $PrismCall1 = Rest-Prism-v1-List-RsDrSnapshot -Fqdn ($Prism + '.ikea.com') -Auth $Auth
        $PrismCall2 = Rest-Prism-v1-List-UnProtectedVm -Fqdn ($Prism + '.ikea.com') -Auth $Auth
        if ($PrismCall = $PrismCall | Where-object {$_.name -match $Prism}) {
            $ProtectionDomain = [PSCustomObject]@{
                'Name'          = $PrismCall.name
                'Prism'         = $Prism
                'RemoteSite'    = $PrismCall.remoteSiteNames
                'RemoteBackups' = ($PrismCall1.entities | Where-Object {$_.protectionDomainName -match $Prism}).count
                'Protected'     = $PrismCall.vms | Select-Object vmName, vmId
                'Unprotected'   = $PrismCall2 | Select-Object vmName, uuid
            }
            $ProtectionDomains += $ProtectionDomain
        }
    }
    End {
        return $ProtectionDomains
    }
}
function Get-NtnxReplicationChain() {
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism
    )
    Begin {
        $GstAccount        = Read-GstAccount # Load GST account for calling API
        $Auth              = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $PCs               = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
        $ReplicationChains = @()
    }
    Process {
        if ($Prism -in $PCs) {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The Prism Central is not supported by this function"
            return $null
        }
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're getting protection domains from $Prism"
        if ($PrismCall = Rest-Prism-v1-List-ProtectionDomain -Fqdn ($Prism + '.ikea.com') -Auth $Auth) {
            $Chain = [PSCustomObject]@{
                'Prism'    = $Prism
                'Outgoing' = $null
                'RemoteSite' = $null
                'Incoming' = $null
            }
            $Outgoing = $PrismCall | Where-Object {$_.name -match $Prism}
            $Incoming = $PrismCall | Where-Object {$_.name -notmatch $Prism}
            $Chain.Outgoing    = $Outgoing.name
            $Chain.RemoteSite  = if($Outgoing.remoteSiteNames[0]){$Outgoing.remoteSiteNames[0].Split("RS_")[1]}
            #$Chain.RemoteSite  = if($Outgoing.remoteSiteNames[0]){$Outgoing.remoteSiteNames[0]}
            $Chain.Incoming    = $Incoming.name
            $ReplicationChains += $Chain
        }
    }
    End {
        return $ReplicationChains
    }
}
function Get-NtnxRemoteSite() {
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [string]                                                                                                    $RemoteSite
    )
    $GstAccount = Read-GstAccount # Load GST account for calling API
    $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
    Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're getting remote site from $Prism"
    if ($PrismCall = Rest-Prism-v1-Get-RemoteSite -Fqdn ($Prism + '.ikea.com') -RsName $RemoteSite -Auth $Auth) {
        return $PrismCall
    }
    return $null
}
function Add-NtnxRemoteSite() {
    param (
        [string] [Parameter(Mandatory = $true)]               $Prism,
        [string]                                              $RemotePrism,
        [bool]                                                $ProxyEnabled = $false,
        [string] [ValidateSet("BACKUP", "DISASTER_RECOVERY")] $Capability = "BACKUP",
        [string]                                              $TargetIp,
        [int]                                                 $TargetPort = 2020,
        [bool]                                                $SshEnabled  = $false
    )
    Begin {
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
    }
    Process {
        $RsObj = [PSCustomObject]@{
            'name' = "RS_$($RemotePrism)"
            'remoteIpPorts' = @{
                "$TargetIp" = $TargetPort
            }
            'proxyEnabled' = $ProxyEnabled
            'compressionEnabled' = $CompressionEnabled
            'sshEnabled' = $SshEnabled
            'capabilities' = @("$Capability")
        }
    }
    End {
        return Rest-Prism-v1-Add-RemoteSite -Fqdn ($Prism + '.ikea.com') -RsObj $RsObj -Auth $Auth
    }
}
function Update-NtnxRemoteSite() {
    param(
        [string] [Parameter(Mandatory = $true)] $Prism,
        [object]                                $RsObj,
        [bool]                                  $IsUpdateBandwidthPolicy = $true,
        [bool]                                  $BandwidthPolicyEnabled = $true,
        [Int64]                                 $BwPolStartTime = ***********,   # 08:00 AM
        [Int64]                                 $BwPolEndTime = 82740000000,     # 10:59 PM
        [float]                                 $DefaultBandwidth = 8,
        [float]                                 $BandwidthLimit = 1.25,
        [Int64]                                 $DaysSelected = 127,             # Sun: 2^0, Mon: 2^1, Tue: 2^2, Wed: 2^3, Thu: 2^4, Fri: 2^5, Sat: 2^6
        [bool]                                  $IsUpdateCompressionEnable = $true,
        [bool]                                  $CompressionEnabled = $true,
        [bool]                                  $IsUpdateVStoreNameMap = $true,
        [string]                                $SrcNetworkName,
        [string]                                $DestNetworkName
    )
    Begin {
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
    }
    Process {
        if ($RsObj.psobject.members.name -contains "stats") {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Removing stats"
            $RsObj.psobject.members.Remove("stats")
        }
        if ($RsObj.replicationLinks.count -ge 1) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Removing replication link stats"
            $RsObj.replicationLinks | ForEach-Object {
                $_.psobject.members.Remove("stats")
            }
        }
        if ($IsUpdateBandwidthPolicy) {
            $RsObj.bandwidthPolicyEnabled = $BandwidthPolicyEnabled
            $RsObj.psobject.members.Remove("bandwidthPolicy")
            if ($BandwidthPolicyEnabled) {
                $BwPolObj = @{
                    'policyName'              = "$($RsObj.name)_policy"
                    'bandwidthConfigurations' = $null
                    'defaultBandwidthLimit'   = [int]($DefaultBandwidth * 1000000)
                }
                if ($BwPolEndTime -le $BwPolStartTime) {
                    $BwPolObj.bandwidthConfigurations = @(
                        @{
                            'startTime'      = $BwPolStartTime
                            'endTime'        = 86340000000
                            'daysSelected'   = $DaysSelected
                            'bandwidthLimit' = [int]($BandwidthLimit * 1000000)
                        }
                    )
                    if ($BwPolEndTime -ne 0) {
                        $BwPolObj.bandwidthConfigurations += @{
                            'startTime'      = 0
                            'endTime'        = $BwPolEndTime
                            'daysSelected'   = $DaysSelected
                            'bandwidthLimit' = [int]($BandwidthLimit * 1000000)
                        }
                    }
                } else {
                    $BwPolObj.bandwidthConfigurations = @(
                        @{
                            'startTime'      = $BwPolStartTime
                            'endTime'        = $BwPolEndTime
                            'daysSelected'   = $DaysSelected
                            'bandwidthLimit' = [int]($BandwidthLimit * 1000000)
                        }
                    )
                }
                $RsObj | Add-Member -MemberType NoteProperty -Name "bandwidthPolicy" -Value $BwPolObj
            }
        }
        if ($IsUpdateCompressionEnable) {
            $RsObj.compressionEnabled = $CompressionEnabled
        }
        if ($IsUpdateVStoreNameMap) {
            $VStoreNameMap = @{
                'SelfServiceContainer' = "SelfServiceContainer"
           }
           $RsObj.vstoreNameMap = $VStoreNameMap
        }
        if ($SrcNetworkName -and $DestNetworkName) {
            $NwMapHash = @{
                'srcHypervisorType'  = "kKvm"
                'srcNetworkName'     = $SrcNetworkName
                'destHypervisorType' = 'kKvm'
                'destNetworkName'    = $DestNetworkName
            }
            [Array]$l2NetworkMappings += $NwMapHash
            $NetworkMapping = @{
                'uuid' = $null
                'l2NetworkMappings' = $l2NetworkMappings
            }
            $RsObj.networkMapping = $NetworkMapping
        }
    }
    End {
        return Rest-Prism-v1-Update-RemoteSite -Fqdn ($Prism + '.ikea.com') -RsObj $RsObj -Auth $Auth
    }

}
function Delete-NtnxRemoteSite() {
    param (
        [string] [Parameter(Mandatory = $true)] $Prism,
        [string] [Parameter(Mandatory = $true)] $RsName
    )
    Begin {
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
    }
    Process {
        $Hide = Rest-Prism-v1-Delete-RemoteSite -Fqdn ($Prism + '.ikea.com') -RsName $RsName -Auth $Auth
    }
    End {
        return $Hide
    }
}
function Add-NtnxVmToProtectionDomain() {
    param (
        [string] [Parameter(Mandatory = $true)] $Prism,
        [string] [Parameter(Mandatory = $true)] $PdName,
        [array]                                 $VmIds
    )
    Begin {
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $PCs        = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
    }
    Process {
        if ($Prism -in $PCs) {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The Prism Central is not supported by this function"
            return $null
        }
        if ($PrismCall = Rest-Prism-v1-Add-VmToProtectionDomain -Fqdn ($Prism + '.ikea.com') -PdName $PdName -VmIds $VmIds -Auth $Auth) {
            $ProtectionDomain = [PSCustomObject]@{
                'Name'        = $PrismCall.name
                'Prism'       = $Prism
                'RemoteSite'  = $PrismCall.remoteSiteNames
                'Protected'   = $PrismCall.vms | Select-Object vmName, vmId
            }
        }
    }
    End {
        return $ProtectionDomain
    }
}
function Remove-NtnxVmFromProtectionDomain() {
    param (
        [string] [Parameter(Mandatory = $true)] $Prism,
        [string] [Parameter(Mandatory = $true)] $PdName,
        [array]                                 $VmIds
    )
    Begin {
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $PCs        = $(Read-Var).Infrastructure.Retail.Nutanix.PrismCentral.Name # Load a list of existing Prism Central
    }
    Process {
        if ($Prism -in $PCs) {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The Prism Central is not supported by this function"
            return $null
        }
        if ($PrismCall = Rest-Prism-v1-Remove-VmFromProtectionDomain -Fqdn ($Prism + '.ikea.com') -PdName $PdName -VmIds $VmIds -Auth $Auth) {
            $ProtectionDomain = [PSCustomObject]@{
                'Name'        = $PrismCall.name
                'Prism'       = $Prism
                'RemoteSite'  = $PrismCall.remoteSiteNames
                'Protected'   = $PrismCall.vms | Select-Object vmName, vmId
            }
        }
    }
    End {
        return $ProtectionDomain
    }
}
function Get-NtnxClusterAverageMetric() {
    param (
        [string] [Parameter(Mandatory = $true)] $Prism,
        [string]                                $Uuid,
        [int64]                                 $Days
    )
    $GstAccount = Read-GstAccount
    $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password
    [int64]$T0  = Get-date -AsUTC -UFormat %s
    $Body       = [PSCustomObject]@{
        'entity_type'             = "cluster"
        'entity_ids'              = @($Uuid)
        'group_member_attributes' = @(
            @{
                attribute = "hypervisor_cpu_usage_ppm"
                operation = "AVG"
            }
            @{
                attribute = "aggregate_hypervisor_memory_usage_ppm"
                operation = "AVG"
            }
            @{
                attribute = "storage.usage_bytes"
                operation = "AVG"
            }
        )
        'interval_start_ms'     = $T0*1000 - $Days*24*60*60*1000
        'interval_end_ms'       = $T0*1000
        'downsampling_interval' = 300
        'query_name'            = "prism:CPStatsModel"
    }
    return Rest-Prism-v3-Get-Groups -Fqdn ($Prism + '.ikea.com') -Body $Body -Auth $Auth
}
function Get-NtnxClusterStorageMetric() {
    param (
        [string] [Parameter(Mandatory = $true)] $Prism
    )
    $GstAccount = Read-GstAccount
    $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password
    $Body       = [PSCustomObject]@{
        'entity_type'             = "storage_container"
        'filter_criteria'         = "container_name==.*[s|S][e|E][l|L][f|F][s|S][e|E][r|R][v|V][i|I][c|C][e|E].*"
        'group_member_attributes' = @(
            @{
                attribute = "container_name"
            }
            @{
                attribute = "storage.user_usage_bytes"
            }
            @{
                attribute = "storage.user_capacity_bytes"
            }
            @{
                attribute = "cluster_name"
            }
            @{
                attribute = "cluster"
            }
        )
    }
    return Rest-Prism-v3-Get-Groups -Fqdn ($Prism + '.ikea.com') -Body $Body -Auth $Auth
}
function Get-NtnxImage() {
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [string]                                                                                                    $Uuid
    )
    Begin {
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
        $Images     = @() # The list for store result set
    }
    Process{
        if ($Uuid) {
            if ($PrismCall = Rest-Prism-v3-Get-Image -Fqdn $($Prism + '.ikea.com') -Uuid $Uuid -Auth $Auth) {
                $Image = [PSCustomObject]@{
                    'Prism' = $Prism
                    'Name'  = $PrismCall.spec.name
                    'Uuid'  = $PrismCall.metadata.uuid
                    'Type'  = $PrismCall.spec.resources.image_type
                    'Arch'  = $PrismCall.spec.resources.architecture
                    'State' = $PrismCall.status.state
                }
                $Images += $Image
            }
        }else {
            if ($PrismCall = Rest-Prism-v3-List-Image -Fqdn $($Prism + '.ikea.com') -Auth $Auth) {
                $PrismCall | ForEach-Object {
                    $Image = [PSCustomObject]@{
                        'Prism' = $Prism
                        'Name'  = $_.spec.name
                        'Uuid'  = $_.metadata.uuid
                        'Type'  = $_.spec.resources.image_type
                        'Arch'  = $_.spec.resources.architecture
                        'State' = $_.status.state
                    }
                    $Images += $Image
                }
            }
        }
    }
    End{
        return $Images
    }
}
function Delete-NtnxImage() {
    param (
        [string] [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)] $Prism,
        [string] [Parameter(Mandatory = $true)]                                                                     $Uuid
    )
    Begin{
        $GstAccount = Read-GstAccount # Load GST account for calling API
        $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password # Generate the Base64 formatted authentication string
    }
    Process{
        if ($PrismCall = Rest-Prism-v3-Delete-Image -Fqdn $($Prism + '.ikea.com') -Uuid $Uuid -Auth $auth) {
            $Deletion = [PSCustomObject]@{
                'State' = $PrismCall.status.state
                'TaskUuid' = $PrismCall.status.execution_context[0].task_uuid
            }
        }
    }
    End{
        return $Deletion
    }
}
function Get-NtnxNic() {
    param (
        [string] [Parameter(Mandatory = $true)] $Prism
    )
    $GstAccount = Read-GstAccount
    $Auth       = Get-Base64Auth -Username $GstAccount.username -PWord $GstAccount.password
    $Body       = [PSCustomObject]@{
        'entity_type'             = "host_nic"
        'query_name'              = "prism:EBQueryModel"
        'group_member_attributes' = @(
            @{
                attribute = "id"
            }
            @{
                attribute = "node_uuid"
            }
            @{
                attribute = "mtu_bytes"
            }
            @{
                attribute = "link_capacity"
            }
            @{
                attribute = "mac_address"
            }
            @{
                attribute = "port_name"
            }
            @{
                attribute = "discovery_protocol"
            }
            @{
                attribute = "switch_name"
            }
            @{
                attribute = "switch_port_name"
            }
            @{
                attribute = "switch_mgmt_ip_address"
            }
            @{
                attribute = "switch_port_native_vlan"
            }
            @{
                attribute = "link_detected"
            }
        )
    }
    return Rest-Prism-v3-Get-Groups -Fqdn ($Prism + '.ikea.com') -Body $Body -Auth $Auth
}
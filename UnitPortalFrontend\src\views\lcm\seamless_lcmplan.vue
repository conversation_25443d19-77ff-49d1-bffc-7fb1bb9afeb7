<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="5" >
        <el-col :span="5" style='float:left;margin-top: 15px;'>
          <el-tooltip content="Click to open AOS LCM page" placement="top" effect="light">
            <router-link :to="prodradio == 1 ? '/lcm/siabaos' : '/lcm/wiabaos'" class="btn"> <span class="link-type el-icon-d-arrow-left"/> {{ prodradio==1 == 1 ? 'Siab AOS LCM' : 'Wiab AOS LCM' }}</router-link>
          </el-tooltip>
        </el-col>
        <el-col :span="3" :offset="4" style="margin-top: 15px;">
          <!-- <el-switch style="margin: 0 6px 0 6px;float:right" v-model="filter.prodswitch" active-text="Wiab" 
            inactive-text="Siab" active-color="#13ce66" >
          </el-switch> -->
          <el-radio v-model="prodradio" label="1">Siab</el-radio>
          <el-radio v-model="prodradio" label="2">Wiab</el-radio>
        </el-col>
        <el-col :span="4"   >
          <el-select size="large"
            v-model="filter.selected_pc" multiple collapse-tags placeholder="Filter the PC" style="width:100%;" >
            <!-- <el-option v-for="item in filter.pc_list" :key="item" :label="item" :value="item" style="font-size: large;"/> -->
            <el-option v-for="item in prismoptions" :key="item.key" :label="item.key" :value="item.display_name" />
          </el-select>
        </el-col>
        <el-col :span="4" >
          <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large" @keyup.enter.native="filter_pe_list" />
        </el-col>
        <el-col :span="2" >
          <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="filter_pe_list">
            Search
          </el-button>
        </el-col>
        <el-col :span="2" >
          <el-tooltip :content="!ifpriv? 'No access for creating plans':'Create a plan'" placement="top" effect="light">
            <el-button :disabled="!ifpriv" style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="openCreatePlanDialog">
              Make a plan
            </el-button>
          </el-tooltip>
        </el-col>
      </el-row>
    </div>
    <el-table :data="prodradio == 1 ? current_list : []" style="width: 100%" :key="tableKey" v-loading="listLoading"  fit highlight-current-row @sort-change="sortChange">
      <el-table-column type="expand" >
        <template slot-scope="props">
          <el-table :data="props.row.pes" style="width: 100%" >
            <el-table-column prop="id" label="ID" class-name="status-col" min-width="3%" align="center" sortable="custom" show-overflow-tooltip></el-table-column>
            <el-table-column prop="country" label="Country" class-name="status-col" min-width="4%" align="center" sortable="custom" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span v-html="highlightMatch(row.country)"></span>
              </template>
            </el-table-column>
            <el-table-column prop="pc" label="PC" class-name="status-col" min-width="8%" align="center" sortable="custom" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span v-html="highlightMatch(row.pc)"></span>
              </template>
            </el-table-column>
            <el-table-column prop="pe" label="PE" class-name="status-col" min-width="11%" align="center" sortable="custom">
              <template slot-scope="{ row }">
                <span v-html="highlightMatch(row.pe)"></span>
              </template>
            </el-table-column>
            <el-table-column prop="planned_date" label="Planned Date(UTC)" class-name="status-col" min-width="8%" align="center" sortable="custom" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <span v-html="highlightMatch(row.planned_date)"></span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="Status" class-name="status-col" min-width="6%" align="center" sortable="custom" show-overflow-tooltip>
              <template slot-scope="{row}">
                <el-tag :type="statusFilter(row.status)" size="medium">{{ row.status ? row.status : 'Unknown' }}</el-tag>
                <!-- <el-tag :type="row.status | statusFilter">
                  {{ row.status }}
                </el-tag> -->
              </template>
            </el-table-column>
            <el-table-column label="Operations"  min-width="10px" align="center" sortable="custom">
              <template slot-scope="{row}">
                <el-dropdown split-button type="primary" >
                  Actions
                  <el-dropdown-menu slot="dropdown" placement="right" >
                    <el-dropdown-item :disabled="!ifpriv || row.status != 'Planned'" @click.native="handle_edit_slcmppe(row)">Edit PE </el-dropdown-item>
                    <el-dropdown-item :disabled="!ifpriv || row.status != 'Planned'" @click.native="cancel_slcmppe(row)">Cancel PE</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>

      <el-table-column label="ID" class-name="status-col" min-width="2%" align="center" sortable="custom" prop="id" show-overflow-tooltip></el-table-column>
      <el-table-column label="Creater" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="creater" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span v-html="highlightMatch(row.creater)"></span>
        </template>
      </el-table-column>
      <el-table-column label="Create Date" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="create_date" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span v-html="highlightMatch(row.create_date)"></span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="Daily Report" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="daily_report" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span>{{ row.daily_report|dailyre_filter }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="Interval" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="interval" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span v-html="highlightMatch(row.interval)"></span>
        </template>
      </el-table-column>
      <el-table-column label="Max Count" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="max_count" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span v-html="highlightMatch(row.max_count)"></span>
        </template>
      </el-table-column>
      <el-table-column label="Desired Date (UTC)" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="desired_plan_date" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span v-html="highlightMatch(row.desired_plan_date)"></span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="Info"
        class-name="info-col"
        min-width="4%"
        align="center">
        <template >
          <i class="fas fa-info-circle"></i> 
          <i class="fas fa-check-circle" style="color: yellow;"></i> 
          <i class="fas fa-times-circle"></i>
        </template>
        </el-table-column> -->
      <el-table-column label="Status" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="status" show-overflow-tooltip>
        <template slot-scope="{row}">
          <el-popover
            ref="popover4"
            placement="left"
            width="300"
            content="Click for detail"
            title="Plan Details"
            trigger="click">
            <div>
              <span style="float: right;">Total PE: {{ row.pes.length }}</span>
            </div>
            <el-table :data="row.pes|computedstatus" :row-class-name="statusRowClass" style="width: 100%">
              <el-table-column  label="Status" prop="status"></el-table-column>
              <el-table-column  label="Num." prop="count" ></el-table-column>
              <!-- <el-table-column width="300" property="id" label="address"></el-table-column> -->
            </el-table>
            <div slot="reference" class="name-wrapper">
              <el-tooltip content="Click for detail" placement="top">
                <el-tag :type="row.status && row.status.toLowerCase() === 'completed' && row.pes && row.pes.some(pe => pe.status && pe.status.toLowerCase() === 'error')
                  ? 'danger' 
                  : statusFilter(row.status)" size="medium">{{ row.status ? row.status : 'Unknown' }}</el-tag>
              </el-tooltip>
            </div>
          </el-popover>
          <!-- <el-tag :type="row.status | statusFilter">
            {{ row.status }}
          </el-tag> -->
        </template>
      </el-table-column>
      <!-- <el-table-column label="Interval" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="interval" show-overflow-tooltip></el-table-column>
      <el-table-column label="Max Count" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="max_count" show-overflow-tooltip></el-table-column>
      <el-table-column label="Exc_Sequence" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="execution_sequence" show-overflow-tooltip></el-table-column>
      <el-table-column label="Desired Date (UTC)" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="desired_plan_date" show-overflow-tooltip></el-table-column>
      <el-table-column label="Status" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="status" show-overflow-tooltip></el-table-column> -->
      <el-table-column label="Operations"  min-width="10px" align="center" sortable="custom">
        <template slot-scope="{row}">
          <el-dropdown split-button type="primary" >
            Actions
            <el-dropdown-menu slot="dropdown" placement="right" >
              <!-- <el-dropdown-item :disabled="row.pe_a | ifaction_filter" @click.native="handle_move_development(row)">Delete Plan</el-dropdown-item> -->
              <el-dropdown-item :disabled="!ifpriv||row.status == 'Completed'||row.status == 'Canceled'" @click.native="handle_editplan(row)">Edit Plan </el-dropdown-item>
              <el-dropdown-item :disabled="!ifpriv||row.status == 'Completed'||row.status == 'Canceled'" @click.native="cancel_slcmplan(row)">Cancel Plan</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" />

    <el-dialog :visible.sync="dialogEPVisible" :title="'Edit Plan'" width="800px" :close-on-click-modal="false">
      <el-form ref="editform_plan" :rules="rules" :model="editform_plan" label-position="left" label-width="90px" style="width: 600px; margin-left:50px;padding-top: 2%;">
        <el-form-item label="Creater" prop="creater" >
          <el-input v-model="editform_plan.creater" style="width:60%" readonly ></el-input>
        </el-form-item>
        <el-form-item label="Date(UTC)" prop="timestamp" >
          <el-date-picker v-model="editform_plan.desireddate" type="date" placeholder="Please pick a date" :picker-options="pickerOptions1" style="width:60%"/>
        </el-form-item>
        <el-row :gutter="20">
          <!-- <el-col :span="8">
            <el-form-item label="Exc Seq." prop="execution_sequence">
              <el-select v-model="editform_plan.execution_sequence" placeholder="Exc Seq.">
                <el-option label="PC" value="pc"></el-option>
                <el-option label="PE" value="pe"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <!-- <el-form-item label="Max Cnt." prop="maxcount">
              <el-select v-model="editform_plan.maxcount" placeholder="Max cnt.">
                <el-option v-for="q in selqueue" :key="q.value" :label="q.toString()" :value="q"></el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="Max Cnt." prop="maxcount">
            <el-input-number v-model="editform_plan.maxcount" :min="10" :max="20" style="width:150px;"/>
          </el-form-item>
          </el-col>
          <el-col :span="8" :offset="4">
            <!-- <el-form-item label="Interval" prop="interval">
              <el-select v-model="editform_plan.interval" placeholder="Interval">
                <el-option v-for="q in selqueue" :key="q.value" :label="q.toString()" :value="q"></el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="Interval" prop="interval">
            <el-input-number v-model="editform_plan.interval" :min="10" :max="20" style="width:150px;"/>
          </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="Cluster List" prop="clusterlist">
          <div class="tags-container" @click="handleEditPEClick(isCreatePlan=false)" style="cursor: pointer;">
            <el-tag
              v-for="(tag, index) in editform_plan.clusterlist"
              :key="index"
              :closable="getTagClosable(tag)"
              @close="handleRemoveTag(index)"
              style="margin-left: 6px;"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" @click="dialogEPVisible = false">Cancel</el-button>
        <el-button type="primary" @click="() => { dialogEPVisible = false; edit_slcmplan() }">Confirm</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dialogECVisible" :title="'Edit Cluster'" width="600px" :close-on-click-modal="false">
      <el-form ref="editform_pe" :model="editform_pe" label-width="120px">
        <el-form-item label="Prism Central">
          <el-input :value="editform_pe.pc.toUpperCase()" style="width:60%" readonly></el-input>
        </el-form-item>
        <el-form-item label="Prism Element">
            <el-input v-model="editform_pe.pe" style="width:60%" readonly></el-input>
        </el-form-item>
        <el-form-item label="Country">
          <el-input v-model="editform_pe.country" style="width:30%" readonly></el-input>
        </el-form-item>
        <el-form-item label="Date(UTC)" prop="timestamp" >
          <el-date-picker v-model="editform_pe.planneddate" type="datetime" placeholder="Please pick a date" :picker-options="pickerOptions1"  style="width:60%"/>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button type="warning" @click="dialogECVisible = false">Cancel</el-button>
        <el-button type="primary" @click="() => { dialogECVisible = false; edit_slcmppe()}">Confirm</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dialogSelectClustersVisible" :title="'Select Clusters'"  width="50%" @close="resetcls" :close-on-click-modal="false">
      <div class="container" style="width: 100%;">
        <div class="clslist" style="width: 35%;">
          <el-checkbox :indeterminate="cls.pc.isIndeterminate" v-model="cls.pc.checkAll" @change="handleCheckAllChange($event,'pc')">
              <h3>Prism Central</h3>
          </el-checkbox>
          <el-checkbox-group v-model="cls.pc.selectedlist" @change="handleCheckedChange('pc')">
            <el-checkbox  v-for="(item,index) in cls.pc.datalist" :label="item.value" :key="index" :indeterminate="item.isIndeterminate" v-model="item.checkAll" >{{item.value}}</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="clslist_country">
          <el-checkbox :indeterminate="cls.country.isIndeterminate" v-model="cls.country.checkAll" @change="handleCheckAllChange($event,'country')">
              <h3>Country</h3>
          </el-checkbox>
          <el-checkbox-group v-model="cls.country.selectedlist" @change="handleCheckedChange('country')">
            <el-checkbox style="display: flex; width: 100px; height: 25px;" v-for="(item,index) in cls.country.datalist" :label="item.value" :key="index" :indeterminate="item.isIndeterminate" v-model="item.checkAll">{{item.value}}</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="clslist" style="width: 40%;">
          <el-checkbox :indeterminate="cls.pe.isIndeterminate" v-model="cls.pe.checkAll" @change="handleCheckAllChange($event,'pe')">
            <h3 style="display: inline-block; margin-right: 10px;">Prism Element</h3>
            <el-button v-popover:pop_pefilter size="small" style="padding: 0; border: none; background-color: transparent;">
              <i class="el-icon-s-order" style="font-size: 24px; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;"></i>
            </el-button>
            <el-popover
              trigger="click"
              placement="bottom"
              v-model="popoverVisible"
              ref="pop_pefilter">
              <div>
                <el-input
                  placeholder="input PE name"
                  v-model="searchQuery"
                  @input="filterPeList" 
                  clearable>
                </el-input>
                <div style="margin: 10px 0;"></div>
                <el-checkbox-group v-model="selectedAosVersions" @change="filterPeList">
                  <el-checkbox
                    v-for="(version, index) in aosVersions"
                    :label="version"
                    :key="index">
                    {{ version }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </el-popover>
          </el-checkbox>
          <el-checkbox-group v-model="cls.pe.selectedlist" @change="handleCheckedChange('pe')">
              <el-checkbox v-for="(item, index) in cls.pe.currentlist" :label="item.value" :key="index">{{ item.value }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAddTag(isCreatePlan)">Confirm</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dialogMakeAPlanVisible" :title="'Make a plan'" width="50%" @close="resetMakePlanForm" :close-on-click-modal="false">
      <div class="container" style="width: 100%;">
        <el-form ref="makePlanForm" :rules="makePlanRules" :model="makePlanForm" label-width="280px" style="width: auto;">
          <el-alert type="info" show-icon :closable="false" style="margin-bottom: 30px;">
            <p>You are about to create an LCM scheduler for upgrading AOS to {{ siab_aos_targetversion }}, and AHV to {{ siab_ahv_targetversion }}</p>    <!--TODO: get version from backend?-->
          </el-alert>
          <el-form-item label="Select clusters" prop="clusterlist">
            <div class="tags-container" @click="handleEditPEClick(isCreatePlan=true)" style="cursor: pointer; width:auto">
              <el-tag
                v-for="(tag, index) in makePlanForm.clusterlist"
                :key="index"
                closable
                @close="handleRemoveTag(index)"
                style="margin-left: 6px;"
              >
                {{ tag }}
              </el-tag>
            </div>
          </el-form-item>
          <el-form-item label="The LCM activity will start from (UTC)" prop="desiredPlanDate" :label-style="{ whiteSpace: 'nowrap' }">
            <el-date-picker v-model="makePlanForm.desiredPlanDate" type="date" placeholder="Please pick a date" :picker-options="pickerOptions1"/>
          </el-form-item>
          <el-form-item label="The scheduler will upgrade" prop="maxcount">
            <el-input-number v-model="makePlanForm.maxcount" :min="10" :max="20" style="width:150px;"/>
            <span style="font-weight: bold;"> clusters per pc per day</span>
          </el-form-item>
          <el-form-item label="The LCM will be performed once every" prop="interval">
            <el-input-number v-model="makePlanForm.interval" :min="10" :max="50" style="width:150px;"/>
            <span style="font-weight: bold;"> minutes in sequence according to the cluster during a specific time window (local time 11PM - 4AM)</span>
          </el-form-item>
          <!-- <el-form-item label="Execution sequence" prop="execution_sequence">
            <el-select v-model="makePlanForm.execution_sequence" placeholder="Please select execution sequence" prop="execution_sequence">
                  <el-option label="PC" value="PC"></el-option>
                  <el-option label="PE" value="PE"></el-option>
                  <el-option label="country" value="COUNTRY"></el-option>
            </el-select>
          </el-form-item> -->
          <!-- <el-form-item label="I need daily report" prop="isDailyReportRequired">
            <el-switch v-model="makePlanForm.isDailyReportRequired"></el-switch>
          </el-form-item> -->
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" @click="dialogMakeAPlanVisible = false">Cancel</el-button>
        <el-button type="primary" @click="createSlcmPlan">Confirm</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { CreateSLCMPlans, UpdateSLCMPlans, CancelSLCMPlan, GetSLCMPlans, CancelSLCMPEs, UpdateSLCMPEs, GetTargetVersion} from  '@/api/automation'
import { GetPCPECorrespondence_Lcm, GetPrismList} from '@/api/nutanix'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  name: 'SLCMTable',
  components: { Pagination },
  directives: { waves },
  filters: {
    // statusFilter(status) {
    //   let st = status ? status.toLowerCase() : 'unknown';
    //   const statusMap = {
    //     'planned': 'primary',
    //     'canceled': 'info',
    //     'completed': 'success',
    //     'done': 'success',
    //     'error': 'danger',
    //     'in progress': 'warning',
    //     'unknown': 'warning',
    //     "upgrading": "warning",
    //     "spp upgrading": "warning",
    //     "aos upgrading": "warning",
    //     "ahv upgrading": "warning",
    //     "pre checking": "warning",
    //     "repairing": "warning"
    //   }
    //   return statusMap[st];
    // },
    computedstatus(pes) {
      let status = [
        { status: 'Planned', count: 0 },
        { status: 'In Progress', count: 0 },
        { status: 'Completed', count: 0 },
        { status: 'Error', count: 0 },
        { status: 'Canceled', count: 0 }
      ];
      const inProgressList = [
        'in progress',
        'pre checking',
        'spp upgrading',
        'aos upgrading',
        'ahv upgrading',
        'upgrading',
        'repairing'
      ];

      pes.forEach(pe => {
        if(pe.status){
          if (pe.status === 'Done') {
            const completed = status.find(s => s.status === 'Completed');
            if (completed) completed.count++;
          } else if (inProgressList.includes(pe.status.toLowerCase())) {
            const inprogress = status.find(s => s.status === 'In Progress');
            if (inprogress) inprogress.count++;
          } else {
            const isstatusmatched = status.find(s => s.status === pe.status);
            if (isstatusmatched) {
              isstatusmatched.count++;
            }
          }}
      });
      return status;
    },
    dailyre_filter(value){
      return value?'Yes':'No'
    },
    ppd_filter(pe){
      if(pe.planned_date == null){
        return "No Planned"
      }
      else{
        return pe['planned_date']
      }
    },
    pps_filter(pe){
      if(pe.planned_date == null){
        return "NA"
      }
      else{
        return pe['status']
      }
    },
    plan_filter(plan){
      if(plan==null){
        return "Not Planned"
      }
      else{
        return plan
      }
    },
    ifaction_filter(pe) {
      if (pe == null) {
        return true
      }
      else {
        return false
      }
    },
  },
  data() {
    const validateTime =(rule, value, callback)=>{
      let currentdate = new Date()
      console.log(currentdate.getTime())
      let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
      if (value < utctime){
        callback(new Error('Schedule date must be later then now.'))
      }else{
        let currnettime = utctime.getTime()
        let scheduletime = value.getTime()
        let timediff = scheduletime-currnettime
        if(timediff/1000/60 < 5){
          callback(new Error('Schedule date is too close from now.'))
        }else{
          callback()
        }
      }
      callback()
    }
    return {
      tableKey: 0,
      filtered_list: null,
      current_list: null,
      page_list: null,
      temp_pc_datalist:null,
      filter:{
        pc_list:[],
        selected_pc:[],
        fuzzy_string:"",
        prodswitch: false,
      },
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: '',
        prism: '',
        status: '',
        sort: '+id'
      },
      statusToShowEditButton:['Not Started'],
      statusToShowAbortButton:['In Progress'],
      statusToShowDeleteButton:['Not Started','Done','Error','Aborted'],
      statusOptions: ['Not Started','In Progress','Done','Error','Aborted'],
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      // statusOptions: ['published', 'draft', 'deleted'],
      ShowCreationDate: false,
      selectedrow:'',
      dialogPvVisible: false,
      dialogEPVisible: false,
      dialogECVisible: false,
      dialogSelectClustersVisible: false,
      dialogMakeAPlanVisible: false,
      logdata: [],
      rules: {
        prism: [{ required: true, message: 'prism is required', trigger: 'change' }],
        cluster: [{ required: true, message: 'cluster is required', trigger: 'change' }],
        // timestamp: [{ type: 'date', required: true , trigger: 'change' , validator:validateTime}]
      },
      prismoptions:[],
      pes_list: [],     // The PEs selected in the "select clusters" dialog
      temp_pedatalist:[],
      temp_pccountry_list:[],
      temp_pcpecountry_list:{},
      temp_clusterlist:{},
      searchQuery: '',
      aosVersions: [],
      selectedAosVersion: null, 
      selectedAosVersions: [], 
      popoverVisible: false,
      prodradio: '1', // 1 for siab, 2 for wiab
      ifpriv:false,
      cls:{
        pc:{
          isIndeterminate: false,
          checkAll: false,
          selectedlist: [],
          totallist:[],
          // datalist:[
          //   'SSP-EU-NTXIKEA.COM',
          //   'SSP-NA-NTXIKEA.COM',
          //   'SSP-APAC-NTXIKEA.COM',
          //   'SSP-CN-NTXIKEA.COM',
          //   'SSP-PPE-NTXIKEA.COM'
          // ],
          datalist:[]
        },
        country:{
          isIndeterminate: false,
          checkAll:false,
          selectedlist: [],
          totallist:[],
          // datalist:['SE', 'FR', 'DE', 'BE', 'GB'],
          datalist:[],
        },
        pe:{
          isIndeterminate: false,
          checkAll:false,
          selectedlist: [],
          totallist:[],
          datalist:[],
          currentlist:[],
        },
      },
      checkaction:'',
      planlist: [],
      selqueue: [],
      makePlanForm: {
        creater: '',
        interval: 0,
        desiredPlanDate: new Date(),
        maxcount: 0,
        execution_sequence: 'PC',
        clusterlist: [], 
        pes: [],  
        pc: '',
        country: '',
        isDailyReportRequired: false,
        temppes: []
      },
      makePlanFormDefaultValue: {
        creater: '',
        interval: 0,
        desiredPlanDate: new Date(),
        maxcount: 0,
        execution_sequence: 'PC',
        clusterlist: [],    
        pes: [],
        pc: '',
        country: '',
        isDailyReportRequired: false,
        temppes: []
      },
      makePlanRules: {      // TODO: not working
        maxcount: [
          { required: true }
        ],
        execution_sequence: [
          { required: true, message: 'Please select execution sequence', trigger: 'blur', default: 'pc' },
        ],
        interval: [
          { required: true }
        ],
        clusterlist: [
          { required: true, message: 'Please select clusters' }
        ],
        isDailyReportRequired: [
          { required: true }
        ],
        desiredPlanDate: [
          { required: true, message: 'Please select which date you want to start plan.' }
        ],
      },
      editform_plan: {
          creater: '',
          interval: 0,
          desireddate: '',
          maxcount: 0,
          execution_sequence: '',
          clusterlist: [],
          daily_report: true,
          planid: '',
          pes: '',
          pc: '',
          country: '',
          planneddate: '',
          planid: '',
          temppes: ''
        },
      editform_pe: {
          pe: '',
          pc: '',
          country: '',
          planneddate: new Date(),
          planid: ''
        },
      pickerOptions1: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
      },
      siab_aos_targetversion: null,
      wiab_aos_targetversion: null,
      siab_ahv_targetversion: null,
      wiab_ahv_targetversion: null,
    }
  },
  computed: {
    total() {
      if(this.filtered_list){
        console.log(this.filtered_list.length)
        return this.filtered_list.length
      }
      else{
          return 0
      }
    },

    computedCountries() {
        const countrySet = new Set();
        console.log("Selected PCs:", this.cls.pc.selectedlist);
        console.log("PE List:", this.pes_list);
        // if(this.checkaction != 'pe'){
        // this.cls.pc.selectedlist.forEach(pcName => {
        //     this.pes_list.forEach(peItem => {
        //         if (peItem.pc.toUpperCase() === pcName.toUpperCase()) {
        //             peItem.pe.forEach(pe => {
        //                 countrySet.add(pe.country);
        //             });
        //         }
        //     });
        // })};
        console.log("Computed Countries with PE selected list:", this.cls.pe.selectedlist);
        if(this.checkaction != 'pc'){
          this.cls.pe.selectedlist.forEach(peName => {
              this.pes_list.forEach(peItem => {
                  peItem.pe.forEach(pe => {
                    pe.name.forEach(p => {if (p.fqdn.toUpperCase() === peName.toUpperCase()) { // 如果PE被选中
                        console.log("PE Name:", peName);
                        console.log("///////////////////////")
                          countrySet.add(pe.country);
                      }})
                      // if (pe.fqdn.includes(peName)) { // 如果PE被选中
                      //   console.log("PE Name:", peName);
                      //   console.log("///////////////////////")
                      //     countrySet.add(pe.country);
                      // }
                  });
              });
          })
        }
        this.updateCheckAllAndIndeterminateState();
        console.log("Computed Countries:", Array.from(countrySet));
        console.log(this.cls.country.checkAll)
      console.log(this.cls.pc.checkAll)
      console.log(this.cls.pe.checkAll)
        return Array.from(countrySet);
    },

    computedPCs() {
        const pcSet = new Set();
        console.log("Selected Countries:", this.cls.country.selectedlist);

        // this.cls.country.selectedlist.forEach(countryName => {
        //     this.pes_list.forEach(peItem => {
        //         peItem.pe.forEach(pe => {
        //             if (pe.country === countryName) {
        //                 pcSet.add(peItem.pc.toUpperCase());
        //             }
        //         });
        //     });
        // });
        this.cls.pe.selectedlist.forEach(peName => {
            this.pes_list.forEach(peItem => {
                peItem.pe.forEach(pe => {
                  pe.name.forEach(p => {if (p.fqdn.toUpperCase() === peName.toUpperCase()) { // 如果PE被选中
                      console.log("PE Name:", peName);
                      console.log("///////////////////////")
                      pcSet.add(peItem.pc.toUpperCase());
                    }})
                    // if (pe.fqdn.includes(peName)) { // 如果PE被选中
                    //   console.log("PE Name:", peName);
                    //   console.log("///////////////////////")
                    //     countrySet.add(pe.country);
                    // }
                });
            });
        })
        console.log(this.cls.country.checkAll)
      console.log(this.cls.pc.checkAll)
      console.log(this.cls.pe.checkAll)
        this.updateCheckAllAndIndeterminateState();
        console.log("Computed PCs:", Array.from(pcSet));
        console.log(this.cls.country.checkAll)
      console.log(this.cls.pc.checkAll)
      console.log(this.cls.pe.checkAll)
        return Array.from(pcSet);
    },

    computedPEs() {
        const peSet = new Set();
        console.log("Selected Countries for PEs:", this.cls.country.selectedlist);
        if(this.checkaction != 'country'){
        this.cls.pc.selectedlist.forEach(pcName => {
            this.pes_list.forEach(peItem => {
                if (peItem.pc.toUpperCase() === pcName.toUpperCase()) {
                    peItem.pe.forEach(pe => {
                        pe.name.forEach(p => {
                        peSet.add(p.fqdn);
                    });
                    });
                }
            });
        })};
        if(this.checkaction != 'pc'){
        this.cls.country.selectedlist.forEach(countryName => {
            this.pes_list.forEach(peItem => {
              peItem.pe.forEach(pe =>{
                if (pe.country === countryName) {
                    pe.name.forEach(p => {
                        peSet.add(p.fqdn);
                    });
                }

              })
            });
        });
        }
        console.log(this.cls.country.checkAll)
      console.log(this.cls.pc.checkAll)
      console.log(this.cls.pe.checkAll)
        this.updateCheckAllAndIndeterminateState();
        console.log("Computed PEs:", Array.from(peSet));
        console.log(this.cls.country.checkAll)
      console.log(this.cls.pc.checkAll)
      console.log(this.cls.pe.checkAll)
        return Array.from(peSet);
    },
  computedCountryList() {
    const countrySet = new Set()
    this.cls.pe.currentlist.forEach(peName => {
            this.pes_list.forEach(peItem => {
                peItem.pe.forEach(pe => {
                  pe.name.forEach(p => {if (p.fqdn.toUpperCase() === peName.value.toUpperCase()) { 
                      console.log("PE Name:", peName);
                      console.log("///////////////////////")
                        countrySet.add(pe.country);
                    }})
                });
            });
        });
        console.log("Computed Countries:", Array.from(countrySet));
    return Array.from(countrySet).map(value => ({
      value: value.toUpperCase(),
      isIndeterminate: false,
      checkAll: false
    }));
  },

  computedPcList() {
    const pcSet = new Set();
    this.cls.pe.currentlist.forEach(peName => {
            this.pes_list.forEach(peItem => {
                peItem.pe.forEach(pe => {
                  pe.name.forEach(p => {if (p.fqdn.toUpperCase() === peName.value.toUpperCase()) { 
                      console.log("PE Name:", peName);
                      console.log("///////////////////////")
                      pcSet.add(peItem.pc);
                    }})
                });
            });
        });
console.log("Computed PCs:", Array.from(pcSet));
    return Array.from(pcSet).map(value => ({
      value: value.toUpperCase(),
      isIndeterminate: false,
      checkAll: false
    }));
  }
  },
  created() {
    this.selqueue = Array.from({ length: 5 }, (v, k) => k + 1);
    // this.get_task_list()
    this.get_plan_list()
    this.get_pc_list(),
    this.get_targetversion(),
    this.check_priv()
  },
  methods: {
    statusRowClass({ row }) {
    switch ((row.status || '').toLowerCase()) {
      case 'planned':
        return 'row-status-planned';
      case 'in progress':
        return 'row-status-inprogress';
      case 'completed':
        return 'row-status-completed';
      case 'error':
        return 'row-status-error';
      case 'canceled':
        return 'row-status-canceled';
      default:
        return '';
    }
  },
    statusFilter(status) {
      let st = status ? status.toLowerCase() : 'unknown';
      const statusMap = {
        'planned': 'primary',
        'canceled': 'info',
        'completed': 'success',
        'done': 'success',
        'error': 'danger',
        'in progress': 'warning',
        'unknown': 'warning',
        "upgrading": "warning",
        "spp upgrading": "warning",
        "aos upgrading": "warning",
        "ahv upgrading": "warning",
        "pre checking": "warning",
        "repairing": "warning"
      }
      return statusMap[st];
    },
    check_priv(){
        this.priv = this.$store.getters.all_privilege
        console.log("privilege",this.priv)
        this.ifpriv = this.priv.role_lcm.manage_slcm =='full'? true:false
      },
      updateSelections() {
          this.cls.country.selectedlist = this.computedCountries;
          this.cls.pc.selectedlist = this.computedPCs;
          this.cls.pe.selectedlist = this.computedPEs;
          this.updateCheckAllAndIndeterminateState();
      },

      updateCheckAllAndIndeterminateState() {
        console.log("updateCheckAllAndIndeterminateState")
          const pcTotal = this.cls.pc.datalist.length;
          const selectedPCs = this.cls.pc.selectedlist.length;
          this.cls.pc.checkAll = selectedPCs === pcTotal;
          this.cls.pc.isIndeterminate = selectedPCs > 0 && selectedPCs < pcTotal;

          const countryTotal = this.cls.country.datalist.length;
          const selectedCountries = this.cls.country.selectedlist.length;
          this.cls.country.checkAll = selectedCountries === countryTotal;
          this.cls.country.isIndeterminate = selectedCountries > 0 && selectedCountries < countryTotal;

          const peTotal = this.cls.pe.datalist.length;
          const selectedPEs = this.cls.pe.selectedlist.length;
          this.cls.pe.checkAll = selectedPEs === peTotal;
          this.cls.pe.isIndeterminate = selectedPEs > 0 && selectedPEs < peTotal;
          // handleCheckAllChange(val,items) {
    //   console.log(val,items)
    //   for (let key in this.cls){ 
    //     console.log(key)
    //     this.cls[key].checkAll = val;
    //     this.cls[key].isIndeterminate = false;
    //     this.cls[key].selectedlist = val ? this.cls[key].datalist.map(item => item.value): [];
    //   }
    // },
      },
      updateCheckAndIndeterminateState() {
        console.log("updateCheckAndIndeterminateState")
        let checkedCount = this.cls.country.selectedlist.length;
        this.cls.pc.selectedlist.forEach(sl=>{
          let r = this.temp_pccountry_list.find(p =>p.pc==sl)
          let issubset = this.isSubset(r.country,this.cls.country.selectedlist)
          let slpc = this.cls.pc.datalist.find(pcountry=>pcountry.value==sl)
          slpc.checkAll = this.isContained(this.cls.country.selectedlist, r.country)
          slpc.isIndeterminate = issubset.length > 0 && issubset.length < r.country.length
        })
        let res_pclist = this.isSubset(this.cls.pc.datalist.map(item => item.value), this.cls.pc.selectedlist)
          if(res_pclist.length){
            res_pclist.forEach(p=> {
              let rpc = this.cls.pc.datalist.find(pcountry=>pcountry.value==p)
              rpc.isIndeterminate =false
            })
        }
        let res_countrylist = this.isSubset(this.cls.country.datalist.map(item => item.value), this.cls.country.selectedlist)
          if(res_countrylist.length){
            res_countrylist.forEach(p=> {
              let rcountry = this.cls.country.datalist.find(pcountry=>pcountry.value==p)
                rcountry.isIndeterminate =false
            })
        }
        this.cls.pe.selectedlist.forEach((v,i)=>{
          this.pes_list.forEach(item=>{
            let resault = []
            // resault = item.pe.filter(peitem => peitem.name.indexOf(v) != -1)
            resault = item.pe.filter(peitem => peitem.name.some(n => n.fqdn.indexOf(v) != -1))
            if (resault.length){
              if(this.cls.pc.selectedlist.indexOf(item.pc.toUpperCase()) == -1){
                this.cls.pc.selectedlist.push(item.pc.toUpperCase())
              }
              if(this.cls.country.selectedlist.indexOf(resault[0].country)== -1){
                this.cls.country.selectedlist.push(resault[0].country)
              }
              let issubset = this.isSubset(resault[0].name.map(item => item.fqdn),this.cls.pe.selectedlist)
              let slpc = this.cls.pc.datalist.find(pcountry=>pcountry.value==item.pc.toUpperCase())
              let slcountry = this.cls.country.datalist.find(pcountry=>pcountry.value==resault[0].country)
              if(issubset.length){
                slcountry.checkAll=false
                slcountry.isIndeterminate=true
              }else{
                slcountry.isIndeterminate=false
                slcountry.checkAll=true
              }
              let r = this.temp_pccountry_list.find(p =>p.pc==item.pc.toUpperCase())
              let country_issubset = this.isSubset(r.country,this.cls['country'].selectedlist)
              if(country_issubset.length){
                slpc.checkAll=false
                slpc.isIndeterminate=true
              }else{
                slpc.isIndeterminate=false
                slpc.checkAll=true
              }
            }
          })
        })
        let re_countrylist = this.isSubset(this.cls.country.datalist.map(item => item.value), this.cls['country'].selectedlist)
          if(re_countrylist.length){
            re_countrylist.forEach(p=> {
              let rcountry = this.cls.country.datalist.find(pcountry=>pcountry.value==p)
                rcountry.isIndeterminate =false
            })
        }
        let r_pclist = this.isSubset(this.cls.pc.datalist.map(item => item.value), this.cls['pc'].selectedlist)
          if(r_pclist.length){
            r_pclist.forEach(p=> {
              let rpc = this.cls.pc.datalist.find(pcountry=>pcountry.value==p)
              rpc.isIndeterminate =false
            })
        }
        this.cls.pc.checkAll = this.cls.country.checkAll = checkedCount === this.cls.pe.datalist.length;
        this.cls.pc.isIndeterminate = this.cls.country.isIndeterminate = checkedCount > 0 && checkedCount < this.cls.pe.datalist.length;
        
        this.cls.pc.checkAll = this.cls.pe.checkAll = checkedCount === this.cls.pe.datalist.length;
        this.cls.pc.isIndeterminate = this.cls.pe.isIndeterminate = checkedCount > 0 && checkedCount < this.cls.pe.datalist.length;
      },

      handleCheckedChange(item) {
        console.log("handleCheckedChange")
        this.checkaction = item
          if (item === 'pc') {
            this.cls.country.selectedlist = this.computedCountries;
            this.cls.pe.selectedlist = this.computedPEs;
              // this.updateSelections(); 
          } else if (item === 'country') {
            
            this.cls.pe.selectedlist = this.computedPEs;
            this.cls.pc.selectedlist = this.computedPCs;
              // this.updateSelections(); 
          } else if (item === 'pe') {
            this.cls.country.selectedlist = this.computedCountries;
          this.cls.pc.selectedlist = this.computedPCs;
          }
          this.updateCheckAndIndeterminateState()
          this.updateCheckAllAndIndeterminateState();
      },
    handleCheckAllChange(val,items) {
      console.log(val,items)
      console.log("handleCheckAllChange")
      for (let key in this.cls){ 
        console.log(key)
        this.cls[key].checkAll = val;
        this.cls[key].isIndeterminate = false;
        if(key== 'pe'){
          this.cls[key].selectedlist = val ? this.cls[key].currentlist.map(item => item.value): [];
        } else {
          this.cls[key].selectedlist = val ? this.cls[key].datalist.map(item => item.value): [];
        }
      }
      // this.updateSelections(); 
    },
    matchesAnyFilter(details) {
      console.log("current_", details)
      return this.filter.selected_pc.forEach(pc => {
        return pc && details.includes(pc); 
      });
    },
    resetForm(formName) {
      this.$refs[formName].clearValidate();
    },
    resetcls() {
      console.log("resetcls")
      this.cls.pc.checkAll = false;
      this.cls.country.checkAll = false;
      this.cls.pe.checkAll = false;
      this.cls.pc.selectedlist = [];
      this.cls.country.selectedlist = [];
      this.cls.pe.selectedlist = [];
      this.updateSelections();
    },
    resetMakePlanForm() {
      this.makePlanForm = this.makePlanFormDefaultValue;
      this.makePlanForm.clusterlist = [];
      console.log("Resetting Make Plan Form");
      console.log(this.makePlanForm);
      // self.resetcls();
      // self.resetForm('makePlanForm');
    },
    handle_pes(pes) {
      let result = {};
      pes.forEach(item => {
        let pc = item.pc;
        let country = item.country;
        let pe = item.pe;

        if (!result[pc]) {
          result[pc] = {};
        }
        if (!result[pc][country]) {
          result[pc][country] = [];
        }
        result[pc][country].push(pe);
      });

      return result;
    },
    handleRemoveTag(index) {
      this.editform_plan.temppes.forEach(item => {
        if (item.pe == this.editform_plan.clusterlist[index]) {
          this.editform_plan.temppes.splice(this.editform_plan.temppes.indexOf(item), 1);
          // this.makePlanForm.clusterlist = this.editform_plan.temppes
        }
      })
      this.makePlanForm.pes = this.editform_plan.pes = this.handle_pes(this.editform_plan.temppes)
      this.editform_plan.clusterlist.splice(index, 1);
      this.makePlanForm.clusterlist = this.editform_plan.clusterlist
      // this.editform_plan.pes.forEach(item1=>{
      //   item1.pe = item1.pe.filter(i=>this.editform_plan.clusterlist.includes(i))
      // })  
    },
    handle_updatetag(row) {
      this.editform_plan.clusterlist = this.cls.pe.selectedlist
      row.pes.forEach(item=>{
        this.editform_plan.clusterlist.push(item.pe)
      })
      this.editform_plan.pes = this.handle_pes(row.pes);
      this.editform_plan.clusterlist = this.editform_plan.clusterlist.concat(this.cls.pe.selectedlist);
      this.editform_plan.pes = this.handle_pes(row.pes);
      // row.pes.forEach(item=>{
      //   console.log(item.pe)
      //   this.editform_plan.clusterlist.push(item.pe)
      // })
      // this.editform_plan.pes = this.handle_pes(row.pes);

      // this.editform_plan.clusterlist = this.editform_plan.clusterlist.filter((item, i) => i !== index);
      // this.editform_plan.temppes = this.editform_plan.temppes.filter((item, i) => i !== index);
      // this.editform_plan.pes = this.handle_pes(this.editform_plan.temppes);
    },
    handleAddTag(isCreatePlan) {
      console.log("isCreatePlan: ", isCreatePlan)
      if(isCreatePlan){this.editform_plan.temppes = []}
      // if (!isCreatePlan) {
        // edit form
        console.log("makePlanForm:before", this.makePlanForm.clusterlist)
        console.log("selectedlist:before", this.cls.pe.selectedlist)
      for(let cluster of this.cls.pe.selectedlist){
        if (this.editform_plan.clusterlist.indexOf(cluster) === -1) {
          this.editform_plan.clusterlist.push(cluster);
          // this.makePlanForm.clusterlist.push(cluster);
          this.pes_list.forEach(item => {
            item.pe.forEach(pe => {
              let namesList = pe.name.map(item => item.fqdn);  
              if(namesList.includes(cluster)){
                this.editform_plan.temppes.push({
                  pc: item.pc,
                  country: pe.country,
                  pe: cluster
                });
              }
            })
          })
          this.editform_plan.pes = this.handle_pes(this.editform_plan.temppes);
        }
        if (this.makePlanForm.clusterlist.indexOf(cluster) === -1) {
          this.makePlanForm.clusterlist.push(cluster);
        }
        console.log("this.cls.pe.currentlist:", this.cls.pe.currentlist)
        console.log("tcurrentlist:", cluster)
        this.cls.pe.currentlist = this.cls.pe.currentlist.filter(cl => cl.value !== cluster)
        console.log("this.cls.pe.currentlist after filtering:", this.cls.pe.currentlist)
      }
      console.log("editform_plan:", this.editform_plan.clusterlist)
      console.log("makePlanForm:after", this.makePlanForm.clusterlist)
      this.dialogSelectClustersVisible=false
    },
    getTagClosable(tag) {
      const peObj = this.editform_plan.temppes.find(item => item.pe === tag);
      return !(peObj && peObj.status && peObj.status.toLowerCase() !== 'planned');
    },
    handleEditPEClick(isCreatePlan){
      this.load_pc_pe_list()
      this.updatPECurrentlist()
      this.dialogSelectClustersVisible=true
      this.isCreatePlan = isCreatePlan
    },
    
    get_targetversion() {
      GetTargetVersion(this.$store.getters.token).then(response => {
        
          // this.temp.targetversion = response.data
          // console.log(this.temp.targetversion)
          response.data.forEach((item)=>{
            if(item.index_label=='default'){
              if(item.aos_version){
                this.siab_aos_targetversion = item.aos_version
                this.siab_ahv_targetversion = item.ahv_version
              }else{
              alert("No Siab AOS target version found, please check the target version configuration.")
              }
            }
            if(item.index_label=='Warehouse'){
              if(item.aos_version){
                this.wiab_aos_targetversion = item.aos_version
                this.wiab_ahv_targetversion = item.ahv_version
              }else{
                alert("No Wiab AOS target version found, please check the target version configuration.")
              }
            }
          })
      })
    },
    get_pc_list(){
      GetPrismList(this.$store.getters.token).then(response => {
        for(let pc of response['data']){
          let regex = /-(\w+)-/;
          let match = pc['fqdn'].match(regex);
          if(pc['fqdn'].indexOf('wiab') != -1){
            let k = "Wiab" + "-PC-" + match[1].toUpperCase()
            this.prismoptions.push({"key": k, "display_name": pc['fqdn'].toUpperCase()});
          }else{
            let k = "Retail" + "-PC-" + match[1].toUpperCase()
            this.prismoptions.push({"key": k, "display_name": pc['fqdn'].toUpperCase()});
          }
        }
      })
    }, 
    get_plan_list(){
      GetSLCMPlans().then(response => {
        this.planlist = response.data
        this.filtered_list = this.planlist.reverse()
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)
        this.listLoading = false
      }).catch(error => {
        console.error("Error fetching plans:", error);
        });
    },
    cancel_slcmplan(row){
      CancelSLCMPlan(this.$store.getters.token, row.id).then(response => {
          this.$notify({
            title: 'Success',
            message: 'Plan canceled successfully',
            type: 'success',
            duration: 2000
          });
          location.reload()
          // this.get_plan_list();
      }).catch(error => {
        let msg = error?.response?.data?.description;
          if (!msg && error?.response?.data?.message) {
            msg = error.response.data.message;
          }
          if (!msg) {
            msg = error.message || 'Unknown error';
          }
          this.$notify({
            title: 'Error',
            message: msg,
            type: 'error',
            duration: 4000
          });
          console.error("Error updating PE:", error);
          this.get_plan_list();
      });
    },
    edit_slcmplan(){
      this.dialogEPVisible = false
      this.listLoading = true
      let newdate = this.handleDateChange(this.editform_plan.desireddate)
      let payload = {
        data:{
          "max_count": this.editform_plan.maxcount,
          "interval": this.editform_plan.interval,
          "cluster_list": this.editform_plan.pes,
          "daily_report": this.editform_plan.daily_report,
          "desired_plan_date": newdate,
          "execution_sequence": this.editform_plan.execution_sequence,
          "facility_type": "retail",         
          "lcm_type": "AOS",                 
        },
        token: this.$store.getters.token
      }
      UpdateSLCMPlans(payload, this.editform_plan.planid).then(response => {
          this.$notify({
            title: 'Success',
            message: 'Plan updated successfully',
            type: 'success',
            duration: 2000
          });
          // this.get_plan_list();
          location.reload()
        }).catch(error => {
          let msg = error?.response?.data?.description;
          if (!msg && error?.response?.data?.message) {
            msg = error.response.data.message;
          }
          if (!msg) {
            msg = error.message || 'Unknown error';
          }
          this.$notify({
            title: 'Error',
            message: msg,
            type: 'error',
            duration: 4000
          });
          console.error("Error updating PE:", error);
          this.get_plan_list();
        });
    },
    edit_slcmppe(){
      let newdate = this.handleDateChange(this.editform_pe.planneddate)
      this.dialogECVisible = false
      let payload = {
        data: {
          "plan_id": this.editform_pe.planid,
          "pe_fqdn": this.editform_pe.pe,
          "facility_type": "retail",
          "planned_date": newdate,
          "lcm_type" : "AOS"
        },
        token: this.$store.getters.token
      };
      UpdateSLCMPEs(payload).then(response => {
          this.$notify({
            title: 'Success',
            message: 'PE updated successfully',
            type: 'success',
            duration: 2000
          })
        this.get_plan_list();
      }).catch(error => {
        let msg = error?.response?.data?.description;
        if (!msg && error?.response?.data?.message) {
          msg = error.response.data.message;
        }
        if (!msg) {
          msg = error.message || 'Unknown error';
        }
        this.$notify({
          title: 'Error',
          message: msg,
          type: 'error',
          duration: 4000
        });
        console.error("Error updating PE:", error);
        this.get_plan_list();
      });
    },
    cancel_slcmppe(row){
      console.log(row)
      let payload = {
        data: {
          "plan_id": row.plan_id,
          "pe_fqdn": row.pe,
          "facility_type": "retail",    
          "lcm_type": "AOS"             
        },
        token: this.$store.getters.token
      };
      CancelSLCMPEs(payload).then(response => {
          this.$notify({
            title: 'Success',
            message: 'PE Canceled successfully',
            type: 'success',
            duration: 2000
          });
          location.reload()
          // this.get_plan_list();
      }).catch(error => {
        let msg = error?.response?.data?.description;
        if (!msg && error?.response?.data?.message) {
          msg = error.response.data.message;
        }
        if (!msg) {
          msg = error.message || 'Unknown error';
        }
        this.$notify({
          title: 'Error',
          message: msg,
          type: 'error',
          duration: 4000
        });
        console.error("Error updating PE:", error);
        this.get_plan_list();
      });
    },
    handle_editplan(row){
      console.log(row)
      this.editform_plan.clusterlist = []
      this.dialogEPVisible = true
      this.editform_plan.planid = row.id;
      this.editform_plan.creater = row.creater;
      this.editform_plan.interval = row.interval;
      this.editform_plan.desireddate = row.desired_plan_date;
      this.editform_plan.maxcount = row.max_count;
      this.editform_plan.execution_sequence = row.execution_sequence;
      this.editform_plan.temppes = row.pes;
      // this.editform_plan.pc = '';
      // this.editform_plan.country = '';
      // this.editform_plan.planneddate = '';
      // this.editform_plan.planid = '';
      console.log(row.pes)
      console.log("row.pes")

      row.pes.forEach(item=>{
        console.log(item.pe)
        this.editform_plan.clusterlist.push(item.pe)
      })
      this.editform_plan.pes = this.handle_pes(row.pes);
      console.log(this.editform_plan.pes)
      this.$nextTick(() => {
        this.$refs['editform_plan'].clearValidate()
      })
      // this.edit_slcmplan(row,true)
    },
    handle_edit_slcmppe(row){
      console.log(row)
      this.dialogECVisible = true
      this.editform_pe.pe = row.pe;
      this.editform_pe.pc = row.pc;
      this.editform_pe.planid = row.plan_id;
      this.editform_pe.country = row.country;
      this.editform_pe.planneddate = row.planned_date;
      console.log(row.planned_date);
      console.log(this.editform_pe);
      
      this.$nextTick(() => {
        this.$refs['editform_pe'].clearValidate()
      })
      // this.edit_slcmplan(row); // Uncommented to call edit_slcmplan after handling edit
    },
    openCreatePlanDialog(){
      // if(!this.ifpriv){
      //     alert('You do not have permission to create a plan.');
      //     return;
      //   }
      if(this.prodradio == 1){
        this.makePlanForm = this.makePlanFormDefaultValue;
        this.makePlanForm.clusterlist = [];
        this.dialogMakeAPlanVisible = true
        this.load_pc_pe_list()
        this.$nextTick(() => {
          this.$refs['makePlanForm'].clearValidate()
        })
      }else{
        alert('Seamless LCM for Wiab is under development.');
      }
      //   this.$nextTick(() => {
      //     this.resetcls()
      //     this.$refs['makePlanForm'].clearValidate()
      //     // this.load_pc_pe_list()
      //     // location.reload()
          
      // })
    },
    confirmSelection() {
      console.log('Selected Prism Central:');
    },
    load_pc_pe_list(){
      this.cls.pc.datalist = []
      this.cls.pe.datalist = []
      this.cls.country.datalist = []
      this.cls.pe.currentlist = []
      this.temp_pccountry_list=[]
      this.temp_pcpecountry_list={}
      this.temp_pedatalist = [];
      GetPCPECorrespondence_Lcm().then(response => { 
        // console.log(response)
        // this.pes_list = response.data.filter(item => item.pc !== "ssp-ppe-ntx.ikea.com")
        this.pes_list = response.data.filter(item => 
            // item.pc !== "ssp-ppe-ntx.ikea.com" && 
            item.pc !== "ssp-russia-ntx.ikea.com")
            //item.pc !== "ssp-dhd2-ntx.ikead2.com")
            // item.pc !== "ssp-dt-ntx.ikeadt.com")
          // return {"pc":item.pc,"pe":item.pe.map(pe=>{return {"country":pe.country,"name":pe.name}})}
        // console.log(this.pes_list)
        console.log(this.cls.pe.datalist)
        console.log("this.cls.pe.datalist/////////")
        this.pes_list.forEach(item=>{
          this.cls.pc.datalist.push({
            "value":item.pc.toUpperCase(),
            "isIndeterminate":false,
            "checkAll":false
          })
          this.temp_pccountry_list.push({
            "pc":item.pc.toUpperCase(),
            "country":item.pe.map(c =>c.country)
          })
          item.pe.forEach(i=>{
            this.cls.country.datalist.push({
              "value":i.country.toUpperCase(),
              "isIndeterminate":false,
              "checkAll":false
            })
            i.name.forEach(name => {
              if (!this.aosVersions.includes(name.aos_version)) {
                this.aosVersions.push(name.aos_version);
              }
            });
            // let namesList = i.name.map(item => item.fqdn);
            // console.log(namesList)
            this.temp_pedatalist=this.temp_pedatalist.concat(i.name)
          })
        })
        console.log(this.cls.pe.datalist)
        this.temp_pedatalist.forEach(tp=>{
          this.cls.pe.datalist.push({
            "value":tp.fqdn.toUpperCase(),
            "isIndeterminate":false,
            "checkAll":false, 
            "aosversion": tp.aos_version
          })
        })
        console.log("this.cls.pe.datalist")
        console.log(this.cls.pe.datalist)
        this.cls.pe.currentlist = this.cls.pe.datalist
        this.temp_pc_datalist = this.cls.pc.datalist
        this.cls.country.datalist=this.remove_duplicate(this.cls.country.datalist)
        this.filterPeList();
      })
    },
    filterPeList() {
      this.cls.pe.currentlist = [];
      const selectedVersions = this.selectedAosVersions;
      const searchQuery = this.searchQuery.toLowerCase();

      if (selectedVersions.length > 0) {
        this.cls.pe.currentlist = this.cls.pe.datalist.filter(item => 
          selectedVersions.includes(item.aosversion) && 
          item.value.toLowerCase().includes(searchQuery)
        );
      } else {
        this.cls.pe.currentlist = this.cls.pe.datalist.filter(item => 
          item.value.toLowerCase().includes(searchQuery) 
        );
      }
      this.cls.country.datalist = this.computedCountryList;
      this.cls.pc.datalist = this.computedPcList;
    },
    createSlcmPlan(){
      this.$refs['makePlanForm'].validate((valid) => {
        if (valid) {
          console.log('Form validation passed.');
          this.sendCreateSlcmPlanRequest()
        } else {
          console.log('Form validation failed.');
          return false;
        }
      });
    },
    sendCreateSlcmPlanRequest(){
      this.listLoading = true
      this.temp_pcpecountry_list={}
      this.pes_list.forEach(item=>{
        // console.log(item)
        // console.log("item")
        this.temp_pcpecountry_list[item.pc]={}
        item.pe.forEach(el => {
          this.temp_pcpecountry_list[item.pc][el.country]=el.name
        });
      })
      this.cls['pc'].selectedlist.forEach(pc=>{
        this.temp_clusterlist[pc.toLowerCase()]={}
        this.cls['country'].selectedlist.forEach(co =>{
          if(this.temp_pcpecountry_list[pc.toLowerCase()][co] != undefined){
            this.temp_clusterlist[pc.toLowerCase()][co]= this.temp_pcpecountry_list[pc.toLowerCase()][co].filter(pe=>this.cls['pe'].selectedlist.includes(pe))
          }
        })
      })
      let newdate = this.handleDateChange(this.makePlanForm.desiredPlanDate)
      let payload = {
        data:{
          "cluster_list": this.editform_plan.pes,
          "desired_plan_date": newdate,          
          "max_count": this.makePlanForm.maxcount,                                       // int, max number of clusters to run per day
          "interval": this.makePlanForm.interval,                                         // int, interval between two clusters
          // "status": "Not Started",
          "daily_report": this.makePlanForm.isDailyReportRequired,                               // boolean, TODO: add a button to control it
          "execution_sequence": this.makePlanForm.execution_sequence,                     // ENUM("PC"/"COUNTRY"/"PE")
          "lcm_type": "AOS",
          "facility_type": "retail"
        },
        token: this.$store.getters.token                         // ENUM("PC"/"COUNTRY"/"PE")
      }
      console.log(payload)
      CreateSLCMPlans(payload).then(response => {
        if (response.status == 409) {
          this.$notify({
            title: 'Conflict',
            message: 'A plan with the same parameters already exists.',
            type: 'warning',
            duration: 2000
          })
        }else{
          this.$notify({
            title: 'Success',
            message: 'Plan created successfully',
            type: 'success',
            duration: 2000
          });
        }
          this.get_plan_list();
          this.load_pc_pe_list();
          this.listLoading = false;
          location.reload()
      }).catch(error => {
        console.error("Error Newing Plan:", error);
      });
      // this.listLoading = false
      // this.resetcls()
      this.dialogMakeAPlanVisible=false
    },
    isDisjoint(arr1,arr2) {
      return arr2.every(item => !arr1.includes(item));
    },
    isContained(arr1,arr2){
      return arr2.every(item => arr1.includes(item))
    },
    isSubset(arr1, arr2) {
      const newArr = []
      arr1.forEach(item => {
        if(!arr2.includes(item)){
          newArr.push(item)
        }
        });
        return newArr
    },
    remove_duplicate(arr) {
      const newArr = []
      arr.forEach(item => {
        if (!newArr.includes(item)) {
          newArr.push(item)
        }
      })
      return newArr
    },
    set_page(){
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start,end)
    },
    filter_pe_list(){
      this.listQuery.page = 1
      this.filtered_list = this.planlist
      let temp_list
      if (!this.filter.selected_pc.length){
        console.log("this.filter.selected_pc not existing")
        this.filtered_list = this.planlist
      }else{ 
        console.log(this.filter.selected_pc)
        temp_list =  this.planlist.filter((item) =>{
          console.log("item pes:", item.pes)
          return item.pes.some(pe => this.filter.selected_pc.indexOf(pe.pc.toUpperCase()) !== -1)
        });
        console.log(temp_list)
        this.filtered_list = temp_list
      }
      if(this.filter.fuzzy_string.trim().length){
        let temp_list = this.filtered_list
        let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
        for(let fuzzy of fuzzy_list){
          fuzzy = fuzzy.toString().toLowerCase()
          temp_list = temp_list.filter(item => {
            return Object.values(item).some(value => {
                console.log("itvalueem");
                console.log(value);
                if (Array.isArray(value)) {
                    return value.some(innerItem => {
                        return Object.values(innerItem).some(innerValue => {
                            return String(innerValue).toLowerCase().includes(fuzzy);
                            // const regex = new RegExp(`(${fuzzy})`, 'gi');
                            // return String(innerValue).replace(regex, '<span style="background-color: yellow;">$1</span>');
                        });
                    });
                } else {
                    return String(value).toLowerCase().includes(fuzzy);
                }
            });
          });
          
        }
        this.filtered_list = temp_list;
      }
      this.set_page();
    },
    updatPECurrentlist() {
      console.log("updatPECurrentlist:", this.cls.pe.currentlist)
        if(this.makePlanForm.clusterlist.length > 0){
          let filteredData = this.cls.pe.datalist.filter(cl => this.makePlanForm.clusterlist == cl.value);
          console.log("before:", this.cls.pe.datalist)
          // const filteredData = this.cls.pe.datalist.filter(item => {
          //   console.log("Filtering item:", item.value);
          //   if(this.makePlanForm.clusterlist.includes(item.value)){
          //     console.log("Filtering item:", item.value);
          //     return true;
          //   }
          //   return false;
          // });
          console.log("Filtered Data:", filteredData);
          this.cls.pe.currentlist = filteredData;
          console.log("this.makePlanForm.clusterlist:", this.makePlanForm.clusterlist);
        }
        // if(this.editform_plan.clusterlist.length > 0){
        //   this.cls.pe.currentlist = this.cls.pe.currentlist.filter(cl => this.editform_plan.clusterlist !== cl.value);
    
        //   console.log("this.editform_plan.clusterlist:", this.editform_plan.clusterlist);
        // }
        console.log("in updatePECurrentlist", this.editform_plan.clusterlist, this.makePlanForm.clusterlist);
        console.log("this.cls.pe.currentlist:", this.cls.pe.currentlist);
    },
    handleDateChange(date) {
      if (date) {
        date= new Date(date); // 确保 date 是一个 Date 对象
        // 获取本地日期的年、月、日、小时、分钟、秒
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份要加1
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        // 构建格式为 'YYYY-MM-DDTHH:mm:ss' 的日期字符串
        const dateTimeString = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.000Z`;

        console.log(dateTimeString); // 输出格式：'YYYY-MM-DD HH:mm:ss'
        return dateTimeString; // 返回格式化后的日期字符串
      }
    },
    highlightMatch(value) {
      if (!this.filter.fuzzy_string ) return value; 
      const fuzzy = this.filter.fuzzy_string.trim().toLowerCase();
      const regex = new RegExp(fuzzy, 'i'); 
      // const pcregex = new RegExp(this.filter.selected_pc, 'i'); 
      if (regex.test(String(value)) ) {
        return `<span style="background-color: yellow;">${value}</span>`;
      }
      return value; 
    },
    togglePopover() {
      this.popoverVisible = !this.popoverVisible; 
    },
    handleFilter() {
      this.listQuery.page = 1
    },
    sortChange(data) {
      const { prop, order } = data
      console.log(prop,order)
      console.log(this.current_list)
      if(order==null){
        this.sortChange({prop:'id',order:'ascending'})
        return 
      }
      let flag_num = order=="ascending" ? 1 : -1
      console.log(flag_num)
      this.filtered_list.sort((item1,item2)=>{
        let prop1 = item1[prop]?item1[prop]:''
        let prop2 = item2[prop]?item2[prop]:''        
        return (prop1 > prop2) ? flag_num*1 : ((prop1 < prop2) ? flag_num*-1 : 0)
    })
      this.set_page()
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
  },
  watch: {
    'filter.prodswitch': function(newVal) {
      if (newVal === true) {
        alert('Seamless LCM for Wiab is under development.');
        // let userConfirmed = confirm('Seamless LCM for Wiab is under development.');
        // if (userConfirmed) {
        //   this.filter.prodswitch = false
        // } else {
        //   this.filter.prodswitch = false; 
        // }
      }
    },
    // dialogSelectClustersVisible(newVal) {
    //   if (newVal) {
    //     if(this.makePlanForm.clusterlist.length > 0){
    //       this.cls.pe.currentlist = this.cls.pe.currentlist.filter(cl => !this.makePlanForm.clusterlist.includes(cl.value));
    
    //       console.log("this.makePlanForm.clusterlist:", this.makePlanForm.clusterlist);
    //     }
    //     if(this.editform_plan.clusterlist.length > 0){
    //       this.cls.pe.currentlist = this.cls.pe.currentlist.filter(cl => !this.editform_plan.clusterlist.includes(cl.value));
    
    //       console.log("this.editform_plan.clusterlist:", this.editform_plan.clusterlist);
    //     }
        
    //   }
    
    // },
  },
}
</script>

<style lang="scss" scoped>
    .bigger_font {
      font-size: 13px;
    }
    .container {
      display: flex;
      // justify-content: space-around;
    }
    .clslist {
      padding: 10px;
      border: 1px solid #DCDFE6;
      box-sizing: border-box;
      // border-radius: 4px;
      line-height: 30px;
      overflow: auto;
      max-height: 50vh;
    }
    .clslist_country {
      width: 25%;
      border-top: 1px solid #DCDFE6;
      border-bottom: 1px solid #DCDFE6; 
      padding: 10px; 
      line-height: 30px;
      overflow: auto;
      max-height: 50vh;
    }
    .tags-container {
      margin-bottom: 10px;
      width: 510px;           /* 设置宽度 */
      height: 150px;
      border: 1px solid #DCDFE6;
      box-sizing: border-box;
      border-radius: 4px;
      padding: 10px; /* Added padding for better appearance */
      overflow: auto; /* Added to prevent overflow issues */
    }
    ::v-deep .row-status-planned    { background: #8ed6f7 !important; }
    ::v-deep .row-status-inprogress { background: #f3e7aa !important; }
    ::v-deep .row-status-completed  { background: #c0f78a !important; }
    ::v-deep .row-status-error      { background: #f99b94 !important; }
    ::v-deep .row-status-canceled   { background: #a69f9f !important; }
</style>
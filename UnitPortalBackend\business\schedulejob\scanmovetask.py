from flask import Flask
import logging
from business.generic.commonfunc import DBConfig
from models.models import db
from business.distributedhosting.nutanix.ntx_move import MOVE
from business.distributedhosting.nutanix_wh.ntx_move_wh import MoveWh


def scan_move_datasynctask():
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    app.app_context().push()
    db.init_app(app)
    logging.info("Scaning Move Data Sync tasks............")
    with app.app_context(): # app.app_context().push()  same.
        move = MOVE()
        move.check_task_status()
        db.session.remove()
        db.engine.dispose()
        db.session.close()


def scan_whmove_datasynctask():
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    app.app_context().push()
    db.init_app(app)
    logging.info("Scaning WH Move Data Sync tasks............")
    with app.app_context(): # app.app_context().push()  same.
        move = MoveWh()
        move.check_task_status()
        db.session.remove()
        db.engine.dispose()
        db.session.close()
from flask import Flask
import datetime, pytz
import logging
from models.database import db
from models.auth_models import ModelUser
from models.sli_models import ModelSLIHostSchema, ModelSLIHost
from models.calendar_models import ModelDHCompensationGain, ModelDHCompensationGainSchema, ModelDHCompensationUse, ModelDHCompensationUseSchema

class Compensation():
    def __init__(self,username=None,token=None):
        # username = username.upper()
        # self.username = username
        self.token = token
    
    def get_memo_id(self):
        try:
            return ModelUser.query.filter_by(token=self.token).first()
        except Exception as e:
            return e    
    def get_compensation_gain(self):
        try:
            return ModelDHCompensationGainSchema(many=True).dump(ModelDHCompensationGain.query.order_by(ModelDHCompensationGain.id.desc()).all())
        except Exception as e:
            return e
    def save_compensation_gain(self,compendata):
        try:
            compendata =dict(compendata)
            user = ModelUser.query.filter_by(token=self.token).first()
            username = user.memo_id
            if "gst" in username.lower():     # user using memo id to login
                username = username[3::]
            # ldapadmin = LdapAdmin(username, password)
            local_tz = pytz.timezone('Asia/Shanghai')
            utc_format = '%Y-%m-%dT%H:%M:%S.%fZ'
            start_utc_dt = datetime.datetime.strptime(compendata['start_date'], utc_format)
            end_utc_dt = datetime.datetime.strptime(compendata['end_date'], utc_format)
            start_local_dt = start_utc_dt.replace(tzinfo=pytz.utc).astimezone(local_tz)
            end_local_dt = end_utc_dt.replace(tzinfo=pytz.utc).astimezone(local_tz)
            gain_hours ='%.2f' % compendata['gain_hours']
            total_hours ='%.2f' % compendata['total_hours']
            __compen__={
                "username"          : username.upper(),
                "start_date"        : start_local_dt.strftime('%Y-%m-%d %H:%M'),
                "reason"            : compendata['reason'],
                "end_date"          : end_local_dt.strftime('%Y-%m-%d %H:%M'),
                "total_hours"       : total_hours,
                "times_1_hours"     : compendata['times_1_hours'],
                "times_1p5_hours"   : compendata['times_1p5_hours'],
                "times_2_hours"     : compendata['times_2_hours'],
                "gain_hours"        : gain_hours,
                "detail"            : compendata['detail']
            }
            compen_record = ModelDHCompensationGain(**__compen__)
            db.session.add(compen_record)
            db.session.commit()
            return True
        except Exception as e :
            return False

    def update_compemsation_gain(self,compendata):
        try:
            local_tz = pytz.timezone('Asia/Shanghai')
            utc_format = '%Y-%m-%dT%H:%M:%S.%fZ'
            if compendata['start_date'].endswith('Z'):
                start_utc_dt = datetime.datetime.strptime(compendata['start_date'], utc_format)
                start_local_dt = start_utc_dt.replace(tzinfo=pytz.utc).astimezone(local_tz)
                start_date = start_local_dt.strftime('%Y-%m-%d %H:%M')
            else:
                start_date = compendata['start_date']
            if compendata['end_date'].endswith('Z'):
                end_utc_dt = datetime.datetime.strptime(compendata['end_date'], utc_format)
                end_local_dt = end_utc_dt.replace(tzinfo=pytz.utc).astimezone(local_tz)
                end_date = end_local_dt.strftime('%Y-%m-%d %H:%M')
            else:
                end_date = compendata['end_date']
            __compen__                   = ModelDHCompensationGain.query.filter_by(id=compendata['id']).first()
            __compen__.start_date        = start_date
            __compen__.end_date          = end_date
            __compen__.reason            = compendata['reason']
            __compen__.total_hours       = '%.2f' % compendata['total_hours']
            __compen__.times_1_hours     = compendata['times_1_hours']
            __compen__.times_1p5_hours   = compendata['times_1p5_hours']
            __compen__.times_2_hours     = compendata['times_2_hours']
            __compen__.gain_hours        = '%.2f' % compendata['gain_hours']
            __compen__.detail            = compendata['detail']
            db.session.commit()
            return True
        except Exception as e :
            return False

    def delete_compensation_gain(self,compendata):
        try:
            ModelDHCompensationGain.query.filter_by(id=compendata['id']).delete()
            db.session.commit()
            return True
        except Exception as e :
            return False

    def get_compensation_use(self):
        try:
            return ModelDHCompensationUseSchema(many=True).dump(ModelDHCompensationUse.query.order_by(ModelDHCompensationUse.id.desc()).all())
        except Exception as e:
            return []
        
    def get_avaliable_compensation(self):
        try:
            users = ModelUser.query.filter_by(location="CN",domain="ikea.com").order_by(ModelUser.memo_id).all()
            print(users)
            compen_list=[]
            for user in users:
                hours = 0
                gain = ModelDHCompensationGain.query.filter_by(username=user.memo_id.upper()).all()
                print(gain)
                for _gain in gain:
                    hours += float(_gain.gain_hours)
                use = ModelDHCompensationUse.query.filter_by(username=user.memo_id.upper()).all()
                print(use)
                for _use in use:
                    hours -= float(_use.use_hours)
                compen_list.append({'userid':user.memo_id.upper(), 'avl_compen':round(hours,2)})
                print(compen_list)
            return compen_list
        except Exception as e:
            print(e)
            hours = 'error' 
        # return hours #return float numbers like 1.5 2.0..


    def save_compensation_use(self,compendata):
        try:
            local_tz = pytz.timezone('Asia/Shanghai')
            utc_format = '%Y-%m-%dT%H:%M:%S.%fZ'
            start_utc_dt = datetime.datetime.strptime(compendata['start_date'], utc_format)
            end_utc_dt = datetime.datetime.strptime(compendata['end_date'], utc_format)
            start_local_dt = start_utc_dt.replace(tzinfo=pytz.utc).astimezone(local_tz)
            end_local_dt = end_utc_dt.replace(tzinfo=pytz.utc).astimezone(local_tz)
            user = ModelUser.query.filter_by(token=self.token).first()
            username = user.memo_id
            if "gst" in username.lower():     # user using memo id to login
                username = username[3::]
            # avaliable_compen_hours = self.get_avaliable_compensation()
            hours = 0
            gain = ModelDHCompensationGain.query.filter_by(username=username.upper()).all()
            for _gain in gain:
                hours += float(_gain.gain_hours)
            use = ModelDHCompensationUse.query.filter_by(username=username.upper()).all()
            for _use in use:
                hours -= float(_use.use_hours)
            print(round(hours,2))
            # avaliable_compen_hours = self.get_avaliable_compensation()
            avaliable_compen_hours = round(hours,2)
            if compendata['total_hours'] <= avaliable_compen_hours:
                __compen__ = {
                    "username"       :     username.upper(),
                    "start_date"     :     start_local_dt.strftime('%Y-%m-%d %H:%M'),
                    "end_date"       :     end_local_dt.strftime('%Y-%m-%d %H:%M'),
                    "use_hours"      :     '%.2f' % compendata['total_hours'],
                    "reason"         :     compendata['reason'],
                    "detail"         :     ''
                }
                compen_record = ModelDHCompensationUse(**__compen__)
                db.session.add(compen_record)
                db.session.commit()
                return True, f'used compen added successfuly.'
            else:
                return False, f'Oooops! Please work day and night for more Compen.'
            
        except Exception as e:
            return False

    def delete_compensation_use(self,compendata):
        try:
            ModelDHCompensationUse.query.filter_by(id=compendata['id']).delete()
            db.session.commit()
            return True
        except Exception as e :
            return False

    def update_compemsation_use(self,compendata):
        try:
            local_tz = pytz.timezone('Asia/Shanghai')
            utc_format = '%Y-%m-%dT%H:%M:%S.%fZ'
            if compendata['start_date'].endswith('Z'):
                start_utc_dt = datetime.datetime.strptime(compendata['start_date'], utc_format)
                start_local_dt = start_utc_dt.replace(tzinfo=pytz.utc).astimezone(local_tz)
                start_date = start_local_dt.strftime('%Y-%m-%d %H:%M')
            else:
                start_date = compendata['start_date']
            if compendata['end_date'].endswith('Z'):
                end_utc_dt = datetime.datetime.strptime(compendata['end_date'], utc_format)
                end_local_dt = end_utc_dt.replace(tzinfo=pytz.utc).astimezone(local_tz)
                end_date = end_local_dt.strftime('%Y-%m-%d %H:%M')
            else:
                end_date = compendata['end_date']
            #update record in GDIT_COMPENSATION_USE
            __compen__                   = ModelDHCompensationUse.query.filter_by(id=compendata['id']).first()
            __compen__.start_date        = start_date
            __compen__.end_date          = end_date
            __compen__.reason            = compendata['reason']
            __compen__.use_hours         = '%.2f' % compendata['total_hours']
            __compen__.detail            = ' '
            db.session.commit()
            return True
        except Exception as e :
            return False

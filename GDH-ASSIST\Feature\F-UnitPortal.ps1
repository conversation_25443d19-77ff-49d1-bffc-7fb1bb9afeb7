function Get-UpToken(){
    <#
    .SYNOPSIS
    The function returns the token for Unit portal.
    
    .DESCRIPTION
    The function returns the token for Unit portal.
    It will use the gst account to get the token if no username/password provided.
    The gst account should have the privilege to get the token from Unit Portal.
    
    .PARAMETER Username
    User name that can access Unit Portal. UPN format is required.
    The default value is $null.
    
    .PARAMETER Password
    Password that can access Unit Portal.
    The default value is $null.
    
    .EXAMPLE
    Get-UpToken
    Get-UpToken -Username <EMAIL> -Password xxx
    
    .NOTES
    General notes
    #>
    param(
        [string] $Username,
        [string] $Password
    )
    
    # if no username/password provided, read from gst account
    if (!($Username -and $Password)) {
        $Account = Read-GstAccount
        $Username = $Account.Username
        $Password = $Account.Password
    }

    $Auth = Get-Base64Auth -Username $Username -PWord $Password

    $Endpoint = "dhapi.ikea.com/api/v1"

    $Token =  (Invoke-UpApi -Endpoint $Endpoint `
                           -Request<PERSON>ri "login" `
                           -Method "POST" `
                           -Auth $Auth `
                           -Headers @{'Content-Type' = 'application/json'}).token

    return $Token
}

function Get-VaultSecViaUp(){
    <#
    .SYNOPSIS
    The function returns the password for Nutanix.
    
    .DESCRIPTION
    The function returns passwords for SiaB and WiaB through DH Unit Portal.
    Gst account with correct privilege is required to get the token and Vault information.
    The password will be pasted in clipboard by default.
    The function will also return the username and last rotated time.
    
    .PARAMETER Prism
    Mandatory parameter, it accept full cluster name or just site name.
    If you use just site name, it will add -NXC000 as default cluster name.
    
    .PARAMETER Tier
    This is now set with "PROD" as default.
    We can add D2/DT/PPE in future if needed.
    
    .PARAMETER Secret
    This is the secret name in Vault.
    The default value is "Site_Pe_Nutanix".
    You can also use "Site_Pe_Admin", "Site_Pe_Svc", "Site_Ahv_Nutanix", "Site_Ahv_Root", 
    "Site_Oob", "Site_Pc_Admin", "Site_Pc_Nutanix", "Site_Pc_Svc", "Site_Witness_Admin" 
    and "Site_Witness_Nutanix".
    
    .PARAMETER Display
    This is a switch parameter.
    If you use this parameter, the password will be displayed in console.
    If you don't use this parameter, the password will be hidden in console.
    The default value is $false.
    
    .EXAMPLE
    Get-VaultSecret -Prism retcn856-nxc000
    Get-VaultSecret -Prism retcn856-nxc000 -Secret Site_Pe_Admin -Display
    Get-VaultSecret -Prism retcn888-nxc000 -Secret Site_Pe_Admin -Tier PPE -Display
    
    .NOTES
    General notes
    #>
    param(
        [string] [parameter(Mandatory = $true)]           $Prism,
        [string] [ValidateSet("PROD", "PPE", "DT", "D2")] $Tier = "PROD",
        [string] [ValidateSet("Site_Pe_Nutanix",
                            "Site_Pe_Admin",
                            "Site_Pe_Svc",
                            "Site_Ahv_Nutanix",
                            "Site_Ahv_Root",
                            "Site_Oob",
                            "Site_Pc_Admin",
                            "Site_Pc_Nutanix",
                            "Site_Gw_Svc",
                            "Site_Pc_Svc",
                            "Site_Witness_Admin",
                            "Site_Witness_Nutanix")]      $Secret = "Site_Pe_Nutanix",
        [switch]                                          $Display
    )

    # Convert Prism to standard Prism object
    $Obj    = ConvertTo-StandardPrism -Name $Prism
    $Prism  = $Obj.PrismName
    $Domain = $Obj.Domain
    $Tier   = $Obj.Tier
    $Fqdn   = $Obj.Fqdn

    # Test gst account before get token from Unit portal
    $GstAccount = Read-GstAccount
    if (Test-AdCredential -Username $GstAccount.username -Password $GstAccount.password) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Gst account is valid, start to get token from Unit portal"
    } else {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Gst account is not valid, please update it before continue"
        return
    }

    # Get unit portal token and auth header
    $Token = Get-UpToken
    $Auth = "Bearer $Token"
    $VaultVars = (Read-Var).Vault

    # Customize body for different prism clusters
    if($Prism -like "DS*" -or $Prism -like "MOD*") {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Start to assemble the body for DS cluster"
        $Namespace = $($VaultVars.WareHouse.MasterNamespace + "/" + ([string]$VaultVars.WareHouse.$Tier.NameSpace).Trim())
        Switch($Tier) {
            "PROD" { $BodyTier = "warehouse" }
            "DT"   { $BodyTier = "warehousedt" }
        }
        $Label = $Prism + "/" + $Secret
    } else {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Start to assemble the body for Retail cluster"
        $Namespace = $($VaultVars.Retail.MasterNamespace + "/" + ([string]$VaultVars.Retail.$Tier.NameSpace).Trim())
        Switch($Tier) {
            "PROD" { $BodyTier = "production" }
            "PPE"  { $BodyTier = "preproduction" }
            "DT"   { $BodyTier = "ikeadt" }
            "D2"   { $BodyTier = "ikead2" }
        }
        $Label = $Prism + "/" + $Secret
    }
    
    # Assemble the body to query result from vault
    $Body = @{
        "namespace" = $Namespace
        "tier" = $BodyTier
        "labels"    = @($Label)
    }
    Write-ConsoleLog  -Message "Start to query password for $BodyTier $Namespace"
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Start to query password for $Label"
    $Res = Invoke-UpApi -Endpoint "dhapi.ikea.com/api/v1" `
                    -RequestUri "vault/data" `
                    -Method "POST" `
                    -Auth $Auth `
                    -Body $Body

    # Formulate the result to return
    if($Res) {
        $KV = [PSCustomObject]@{
            "Prism"        = $Prism
            "Secret"       = $Secret
            "Username"     = $Res.$Label.username
            "Last_Rotated" = $Res.$Label.last_autoRotate
        }
        Set-Clipboard -Value $Res.$Label.secret
        switch ($Display){
            $true {
                $KV | Add-Member -MemberType NoteProperty -Name 'Password' -Value $Res.$Label.secret
            }
            $false {
                $KV | Add-Member -MemberType NoteProperty -Name 'Password' -Value 'Only Available In Your Clipboard' # not showing password in console without Display parameter
            }
        }
    }

    return $KV
}
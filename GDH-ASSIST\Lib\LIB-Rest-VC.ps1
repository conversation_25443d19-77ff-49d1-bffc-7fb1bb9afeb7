function Invoke-VC-API{
    param(
        [string]                                     $VCAddress,
        [string]                                     $RequestURI,
        [string] [ValidateSet("GET", "POST", "PUT")] $Method,
        [object]                                     $Headers,
        [object]                                     $Body,
        [int]                                        $MaxTry = 5,
        [int]                                        $TimeoutSec = 30
    )
    if ($MaxTry) {
        $Payload = @{
            'Uri'         = "https://$($VCAddress)/$($RequestURI)"
            'Method'      = $Method
            'Headers'     = $Headers
            'ContentType' = "application/json"
            'TimeoutSec'  = $TimeoutSec
        }
        if ($Body) {
            $Payload['$Body'] = $Body
        }
        try {
            return Invoke-RestMethod @Payload
        }
        catch {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling $VCAddress for $RequestURI. Cause: $_ Retry in 5 seconds"
            Start-Sleep 5
            return Invoke-VC-Api -VCAddress $VCAddress `
                                 -RequestURI $RequestURI `
                                 -Method $Method `
                                 -Headers $Headers `
                                 -Body $Body `
                                 -MaxTry $($MaxTry - 1) `
                                 -TimeoutSec $($TimeoutSec + 5)
        }
    }else {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling $VCAddress for $RequestURI."
        return $null
    }
}
function Rest-VC-Get-Session(){
    param(
        [string] $VCAddress,
        [string] $VCUsername,
        [string] $VCPassword
    )
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are getting the VC session from $VCAddress as the user of $VCUsername"
    return Invoke-VC-API -VCAddress $VCAddress `
                         -RequestURI "rest/com/vmware/cis/session" `
                         -Method POST `
                         -Headers @{"authorization" = $(Get-Base64Auth -Username $VCUsername -PWord $VCPassword)}
}
function Rest-VC-List-Host(){
    param(
        [string] [Parameter(Mandatory=$true)]                 $VCAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $VCUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $VCPassword,
        [string] [Parameter(ParameterSetName = 'Session')]    $Session,
        [array]                                               $Clusters
    )
    $RequestURI = "rest/vcenter/host"
    $Filters = @()
    if ($Clusters) {
        $i = 1
        $Clusters | % {
            $Filters += "filter.clusters.$i=$_"
            $i ++
        }
    }
    if ($Filters.Length) {
        $RequestURI += "?"
        $Filters | % {
            $RequestURI += $_ + "&"
        }
        $RequestURI = $RequestURI.Substring(0, $RequestURI.Length - 1)
    }
    if (!$Session) {
        $Session = (Rest-VC-Get-Session -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    }       
    return Invoke-VC-API -VCAddress $VCAddress `
                         -RequestURI $RequestURI `
                         -Method GET `
                         -Headers @{"vmware-api-session-id" = $Session}
}

function Rest-VC-List-Cluster(){
    param(
        [string] [Parameter(Mandatory=$true)]                 $VCAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $VCUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $VCPassword,
        [string] [Parameter(ParameterSetName = 'Session')]    $Session,
        [array]                                               $Names
    )
    $RequestURI = "rest/vcenter/cluster"
    $Filters = @()
    if ($Names) {
        $i = 1
        $Names | % {
            $Filters += "filter.names.$i=$_"
            $i ++
        }
    }
    if ($Filters.Length) {
        $RequestURI += "?"
        $Filters | % {
            $RequestURI += $_ + "&"
        }
        $RequestURI = $RequestURI.Substring(0, $RequestURI.Length - 1)
    }
    if (!$Session) {
        $Session = (Rest-VC-Get-Session -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    }
    return Invoke-VC-API -VCAddress $VCAddress `
                         -RequestURI $RequestURI `
                         -Method GET `
                         -Headers @{"vmware-api-session-id" = $Session}
}
function Rest-VC-Get-Cluster(){
    param(
        [string] [Parameter(Mandatory=$true)]                 $VCAddress,
        [string] [Parameter(Mandatory=$true)]                 $Cluster,
        [string] [Parameter(ParameterSetName = 'Credential')] $VCUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $VCPassword,
        [string] [Parameter(ParameterSetName = 'Session')]    $Session
        
    )
    if (!$Session) {
        $Session = (Rest-VC-Get-Session -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    }
    return Invoke-VC-API -VCAddress $VCAddress `
                         -RequestURI "rest/vcenter/cluster/$($Cluster)" `
                         -Method GET `
                         -Headers @{"vmware-api-session-id" = $Session}
}
function Rest-VC-List-VM(){
    param(
        [string] [Parameter(Mandatory=$true)]                 $VCAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $VCUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $VCPassword,
        [string] [Parameter(ParameterSetName = 'Session')]    $Session
    )
    ##$RequestURI = "rest/vcenter/vm"
    $RequestURI = "api/virtual_machines"
    if (!$Session) {
        $Session = (Rest-VC-Get-Session -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    }
    return Invoke-VC-API -VCAddress $VCAddress `
                         -RequestURI $RequestURI `
                         -Method GET `
                         -Headers @{"vmware-api-session-id" = $Session}
}
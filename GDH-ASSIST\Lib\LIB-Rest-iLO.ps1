function Invoke-iLO-API(){
    param(
        [string]                                                        $iLOAddress,
        [string]                                                        $RequestURI,
        [string] [ValidateSet("GET", "POST", "PUT", "DELETE", "PATCH")] $Method,
        [object]                                                        $Headers,
        [object]                                                        $Body,
        [string] [ValidateSet("WebRequest", "RestMethod")]              $Channel = "RestMethod",
        [int]                                                           $MaxTry = 3,
        [int]                                                           $TimeoutSec = 30
    )
    if ($MaxTry) {
        $Payload = @{
            'Uri'                  = "https://$($iLOAddress)/redfish/v1/$($RequestURI)"
            'Method'               = $Method
            'ContentType'          = "application/json"
            'SkipCertificateCheck' = $true
            'TimeoutSec'           = $TimeoutSec
        }
        if($Headers){
            $Payload['Headers'] = $Headers
        }
        if($Body){
            $Payload['Body'] = $Body
        }
        try {
            switch ($Channel){
                "WebRequest" {
                    return Invoke-WebRequest @Payload
                }
                "RestMethod" {
                    return Invoke-RestMethod @Payload
                }
            }
        } catch {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling $iLOAddress for $RequestURI. Cause: $_ Retry in 5 seconds"
            Start-Sleep 5
            return Invoke-iLO-API -iLOAddress $iLOAddress `
                                  -RequestURI $RequestURI `
                                  -Method $Method `
                                  -Headers $Headers `
                                  -Body $Body `
                                  -Channel $Channel `
                                  -MaxTry $($MaxTry - 1) `
                                  -TimeoutSec $($TimeoutSec + 5)
        }
    } else {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling $iLOAddress for $RequestURI."
        return $null
    }
}

function Rest-iLO-Get-Bios(){
    param(
        [string]                                              $iLOAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                    [Parameter(ParameterSetName = 'Session')] $Session
    )
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are getting the iLO Bios from $iLOAddress"
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if ($Session) {
        return $(Invoke-iLO-API -iLOAddress $iLOAddress `
                                -RequestURI "Systems/1/bios" `
                                -Method Get `
                                -Headers @{'X-Auth-Token' = $Session} `
                                -Channel RestMethod)
    }
    Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "We are unable to get session from $iLOAddress"
    return $null
}

function Rest-iLO-Get-Managers(){
    param(
        [string]                                              $iLOAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                    [Parameter(ParameterSetName = 'Session')] $Session
    )
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are getting the iLO managers from $iLOAddress"
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if ($Session) {
        return $(Invoke-iLO-API -iLOAddress $iLOAddress `
                                -RequestURI "managers/1/" `
                                -Method Get `
                                -Headers @{'X-Auth-Token' = $Session} `
                                -Channel RestMethod)
    }
    Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "We are unable to get session from $iLOAddress"
    return $null
}

function Rest-iLO-Get-Powerstate () {
    param(
        [string]                                             $iLOAddress,
        [string][Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string][Parameter(ParameterSetName = 'Credential')] $iLOPassword
    )
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if ($Session) {
        return $(Invoke-iLO-API -iLOAddress $iLOAddress `
                                -RequestURI "Systems/1/" `
                                -Method GET `
                                -Headers @{"X-Auth-Token" = $Session} `
                                -Channel RestMethod)
    }
}

function Rest-iLO-Get-Session(){
    param(
        [string] $iLOAddress,
        [string] $iLOUsername,
        [string] $iLOPassword
    )
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are getting the iLO session from $iLOAddress as the user of $iLOUsername"
    return  $(Invoke-iLO-API -iLOAddress $iLOAddress `
                             -RequestURI "SessionService/Sessions/" `
                             -Method POST `
                             -Body $(@{'UserName' = $iLOUsername; 'Password' = $iLOPassword} | ConvertTo-Json) `
                             -Channel WebRequest).Headers.'X-Auth-Token'
}

function Rest-iLO-Get-Systems(){
    param(
        [string]                                              $iLOAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                    [Parameter(ParameterSetName = 'Session')] $Session
    )
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are getting the iLO systems from $iLOAddress"
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if ($Session) {
        return $(Invoke-iLO-API -iLOAddress $iLOAddress `
                                -RequestURI "Systems/1/" `
                                -Method Get `
                                -Headers @{'X-Auth-Token' = $Session} `
                                -Channel RestMethod)
    }
    Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "We are unable to get session from $iLOAddress"
    return $null
}

function Rest-iLO-5-Reset-BMC(){
    param(
        [string]                                              $iLOAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                    [Parameter(ParameterSetName = 'Session')] $Session
    )
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress `
                                        -iLOUsername $iLOUsername `
                                        -iLOPassword $iLOPassword
    }
    $Body = @{
        'method'      = "reset_ilo"
        'cause'       = "config"
        'session_key' = $Session
    }
    $Cookie        = New-Object System.Net.Cookie
    $Cookie.Name   = "sessionKey"
    $Cookie.Value  = $Session
    $Cookie.Domain = $iLOAddress
    $WebSession    = New-Object Microsoft.PowerShell.Commands.WebRequestSession
    $WebSession.Cookies.Add($Cookie)
    $Payload = @{
        'Uri'                  = "https://$($iLOAddress)/json/ilo_status"
        'Method'               = "POST"
        'Headers'              = @{'Authorization' = "X-Auth-Token $Session"}
        'Body'                 = $Body | ConvertTo-Json -Depth 2
        'ContentType'          = "application/json"
        'SkipCertificateCheck' = $true
        'WebSession'           = $WebSession
    }
    try {
        return Invoke-RestMethod @Payload
    } catch {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "$($iLOAddress), $($_)"
    }
    return $null
}

function Rest-iLO-Reset-Power() {
    param(
        [string]                                                        $iLOAddress,
        [string][Parameter(ParameterSetName = 'Credential')]            $iLOUsername,
        [string][Parameter(ParameterSetName = 'Credential')]            $iLOPassword,
        [string][Parameter(Mandatory = $true)][ValidateSet("On","Off")] $Type
    )

    switch ($Type) {
        "On" { $Body = @{"ResetType" = "On"} | ConvertTo-Json }
        "Off" { $Body = @{"ResetType" = "ForceOff"} | ConvertTo-Json }
    }

    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if ($Session) {
        return $(Invoke-iLO-API -iLOAddress $iLOAddress `
                                -RequestURI "Systems/1/Actions/ComputerSystem.Reset" `
                                -Method POST `
                                -Headers @{"X-Auth-Token" = $Session} `
                                -Body $Body `
                                -Channel RestMethod)
    }
}

function Rest-iLO-4-Get-NetworkService(){
    param(
        [string]                                              $iLOAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                    [Parameter(ParameterSetName = 'Session')] $Session
    )
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are getting the iLO network service from $iLOAddress"
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if ($Session) {
        return $(Invoke-iLO-API -iLOAddress $iLOAddress `
                                -RequestURI "Managers/1/NetworkService" `
                                -Method GET `
                                -Headers @{"X-Auth-Token" = $Session} `
                                -Channel RestMethod)
    }
    Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "We are unable to get session from $iLOAddress"
    return $null
}

function Rest-iLO-5-Get-NetworkService(){
    param(
        [string]                                              $iLOAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                    [Parameter(ParameterSetName = 'Session')] $Session
    )
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are getting the iLO network service from $iLOAddress"
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if ($Session) {
        return $(Invoke-iLO-API -iLOAddress $iLOAddress `
                                -RequestURI "Managers/1/NetworkProtocol" `
                                -Method GET `
                                -Headers @{"X-Auth-Token" = $Session} `
                                -Channel RestMethod)
    }
    Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "We are unable to get session from $iLOAddress"
    return $null
}

function Rest-iLO-Get-EthernetInterfaces-1(){
    param(
        [string]                                              $iLOAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                    [Parameter(ParameterSetName = 'Session')] $Session
    )
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are getting the iLO ethernet interfaces from $iLOAddress"
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if ($Session) {
        return $(Invoke-iLO-API -iLOAddress $iLOAddress `
                                -RequestURI "Managers/1/EthernetInterfaces/1" `
                                -Method GET `
                                -Headers @{"X-Auth-Token" = $Session} `
                                -Channel RestMethod)
    }
    Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "We are unable to get session from $iLOAddress"
    return $null
}

#2 for System ROM
function Rest-iLO-5-Get-FirmwareInventory-2(){
    param(
        [string]                                              $iLOAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                    [Parameter(ParameterSetName = 'Session')] $Session
    )
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if ($Session) {
        return $(Invoke-iLO-API -iLOAddress $iLOAddress `
                                -RequestURI "UpdateService/FirmwareInventory/2" `
                                -Method GET `
                                -Headers @{"X-Auth-Token" = $Session} `
                                -Channel RestMethod)
    }
    Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "We are unable to get session from $iLOAddress"
    return $null
}

function Rest-iLO-5-GenerateCSR(){
    param(
        [string]                                              $iLOAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                    [Parameter(ParameterSetName = 'Session')] $Session
    )
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
}

# Get all firmware inventory info for iLO5
function Rest-iLO-5-GetFirmwareInventoryAll(){
    param(
        [string]                                              $iLOAddress,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string] [Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                    [Parameter(ParameterSetName = 'Session')] $Session
    )
    if(!$Session){
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if($Session){
        $Uri = 'https://' + $iLOAddress + '/redfish/v1/UpdateService/FirmwareInventory/?$expand=.&_=1'
        $Firmwares = (Invoke-RestMethod -Uri $Uri `
                                        -Method Get `
                                        -Headers @{'Authorization' = Get-Base64Auth -Username $iLOUsername -PWord $iLOPassword}).Members
        Return $Firmwares
    }
    Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "We are not able to get session from $iLOAddress"
    return $null
}

function Rest-iLO-UpdateNTPServer() {
    param(
        [string]                                             $iLOAddress,
        [string][Parameter(ParameterSetName = 'Credential')] $iLOUsername,
        [string][Parameter(ParameterSetName = 'Credential')] $iLOPassword,
                                                             $Body
    )
    if (!$Session) {
        $Session = Rest-iLO-Get-Session -iLOAddress $iLOAddress -iLOUsername $iLOUsername -iLOPassword $iLOPassword
    }
    if ($Session) {
        return $(Invoke-iLO-API -iLOAddress $iLOAddress `
                                -RequestURI "Managers/1/DateTime" `
                                -Method PATCH `
                                -Headers @{"X-Auth-Token" = $Session} `
                                -Body $Body `
                                -Channel RestMethod)
    }
}

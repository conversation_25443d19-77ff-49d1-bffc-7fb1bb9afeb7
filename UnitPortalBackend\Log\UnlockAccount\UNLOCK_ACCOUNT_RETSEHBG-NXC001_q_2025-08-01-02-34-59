2025-08-01 10:34:59,550 INFO Start to run the task.
2025-08-01 10:34:59,560 INFO Starting to unlock account for PE: RETSEHBG-NXC001
2025-08-01 10:34:59,571 INFO Getting PE information
2025-08-01 10:34:59,621 INFO Found PE in retail: retsehbg-nxc001.ikea.com, PC: ssp-eu1-ntx.ikea.com
2025-08-01 10:34:59,632 INFO Executing account unlock operation
2025-08-01 10:34:59,642 INFO Connecting to Nutanix cluster...
2025-08-01 10:35:01,426 INFO Unlocking accounts...
2025-08-01 10:35:01,426 INFO Trying to SSH to the pe retsehbg-nxc001.
2025-08-01 10:35:01,427 INFO SSH connecting to retsehbg-nxc001.ikea.com, this is the '1' try.
2025-08-01 10:35:03,992 INFO SSH connected to retsehbg-nxc001.ikea.com.
2025-08-01 10:35:04,057 ERROR Task failed. Detail: ['Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\base_up_task.py", line 88, in start_task\n    self.task_process()\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\toolbox\\unlock_account_task.py", line 63, in task_process\n    cli.unlock_account(prism_name=self.pe_name, pc_model_class=pc_model_class, pe_model_class=pe_model_class)\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\nutanix.py", line 2674, in sec\n    return func(self, *args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\nutanix.py", line 2809, in unlock_account\n    tier = ModelNtxBenchmark.query.filter_by(id=pe_prism_info.bmk_id).first().tier\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', "AttributeError: 'NoneType' object has no attribute 'tier'\n"]

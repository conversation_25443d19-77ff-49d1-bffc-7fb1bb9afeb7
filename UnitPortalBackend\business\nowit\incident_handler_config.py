"""
╔═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
║Module: incident handler config                                                                                                        ║
║                                                                                                                                       ║
║Author:         Incident Devourer                                                                                                      ║
║Created:        2024-08-01                                                                                                             ║
║Last Updated:   2024-08-16                                                                                                             ║
║                                                                                                                                       ║
║Description:                                                                                                                           ║
║Parameters needed for incident handler scripts                                                                                         ║
║                                                                                                                                       ║
║Usage:                                                                                                                                 ║
║It stores and manages parameters needed for incident handler scripts.                                                                   ║
║                                                                                                                                       ║
║Update Logs:                                                                                                                           ║
║-[Date]-[Author]-[Update content]                                                                                                      ║
╚═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
"""


class IncHandlerCfg:
    INC_ASSIGN: str = 'inc_assign'
    INC_CHK: str = 'inc_chk'
    NX_ALERT_CHK: str = 'nx_alert_chk'
    INC_UNASSIGN: str = 'inc_unassign'
    INC_ASSIGNEE: str = 'ivfan'
    INC_RESOLVED: str = 'inc_resolved'
    NX_INC_SKIPPED_REPL_PREFIX = '[Human-2 Remote site configed or No RS backup] '
    NX_INC_SHORT_DESC_PREFIX = '[Human'
    NX_INC_SHORT_DES_PREFIX_HUMAN = '[Human] '
    WTP_INC_SHORT_DESC_PREFIX = '[Human] '
    ILO_API_PREFIX = '[ilo API failed]'
    NX_API_PREFIX = '[NTX API failed]'
    NX_SSH_CVM_NAME = 'nutanix'
    """
    <nutanix alerts type defination>
    ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
    """
    zookeeper_not_active_uuid = 'A111009'
    nx_cvm_reboot_alert_uuid: str = 'A1024'
    nx_connectivity_to_remote_uuid: str = 'A110002'
    nutanix_host_connection_failed_uuid: str = 'A1137'
    nutanix_cluster_service_restart_uuid: str = 'A3034'
    NX_VM_MISSING_ENTITIES_UUID: str = 'A110265'   
    NX_VM_FAILED_SNAPSHOT_ENTITIES_UUID: str = 'A130088'
    NX_UNABLE_LOCATE_VM_UUID: str = 'A130083'
    NX_POWER_SUPPLY_UUID: str = 'A1046'
    NX_DISK_MARKED_OFFLINE_UUID: str = 'A1044'
    nx_ahv_incompatible_uuid: str = 'A111061'
    nx_ahv_crash_file_uuid: str = 'A111053'
    nx_host_link_down_uuid: str = 'A1082'
    nx_cvm_service_restart_uuid: str = 'A3037'
    NX_CVM_TIME_SYNC_UUID: str = 'A3026'
    NX_CVM_FANOUTPORT_UUID: str = 'A805201'
    NX_PE_PC_CONNECTION_UUID: str = 'A200802'
    #"Protection domain {protection_domain_name} replication to remote site {remote_name} failed. {reason}."
    NX_PD_NOT_ANY_PROG_UUID: str = 'A130137'
    NX_PD_REPL_FAILED_UUID: str = 'A1015' 
    NX_IDF_NOT_SYNC_UUID: str = 'A200001'
    NX_NIC_FLAPS_UUID: str = 'A3066'
    NX_CVM_DISCED_UUID: str = 'A1001'
    NX_PULSE_REST_UUID: str = 'A140001'
    NX_NTP_NOT_CONF_UUID: str = 'A3026'  #NTP is not configured on CVM
    NX_HOST_NOT_SYNC_NTP_UUID: str = 'A103090' #The Host time not synchronized with any NTP server
    NX_CVM_TIME_DIFF_UUID: str = 'A1017'
    NX_DISK_HIGH_UUID: str = 'A1031'
    NX_SKIPPED_REPL_UUID: str = 'A1113' #RETxxyyy-NXC000#Skipped Replication Of Snapshot For RETxxyyy-NXC000-Gold_CCG
    NX_NET_MAP_INVALID_UUID: str = 'A1157'
    NX_NOT_SUPPORT_RPO_UUID: str = 'A111083'
    NX_CALM_TRIAL_EXPIRED_UUID: str = 'A14007'
    NX_REPL_OF_PD_UUID: str = 'A130137'
    NX_LIC_VIO_UUID: str = 'A1077'
    NX_EXT4_UUID: str = 'A3038'
    SIAB_HOST_DEFAULT_PW_UUID: str = 'A6219'
    CONNECT_ISSUE_CLUSTER_UUID: str = 'A200000'
    IPMI_MISMSTCH_UUID: str = 'A1008'

    """
    ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
    <nutanix alerts type defination>
    """
    #................................................
    """
    <SiaB models regular expression>
    ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
    """
    NX_IDF_NOT_SYNC_PAT: str = r"(.*)#IDF Source to Remote Sync Status"
    NX_PD_NOT_ANY_PROG_PAT: str = r"(.*)#Replication of protection domain.*?has"
    NX_PD_REPL_FAILED_PAT: str = r"(.*)#Protection domain.*?replication fail"
    cvm_reboot_pattern: str = r'\bCVM\b.*\brebooted\b'
    zookeeper_not_active_pattern = r"(.*)#Zookeeper Not Active on All CVMs"
    NX_CLUSTER_NAME_PAT: str = r'(null|([^\s#-]+-[^\s#]+))(?=#)'
    NX_INC_ORIG_DESC_PAT: str = r'((\w+-\w+#.*|\w+:null#.*$)|(WiaB:\w+:\w+-[\w\s]+\*\*.*|WiaB:.*))'
    NX_NO_MODEL_ORIG_DESC_PAT: str = r'\[.*?\]|([^\[\]]+)'
    wtp_host_connection_original_description_pattern: str = r'(WTP.*$)'
    wtp_host_connection_failure_pattern: str = r'WTP.*\|.*\| (.*?) \| Host connection failure'
    wtp_power_supply_loss_pattern: str = r'\| power supply \d (input power loss)$|power supplies not redundant'
    wtp_power_supply_host: str = r'\| ([a-zA-Z0-9-]+) \| power suppl'
    nutanix_host_connection_failure_pattern: str = r'(.*)#Host (\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) appears to have failed.$'
    nx_host_link_down_pattern: str = r'(.*)#NIC link down on host (\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
    remote_site_connectivity_pattern: str = r".*Remote site connectivity not normal.*"
    nutanix_cluster_service_restart_pattern: str = r"(.*)#Cluster Service '"
    nx_cvm_service_restart_pattern: str = r"(.*)#CVM.*Service\(s\) Restarting Frequently"
    NX_SKIPPED_REPL_PAT: str = r"(.*)#Skipped Replication Of Snapshot For (.*)"
    nx_vm_not_found_pattern: str = r'Protected VM\(s\) Not Found|Failed To Snapshot Entities|Latest snapshot of protection domain is missing entities'
    NX_GET_PD_PAT: str = r"protection domain\s+(\S+)"  # 不带 ‘ ’ 的 protection domain
    NX_GET_PD_PAT2: str = r"protection domain\s+'([^']+)'"  # 带 ‘ ’ 的protection domain  Unable to loacte...
    NX_GET_PD_PAT3: str = r"protection domain\s+(\S+?),"  # using for Failed to...
    NX_GET_PE_NAME_PAT: str = r'(?:\[[^\]]*\]\s*-?\s*)?(?:SiaB:)?(.*?)(?=#)'
    nx_disk_offline_pattern: str = r"#Disk? mounted|Disks? mounted"
    nx_disk_sn_pattern: str = r'/disks/([A-Z0-9]+)'
    NX_VM_NAME_PAT: str = [
        r"VM\(s\) (.*?) protected by protection domain",
        r'entities (.*?) in',
        r'missing entities\s+(.*?)\s*#'
    ]
    nx_power_supply_down_pattern: str = r'Power supply.*?is Down'
    nx_SN_pattern: str = r'Down on block (\w+)'
    nx_ahv_incompatible_pattern: str = r'#Recent AHV Crash File Detected |#Detected incompatible AHV'
    NX_AHV_CRASH_PAT: str = r'#Recent AHV Crash File Detected'
    nx_inc_cluster_id: str = r"#ClusterUUID:([\w-]+)"
    NX_PE_PC_CONNECTION_PATTERN: str = r'#PE-PC Connection Failure'
    NX_FANOUTPORT_PATTERN: str = r'#Fanout Secure Port Connection to PC'
    NX_NTP_NOT_CONF_PAT: str = r'#NTP is not configured'
    NX_CVM_TIME_DIFF_PAT: str = r'#CVM Time Difference High'
    NX_TIME_NOT_SYNC_PAT: str = r'(SiaB:)?.*#.*time not synchronized'
    IP_PATTERN: str = r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'
    IP_PATTERN2: str = r'(\d+\.\d+\.\d+\.\d+)\s+.*?\s'  # get ip with key string
    CLUSTER_SUFFIX_PATTERN: str = r'-(.*)'
    ECOX_INC_PAT: str = r'^EcoX.*'
    NX_CVM_DISCONED_PAT: str = r'disconnected from network'
    NX_PULSE_INC_PAT: str = r'#Pulse cannot connect to REST'
    NX_DISK_HIGH_INC_PAT: str = r'#Disk space usage high'
    NX_DISK_USAGE_PAT: str = r'(\d+)%\s+/home\b'
    NX_EXT4_DISK_ERR_PAT: str = r'#File system inconsistencies'
    NX_NIC_FLAPS_INC_PAT: str = r'#NIC Flaps detected'
    NX_META_DATA_INC_PAT: str = r'#Node Marked To Be Detached From Metadata Ring'
    PE_UUID_PATTERN: str = r'ClusterUUID:([0-9a-fA-F-]+)'
    PC_ADDRESS_PATTERN: str = r'https://(.*?)\:9440'
    NET_MAPPING_IVALID_PAT: str = r'Network Mapping Invalid'
    DO_NOT_SUPPORT_RPO_PAT: str = r'do not support RPO under'
    CALM_TRIAL_EXPIRY_PAT: str = r'#Calm Trial License Expiry'
    REPLICATION_OF_PD_PAT: str = r'#Replication of protection domain'
    NX_LIC_VIO_PAT: str = r'#License Feature V'
    SIAB_HOST_DEFAULT_PW: str = r'#Host (\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}) is using default password'
    SIAB_IAM_AUTH_DB_ERR: str = r'#IAMv2 authentication Database Connectivity'
    CURR_MEMORY_CONFIG_PAT: str = r'#Current memory configuration on nod'
    CONNECT_ISSUE_CLUSTER: str = r'#Connectivity issues on cluster'
    FANOUT_IP_PAT: str = r"\('\d+\.\d+\.\d+\.\d+', \d+\)"
    CVM_IP_PAT: str = r"CVM IP:\s*(\d+\.\d+\.\d+\.\d+)"
    CVM_IP_PAT2: str = r"Controller VM \s*(\d+\.\d+\.\d+\.\d+)"
    IPMI_MISMATCH_PAT: str = r'IPMI IP Address Mismatch'
    SIAB_IPMI_PAT: str = r'(SiaB:).*?(\bIPMI IP Address Mismatch\b)'
    """
    ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
    <nutanix models regular expression>
    """
    # ................................................
    """
       <WiaB models regular expression>
       ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
    """
    WIAB_INC_ORIG_PAT: str = r'WiaB:\w+:\w+-[\w\s]+\*\*.*|WiaB:.*'  # WiaB:Warning:dsxxx-nx7001 **|WiaB:
    NX_WIAB_GET_PE_NAME_PAT: str = r'WiaB:\w+:(.*?) \*\*'
    WIAB_PE_NAME_PAT: str = r'UTC\s*\n([^:]+):'  # from description get cluster name
    WIAB_PE_NAME_PAT2: str = r'(ds[^*]*?) \*\*'  # from short description get cluster name

    WIAB_NTP_NOT_CONF_PAT: str = r'(WiaB:).*?(\bThe Host\b).*?\(.*?\)\s+(time not synchronized)'
    WIAB_CVM_TIME_SYNC_PATTERN: str = r'(WiaB:).*?(\bCVM\b).*?\(.*?\)\s+(time not synchronized)'
    WIAB_NIC_LINK_PAT: str = r'\*\* NIC link down on host (\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
    WIAB_FANOUT_PAT: str = r'(WiaB:).*?(\bFanout Secure Port\b)'
    WIAB_IPMI_PAT: str = r'(WiaB:).*?(\bIPMI IP Address Mismatch\b)'

    """
       ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
       <nutanix models regular expression>
    """
    # ................................................
    INC_STATE_NEW: int = 1
    INC_STATE_IN_PROGRESS: int = 2
    INC_STATE_ON_HOLD: int = 3
    INC_STATE_RESOLVED: int = 6
    INC_STATE_CANCELED: int = 8
    INC_STATE_CLOSED: int = 7
    INC_URGENCY_LOW: str = '3'
    INC_URGENCY_MED: str = '2'
    INC_URGENCY_HIGH: str = '1'
    INC_IMPACT_LOW: str = '3'
    INC_IMPACT_MED: str = '2'
    INC_IMPACT_HIGH: str = '1'
    LIT_GROUP = [
        "LOCALIT-AT - Local IT Support Austria",
        "LOCALIT-AU - Local IT Support Australia",
        "LOCALIT-BE - Local IT Support Belgium",
        "LOCALIT-CA - Local IT Support Canada",
        "LOCALIT-CH - Local IT Support Switzerland",
        "LOCALIT-CN - Local IT Support China",
        "LOCALIT-CZ - Local IT Support Czech Republic",
        "LOCALIT-DE - Local IT Support Germany",
        "LOCALIT-DK - Local IT Support Denmark",
        "LOCALIT-ES - Local IT Support Spain",
        "LOCALIT-FR - Local IT Support France",
        "LOCALIT-HR - Local IT Support Croatia",
        "LOCALIT-HU - Local IT Support Hungary",
        "LOCALIT-IT - Local IT Support Italy",
        "LOCALIT-IN - Local IT Support India",
        "LOCALIT-JP - Local IT Support Japan",
        "LOCALIT-KR - Local IT Support South Korea",
        "LOCALIT-NL - Local IT Support the Netherlands",
        "LOCALIT-NO - Local IT Support Norway",
        "LOCALIT-PL - Local IT Support Poland",
        "LOCALIT-PT - Local IT Support Portugal",
        "LOCALIT-RO - Local IT Support Romania",
        "LOCALIT-RS - Local IT Support Serbia",
        "LOCALIT-US - Local IT Support USA",
        "LOCALIT-SE - Local IT Support Sweden"
    ]
    MODEL_MAPPING = {
            cvm_reboot_pattern: 'SiaB-CVM reboot',
            nx_vm_not_found_pattern: 'SiaB-VMs not found',
            nutanix_host_connection_failure_pattern: 'SiaB-Host connection failed',
            nx_cvm_service_restart_pattern: 'SiaB-CVM services restart',
            nutanix_cluster_service_restart_pattern: 'SiaB-Cluster services restart',
            NX_TIME_NOT_SYNC_PAT: 'SiaB-Time not sync',
            NX_DISK_HIGH_INC_PAT: 'SiaB-Disk high usage',
            nx_power_supply_down_pattern: 'SiaB-Power supply down',
            nx_ahv_incompatible_pattern: 'SiaB-Incompatible AHV version',
            nx_disk_offline_pattern: 'SiaB-Disk offline',
            NX_CVM_DISCONED_PAT: 'SiaB-CVM disconnection',
            nx_host_link_down_pattern: 'SiaB-Host NIC link down',
            NX_PE_PC_CONNECTION_PATTERN: 'SiaB-PE-PC Connection',
            remote_site_connectivity_pattern: 'SiaB-Remote site connection',
            NX_PD_NOT_ANY_PROG_PAT: 'SiaB-Backup',
            NX_PD_REPL_FAILED_PAT: 'SiaB-Backup',
            NX_SKIPPED_REPL_PAT: 'SiaB-Backup',
            NX_IDF_NOT_SYNC_PAT: 'SiaB-IDF Remote Sync',
            NX_NIC_FLAPS_INC_PAT: 'SiaB-NIC Flaps',
            NX_PULSE_INC_PAT: 'SiaB-connection issue',
            ECOX_INC_PAT: 'SiaB-EcoX alert',
            NX_META_DATA_INC_PAT: 'SiaB-Metadata ring',
            NX_CVM_TIME_DIFF_PAT: 'SiaB-CVM time difference',
            NX_NTP_NOT_CONF_PAT: 'SiaB-NTP not configured',
            NX_FANOUTPORT_PATTERN: 'SiaB-Fanout port not reachable',
            NX_AHV_CRASH_PAT: 'SiaB-AHV Crash File',
            zookeeper_not_active_pattern: 'SiaB-Zookeeper not active',
            NX_EXT4_DISK_ERR_PAT: 'SiaB-Host Disk issue',
            DO_NOT_SUPPORT_RPO_PAT: 'SiaB-CVM RAM CPU insufficient',
            NET_MAPPING_IVALID_PAT: 'SiaB-Net mapping invalid',
            SIAB_HOST_DEFAULT_PW: 'SiaB-Default psd',
            NX_LIC_VIO_PAT: 'SiaB-License Violation',
            CALM_TRIAL_EXPIRY_PAT: 'SiaB-Calm Trial License Expiry',
            wtp_host_connection_failure_pattern: 'WTP-Host connection',
            wtp_power_supply_loss_pattern: 'WTP-Power supply',
            WIAB_NTP_NOT_CONF_PAT: 'WiaB-Host time not sync',
            WIAB_CVM_TIME_SYNC_PATTERN: 'WiaB-CVM time not sync',
            WIAB_FANOUT_PAT: 'WiaB-Fanout Secure Port',
            WIAB_NIC_LINK_PAT: 'WiaB-Host link down',
            SIAB_IAM_AUTH_DB_ERR: 'SiaB-IAMv2 error',
            CURR_MEMORY_CONFIG_PAT: 'SiaB-Mem config issue',
            SIAB_IPMI_PAT: 'SiaB-IPMI ip mismatch',
            WIAB_IPMI_PAT: 'WiaB-IPMI ip mismatch',
            CONNECT_ISSUE_CLUSTER: 'SiaB-Cluster Connectivity issue',
            'WiaB-others': 'WiaB-Others',
            'WTP-others': 'WTP-Others',
            'SiaB-others': 'SiaB-Others'
        }

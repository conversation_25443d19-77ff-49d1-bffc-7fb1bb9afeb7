-- ----------------------------
-- Table structure for dh_ntx_benchmark
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark]

CREATE TABLE
  dh_ntx_benchmark (
    id int IDENTITY(1, 1) NOT NULL,
    name varchar(255) NULL,
    facility varchar(255) NULL,
    arch varchar(255) NULL,
    tier varchar(255) NULL,
    bmk_systems int NULL,
    bmk_backup int NULL,
    bmk_certificate int NULL,
    bmk_cluster_network int NULL,
    bmk_deployment int NULL,
    bmk_desire_state int NULL,
    bmk_endpoint int NULL,
    bmk_eula int NULL,
    bmk_group_mapping int NULL,
    bmk_lcm_activity int NULL,
    bmk_lcm_component int NULL,
    bmk_lcm_version int NULL,
    bmk_password_rotation int NULL,
    bmk_pm_activity int NULL,
    bmk_recipient int NULL,
    bmk_scheduler int NULL,
    bmk_storage int NULL,
    bmk_limits int NULL,
    bmk_timer int NULL,
    bmk_vault int NULL,
    bmk_vlan_config int NULL,
    bmk_brand int NULL,
    bmk_firewall_rules int NULL,
    bmk_site int NULL,
    bmk_atm_step int NULL,
  )

ALTER TABLE [dbo].[dh_ntx_benchmark] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_atm_step
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_atm_step]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_atm_step]

CREATE TABLE dh_ntx_benchmark_atm_step (
  id int IDENTITY(1,1) NOT NULL,
  index_label varchar(255)  NULL,
  steps_json varchar(500)  NULL,
)

ALTER TABLE [dbo].[dh_ntx_benchmark_atm_step] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_backup
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_backup]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_backup]


CREATE TABLE
  dh_ntx_benchmark_backup (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    rs_bw_policy_start_time varchar(255) NULL,
    rs_bw_policy_end_time varchar(255) NULL,
    day_capability_percentage int NULL,
    night_capability_percentage int NULL,
    rs_compression_enabled int NULL,
    snapshot_expire_interval_days int NULL,
    gold_schedules_json varchar(MAX) NULL,
    silver_schedules_json varchar(MAX) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_backup] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_brand
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_brand]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_brand]


CREATE TABLE
  dh_ntx_benchmark_brand (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    brands_json varchar(MAX) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_brand] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_certificate
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_certificate]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_certificate]


CREATE TABLE
  dh_ntx_benchmark_certificate (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NOT NULL,
    organization_name varchar(255) NOT NULL,
    template varchar(255) NOT NULL,
    ou_name varchar(255) NOT NULL,
    chain varchar(8000) NOT NULL,
    authority varchar(255) NOT NULL,
    cert_owner_group varchar(255) NOT NULL,
    email_contact varchar(255) NOT NULL,
    max_renewal_days_before_expiry int NULL,
    service_account varchar(255) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_certificate] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_cluster_network
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_cluster_network]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_cluster_network]


CREATE TABLE
  dh_ntx_benchmark_cluster_network (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    bond_0_nics int NULL,
    bond_0_speed int NULL,
    bond_0_type varchar(255) NULL,
    bond_1_nics int NULL,
    bond_1_speed int NULL,
    bond_1_type varchar(255) NULL,
    ahv_cvm_native int NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_cluster_network] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_deployment
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_deployment]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_deployment]


CREATE TABLE
  dh_ntx_benchmark_deployment (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    install_pc int NULL,
    join_pc int NULL,
    join_self int NULL,
    install_gateway int NULL,
    oob_network_address varchar(255) NULL,
    cvm_ram_gb int NULL,
    is_partof_metro int NULL,
    is_pci_dss int NULL,
    remote_diag_disable bit NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_deployment] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_desire_state
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_desire_state]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_desire_state]


CREATE TABLE
  dh_ntx_benchmark_desire_state (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    config_items_json varchar(500) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_desire_state] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_endpoint
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_endpoint]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_endpoint]


CREATE TABLE
  dh_ntx_benchmark_endpoint (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    endpoints_json varchar(MAX) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_endpoint] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_eula
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_eula]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_eula]


CREATE TABLE
  dh_ntx_benchmark_eula (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    name varchar(255) NULL,
    company varchar(255) NULL,
    role varchar(255) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_eula] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_firewall_rules
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_firewall_rules]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_firewall_rules]


CREATE TABLE
  dh_ntx_benchmark_firewall_rules (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    rules_json varchar(MAX) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_firewall_rules] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_group_mapping
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_group_mapping]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_group_mapping]


CREATE TABLE
  dh_ntx_benchmark_group_mapping (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    pe_site_admin_group varchar(255) NULL,
    px_site_viewer_users varchar(255) NULL,
    pc_site_viewer_group varchar(255) NULL,
    portal_site_admin_group varchar(255) NULL,
    portal_site_user_group varchar(255) NULL,
    ilo_ad_group varchar(255) NULL,
    ilo_join_cred varchar(255) NULL,
    roles_json varchar(MAX) NULL,
    var_groups varchar(255) NULL,
    domain_group_search_mode varchar(255) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_group_mapping] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_lcm_activity
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_lcm_activity]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_lcm_activity]


CREATE TABLE
  dh_ntx_benchmark_lcm_activity (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    allowed_start int NULL,
    allowed_end int NULL,
    aos_upgrade_timeout_sec int NULL,
    aos_upgrade_tasks_limit int NULL,
    spp_upgrade_timeout_sec int NULL,
    spp_upgrade_tasks_limit int NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_lcm_activity] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_lcm_component
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_lcm_component]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_lcm_component]


CREATE TABLE
  dh_ntx_benchmark_lcm_component (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    components_json varchar(MAX) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_lcm_component] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_lcm_version
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_lcm_version]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_lcm_version]


CREATE TABLE
  dh_ntx_benchmark_lcm_version (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    lcm_version varchar(255) NULL,
    aos_version varchar(255) NULL,
    aos_binary varchar(255) NULL,
    aos_metadata varchar(255) NULL,
    ahv_version varchar(255) NULL,
    ahv_binary varchar(255) NULL,
    foundation_version varchar(255) NULL,
    foundation_binary varchar(255) NULL,
    spp_lowest_version varchar(255) NULL,
    spp_step_version varchar(255) NULL,
    spp_base_version varchar(255) NULL,
    spp_latest_version varchar(255) NULL,
    pc_version varchar(255) NULL,
    pc_binary varchar(255) NULL,
    pc_metadata varchar(255) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_lcm_version] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_limits
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_limits]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_limits]


CREATE TABLE
  dh_ntx_benchmark_limits (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    max_pc_latency int NULL,
    max_witness_latency int NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_limits] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_password_rotation
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_password_rotation]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_password_rotation]


CREATE TABLE
  dh_ntx_benchmark_password_rotation (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    rotation_policy_json varchar(MAX) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_password_rotation] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_pm_activity
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_pm_activity]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_pm_activity]


CREATE TABLE
  dh_ntx_benchmark_pm_activity (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    graceful_shutdown_sleep_sec int NULL,
    hard_shutdown_sleep_sec int NULL,
    cluster_shutdown_sleep_sec int NULL,
    cluster_startup_sleep_sec int NULL,
    ahv_shutdown_sleep_sec int NULL,
    ahv_startup_sleep_sec int NULL,
    host_state_loop_sleep_sec int NULL,
    query_oob_interval_sec int NULL,
    query_cvm_interval_sec int NULL,
    cvm_startup_sleep_sec int NULL,
    firewall_startup_sleep_sec int NULL,
    host_state_loop_times int NULL,
    start_cluster_loop_times int NULL,
    action_json varchar(255) NULL,
    server_start_sequence_json varchar(255) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_pm_activity] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_recipient
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_recipient]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_recipient]


CREATE TABLE
  dh_ntx_benchmark_recipient (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    site_hands_email varchar(255) NULL,
    alert_receiver_email varchar(255) NULL,
    smtp_port int NULL,
    smtp_server varchar(255) NULL,
    sender_email varchar(255) NULL,
    smtp_security varchar(255) NULL,
    code_receiver_email varchar(255) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_recipient] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_scheduler
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_scheduler]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_scheduler]


CREATE TABLE
  dh_ntx_benchmark_scheduler (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    atm_schedule_json varchar(MAX) NULL,
    lcm_schedule_json varchar(MAX) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_scheduler] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_site
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_site]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_site]


CREATE TABLE
  dh_ntx_benchmark_site (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    site_bu_json varchar(MAX) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_site] SET (LOCK_ESCALATION = TABLE)



-- ----------------------------
-- Table structure for dh_ntx_benchmark_storage
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_storage]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_storage]


CREATE TABLE
  dh_ntx_benchmark_storage (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    container_rules_json varchar(MAX) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_storage] SET (LOCK_ESCALATION = TABLE)



-- ----------------------------
-- Table structure for dh_ntx_benchmark_systems
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_systems]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_systems]


CREATE TABLE
  dh_ntx_benchmark_systems (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    pc_cluster_ip varchar(255) NULL,
    dns_servers_json varchar(MAX) NULL,
    ntp_servers_json varchar(MAX) NULL,
    dark_site_json varchar(MAX) NULL,
    self_service_name varchar(255) NULL,
    ldap_domain varchar(255) NULL,
    ldap_domain_host varchar(255) NULL,
    dns_zone varchar(255) NULL,
    dc_witness_ip varchar(255) NULL,
    dc_witness_max_latency varchar(255) NULL,
    dc_witness_name varchar(255) NULL,
    site_proxy_name varchar(255) NULL,
    site_proxy_ip varchar(255) NULL,
    site_proxy_port varchar(255) NULL,
    site_proxy_extra_white_list varchar(255) NULL,
    region varchar(255) NULL,
    oneview varchar(255) NULL,
    oneview_scope varchar(255) NULL,
    timezone varchar(255) NULL,
    central_pe varchar(255) NULL,
    central_pe_ip varchar(255) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_systems] SET (LOCK_ESCALATION = TABLE)


-- ----------------------------
-- Table structure for dh_ntx_benchmark_timer
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_timer]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_timer]


CREATE TABLE
  dh_ntx_benchmark_timer (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    timers_json varchar(MAX) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_timer] SET (LOCK_ESCALATION = TABLE)



-- ----------------------------
-- Table structure for dh_ntx_benchmark_vault
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_vault]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_vault]


CREATE TABLE
  dh_ntx_benchmark_vault (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    engine varchar(255) NULL,
    master_namespace varchar(255) NULL,
    tier_namespace varchar(255) NULL,
    site_labels_json varchar(MAX) NULL,
    central_labels_json varchar(MAX) NULL,
    dc_labels_json varchar(MAX) NULL,
    new_cluster_labels_json varchar(MAX) NULL,
    service_account varchar(255) NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_vault] SET (LOCK_ESCALATION = TABLE)



-- ----------------------------
-- Table structure for dh_ntx_benchmark_vlan_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_ntx_benchmark_vlan_config]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_ntx_benchmark_vlan_config]


CREATE TABLE
  dh_ntx_benchmark_vlan_config (
    id int IDENTITY(1, 1) NOT NULL,
    index_label varchar(255) NULL,
    ahv_cvm_vlan int NULL,
    oob_vlan int NULL,
    pc_vlan int NULL,
  )


ALTER TABLE [dbo].[dh_ntx_benchmark_vlan_config] SET (LOCK_ESCALATION = TABLE)



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark] ADD CONSTRAINT [PK__dh_ntx__3213E83F5F046D9B] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = OFF, ALLOW_PAGE_LOCKS = OFF)
ON [PRIMARY]


-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_atm_step
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_atm_step] ADD CONSTRAINT [PK__dh_ntx__3213E83F7DBF9515] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_backup
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_backup] ADD CONSTRAINT [PK__dh_ntx__3213E83F7DBF9515_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_brand
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_brand] ADD CONSTRAINT [PK__dh_ntx__3213E83FD3BE06CC] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_certificate
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_certificate] ADD CONSTRAINT [PK__dh_ntx__3213E83F699520FE] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_cluster_network
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_cluster_network] ADD CONSTRAINT [PK__dh_ntx__3213E83F5F197D49] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_deployment
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_deployment] ADD CONSTRAINT [PK__dh_ntx__3213E83F122E8B0A] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_desire_state
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_desire_state] ADD CONSTRAINT [PK__dh_ntx__3213E83F59872896] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_endpoint
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_endpoint] ADD CONSTRAINT [PK__dh_ntx__3213E83F896C153C] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_eula
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_eula] ADD CONSTRAINT [PK__dh_ntx__3213E83FA429A3DB] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_firewall_rules
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_firewall_rules] ADD CONSTRAINT [PK__dh_ntx__3213E83F4AC86E08] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = OFF, ALLOW_PAGE_LOCKS = OFF)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_group_mapping
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_group_mapping] ADD CONSTRAINT [PK__dh_ntx__3213E83F9151D300] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_lcm_activity
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_lcm_activity] ADD CONSTRAINT [PK__dh_ntx__3213E83F165ADE9B_copy1_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_lcm_component
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_lcm_component] ADD CONSTRAINT [PK__dh_ntx__3213E83FE57A8D19] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = OFF, ALLOW_PAGE_LOCKS = OFF)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_lcm_version
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_lcm_version] ADD CONSTRAINT [PK__dh_ntx__3213E83FA7A444F6] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_limits
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_limits] ADD CONSTRAINT [PK__dh_ntx__3213E83FE84EC379] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_password_rotation
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_password_rotation] ADD CONSTRAINT [PK__dh_ntx__3213E83F329A4A0A] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_pm_activity
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_pm_activity] ADD CONSTRAINT [PK__dh_ntx__3213E83F165ADE9B_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_recipient
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_recipient] ADD CONSTRAINT [PK__dh_ntx__3213E83F3031BF81] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_scheduler
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_scheduler] ADD CONSTRAINT [PK__dh_ntx__3213E83F25B2DA94] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_site
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_site] ADD CONSTRAINT [PK__dh_ntx__3213E83FE9C84270] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_storage
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_storage] ADD CONSTRAINT [PK__dh_ntx__3213E83F1625657D] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_systems
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_systems] ADD CONSTRAINT [PK__dh_ntx__3213E83F9BE7EC72] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_timer
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_timer] ADD CONSTRAINT [PK__dh_ntx__3213E83F165ADE9B] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_vault
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_vault] ADD CONSTRAINT [PK__dh_ntx__3213E83F989E04E3] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]



-- ----------------------------
-- Primary Key structure for table dh_ntx_benchmark_vlan_config
-- ----------------------------
ALTER TABLE [dbo].[dh_ntx_benchmark_vlan_config] ADD CONSTRAINT [PK__dh_ntx__3213E83FA429A3DB_copy1_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]

-- ----------------------------
-- Records of [dh_ntx_benchmark]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark] ON


INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 2, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 1, 'retail', 1, 'Store-AP', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 2, 2, 1, 2, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 2, 1, 1, 2, 1, 1, 1, 'retail', 2, 'Store-CN', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 2, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 1, 1, 3, 1, 1, 1, 'retail', 3, 'Store-NA', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 2, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 1, 1, 4, 1, 1, 1, 'retail', 4, 'Store-EU', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 3, 1, 2, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 5, 1, 1, 5, 1, 2, 1, 'retail', 5, 'Store-PPE', 'PreProduction');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 2, 1, 4, 1, 2, 2, 2, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 6, 1, 1, 6, 1, 3, 4, 'retail', 6, 'Store-DT', 'IKEADT');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 3, 1, 5, 1, 2, 3, 5, 1, 1, 3, 1, 1, 1, 1, 1, 1, 4, 7, 1, 1, 7, 1, 4, 5, 'retail', 7, 'Store-D2', 'IKEAD2');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 8, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 1, 'retail', 8, 'RET-Central-AP-1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 2, 2, 1, 8, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 2, 1, 1, 2, 1, 1, 1, 'retail', 9, 'RET-Central-CN-1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 8, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 1, 1, 3, 1, 1, 1, 'retail', 10, 'RET-Central-NA-1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 8, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 1, 1, 4, 1, 1, 1, 'retail', 11, 'RET-Central-EU-1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 3, 1, 8, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 5, 1, 1, 5, 1, 2, 1, 'retail', 12, 'RET-Central-PPE-1', 'PreProduction');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 2, 1, 4, 1, 8, 2, 2, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 6, 1, 1, 6, 1, 3, 1, 'retail', 13, 'RET-Central-DT-1', 'IKEADT');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 3, 1, 5, 1, 8, 3, 5, 1, 1, 3, 1, 1, 1, 1, 1, 1, 4, 7, 1, 1, 7, 1, 4, 1, 'retail', 14, 'RET-Central-D2-1', 'IKEAD2');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 4, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 2, 'retail', 15, 'SO-AP', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 2, 2, 1, 4, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 2, 1, 1, 2, 1, 1, 2, 'retail', 16, 'SO-CN', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 4, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 1, 1, 3, 1, 1, 2, 'retail', 17, 'SO-NA', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 4, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 1, 1, 4, 1, 1, 2, 'retail', 18, 'SO-EU', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 6, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 3, 'retail', 19, 'CSC-AP', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 2, 2, 1, 6, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 2, 1, 1, 2, 1, 1, 3, 'retail', 20, 'CSC-CN', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 6, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 1, 1, 3, 1, 1, 3, 'retail', 21, 'CSC-NA', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 6, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 1, 1, 4, 1, 1, 3, 'retail', 22, 'CSC-EU', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 3, 1, 5, 1, 2, 3, 5, 1, 1, 3, 1, 1, 1, 1, 1, 1, 4, 7, 1, 1, 7, 1, 4, 6, 'retail', 23, 'Store-D2-551', 'IKEAD2');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 2, 4, 1, 6, 5, 14, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 8, 2, 2, 8, 2, 5, 8, 'warehouse', 24, 'DS-AP', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 3, 4, 2, 6, 5, 14, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 9, 2, 2, 9, 2, 5, 8, 'warehouse', 25, 'DS-CN', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 4, 4, 1, 6, 5, 14, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 10, 2, 2, 10, 2, 5, 8, 'warehouse', 26, 'DS-NA', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 5, 4, 1, 6, 5, 14, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 11, 2, 2, 11, 2, 5, 8, 'warehouse', 27, 'DS-EU', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 6, 5, 1, 7, 5, 14, 4, 7, 2, 2, 5, 2, 2, 3, 2, 2, 2, 6, 12, 2, 2, 12, 2, 7, 8, 'warehouse', 28, 'DS-DT', 'IKEADT');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 2, 4, 1, 6, 5, 15, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 8, 2, 2, 8, 2, 5, 8, 'warehouse', 29, 'DS-Central-AP-1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 3, 4, 2, 6, 5, 15, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 9, 2, 2, 9, 2, 5, 8, 'warehouse', 30, 'DS-Central-CN-1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 4, 4, 1, 6, 5, 15, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 10, 2, 2, 10, 2, 5, 8, 'warehouse', 31, 'DS-Central-NA-1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 5, 4, 1, 6, 5, 15, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 11, 2, 2, 11, 2, 5, 8, 'warehouse', 32, 'DS-Central-EU-1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 6, 5, 1, 7, 5, 15, 5, 7, 2, 2, 5, 2, 2, 3, 2, 2, 2, 6, 12, 2, 2, 12, 2, 7, 8, 'warehouse', 33, 'DS-Central-DT-1', 'IKEADT');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 2, 1, 8, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 1, 'retail', 34, 'RET-Central-AP-2', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 2, 1, 8, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 2, 1, 1, 2, 1, 1, 1, 'retail', 35, 'RET-Central-CN-2', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 2, 1, 8, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 1, 1, 3, 1, 1, 1, 'retail', 36, 'RET-Central-NA-2', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 2, 1, 8, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 1, 1, 4, 1, 1, 1, 'retail', 37, 'RET-Central-EU-2', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 3, 1, 8, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 5, 1, 1, 5, 1, 2, 1, 'retail', 38, 'RET-Central-PPE-2', 'PreProduction');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 4, 1, 8, 2, 2, 1, 1, 2, 1, 1, 1, 1, 1, 1, 3, 6, 1, 1, 6, 1, 3, 2, 'retail', 39, 'RET-Central-DT-2', 'IKEADT');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 5, 1, 8, 3, 5, 1, 1, 3, 1, 1, 1, 1, 1, 1, 2, 7, 1, 1, 7, 1, 4, 3, 'retail', 40, 'RET-Central-D2-2', 'IKEAD2');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 6, 5, 5, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 8, 2, 2, 8, 2, 5, 8, 'warehouse', 41, 'DS-Central-AP-2', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 6, 5, 5, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 9, 2, 2, 9, 2, 5, 8, 'warehouse', 42, 'DS-Central-CN-2', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 6, 5, 5, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 10, 2, 2, 10, 2, 5, 8, 'warehouse', 43, 'DS-Central-NA-2', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 6, 5, 5, 4, 6, 2, 2, 4, 2, 2, 3, 2, 2, 2, 5, 11, 2, 2, 11, 2, 5, 8, 'warehouse', 44, 'DS-Central-EU-2', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 7, 5, 5, 5, 7, 2, 2, 5, 2, 2, 3, 2, 2, 2, 6, 12, 2, 2, 12, 2, 7, 8, 'warehouse', 45, 'DS-Central-DT-2', 'IKEADT');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 2, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 1, 1, 13, 1, 1, 1, 'retail', 46, 'Store-EU1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 8, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 1, 1, 13, 1, 1, 1, 'retail', 47, 'RET-Central-EU1-1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PC', NULL, NULL, NULL, 2, 1, 8, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 1, 1, 13, 1, 1, 1, 'retail', 48, 'RET-Central-EU1-2', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 4, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 1, 1, 13, 1, 1, 2, 'retail', 49, 'SO-EU1', 'Production');
INSERT INTO [dh_ntx_benchmark] ([arch], [bmk_atm_step], [bmk_backup], [bmk_brand], [bmk_certificate], [bmk_cluster_network], [bmk_deployment], [bmk_desire_state], [bmk_endpoint], [bmk_eula], [bmk_firewall_rules], [bmk_group_mapping], [bmk_lcm_activity], [bmk_lcm_component], [bmk_lcm_version], [bmk_limits], [bmk_password_rotation], [bmk_pm_activity], [bmk_recipient], [bmk_scheduler], [bmk_site], [bmk_storage], [bmk_systems], [bmk_timer], [bmk_vault], [bmk_vlan_config], [facility], [id], [name], [tier]) VALUES ('PE', 1, 1, 1, 2, 1, 6, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 4, 1, 1, 4, 1, 1, 3, 'retail', 50, 'CSC-EU1', 'Production');

SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_atm_step]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_atm_step] ON


INSERT INTO [dbo].[dh_ntx_benchmark_atm_step] ([steps_json], [id], [index_label]) VALUES ('{"rotate_password":true,"renew_certificate":true,"desired_state_config":true}', 1, 'Retail-default');
INSERT INTO [dbo].[dh_ntx_benchmark_atm_step] ([steps_json], [id], [index_label]) VALUES ('{"rotate_password":true,"renew_certificate":true,"desired_state_config":true}', 2, 'Warehouse-AP');
INSERT INTO [dbo].[dh_ntx_benchmark_atm_step] ([steps_json], [id], [index_label]) VALUES ('{"rotate_password":true,"renew_certificate":true,"desired_state_config":true}', 3, 'Warehouse-CN');
INSERT INTO [dbo].[dh_ntx_benchmark_atm_step] ([steps_json], [id], [index_label]) VALUES ('{"rotate_password":true,"renew_certificate":true,"desired_state_config":false}', 4, 'Warehouse-NA');
INSERT INTO [dbo].[dh_ntx_benchmark_atm_step] ([steps_json], [id], [index_label]) VALUES ('{"rotate_password":true,"renew_certificate":true,"desired_state_config":false}', 5, 'Warehouse-EU');
INSERT INTO [dbo].[dh_ntx_benchmark_atm_step] ([steps_json], [id], [index_label]) VALUES ('{"rotate_password":true,"renew_certificate":true,"desired_state_config":false}', 6, 'Warehouse-DT');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_atm_step] OFF

-- ----------------------------
-- Records of [dh_ntx_benchmark_backup]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_backup] ON


INSERT INTO [dbo].[dh_ntx_benchmark_backup] ([day_capability_percentage], [gold_schedules_json], [id], [index_label], [night_capability_percentage], [rs_bw_policy_end_time], [rs_bw_policy_start_time], [rs_compression_enabled], [silver_schedules_json], [snapshot_expire_interval_days]) VALUES (20, '[{"IntervalType":"DAILY","Interval":1,"LocalCount":35,"RemoteCount":7},{"IntervalType":"HOURLY","Interval":2,"LocalCount":24,"RemoteCount":0}]', 1, 'Retail-default', 90, '22:00:00', '06:00:00', 1, '[{"IntervalType":"HOURLY","Interval":6,"LocalCount":35,"RemoteCount":1},{"IntervalType":"DAILY","Interval":1,"LocalCount":35,"RemoteCount":7}]', 180);
INSERT INTO [dbo].[dh_ntx_benchmark_backup] ([day_capability_percentage], [gold_schedules_json], [id], [index_label], [night_capability_percentage], [rs_bw_policy_end_time], [rs_bw_policy_start_time], [rs_compression_enabled], [silver_schedules_json], [snapshot_expire_interval_days]) VALUES (20, '[{"IntervalType":"DAILY","Interval":1,"LocalCount":35,"RemoteCount":7},{"IntervalType":"HOURLY","Interval":2,"LocalCount":24,"RemoteCount":0}]', 2, 'Retail-dt', 90, '22:00:00', '06:00:00', 1, '[{"IntervalType":"HOURLY","Interval":6,"LocalCount":35,"RemoteCount":1},{"IntervalType":"DAILY","Interval":1,"LocalCount":35,"RemoteCount":7}]', 180);
INSERT INTO [dbo].[dh_ntx_benchmark_backup] ([day_capability_percentage], [gold_schedules_json], [id], [index_label], [night_capability_percentage], [rs_bw_policy_end_time], [rs_bw_policy_start_time], [rs_compression_enabled], [silver_schedules_json], [snapshot_expire_interval_days]) VALUES (20, '[{"IntervalType":"DAILY","Interval":1,"LocalCount":35,"RemoteCount":7},{"IntervalType":"HOURLY","Interval":2,"LocalCount":24,"RemoteCount":0}]', 3, 'Retail-d2', 90, '22:00:00', '06:00:00', 1, '[{"IntervalType":"HOURLY","Interval":6,"LocalCount":35,"RemoteCount":1},{"IntervalType":"DAILY","Interval":1,"LocalCount":35,"RemoteCount":7}]', 180);
INSERT INTO [dbo].[dh_ntx_benchmark_backup] ([day_capability_percentage], [gold_schedules_json], [id], [index_label], [night_capability_percentage], [rs_bw_policy_end_time], [rs_bw_policy_start_time], [rs_compression_enabled], [silver_schedules_json], [snapshot_expire_interval_days]) VALUES (NULL, NULL, 4, 'Warehouse-default', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[dh_ntx_benchmark_backup] ([day_capability_percentage], [gold_schedules_json], [id], [index_label], [night_capability_percentage], [rs_bw_policy_end_time], [rs_bw_policy_start_time], [rs_compression_enabled], [silver_schedules_json], [snapshot_expire_interval_days]) VALUES (NULL, NULL, 5, 'Warehouse-dt', NULL, NULL, NULL, NULL, NULL, NULL);


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_backup] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_brand]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_brand] ON


INSERT INTO [dbo].[dh_ntx_benchmark_brand] ([brands_json], [id], [index_label]) VALUES ('{
"node":"Hpe",
"wan":"Versa",
"lan":"Aruba"
}', 1, 'global');
INSERT INTO [dbo].[dh_ntx_benchmark_brand] ([brands_json], [id], [index_label]) VALUES ('{
"node":"Hpe",
"wan":"Versa",
"lan":"Huawei"
}', 2, 'China');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_brand] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_certificate]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_certificate] ON


INSERT INTO [dbo].[dh_ntx_benchmark_certificate] ([authority], [cert_owner_group], [chain], [email_contact], [id], [index_label], [max_renewal_days_before_expiry], [organization_name], [ou_name], [service_account], [template]) VALUES ('IKEA Issuing CA V4', 'DPC_Siab', '-----BEGIN CERTIFICATE-----
MIIGVzCCBD+gAwIBAgIUSf1u8swsCVFX0DpbFq8/66zJuowwDQYJKoZIhvcNAQEL
BQAwSDELMAkGA1UEBhMCTkwxHjAcBgNVBAoMFUludGVyIElLRUEgU3lzdGVtcyBC
VjEZMBcGA1UEAwwQSUlTQlYgUm9vdCBDQSB2NDAeFw0yMTAxMTQxNDEyMDhaFw0z
MTAxMTIxNDEyMDdaMEgxEzARBgoJkiaJk/IsZAEZFgNjb20xFDASBgoJkiaJk/Is
ZAEZFgRpa2VhMRswGQYDVQQDDBJJS0VBIElzc3VpbmcgQ0EgdjQwggIiMA0GCSqG
SIb3DQEBAQUAA4ICDwAwggIKAoICAQDD6Lq1GuMc6byoLhdOq6ghpKcsUgZqxu8F
dz5s2wIz12dG1rFsa0ZjknQbPopa6wmm42/Bd/bIG72KhKsgqfkB57Ifxnf/0OUP
wrDH64jCQ7PZDJGT4m8Q+vc0IFXZrvJcmSt0SyJe1I0q8pm1HUlNj8IIdlvWsTPm
Y+4TGAPorTzIVvYvQ2jJ1UrEvXX8xJLt3pyD8igaNKNy5qX+dl2gnSS80Lhh4Ub+
LV+EibrHOhj0dCQ93x+EU352FyfFwIbMyNv7+zRq1a1i5CbSEj7zs+Zjc+aq5O6A
iSCsrNGsYf84vVFmOfZ7NrqzB6OYGVBN9YZPZQX2PjjJosT3KeTujtCWZlVGXu3f
ER6CogywLt0CTXlnRmQisdt4Ea4U5UZPP4mM9ozvgbi+WY0EyOAxVpzpDIntoOJq
Ecc/ne6UwCKvhJbO5KXi3WN/Z7yPunbb6LtR6en2knPGqsergtIlhj5icAcTMzTI
7ONlT7/Ix5xfFZ08AHv0GdrT2SDFXR+LP/97gRcsjx9HxZPRSl/tQI+y0VhoLBdu
nYBYCIblTnTIPeuaX8g/ho7j9rnira3vUKJZNoJS95sLb9U1fnq5VHOqRjzaBcWj
2Civrw6u+Hg+ysyXhs4Jfo0IrDeYDV/bOIJKt7S2zIRWebqq82ZbvR5I+oQHYv9O
CpTlXzwuuwIDAQABo4IBNzCCATMwEgYDVR0TAQH/BAgwBgEB/wIBADAfBgNVHSME
GDAWgBS0lveMTlDEmz4bazcmpQ00nwDXHDCBiAYIKwYBBQUHAQEEfDB6MEUGCCsG
AQUFBzAChjlodHRwOi8vY2FjZXJ0LWNlcnRwa2kueHdwLmlrZWEuY29tL3Jvb3Qv
SUlTQlZSb290Q0F2NC5jcnQwMQYIKwYBBQUHMAGGJWh0dHA6Ly9vY3NwLWNlcnRw
a2kueHdwLmlrZWEuY29tL29jc3AwQgYDVR0fBDswOTA3oDWgM4YxaHR0cDovL2Ny
bC1jZXJ0cGtpLnh3cC5pa2VhLmNvbS9JSVNCVlJvb3RDQXY0LmNybDAdBgNVHQ4E
FgQUbTdq8ylvkdzFJK5BYFD5FdYMz4QwDgYDVR0PAQH/BAQDAgGGMA0GCSqGSIb3
DQEBCwUAA4ICAQApQXW2D1Jr46R9W6EjOKk/+s8s3ltvkUSvjIF6VO7X8vESndd0
3dO742FXgG3qQ852JGW/bVIxZw4hd+CkxG9WcqZUvp8JqfwhaXFyTGK5pWJpoRty
2Su90bt7ahLogSdMLPkg3ObbDhkieIivQ07zSFG0kHBB1dOxuu7H2h5IUfGQVBBA
1zGyCuv/3R72sdtm6JADtdi9DH6OI+HU3+nqavr/mhsya5R+ygda+bKbrQTgtsQ3
hh8OE7uhHx7zKTCs21spMCeK1KgvPEgkb2b+PRfV3iSNa7lkLhjsEYYd4YvjshMI
eFvW2+oeiLUJUK/NfW4mOBSx3SOfokTZE0he3JKlXoaQ9hr9W5NrclafjgfVAEdQ
G6lCsw/O3YENc36iGV88pRUKh2e3VimgewOZwctRe3xUk7C1t58/gTNLhpUhSTFu
GFu9sH/BVxdCPBXzBGAjuCNdkYWiydiCk+LpB8nhR9v/B5l1UC/qU17h8mPOOgFE
gH/iAsem+hBOVpEzDhAO0RQHCsqkcjm1K31BVK/kDLshxrfkTbbxYSiP/qe2Vdbw
K6y6L6NBUwfqlBzggQaWycveCwHFd5Iz6/yTWSow40EJ5opcAMyhiNHjSxL6LSmE
QA7CiHx7eY/qoqZ+gjL8/hqwuX+2+8mSODK2dlzYHHNSIH+MAx64lIQrsg==
-----END CERTIFICATE-----', '<EMAIL>', 1, 'default', 38, 'IKEA', 'Infrastructure Engineering', 'siab_ssl', 'IKEAWebServerAutomated');
INSERT INTO [dbo].[dh_ntx_benchmark_certificate] ([authority], [cert_owner_group], [chain], [email_contact], [id], [index_label], [max_renewal_days_before_expiry], [organization_name], [ou_name], [service_account], [template]) VALUES ('IKEA Issuing CA V4', 'DPC_Siab', '-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----', '<EMAIL>', 2, 'retail_production', 38, 'IKEA', 'Infrastructure Engineering', 'siab_ssl', 'IKEAWebServerAutomated');
INSERT INTO [dbo].[dh_ntx_benchmark_certificate] ([authority], [cert_owner_group], [chain], [email_contact], [id], [index_label], [max_renewal_days_before_expiry], [organization_name], [ou_name], [service_account], [template]) VALUES ('IKEA Issuing CA V4', 'DPC_Siab', '-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----', '<EMAIL>', 3, 'retail_preproduction', 38, 'IKEA', 'Infrastructure Engineering', 'siab_ssl', 'IKEAWebServerAutomated');
INSERT INTO [dbo].[dh_ntx_benchmark_certificate] ([authority], [cert_owner_group], [chain], [email_contact], [id], [index_label], [max_renewal_days_before_expiry], [organization_name], [ou_name], [service_account], [template]) VALUES ('IKEADT Issuing CA V4', 'DPC_Siab', '-----BEGIN CERTIFICATE-----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=
-----END CERTIFICATE-----', '<EMAIL>', 4, 'retail_dt', 38, 'IKEA', 'Infrastructure Engineering', 'siab_ssl', 'IKEADTWebServerAutomated');
INSERT INTO [dbo].[dh_ntx_benchmark_certificate] ([authority], [cert_owner_group], [chain], [email_contact], [id], [index_label], [max_renewal_days_before_expiry], [organization_name], [ou_name], [service_account], [template]) VALUES ('IKEAD2 Issuing CA V4', 'DPC_Siab', '-----BEGIN CERTIFICATE-----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=
-----END CERTIFICATE-----', '<EMAIL>', 5, 'retail_d2', 38, 'IKEA', 'Infrastructure Engineering', 'siab_ssl', 'IKEAD2WebServerAutomated');
INSERT INTO [dbo].[dh_ntx_benchmark_certificate] ([authority], [cert_owner_group], [chain], [email_contact], [id], [index_label], [max_renewal_days_before_expiry], [organization_name], [ou_name], [service_account], [template]) VALUES ('IKEA Issuing CA V4', 'DPC_Siab', '-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----', '<EMAIL>', 6, 'warehouse', 38, 'IKEA', 'Infrastructure Engineering', 'siab_ssl', 'IKEAWebServerAutomated');
INSERT INTO [dbo].[dh_ntx_benchmark_certificate] ([authority], [cert_owner_group], [chain], [email_contact], [id], [index_label], [max_renewal_days_before_expiry], [organization_name], [ou_name], [service_account], [template]) VALUES ('IKEADT Issuing CA V4', 'DPC_Siab', '-----BEGIN CERTIFICATE-----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=
-----END CERTIFICATE-----', '<EMAIL>', 7, 'warehouse_dt', 38, 'IKEA', 'Infrastructure Engineering', 'siab_ssl', 'IKEADTWebServerAutomated');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_certificate] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_cluster_network]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_cluster_network] ON


INSERT INTO [dbo].[dh_ntx_benchmark_cluster_network] ([ahv_cvm_native], [bond_0_nics], [bond_0_speed], [bond_0_type], [bond_1_nics], [bond_1_speed], [bond_1_type], [id], [index_label]) VALUES (1, 2, 10000, 'Active-Passive', 0, 0, NULL, 1, '10G Dual Active-Passive');
INSERT INTO [dbo].[dh_ntx_benchmark_cluster_network] ([ahv_cvm_native], [bond_0_nics], [bond_0_speed], [bond_0_type], [bond_1_nics], [bond_1_speed], [bond_1_type], [id], [index_label]) VALUES (1, 4, 1000, 'Active-Passive', 0, 0, NULL, 2, '1G Quad Active-Passive');
INSERT INTO [dbo].[dh_ntx_benchmark_cluster_network] ([ahv_cvm_native], [bond_0_nics], [bond_0_speed], [bond_0_type], [bond_1_nics], [bond_1_speed], [bond_1_type], [id], [index_label]) VALUES (1, 2, 1000, 'Active-Passive', 0, 0, NULL, 3, '1G Dual Active-Passive');
INSERT INTO [dbo].[dh_ntx_benchmark_cluster_network] ([ahv_cvm_native], [bond_0_nics], [bond_0_speed], [bond_0_type], [bond_1_nics], [bond_1_speed], [bond_1_type], [id], [index_label]) VALUES (1, 6, 1000, 'Active-Passive', 0, 0, NULL, 4, '1G 6x Active-Passive');
INSERT INTO [dbo].[dh_ntx_benchmark_cluster_network] ([ahv_cvm_native], [bond_0_nics], [bond_0_speed], [bond_0_type], [bond_1_nics], [bond_1_speed], [bond_1_type], [id], [index_label]) VALUES (1, 2, 25000, 'Active-Passive', 0, 0, NULL, 5, '25G Quad Active-Passive');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_cluster_network] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_deployment]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_deployment] ON


INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (40, 1, 'default', 0, 0, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (40, 2, 'Retail-Store-157-158', 0, 0, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (40, 3, 'Retail-Store-157', 0, 0, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (40, 4, 'Retail-SO-357-358', 0, 0, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (40, 5, 'Retail-SO-357', 0, 0, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (40, 6, 'Retail-CSC-557-558', 0, 0, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (40, 7, 'Retail-CSC-557', 0, 0, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (32, 8, 'Retail-Central-157-158', 1, 1, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (32, 9, 'Retail-Central-157', 1, 1, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (32, 10, 'Retail-Central-357-358', 1, 1, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (32, 11, 'Retail-Central-357', 1, 1, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (32, 12, 'Retail-Central-557-558', 1, 1, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (32, 13, 'Retail-Central-557', 1, 1, 0, 1, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (64, 14, 'Warehouse-157-158', 0, 0, 1, 0, 1, 0, NULL, 'true');
INSERT INTO [dbo].[dh_ntx_benchmark_deployment] ([cvm_ram_gb], [id], [index_label], [install_gateway], [install_pc], [is_partof_metro], [is_pci_dss], [join_pc], [join_self], [oob_network_address], [remote_diag_disable]) VALUES (64, 15, 'Warehouse-Central-157-158', 1, 1, 1, 0, 1, 0, NULL, 'true');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_deployment] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_desire_state]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_desire_state] ON


INSERT INTO [dbo].[dh_ntx_benchmark_desire_state] ([config_items_json], [id], [index_label]) VALUES ('{"cvm_ram":1,"storage_config":1,"ldap":1,"child_projects":1,"smtp_config":1,"oob_health":1,"ha_reservation":1,"dns_ntp_config":1,"ahv_host_names":1,"cvm_host_names":1,"categories":0,"role_vm_rbac":1,"pulse":1,"pm_start_vm":1}', 1, 'Retail-default');
INSERT INTO [dbo].[dh_ntx_benchmark_desire_state] ([config_items_json], [id], [index_label]) VALUES ('{"cvm_ram":0,"storage_config":1,"ldap":1,"child_projects":0,"smtp_config":0,"oob_health":0,"ha_reservation":0,"dns_ntp_config":0,"ahv_host_names":0,"cvm_host_names":0,"categories":1,"role_vm_rbac":0,"pulse":0,"pm_start_vm":0}', 2, 'Retail-DT-Central');
INSERT INTO [dbo].[dh_ntx_benchmark_desire_state] ([config_items_json], [id], [index_label]) VALUES ('{"cvm_ram":0,"storage_config":1,"ldap":1,"child_projects":0,"smtp_config":0,"oob_health":0,"ha_reservation":0,"dns_ntp_config":0,"ahv_host_names":0,"cvm_host_names":0,"categories":1,"role_vm_rbac":0,"pulse":0,"pm_start_vm":0}', 3, 'Retail-D2-Central');
INSERT INTO [dbo].[dh_ntx_benchmark_desire_state] ([config_items_json], [id], [index_label]) VALUES ('{"cvm_ram":1,"storage_config":1,"ldap":1,"child_projects":1,"smtp_config":1,"oob_health":1,"ha_reservation":1,"dns_ntp_config":1,"ahv_host_names":1,"cvm_host_names":1,"categories":0,"role_vm_rbac":1,"pulse":1,"pm_start_vm":1}', 4, 'Warehouse-default');
INSERT INTO [dbo].[dh_ntx_benchmark_desire_state] ([config_items_json], [id], [index_label]) VALUES ('{"cvm_ram":0,"storage_config":1,"ldap":1,"child_projects":0,"smtp_config":0,"oob_health":0,"ha_reservation":0,"dns_ntp_config":0,"ahv_host_names":0,"cvm_host_names":0,"categories":1,"role_vm_rbac":0,"pulse":0,"pm_start_vm":0}', 5, 'Warehouse-DT-Central');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_desire_state] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_endpoint]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_endpoint] ON


INSERT INTO [dbo].[dh_ntx_benchmark_endpoint] ([endpoints_json], [id], [index_label]) VALUES ('{"windows":{"endpoint":"thorshammereu.ikea.com/api/runmethod","is_https":true,"port":80},"linux":{"endpoint":"ansible.ikea.com","is_https":true,"port":80},"rhel_image_repo":{"endpoint":"itseelm-lx6248.ikea.com","is_https":false,"port":9090},"pki":{"endpoint":"ikea.keyfactorpki.com","is_https":true,"port":443},"tower":{"endpoint":"tower.ikea.com","is_https":true,"port":443},"vault":{"endpoint":"vault-prod.build.ingka.ikea.com","is_https":true,"port":443},"ipam":{"endpoint":"ipam.ikea.com","is_https":true,"port":443}}', 1, 'default');
INSERT INTO [dbo].[dh_ntx_benchmark_endpoint] ([endpoints_json], [id], [index_label]) VALUES ('{"windows":{"endpoint":"thorshammereu.ikeadt.com/api/runmethod","is_https":true,"port":80},"linux":{"endpoint":"ansible.ikea.com","is_https":true,"port":80},"rhel_image_repo":{"endpoint":"itseelm-lx6248.ikea.com","is_https":false,"port":9090},"pki":{"endpoint":"ikea.keyfactorpki.com","is_https":true,"port":443},"tower":{"endpoint":"tower.ikea.com","is_https":true,"port":443},"vault":{"endpoint":"vault-prod.build.ingka.ikea.com","is_https":true,"port":443},"ipam":{"endpoint":"ipam.ikea.com","is_https":true,"port":443}}', 2, 'DT-EMEA');
INSERT INTO [dbo].[dh_ntx_benchmark_endpoint] ([endpoints_json], [id], [index_label]) VALUES ('{"windows":{"endpoint":"thorshammereu.ikea.com/api/runmethod","is_https":true,"port":80},"linux":{"endpoint":"ansible.ikea.com","is_https":true,"port":80},"rhel_image_repo":{"endpoint":"itseelm-lx6248.ikea.com","is_https":false,"port":9090},"pki":{"endpoint":"ikea.keyfactorpki.com","is_https":true,"port":443},"tower":{"endpoint":"tower.ikea.com","is_https":true,"port":443},"vault":{"endpoint":"vault-prod.build.ingka.ikea.com","is_https":true,"port":443},"ipam":{"endpoint":"ipam.ikea.com","is_https":true,"port":443}}', 3, 'PPE-EMEA');
INSERT INTO [dbo].[dh_ntx_benchmark_endpoint] ([endpoints_json], [id], [index_label]) VALUES ('{"windows":{"endpoint":"thorshammereu.ikea.com/api/runmethod","is_https":true,"port":80},"linux":{"endpoint":"ansible.ikea.com","is_https":true,"port":80},"rhel_image_repo":{"endpoint":"itseelm-lx6248.ikea.com","is_https":false,"port":9090},"pki":{"endpoint":"ikea.keyfactorpki.com","is_https":true,"port":443},"tower":{"endpoint":"tower.ikea.com","is_https":true,"port":443},"vault":{"endpoint":"vault-prod.build.ingka.ikea.com","is_https":true,"port":443},"ipam":{"endpoint":"ipam.ikea.com","is_https":true,"port":443}}', 4, 'PROD');
INSERT INTO [dbo].[dh_ntx_benchmark_endpoint] ([endpoints_json], [id], [index_label]) VALUES ('{"windows":{"endpoint":"thorshammereu.ikead2.com/api/runmethod","is_https":true,"port":80},"linux":{"endpoint":"ansible.ikea.com","is_https":true,"port":80},"rhel_image_repo":{"endpoint":"itseelm-lx6248.ikea.com","is_https":false,"port":9090},"pki":{"endpoint":"ikea.keyfactorpki.com","is_https":true,"port":443},"tower":{"endpoint":"tower.ikea.com","is_https":true,"port":443},"vault":{"endpoint":"vault-prod.build.ingka.ikea.com","is_https":true,"port":443},"ipam":{"endpoint":"ipam.ikea.com","is_https":true,"port":443}}', 5, 'D2-EMEA');
INSERT INTO [dbo].[dh_ntx_benchmark_endpoint] ([endpoints_json], [id], [index_label]) VALUES ('{"windows":{"endpoint":"thorshammereu.ikea.com/api/runmethod","is_https":true,"port":80},"linux":{"endpoint":"ansible.ikea.com","is_https":true,"port":80},"rhel_image_repo":{"endpoint":"itseelm-lx6248.ikea.com","is_https":false,"port":9090},"pki":{"endpoint":"ikea.keyfactorpki.com","is_https":true,"port":443},"tower":{"endpoint":"tower.ikea.com","is_https":true,"port":443},"vault":{"endpoint":"vault-prod.build.ingka.ikea.com","is_https":true,"port":443},"ipam":{"endpoint":"ipam.ikea.com","is_https":true,"port":443}}', 6, 'Warehouse-default');
INSERT INTO [dbo].[dh_ntx_benchmark_endpoint] ([endpoints_json], [id], [index_label]) VALUES ('{"windows":{"endpoint":"thorshammereu.ikeadt.com/api/runmethod","is_https":true,"port":80},"linux":{"endpoint":"ansible.ikea.com","is_https":true,"port":80},"rhel_image_repo":{"endpoint":"itseelm-lx6248.ikea.com","is_https":false,"port":9090},"pki":{"endpoint":"ikea.keyfactorpki.com","is_https":true,"port":443},"tower":{"endpoint":"tower.ikea.com","is_https":true,"port":443},"vault":{"endpoint":"vault-prod.build.ingka.ikea.com","is_https":true,"port":443},"ipam":{"endpoint":"ipam.ikea.com","is_https":true,"port":443}}', 7, 'Warehouse-DT');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_endpoint] OFF



-- ----------------------------
-- Records of [dh_ntx_benchmark_eula]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_eula] ON


INSERT INTO [dbo].[dh_ntx_benchmark_eula] ([company], [id], [index_label], [name], [role]) VALUES ('IKEA', 1, 'Retail', 'Emil Nilsson', 'Digital Technology Engineer');
INSERT INTO [dbo].[dh_ntx_benchmark_eula] ([company], [id], [index_label], [name], [role]) VALUES ('IKEA', 2, 'Warehouse', 'Sea Zhou', 'Digital Technology Engineer');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_eula] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_firewall_rules]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_firewall_rules] ON


INSERT INTO [dbo].[dh_ntx_benchmark_firewall_rules] ([id], [index_label], [rules_json]) VALUES (1, 'Retail-default', '[{"subject":"Central_Pe","source":"Site","endpoint":["systems","central_pe_ip"],"services":[{"port":2020,"is_udp":false},{"port":9440,"is_udp":false},{"port":161,"is_udp":true}],"is_mandatory":true},{"subject":"Central_Pc","source":"Site","endpoint":["systems","pc_cluster_ip"],"services":[{"port":9301,"is_udp":false},{"port":9300,"is_udp":false}],"is_mandatory":true},{"subject":"Dark_Site","source":"Site","endpoint":["systems","dark_site","endpoint"],"services":[{"port":80,"is_udp":false}],"is_mandatory":true},{"subject":"Ntp_Servers","source":"Site","endpoint":["systems","ntp_servers"],"services":[{"port":123,"is_udp":true}],"is_mandatory":true},{"subject":"Dns_Servers","source":"Site","endpoint":["systems","dns_servers"],"services":[{"port":53,"is_udp":true}],"is_mandatory":true},{"subject":"Smtp","source":"Site","endpoint":["recipient","smtp_server"],"services":[{"port":25,"is_udp":false}],"is_mandatory":false}]');
INSERT INTO [dbo].[dh_ntx_benchmark_firewall_rules] ([id], [index_label], [rules_json]) VALUES (2, 'Warehouse-default', '[{"subject":"Central_Pe","source":"Site","endpoint":["systems","central_pe_ip"],"services":[{"port":2020,"is_udp":false},{"port":9440,"is_udp":false},{"port":161,"is_udp":true}],"is_mandatory":true},{"subject":"Central_Pc","source":"Site","endpoint":["systems","pc_cluster_ip"],"services":[{"port":9301,"is_udp":false},{"port":9300,"is_udp":false}],"is_mandatory":true},{"subject":"Dark_Site","source":"Site","endpoint":["systems","dark_site","endpoint"],"services":[{"port":80,"is_udp":false}],"is_mandatory":true},{"subject":"Ntp_Servers","source":"Site","endpoint":["systems","ntp_servers"],"services":[{"port":123,"is_udp":true}],"is_mandatory":true},{"subject":"Dns_Servers","source":"Site","endpoint":["systems","dns_servers"],"services":[{"port":53,"is_udp":true}],"is_mandatory":true},{"subject":"Smtp","source":"Site","endpoint":["recipient","smtp_server"],"services":[{"port":25,"is_udp":false}],"is_mandatory":false}]');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_firewall_rules] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_group_mapping]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_group_mapping] ON


INSERT INTO [dbo].[dh_ntx_benchmark_group_mapping] ([domain_group_search_mode], [id], [ilo_ad_group], [ilo_join_cred], [index_label], [pc_site_viewer_group], [pe_site_admin_group], [portal_site_admin_group], [portal_site_user_group], [px_site_viewer_users], [roles_json], [var_groups]) VALUES ('NON_RECURSIVE', 1, 'NXadmin', 'LDAP', 'Retail-default', 'NXView', 'NXadmin', 'UG-NutanixSelfServiceAdmin-CG@HBG-RET-SE-HBG', 'UG-NutanixSelfServiceUsers-CG@HBG-RET-SE-HBG', '', '[{"Name":"IKEA-Linux-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-LX","MustNotMatch":"-NT|PL|-WL|-LC|-LX6|POS|NTXUSPHI-NT4017|RETRUSO-NT4017|NTXCNCHN-NT4017|NTXSGSNG-NT4017|NTXSEELM-NT4017","Groups":["UG-GLINUX-CG@ELM-IT-SE-ELM"]}},{"Name":"IKEA-Windows-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-NT","MustNotMatch":"PL|-WL|-LC|-LX|NTXUSPHI-NT4017|RETRUSO-NT4017|NTXCNCHN-NT4017|NTXSGSNG-NT4017|NTXSEELM-NT4017","Groups":["UG-DH_TCSTeam-CG@INF-NTX-SE-ELM"]}},{"Name":"IKEA-Networking-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine","Create_App","Launch_Blueprint","View_Marketplace_Item","View_Account","View_App","View_Blueprint","View_Project","View_App_Icon","View_App_Task","View_App_Variable","Abort_App_Runlog","Delete_App","Validate_App","View_App_Showback","View_App_Icon","View_Value_Category","View_Category_Mapping","View_Environment","Action_Run_App","Download_App_Runlog","View_App_Runlog","View_Subnet","View_Name_Category","Update_App","View_Endpoint","Update_Virtual_Machine","View_Image","View_Resource_Type"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-PL|-WL|-LC","MustNotMatch":"-NT|-LX|POS","Groups":["UG-Network_Admin_Employee-CG@INF-ACS-SE-ELM","UG-Network_Admin_Consultant-CG@INF-ACS-SE-ELM"]}}]', 'Filter_NamingConvention_SiteName');
INSERT INTO [dbo].[dh_ntx_benchmark_group_mapping] ([domain_group_search_mode], [id], [ilo_ad_group], [ilo_join_cred], [index_label], [pc_site_viewer_group], [pe_site_admin_group], [portal_site_admin_group], [portal_site_user_group], [px_site_viewer_users], [roles_json], [var_groups]) VALUES ('NON_RECURSIVE', 2, 'UG-DHRTadmin-CG@PPE-PP-SE-ELM', 'LDAP', 'Retail-dt', 'UG-NXview-CG@HBG-RET-SE-ELM', 'UG-DHRTadmin-CG@PPE-PP-SE-ELM', 'UG-NutanixSelfServiceAdmin-CG@HBG-RET-SE-ELM', 'UG-NutanixSelfServiceUsers-CG@HBG-RET-SE-ELM', '', '[{"Name":"IKEA-Linux-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-LX","MustNotMatch":"-NT|-LX6|-PL|-WL|-LC|POS","Groups":["UG-NutanixSelfServiceReadOnlyAdmin-CG@HBG-RET-SE-ELM"]}},{"Name":"IKEA-Windows-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-NT","MustNotMatch":"-PL|-WL|-LC|-LX|POS","Groups":["UG-NutanixStackReadOnlyAdmin-CG@HBG-RET-SE-ELM"]}},{"Name":"IKEA-Networking-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine","Create_App","Launch_Blueprint","View_Marketplace_Item","View_Account","View_App","View_Blueprint","View_Project","View_App_Icon","View_App_Task","View_App_Variable","Abort_App_Runlog","Delete_App","Validate_App","View_App_Showback","View_App_Icon","View_Value_Category","View_Category_Mapping","View_Environment","Action_Run_App","Download_App_Runlog","View_App_Runlog","View_Subnet","View_Name_Category","Update_App","View_Endpoint","Update_Virtual_Machine","View_Image","View_Resource_Type"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-PL|-WL|-LC","MustNotMatch":"-NT|-LX|POS","Groups":["I-ClientRISInstaller-CG@ELM-CMP-SE","I-Groups-CG@ELM-CMP-SE"]}}]', 'Filter_NamingConvention_SiteName');
INSERT INTO [dbo].[dh_ntx_benchmark_group_mapping] ([domain_group_search_mode], [id], [ilo_ad_group], [ilo_join_cred], [index_label], [pc_site_viewer_group], [pe_site_admin_group], [portal_site_admin_group], [portal_site_user_group], [px_site_viewer_users], [roles_json], [var_groups]) VALUES ('NON_RECURSIVE', 3, 'NXAdmin', 'LDAP', 'Retail-d2', 'NXViewer', 'NXAdmin', 'UG-NutanixSelfServiceAdmin-CG@HBG-RET-SE-ELM', 'UG-NutanixSelfServiceUsers-CG@HBG-RET-SE-ELM', '', '[{"Name":"IKEA-Linux-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-LX","MustNotMatch":"-NT|-LX6|-PL|-WL|-LC|POS","Groups":["I-Groups-CG@HBG-RET-SE-ELM"]}},{"Name":"IKEA-Windows-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-NT","MustNotMatch":"-PL|-WL|-LC|-LX|POS","Groups":["I-Groups-CG@HBG-RET-SE-ELM"]}},{"Name":"IKEA-Networking-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine","Create_App","Launch_Blueprint","View_Marketplace_Item","View_Account","View_App","View_Blueprint","View_Project","View_App_Icon","View_App_Task","View_App_Variable","Abort_App_Runlog","Delete_App","Validate_App","View_App_Showback","View_App_Icon","View_Value_Category","View_Category_Mapping","View_Environment","Action_Run_App","Download_App_Runlog","View_App_Runlog","View_Subnet","View_Name_Category","Update_App","View_Endpoint","Update_Virtual_Machine","View_Image","View_Resource_Type"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-PL|-WL|-LC","MustNotMatch":"-NT|-LX|POS","Groups":["I-ClientRISInstaller-CG@HBG-RET-SE-ELM","I-Groups-CG@HBG-RET-SE-ELM"]}}]', 'Exact');
INSERT INTO [dbo].[dh_ntx_benchmark_group_mapping] ([domain_group_search_mode], [id], [ilo_ad_group], [ilo_join_cred], [index_label], [pc_site_viewer_group], [pe_site_admin_group], [portal_site_admin_group], [portal_site_user_group], [px_site_viewer_users], [roles_json], [var_groups]) VALUES ('NON_RECURSIVE', 4, 'UG-WIABadmin', 'LDAP', 'Warehouse-default', 'UG-WIABviewer-CG@INF-NTX-SE-DS', 'ug-wiabadmin', 'UG-NutanixSelfServiceAdmin-CG@HBG-RET-SE-ELM', 'UG-WIABadmin-CG@INF-NTX-SE-DSDT', 'UG-WIABviewer-CG@INF-NTX-SE-DS', '[{"Name":"IKEA-Linux-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-LX","MustNotMatch":"-NT|-LX6|-PL|-WL|-LC|POS","Groups":["I-Groups-CG@HBG-RET-SE-ELM"]}},{"Name":"IKEA-Windows-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-NT","MustNotMatch":"-PL|-WL|-LC|-LX|POS","Groups":["I-Groups-CG@HBG-RET-SE-ELM"]}},{"Name":"IKEA-Networking-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine","Create_App","Launch_Blueprint","View_Marketplace_Item","View_Account","View_App","View_Blueprint","View_Project","View_App_Icon","View_App_Task","View_App_Variable","Abort_App_Runlog","Delete_App","Validate_App","View_App_Showback","View_App_Icon","View_Value_Category","View_Category_Mapping","View_Environment","Action_Run_App","Download_App_Runlog","View_App_Runlog","View_Subnet","View_Name_Category","Update_App","View_Endpoint","Update_Virtual_Machine","View_Image","View_Resource_Type"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-PL|-WL|-LC","MustNotMatch":"-NT|-LX|POS","Groups":["I-ClientRISInstaller-CG@HBG-RET-SE-ELM","I-Groups-CG@HBG-RET-SE-ELM"]}}]', 'Filter_NamingConvention_SiteName');
INSERT INTO [dbo].[dh_ntx_benchmark_group_mapping] ([domain_group_search_mode], [id], [ilo_ad_group], [ilo_join_cred], [index_label], [pc_site_viewer_group], [pe_site_admin_group], [portal_site_admin_group], [portal_site_user_group], [px_site_viewer_users], [roles_json], [var_groups]) VALUES ('NON_RECURSIVE', 5, 'UG-WIABadmin-CG@INF-NTX-SE-DSDT', 'LDAP', 'Warehouse-dt', 'UG-WIABadmin-CG@INF-NTX-SE-DSDT', 'UG-WIABadmin-CG@INF-NTX-SE-DSDT', 'UG-NutanixSelfServiceAdmin-CG@HBG-RET-SE-ELM', 'UG-WIABadmin-CG@INF-NTX-SE-DSDT', 'UG-WIABadmin-CG@INF-NTX-SE-DSDT', '[{"Name":"IKEA-Linux-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-LX","MustNotMatch":"-NT|-LX6|-PL|-WL|-LC|POS","Groups":["I-Groups-CG@HBG-RET-SE-ELM"]}},{"Name":"IKEA-Windows-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-NT","MustNotMatch":"-PL|-WL|-LC|-LX|POS","Groups":["I-Groups-CG@HBG-RET-SE-ELM"]}},{"Name":"IKEA-Networking-Operator","Permissions":["Access_Console","Access_Console_Virtual_Machine","Update_Virtual_Machine_Power_State","View_Virtual_Machine","Create_App","Launch_Blueprint","View_Marketplace_Item","View_Account","View_App","View_Blueprint","View_Project","View_App_Icon","View_App_Task","View_App_Variable","Abort_App_Runlog","Delete_App","Validate_App","View_App_Showback","View_App_Icon","View_Value_Category","View_Category_Mapping","View_Environment","Action_Run_App","Download_App_Runlog","View_App_Runlog","View_Subnet","View_Name_Category","Update_App","View_Endpoint","Update_Virtual_Machine","View_Image","View_Resource_Type"],"AssignmentScope":{"ObjectType":"VM","MustMatch":"-PL|-WL|-LC","MustNotMatch":"-NT|-LX|POS","Groups":["I-ClientRISInstaller-CG@HBG-RET-SE-ELM","I-Groups-CG@HBG-RET-SE-ELM"]}}]', 'Filter_NamingConvention_SiteName');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_group_mapping] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_lcm_activity]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_lcm_activity] ON


INSERT INTO [dbo].[dh_ntx_benchmark_lcm_activity] ([allowed_end], [allowed_start], [aos_upgrade_tasks_limit], [aos_upgrade_timeout_sec], [id], [index_label], [spp_upgrade_tasks_limit], [spp_upgrade_timeout_sec]) VALUES (4, 21, 50, 86400, 1, 'Retail-default', 10, 86400);
INSERT INTO [dbo].[dh_ntx_benchmark_lcm_activity] ([allowed_end], [allowed_start], [aos_upgrade_tasks_limit], [aos_upgrade_timeout_sec], [id], [index_label], [spp_upgrade_tasks_limit], [spp_upgrade_timeout_sec]) VALUES (4, 21, 50, 86400, 2, 'Warehouse-default', 10, 86400);


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_lcm_activity] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_lcm_component]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_lcm_component] ON


INSERT INTO [dbo].[dh_ntx_benchmark_lcm_component] ([components_json], [id], [index_label]) VALUES ('{"epsilon":"Epsilon","calm":"Calm","cmu":"ClusterMaintenanceUtilities","flowsecurity":"FlowSecurity","licensing":"Licensing","ncc":"NCC","pc":"PC"}', 1, 'Retail-default');
INSERT INTO [dbo].[dh_ntx_benchmark_lcm_component] ([components_json], [id], [index_label]) VALUES ('{"epsilon":"Epsilon","calm":"Calm","cmu":"ClusterMaintenanceUtilities","flowsecurity":"FlowSecurity","licensing":"Licensing","ncc":"NCC","pc":"PC"}', 2, 'Warehouse-default');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_lcm_component] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_lcm_version]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_lcm_version] ON


INSERT INTO [dbo].[dh_ntx_benchmark_lcm_version] ([ahv_binary], [ahv_version], [aos_binary], [aos_metadata], [aos_version], [foundation_binary], [foundation_version], [id], [index_label], [lcm_version], [pc_binary], [pc_metadata], [pc_version], [spp_base_version], [spp_latest_version], [spp_lowest_version], [spp_step_version]) VALUES (NULL, '20220304.462', 'nutanix_installer_package-release-fraser-6.5.4.5-stable-49bd685cde4e9488d6347655f7b655df97e1849e-x86_64.tar.gz', 'generated-nutanix_installer_package-release-fraser-6.5.4.5-stable-49bd685cde4e9488d6347655f7b655df97e1849e-x86_64-metadata.json', '6.5.4.5', 'foundation-5.6.1.tar.gz', '5.6.1', 1, 'default', '2.7.1', 'pc.2022.6.0.5-279ffd67a3d647834a40ed90af23a3161553b04a-x86_64.tar.gz', 'generated-pc.2022.6.0.5-279ffd67a3d647834a40ed90af23a3161553b04a-metadata.json', 'pc.2022.6.0.5', '2023.09.00.00.02', '2023.09.00.00.02', '2023.03.00.00.05', '2023.03.00.00.05');
INSERT INTO [dbo].[dh_ntx_benchmark_lcm_version] ([ahv_binary], [ahv_version], [aos_binary], [aos_metadata], [aos_version], [foundation_binary], [foundation_version], [id], [index_label], [lcm_version], [pc_binary], [pc_metadata], [pc_version], [spp_base_version], [spp_latest_version], [spp_lowest_version], [spp_step_version]) VALUES (NULL, '20220304.462', 'nutanix_installer_package-release-fraser-6.5.4.5-stable-49bd685cde4e9488d6347655f7b655df97e1849e-x86_64.tar.gz', 'generated-nutanix_installer_package-release-fraser-6.5.4.5-stable-49bd685cde4e9488d6347655f7b655df97e1849e-x86_64-metadata.json', '6.5.4.5', 'foundation-5.6.1.tar.gz', '5.6.1', 2, 'Retail', '3.1', 'pc.2022.6.0.5-279ffd67a3d647834a40ed90af23a3161553b04a-x86_64.tar.gz', 'generated-pc.2022.6.0.5-279ffd67a3d647834a40ed90af23a3161553b04a-metadata.json', 'pc.2022.6.0.5', '2023.09.00.00.02', '2023.09.00.00.02', '2023.03.00.00.05', '2023.03.00.00.05');
INSERT INTO [dbo].[dh_ntx_benchmark_lcm_version] ([ahv_binary], [ahv_version], [aos_binary], [aos_metadata], [aos_version], [foundation_binary], [foundation_version], [id], [index_label], [lcm_version], [pc_binary], [pc_metadata], [pc_version], [spp_base_version], [spp_latest_version], [spp_lowest_version], [spp_step_version]) VALUES (NULL, '20230302', 'nutanix_installer_package-release-fraser-6.7.1.6-stable-2ed047f61715fe111512a82ee867d9517975c770-x86_64.tar.gz', 'generated-nutanix_installer_package-release-fraser-6.7.1.6-stable-2ed047f61715fe111512a82ee867d9517975c770-x86_64-metadata.json', '6.7.1.6', 'foundation-5.6.1.tar.gz', '5.6.1', 3, 'Warehouse', '3.0.1.1', NULL, NULL, 'pc.2023.4', '2023.09.00.00.02', '2023.09.00.00.02', NULL, NULL);


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_lcm_version] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_limits]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_limits] ON


INSERT INTO [dbo].[dh_ntx_benchmark_limits] ([id], [index_label], [max_pc_latency], [max_witness_latency]) VALUES (1, 'Retail-default', 90, 300);
INSERT INTO [dbo].[dh_ntx_benchmark_limits] ([id], [index_label], [max_pc_latency], [max_witness_latency]) VALUES (2, 'Warehouse-default', 90, 300);


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_limits] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_password_rotation]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_password_rotation] ON


INSERT INTO [dbo].[dh_ntx_benchmark_password_rotation] ([id], [index_label], [rotation_policy_json]) VALUES (1, 'Retail-default', '{"Site_Pe_Admin":16,"Site_Ahv_Root":16,"Site_Ahv_Nutanix":16,"Site_Ahv_Admin":16,"Site_Move":16,"Site_Pe_Nutanix":16,"Site_Pe_Svc":16,"Site_Oob":16,"Site_Pc_Admin":16,"Site_Pc_Nutanix":16,"Site_Pc_Svc":16,"Site_Witness_Admin":0,"Site_Witness_Nutanix":0,"Use_Password_History":1,"Site_Pe_Files_Root":16,"Site_Pe_Files_Nutanix":16,"Site_Pe_Files_Admin":16}');
INSERT INTO [dbo].[dh_ntx_benchmark_password_rotation] ([id], [index_label], [rotation_policy_json]) VALUES (2, 'Warehouse-default', '{"Site_Pe_Admin":16,"Site_Ahv_Root":16,"Site_Ahv_Nutanix":16,"Site_Ahv_Admin":16,"Site_Move":16,"Site_Pe_Nutanix":16,"Site_Pe_Svc":16,"Site_Oob":16,"Site_Pc_Admin":16,"Site_Pc_Nutanix":16,"Site_Pc_Svc":16,"Site_Witness_Admin":0,"Site_Witness_Nutanix":0,"Use_Password_History":1,"Site_Pe_Files_Root":16,"Site_Pe_Files_Nutanix":16,"Site_Pe_Files_Admin":16}');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_password_rotation] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_pm_activity]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_pm_activity] ON


INSERT INTO [dbo].[dh_ntx_benchmark_pm_activity] ([action_json], [ahv_shutdown_sleep_sec], [ahv_startup_sleep_sec], [cluster_shutdown_sleep_sec], [cluster_startup_sleep_sec], [cvm_startup_sleep_sec], [firewall_startup_sleep_sec], [graceful_shutdown_sleep_sec], [hard_shutdown_sleep_sec], [host_state_loop_sleep_sec], [host_state_loop_times], [id], [index_label], [query_cvm_interval_sec], [query_oob_interval_sec], [server_start_sequence_json], [start_cluster_loop_times]) VALUES ('{"on":"poweron","off":"poweroff"}', 120, 360, 180, 120, 300, 300, 180, 120, 120, 8, 1, 'Retail-default', 10, 180, '[".*-PL000(1|2).*"]', 5);
INSERT INTO [dbo].[dh_ntx_benchmark_pm_activity] ([action_json], [ahv_shutdown_sleep_sec], [ahv_startup_sleep_sec], [cluster_shutdown_sleep_sec], [cluster_startup_sleep_sec], [cvm_startup_sleep_sec], [firewall_startup_sleep_sec], [graceful_shutdown_sleep_sec], [hard_shutdown_sleep_sec], [host_state_loop_sleep_sec], [host_state_loop_times], [id], [index_label], [query_cvm_interval_sec], [query_oob_interval_sec], [server_start_sequence_json], [start_cluster_loop_times]) VALUES ('2', 120, 360, 180, 120, 300, 300, 180, 120, 120, 8, 2, 'Warehouse-default', 10, 180, NULL, 5);


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_pm_activity] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_recipient]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_recipient] ON


INSERT INTO [dbo].[dh_ntx_benchmark_recipient] ([alert_receiver_email], [code_receiver_email], [id], [index_label], [sender_email], [site_hands_email], [smtp_port], [smtp_security], [smtp_server]) VALUES ('<EMAIL>', '', 1, 'default', '<EMAIL>', '<EMAIL>', 25, 'NONE', 'smtp-gw.ikea.com');
INSERT INTO [dbo].[dh_ntx_benchmark_recipient] ([alert_receiver_email], [code_receiver_email], [id], [index_label], [sender_email], [site_hands_email], [smtp_port], [smtp_security], [smtp_server]) VALUES ('<EMAIL>', '', 2, 'DT', '<EMAIL>', '<EMAIL>', 25, 'NONE', 'smtp-gw.ikeadt.com');
INSERT INTO [dbo].[dh_ntx_benchmark_recipient] ([alert_receiver_email], [code_receiver_email], [id], [index_label], [sender_email], [site_hands_email], [smtp_port], [smtp_security], [smtp_server]) VALUES ('<EMAIL>', NULL, 3, 'Prod', '<EMAIL>', '<EMAIL>', 25, 'NONE', 'smtp-gw.ikea.com');
INSERT INTO [dbo].[dh_ntx_benchmark_recipient] ([alert_receiver_email], [code_receiver_email], [id], [index_label], [sender_email], [site_hands_email], [smtp_port], [smtp_security], [smtp_server]) VALUES ('<EMAIL>', NULL, 4, 'D2', '<EMAIL>', '<EMAIL>', 25, 'NONE', 'smtp-gw.ikea.com');
INSERT INTO [dbo].[dh_ntx_benchmark_recipient] ([alert_receiver_email], [code_receiver_email], [id], [index_label], [sender_email], [site_hands_email], [smtp_port], [smtp_security], [smtp_server]) VALUES (NULL, NULL, 5, 'Warehouse-Prod', NULL, '<EMAIL>', NULL, NULL, 'Not-Implemented');
INSERT INTO [dbo].[dh_ntx_benchmark_recipient] ([alert_receiver_email], [code_receiver_email], [id], [index_label], [sender_email], [site_hands_email], [smtp_port], [smtp_security], [smtp_server]) VALUES (NULL, NULL, 6, 'Warehouse-DT', NULL, '<EMAIL>', NULL, NULL, 'Not-Implemented');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_recipient] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_scheduler]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_scheduler] ON


INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-apac-ntx.ikea.com","time_zone":"Asia/Tokyo","days":"thu,fri","hour":23,"minute":0,"interval_min":10}', 1, 'Retail_AP', '{"pc":"ssp-apac-ntx.ikea.com","time_zone":"Asia/Tokyo","days":"thu,fri","hour":23,"minute":0,"interval_min":10}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-china-ntx.ikea.com","time_zone":"Asia/Shanghai","days":"mon,tue","hour":23,"minute":0,"interval_min":10}', 2, 'Retail_China', '{"pc":"ssp-china-ntx.ikea.com","time_zone":"Asia/Shanghai","days":"mon,tue","hour":23,"minute":0,"interval_min":10}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-na-ntx.ikea.com","time_zone":"America/Los_Angeles","days":"wed,thu","hour":23,"minute":0,"interval_min":5}', 3, 'Retail_NA', '{"pc":"ssp-na-ntx.ikea.com","time_zone":"America/Los_Angeles","days":"wed,thu","hour":23,"minute":0,"interval_min":5}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-eu-ntx.ikea.com","time_zone":"Europe/Paris","days":"tue,wed","hour":20,"minute":0,"interval_min":2}', 4, 'Retail_EU', '{"pc":"ssp-eu-ntx.ikea.com","time_zone":"Europe/Paris","days":"tue,wed","hour":20,"minute":0,"interval_min":2}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-ppe-ntx.ikea.com","time_zone":"Europe/Paris","days":"mon,tue","hour":23,"minute":0,"interval_min":10}', 5, 'Retail_PPE', '{"pc":"ssp-ppe-ntx.ikea.com","time_zone":"Europe/Paris","days":"mon,tue","hour":23,"minute":0,"interval_min":10}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-dhdt-ntx.ikea.com","time_zone":"Europe/Paris","days":"tue,wed","hour":20,"minute":0,"interval_min":2}', 6, 'Retail_DT', '{"pc":"ssp-dhdt-ntx.ikea.com","time_zone":"Europe/Paris","days":"tue,wed","hour":20,"minute":0,"interval_min":2}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-dhd2-ntx.ikea.com","time_zone":"Europe/Paris","days":"tue,wed","hour":20,"minute":0,"interval_min":2}', 7, 'Retail_D2', '{"pc":"ssp-dhd2-ntx.ikea.com","time_zone":"Europe/Paris","days":"tue,wed","hour":20,"minute":0,"interval_min":2}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-apac-wiab-ntx.ikea.com","time_zone":"Europe/Paris","days":"sun,mon","hour":22,"minute":0,"interval_min":10}', 8, 'Warehouse_AP', '{"pc":"ssp-apac-wiab-ntx.ikea.com","time_zone":"Europe/Paris","days":"thu,fri","hour":23,"minute":0,"interval_min":10}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-china-wiab-ntx.ikea.com","time_zone":"Europe/Paris","days":"sun,mon","hour":22,"minute":0,"interval_min":10}', 9, 'Warehouse_China', '{"pc":"ssp-china-wiab-ntx.ikea.com","time_zone":"Europe/Paris","days":"mon,tue","hour":23,"minute":0,"interval_min":10}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-na-wiab-ntx.ikea.com","time_zone":"Europe/Paris","days":"sun,mon","hour":22,"minute":0,"interval_min":10}', 10, 'Warehouse_NA', '{"pc":"ssp-na-wiab-ntx.ikea.com","time_zone":"Europe/Paris","days":"wed,thu","hour":23,"minute":0,"interval_min":10}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"ssp-eu-wiab-ntx.ikea.com","time_zone":"Europe/Paris","days":"sun,mon","hour":22,"minute":0,"interval_min":8}', 11, 'Warehouse_EU', '{"pc":"ssp-eu-wiab-ntx.ikea.com","time_zone":"Europe/Paris","days":"tue,wed","hour":20,"minute":0,"interval_min":8}');
INSERT INTO [dbo].[dh_ntx_benchmark_scheduler] ([atm_schedule_json], [id], [index_label], [lcm_schedule_json]) VALUES ('{"pc":"wiab-dt-ntx.ikeadt.com","time_zone":"Europe/Paris","days":"sun,mon","hour":22,"minute":0,"interval_min":10}', 12, 'Warehouse_DT', '{"pc":"wiab-dt-ntx.ikeadt.com","time_zone":"Europe/Paris","days":"tue,wed","hour":20,"minute":0,"interval_min":10}');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_scheduler] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_site]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_site] ON


INSERT INTO [dbo].[dh_ntx_benchmark_site] ([id], [index_label], [site_bu_json]) VALUES (1, 'Retail-default', '{"distributed":{"bu_type":{"STORE":["R","RET"],"SERVICE OFFICE":["RET"],"CUSTOMER SERVICE CENTER":["RET"],"FINANCE":["FIN"],"INTER IKEA CENTER":["IIC"],"IKEA SERVICES":["IS"],"BUSINESS SERVICE CENTER":["BSC"],"TRADING SERVICE OFFICE":["TSO"],"IKEA IT":["IT"]}},"central":{"bu_type":"RET"}}');
INSERT INTO [dbo].[dh_ntx_benchmark_site] ([id], [index_label], [site_bu_json]) VALUES (2, 'Warehouse-default', '{"distributed":{"bu_type":["DS"]},"central":{"bu_type":["DS"]}}');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_site] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_storage]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_storage] ON


INSERT INTO [dbo].[dh_ntx_benchmark_storage] ([container_rules_json], [id], [index_label]) VALUES ('{"compression_enabled":true,"compression_delay_sec":0,"exceptions":["NutanixManagementShare"]}', 1, 'Retail-default');
INSERT INTO [dbo].[dh_ntx_benchmark_storage] ([container_rules_json], [id], [index_label]) VALUES ('{"compression_enabled":true,"compression_delay_sec":0,"exceptions":["NutanixManagementShare"]}', 2, 'Warehouse-default');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_storage] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_systems]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_systems] ON

INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('RETSGSNG-NXC000', '************', '{"endpoint":"************","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', '************', 'Use Global', 'RETSGSNG-NX4002.ikea.com', '["***********","***********","***********"]', 'ikea.com', 1, 'Retail_AP', 'ikea.com', 'ikea.com', '["ntp1-ap.ikea.com","ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com","ntp1-cn.ikea.com"]', 'dhov02.ikea.com', 'DPC_APAC_PROD', '************', 'APAC', 'SSP-APAC-NTX', NULL, NULL, NULL, NULL, '+9');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('RETCNCHN-NXC000', '************', '{"endpoint":"************","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', '************', 'Use Global', 'RETCNCHN-NX4002.ikea.com', '["***********","************","************"]', 'ikea.com', 2, 'Retail_China', 'ikea.com', 'ikea.com', '["ntp1-cn.ikea.com","ntp1-ap.ikea.com","ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com"]', 'dhov02.ikea.com', 'DPC_CN_PROD', '************', 'China', 'SSP-China-NTX', NULL, NULL, NULL, NULL, '+8');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('RETUSPHI-NXC000', '************', '{"endpoint":"************","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', '************', 'Use Global', 'RETUSPHI-NX4002.ikea.com', '["**********","**********","**********"]', 'ikea.com', 3, 'Retail_NA', 'ikea.com', 'ikea.com', '["ntp1-na.ikea.com","ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-ap.ikea.com","ntp1-cn.ikea.com"]', 'dhov02.ikea.com', 'DPC_NA_PROD', '************', 'NA', 'SSP-NA-NTX', NULL, NULL, NULL, NULL, '-4');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('RETSEHBG-NXC000', '*************', '{"endpoint":"*************","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', '*************', 'Use Global', 'RETSEHBG-NX4002.ikea.com', '["***********","**********","**********"]', 'ikea.com', 4, 'Retail_EU', 'ikea.com', 'ikea.com', '["ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com","ntp1-ap.ikea.com","ntp1-cn.ikea.com"]', 'dhov01.ikea.com', 'DPC_EU_PROD', '*************', 'EU', 'SSP-EU-NTX', NULL, NULL, NULL, NULL, '+2');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('RETSEELM-NXC000', '************', '{"endpoint":"**********","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', '**********', 'Use Global', 'RETSEELM-NX4033.ikea.com', '["***********","**********","**********"]', 'ikea.com', 5, 'Retail_PPE', 'ikea.com', 'ikea.com', '["ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com","ntp1-ap.ikea.com","ntp1-cn.ikea.com"]', 'dhov01.ikea.com', 'DPC_EU_PPE', '**********', 'EMEA', 'SSP-PPE-NTX', NULL, NULL, NULL, NULL, '+2');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('RETSEELM-NXC000', '**************', '{"endpoint":"**************","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', '**************', 'Use Global', 'PTSEELM-NX4003.ikeadt.com', '["************","************","0.0.0.0"]', 'ikeadt.com', 6, 'Retail_DT', 'ikeadt.com', 'ikeadt.com', '["ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com","ntp1-ap.ikea.com","ntp1-cn.ikea.com"]', 'dhov01.ikea.com', 'DPC_EU_DT', '**************', 'EMEA', 'SSP-DT-NTX', NULL, NULL, NULL, NULL, '+2');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('RETSEELM-NXC000', '************', '{"endpoint":"************","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', NULL, NULL, NULL, '["************","************","0.0.0.0"]', 'ikead2.com', 7, 'Retail_D2', 'ikead2.com', 'ikead2.com', '["ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com","ntp1-ap.ikea.com","ntp1-cn.ikea.com"]', 'dhov01.ikea.com', 'DPC_EU_D2', '************', 'EMEA', 'SSP-DHD2-NTX', NULL, NULL, NULL, NULL, '+2');

INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('DSSGSNG-NXC000', '************', '{"endpoint":"************","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', NULL, 'Use Global', NULL, '["***********","***********","***********"]', 'ikea.com', 8, 'Warehouse_AP', 'ikea.com', 'ikea.com', '["ntp1-ap.ikea.com","ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com","ntp1-cn.ikea.com"]', 'dhov02.ikea.com', 'WIAB_APAC_PROD', '************', 'APAC', 'SSP-APAC-WIAB-NTX', NULL, NULL, NULL, NULL, '+9');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('DSCNCHN-NXC000', '************', '{"endpoint":"************","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', NULL, 'Use Global', NULL, '["***********","************","************"]', 'ikea.com', 9, 'Warehouse_China', 'ikea.com', 'ikea.com', '["ntp1-cn.ikea.com","ntp1-ap.ikea.com","ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com"]', 'dhov02.ikea.com', 'WIAB_CN_PROD', '************', 'China', 'SSP-China-WIAB-NTX', NULL, NULL, NULL, NULL, '+8');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('DSUSDC6-NXC000', '************', '{"endpoint":"**********","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', NULL, 'Use Global', NULL, '["**********","**********","**********"]', 'ikea.com', 10, 'Warehouse_NA', 'ikea.com', 'ikea.com', '["ntp1-na.ikea.com","ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-ap.ikea.com","ntp1-cn.ikea.com"]', 'dhov02.ikea.com', 'WIAB_NA_PROD', '***********', 'NA', 'SSP-NA-WIAB-NTX', NULL, NULL, NULL, NULL, '-4');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('DSSEELM-NXC000', '*************', '{"endpoint":"***********","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', NULL, 'Use Global', NULL, '["***********","**********","**********"]', 'ikea.com', 11, 'Warehouse_EU', 'ikea.com', 'ikea.com', '["ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com","ntp1-ap.ikea.com","ntp1-cn.ikea.com"]', 'dhov01.ikea.com', 'WIAB_EU_PROD', '*************', 'EU', 'SSP-EU-WIAB-NTX', NULL, NULL, NULL, NULL, '+2');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('DSSE999-NXC000', '*************', '{"endpoint":"************","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', NULL, 'Use Global', NULL, '["************","************","0.0.0.0"]', 'ikeadt.com', 12, 'Warehouse_DT', 'ikeadt.com', 'ikeadt.com', '["ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com","ntp1-ap.ikea.com","ntp1-cn.ikea.com"]', 'dhov01.ikea.com', 'WIAB_EU_DT', '*************', 'EMEA', 'WIAB-DT-NTX', NULL, NULL, NULL, NULL, '+2');
INSERT INTO [dh_ntx_benchmark_systems] ([central_pe], [central_pe_ip], [dark_site_json], [dc_witness_ip], [dc_witness_max_latency], [dc_witness_name], [dns_servers_json], [dns_zone], [id], [index_label], [ldap_domain], [ldap_domain_host], [ntp_servers_json], [oneview], [oneview_scope], [pc_cluster_ip], [region], [self_service_name], [site_proxy_extra_white_list], [site_proxy_ip], [site_proxy_name], [site_proxy_port], [timezone]) VALUES ('RETSEHBG-NXC001', '*************', '{"endpoint":"*************","is_https":false,"dir":{"lcm":"/release","fw":"/1CN","help":"/help","local_base":"D:\\NTX_DarkSite\\wwwroot"},"auto_image_enabled":true}', '*************', 'Use Global', 'RETSEHBG-NX4002.ikea.com', '["***********","**********","**********"]', 'ikea.com', 13, 'Retail_EU1', 'ikea.com', 'ikea.com', '["ntp1-eu.ikea.com","ntp2-eu.ikea.com","ntp1-na.ikea.com","ntp1-ap.ikea.com","ntp1-cn.ikea.com"]', 'dhov01.ikea.com', 'DPC_EU_PROD', '*************', 'EU', 'SSP-EU1-NTX', NULL, NULL, NULL, NULL, '+2');

SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_systems] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_timer]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_timer] ON


INSERT INTO [dbo].[dh_ntx_benchmark_timer] ([id], [index_label], [timers_json]) VALUES (1, 'Retail-default', '{"site_wait_interval_sec":120,"node_wait_interval_sec":120,"site_wait_max_sec":20000,"node_wait_max_sec":20000}');
INSERT INTO [dbo].[dh_ntx_benchmark_timer] ([id], [index_label], [timers_json]) VALUES (2, 'Warehouse-default', '{"site_wait_interval_sec":120,"node_wait_interval_sec":120,"site_wait_max_sec":20000,"node_wait_max_sec":20000}');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_timer] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_vault]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_vault] ON


INSERT INTO [dbo].[dh_ntx_benchmark_vault] ([central_labels_json], [dc_labels_json], [engine], [id], [index_label], [master_namespace], [new_cluster_labels_json], [service_account], [site_labels_json], [tier_namespace]) VALUES ('{"site_gw_svc":"Site_Gw_Svc","site_pc_admin":"Site_Pc_Admin","site_pc_nutanix":"Site_Pc_Nutanix","site_pc_svc":"Site_Pc_Svc","site_witness_admin":"Site_Witness_Admin","site_witness_nutanix":"Site_Witness_Nutanix"}', '{"ad_query":"A_Central-Production/AdQuery","ad_query_upn":"A_Central-Production/AdQueryUPN","ansible_tower":"A_Central-Production/AnsibleTower","certificate_pki3":"A_Central-Production/CertificatePki3","certificate_request":"A_Central-Production/CertificateRequest","cmdb_smb":"A_Central-Production/CmdbSmb","gateway_ssh_priv_key":"A_Central-Production/GatewaySshPrivKey","gateway_ssh_pub_key":"A_Central-Production/GatewaySshPubKey","hpe_oneview":"A_Central-Production/HpeOneview","oob_default":"A_Central-Production/OobDefault","oob_default_nx":"A_Central-Production/OobDefaultNX","slack_hook":"A_Central-Production/SlackHook","solid_ipam":"A_Central-Production/SolidIpam","thors_hammer":"A_Central-Production/ThorsHammer","trip_wire":"A_Central-Production/TripWire","trip_wire_ssh_priv_key":"A_Central-Production/TripWIreSshPrivKey","trip_wire_ssh_pub_key":"A_Central-Production/TripWIreSshPubKey"}', 'NutanixClusters', 1, 'Retail-Production', 'dist-host-retail', '{"Site_Ahv_Root":"ahv_root_default","Site_Oob":"ilo_default","Site_Pe_Nutanix":"cvm_nutanix_default","Site_Pe_Svc":"svc_default","Site_Ahv_Nutanix":"ahv_nutanix_default","Site_Pe_Admin":"cvm_admin_default","Site_Gw_Priv_Key":"gw_priv_key_default","Site_Gw_Pub_Key":"gw_pub_key_default"}', 'vault_production', '{"site_ahv_admin": "Site_Ahv_Admin","site_ahv_admin_username": "admin","site_ahv_nutanix": "Site_Ahv_Nutanix","site_ahv_nutanix_username": "nutanix","site_ahv_root": "Site_Ahv_Root","site_ahv_root_username": "root","site_gw_priv_key": "Site_Gw_Priv_Key","site_gw_pub_key": "Site_Gw_Pub_Key","site_move": "Site_Move","site_move_username": "Svc_Move","site_oob": "Site_Oob","site_oob_username": "ADMIN","site_pe_admin": "Site_Pe_Admin","site_pe_admin_username": "admin","site_pe_nutanix": "Site_Pe_Nutanix","site_pe_nutanix_username": "nutanix","site_pe_svc": "Site_Pe_Svc","site_pe_svc_username": "1-click-nutanix"}', 'dhprod');
INSERT INTO [dbo].[dh_ntx_benchmark_vault] ([central_labels_json], [dc_labels_json], [engine], [id], [index_label], [master_namespace], [new_cluster_labels_json], [service_account], [site_labels_json], [tier_namespace]) VALUES ('{"site_gw_svc":"Site_Gw_Svc","site_pc_admin":"Site_Pc_Admin","site_pc_nutanix":"Site_Pc_Nutanix","site_pc_svc":"Site_Pc_Svc","site_witness_admin":"Site_Witness_Admin","site_witness_nutanix":"Site_Witness_Nutanix"}', '{"ad_query":"A_Central-PreProduction/AdQuery","ad_query_upn":"A_Central-PreProduction/AdQueryUPN","ansible_tower":"A_Central-PreProduction/AnsibleTower","certificate_pki3":"A_Central-PreProduction/CertificatePki3","certificate_request":"A_Central-PreProduction/CertificateRequest","cmdb_smb":"A_Central-PreProduction/CmdbSmb","gateway_ssh_priv_key":"A_Central-PreProduction/GatewaySshPrivKey","gateway_ssh_pub_key":"A_Central-PreProduction/GatewaySshPubKey","hpe_oneview":"A_Central-PreProduction/HpeOneview","oob_default":"A_Central-PreProduction/OobDefault","oob_default_nx":"A_Central-PreProduction/OobDefaultNX","slack_hook":"A_Central-PreProduction/SlackHook","solid_ipam":"A_Central-PreProduction/SolidIpam","thors_hammer":"A_Central-PreProduction/ThorsHammer","trip_wire":"A_Central-PreProduction/TripWire","trip_wire_ssh_priv_key":"A_Central-PreProduction/TripWIreSshPrivKey","trip_wire_ssh_pub_key":"A_Central-PreProduction/TripWIreSshPubKey"}', 'NutanixClusters', 2, 'Retail-PreProduction', 'dist-host-retail', '{"Site_Ahv_Root":"ahv_root_default","Site_Oob":"ilo_default","Site_Pe_Nutanix":"cvm_nutanix_default","Site_Pe_Svc":"svc_default","Site_Ahv_Nutanix":"ahv_nutanix_default","Site_Pe_Admin":"cvm_admin_default","Site_Gw_Priv_Key":"gw_priv_key_default","Site_Gw_Pub_Key":"gw_pub_key_default"}', 'vault_preproduction', '{"site_ahv_admin": "Site_Ahv_Admin","site_ahv_admin_username": "admin","site_ahv_nutanix": "Site_Ahv_Nutanix","site_ahv_nutanix_username": "nutanix","site_ahv_root": "Site_Ahv_Root","site_ahv_root_username": "root","site_gw_priv_key": "Site_Gw_Priv_Key","site_gw_pub_key": "Site_Gw_Pub_Key","site_move": "Site_Move","site_move_username": "Svc_Move","site_oob": "Site_Oob","site_oob_username": "ADMIN","site_pe_admin": "Site_Pe_Admin","site_pe_admin_username": "admin","site_pe_nutanix": "Site_Pe_Nutanix","site_pe_nutanix_username": "nutanix","site_pe_svc": "Site_Pe_Svc","site_pe_svc_username": "1-click-nutanix"}', 'dhppe');
INSERT INTO [dbo].[dh_ntx_benchmark_vault] ([central_labels_json], [dc_labels_json], [engine], [id], [index_label], [master_namespace], [new_cluster_labels_json], [service_account], [site_labels_json], [tier_namespace]) VALUES ('{"site_gw_svc":"Site_Gw_Svc","site_pc_admin":"Site_Pc_Admin","site_pc_nutanix":"Site_Pc_Nutanix","site_pc_svc":"Site_Pc_Svc","site_witness_admin":"Site_Witness_Admin","site_witness_nutanix":"Site_Witness_Nutanix"}', '{"ad_query":"A_Central-EMEA-DT/AdQuery","ad_query_upn":"A_Central-EMEA-DT/AdQueryUPN","ansible_tower":"A_Central-EMEA-DT/AnsibleTower","certificate_pki3":"A_Central-EMEA-DT/CertificatePki3","certificate_request":"A_Central-EMEA-DT/CertificateRequest","cmdb_smb":"A_Central-EMEA-DT/CmdbSmb","gateway_ssh_priv_key":"A_Central-EMEA-DT/GatewaySshPrivKey","gateway_ssh_pub_key":"A_Central-EMEA-DT/GatewaySshPubKey","hpe_oneview":"A_Central-EMEA-DT/HpeOneview","oob_default":"A_Central-EMEA-DT/OobDefault","oob_default_nx":"A_Central-EMEA-DT/OobDefaultNX","slack_hook":"A_Central-EMEA-DT/SlackHook","solid_ipam":"A_Central-EMEA-DT/SolidIpam","thors_hammer":"A_Central-EMEA-DT/ThorsHammer","trip_wire":"A_Central-EMEA-DT/TripWire","trip_wire_ssh_priv_key":"A_Central-EMEA-DT/TripWIreSshPrivKey","trip_wire_ssh_pub_key":"A_Central-EMEA-DT/TripWIreSshPubKey"}', 'NutanixClusters', 3, 'Retail-DT', 'dist-host-retail', '{"Site_Ahv_Root":"ahv_root_default","Site_Oob":"ilo_default","Site_Pe_Nutanix":"cvm_nutanix_default","Site_Pe_Svc":"svc_default","Site_Ahv_Nutanix":"ahv_nutanix_default","Site_Pe_Admin":"cvm_admin_default","Site_Gw_Priv_Key":"gw_priv_key_default","Site_Gw_Pub_Key":"gw_pub_key_default"}', 'vault_ikeadt', '{"site_ahv_admin": "Site_Ahv_Admin","site_ahv_admin_username": "admin","site_ahv_nutanix": "Site_Ahv_Nutanix","site_ahv_nutanix_username": "nutanix","site_ahv_root": "Site_Ahv_Root","site_ahv_root_username": "root","site_gw_priv_key": "Site_Gw_Priv_Key","site_gw_pub_key": "Site_Gw_Pub_Key","site_move": "Site_Move","site_move_username": "Svc_Move","site_oob": "Site_Oob","site_oob_username": "ADMIN","site_pe_admin": "Site_Pe_Admin","site_pe_admin_username": "admin","site_pe_nutanix": "Site_Pe_Nutanix","site_pe_nutanix_username": "nutanix","site_pe_svc": "Site_Pe_Svc","site_pe_svc_username": "1-click-nutanix"}', 'dhdt');
INSERT INTO [dbo].[dh_ntx_benchmark_vault] ([central_labels_json], [dc_labels_json], [engine], [id], [index_label], [master_namespace], [new_cluster_labels_json], [service_account], [site_labels_json], [tier_namespace]) VALUES ('{"site_gw_svc":"Site_Gw_Svc","site_pc_admin":"Site_Pc_Admin","site_pc_nutanix":"Site_Pc_Nutanix","site_pc_svc":"Site_Pc_Svc","site_witness_admin":"Site_Witness_Admin","site_witness_nutanix":"Site_Witness_Nutanix"}', '{"ad_query": "A_Central-EMEA-D2/AdQuery","ad_query_upn": "A_Central-EMEA-D2/AdQueryUPN","ansible_tower": "A_Central-EMEA-D2/AnsibleTower","certificate_pki3": "A_Central-EMEA-D2/CertificatePki3","certificate_request": "A_Central-EMEA-D2/CertificateRequest","cmdb_smb": "A_Central-EMEA-D2/CmdbSmb","gateway_ssh_priv_key": "A_Central-EMEA-D2/GatewaySshPrivKey","gateway_ssh_pub_key": "A_Central-EMEA-D2/GatewaySshPubKey","hpe_oneview": "A_Central-EMEA-D2/HpeOneview","oob_default": "A_Central-EMEA-D2/OobDefault","oob_default_nx": "A_Central-EMEA-D2/OobDefaultNX","slack_hook": "A_Central-EMEA-D2/SlackHook","solid_ipam": "A_Central-EMEA-D2/SolidIpam","thors_hamer": "A_Central-EMEA-D2/ThorsHammer","trip_wire": "A_Central-EMEA-D2/TripWire","trip_wire_ssh_priv_key": "A_Central-EMEA-D2/TripWIreSshPrivKey","trip_wire_ssh_pub_key":"A_Central-EMEA-D2/TripWIreSshPubKey"}', 'NutanixClusters', 4, 'Retail-D2', 'dist-host-retail', '{"Site_Ahv_Root":"ahv_root_default","Site_Oob":"ilo_default","Site_Pe_Nutanix":"cvm_nutanix_default","Site_Pe_Svc":"svc_default","Site_Ahv_Nutanix":"ahv_nutanix_default","Site_Pe_Admin":"cvm_admin_default","Site_Gw_Priv_Key":"gw_priv_key_default","Site_Gw_Pub_Key":"gw_pub_key_default"}', 'vault_ikead2', '{"site_ahv_admin": "Site_Ahv_Admin","site_ahv_admin_username": "admin","site_ahv_nutanix": "Site_Ahv_Nutanix","site_ahv_nutanix_username": "nutanix","site_ahv_root": "Site_Ahv_Root","site_ahv_root_username": "root","site_gw_priv_key": "Site_Gw_Priv_Key","site_gw_pub_key": "Site_Gw_Pub_Key","site_move": "Site_Move","site_move_username": "Svc_Move","site_oob": "Site_Oob","site_oob_username": "ADMIN","site_pe_admin": "Site_Pe_Admin","site_pe_admin_username": "admin","site_pe_nutanix": "Site_Pe_Nutanix","site_pe_nutanix_username": "nutanix","site_pe_svc": "Site_Pe_Svc","site_pe_svc_username": "1-click-nutanix"}', 'ikead2');
INSERT INTO [dbo].[dh_ntx_benchmark_vault] ([central_labels_json], [dc_labels_json], [engine], [id], [index_label], [master_namespace], [new_cluster_labels_json], [service_account], [site_labels_json], [tier_namespace]) VALUES ('{"site_gw_svc":"Site_Gw_Svc","site_pc_admin":"Site_Pc_Admin","site_pc_nutanix":"Site_Pc_Nutanix","site_pc_svc":"Site_Pc_Svc","site_witness_admin":"Site_Witness_Admin","site_witness_nutanix":"Site_Witness_Nutanix"}', '{"ad_query":"A_WIAB-EMEA/AdQuery","ad_query_upn":"A_WIAB-EMEA/AdQueryUPN","ansible_tower":"A_WIAB-EMEA/AnsibleTower","certificate_pki3":"A_WIAB-EMEA/PKI_account","certificate_request":"A_WIAB-EMEA/CertificateRequest","cmdb_smb":"A_WIAB-EMEA/CmdbSmb","gateway_ssh_priv_key":"A_WIAB-EMEA/GatewaySshPrivKey","gateway_ssh_pub_key":"A_WIAB-EMEA/GatewaySshPubKey","hpe_oneview":"A_WIAB-EMEA/HpeOneview","oob_default":"A_WIAB-EMEA/OobDefault","oob_default_nx":"A_WIAB-EMEA/OobDefaultNX","slack_hook":"A_WIAB-EMEA/SlackHook","solid_ipam":"A_WIAB-EMEA/SolidIpam","thors_hammer":"A_WIAB-EMEA/ThorsHammer","trip_wire":"A_WIAB-EMEA/TripWire","trip_wire_ssh_priv_key":"A_WIAB-EMEA/TripWIreSshPrivKey","trip_wire_ssh_pub_key":"A_WIAB-EMEA/TripWIreSshPubKey"}', 'NutanixClusters', 5, 'Warehouse-Production', 'digital-warehouse', '{"Site_Ahv_Root":"ahv_root_default","Site_Oob":"ilo_default","Site_Pe_Nutanix":"cvm_nutanix_default","Site_Pe_Svc":"svc_default","Site_Ahv_Nutanix":"ahv_nutanix_default","Site_Pe_Admin":"cvm_admin_default","Site_Gw_Priv_Key":"gw_priv_key_default","Site_Gw_Pub_Key":"gw_pub_key_default"}', 'vault_warehouse', '{"site_ahv_admin": "Site_Ahv_Admin","site_ahv_admin_username": "admin","site_ahv_nutanix": "Site_Ahv_Nutanix","site_ahv_nutanix_username": "nutanix","site_ahv_root": "Site_Ahv_Root","site_ahv_root_username": "root","site_gw_priv_key": "Site_Gw_Priv_Key","site_gw_pub_key": "Site_Gw_Pub_Key","site_move": "Site_Move","site_move_username": "Svc_Move","site_oob": "Site_Oob","site_oob_username": "ADMIN","site_pe_admin": "Site_Pe_Admin","site_pe_admin_username": "admin","site_pe_nutanix": "Site_Pe_Nutanix","site_pe_nutanix_username": "nutanix","site_pe_svc": "Site_Pe_Svc","site_pe_svc_username": "1-click-nutanix"}', 'dhprod');
INSERT INTO [dbo].[dh_ntx_benchmark_vault] ([central_labels_json], [dc_labels_json], [engine], [id], [index_label], [master_namespace], [new_cluster_labels_json], [service_account], [site_labels_json], [tier_namespace]) VALUES ('{"site_gw_svc":"Site_Gw_Svc","site_pc_admin":"Site_Pc_Admin","site_pc_nutanix":"Site_Pc_Nutanix","site_pc_svc":"Site_Pc_Svc","site_witness_admin":"Site_Witness_Admin","site_witness_nutanix":"Site_Witness_Nutanix"}', '{"ad_query":"A_WIAB-EMEA-PPE/AdQuery","ad_query_upn":"A_WIAB-EMEA-PPE/AdQueryUPN","ansible_tower":"A_WIAB-EMEA-PPE/AnsibleTower","certificate_pki3":"A_WIAB-EMEA-PPE/PKI_account","certificate_request":"A_WIAB-EMEA-PPE/CertificateRequest","cmdb_smb":"A_WIAB-EMEA-PPE/CmdbSmb","gateway_ssh_priv_key":"A_WIAB-EMEA-PPE/GatewaySshPrivKey","gateway_ssh_pub_key":"A_WIAB-EMEA-PPE/GatewaySshPubKey","hpe_oneview":"A_WIAB-EMEA-PPE/HpeOneview","oob_default":"A_WIAB-EMEA-PPE/OobDefault","oob_default_nx":"A_WIAB-EMEA-PPE/OobDefaultNX","slack_hook":"A_WIAB-EMEA-PPE/SlackHook","solid_ipam":"A_WIAB-EMEA-PPE/SolidIpam","thors_hammer":"A_WIAB-EMEA-PPE/ThorsHammer","trip_wire":"A_WIAB-EMEA-PPE/TripWire","trip_wire_ssh_priv_key":"A_WIAB-EMEA-PPE/TripWIreSshPrivKey","trip_wire_ssh_pub_key":"A_WIAB-EMEA-PPE/TripWIreSshPubKey"}', 'NutanixClusters', 6, 'Warehouse-PreProduction', 'digital-warehouse', '{"Site_Ahv_Root":"ahv_root_default","Site_Oob":"ilo_default","Site_Pe_Nutanix":"cvm_nutanix_default","Site_Pe_Svc":"svc_default","Site_Ahv_Nutanix":"ahv_nutanix_default","Site_Pe_Admin":"cvm_admin_default","Site_Gw_Priv_Key":"gw_priv_key_default","Site_Gw_Pub_Key":"gw_pub_key_default"}', 'vault_warehoused2', '{"site_ahv_admin": "Site_Ahv_Admin","site_ahv_admin_username": "admin","site_ahv_nutanix": "Site_Ahv_Nutanix","site_ahv_nutanix_username": "nutanix","site_ahv_root": "Site_Ahv_Root","site_ahv_root_username": "root","site_gw_priv_key": "Site_Gw_Priv_Key","site_gw_pub_key": "Site_Gw_Pub_Key","site_move": "Site_Move","site_move_username": "Svc_Move","site_oob": "Site_Oob","site_oob_username": "ADMIN","site_pe_admin": "Site_Pe_Admin","site_pe_admin_username": "admin","site_pe_nutanix": "Site_Pe_Nutanix","site_pe_nutanix_username": "nutanix","site_pe_svc": "Site_Pe_Svc","site_pe_svc_username": "1-click-nutanix"}', 'dhppe');
INSERT INTO [dbo].[dh_ntx_benchmark_vault] ([central_labels_json], [dc_labels_json], [engine], [id], [index_label], [master_namespace], [new_cluster_labels_json], [service_account], [site_labels_json], [tier_namespace]) VALUES ('{"site_gw_svc":"Site_Gw_Svc","site_pc_admin":"Site_Pc_Admin","site_pc_nutanix":"Site_Pc_Nutanix","site_pc_svc":"Site_Pc_Svc","site_witness_admin":"Site_Witness_Admin","site_witness_nutanix":"Site_Witness_Nutanix"}', '{"ad_query":"A_WIAB-EMEA-DT/AdQuery","ad_query_upn":"A_WIAB-EMEA-DT/AdQueryUPN","ansible_tower":"A_WIAB-EMEA-DT/AnsibleTower","certificate_pki3":"A_WIAB-EMEA-DT/PKI_account","certificate_request":"A_WIAB-EMEA-DT/CertificateRequest","cmdb_smb":"A_WIAB-EMEA-DT/CmdbSmb","gateway_ssh_priv_key":"A_WIAB-EMEA-DT/GatewaySshPrivKey","gateway_ssh_pub_key":"A_WIAB-EMEA-DT/GatewaySshPubKey","hpe_oneview":"A_WIAB-EMEA-DT/HpeOneview","oob_default":"A_WIAB-EMEA-DT/OobDefault","oob_default_nx":"A_WIAB-EMEA-DT/OobDefaultNX","slack_hook":"A_WIAB-EMEA-DT/SlackHook","solid_ipam":"A_WIAB-EMEA-DT/SolidIpam","thors_hammer":"A_WIAB-EMEA-DT/ThorsHammer","trip_wire":"A_WIAB-EMEA-DT/TripWire","trip_wire_ssh_priv_key":"A_WIAB-EMEA-DT/TripWIreSshPrivKey","trip_wire_ssh_pub_key":"A_WIAB-EMEA-DT/TripWIreSshPubKey"}', 'NutanixClusters', 7, 'Warehouse-DT', 'digital-warehouse', '{"Site_Ahv_Root":"ahv_root_default","Site_Oob":"ilo_default","Site_Pe_Nutanix":"cvm_nutanix_default","Site_Pe_Svc":"svc_default","Site_Ahv_Nutanix":"ahv_nutanix_default","Site_Pe_Admin":"cvm_admin_default","Site_Gw_Priv_Key":"gw_priv_key_default","Site_Gw_Pub_Key":"gw_pub_key_default"}', 'vault_WAREHOUSEDT', '{"site_ahv_admin": "Site_Ahv_Admin","site_ahv_admin_username": "admin","site_ahv_nutanix": "Site_Ahv_Nutanix","site_ahv_nutanix_username": "nutanix","site_ahv_root": "Site_Ahv_Root","site_ahv_root_username": "root","site_gw_priv_key": "Site_Gw_Priv_Key","site_gw_pub_key": "Site_Gw_Pub_Key","site_move": "Site_Move","site_move_username": "Svc_Move","site_oob": "Site_Oob","site_oob_username": "ADMIN","site_pe_admin": "Site_Pe_Admin","site_pe_admin_username": "admin","site_pe_nutanix": "Site_Pe_Nutanix","site_pe_nutanix_username": "nutanix","site_pe_svc": "Site_Pe_Svc","site_pe_svc_username": "1-click-nutanix"}', 'dhdt');


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_vault] OFF


-- ----------------------------
-- Records of [dh_ntx_benchmark_vlan_config]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_vlan_config] ON


INSERT INTO [dbo].[dh_ntx_benchmark_vlan_config] ([ahv_cvm_vlan], [id], [index_label], [oob_vlan], [pc_vlan]) VALUES (157, 1, 'store-default', 158, 157);
INSERT INTO [dbo].[dh_ntx_benchmark_vlan_config] ([ahv_cvm_vlan], [id], [index_label], [oob_vlan], [pc_vlan]) VALUES (357, 2, 'so-default', 358, 357);
INSERT INTO [dbo].[dh_ntx_benchmark_vlan_config] ([ahv_cvm_vlan], [id], [index_label], [oob_vlan], [pc_vlan]) VALUES (557, 3, 'csc-default', 558, 557);
INSERT INTO [dbo].[dh_ntx_benchmark_vlan_config] ([ahv_cvm_vlan], [id], [index_label], [oob_vlan], [pc_vlan]) VALUES (2920, 4, 'dt-default', 2928, 2920);
INSERT INTO [dbo].[dh_ntx_benchmark_vlan_config] ([ahv_cvm_vlan], [id], [index_label], [oob_vlan], [pc_vlan]) VALUES (788, 5, 'd2-550', 790, 788);
INSERT INTO [dbo].[dh_ntx_benchmark_vlan_config] ([ahv_cvm_vlan], [id], [index_label], [oob_vlan], [pc_vlan]) VALUES (710, 6, 'd2-551', 749, 710);
INSERT INTO [dbo].[dh_ntx_benchmark_vlan_config] ([ahv_cvm_vlan], [id], [index_label], [oob_vlan], [pc_vlan]) VALUES (793, 7, 'd2-552', 795, 793);
INSERT INTO [dbo].[dh_ntx_benchmark_vlan_config] ([ahv_cvm_vlan], [id], [index_label], [oob_vlan], [pc_vlan]) VALUES (157, 8, 'warehouse-default', 158, 157);
INSERT INTO [dbo].[dh_ntx_benchmark_vlan_config] ([ahv_cvm_vlan], [id], [index_label], [oob_vlan], [pc_vlan]) VALUES (2222, 9, 'eu-central', 295, 2222);


SET IDENTITY_INSERT [dbo].[dh_ntx_benchmark_vlan_config] OFF

-- ----------------------------
-- Resetting central PE relationship
-- ----------------------------
-- Retail
UPDATE dh_retail_ntx_pe
SET is_central_pe='true'
WHERE fqdn IN (
  SELECT central_pe_fqdn
  FROM dh_retail_ntx_pc
)

UPDATE dh_retail_ntx_pe
SET is_central_pe='false'
WHERE fqdn NOT IN (
  SELECT central_pe_fqdn
  FROM dh_retail_ntx_pc
)
-- Warehouse
UPDATE dh_wh_ntx_pe
SET is_central_pe='true'
WHERE fqdn IN (
  SELECT central_pe_fqdn
  FROM dh_wh_ntx_pc
)

UPDATE dh_wh_ntx_pe
SET is_central_pe='false'
WHERE fqdn NOT IN (
  SELECT central_pe_fqdn
  FROM dh_wh_ntx_pc
)

-- ----------------------------
-- Mapping benchmark id to PC/PE tables for Retail/Warehouse
-- ----------------------------
-- Retail
-- PE
-- Store AP
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'Store-AP'
)
WHERE prism='ssp-apac-ntx.ikea.com' AND site_code LIKE '[0-9][0-9][0-9]' AND is_central_pe='false'

-- Store CN
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'Store-CN'
)
WHERE prism='ssp-china-ntx.ikea.com' AND site_code LIKE '[0-9][0-9][0-9]' AND is_central_pe='false'

-- Store NA
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'Store-NA'
)
WHERE prism='ssp-na-ntx.ikea.com' AND (site_code LIKE '[0-9][0-9][0-9]' OR site_code='MXC') AND is_central_pe='false'

-- Store EU
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'Store-EU'
)
WHERE prism='ssp-eu-ntx.ikea.com' AND (site_code LIKE '[0-9][0-9]' OR site_code LIKE '[0-9][0-9][0-9]') AND is_central_pe='false'

-- Store EU1
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'Store-EU1'
)
WHERE prism='ssp-eu1-ntx.ikea.com' AND (site_code LIKE '[0-9][0-9]' OR site_code LIKE '[0-9][0-9][0-9]') AND is_central_pe='false'

-- Store 12852
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'Store-EU'
)
WHERE site_code='12852'

-- Store PPE
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'Store-PPE'
)
WHERE prism='ssp-ppe-ntx.ikea.com' AND site_code LIKE '[0-9][0-9][0-9]' AND is_central_pe='false'

-- Store DT
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'Store-DT'
)
WHERE prism='ssp-dt-ntx.ikeadt.com' AND site_code LIKE '[0-9][0-9][0-9]' AND is_central_pe='false'

-- Store D2
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'Store-D2'
)
WHERE prism='ssp-dhd2-ntx.ikead2.com' AND site_code LIKE '[0-9][0-9][0-9]' AND is_central_pe='false'

-- SO AP
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'SO-AP'
)
WHERE prism='ssp-apac-ntx.ikea.com' AND (name LIKE '%SO-NXC00%' OR name LIKE '%SOS-NXC00%' ) AND is_central_pe='false'

-- SO CN
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'SO-CN'
)
WHERE prism='ssp-china-ntx.ikea.com' AND (name LIKE '%SO-NXC00%' OR name LIKE '%SOS-NXC00%' ) AND is_central_pe='false'

-- SO NA
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'SO-NA'
)
WHERE prism='ssp-na-ntx.ikea.com' AND (name LIKE '%SO-NXC00%' OR name LIKE '%SOS-NXC00%' ) AND is_central_pe='false'

-- SO EU
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'SO-EU'
)
WHERE prism='ssp-eu-ntx.ikea.com' AND (name LIKE '%SO-NXC00%' OR name LIKE '%SOS-NXC00%' ) AND is_central_pe='false'

-- SO EU1
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'SO-EU1'
)
WHERE prism='ssp-eu1-ntx.ikea.com' AND (name LIKE '%SO-NXC00%' OR name LIKE '%SOS-NXC00%' ) AND is_central_pe='false'

-- CSC AP
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'CSC-AP'
)
WHERE prism='ssp-apac-ntx.ikea.com' AND (name LIKE '%CSC-NXC00%' OR name LIKE '%C0_-NXC00%' ) AND is_central_pe='false'

-- CSC CN
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'CSC-CN'
)
WHERE prism='ssp-china-ntx.ikea.com' AND (name LIKE '%CSC-NXC00%' OR name LIKE '%C0_-NXC00%' ) AND is_central_pe='false'

-- CSC NA
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'CSC-NA'
)
WHERE prism='ssp-na-ntx.ikea.com' AND (name LIKE '%CSC-NXC00%' OR name LIKE '%C0_-NXC00%' ) AND is_central_pe='false'

-- CSC EU
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'CSC-EU'
)
WHERE prism='ssp-eu-ntx.ikea.com' AND (name LIKE '%CSC-NXC00%' OR name LIKE '%C0_-NXC00%' ) AND is_central_pe='false'

-- CSC EU1
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'CSC-EU1'
)
WHERE prism='ssp-eu1-ntx.ikea.com' AND (name LIKE '%CSC-NXC00%' OR name LIKE '%C0_-NXC00%' ) AND is_central_pe='false'

-- CSC RETDEBER, RETDEROS, FINCHPRA, BSCPLPOZ, FINNLHQ
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'CSC-EU'
)
WHERE prism='ssp-eu-ntx.ikea.com' AND site_code IN ('BER', 'ROS', 'PRA', 'POZ', 'HQ')

UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'CSC-EU1'
)
WHERE prism='ssp-eu1-ntx.ikea.com' AND site_code IN ('BER', 'ROS', 'PRA', 'POZ', 'HQ')

-- Central AP
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-AP-1'
)
WHERE prism='ssp-apac-ntx.ikea.com' AND is_central_pe='true'

-- Central CN
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-CN-1'
)
WHERE prism='ssp-china-ntx.ikea.com' AND is_central_pe='true'

-- Central NA
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-NA-1'
)
WHERE prism='ssp-na-ntx.ikea.com' AND is_central_pe='true'

-- Central EU
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-EU-1'
)
WHERE (prism='ssp-eu-ntx.ikea.com' OR prism='ssp-eu1-ntx.ikea.com') AND is_central_pe='true'

-- Central EU1
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-EU1-1'
)
WHERE prism='ssp-eu1-ntx.ikea.com' AND is_central_pe='true'

-- Central PPE
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-PPE-1'
)
WHERE prism='ssp-ppe-ntx.ikea.com' AND is_central_pe='true'

-- Central DT
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-DT-1'
)
WHERE prism='ssp-dt-ntx.ikeadt.com' AND is_central_pe='true'

-- Central D2
UPDATE dh_retail_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-D2-1'
)
WHERE prism='ssp-dhd2-ntx.ikead2.com' AND is_central_pe='true'

-- PC
-- Central AP
UPDATE dh_retail_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-AP-2'
)
WHERE fqdn='ssp-apac-ntx.ikea.com'

-- Central CN
UPDATE dh_retail_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-CN-2'
)
WHERE fqdn='ssp-china-ntx.ikea.com'

-- Central NA
UPDATE dh_retail_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-NA-2'
)
WHERE fqdn='ssp-na-ntx.ikea.com'

-- Central EU
UPDATE dh_retail_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-EU-2'
)
WHERE fqdn='ssp-eu-ntx.ikea.com'

-- Central EU1
UPDATE dh_retail_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-EU1-2'
)
WHERE fqdn='ssp-eu1-ntx.ikea.com'

-- Central PPE
UPDATE dh_retail_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-PPE-2'
)
WHERE fqdn='ssp-ppe-ntx.ikea.com'

-- Central DT
UPDATE dh_retail_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-DT-2'
)
WHERE fqdn='ssp-dt-ntx.ikeadt.com'

-- Central D2
UPDATE dh_retail_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'RET-Central-D2-2'
)
WHERE fqdn='ssp-dhd2-ntx.ikead2.com'

-- Warehouse
-- PE
-- DS AP
UPDATE dh_wh_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-AP'
)
WHERE prism='ssp-apac-wiab-ntx.ikea.com' AND site_code LIKE '[0-9][0-9][0-9]' AND is_central_pe='false'

-- DS CN
UPDATE dh_wh_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-CN'
)
WHERE prism='ssp-china-wiab-ntx.ikea.com' AND site_code LIKE '[0-9][0-9][0-9]' AND is_central_pe='false'

-- DS NA
UPDATE dh_wh_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-NA'
)
WHERE prism='ssp-na-wiab-ntx.ikea.com' AND site_code LIKE '[0-9][0-9][0-9]' AND is_central_pe='false'

-- DS EU
UPDATE dh_wh_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-EU'
)
WHERE prism='ssp-eu-wiab-ntx.ikea.com' AND site_code LIKE '[0-9][0-9][0-9]' AND is_central_pe='false'

-- DS DT
UPDATE dh_wh_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-DT'
)
WHERE prism='wiab-dt-ntx.ikeadt.com' AND is_central_pe='false'

-- Central AP
UPDATE dh_wh_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-Central-AP-1'
)
WHERE prism='ssp-apac-wiab-ntx.ikea.com' AND is_central_pe='true'

-- Central CN
UPDATE dh_wh_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-Central-CN-1'
)
WHERE prism='ssp-china-wiab-ntx.ikea.com' AND is_central_pe='true'

-- Central NA
UPDATE dh_wh_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-Central-NA-1'
)
WHERE prism='ssp-na-wiab-ntx.ikea.com' AND is_central_pe='true'

-- Central EU
UPDATE dh_wh_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-Central-EU-1'
)
WHERE prism='ssp-eu-wiab-ntx.ikea.com' AND is_central_pe='true'

-- Central DT
UPDATE dh_wh_ntx_pe
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-Central-DT-1'
)
WHERE prism='wiab-dt-ntx.ikeadt.com' AND is_central_pe='true'

-- PC
-- Central AP
UPDATE dh_wh_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-Central-AP-2'
)
WHERE fqdn='ssp-apac-wiab-ntx.ikea.com'

-- Central CN
UPDATE dh_wh_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-Central-CN-2'
)
WHERE fqdn='ssp-china-wiab-ntx.ikea.com'

-- Central NA
UPDATE dh_wh_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-Central-NA-2'
)
WHERE fqdn='ssp-na-wiab-ntx.ikea.com'

-- Central EU
UPDATE dh_wh_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-Central-EU-2'
)
WHERE fqdn='ssp-eu-wiab-ntx.ikea.com'

-- Central DT
UPDATE dh_wh_ntx_pc
SET bmk_id=(
	SELECT id
  FROM dh_ntx_benchmark
  WHERE name = 'DS-Central-DT-2'
)
WHERE fqdn='wiab-dt-ntx.ikeadt.com'


# Python Script
# Created by : <PERSON><PERSON>
# 25-April-2023
# <PERSON><PERSON><PERSON> to compare <PERSON><PERSON>ts name & version with task scheduler and delete old version with new version accordingly in Windows Task Scheduler .
import logging
import os
import re
import sys
import win32com.client
import json
import pywintypes
import win32api
import xmltodict
from typing import Final
from datetime import datetime

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
from dpcdata.ntx.jobs.scripts.TaskEnumeration import TaskEnumeration


def map_task_name_and_version(task_names):
    """
    Input:
        task_names = ['Run-Job-RetailNtxPc-v1', 'Run-Job-RetailNtxPe-TimeZone-v1']
    Output:
        mapping = {
            "Run-Job-RetailNtxPc": "v1",
            "Run-Job-RetailNtxPe-TimeZone": "v1"
        }
    """
    mapping = dict()
    for task in task_names:
        mapping[task.rsplit('-', 1)[0]] = task.rsplit('-', 1)[1]
    return mapping


TASK_FOLDER: Final[str] = "\\UnitPortal"
TASK_CONFIG_FILE: Final[str] = "schedule_task.json"
DEFAULT_TASK_START_BOUNDARY: Final[str] = "2023-01-01T00:00:00"
DEFAULT_TASK_ENABLED: Final[bool] = True
DEFAULT_TASK_DESCRIPTION: Final[str] = ""
TASK_SCHEDULER_ACTION = "task_scheduler_action"
ADD_ACTION = "ADD"
UPDATE_ACTION = "UPDATE"
DELETE_ACTION = "DELETE"


class TaskSchedulerHandler:
    def __init__(self):
        self.log = None
        self.setup_logger()
        self.scheduler = win32com.client.Dispatch('Schedule.Service')
        self.scheduler.Connect()
        try:
            self.root_folders = self.scheduler.GetFolder(TASK_FOLDER)
        except pywintypes.com_error:
            folder = self.scheduler.GetFolder("\\")
            folder.CreateFolder(TASK_FOLDER)
            self.root_folders = self.scheduler.GetFolder(TASK_FOLDER)

        self.script_folder = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))

        self.task_config = self.parse_config()
        self.local_scripts = self.load_local_scripts()
        self.existing_tasks = self.load_existing_tasks()
        self.existing_task_names = [task.name for task in self.existing_tasks]
        self.tasks_to_delete = None

    def setup_logger(self):
        self.log = logging.getLogger(__name__)
        self.log.setLevel(logging.DEBUG)
        # log_format = logging.Formatter('[%(asctime)s] [%(levelname)s] - %(message)s')
        # console_handler = logging.StreamHandler(sys.stdout)
        # console_handler.setLevel(logging.DEBUG)
        # console_handler.setFormatter(log_format)
        # self.log.addHandler(console_handler)

    def parse_config(self):
        with open(f"{self.script_folder}\\{TASK_CONFIG_FILE}") as f:
            return json.loads(f.read())['Tasks']

    def load_task_action(self):
        defined_task_names = []
        for i, item in enumerate(self.task_config):
            task_name = item["General"]["Name"]
            defined_task_names.append(task_name)
            if task_name not in self.existing_task_names:
                item[TASK_SCHEDULER_ACTION] = ADD_ACTION
            elif self.is_config_updated(task_name, item):
                item[TASK_SCHEDULER_ACTION] = UPDATE_ACTION
        self.tasks_to_delete = [
            task_name for task_name in self.existing_task_names if task_name not in defined_task_names
        ]
        self.log.debug(f"tasks to delete: {self.tasks_to_delete}")

    def is_config_updated(self, task_name, item):
        task = self.root_folders.getTask(task_name)
        # trigger = task.Definition.Triggers.Item()   # Not implemented! Have to use task.XmlText
        existing_config = xmltodict.parse(task.Xml)
        calendar_trigger = existing_config['Task']['Triggers']["CalendarTrigger"]
        settings = existing_config['Task']['Settings']["IdleSettings"]

        def is_enabled_updated():
            self.log.debug("Checking if Triggers.CalendarTrigger.Enabled updated...")
            return task.Enabled != item["Triggers"]["CalendarTrigger"]["Enabled"]

        def is_start_boundary_updated():
            self.log.debug("Checking if Triggers.CalendarTrigger.StartBoundary updated...")
            return datetime.strptime(calendar_trigger.get("StartBoundary"), '%Y-%m-%dT%H:%M:%S') \
                != datetime.strptime(item["Triggers"]["CalendarTrigger"]["StartBoundary"], '%Y-%m-%dT%H:%M:%S')

        def is_days_interval_updated():
            self.log.debug("Checking if Triggers.CalendarTrigger.ScheduleByDay.DaysInterval updated...")
            return int(calendar_trigger.get("ScheduleByDay").get("DaysInterval")) \
                != int(item["Triggers"]["CalendarTrigger"]["ScheduleByDay"]["DaysInterval"])

        def is_wait_timeout_updated():
            self.log.debug("Checking if Settings.IdleSettings.WaitTimeout updated...")
            return settings.get("WaitTimeout") != item["Settings"]["IdleSettings"]["WaitTimeout"]

        return is_enabled_updated() or is_start_boundary_updated() or is_days_interval_updated() \
            or is_wait_timeout_updated()

    def load_local_scripts(self):
        local_scripts = []
        for path in os.listdir(self.script_folder):
            file = os.path.join(self.script_folder, path)
            if os.path.isfile(file) and re.match(r".*\-v\d+\.ps1", file):
                local_scripts.append(path)
        return local_scripts

    # Function returns TASK names as list variable from task scheduler folder.  
    def load_existing_tasks(self):
        existing_tasks = []
        # currently only handle 1 level folder
        for task in self.root_folders.GetTasks(not TaskEnumeration.TASK_ENUM_HIDDEN):
            existing_tasks.append(task)
        return existing_tasks

    def register_tasks_from_config(self):
        self.load_task_action()
        self.create_or_update_tasks_from_config()
        self.delete_tasks(self.tasks_to_delete)

    def create_or_update_tasks_from_config(self):
        self.log.info("Start to create/update tasks...")
        for i, task in enumerate(self.task_config):
            if not task.get(TASK_SCHEDULER_ACTION) == ADD_ACTION and not task.get(
                    TASK_SCHEDULER_ACTION) == UPDATE_ACTION:
                continue
            task_name = task['General']['Name']
            self.log.info(
                f"Start to {'create' if task.get(TASK_SCHEDULER_ACTION) == ADD_ACTION else 'update'} task: {task_name}: {task}")
            task_def = self.scheduler.NewTask(0)
            # Create Trigger
            trigger = task_def.Triggers.Create(TaskEnumeration.TASK_TRIGGER_DAILY)
            trigger.StartBoundary = task["Triggers"]["CalendarTrigger"]["StartBoundary"]
            trigger.DaysInterval = task["Triggers"]["CalendarTrigger"]["ScheduleByDay"]["DaysInterval"]
            trigger.Enabled = task["Triggers"]["CalendarTrigger"]["Enabled"]

            # Create action
            action = task_def.Actions.Create(TaskEnumeration.TASK_ACTION_EXEC)
            action.Path = 'pwsh.exe'
            action.Arguments = f"-file {self.script_folder}\\{task_name}.ps1"

            # Other task settings
            task_def.RegistrationInfo.Description = task["General"]["Description"]

            # Define principal, to "Run whether user is logged on or not" & "Run with highest privileges"
            task_def.Principal.UserID = "SYSTEM"
            task_def.Principal.DisplayName = "SYSTEM"
            # task_def.Principal.GroupID = "Administrators"
            task_def.Principal.LogonType = TaskEnumeration.TASK_LOGON_SERVICE_ACCOUNT
            task_def.Principal.RunLevel = TaskEnumeration.TASK_RUNLEVEL_HIGHEST

            task_def.Settings.IdleSettings.WaitTimeout = task["Settings"]["IdleSettings"]["WaitTimeout"]

            try:
                # Create/Update task
                self.root_folders.RegisterTaskDefinition(
                    task_name.removesuffix(".ps1"),
                    task_def,
                    TaskEnumeration.TASK_CREATE_OR_UPDATE,
                    task_def.Principal.UserID,
                    None,
                    TaskEnumeration.TASK_LOGON_SERVICE_ACCOUNT
                )
            except pywintypes.com_error as e:
                # e: (-**********, 'Exception occurred.', (0, None, '(44,8):Count:', None, 0, -**********), None)
                self.log.error(f"Origin error: {e}")
                self.log.error(f"Outside error: {e.strerror}")
                self.log.error(f"Inside error: {win32api.FormatMessage(e.excepinfo[-1])}")
                sys.exit(0)
        self.log.info("registering the latest scripts in task scheduler is completed.")

    def delete_tasks(self, task_list):
        self.log.info(f"Start to delete tasks: {task_list}")
        for item_task_name in task_list:
            try:
                self.root_folders.DeleteTask(item_task_name, 0)
                self.log.info(f"Item Name :: {item_task_name}")
                self.log.info("Item Deleted successfully.")
            except pywintypes.com_error as e:
                # e: (-**********, 'Exception occurred.', (0, None, '(44,8):Count:', None, 0, -**********), None)
                self.log.error(f"Origin error: {e}")
                self.log.error(f"Outside error: {e.strerror}")
                self.log.error(f"Inside error: {win32api.FormatMessage(e.excepinfo[-1])}")
                sys.exit(0)
        self.log.info("old version task schedule items have been deleted.")


if __name__ == "__main__":
    # if not pyuac.isUserAdmin():
    # print("Re-launching as admin!")
    # pyuac.runAsAdmin()
    handler = TaskSchedulerHandler()
    handler.register_tasks_from_config()

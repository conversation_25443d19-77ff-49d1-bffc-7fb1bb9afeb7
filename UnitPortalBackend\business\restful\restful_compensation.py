from flask_restful import Resource
from flask import abort, request, jsonify
#from business.authentication.tokenvalidation import admintokencheck, PrivilegeValidation
from business.distributedhosting.calendar.compen import Compensation
from business.generic.commonfunc import  get_request_token
from .route import route


@route('/api/v1/compen/get_memo_id')
class RestfulGetMemoID(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    def get(self):
        try:
            token = get_request_token()
            print(token)
            _compen = Compensation(token=token)
            if memo_id := _compen.get_memo_id():
                print(memo_id)
                username = memo_id.memo_id
                if "gst" in username.lower():     # user using memo id to login
                    username = username[3::]
                return username.upper()
            raise Exception("Failed to get the memo_id.")
        except:
            abort(500, "Internal error")
  
   
@route('/api/v1/compen/list_gain_compen')
class RestfulListCompensationGain(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    def get(self):
        try:
            # token = get_request_token()#{'Authorization':"bearer ****-*****-*****-****"}
            _compen = Compensation()
            if compen_list := _compen.get_compensation_gain():
                return jsonify(compen_list)
            raise Exception("Failed to get the gain compen list.")
        except:
            abort(500, "Internal error")
 
            
@route('/api/v1/compen/add_gain_compen')
class RestfulAddCompensationGain(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    def post(self):
        try:
            if param := request.get_json(force=True):
                for _key in ['start_date', 'reason', 'end_date', 'gain_hours', 'total_hours']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) 
            else:
                raise Exception({'code': 400, 'message': 'Missing Paramaters'}) 
            token = get_request_token()
            print(token)
            print(param)
            _compen = Compensation(token=token)
            if add_compen := _compen.save_compensation_gain(param):
                return jsonify(add_compen)
            raise Exception("Failed to add the gain compen.")
        except:
            abort(500, "Internal error")
            
            
@route('/api/v1/compen/update_gain_compen')
class RestfulUpdateCompensationGain(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    def post(self):
        try:
            if param := request.get_json(force=True):
                for _key in ['start_date', 'reason', 'end_date', 'gain_hours', 'total_hours']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) 
            else:
                raise Exception({'code': 400, 'message': 'Missing Paramaters'}) 
            token = get_request_token()
            print(token)
            print(param)
            _compen = Compensation(token=token)
            if update_compen := _compen.update_compemsation_gain(param):
                return jsonify(update_compen)
            raise Exception("Failed to update the gain compen.")
        except:
            abort(500, "Internal error")
            
            
@route('/api/v1/compen/delete_gain_compen')
class RestfulDeleteCompensationGain(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    def post(self):
        try:
            param = request.get_json(force=True)
            token = get_request_token()
            print(token)
            print(param)
            _compen = Compensation(token=token)
            if delete_compen := _compen.delete_compensation_gain(param):
                return jsonify(delete_compen)
            raise Exception("Failed to delete the gain compen.")
        except:
            abort(500, "Internal error")


@route('/api/v1/compen/delete_use_compen')
class RestfulDeleteCompensationUse(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    def post(self):
        try:
            param = request.get_json(force=True)
            token = get_request_token()
            print(token)
            print(param)
            _compen = Compensation(token=token)
            if delete_compen := _compen.delete_compensation_use(param):
                return jsonify(delete_compen)
            raise Exception("Failed to delete the used compen.")
        except:
            abort(500, "Internal error")


@route('/api/v1/compen/update_use_compen')
class RestfulUpdateCompensationUse(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    def post(self):
        try:
            if param := request.get_json(force=True):
                for _key in ['start_date', 'reason', 'end_date', 'total_hours']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) 
            else:
                raise Exception({'code': 400, 'message': 'Missing Paramaters'}) 
            token = get_request_token()
            print(token)
            print(param)
            _compen = Compensation(token=token)
            if update_compen := _compen.update_compemsation_use(param):
                return jsonify(update_compen)
            raise Exception("Failed to update the used compen.")
        except:
            abort(500, "Internal error")


@route('/api/v1/compen/add_use_compen')
class RestfulAddCompensationUse(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    def post(self):
        try:
            if param := request.get_json(force=True):
                for _key in ['start_date', 'reason', 'end_date', 'total_hours']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) 
            else:
                raise Exception({'code': 400, 'message': 'Missing Paramaters'}) 
            token = get_request_token()
            print(token)
            print(param)
            _compen = Compensation(token=token)
            if add_compen := _compen.save_compensation_use(param):
                return jsonify(add_compen)
            raise Exception("Failed to add the used compen.")
        except:
            abort(500, "Internal error")


@route('/api/v1/compen/list_use_compen')
class RestfulListCompensationUse(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    def get(self):
        try:
            token = get_request_token()
            _compen = Compensation(token=token)
            if list_compen := _compen.get_compensation_use():
                return jsonify(list_compen)
            raise Exception("Failed to list the used compen.")
        except:
            abort(500, "Internal error")


@route('/api/v1/compen/list_avaliable_compen')
class RestfulListCompensationAvaliable(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    def get(self):
        try:
            
            token = get_request_token()
            print(token)
            _compen = Compensation(token=token)
            if list_compen := _compen.get_avaliable_compensation():
                return list_compen
            raise Exception("Failed to list the avaliable compen.")
        except:
            abort(500, "Internal error")
<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="5">
        <el-col :span="3" >
          <el-button class="filter-item"  type="success" icon="el-icon-edit" @click="handleCreate" v-show="this.priv.role_pm.create_ntx_pm!='empty'">
            Create a PM!
          </el-button>
        </el-col>
        <el-col :span="4" :offset="this.priv.role_pm.create_ntx_pm!='empty'?10:13">
          <el-select v-model="listQuery.prism" multiple   collapse-tags placeholder="Prism" clearable class="filter-item" style="width:100%">
            <el-option v-for="item in prismoptions" :key="item.key" :label="item.display_name+'('+item.key+')'" :value="item.display_name" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-input v-model="listQuery.fuzzy" placeholder="Fuzzy search" clearable class="filter-item" @keyup.enter.native="handleFilter"  />
        </el-col>
        <el-col :span="2">
          <el-select v-model="listQuery.status" placeholder="Status" clearable  class="filter-item">
            <el-option v-for="item in statusOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-col>
        <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
          Search
        </el-button>
      
    </el-row>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="current_list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="id"  align="center" min-width="4%" >
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Prism" min-width="10%" align="center" >
        <template slot-scope="{row}">
          <span>{{ row.prism }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Cluster" min-width="12%" align="center">
        <template slot-scope="{row}">
          <span>{{ row.cluster }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column v-if="showReviewer" label="Reviewer" width="110px" align="center">
        <template slot-scope="{row}">
          <span style="color:red;">{{ row.id }}</span>
        </template>
      </el-table-column> -->

      <el-table-column label="Latest log(click to show all logs)" align="center" min-width="18%">
        <template slot-scope="{row}">
          <span>{{ row.latestlog.loginfo }}</span><span class="link-type"  @click="handleFetchBriefLog(row)">     More</span>
        </template>
      </el-table-column>
      <el-table-column label="Description" min-width="10%" align="center" >
        <template slot-scope="{row}">
          <span>{{ row.description }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Status" class-name="status-col" min-width="6%" align="center" >
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusFilter">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Action" class-name="status-col" min-width="5%" align="center" >
        <template slot-scope="{row}">
          <svg-icon :icon-class="row.pmtype" class-name="card-panel-icon" style="font-size: 25px"/>
        </template>
      </el-table-column>
      <el-table-column label="Date(UTC)" min-width="5%" align="center">
        <template slot-scope="{row}">
          <span>{{ row.startdate }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="ShowCreationDate" label="Creation Date(UTC)" min-width="10%" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createdate }}</span>
        </template>
      </el-table-column>
      <el-table-column  label="Creater" min-width="6%" align="center">
        <template slot-scope="{row}">
          <span>{{ row.creater}}</span>
        </template>
      </el-table-column>
      <el-table-column label="Actions" align="center" min-width="10%" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
            <el-button v-if="(statusToShowEditButton.includes(row.status))" type="primary" size="mini" @click="handleUpdate(row)">
            Edit
          </el-button>
            <el-button v-if="(statusToShowDeleteButton.includes(row.status))" size="mini" type="warning" @click="handleDelete(row)">
            Delete
          </el-button>
          <el-button v-if="(statusToShowAbortButton.includes(row.status))" size="mini" type="danger" @click="handleAbort(row)">
            Abort
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="90px" style="width: 600px; margin-left:50px;padding-top: 2%;">
        <el-form-item label="Prism" prop="prism" >
          <el-select v-model="temp.prism" class="filter-item" placeholder="Please select" style="width:50%">
            <el-option v-for="item in prismoptions" :key="item.key" :label="item.display_name" :value="item.display_name" />
          </el-select>
        </el-form-item>
        <el-form-item label="Cluster" prop="cluster" >
          <el-select v-model="temp.cluster" class="filter-item" placeholder="Please select" filterable style="width:50%">
            <el-option v-for="item in peTypeKeyValue[temp.prism]" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Date(UTC)" prop="timestamp" >
          <el-date-picker v-model="temp.timestamp" type="datetime" placeholder="Please pick a date"  :disabled="temp.datatimepickerdisabled" style="width:50%"/>
          <el-radio style="margin-left: 20px;" v-model="temp.startnow" label="1" @change="temp.datatimepickerdisabled=!temp.datatimepickerdisabled" >Schedule</el-radio>
          <el-radio v-model="temp.startnow" label="2" @change="temp.datatimepickerdisabled=!temp.datatimepickerdisabled" >Start Now</el-radio>
        </el-form-item>
        <el-form-item label="Action">
          <el-radio style="margin-left: 20px;" v-model="temp.pmtype" label="1"  >Power Off</el-radio>
          <el-radio v-model="temp.pmtype" label="2" >Power On</el-radio>
        </el-form-item>
        <el-form-item label="Description">
          <el-input v-model="temp.description" :autosize="{ minRows: 2, maxRows: 4}" type="textarea" placeholder="Please input" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          Cancel
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createPM():updatePM()">
          Confirm
        </el-button>
      </div>
    </el-dialog>


    <el-dialog :visible.sync="dialogPvVisible" :title="'PM Log(brief)'" >

      <el-table :data="logdata" border fit highlight-current-row style="width: 100%" max-height="500" >
        <el-table-column prop="key" label="log date"  min-width="25%" >
          <template slot-scope="{row}">
            <span>{{ row.logdate }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pv" label="log info" min-width="55%"  >
          <template slot-scope="{row}">
            <span>{{ row.loginfo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pv" label="log severity"  min-width="10%"  >
          <template slot-scope="{row}">
            <span>{{ row.severity }}</span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" @click="downloadLogFile()">Download Detail Log</el-button>
        <el-button type="primary" @click="dialogPvVisible = false">OK</el-button>
        
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {CreateNTXPM, UpdateNTXPM, GetPMTaskList,AbortNTXPM,DeleteNTXPM,DownloadNTXPMLog} from '@/api/nutanixpm'
import {GetPrismList, GetPCPECorrespondence} from '@/api/nutanix'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  name: 'ComplexTable',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      let st = status.toLowerCase();
      const statusMap = {
        'not started': 'info',
        'done': 'success',
        'error': 'danger',
        'in progress':'primary',
        'aborted':'warning'
      }
      return statusMap[st]
    }
    // typeFilter(type) {
    //   return calendarTypeKeyValue[type]
    // }
  },
  data() {
    const validateTime =(rule, value, callback)=>{
      if(this.temp.datatimepickerdisabled){
        callback()
      }
      let currentdate = new Date()
      let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
      if (value < utctime){
        callback(new Error('Schedule date must be later then now.'))
      }else{
        let currnettime = utctime.getTime()
        let scheduletime = value.getTime()
        let timediff = scheduletime-currnettime
        if(timediff/1000/60 < 5){
          callback(new Error('Schedule date is too close from now.'))
        }else{
          callback()
        }
      }
      callback()
    }
    return {
      priv:"",
      tableKey: 0,
      totalItems: 0,
      list: null,
      current_list:null,
      filtered_list:null,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        fuzzy: '',
        prism: '',
        status: ''
      },
      statusToShowEditButton:['Not Started'],
      statusToShowAbortButton:['In Progress'],
      statusToShowDeleteButton:['Not Started','Done','Error','Aborted'],
      statusOptions: ['Not Started','In Progress','Done','Error','Aborted'],
      prismoptions:[],
      peTypeKeyValue:{},
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      // statusOptions: ['published', 'draft', 'deleted'],
      ShowCreationDate: true,
      temp: {
        id: '',
        timestamp: new Date(),
        cluster:'',
        prism: '',
        status: '',
        startnow: 2 ,
        datatimepickerdisabled:true,
        description: '',
        pmtype: 1
      },
      selectedrow:'',
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: 'Edit the PM',
        create: 'Create a new PM'
      },
      dialogPvVisible: false,
      logdata: [],
      rules: {
        prism: [{ required: true, message: 'prism is required', trigger: 'change' }],
        cluster: [{ required: true, message: 'cluster is required', trigger: 'change' }],
        timestamp: [{ type: 'date', required: true , trigger: 'change' , validator:validateTime}]
      },
      downloadLoading: false,
      intervaljob:''
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.totalItems / this.pageSize);
    },
    paginatedItems() {
      const start = (this.page - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.current_list = this.items.slice(start, end);
      console.log(this.current_list)
      return this.current_list;
    },
    total() {
      if(this.filtered_list){
        return this.filtered_list.length
      }
      else{
          return 0
      }
    }
  },
  mounted() {
    this.loadData(); 
  },
  created() {
    this.get_pc_list()
    this.get_pe_list()
    // this.get_pm_task_list()
    this.init_priv()
    this.intervaljob = setInterval(()=>{
      this.loadData()// refresh the page every 30 seconds.
    },80000)
  },
  methods: {
    init_priv(){
        this.priv = this.$store.getters.all_privilege
        console.log(this.priv)
    },
    get_pc_list(){
      console.log("getting PC list.")
      GetPrismList(this.$store.getters.token).then(response => {
        for(let pc of response['data']){
          pc['fqdn'].match(/.*-(.*)-.*/)
          let k = RegExp.$1
          this.prismoptions.push({"key": k.toUpperCase(), "display_name":pc['fqdn'].toUpperCase()})
        }
        console.log(this.prismoptions)
      })
    },    
    get_pe_list(){
      console.log("getting PE list.")
      GetPCPECorrespondence(this.$store.getters.token).then(response => {
        this.peoptions = response['data']
        console.log(this.peoptions)
        this.peTypeKeyValue = this.peoptions.reduce((acc, cur) => {
          acc[cur.pc] = cur.pe.sort()
          return acc
        }, {})
      })
    },
    async loadData() {
      if (this.loading) return; 
      this.loading = true; 
      try {
        const response = await GetPMTaskList(this.$store.getters.token); 
        console.log(response.data)
        console.log("data")
        const data = response.data || [];
        // const data = await response.json();
        this.items = data; 
        this.totalItems = response.data.length; 
        this.list = response.data
        this.list.sort((a, b) => {
                const _a = a.id; // ignore upper and lowercase
                const _b = b.id; // ignore upper and lowercase
                if (_a <  _b) {
                  return 1;
                }
                if (_a >  _b) {
                  return -1;
                }
              
                // names must be equal
                return 0;
              });
        this.filtered_list = this.list
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)

        this.listLoading = false
        this.handleFilter()
      } catch (error) {
        console.error('Failed to load data', error);
      } finally {
        this.loading = false; 
      }
    },
    get_pm_task_list() {
      this.listLoading = true
      GetPMTaskList(this.$store.getters.token).then(response => {
        this.list = response.data
        this.list.sort((a, b) => {
                const _a = a.id; // ignore upper and lowercase
                const _b = b.id; // ignore upper and lowercase
                if (_a <  _b) {
                  return 1;
                }
                if (_a >  _b) {
                  return -1;
                }
              
                // names must be equal
                return 0;
              });
        this.filtered_list = this.list
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)

        this.listLoading = false
        this.handleFilter()
      })
    },
    nextPage() {
      if (this.page < this.totalPages) {
        this.page += 1;
        this.loadData(); 
      }
    },
    prevPage() {
      if (this.page > 1) {
        this.page -= 1;
        this.loadData(); 
      }
    },
    handleFilter() {
      //screen the table as per filters
      this.listQuery.page = 1
      let temp_list
      //filter selected pc first.
      if (this.listQuery.prism.length){
        //No filter, so select all
        temp_list = this.list.filter((item)=>{
          return this.listQuery.prism.includes(item['prism'])
        })
        this.filtered_list = temp_list
      }
      else{
        this.filtered_list = this.list
      }

      //filter selected status secondly
      if (this.listQuery.status.length){
        //No filter, so select all
        temp_list = this.filtered_list.filter((item)=>{
          console.log(item)
          return this.listQuery.status.includes(item['status'])
        })
        this.filtered_list = temp_list
      }

      if(this.listQuery.fuzzy.trim().length){
        let temp_list = this.filtered_list
        let fuzzy_list = this.listQuery.fuzzy.trim().split(/\s+/)

        for(let fuzzy of fuzzy_list){
          fuzzy = fuzzy.toString().toLowerCase()
          temp_list = temp_list.filter((k)=>{
          if( k.id.toString().toLowerCase().search(fuzzy)!= -1
              || k.prism.toLowerCase().search(fuzzy) != -1
              || k.cluster.toLowerCase().search(fuzzy) != -1
              || k.status.toLowerCase().search(fuzzy) != -1
              || k.creater.toLowerCase().search(fuzzy) != -1
              || k.description.toLowerCase().search(fuzzy) != -1
          ){
            return true
          }
        })
        }

        this.filtered_list = temp_list
      }
      this.set_page()
    },
    resetTemp() {
      let localtime = new Date()
      let utctime =new Date( localtime.getTime() + 60*1000*localtime.getTimezoneOffset())
      this.temp = {
        id: undefined,
        timestamp: utctime,
        status: 'published',
        type: '',
        prism: '',
        startnow: "2" ,
        pmtype: "1" ,
        datatimepickerdisabled: true
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleAbort(row){
      let payload = {
        data:row,
        token: this.$store.getters.token
      }
      AbortNTXPM(payload).then(() => {
            this.$notify({
              title: 'Success',
              message: 'PM aborted',
              type: 'info',
              duration: 2000
            })
            this.loadData()
          }
      )
      .catch((error)=>{
        this.$notify({
              title: 'Error',
              message: error.response.data.message,
              type: 'error',
              duration: 2000
          })
      })
    },
    handleDelete(row){
      let payload = {
        data:{id:row.id},
        token: this.$store.getters.token
      }
      DeleteNTXPM(payload).then(()=>{
        this.$notify({
              title: 'Success',
              message: 'PM task was deleted.',
              type: 'success',
              duration: 2000
            })
        this.loadData()
      })
      .catch((error)=>{
        this.$notify({
              title: 'Failed',
              message: error.response.data.message,
              type: 'error',
              duration: 2000
            })
        this.loadData()
      })
    },
    createPM() {
      let dt = this.temp.timestamp
      let scheduledate =new Date( dt.getTime() - 60*1000*dt.getTimezoneOffset()) // add timezone math 
      let payload = {
        data:{        
          "prism": this.temp.prism,
          "cluster": this.temp.cluster,
          "startnow": this.temp.datatimepickerdisabled,
          "scheduledate": scheduledate,
          "pmtype": this.temp.pmtype==1?"poweroff":"poweron",
          "description": this.temp.description ?this.temp.description:"webpage entrance"
        },
        token: this.$store.getters.token
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          CreateNTXPM(payload)
          .then(() => {
            this.$notify({
              title: 'Success',
              message: 'PM Created Successfully',
              type: 'success',
              duration: 5000
            })
            this.dialogFormVisible = false
            this.loadData()
          })
          .catch((error) => {
            this.dialogFormVisible  = false
            this.$notify({
              title: 'Error',
              message: error.response.data.message||"error, please contact Curry/Zoey.",
              type: 'error',
              duration: 5000
            })
          })
        }
      })
      return 0
    },
    handleUpdate(row) {
      this.temp.prism = row.prism
      this.temp.id = row.id
      this.temp.cluster = row.cluster
      this.temp.description =  row.description
      console.log(row.pmtype=='poweroff')
      this.temp.pmtype =  row.pmtype=='poweroff'?'1':'2'
      if (row.startdate ==='startnow'){
        this.temp.timestamp  = new Date()
        this.temp.datatimepickerdisabled = true
        this.temp.startnow = '2'
      }
      else{
        let dt = new Date(row.startdate)
        this.temp.timestamp = new Date(dt.getTime() + 60*1000*dt.getTimezoneOffset())
        this.temp.datatimepickerdisabled = false
        this.temp.startnow = '1'
      }

      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updatePM() {
      let dt = this.temp.timestamp
      let scheduledate =new Date( dt.getTime() - 60*1000*dt.getTimezoneOffset()) // add timezone math 
      let payload = {
        data:{        
          "id": this.temp.id,
          "prism": this.temp.prism,
          "cluster": this.temp.cluster,
          "startdate":this.temp.datatimepickerdisabled?'startnow':scheduledate,
          "pmtype": this.temp.pmtype==1?"poweroff":"poweron",
          "description": this.temp.description ?this.temp.description:"webpage entrance"
        },
        token: this.$store.getters.token
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          UpdateNTXPM(payload)
          .then(() => {
            this.$notify({
              title: 'Success',
              message: ' PM Updated Successfully',
              type: 'success',
              duration: 2000
            })
            this.dialogFormVisible = false
            this.loadData()
          })
          .catch((error) => {
            console.log(error)
            console.log(error.response)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Error',
              message: error.response.data.message,
              type: 'error',
              duration: 2000
            })
          })
        }
      })
    },
    downloadLogFile(){
      if (!this.selectedrow.detaillogpath){
        this.$notify({
              title: 'Ooooops',
              message: 'No log yet!',
              type: 'info',
              duration: 2000
            })
      }
      let payload = {
        data:{  id:this.selectedrow.id,
                filepath:this.selectedrow.detaillogpath},
        token: this.$store.getters.token
      }
      DownloadNTXPMLog(payload)
      .then((response)=>{
        const href = URL.createObjectURL(response.data);
        // create "a" HTML element with href to file & click
        const link = document.createElement('a');
        link.href = href;
        link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
        document.body.appendChild(link);
        link.click();
        // clean up "a" element & remove ObjectURL
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      })
    },
    handleFetchBriefLog(row) {
      this.selectedrow = row
      this.logdata = row.logs
      this.dialogPvVisible = true
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    set_page(){
      // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算

      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start,end)
    },
  },
  beforeDestroy(){
    console.log("destroying.....")
    clearInterval( this.intervaljob )
  }
}
</script>
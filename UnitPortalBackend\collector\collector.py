"""
    the class is specially made for collecting data from nutanix platform.

"""
from collector.collectors.pc_collector import PCCollector
from collector.collectors.pe_collector import PECollector
from collector.collectors.vm_collector import VMCollector
from collector.collectors.host_collector import HostCollector
#from collector.collectors.rs_collector import <PERSON>Collector
from collector.collectors.modules.api_calls import Meta<PERSON>ache, ServiceAccountManager
import logging
import time
from models.database import db
import logging
import time


class CollectorRunner:

    DEFAULT_COLLECTORS_RETAIL = [
        <PERSON>Collector,
        PECollector,
        HostCollector,
        VMCollector,
        #CBDCollector,
    ]
    DEFAULT_COLLECTORS_WH = DEFAULT_COLLECTORS_RETAIL


    def __init__(self, collectors_retail = None, collectors_wh = None) -> None:
        """Arguments exist for restful API calls to select collectors, with option for warehouse or retail."""
        """If API is not sending for one of the facility types, the default collectors will not be used since a list with ['empty'] will be added from restful function and sent in its name."""
        self.collectors_retail = collectors_retail if collectors_retail else self.DEFAULT_COLLECTORS_RETAIL
        self.collectors_wh = collectors_wh if collectors_wh else self.DEFAULT_COLLECTORS_WH


    def collect(self):
        """Perform collection process for retail and warehouse."""
        logging.info("Starting collection process...")
        start_time = time.time()
        cache_handler = MetaCache()
        svc_manager = ServiceAccountManager()

        try:
            cache_handler.clear_cache_data()  # Clear stored cache data before collecting new data
        except Exception:
            pass

        retail = self.collectors_retail
        warehouse = self.collectors_wh

        failed_collectors = []
        successful_collectors_retail = []
        successful_collectors_wh = []
        for collector in CollectorRunner.DEFAULT_COLLECTORS_RETAIL:  # Always runs in hierarchical order

            if collector in retail:
                logging.info(f"Starting {collector.__name__} for retail...")
                try:
                    collector().collect()
                    successful_collectors_retail.append(collector.__name__)
                except Exception as e:
                    logging.error(f"Failed to collect data from {collector.__name__} for retail. Because of: {e}.")
                    failed_collectors.append(collector.__name__+" for retail")
                db.session.close()  # Close the session after each collector to avoid memory leaks

            if collector in warehouse:
                try:
                    logging.info(f"Starting {collector.__name__} for warehouse...")
                    collector(facility_type = "warehouse").collect(warehouse=True)
                    successful_collectors_wh.append(collector.__name__)
                except Exception as e:
                    logging.error(f"Failed to collect data from {collector.__name__} for warehouse. Because of: {e}.")
                    failed_collectors.append(collector.__name__+" for warehouse")
                db.session.close()  # Close the session after each collector to avoid memory leaks

        stats = svc_manager.get_stats()
        failed_vault = svc_manager.get_failed_vault()
        svc_manager.reset()
        cache_handler.clear_cache_data()  # Clear stored cache data after collecting new data

        if HostCollector in retail or HostCollector in warehouse:
            HostCollector.reset_oneview_data_cache()  # Reset oneview data for hosts

        end_time = time.time()


        logging.info(f"Finished collecting! We collected: {successful_collectors_retail} from retail and: {successful_collectors_wh} from warehouse. Total time: {end_time - start_time:.2f} seconds")
        if failed_collectors:
            logging.error(f"Failed to collect data for: {failed_collectors}.")
        logging.info(f"Collector vault account usage: {stats}")
        if failed_vault:
            logging.error(f"Failed to collect data from vault for: {failed_vault}.")

from sqlalchemy import Column, String, DateTime, Text

from business.distributedhosting.nutanix.automation.seamless_lcm_specs import SeamlessLcmSpec
from models.database import db, ma


class ModelSeamlessLcmJobStore(db.Model):
    __tablename__           = SeamlessLcmSpec.JOB_STORE
    id                      = Column(String, primary_key=True)
    next_run_time           = Column(DateTime, nullable=True)
    job_state               = Column(Text, nullable=False)


class ModelSeamlessLcmJobStoreSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelSeamlessLcmJobStore
        load_instance = True
        include_fk = True
        ordered = True

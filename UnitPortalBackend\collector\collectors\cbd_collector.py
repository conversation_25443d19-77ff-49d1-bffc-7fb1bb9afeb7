import re
from models.database import db
from collector.collectors.base_collector import BaseCollector
from business.generic.cbd import Cbd
from business.generic.commonfunc import split_pe_into_parts
from models.cbd_models import ModelDhCbd
from models.ntx_models import ModelPrismElement
from models.ntx_models_wh import ModelWarehousePrismElement


class CbdCollector(BaseCollector):
    def __init__(self, sa=None, logger=None) -> None:
        super().__init__(sa)
        # for retail and warehouse sites, they own different bu_types
        self.bu_types = {
            'retail': ['STO', 'SO', 'CSC', 'LSC', 'OFB', 'ITO', 'SHC', 'BSC'],
            'warehouse': ['CDC', 'DT']
        }
        self.logger = logger
        self.cbd = Cbd(tier='production', logger=self.logger)
    
    def collect(self, warehouse=False):
        if warehouse:
            # if the caller is from warehouse collector, then we only collect warehouse bu data
            self.logger.info("Collecting CBD data for warehouse sites")
            self.collect_warehouse_cbd()
        else:
            self.logger.info("Collecting CBD data for retail sites")
            self.collect_retail_cbd()
    
    def update_bu(self, bu_s):
        # send collected bu data to db table 'cbd'
        for bu in bu_s:
            payload = {
                'id': f"{bu.get('buKey').get('type')}{bu.get('buKey').get('code')}",
                'bu_type': bu.get('buKey').get('type'),
                'bu_code': bu.get('buKey').get('code'),
                'name': bu.get('name'),
                'street': bu.get('address')[0].get('street'),
                'postal_code': bu.get('address')[0].get('postalCode'),
                'city': bu.get('address')[0].get('city'),
                'country_code': bu.get('address')[0].get('countryCode'),
                'time_zone': bu.get('address')[0].get('timezone'),
                'latitude': bu.get('address')[0].get('latitude'),
                'longitude': bu.get('address')[0].get('longitude')
            }
            self.logger.info(f"Working on the BU with id:{payload['id']}, bu_type:{payload['bu_type']}, bu_code:{payload['bu_code']}, la:{payload['latitude']}, lo:{payload['longitude']}")
            data = ModelDhCbd(**payload)
            # using merge method to insert or update rows by primary key is 'id'
            db.session.merge(data)
        db.session.commit()
    
    def collect_retail_cbd(self):
        retail_pe_s = ModelPrismElement.query.filter(ModelPrismElement.status != "Decommissioned").all()
        self.logger.info(f"{len(retail_pe_s)} retail sites are loaded")
        retail_cbd_search_payload = dict()
        bu_s = []
        for retail_pe in retail_pe_s:
            self.logger.info(f"Working on {retail_pe.name}")
            if retail_pe.bu_type and retail_pe.bu_code:
                # if a PE has matched bu_type and bu_code and has manually fixed type and code (e.g. RETUS100: STO100 -> SO014), we do not re-calculate its type and code, just re-fetch and keep cbd data of it up to date
                bu_s += self.cbd.get_business_unit(bu_type=retail_pe.bu_type, bu_code=retail_pe.bu_code, country=retail_pe.country_code)
                retail_pe.cbd_id = f"{retail_pe.bu_type}{retail_pe.bu_code}"
                db.session.commit()
                continue
            # if a PE has neither bu_type nor bu_code, for example, a new site, we calculate its bu_type and bu_code, and it could be incorrect and needs manually fixed in possible
            bu, _, bu_code = split_pe_into_parts(pe_name = retail_pe.name)
            # roughly match the bu_type and bu_code
            if bu in ("RET", "R", "IIS"):
                if bu_code in ("SO", "SOS"):
                    # e.g. RETCHSO
                    bu_type = "SO"
                elif re.match(r"^C\d{2}$", bu_code) :
                    # e.g. RETCNC02
                    bu_type = "CSC"
                else:
                    bu_type = "STO"
            elif bu == "IT":
                bu_type = "ITO"
            elif bu == "BSC":
                bu_type = "BSC"
            elif bu == "IIC":
                bu_type = "SHC"
            elif bu == "FIN":
                bu_type = "OFB"
            elif bu == "TSO":
                bu_type = "LSC"
            else:
                bu_type = None
            if not re.match(r"\d{3,5}$", bu_code):
                bu_code = None
            if bu_type and bu_code:
                retail_cbd_search_payload.setdefault(bu_type, [])
                if bu_code not in retail_cbd_search_payload[bu_type]:
                    retail_cbd_search_payload[bu_type].append(bu_code)
            self.logger.info(f"PE:{retail_pe.name}, bu_type:{bu_type}, bu_code:{bu_code}")
            retail_pe.bu_type, retail_pe.bu_code, retail_pe.cbd_id = bu_type, bu_code, f"{bu_type}{bu_code}" if bu_type and bu_code else None
            db.session.commit()
        for bu_type in retail_cbd_search_payload.keys():
            for bu_code in retail_cbd_search_payload.get(bu_type):
                self.logger.info(f"Fetching CBD data of bu_type:{bu_type}, bu_code:{bu_code}")
                # fetch cbd data through API by bu_type and bu_code
                bu_s += self.cbd.get_business_unit(bu_type=bu_type, bu_code=bu_code)
        self.logger.info(f"{len(bu_s)} BUs are collected")
        self.update_bu(bu_s=bu_s)
    
    def collect_warehouse_cbd(self):
        # pe name of warehouse in bu DT or in bu CDC are started with 'DS', we need to confirm if it is in DT or in CDC by check from cbd with potential bu_type and country_code and bu_code
        warehouse_pe_s = ModelWarehousePrismElement.query.filter(ModelWarehousePrismElement.status != "Decommissioned").all()
        bu_s = []
        for warehouse_pe in warehouse_pe_s:
            if warehouse_pe.bu_type and warehouse_pe.bu_code:
                bu_s += self.cbd.get_business_unit(bu_type=warehouse_pe.bu_type, bu_code=warehouse_pe.bu_code, country=warehouse_pe.country_code)
                warehouse_pe.cbd_id = f"{warehouse_pe.bu_type}{warehouse_pe.bu_code}"
                db.session.commit()
                continue
            _, country, bu_code = split_pe_into_parts(pe_name = warehouse_pe.name)
            if not re.match(r"\d{3}$", bu_code):
                bu_type = None
                bu_code = None
            if bu_code:
                # select and test bu_type from ['DT', 'CDC'] and get bu data from cbd
                for bu_type in self.bu_types['warehouse']:
                    tmp_bu_s = self.cbd.get_business_unit(bu_type=bu_type, bu_code=bu_code, country=country)
                    if len(tmp_bu_s) == 1:
                        # if it returns an exact bu, then we've got the correct by_type
                        bu_s += tmp_bu_s
                        break
                    else:
                        # otherwise, we reset the bu_type to None
                        bu_type = None
            warehouse_pe.bu_type, warehouse_pe.bu_code, warehouse_pe.cbd_id = bu_type, bu_code, f"{bu_type}{bu_code}" if bu_type and bu_code else None
            db.session.commit()
        self.logger.info(f"{len(bu_s)} BUs are collected")
        self.update_bu(bu_s=bu_s)
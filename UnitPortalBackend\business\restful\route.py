from flask_apispec import FlaskApiSpec

from business.distributedhosting.upwidgets import APILogTracer
from business.restful.blueprint import api

swagger_docs = FlaskApiSpec()


def route(urls, **kwargs):
    """
    Decorator to define routes for Resource classes and handle APILogTracer.
    The endpoint will be included in Swagger UI if any of its http methods
       have @doc decorators, define swagger=False to exclude the endpoint from Swagger UI with higher priority.

    Args:
        urls (str or list): The URL path(s) for the route
        trace (bool): Whether to include the endpoint in the trace, default is True
        swagger (bool): Whether to include the endpoint in the Swagger UI, default is False
    """
    # Convert single URL to list for consistent handling
    if isinstance(urls, str):
        urls = [urls]

    def decorator(cls):
        # Store the original class
        original_cls = cls

        # Create a unique class name based on the original class name and first URL by replacing all none-alphanumeric characters with underscores
        unique_class_name = f"{original_cls.__name__}_{''.join(c if c.isalnum() else '_' for c in urls[0])}".lower()

        # Create a new class that inherits from the original with a unique name
        WrappedResource = type(unique_class_name, (original_cls,), {})

        for method_name in ["get", "post", "put", "patch", "delete", "head", "options", "trace"]:
            if not hasattr(original_cls, method_name):
                continue

            original_method = getattr(original_cls, method_name)

            # Apply APILogTracer if not already applied
            if kwargs.get("trace", True):
                setattr(WrappedResource, method_name, APILogTracer()(original_method))

            # check if we should add the endpoint to the Swagger UI
            if "swagger" in kwargs and not kwargs["swagger"]:
                ...
            elif kwargs.get("swagger", False) or (
                "__wrapped__" in original_method.__dict__
                and "__apispec__" in original_method.__wrapped__.__dict__
                and "docs" in original_method.__wrapped__.__apispec__
                and len(original_method.__wrapped__.__apispec__["docs"]) > 0
            ):
                # If the resource has doc decorators, add it to the list to be registered with Swagger UI
                swagger_docs.register(WrappedResource, blueprint="distributedhosting")

        # Register the routes with Flask-RESTful
        api.add_resource(WrappedResource, *urls)

        return WrappedResource

    return decorator

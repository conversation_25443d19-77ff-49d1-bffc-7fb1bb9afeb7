function Enable-WarehouseVcIloRemoteSyslog(){
    param(
        [string]
        $Cluster,
        [ValidateSet(
            "True",
            "False"
        )]
        $RmSyslogEnabled,
        [Int32]
        $RmSyslogPort = 514,
        [string]
        $RmSyslogServer
    )
    switch($RmSyslogEnabled) {
        "True" {
            if(!($RmSyslogPort) -or !($RmSyslogServer)){
                Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The value of RmSyslogPort and RmSyslogServer must be assigned"
                return $null
            }
            $RmSyslogEnabled = $true
            #Construct payload of RmSysEnabled for iLO
            $Payload = [PSCustomObject]@{
                '4' = [PSCustomObject]@{
                    'Body' = [PSCustomObject]@{
                        'Oem' = [PSCustomObject]@{
                            'Hp' = [PSCustomObject]@{
                                'RemoteSyslogEnabled'   = $RmSyslogEnabled
                                'RemoteSyslogPort'      = $RmSyslogPort
                                'RemoteSyslogServer'    = $RmSyslogServer
                            }
                        }
                    } 
                }
                '5' = [PSCustomObject]@{
                    'Body' = [PSCustomObject]@{
                        'Oem' = [PSCustomObject]@{
                            'Hpe' = [PSCustomObject]@{
                                'RemoteSyslogEnabled'   = $RmSyslogEnabled
                                'RemoteSyslogPort'      = $RmSyslogPort
                                'RemoteSyslogServer'    = $RmSyslogServer
                            }
                        }
                    }
                }
            }
        }
        "False" {
            $RmSyslogEnabled = $false
            #Construct payload of NOT RmSysEnabled for iLO
            $Payload = [PSCustomObject]@{
                '4' = [PSCustomObject]@{
                    'Body' = [PSCustomObject]@{
                        'Oem' = [PSCustomObject]@{
                            'Hp' = [PSCustomObject]@{
                                'RemoteSyslogEnabled'   = $RmSyslogEnabled
                                'RemoteSyslogServer'    = ''
                            }
                        }
                    }
                }
                '5' = [PSCustomObject]@{
                    'Body' = [PSCustomObject]@{
                        'Oem' = [PSCustomObject]@{
                            'Hpe' = [PSCustomObject]@{
                                'RemoteSyslogEnabled'   = $RmSyslogEnabled
                                'RemoteSyslogServer'    = ''
                            }
                        }
                    }
                }
            }
        }
    }
    $Successful     = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Skipped        = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Failed         = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Results        = @{}
    $Vars           = Read-Var
    $VCAddress      = $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Name + `
                      "." + `
                      $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Domain
    $VCUsername     = $Vars.GstAccount.username
    $VCPassword     = $Vars.GstAccount.password
    $VCHosts        = (Rest-VC-List-Host -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    $iLOUsername    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Username
    $iLOPassword    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Password
    #Convert VC host to iLO, and filtering out the set of the configurable
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We have $($VCHosts.Length) hosts"
    if ($Cluster) {
        $VCHosts = $VCHosts | Where-Object {$_.name -match $Cluster}
    }
    $iLOs = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $VCHosts | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $Splits      = $_.name.split(".")
        $iLOAddress  = $Splits[0] + `
                       "ra." + `
                       $Splits[1] + `
                       "." + `
                       $Splits[2]
        $Dict        = $using:iLOs
        $DictSkipped = $using:Skipped
        $iLO         = [PSCustomObject]@{
            'Address' = $iLOAddress
            'Session' = $(Rest-iLO-Get-Session -iLOAddress $iLOAddress `
                                          -iLOUsername $using:iLOUsername `
                                          -iLOPassword $using:iLOPassword)
        }
        if($iLO.Session) {
            $Managers = Rest-iLO-Get-Managers -iLOAddress $iLOAddress `
                                              -Session $iLO.Session
            if($Managers.FirmwareVersion -match 'iLO 5') {
                $Gen = "5"
            }else {
                $Gen = "4"
            }
            $iLO | Add-Member -MemberType NoteProperty -Name 'Gen' -Value $Gen
            $Dict.Add($iLO)
        }else {
            $DictSkipped.Add($iLOAddress)
        }
        
    } -ThrottleLimit 250
    #Send payload to iLO through API
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We have $($iLOs.Count) iLOs are configurable"
    $iLOs | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $iLOAddress     = $_.Address
        $Session        = $_.Session
        $Body           = ($using:Payload).($_.Gen).Body
        $DictSuccessful = $using:Successful
        $DictFailed     = $using:Failed
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're sending payload to $iLOAddress"
        switch ($_.Gen) {
            "4" {
                $Response = Invoke-iLO-API -iLOAddress $iLOAddress `
                                           -RequestURI "Managers/1/NetworkService" `
                                           -Method PATCH `
                                           -Headers @{"X-Auth-Token" = $Session} `
                                           -Body $($Body | ConvertTo-Json) `
                                           -Type RestMethod
            }
            "5" {
                $Response = Invoke-iLO-API -iLOAddress $iLOAddress `
                                           -RequestURI "Managers/1/NetworkProtocol" `
                                           -Method PATCH `
                                           -Headers @{"X-Auth-Token" = $Session} `
                                           -Body $($Body | ConvertTo-Json) `
                                           -Type RestMethod
            }
        }
        if($Response) {
            $DictSuccessful.Add($iLOAddress)
        }else {
            $DictFailed.Add($iLOAddress)
        }
    }
    $Results.Successful = $Successful
    $Results.Failed     = $Failed
    $Results.Skipped    = $Skipped
    return $Results
}

function Get-WarehouseVcIloRemoteSyslog(){
    param(
        [string]
        [Parameter(ParameterSetName = 'Cluster')]
        $Cluster
    )
    $Successful     = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Skipped        = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Failed         = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Results        = @{}
    $Vars           = Read-Var
    $VCAddress      = $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Name + `
                      "." + `
                      $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Domain
    $VCUsername     = $Vars.GstAccount.username
    $VCPassword     = $Vars.GstAccount.password
    $VCHosts        = (Rest-VC-List-Host -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    $iLOUsername    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Username
    $iLOPassword    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Password
    #Convert VC host to iLO, and filtering out the set of the configurable
    if ($Cluster) {
        $VCHosts = $VCHosts | Where-Object {$_.name -match $Cluster}
    }
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We have $($VCHosts.Length) hosts"
    $iLOs = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $VCHosts | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $Splits      = $_.name.split(".")
        $iLOAddress  = $Splits[0] + `
                       "ra." + `
                       $Splits[1] + `
                       "." + `
                       $Splits[2]
        $DictiLOs    = $using:iLOs
        $DictSkipped = $using:Skipped

        $iLO = [PSCustomObject]@{
            'Address' = $iLOAddress
            'Session' = $(Rest-iLO-Get-Session -iLOAddress $iLOAddress `
                                          -iLOUsername $using:iLOUsername `
                                          -iLOPassword $using:iLOPassword)
        }
        if($iLO.Session) {
            $Managers = Rest-iLO-Get-Managers -iLOAddress $iLOAddress `
                                              -Session $iLO.Session
            if($Managers.FirmwareVersion -match 'iLO 5') {
                $Gen = "5"
            }else {
                $Gen = "4"
            }
            $iLO | Add-Member -MemberType NoteProperty -Name 'Gen' -Value $Gen
            $DictiLOs.Add($iLO)
        }else {
            $DictSkipped.Add($iLOAddress)
        }
    } -ThrottleLimit 250
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are proceeding to $($iLOs.Count) iLOs"
    $iLOs | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $DictSuccessful = $using:Successful
        $DictFailed     = $using:Failed
        $Response       = & $("Rest-iLO-" + $_.Gen + "-Get-NetworkService") -iLOAddress $_.Address `
                                                                            -Session $_.Session
        if ($Response) {
            $Result = [PSCustomObject]@{
                'Address' = $_.Address
            }
            switch ($_.Gen) {
                "4" {
                    $Result | Add-Member -NotePropertyName 'RemoteSystemEnabled' -NotePropertyValue $Response.Oem.Hp.RemoteSyslogEnabled
                    $Result | Add-Member -NotePropertyName 'RemoteSyslogPort' -NotePropertyValue $Response.Oem.Hp.RemoteSyslogPort
                    $Result | Add-Member -NotePropertyName 'RemoteSyslogServer' -NotePropertyValue $Response.Oem.Hp.RemoteSyslogServer
                }
                "5" {
                    $Result | Add-Member -NotePropertyName 'RemoteSystemEnabled' -NotePropertyValue $Response.Oem.Hpe.RemoteSyslogEnabled
                    $Result | Add-Member -NotePropertyName 'RemoteSyslogPort' -NotePropertyValue $Response.Oem.Hpe.RemoteSyslogPort
                    $Result | Add-Member -NotePropertyName 'RemoteSyslogServer' -NotePropertyValue $Response.Oem.Hpe.RemoteSyslogServer
                }
            }
            $DictSuccessful.Add($Result)
        }else {
            $DictFailed.Add($_.Address)
        }
    } -ThrottleLimit 250
    $Results.Successful = $Successful
    $Results.Failed     = $Failed
    $Results.Skipped    = $Skipped
    return $Results
    #return $iLOs
}
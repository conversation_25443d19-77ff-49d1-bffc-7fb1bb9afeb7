/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const adminRouter = {
  path: '/administration',
  component: Layout,
  redirect: '/administration/user',
  // name: 'Table',
  meta: {
    title: 'Administration',
    icon: 'administration',
    roles: ['superadmin','pmuser'],
    privilege:"role_administration"
  },
  children: [
    {
      path: 'user',
      component: () => import('@/views/administration/user'),
      name: 'Users',
      meta: { title: 'User' , roles: ['admin','pmuser','superadmin'], privilege:"view_user"}
    },
    {
      path: 'role',
      component: () => import('@/views/administration/role'),
      name: 'Role',
      meta: { title: 'Role' , roles: ['admin','pmuser','superadmin'], privilege:"view_role"}
    },
    {
      path: 'compen',
      component: () => import('@/views/administration/compensation'),
      name: 'Compen',
      meta: { title: 'Compen' , roles: ['admin','pmuser','superadmin'],privilege:"view_role"}
    },
    {
      path: 'comment',
      component: () => import('@/views/administration/comment'),
      name: 'Comment',
      meta: { title: 'Comment' , roles: ['admin','pmuser','superadmin'],privilege:"view_comment"}
    },
    {
      path: 'serviceAccount',
      component: () => import('@/views/administration/service-account'),
      name: 'Service Account',
      meta: { title: 'Service Account' , roles: ['admin','pmuser','superadmin'], privilege:"view_role"}
    }
  ]
}
export default adminRouter

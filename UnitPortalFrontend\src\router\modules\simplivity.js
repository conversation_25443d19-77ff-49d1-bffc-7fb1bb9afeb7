/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const sliRouter = {
  path: '/simplivity',
  component: Layout,
  redirect: '/simplivity/vcenter',
  // name: 'Table',
  meta: {
    title: 'SimpliVity',
    icon: 'hp',
    roles: ['admin','pmuser','superadmin'],
    privilege:'role_sli'
  },
  children: [
    {
      path: 'vc',
      component: () => import('@/views/simplivity/vcenter'),
      name: 'vCenter',
      meta: { title: 'vCenter' , roles: ['admin','pmuser','superadmin'],privilege:'view_vc'}
    },
    {
      path: 'cluster',
      component: () => import('@/views/simplivity/cluster'),
      name: 'Cluster',
      meta: { title: 'Cluster' , roles: ['admin','pmuser','superadmin'],privilege:'view_cluster'}
    },
    {
      path: 'slihosts',
      component: () => import('@/views/simplivity/slihosts'),
      name: 'Host',
      meta: { title: 'Host' , roles: ['admin','pmuser','superadmin'],privilege:'view_vm'}
    },
    {
      path: 'slivm',
      component: () => import('@/views/simplivity/slivm'),
      name: 'Virtual Machine',
      meta: { title: 'Virtual Machine' , roles: ['admin','pmuser','superadmin'],privilege:'view_vm'}
    }
  ]
}
export default sliRouter

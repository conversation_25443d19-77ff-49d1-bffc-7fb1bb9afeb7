"""
╔═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
║Module: incident handler models                                                                                                        ║
║                                                                                                                                       ║
║Author:         Incident Devourer                                                                                                      ║
║Created:        2024-08-01                                                                                                             ║
║Last Updated:   2024-08-27                                                                                                             ║
║                                                                                                                                       ║
║Description:                                                                                                                           ║
║It stores and manages incidents models for incident handler scripts.                                                                    ║           ║
║                                                                                                                                       ║
║Usage:                                                                                                                                 ║
║Incidents models>>                                                                                                                     ║
║   -SiaB-Nutanix-CVM reboot model                                                                                                      ║
║   -SiaB-Nutanix-Remote site connectivity not normal                                                                                   ║
║   -SiaB-Nutanix-Host connection                                                                                                       ║
║   -SiaB-Nutanix-cluster service restart frequently
║   -SiaB-Nutanix-VMs not fund
║   -SiaB-Nutanix-Power supply failed
║   -WTP-Host connection                                                                                                                ║
║                                                                                                                                       ║
║Update Logs:                                                                                                                           ║
║-[2024/8/16]-[IVFAN]-[Added two incidents models-Nutanix cluster service restart&Nutanix host connection]                              ║
║-[2024/8/27]-[IVFAN]-[Opertimized the process of Nutanix host unreachable] 
║            -[EJ]   -[Added 'VM not fund' and 'power supply' models]
║                                                                                                                                       ║
╚═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
"""
import os
import re
import time
import logging
import uuid
import ast
from base_path import application_path
from .incident_handler_config import IncHandlerCfg
from .incident_handler_tools import IncidentHandlerTools
from ..generic.commonfunc import setup_common_logger


class IncidentAnalyseModel:
    def __init__(self) -> None:
        self.incident_handler_tools = IncidentHandlerTools()
        incident_handler = os.path.join(application_path, "Log", "incidentHandler")
        self.logger = setup_common_logger(str(uuid.uuid4()), incident_handler + "\\IncidentHandlerLog")

    @staticmethod
    def exception_handler(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                inc_num = args[1]
                inc_short_desc = args[2]
                logging.error(f"Error: Error processing incident {inc_num} - {e}")
                # self.logger.error(f"Error: Error processing incident {inc_num} - {e}")
                args[0].incident_handler_tools.commit_inc_handler_error_log(inc_num, inc_short_desc,
                                                                            f"Error: {e}")  #agrs[0]为self参数

        return wrapper

    @exception_handler
    def cvm_reboot_model(self, inc_num, orig_short_desc, pe, inc_creation_time, svc_type):
        cvm_reboot_uuid = IncHandlerCfg.nx_cvm_reboot_alert_uuid
        latest_alert = self.incident_handler_tools.nutanix_case_handle_common_process(inc_num, pe, orig_short_desc,
                                                                                      cvm_reboot_uuid,
                                                                                      svc_type
                                                                                      )
        if latest_alert is None:
            self.incident_handler_tools.commit_inc_handler_error_log(inc_num, orig_short_desc,
                                                                     f"WARN:{latest_alert} is none!")
            self.logger.error(f"ERROR:{latest_alert} is none!")
            raise ValueError("latest_alert is none")
        if latest_alert.get('resolved', False):
            self.incident_handler_tools.handle_resolved_alert(inc_num, latest_alert, orig_short_desc, svc_type)
        else:
            self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
            end_time, start_time_minus_1_day, start_time_minus_1_week = self.incident_handler_tools.calculate_new_time_duration(
                inc_creation_time)
            alerts_number_description = self.incident_handler_tools.get_nutanix_history_alerts_number(pe,
                                                                                                      start_time_minus_1_day,
                                                                                                      start_time_minus_1_week,
                                                                                                      end_time,
                                                                                                      cvm_reboot_uuid)
            self.incident_handler_tools.handle_unresolved_alert(inc_num, alerts_number_description, orig_short_desc)

    @exception_handler
    def wtp_host_connection_model(self, inc_num, orig_short_desc, host_ip, host_name, svc_type):
        if self.incident_handler_tools.ping_ip(host_ip):
            wtp_host_connection_worknotes = self.incident_handler_tools.wtp_host_connection_worknote(host_ip, host_name)
            self.incident_handler_tools.incident_terminator(inc_num, wtp_host_connection_worknotes, orig_short_desc, svc_type)
            self.incident_handler_tools.commit_incident_handler_data(inc_num, orig_short_desc,
                                                                     wtp_host_connection_worknotes,
                                                                     task_type='INC_RESOLVED')
            self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_RESOLVED,
                                                                    f"{inc_num} is resolved successfully!")
            self.logger.info(f"{inc_num} is resolved successfully!")
        else:
            self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
            new_short_description = IncHandlerCfg.WTP_INC_SHORT_DESC_PREFIX + orig_short_desc
            self.incident_handler_tools.unassign_gdh_incident(number=inc_num, new_dscription=new_short_description)
            self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.NX_ALERT_CHK,
                                                                    f"{inc_num}-{host_name}-{host_ip} is still not reachable!")
            self.logger.info(f"{inc_num}-{host_name}-{host_ip} is still not reachable!")
            self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_UNASSIGN,
                                                                    f"{inc_num} is unassigned with {new_short_description} description")
            self.logger.info(f"{inc_num} is unassigned with {new_short_description} description")

    @exception_handler
    def remote_site_connectivity_not_normal_model(self, inc_num, orig_short_desc, pe, inc_creation_time, svc_type):
        remote_site_connectivity_uuid = IncHandlerCfg.nx_connectivity_to_remote_uuid
        latest_alert = self.incident_handler_tools.nutanix_case_handle_common_process(inc_num, pe, orig_short_desc,
                                                                                      remote_site_connectivity_uuid,
                                                                                      svc_type
                                                                                      )
        if latest_alert is None:
            self.logger.error(f"ERROR:{latest_alert} is none!")
            raise ValueError("latest_alert is None")
        if latest_alert.get('resolved', False):
            self.incident_handler_tools.handle_resolved_alert(inc_num, latest_alert, orig_short_desc, svc_type)
        else:
            self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
            end_time, start_time_minus_1_day, start_time_minus_1_week = self.incident_handler_tools.calculate_new_time_duration(
                inc_creation_time)
            alerts_number_description = self.incident_handler_tools.get_nutanix_history_alerts_number(pe,
                                                                                                      start_time_minus_1_day,
                                                                                                      start_time_minus_1_week,
                                                                                                      end_time,
                                                                                                      remote_site_connectivity_uuid)
            self.incident_handler_tools.handle_unresolved_alert(inc_num, alerts_number_description, orig_short_desc)

    @exception_handler
    def nutanix_host_connection_model(self, inc_num, orig_short_desc, pe, host_ip, inc_creation_time, svc_type):
        time.sleep(300)
        self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK,
                                                                f"{inc_num}-{pe}-{host_ip} wait for 5 minutes to check the connection!")
        self.logger.info(f"{inc_num}-{pe}-{host_ip} wait for 5 minutes to check the connection!")
        nutanix_host_connection_uuid = IncHandlerCfg.nutanix_host_connection_failed_uuid
        latest_alert = self.incident_handler_tools.nutanix_case_handle_common_process(inc_num, pe, orig_short_desc,
                                                                                      nutanix_host_connection_uuid,
                                                                                      svc_type
                                                                                      )
        if latest_alert is None:
            self.logger.error(f"ERROR:{latest_alert} is none!")
            raise ValueError("latest_alert is None")
        if latest_alert.get('resolved', False):
            self.incident_handler_tools.handle_resolved_alert(inc_num, latest_alert, orig_short_desc, svc_type)
        else:
            if self.incident_handler_tools.ping_ip(host_ip):
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK,
                                                                        f"{inc_num}-{pe}-{host_ip} is reachable, but NX alert is still exist, closing NX alerts!")
                self.logger.info(f"{inc_num}-{pe}-{host_ip} is reachable, but NX alert is still exist, closing NX alerts!")
                get_unresolve_alert = self.incident_handler_tools.get_unresolved_nutanix_cluster_alerts(pe,
                                                                                                        nutanix_host_connection_uuid)
                for entity in get_unresolve_alert['entities']:
                    self.incident_handler_tools.post_resolve_alerts(pe, entity['id'])
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK,
                                                                        f"{inc_num}-{pe}-{host_ip}-NX alerts are resolved,closing the case!")
                self.logger.info(f"{inc_num}-{pe}-{host_ip}-NX alerts are resolved,closing the case!")
                self.incident_handler_tools.handle_resolved_alert(inc_num, latest_alert, orig_short_desc, svc_type)
            else:
                self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
                self.incident_handler_tools.commit_incident_handler_log(
                    IncHandlerCfg.INC_UNASSIGN,
                    f"{inc_num}-{pe}-{host_ip} is unreachable, adjust the case priority to critical!")
                self.logger.info(f"{inc_num}-{pe}-{host_ip} is unreachable, adjust the case priority to critical!")
                end_time, start_time_minus_1_day, start_time_minus_1_week = self.incident_handler_tools.calculate_new_time_duration(
                    inc_creation_time)
                alerts_number_description = self.incident_handler_tools.get_nutanix_history_alerts_number(pe,
                                                                                                          start_time_minus_1_day,
                                                                                                          start_time_minus_1_week,
                                                                                                          end_time,
                                                                                                          nutanix_host_connection_uuid)
                new_short_despt = IncHandlerCfg.NX_INC_SHORT_DESC_PREFIX + alerts_number_description + orig_short_desc
                self.incident_handler_tools.unassign_gdh_host_critical_incident(inc_num, host_ip, new_short_despt)

    @exception_handler
    def nutanix_cluster_service_restart_model(self, inc_num, orig_short_desc, pe, inc_creation_time, svc_type):
        nutanix_cluster_service_restart_uuid = IncHandlerCfg.nutanix_cluster_service_restart_uuid
        latest_alert = self.incident_handler_tools.nutanix_case_handle_common_process(inc_num, pe, orig_short_desc,
                                                                                      nutanix_cluster_service_restart_uuid,
                                                                                      svc_type
                                                                                      )
        if latest_alert is None:
            self.logger.error(f"ERROR:{latest_alert} is none!")
            raise ValueError("latest_alert is None")
        if latest_alert.get('resolved', False):
            self.incident_handler_tools.handle_resolved_alert(inc_num, latest_alert, orig_short_desc, svc_type)
        else:
            self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
            end_time, start_time_minus_1_day, start_time_minus_1_week = self.incident_handler_tools.calculate_new_time_duration(
                inc_creation_time)
            alerts_number_description = self.incident_handler_tools.get_nutanix_history_alerts_number(pe,
                                                                                                      start_time_minus_1_day,
                                                                                                      start_time_minus_1_week,
                                                                                                      end_time,
                                                                                                      IncHandlerCfg.nutanix_cluster_service_restart_uuid)
            self.incident_handler_tools.handle_unresolved_alert(inc_num, alerts_number_description, orig_short_desc)

    @exception_handler
    def ntx_power_supply_model(self, inc_num, orig_short_desc, pe, sn, svc_type):
        find_alert = self.incident_handler_tools.get_unresolved_nutanix_cluster_alerts(pe,
                                                                                       IncHandlerCfg.NX_POWER_SUPPLY_UUID)
        if 'error' in find_alert:
            prefix = IncHandlerCfg.NX_API_PREFIX
            self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix, worknotes=None)
        else:
            latest_alert = find_alert.get('entities')
            if not latest_alert:  # alert == [], close alert and inc
                worknotes = '''GDH update>>
                                    The power down alert already resolved,hence closing the inc!'''
                self.incident_handler_tools.incident_terminator(inc_num, worknotes, orig_short_desc, svc_type)
                self.incident_handler_tools.commit_incident_handler_data(inc_num, orig_short_desc, worknotes,
                                                                         task_type='incident_resolved')
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_RESOLVED,
                                                                        f"{inc_num}ntx power down alert is resolved successfully!")
                self.logger.info(f"{inc_num}ntx power down alert is resolved successfully!")
            else:
                ilo_ip = self.incident_handler_tools.get_nx_host_info(pe, sn)
                # if iLO ip not Null, using ilo redfish API get the power status
                if ilo_ip:
                    status_data = self.incident_handler_tools.get_ntx_ilo_power_states(pe, ilo_ip)
                    if status_data:
                        try:
                            all_power_status = status_data.get('PowerSupplies', [])
                            power_health = [psu['Status']['Health'] for psu in all_power_status]
                            if power_health[0] == 'OK' and power_health[1] == 'OK':  # power supply is OK,close INC
                                self.incident_handler_tools.handle_resolved_alert(inc_num, latest_alert[0],
                                                                                  orig_short_desc, svc_type)
                            else:
                                # power supply issue, need contact HPE or LIT
                                worknotes = '[power supply1:' + power_health[0] + ' powersupply2:' + power_health[
                                    1] + ']'
                                prefix = '[Manually check]'
                                self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix,
                                                                                 worknotes)
                        except Exception as error:
                            prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                            self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix,
                                                                             worknotes=f"Exception error+{error}")
                    else:  # status_data = None, ilo API failed!, unassign inc
                        prefix = IncHandlerCfg.ILO_API_PREFIX
                        self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix,
                                                                         worknotes=None)
                else:  # ilo_ip =None, Get ip failed from nutanix API
                    prefix = IncHandlerCfg.NX_API_PREFIX
                    self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix, worknotes=None)

    @exception_handler
    def wtp_power_supply_model(self, inc_num, orig_short_desc, hostname, svc_type):
        status_data = self.incident_handler_tools.get_wtp_ilo_power_state(hostname)
        if status_data:  # if json data not null
            all_power_status = status_data.get('PowerSupplies', [])
            power_health = [psu['Status']['Health'] for psu in all_power_status]
            if power_health[0] == 'OK' and power_health[1] == 'OK':
                wtp_ilo_worknotes = f"GDH updates>>\nThe {hostname} power supply are redundant,close the case."
                self.incident_handler_tools.incident_terminator(inc_num, wtp_ilo_worknotes, orig_short_desc, svc_type)
                self.incident_handler_tools.commit_incident_handler_data(inc_num, orig_short_desc, wtp_ilo_worknotes,
                                                                         task_type='incident_resolved')
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_RESOLVED,
                                                                        f"{inc_num} wtp power supply resolved successfully!")
                self.logger.info(f"{inc_num} wtp power supply resolved successfully!")
            else:  # power supply issue, unassign inc and check mannually,please up priority
                prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix, worknotes=None)
        else:  # status_data = None, ilo API failed!, unassign inc
            prefix = IncHandlerCfg.ILO_API_PREFIX
            self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix, worknotes=None)

    @exception_handler
    def vm_not_found_model(self, inc_num, orig_short_desc, inc_desc, svc_type):
        # get protection domain name and VM name[]
        if 'Unable' in inc_desc:
            protect_domain_name = re.search(IncHandlerCfg.NX_GET_PD_PAT2, inc_desc).group(1)
            alert_uuid = IncHandlerCfg.NX_UNABLE_LOCATE_VM_UUID
        elif 'Latest' in inc_desc:
            protect_domain_name = re.search(IncHandlerCfg.NX_GET_PD_PAT, inc_desc).group(1)
            alert_uuid = IncHandlerCfg.NX_VM_MISSING_ENTITIES_UUID
        else:  #Failed to ...
            protect_domain_name = re.search(IncHandlerCfg.NX_GET_PD_PAT3, inc_desc).group(1)
            alert_uuid = IncHandlerCfg.NX_VM_FAILED_SNAPSHOT_ENTITIES_UUID
        if 'null' in orig_short_desc:
            self.incident_handler_tools.handle_null_pe(inc_num, orig_short_desc, svc_type)
            return
        # if[Human] in short description, removed. else nothing changed
        new_inc_short_dscpt = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, orig_short_desc).group(1)
        pe_name = re.search(IncHandlerCfg.NX_CLUSTER_NAME_PAT, new_inc_short_dscpt).group(1)
        inc_vm_name = self.incident_handler_tools.pattern_match(text=inc_desc, patterns=IncHandlerCfg.NX_VM_NAME_PAT)
        prefix_pd = protect_domain_name.split('-')[
            0]  # split RETRSSO-NXC000-Gold_CCG ---> RETRSSO NXC000 Gold_CCG  get RETRSSO
        temp_pe_name = '-'.join(protect_domain_name.split('-')[:2])  # get RETRSSO-NXC000
        prefix_pe = pe_name.split('-')[0]
        if prefix_pe != prefix_pd:
            pe_name = temp_pe_name
        find_alert = self.incident_handler_tools.get_unresolved_nutanix_cluster_alerts(pe_name, alert_uuid)
        latest_alert = find_alert.get('entities')
        if latest_alert:
            try:
                alert_id = latest_alert[0]['id']
                #start NTX API process, return True or False:
                if self.incident_handler_tools.reset_protection_domain(pe_name, protect_domain_name, inc_vm_name):
                    status_code = self.incident_handler_tools.post_resolve_alerts(pe_name, alert_id)
                    # test
                    if status_code in (200, 201, 202):
                        #close inc
                        self.incident_handler_tools.handle_common_resolve(inc_num, orig_short_desc, svc_type)
                    else:
                        self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.NX_ALERT_CHK,
                                                                                f"{inc_num} resolve nutanix alert error{status_code}")
                        self.logger.error(f"{inc_num} resolve nutanix alert error{status_code}")
                else:
                    #reset pd failed，add [human]and unassign inc
                    worknotes = ' reset Protect Domain failed!'
                    self.incident_handler_tools.add_workload_payload(worknotes)
                    prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                    self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
                    self.incident_handler_tools.handle_common_failed(inc_num, new_inc_short_dscpt, prefix=prefix,
                                                                     worknotes=worknotes)
            except Exception as error:
                prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                self.incident_handler_tools.handle_common_failed(inc_num, new_inc_short_dscpt, prefix=prefix,
                                                                 worknotes=f"Exception error+{error}")
        else:
            #no unsolved alert for this type, close incident directly
            worknotes = '''GDH update>>
            The alert already resolved,hence closing the inc!'''
            self.incident_handler_tools.commit_incident_handler_data(inc_num, orig_short_desc, worknotes,
                                                                     task_type='incident_resolved')
            self.incident_handler_tools.incident_terminator(inc_num, worknotes, new_inc_short_dscpt, svc_type)
            self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_RESOLVED,
                                                                    f"{inc_num} vm not found is resolved successfully!")
            self.logger.info(f"{inc_num} vm not found is resolved successfully!")

    @exception_handler
    def disk_offline_model(self, inc_num, orig_short_desc, pe, disk_sn, svc_type):
        alert_uuid = IncHandlerCfg.NX_DISK_MARKED_OFFLINE_UUID
        pe_name = re.search(IncHandlerCfg.NX_GET_PE_NAME_PAT, orig_short_desc).group(1)
        inc_brief = 'disk offline'
        result = self.incident_handler_tools.common_model_handler(inc_num, pe_name, alert_uuid, orig_short_desc,
                                                                  inc_brief, svc_type)  # return alert_id or 'close'
        if 'close' not in result:
            if self.incident_handler_tools.get_nx_disk_info(pe,
                                                            disk_sn):  # disk info return by mounted True or False, if True close inc
                self.incident_handler_tools.close_nx_inc_alert(inc_num, orig_short_desc, pe_name, result, svc_type)
            else:
                self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
                prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                worknotes = """Incident-Handler update>>
                Please check disk status manually"""
                self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix=prefix,
                                                                 worknotes=worknotes)

    @exception_handler
    def ahv_incompatible_model(self, inc_num, orig_short_desc, pe, cluster_uuid, svc_type):
        if 'incompatible' in orig_short_desc:
            alert_uuid = IncHandlerCfg.nx_ahv_incompatible_pattern
        elif 'Crash' in orig_short_desc:
            alert_uuid = IncHandlerCfg.nx_ahv_crash_file_uuid
        inc_brief = 'AHV/Fanout port'
        alert_id = self.incident_handler_tools.common_model_handler(inc_num, pe, alert_uuid, orig_short_desc, inc_brief, svc_type)
        if 'close' not in alert_id:
            version = self.incident_handler_tools.get_nx_ahv_version(pe, cluster_uuid)  #get ahv version from api
            if len(version) == 1:  # all ahv version are the same,close the NX alert and INC
                self.incident_handler_tools.close_nx_inc_alert(inc_num, orig_short_desc, pe, alert_id, svc_type)
            else:
                self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
                prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                worknotes = 'detected different AHV version or error'
                self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix, worknotes)

    @exception_handler
    def nx_host_link_down_model(self, inc_num, orig_short_desc, pe, host, inc_creation_time, svc_type):
        nx_host_link_down_uuid = IncHandlerCfg.nx_host_link_down_uuid
        last_alert = self.incident_handler_tools.nutanix_case_handle_common_process(inc_num, pe, orig_short_desc,
                                                                                    nx_host_link_down_uuid,
                                                                                    svc_type
                                                                                    )
        if last_alert is None:
            print(f"No alert for {inc_num} {pe} {host}")
            self.logger.error(f"No alert for {inc_num} {pe} {host}")
        if last_alert.get('resolved', False):
            self.incident_handler_tools.handle_resolved_alert(inc_num, last_alert, orig_short_desc, svc_type)
        else:
            host_root_pw = self.incident_handler_tools.get_vault_psd(f'{pe}/Site_Ahv_Root')
            all_true = True
            for i in range(2):
                command = f'cat /sys/class/net/eth{i}/operstate'
                output = self.incident_handler_tools.run_remote_command(
                    host=host,
                    password=host_root_pw,
                    command=command,
                    username='root')
                result = True if 'up' in output else False
                all_true = all_true and result
            if all_true:
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK,
                                                                        f"{inc_num}-{pe}-{host} nic link is up, but NX alert is still exist, closing NX alerts!")
                self.logger.info(f"{inc_num}-{pe}-{host} nic link is up, but NX alert is still exist, closing NX alerts!")
                get_unresolve_alert = self.incident_handler_tools.get_unresolved_nutanix_cluster_alerts(pe,
                                                                                                        nx_host_link_down_uuid)
                for entity in get_unresolve_alert['entities']:
                    self.incident_handler_tools.post_resolve_alerts(pe, entity['id'])
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK,
                                                                        f"{inc_num}-{pe}-{host}-nic link down alert is resolved,closing the case!")
                self.logger.info(f"{inc_num}-{pe}-{host}-nic link down alert is resolved,closing the case!")
                host_nic_down_worknotes = f"""GDH update>>
                The {host} nic connection has no problem, close the case!
                """
                self.incident_handler_tools.incident_terminator(inc_num, host_nic_down_worknotes, orig_short_desc, svc_type)
            else:
                end_time, start_time_minus_1_day, start_time_minus_1_week = self.incident_handler_tools.calculate_new_time_duration(
                    inc_creation_time)
                alert_num_despt = self.incident_handler_tools.get_nutanix_history_alerts_number(pe,
                                                                                                start_time_minus_1_day,
                                                                                                start_time_minus_1_week,
                                                                                                end_time,
                                                                                                nx_host_link_down_uuid)
                new_short_despt = IncHandlerCfg.NX_INC_SHORT_DESC_PREFIX + alert_num_despt + orig_short_desc
                comments = f"{host} NIC is down, pls check"
                self.incident_handler_tools.unassign_inc_and_promote_to_high(inc_num, new_short_despt, comments)

    @exception_handler
    def nx_cvm_srv_restart_model(self, inc_num, orig_short_desc, pe, inc_creation_time, svc_type):
        cvm_srv_restart_uuid = IncHandlerCfg.nx_cvm_service_restart_uuid
        latest_alert = self.incident_handler_tools.nutanix_case_handle_common_process(inc_num, pe, orig_short_desc,
                                                                                      cvm_srv_restart_uuid,
                                                                                      svc_type
                                                                                      )
        if latest_alert is None:
            self.logger.error(f"ERROR:{latest_alert} is none!")
            raise ValueError("latest_alert is None")
        if latest_alert.get('resolved', False):
            self.incident_handler_tools.handle_resolved_alert(inc_num, latest_alert, orig_short_desc, svc_type)
        else:
            self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
            end_time, start_time_minus_1_day, start_time_minus_1_week = self.incident_handler_tools.calculate_new_time_duration(
                inc_creation_time)
            alerts_number_description = self.incident_handler_tools.get_nutanix_history_alerts_number(pe,
                                                                                                      start_time_minus_1_day,
                                                                                                      start_time_minus_1_week,
                                                                                                      end_time,
                                                                                                      cvm_srv_restart_uuid)
            self.incident_handler_tools.handle_unresolved_alert(inc_num, alerts_number_description, orig_short_desc)

    @exception_handler
    def nx_pd_not_any_prog_model(self, inc_num, orig_short_desc, pe, inc_creation_time, svc_type):
        nx_pd_not_any_prog_uuid = IncHandlerCfg.NX_PD_NOT_ANY_PROG_UUID
        latest_alert = self.incident_handler_tools.nutanix_case_handle_common_process(inc_num, pe, orig_short_desc,
                                                                                      nx_pd_not_any_prog_uuid,
                                                                                      svc_type
                                                                                      )
        if latest_alert is None:
            self.logger.error(f"ERROR:{latest_alert} is none!")
            raise ValueError("latest_alert is None")
        if latest_alert.get('resolved', False):
            self.incident_handler_tools.handle_resolved_alert(inc_num, latest_alert, orig_short_desc, svc_type)
        else:
            self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
            end_time, start_time_minus_1_day, start_time_minus_1_wk = self.incident_handler_tools.calculate_new_time_duration(
                inc_creation_time)
            alert_desc = self.incident_handler_tools.get_nutanix_history_alerts_number(pe, start_time_minus_1_day,
                                                                                       start_time_minus_1_wk, end_time,
                                                                                       nx_pd_not_any_prog_uuid)
            self.incident_handler_tools.handle_unresolved_alert(inc_num, alert_desc, orig_short_desc)

    @exception_handler
    def nx_pd_repl_failed_model(self, inc_num, orig_short_desc, pe, inc_creation_time, svc_type):
        nx_pd_repl_failed_uuid = IncHandlerCfg.NX_PD_REPL_FAILED_UUID
        latest_alert = self.incident_handler_tools.nutanix_case_handle_common_process(inc_num, pe, orig_short_desc,
                                                                                      nx_pd_repl_failed_uuid,
                                                                                      svc_type
                                                                                      )
        if latest_alert is None:
            self.logger.error(f"ERROR:{latest_alert} is none!")
            raise ValueError("latest_alert is None")
        if latest_alert.get('resolved', False):
            self.incident_handler_tools.handle_resolved_alert(inc_num, latest_alert, orig_short_desc, svc_type)
        else:
            self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
            end_time, start_time_minus_1_day, start_time_minus_1_wk = self.incident_handler_tools.calculate_new_time_duration(
                inc_creation_time)
            alert_desc = self.incident_handler_tools.get_nutanix_history_alerts_number(pe, start_time_minus_1_day,
                                                                                       start_time_minus_1_wk, end_time,
                                                                                       nx_pd_repl_failed_uuid)
            self.incident_handler_tools.handle_unresolved_alert(inc_num, alert_desc, orig_short_desc)

    @exception_handler
    def common_check_model(self, inc_num, inc_shortdesc, inc_desc, svc_type):
        if 'Unnamed' in inc_shortdesc or 'null' in inc_shortdesc:
            self.incident_handler_tools.handle_null_pe(inc_num, inc_shortdesc, svc_type)
            return
        try:
            original_dscpt = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_shortdesc).group(1)
            pe = re.search(IncHandlerCfg.NX_GET_PE_NAME_PAT, original_dscpt).group(1)
        except AttributeError as error:
            self.logger.error(f"Error in handling {inc_num} , error: {error}")
            self.incident_handler_tools.commit_inc_handler_error_log(inc_num, inc_shortdesc, "failed catch string")
            return
        match True:
            case _ if 'NTP is not configured' in inc_shortdesc:
                alert_uuid = IncHandlerCfg.NX_NTP_NOT_CONF_UUID
                inc_brief = 'NTP sync'
            case _ if 'not synchronizing with any NTP' in inc_desc:
                alert_uuid = IncHandlerCfg.NX_HOST_NOT_SYNC_NTP_UUID
                inc_brief = 'NTP sync'
            case _ if 'CVM Time Difference High' in inc_shortdesc:
                alert_uuid = IncHandlerCfg.NX_CVM_TIME_DIFF_UUID
                inc_brief = 'CVM time difference'
            case _ if 'Fanout' in inc_shortdesc:
                alert_uuid = IncHandlerCfg.NX_CVM_FANOUTPORT_UUID
                inc_brief = 'Fanout Secure port connection'
            case _ if 'not synchronizing to an external NTP' in inc_desc:
                alert_uuid = IncHandlerCfg.NX_CVM_TIME_SYNC_UUID
                inc_brief = 'CVM time not sync'
            case _ if 'NIC Flaps' in inc_shortdesc:
                alert_uuid = IncHandlerCfg.NX_NIC_FLAPS_UUID
                inc_brief = 'NIC Flaps'
            case _ if 'License Feature' in inc_shortdesc:
                alert_uuid = IncHandlerCfg.NX_LIC_VIO_UUID
                inc_brief = 'License Feature Violation'
            case _ if 'inconsistencies are detected' in inc_shortdesc:
                alert_uuid = IncHandlerCfg.NX_EXT4_UUID
                inc_brief = 'EXT4 file system errors are detected'
            case _:
                alert_uuid = '000'
                inc_brief = 'nothing get'
        alert_id = self.incident_handler_tools.common_model_handler(inc_num, pe, alert_uuid, original_dscpt, inc_brief, svc_type)
        if 'close' not in alert_id:
            prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
            self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, original_dscpt)
            self.incident_handler_tools.handle_common_failed(inc_num, original_dscpt, prefix, worknotes=None)

    @exception_handler
    def wiab_common_check_model(self, inc_num, inc_shortdesc, inc_desc, svc_type):
        if 'Unnamed' in inc_shortdesc or 'unnamed' in inc_shortdesc or 'null' in inc_shortdesc:
            self.incident_handler_tools.handle_null_pe(inc_num, inc_shortdesc, svc_type)
            return
        try:
            original_dscpt = re.search(IncHandlerCfg.WIAB_INC_ORIG_PAT, inc_shortdesc).group(0)
            pe = re.search(IncHandlerCfg.WIAB_PE_NAME_PAT, inc_desc).group(1)
        except AttributeError as error:
            self.logger.error(f"Error in handling {inc_num} , error: {error}")
            self.incident_handler_tools.commit_inc_handler_error_log(inc_num, inc_shortdesc, "failed catch string")
            self.logger.error("failed catch string")
            return
        match inc_shortdesc:
            case _ if 'Host' in inc_shortdesc and 'time not sync' in inc_shortdesc:
                alert_uuid = IncHandlerCfg.NX_HOST_NOT_SYNC_NTP_UUID
                inc_brief = 'NTP sync'
            case _ if 'CVM' in inc_shortdesc and 'time not sync' in inc_shortdesc:
                alert_uuid = IncHandlerCfg.NX_CVM_TIME_SYNC_UUID
                inc_brief = 'CVM time not sync'
            case _:
                alert_uuid = '000'
                inc_brief = 'nothing get'
        alert_id = self.incident_handler_tools.common_model_handler(inc_num, pe, alert_uuid, original_dscpt, inc_brief, svc_type)
        if 'close' not in alert_id:
            prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
            self.incident_handler_tools.handle_common_failed(inc_num, original_dscpt, prefix, worknotes=None)

    def ecox_model(self, inc_num, orig_short_desc, inc_creation_time, svc_type):
        if IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN in orig_short_desc:  #[Human] prefix in short desc
            return 0
        if 'temperature' in orig_short_desc or 'Temperature' in orig_short_desc:
            # up priority
            state = IncHandlerCfg.INC_STATE_IN_PROGRESS
            impact = IncHandlerCfg.INC_IMPACT_MED
            urgency = IncHandlerCfg.INC_URGENCY_HIGH
            worknotes = """GDH>> 
                        we found this site had temperature high caution from Ecox sensor.
                        please help to check and close this inc if everything ok,thanks.
                        """
            assign_gp = self.incident_handler_tools.ecro_handle(orig_short_desc)
            new_short_despt = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN + orig_short_desc
            if assign_gp:
                self.incident_handler_tools.incident_transfer(inc_num, state, impact, urgency, worknotes, assign_gp,
                                                              new_short_despt)
            else:
                prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                notes = "assign group failed,not found LIT group"
                self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix=prefix,
                                                                 worknotes=notes)
        else:
            flag = self.incident_handler_tools.ecro_time_cal(inc_creation_time)
            if flag:  # true,close the inc
                self.incident_handler_tools.handle_common_resolve(inc_num, orig_short_desc, svc_type)
            else:
                prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                worknotes = 'EcoX'
                self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix, worknotes)

    @exception_handler
    def cvm_disconnected_model(self, inc_num, orig_short_desc, pe_name, ping_ip, ssh_ip, svc_type):
        alert_uuid = IncHandlerCfg.NX_CVM_DISCED_UUID
        inc_brief = 'cvm disconnected'
        alert_id = self.incident_handler_tools.common_model_handler(inc_num, pe_name, alert_uuid, orig_short_desc,
                                                                    inc_brief, svc_type)
        if 'close' not in alert_id:
            if self.incident_handler_tools.handle_cvm_disconnected(pe_name, ping_ip,
                                                                   ssh_ip):  # true-close inc, false-add [human]
                self.incident_handler_tools.close_nx_inc_alert(inc_num, orig_short_desc, pe_name, alert_id, svc_type)
            else:
                prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                worknotes = f'cvm ip {ping_ip} unreachable'
                self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix, worknotes)

    @exception_handler
    def connect_rest_model(self, inc_num, orig_short_desc, pe_name, ping_ip, svc_type):
        alert_uuid = IncHandlerCfg.NX_PULSE_REST_UUID
        inc_brief = 'cvm connection'
        alert_id = self.incident_handler_tools.common_model_handler(inc_num, pe_name, alert_uuid, orig_short_desc,
                                                                    inc_brief, svc_type)  # return alert_id or 'close'
        if 'close' not in alert_id:
            if self.incident_handler_tools.ping_ip(ping_ip):
                self.incident_handler_tools.close_nx_inc_alert(inc_num, orig_short_desc, pe_name, alert_id, svc_type)
            else:
                prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                worknotes = f'cvm ip {ping_ip} unreachable'
                self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix, worknotes)

    @exception_handler
    def disk_usage_high_model(self, inc_num, inc_short_desc, cvm_ip, svc_type):
        if 'Unnamed' in inc_short_desc or 'null' in inc_short_desc:
            self.incident_handler_tools.handle_null_pe(inc_num, inc_short_desc, svc_type)
            return
        original_dscpt = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
        pe_name = re.search(IncHandlerCfg.NX_GET_PE_NAME_PAT, original_dscpt).group(1)
        alert_uuid = IncHandlerCfg.NX_DISK_HIGH_UUID
        inc_brief = 'disk usage high'
        alert_id = self.incident_handler_tools.common_model_handler(inc_num, pe_name, alert_uuid, inc_short_desc,
                                                                    inc_brief, svc_type)
        if 'close' not in alert_id:
            result = self.incident_handler_tools.del_cvm_logs(pe_name, cvm_ip)
            print(result)
            if result < 75:  # close inc
                self.incident_handler_tools.close_nx_inc_alert(inc_num, inc_short_desc, pe_name, alert_id, svc_type)
            else:
                prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                worknotes = 'delete logs failed,please check other paths'
                self.incident_handler_tools.handle_common_failed(inc_num, inc_short_desc, prefix, worknotes)

    @exception_handler
    def metadata_ring_model(self, inc_num, inc_short_desc, cvm_ip, svc_type):
        if 'Unnamed' in inc_short_desc or 'null' in inc_short_desc:
            self.incident_handler_tools.handle_null_pe(inc_num, inc_short_desc, svc_type)
            return
        original_dscpt = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
        pe_name = re.search(IncHandlerCfg.NX_GET_PE_NAME_PAT, original_dscpt).group(1)
        alert_uuid = None
        inc_brief = 'Metadata Ring'
        alert_id = self.incident_handler_tools.common_model_handler(inc_num, pe_name, alert_uuid, inc_short_desc,
                                                                    inc_brief, svc_type)
        if 'close' not in alert_id:
            flag, result = self.incident_handler_tools.check_maintenance_mode(pe_name, cvm_ip)
            if flag:
                self.incident_handler_tools.close_nx_inc_alert(inc_num, inc_short_desc, pe_name, alert_id, svc_type)
            else:
                prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
                worknotes = result + 'in maintenance,please manually check'
                self.incident_handler_tools.handle_common_failed(inc_num, inc_short_desc, prefix, worknotes)

    @exception_handler
    def sipped_repl_model(self, inc_num, inc_short_desc, svc_type, snap_id, inc_desc, alert_type_id):
        if 'Unnamed' in inc_short_desc or 'null' in inc_short_desc:
            self.incident_handler_tools.handle_null_pe(inc_num, inc_short_desc, svc_type)
            return
        orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group()
        pe = re.search(IncHandlerCfg.NX_SKIPPED_REPL_PAT, orig_short_desc).group(1)
        #Try to get the latest replication end time
        last_repl_snap_id = self.incident_handler_tools.get_last_repl_snap_id(pe)
        if last_repl_snap_id is None:
            print(f"last_repl_snap_id is None, {pe} has no replication history")
            return
        if last_repl_snap_id > snap_id:
            pc_add = re.search(IncHandlerCfg.PC_ADDRESS_PATTERN, inc_desc).group(1)
            pe_uuid = re.search(IncHandlerCfg.PE_UUID_PATTERN, inc_desc).group(1)
            act_alert_info_dict = self.incident_handler_tools.get_active_alert_info_pc(pc_add, alert_type_id)
            _workenote = self.incident_handler_tools.pc_resolved_alert_worknote(pe_uuid, pc_add)
            if act_alert_info_dict is None:
                self.logger.error(f"act_alert_info_dict is None, {pe} has no active alert")
                return
            if pe_uuid not in act_alert_info_dict:
                self.incident_handler_tools.incident_terminator(inc_num, _workenote, orig_short_desc, svc_type)
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_RESOLVED,
                                                                f"{inc_num} is resolved now!")
            else:
                logging.info(f"Remote site backup is running well, resolving NX alert from {pc_add}!")
                self.incident_handler_tools.post_resolve_pc_alerts(pc_add, act_alert_info_dict[pe_uuid])
                self.incident_handler_tools.incident_terminator(inc_num, _workenote, orig_short_desc, svc_type)
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_RESOLVED,
                                                                f"{inc_num} is resolved now!")
        else:
            new_short_desc = "[Human]" + orig_short_desc
            self.incident_handler_tools.unassign_gdh_incident(number=inc_num, new_dscription=new_short_desc)

    @exception_handler
    def zookeeper_not_active_model(self, inc_num, orig_short_desc, pe, inc_creation_time, svc_type):
        zookeeper_not_active_uuid = IncHandlerCfg.zookeeper_not_active_uuid
        latest_alert = self.incident_handler_tools.nutanix_case_handle_common_process(inc_num, pe, orig_short_desc,
                                                                                      zookeeper_not_active_uuid,
                                                                                      svc_type
                                                                                      )
        if latest_alert is None:
            self.logger.error(f"ERROR:{latest_alert} is none!")
        if latest_alert.get('resolved', False):
            self.incident_handler_tools.handle_resolved_alert(inc_num, latest_alert, orig_short_desc, svc_type)
        else:
            self.incident_handler_tools.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
            end_time, start_time_minus_1_day, start_time_minus_1_week = self.incident_handler_tools.calculate_new_time_duration(
                inc_creation_time)
            alerts_number_description = self.incident_handler_tools.get_nutanix_history_alerts_number(pe,
                                                                                                      start_time_minus_1_day,
                                                                                                      start_time_minus_1_week,
                                                                                                      end_time,
                                                                                                      IncHandlerCfg.nutanix_cluster_service_restart_uuid)
            self.incident_handler_tools.handle_unresolved_alert(inc_num, alerts_number_description, orig_short_desc)

    @exception_handler
    def wiab_host_link_down_model(self, inc_num, orig_short_desc, pe, pc, pe_uuid, host, svc_type):
        nx_host_link_down_uuid = IncHandlerCfg.nx_host_link_down_uuid
        active_alert_info_dict = self.incident_handler_tools.get_active_alert_info_pc(pc, nx_host_link_down_uuid)
        last_alert = next((alert_id_value for cluster_uuid_key, alert_id_value in active_alert_info_dict.items() if cluster_uuid_key == pe_uuid), None)

        if last_alert is None:  # resolve incident
            worknotes = self.incident_handler_tools.pc_resolved_alert_worknote(pe_uuid, pc)
            self.incident_handler_tools.handle_common_resolve(inc_num, orig_short_desc, svc_type, worknotes)
            return
        pe = pe.upper()
        host_root_pw = self.incident_handler_tools.get_vault_psd(secret=f'{pe}/Site_Ahv_Root', prod='wiab')
        all_true = True
        for i in range(2):
            if i == 1:
                continue
            command = f'cat /sys/class/net/eth{i}/operstate'
            output = self.incident_handler_tools.run_remote_command(
                host=host,
                password=host_root_pw,
                command=command,
                username='root')
            result = True if 'up' in output else False
            all_true = all_true and result
        if all_true:
            self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK,
                                                                    f"{inc_num}-{pe}-{host} nic link is up, but NX alert is still exist, closing NX alerts!")
            task_uuid = self.incident_handler_tools.post_resolve_pc_alerts(pc, last_alert)
            print(task_uuid)  # test
            self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK,
                                                                    f"{inc_num}-{pe}-{host}-nic link down alert is resolved,closing the case!")
            host_nic_down_worknotes = f"""GDH update>>
                              The {host} nic connection has no problem, close the case!
                              """
            self.incident_handler_tools.incident_terminator(inc_num, host_nic_down_worknotes, orig_short_desc, svc_type)
            return
        new_short_despt = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN + orig_short_desc
        comments = f"{host} NIC is down, pls check"
        self.incident_handler_tools.unassign_inc_and_promote_to_high(inc_num, new_short_despt, comments)

    @exception_handler
    def wiab_fanout_secure_port_model(self, inc_num, orig_short_desc, inc_desc, svc_type):
        try:
            pc_add = re.search(IncHandlerCfg.PC_ADDRESS_PATTERN, inc_desc).group(1)  # ssp-eu-wiab-ntx.ikea.com
            pe_cluster_uuid = re.search(IncHandlerCfg.PE_UUID_PATTERN, inc_desc).group(1)
            pe = re.search(IncHandlerCfg.WIAB_PE_NAME_PAT, inc_desc).group(1)
            result = re.findall(IncHandlerCfg.FANOUT_IP_PAT, inc_desc)
            ip_port_list = [ast.literal_eval(match) for match in
                            result]  # [('*************', 9300), ('*************', 9301)]
            cvm_ip = re.search(IncHandlerCfg.CVM_IP_PAT, inc_desc).group(1)
        except AttributeError as e:
            self.incident_handler_tools.commit_inc_handler_error_log(inc_num, orig_short_desc, f"AttributeError:{e}")
            return
        active_alert_info_dict = self.incident_handler_tools.get_active_alert_info_pc(pc_add,
                                                                                      IncHandlerCfg.NX_CVM_FANOUTPORT_UUID)
        last_alert = next((alert_id_value for cluster_uuid_key, alert_id_value in active_alert_info_dict.items() if
                           cluster_uuid_key == pe_cluster_uuid), None)
        if last_alert is None:  # resolve incident
            worknotes = self.incident_handler_tools.pc_resolved_alert_worknote(pe_cluster_uuid, pc_add)
            self.incident_handler_tools.handle_common_resolve(inc_num, orig_short_desc, svc_type, worknotes)
            return
        pe = pe.upper()
        cvm_pw = self.incident_handler_tools.get_vault_psd(secret=f'{pe}/Site_Pe_Nutanix', prod='wiab')
        flag = True
        for _, port in ip_port_list:
            command = rf"allssh 'echo \$ | nc -tv {pc_add} {port}'"
            output = self.incident_handler_tools.run_remote_command(
                host=cvm_ip,
                password=cvm_pw,
                command=command,
                username='nutanix')
            result = True if 'Connected' in output else False
            flag = flag and result
        if flag:
            self.incident_handler_tools.post_resolve_pc_alerts(pc_add, last_alert)
            self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK,
                                                                    f"{inc_num}-{pe}-PC is reachable,closing the case!")
            fanout_worknotes = f"""GDH update>>
                                          The {cvm_ip} to PC connection has no problem, close the case!
                                          """
            self.incident_handler_tools.incident_terminator(inc_num, fanout_worknotes, orig_short_desc, svc_type)
            return
        prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
        worknotes = f"{cvm_ip} to PC Connection timed out"
        self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix, worknotes)

    @exception_handler
    def ipmi_ip_mismatch_model(self, inc_num, inc_short_desc, inc_desc, svc_type):
        if IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN in inc_short_desc:
            self.incident_handler_tools.common_handle_pc_alert_inc(
                inc_short_desc,
                inc_num,
                inc_desc,
                IncHandlerCfg.IPMI_MISMATCH_PAT,
                IncHandlerCfg.IPMI_MISMSTCH_UUID,
                svc_type,
                ncc_chk='15013'  # check ID
            )
            return
        orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
        if 'Unnamed' in inc_short_desc or 'null' in inc_short_desc:
            self.incident_handler_tools.handle_null_pe(inc_num, inc_short_desc, svc_type)
            return
        try:
            cvm_ip = re.search(IncHandlerCfg.CVM_IP_PAT2, inc_desc).group(1)
            pc_add = re.search(IncHandlerCfg.PC_ADDRESS_PATTERN, inc_desc).group(1)  # ssp-eu-wiab-ntx.ikea.com
            pe_cluster_uuid = re.search(IncHandlerCfg.PE_UUID_PATTERN, inc_desc).group(1)
            if 'WiaB' in inc_short_desc:
                pe = re.search(IncHandlerCfg.WIAB_PE_NAME_PAT, inc_desc).group(1)
                pe = pe.upper()
                cvm_pw = self.incident_handler_tools.get_vault_psd(secret=f'{pe}/Site_Pe_Nutanix', prod='wiab')
            else:
                pe = re.search(IncHandlerCfg.NX_GET_PE_NAME_PAT, inc_short_desc).group(1)
                pe = pe.upper()
                cvm_pw = self.incident_handler_tools.get_vault_psd(secret=f'{pe}/Site_Pe_Nutanix')
        except AttributeError as e:
            self.incident_handler_tools.commit_inc_handler_error_log(inc_num, orig_short_desc, f"AttributeError:{e}")
            return
        active_alert_info_dict = self.incident_handler_tools.get_active_alert_info_pc(pc_add,
                                                                                      IncHandlerCfg.IPMI_MISMSTCH_UUID)
        last_alert = next((alert_id_value for cluster_uuid_key, alert_id_value in active_alert_info_dict.items() if
                           cluster_uuid_key == pe_cluster_uuid), None)
        if last_alert is None:
            worknotes = self.incident_handler_tools.pc_resolved_alert_worknote(pe_cluster_uuid, pc_add)
            self.incident_handler_tools.handle_common_resolve(inc_num, orig_short_desc, svc_type, worknotes)
            return
        command = "allssh 'genesis restart'"
        try:
            self.incident_handler_tools.run_remote_command(
                host=cvm_ip,
                password=cvm_pw,
                command=command,
                username='nutanix')
            comments = "genesis restarted,let's wait for next time check"
        except Exception as e:
            comments = f"ssh failed,reason:{e}"
        prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
        self.incident_handler_tools.handle_common_failed(inc_num, orig_short_desc, prefix, comments)
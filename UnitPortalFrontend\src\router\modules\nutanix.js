/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const ntxRouter = {
  path: '/nutanix',
  component: Layout,
  redirect: '/nutanix/prism',
  // name: 'Table',
  meta: {
    title: 'Nutanix',
    icon: 'nutanix',
    roles: ['admin','pmuser','superadmin'],
    privilege:'role_ntx'
  },
  children: [
    {
      path: 'prism',
      component: () => import('@/views/nutanix/prism'),
      name: 'Prism Central',
      meta: { title: 'Prism Central' , roles: ['admin','pmuser','superadmin'],privilege:'view_pc'}
    },
    {
      path: 'pe',
      component: () => import('@/views/nutanix/pe'),
      name: 'Prism Element',
      meta: { title: 'Prism Element' , roles: ['admin','pmuser','superadmin'],privilege:'view_pe'}
    },
    {
      path: 'ahv',
      component: () => import('@/views/nutanix/ahv'),
      name: 'AHV',
      meta: { title: 'AHV' , roles: ['admin','pmuser','superadmin'],privilege:'view_ahv'}
    },
    {
      path: 'vm',
      component: () => import('@/views/nutanix/vm'),
      name: 'Virtual Machine',
      meta: { title: 'Virtual Machine' , roles: ['admin','pmuser','superadmin'],privilege:'view_vm'}
    }
  ]
}
export default ntxRouter

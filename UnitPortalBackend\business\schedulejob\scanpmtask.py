
#installed modules
import logging
from flask import Flask
#local files
from models.ntx_models import ModelPrismElement
from business.distributedhosting.nutanix.automation.lcm import LCM
from models.database import db
from models.pm_models import ModelSLIPMTask
from business.generic.commonfunc import DBConfig


def scan_pm_task():
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    app.app_context().push()
    db.init_app(app)            # db is in models.py
    logging.info("Scaning PM tasks............")
    with app.app_context():     # app.app_context().push()  same.
        # ntx_tasks = ModelNTXPMTask.query.filter_by(status='Not Started',startdate='startnow').all()
        sli_tasks = ModelSLIPMTask.query.filter_by(status='Not Started', startdate='startnow').all()    # noqa  # pylint: disable=W0612
        db.session.remove()
        db.engine.dispose()
        db.session.close_all()
        # for task in ntx_tasks:
        #     try:
        #         logging.info(f'Got one "startnow" NTX PM task, task_id:{task.id} task_pc:{task.prism} task_cluster:{task.cluster}, starting it !')    # noqa
        #         p = multiprocessing.Process(target=start_ntx_pm,args=[task.id])
        #         p.start()
        #         task.pid = p.pid
        #         db.session.commit()
        #         logging.info(f'NTX PM task_id:{task.id} has been started, pid is {p.pid}')
        #     except Exception as e:
        #         pass #process itself will handle the error.
        # for task in sli_tasks:
        #     try:
        #         logging.info(f'Got one "startnow" SLI PM task, task_id:{task.id} task_pc:{task.vcenter.upper()} task_cluster:{task.cluster}, starting it !')    # noqa
        #         p = multiprocessing.Process(target=start_simplivity_pm,args=[task.id])
        #         p.start()
        #         task.pid = p.pid
        #         db.session.commit()
        #         logging.info(f'PM task_id:{task.id} has been started, pid is {p.pid}')
        #     except Exception as e:
        #         pass #process itself will handle the error.


def fill_lcm_version():
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    app.app_context().push()
    db.init_app(app)#db is in models.py    
    app.app_context().push()
    pe_list = ModelPrismElement.query.all()
    for pe in pe_list:
        try:
            if not pe.lcm_version:
                lcm = LCM(pe=pe.name)
                lcm.update_lcm_version_info_to_pe()
        except Exception as _:
            continue    

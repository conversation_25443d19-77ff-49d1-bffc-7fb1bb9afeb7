name: Lint & Format check

on:
  pull_request:
    branches:
      - main
      - dev_data_fetch
      - 'bugfix_*'

jobs:
  build:
    runs-on: [ dependabot ]

    strategy:
      matrix:
        python-version: ["3.11"]
    outputs:
      changed_python_files: ${{ steps.get_changed_python_files.outputs.changed_python_files }}
    steps:
    - uses: actions/checkout@v4
      with:
        ref: ${{ github.event.pull_request.head.ref }}
        fetch-depth: 0
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pylint flake8
    - name: Get changed python files
      id: get_changed_python_files
      shell: bash
      run: |
        echo "-------All changed files-------"
        git diff --name-only "${{ github.event.pull_request.base.sha }}" "${{ github.event.pull_request.head.sha }}"

        # Display changed Python files
        echo "-------Changed python files-------"
        git diff --name-only "${{ github.event.pull_request.base.sha }}" "${{ github.event.pull_request.head.sha }}" -- '*.py'

        # Capture changed Python files and write them to GITHUB_OUTPUT
        changed_python_files=$(git diff --name-only "${{ github.event.pull_request.base.sha }}" "${{ github.event.pull_request.head.sha }}" -- '*.py')
        changed_python_files=$(echo "$changed_python_files" | xargs -I {} bash -c 'if [ -f "{}" ]; then echo "{}"; fi')
        changed_python_files=`echo -e "$changed_python_files" | tr '\n' ' ' | xargs`
        echo "$changed_python_files"
        echo "changed_python_files=${changed_python_files}" >> $GITHUB_OUTPUT
    - name: Run flake8
      id: check_flake8
      shell: bash
      if: steps.get_changed_python_files.outputs.changed_python_files != ''
      run: |
        if [ -f .github/workflows/lint_with_filter.py ]; then
          python .github/workflows/lint_with_filter.py -f flake8 -b "${{ github.event.pull_request.base.sha }}" -t HEAD ${{ steps.get_changed_python_files.outputs.changed_python_files }}
        else
          flake8 ${{ steps.get_changed_python_files.outputs.changed_python_files }} --count --max-complexity=10 --statistics
        fi
    - name: Run pylint
      id: check_pylint
      shell: bash
      if: ${{ !cancelled() && steps.get_changed_python_files.outputs.changed_python_files != '' }}
      run: |
        if [ -f .github/workflows/lint_with_filter.py ]; then
          python .github/workflows/lint_with_filter.py -f pylint -b "${{ github.event.pull_request.base.sha }}" -t HEAD ${{ steps.get_changed_python_files.outputs.changed_python_files }}
        else
          pylint ${{ steps.get_changed_python_files.outputs.changed_python_files }}
        fi
    - name: PyTest
      id: run_pytest
      if: ${{ !cancelled() && steps.get_changed_python_files.outputs.changed_python_files != '' }}
      run: |
        if [ -f tests/test_route_registration.py ]; then
          pip install -r requirements.txt -r tests/requirements.txt
          pytest
        fi
    - name: Show result
      shell: bash
      if: always()
      run: |
        echo 'Flake8 check result: "${{ steps.check_flake8.outcome }}"'
        check_flake8_outcome="${{ steps.check_flake8.outcome }}"
        echo 'Pylint check result: "${{ steps.check_pylint.outcome }}"'
        check_pylint_outcome="${{ steps.check_pylint.outcome }}"
        if { [[ "$check_flake8_outcome" != "success" && "$check_flake8_outcome" != "skipped" ]] || [[ "$check_pylint_outcome" != 'success' && "$check_pylint_outcome" != "skipped" ]]; }; then
            echo "Failed in previous steps, please check and fix errors."
            echo "If files you didn't modify are linted, please pull the latest code or check your commit."
            exit 1
        fi
        echo 'PyTest check result: "${{ steps.run_pytest.outcome }}"'

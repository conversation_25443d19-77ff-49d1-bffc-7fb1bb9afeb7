name: Rota<PERSON> JFrog/Artifactory Access Token in vault
on:
  workflow_dispatch: # <PERSON><PERSON> manually
  schedule:
    - cron: '0 0 27 * *' # Trigger on 27th of each month at 00:00

jobs:
  call-artifactory-token-rotator:
    uses: development-pipeline/es-jfrog-workflows-library/.github/workflows/rotate-jfrog-token-vault-secret.yml@v1
    with:
      VAULT_NAMESPACE: dist-host-retail/cicd
      VAULT_ROLE: siab-cicd
      VAULT_AUTH_PATH: jwt
      SECRET_PATH_WITH_KEY: CICD/data/distributedhostingcodecommunity-dhup-docker-dev-local_jfrog-token token
      EXPIRY_IN_SECONDS: '3024000' # Set expiry to 35 days

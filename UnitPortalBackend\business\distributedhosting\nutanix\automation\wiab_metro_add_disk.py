import ntnx_volumes_py_client
import ntnx_clustermgmt_py_client
import uuid
import datetime

from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
from business.authentication.authentication import Vault
from business.generic.base_up_exception import VaultGet<PERSON><PERSON>retFailed
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.distributedhosting.nutanix.base_up_task_property import BaseUpTaskProperty
from models.metro_vg_models import ModelMet<PERSON>VGTask, ModelMetroVGTaskSchema, ModelMetroVGTaskLog, ModelMetroVGTaskLogSchema

from business.generic.commonfunc import setup_common_logger, create_file, convert_GiB_to_bytes
from static.SETTINGS import METRO_VG_LOG_PATH



class WiabMetroAddDisk(BaseUpTask):
    LOG_DIR = METRO_VG_LOG_PATH
    TASK_TYPE = "ADD_DISK_TO_VG"

    def __init__(self, payload):
        super().__init__(
            ModelMetroVGTask, ModelMetroVGTaskSchema, ModelMetroVGTaskLog, ModelMetroVGTaskLogSchema, payload
        )
        self.payload = payload
        self.pe = payload.get("pe")
        self.vg = payload.get("vg")
        self.disk_size = payload.get("disk_size")
        self.pc_svc_account = self.init_svc_account()
        self.task_info = {
            BaseUpTaskProperty.PE:          payload.get("pe"),
        }
        self.task_duplicated_kwargs = {
            "pe": payload.get("pe")
        }
        self.task_identifier = payload.get("pe")
        self.log_path = create_file(filepath=METRO_VG_LOG_PATH, filename=f'ADD_DISK_TO_VG_{self.vg}_{datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d_%H:%M:%S")}')
        self.logger = setup_common_logger(str(uuid.uuid4()), self.log_path)
        self.volumes_client = None
        self.clustermgmt_client = None

    def init_svc_account(self):
        def get_central_pe_name():
            prism = ModelWarehousePrismElement.query.filter_by(name=self.payload.get("pe")).first().prism
            central_pe_fqdn = ModelWarehousePrismCentral.query.filter_by(fqdn=prism).first().central_pe_fqdn
            central_pe_name = ModelWarehousePrismElement.query.filter_by(fqdn=central_pe_fqdn).first().name
            return prism, central_pe_name
        prism, central_pe_name = get_central_pe_name()
        vault = Vault(tier=ModelWarehousePrismCentral.query.filter_by(fqdn=prism).first().tier)
        pc_svc_label = f"{central_pe_name.upper()}/Site_Pc_Svc"
        res, data = vault.get_secret(pc_svc_label)
        if not res:
            raise VaultGetSecretFailed(pc_svc_label)
        pc_svc_account = {"username": data["username"], "password": data["secret"]}
        return pc_svc_account

    def init_api_client_config(self, config):
        config.host = ModelWarehousePrismElement.query.filter_by(name=self.payload.get("pe")).first().prism
        config.port = 9440  # Port to which to connect to
        config.username = self.pc_svc_account['username']  # UserName to connect to the cluster
        config.password = self.pc_svc_account['password']  # Password to connect to the cluster
        config.verify_ssl = True  # TODO: set to True for prod
        return config

    def init_ntx_clients(self):
        self.logger.info("Initializing API client(s)...")
        self.volumes_client = ntnx_volumes_py_client.ApiClient(self.init_api_client_config(ntnx_volumes_py_client.Configuration()))
        self.clustermgmt_client = ntnx_clustermgmt_py_client.ApiClient(self.init_api_client_config(ntnx_clustermgmt_py_client.Configuration()))

    def find_volume_group(self, filter=None):
        self.logger.info(f"Looking for volume group: {filter}")
        volume_groups_api = ntnx_volumes_py_client.VolumeGroupsApi(api_client=self.volumes_client)

        filter = f"name eq '{filter}'"

        try:
            api_response = volume_groups_api.list_volume_groups(_filter=filter)
            if api_response.metadata.total_available_results != 1:
                raise Exception("Expected to find exactly one VG, but found {}".format(api_response.metadata.total_available_results))
            self.logger.info(f"Found volume group with ext id: {api_response.data[0].ext_id}")
            return api_response.data[0].ext_id
        except ntnx_volumes_py_client.rest.ApiException as e:
            self.logger.error("API Exception occurred: %s", e, exc_info=True)
        except Exception as e:
            self.logger.error("Exception occurred: %s", e, exc_info=True)

    # Find ext_id of the room A cluster.
    def find_cluster(self, filter):
        self.logger.info(f"Looking for cluster: {filter}")
        clusters_api = ntnx_clustermgmt_py_client.ClustersApi(api_client=self.clustermgmt_client)

        filter = f"name eq '{filter}'"

        try:
            api_response = clusters_api.list_clusters(_filter=filter)
            if api_response.metadata.total_available_results != 1:
                raise Exception("Expected to find exactly one room A cluster, but found {}".format(api_response.metadata.total_available_results))
            self.logger.info(f"Found cluster with ext id: {api_response.data[0].ext_id}")
            return api_response.data[0].ext_id
        except ntnx_clustermgmt_py_client.rest.ApiException as e:
            self.logger.error("API Exception occurred: %s", e, exc_info=True)
        except Exception as e:
            self.logger.error("Exception occurred: %s", e, exc_info=True)

    # Find cluster storage containers.
    def list_storage_containers(self, filter):
        self.logger.info("Looking for storage container, SelfServiceContainer...")
        storage_containers_api = ntnx_clustermgmt_py_client.StorageContainersApi(api_client=self.clustermgmt_client)

        filter = f"clusterExtId eq '{filter}' and name eq 'SelfServiceContainer'"

        try:
            api_response = storage_containers_api.list_storage_containers(_filter=filter)
            if api_response.metadata.total_available_results != 1:
                raise Exception("Expected to find exactly one SelfServiceContainer, but found {}".format(api_response.metadata.total_available_results))
            self.logger.info(f"Found storage container with ext id: {api_response.data[0].container_ext_id}")
            return api_response.data[0].container_ext_id
        except ntnx_clustermgmt_py_client.rest.ApiException as e:
            self.logger.error("API Exception occurred: %s", e, exc_info=True)
        except Exception as e:
            self.logger.error("Exception occurred: %s", e, exc_info=True)

    def create_new_volume_disk(self, vg_ext_id, disk_size_gib, storage_container_id):
        disk_size_bytes = convert_GiB_to_bytes(disk_size_gib)
        self.logger.info(f"Adding {disk_size_gib} GiB disk to storage container {storage_container_id} in volume group {vg_ext_id}")
        volume_groups_api = ntnx_volumes_py_client.VolumeGroupsApi(api_client=self.volumes_client)
        volume_disk = ntnx_volumes_py_client.VolumeDisk()

        volume_disk = {
            "diskSizeBytes": disk_size_bytes,
            "description": "string",
            "diskDataSourceReference": {
                "extId": storage_container_id,
                "name": "string",
                "uris": [
                "string"
                ],
                "entityType": "STORAGE_CONTAINER"
            }
        }

        volume_group_ext_id = vg_ext_id

        try:
            volume_groups_api.create_volume_disk(volumeGroupExtId=volume_group_ext_id, body=volume_disk)
        except ntnx_volumes_py_client.rest.ApiException as e:
            self.logger.error("API Exception occurred: %s", e, exc_info=True)

    def task_process(self):
        self.init_ntx_clients()
        vg = self.find_volume_group(self.vg)
        pe = self.find_cluster(self.pe)
        sc = self.list_storage_containers(pe)
        self.create_new_volume_disk(vg, self.disk_size, sc)
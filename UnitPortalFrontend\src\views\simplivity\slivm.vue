<template>
    <div class="app-container">
      <div class="filter-container">
        <el-row :gutter="5" >
          <el-col :span="3" >
            <el-popover placement="bottom"  width="80" trigger="click">
                <div class="trends_btn_wrap">
                    <el-checkbox-group v-model="checkedOptions" @change="handleCheckedData" >
                        <el-checkbox
                          v-for="item in headerOptions" :key="item.label" :label="item" :value="item.value" :checked="item.checked" class="checkbox-line"
                        ><p style="font-size: 12px">{{item.label}}</p></el-checkbox>
                    </el-checkbox-group>
                </div>
              <el-button slot="reference" size="small"><i class="el-icon-arrow-down">column</i></el-button>
            </el-popover>
          </el-col>
          <el-col :span="2"  :offset="7" style="margin-top: 15px;" >
            <span style="margin: 0 6px 0 6px;float:right">Hide OVCs</span>
            <el-checkbox v-model="filter.hide_ovc" style="float:right;margin-left:10%"  @change="filter_vm_list"/>
          </el-col>
          <el-col :span="4"  >
            <el-select    size="large"
              v-model="filter.selected_vhost" multiple collapse-tags placeholder="Filter the HOST" style="width:100%;" filterable>
  
              <el-option v-for="item in filter.pc_list" :key="item" :label="item" :value="item" style="font-size: large;"/>
  
            </el-select>
          </el-col>
          <el-col :span="4" >
            <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " @keyup.enter.native="filter_vm_list" size="large"/>
          </el-col>
          <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item"  type="success" size="large" @click="download_vm_list">
            Download
          </el-button>
        </el-col>
          <el-col :span="2" style='float:right;'>
            <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="filter_vm_list">
              Search
            </el-button>
          </el-col>
         
        
      </el-row>
      </div>
      <el-table :key="tableKey" 
                v-loading="listLoading" 
                :data="current_list" 
                border 
                fit 
                highlight-current-row 
                style="width: 100%;" 
                @sort-change="sortChange"  >

        <el-table-column label="Name" prop="name" align="center" min-width="10%" sortable="custom" v-if="isShowForm.name">
          <template slot-scope="{row}">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>
  
        <el-table-column label="Host Name" class-name="status-col" min-width="10%" align="center" sortable="custom" prop="vmhost_name" v-if="isShowForm.vmhost_name">
          <template slot-scope="{row}">
            <span >{{ row.vmhost_name.toUpperCase() }}</span>
          </template>
        </el-table-column>
  
        <el-table-column label="Guest OS" min-width="10%" align="center"  sortable="custom" prop="guest_id" >
          <template slot-scope="{row}">
            <span >{{ row.guest_id.toUpperCase() }}</span>
          </template>
        </el-table-column>

        <el-table-column label="RAM" min-width="4%" align="center" sortable="custom" prop="memory_total_gb" v-if="isShowForm.memory_total_gb">
          <template slot-scope="{row}">
            <span >{{ row.memory_total_gb }}</span>
          </template>
        </el-table-column>
  
        <el-table-column label="CPU" align="center" min-width="4%" sortable="custom" prop="num_cpu" >
          <template slot-scope="{row}">
            <span >{{ row.num_cpu }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Used SpaceGB" min-width="4%" align="center"  sortable="custom" prop="used_space_gb" >
          <template slot-scope="{row}">
            <span>{{ row.used_space_gb }}</span>
          </template>
        </el-table-column>

        <el-table-column label="ProvisionedGB" min-width="4%" align="center"  sortable="custom" prop="provisioned_space_gb" >
          <template slot-scope="{row}">
            <span>{{ row.provisioned_space_gb }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" /> 
  
    </div>
  </template>
  
  <script>
  import {GetSLIVMList} from '@/api/simplivity'
  import waves from '@/directive/waves' // waves directive
  import { parseTime } from '@/utils'
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination
  
  export default {
    name: 'PETable',
    components: { Pagination },
    directives: { waves },
    filters: {
  
    },
    data() {
      return {
        tableKey: 0,
        all_vm_list: null,
        filtered_list: null,
        current_list: null,
        page_list: null,
        filter:{
          pc_list:[],
          selected_vhost:[],
          fuzzy_string: "",
          hide_ovc: false
        },
        listLoading: true,
        listQuery: {
          page: 1,
          limit: 20,
          cluster: '',
          prism: '',
          status: '',
          sort: '+id'
        },
        menuVisible: false,
        isShowForm: {
          name: true,
          vmhost_name: true,
          memory_total_gb: true
        },
        checkedOptions: [],
        headerOptions: [
          {
          label: 'Name',
          value: 'name'
          },
          {
          label: 'Host Name',
          value: 'vmhost_name'
          },
          {
          label: 'RAM',
          value: 'memory_total_gb'
        }],
      }
    },
    computed: {
      total() {
        if(this.filtered_list){
          return this.filtered_list.length
        }
        else{
            return 0
        }
      }
    },
    created() {
      this.get_vm_list();
    },

    methods: {
        handleCheckedData(val) {
          const name = val.find((item)=> item.value =="name")
          const vmhost_name = val.find((item)=> item.value =="vmhost_name")
          const memory_total_gb = val.find((item)=> item.value =="memory_total_gb")
          this.isShowForm.name= name ? 1 : 0
          this.isShowForm.vmhost_name = vmhost_name ? 1 : 0
          this.isShowForm.memory_total_gb = memory_total_gb ? 1 : 0
        },
      showPinel() {
      this.menuVisible = !this.menuVisible
    },
      get_vm_list() {
        this.listLoading = true
        GetSLIVMList(this.$store.getters.token).then(response => {
          this.all_vm_list = response.data.map((e)=>{
          e.memory_total_gb = parseInt(e.memory_total_gb)
          e.num_cpu = parseInt(e.num_cpu)
          e.used_space_gb = parseInt(e.used_space_gb)
          e.provisioned_space_gb = parseInt(e.provisioned_space_gb)
          return e
        })
          this.filtered_list = this.all_vm_list
          let page = this.listQuery.page
          let limit = this.listQuery.limit
          let start , end
          if(page*limit>=this.total){
            start = (page-1)*limit
            end = this.total
          }
          else{
            start = (page-1)*limit
            end = page * limit
          }
          this.current_list = this.filtered_list.slice(start,end)
          this.listLoading = false
          let all_prism_list = this.all_vm_list.map((obj,index)=>{return obj['vmhost_name']})
          this.filter.pc_list = this.remove_duplicate(all_prism_list)
        })
        
      },
      remove_duplicate(arr) {
        //去除重复值
        const newArr = []
        arr.forEach(item => {
          if (!newArr.includes(item)) {
            newArr.push(item)
          }
        })
        return newArr
      },
      set_page(){
        // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total 
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)
      },
      filter_vm_list(){
        //根据过滤条件筛选表格显示内容
        //screen the table as per filters
        this.listQuery.page = 1
        let temp_list
        //filter selected pc first.
        if (this.filter.selected_vhost.length){
          //No filter, so select all
          temp_list = this.all_vm_list.filter((item)=>{
            return this.filter.selected_vhost.includes(item['vmhost_name'].toLowerCase())
          })
          this.filtered_list = temp_list
        }
        else{
          this.filtered_list = this.all_vm_list
        }
        //filter if only cvm
        if (this.filter.hide_ovc) {
        let temp_list = this.filtered_list
        temp_list = temp_list.filter((item) => {
          let fuzzy = "OmniStackVC".toLowerCase()
          if (item.name.toLowerCase().search(fuzzy) == -1) {
            return true
          }
        })
          this.filtered_list = temp_list
        }
  
        if(this.filter.fuzzy_string.trim().length){
          let temp_list = this.filtered_list
          let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
              for(let fuzzy of fuzzy_list){
                  fuzzy = fuzzy.toString().toLowerCase()
                  temp_list = temp_list.filter((k) => {
                if(  k.name.toLowerCase().search(fuzzy) != -1
                    || k.guest_id.toLowerCase().search(fuzzy) != -1
                    || k.vmhost_name.toLowerCase().search(fuzzy) != -1                     
                ){
                  return true
                }
              })
  
              }
          this.filtered_list = temp_list
        }
        this.set_page()
      },
      sortChange(data) {
        const { prop, order } = data
        if(order==null){
          this.sortChange({prop:'id',order:'ascending'})
          return 
        }
        let flag_num = order=="ascending" ? 1 : -1
        this.filtered_list.sort((item1,item2)=>(
          (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
        ))
        this.set_page()
      },
      formatJson(filterVal) {
        return this.list.map(v => filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        }))
      },
      download_vm_list(){
        GetSLIVMList(this.$store.getters.token,  true)
        .then((response)=>{
          const href = URL.createObjectURL(response.data);
          // create "a" HTML element with href to file & click
          const link = document.createElement('a');
          link.href = href;
          link.setAttribute('download', "slivm_list"); //or any other extension
          document.body.appendChild(link);
          link.click();
          // clean up "a" element & remove ObjectURL
          document.body.removeChild(link);
          URL.revokeObjectURL(href);
        })
      },
    }
  }
  </script>
  <style lang="scss" scoped>
      .bigger_font {
        font-size: 16px;
      }
  </style>
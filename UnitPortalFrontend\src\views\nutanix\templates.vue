<template>
    <div class="app-container">
      <div class="filter-container">
        <el-row :gutter="5" >
          <el-col :span="3" >
            <el-button class="filter-item"  type="primary" @click="handle_create">
              Create
            </el-button>
          </el-col>
          <el-col :span="4" :offset="17" >
            <el-button style='float:right' class="filter-item"  type="danger" >
              Delete
            </el-button>
          </el-col>
        </el-row>
      </div>
  
      <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="templatelist"
        border
        fit
        highlight-current-row
        style="width: 100%;"
        @sort-change="sortChange"
        @row-click="handle_row_click"
        class='template-table'
        ref='pctable'
      >
  
        <el-table-column label="ID" min-width="3%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Name" class-name="status-col" min-width="10%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Workload Type" min-width="5%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.workload_type }}</span>
          </template>
        </el-table-column>
        <el-table-column label="CPU" align="center" min-width="4%">
          <template slot-scope="{row}">
            <span>{{ row.cpu }}</span>
          </template>
        </el-table-column>
        <el-table-column label="CPU core" min-width="4%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.cpu_core }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Disk" class-name="status-col" min-width="7%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.disk }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Image" class-name="status-col" min-width="7%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.image_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Boot Mode" min-width="6%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.boot_mode }}</span>
          </template>
        </el-table-column>
        <el-table-column  label="VlanID" min-width="5%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.vlan_id}}</span>
          </template>
        </el-table-column>
        <el-table-column  label="Naming convention" min-width="15%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.naming_convention}}</span>
          </template>
        </el-table-column>
      </el-table>
  
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="get_prism_list" />
  
      <el-dialog :title="'Add a prism central'" :visible.sync="dialogFormVisible" style="width:1500px;margin-left:12%">
        <el-form ref="dataForm" :rules="rules" :model="addprismform" label-position="left" label-width="125px" style="width: 600px; margin-left:50px;">
          <el-form-item label="FQDN" prop="fqdn" >
            <el-input  v-model=addprismform.fqdn style="width:60%">
  
            </el-input>
          </el-form-item>
          <el-form-item label="Tier" prop="tier" >
            <el-select v-model="addprismform.tier" class="filter-item" placeholder="Please select" style="width:60%" >
              <el-option v-for="key,item of prismtier" :key="key" :label="key" :value="key" />
            </el-select>
          </el-form-item>
          <el-form-item label="Service Account" >
            <el-radio style="margin-left: 20px;" v-model="addprismform.newserviceaccount" label="1"  >Select from the list</el-radio>
            <el-radio v-model="addprismform.newserviceaccount" label="2" >Use a new account.</el-radio>
          </el-form-item>
          <el-form-item label="Account List" v-show="addprismform.newserviceaccount == '1'"> 
            <el-select v-model="addprismform.existingserviceaccount" class="filter-item" placeholder="Please select"  style="width:60%">
              <el-option v-for="item in serviceaccountlist" :key="item.id" :label="item.username" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="User Name" v-show="addprismform.newserviceaccount == '2'"  style="width:70%" > 
            <el-input v-model="addprismform.serviceaccountname" placeholder="Please input"/>
          </el-form-item>
          <el-form-item label="Password" v-show="addprismform.newserviceaccount == '2'"   style="width:70%">
            <el-input  v-model=addprismform.serviceaccountpassword :type="addprismform.passwordType" placeholder="Please input"/>
            <span class="show-pwd" @click="showPwd" >
              <svg-icon :icon-class="addprismform.passwordType === 'password' ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="add_prism()">
            Confirm
          </el-button>
        </div>
      </el-dialog>

    </div>
  </template>
  
  <script>
  import {GetTemplateList, AddPrism} from '@/api/nutanix'
  import {GetSAList} from '@/api/common'
  import {CreateNTXPM, UpdateNTXPM, GetPMTaskList,AbortNTXPM,DeleteNTXPM,DownloadNTXPMLog} from '@/api/nutanixpm'
  import waves from '@/directive/waves' // waves directive
  import { parseTime } from '@/utils'
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination
  
  const prismoptions = [
    { key: 'EU', display_name: 'SSP-EU-NTX.IKEA.COM' },
    { key: 'CN', display_name: 'SSP-CHINA-NTX.IKEA.COM' },
    { key: 'APAC', display_name: 'SSP-APAC-NTX.IKEA.COM' },
    { key: 'NA', display_name: 'SSP-NA-NTX.IKEA.COM' },
    { key: 'RU', display_name: 'SSP-RUSSIA-NTX.IKEA.COM' },
    { key: 'DT', display_name: 'SSP-DT-NTX.IKEADT.COM' },
    { key: 'PPE', display_name: 'SSP-PPE-NTX.IKEA.COM' },
  ]
  const peoptions =[
    { pc: 'SSP-EU-NTX.IKEA.COM', pe:['RETDE068-NXC000.ikea.com','RETSEHBG-NXC000.ikea.com','RETDE124-NXC000.ikea.com','RETFR134-NXC000.ikea.com'] },
    { pc: 'SSP-CHINA-NTX.IKEA.COM', pe:['RETCN856-NXC000.ikea.com','RETCNCHN-NXC000.ikea.com','RETCN644-NXC000.ikea.com','RETCNSOS-NXC000.ikea.com'] },
    { pc: 'SSP-APAC-NTX.IKEA.COM', pe:['RETKR373-NXC000.ikea.com','RETJP509-NXC000.ikea.com','RETKR522-NXC000.ikea.com','RETKRSO-NXC000.ikea.com'] },
    { pc: 'SSP-NA-NTX.IKEA.COM', pe:['RETUS100-NXC000.ikea.com','RETCA040-NXC000.ikea.com','RETUS209-NXC000.ikea.com','RETUS374-NXC000.ikea.com'] },
    { pc: 'SSP-RUSSIA-NTX.IKEA.COM', pe:['RETRU401','RETRU403','RETRU551','RETRU513'] },
    { pc: 'SSP-DT-NTX.IKEADT.COM', pe:['RETSEELM-NXC000.ikea.com'] },
    { pc: 'SSP-PPE-NTX.IKEA.COM', pe:['RETSE999-NXC000.ikea.com','RETCN888-NXC000.ikea.com'] },
  ]
  // arr to obj, such as { CN : "China", US : "USA" }
  const calendarTypeKeyValue = prismoptions.reduce((acc, cur) => {
    acc[cur.key] = cur.display_name
    return acc
  }, {})
  
  const peTypeKeyValue = peoptions.reduce((acc, cur) => {
    acc[cur.pc] = cur.pe
    return acc
  }, {})
  export default {
    name: 'PrismTable',
    components: { Pagination },
    directives: { waves },
    filters: {
      statusFilter(status) {
        let st = status.toLowerCase();
        const statusMap = {
          'not started': 'info',
          'done': 'success',
          'error': 'danger',
          'in progress':'primary'
        }
        return statusMap[st]
      },
      typeFilter(type) {
        return calendarTypeKeyValue[type]
      }
    },
    data() {
      const validateTime =(rule, value, callback)=>{
        if(this.temp.datatimepickerdisabled){
          callback()
        }
        let currentdate = new Date()
        let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
        if (value < utctime){
          callback(new Error('Schedule date must be later then now.'))
        }else{
          let currnettime = utctime.getTime()
          let scheduletime = value.getTime()
          let timediff = scheduletime-currnettime
          if(timediff/1000/60 < 5){
            callback(new Error('Schedule date is too close from now.'))
          }else{
            callback()
          }
        }
        callback()
      }
      return {
        prismtier:[
          'Production',
          'PreProduction',
          'IKEADT',
          'IKEAD2'
        ],
        addprismform:{
          fqdn: '',
          serviceaccountname:'',
          serviceaccountpassword:'',
          tier:'',
          existingserviceaccount:'',
          newserviceaccount:'1',
          passwordType:'password',
        },
        serviceaccountlist:[],
        tableKey: 0,
        list: null,
        templatelist : null,
        prismlist: null,
        total: 0,
        listLoading: true,
        listQuery: {
          page: 1,
          limit: 20,
          cluster: '',
          prism: '',
          status: '',
          sort: '+id'
        },
        statusToShowEditButton:['Not Started'],
        statusToShowAbortButton:['In Progress'],
        statusToShowDeleteButton:['Not Started','Done','Error','Aborted'],
        statusOptions: ['Not Started','In Progress','Done','Error','Aborted'],
        prismoptions,
        peTypeKeyValue,
        sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
        // statusOptions: ['published', 'draft', 'deleted'],
        ShowCreationDate: false,
        temp: {
          id: '',
          fqdn: '',
          tier:'',
          timestamp: new Date(),
          cluster:'',
          prism: '',
          status: '',
          startnow: 1 ,
          datatimepickerdisabled:false,
          description: '',
          pmtype: 1,
          newsa:1,//flag for if use new service account or not, 1 means use new one, 0 means select from current list.
        },
        selectedrow:'',
        dialogFormVisible: false,
        dialogPvVisible: false,
        logdata: [],
        rules: {
          fqdn: { required: true, message: 'FQDN is required', trigger: 'change' },
          tier: { required: true, message: 'Tier is required', trigger: 'change' },
          // cluster: { required: true, message: 'cluster is required', trigger: 'change' },
          // timestamp: { type: 'date', required: true , trigger: 'change' , validator:validateTime}
        },
        downloadLoading: false,
        intervaljob:''
      }
    },
    created() {
      this.get_template_list()
      this.get_prism_list()
      this.get_sa_list()
    },
    methods: {      
      get_template_list(){
        console.log("getting template list.")
        this.listLoading = true
        GetTemplateList(this.$store.getters.token).then(response => {
          console.log("response",response)
          this.templatelist = response.data
          this.total = response.data.length
          this.listLoading = false
        })
      },
      get_prism_list(){
        console.log("getting prismlist.")
        this.listLoading = true
        GetPrismList(this.$store.getters.token).then(response => {
          console.log("response",response)
          this.prismlist = response.data
          this.total = response.data.length
          this.listLoading = false
        })
      },
      get_sa_list(){
        console.log("getting service account list.")
        GetSAList(this.$store.getters.token).then(response => {
          this.serviceaccountlist = response.data
        })
      },
      handleFilter() {
        this.listQuery.page = 1
      },
      sortChange(data) {
        const { prop, order } = data
        if (prop === 'id') {
          this.sortByID(order)
        }
      },
      sortByID(order) {
        if (order === 'ascending') {
          this.listQuery.sort = '+id'
        } else {
          this.listQuery.sort = '-id'
        }
        this.handleFilter()
      },
      resetTemp() {
        let localtime = new Date()
        let utctime =new Date( localtime.getTime() + 60*1000*localtime.getTimezoneOffset())
        this.temp = {
          id: undefined,
          timestamp: utctime,
          status: 'published',
          type: '',
          prism: '',
          startnow: "1" ,
          pmtype: "1" ,
          newsa: "1",
          datatimepickerdisabled: false
        }
      },
      handle_create() {
        this.resetTemp()
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      handle_delete(){
        console.log( this.selectedrow)
         this.$confirm('You sure??', '?!', {
            confirmButtonText: 'YES',
            cancelButtonText: 'NO',
            type: 'danger'
          }).then(() => {
            this.$message({
              type: 'success',
              message: 'I am not gonna let you do this....'
            });
          }).catch(() => {
            this.$message({
              type: 'info',
              message: 'I am not gonna let you do this....'
            });          
          });
      },
      handle_row_click(row,column,event){
        this.selectedrow = row
      },
      handleAbort(row){
        let payload = {
          data:row,
          token: this.$store.getters.token
        }
        AbortNTXPM(payload).then(() => {
              this.$notify({
                title: 'Success',
                message: 'PM aborted',
                type: 'info',
                duration: 2000
              })
            }
        )
        .catch((error)=>{
          this.$notify({
                title: 'Error',
                message: error.response.data.message,
                type: 'error',
                duration: 2000
            })
        })
      },
      handleDelete(row){
        let payload = {
          data:{id:row.id},
          token: this.$store.getters.token
        }
        DeleteNTXPM(payload).then(()=>{
          this.$notify({
                title: 'Success',
                message: 'PM task was deleted.',
                type: 'success',
                duration: 2000
              })
        })
        .catch((error)=>{
          this.$notify({
                title: 'Failed',
                message: error.response.data.message,
                type: 'error',
                duration: 2000
              })
        })
      },
      createPM() {
        let dt = this.temp.timestamp
        let scheduledate =new Date( dt.getTime() - 60*1000*dt.getTimezoneOffset()) // add timezone math 
        let payload = {
          data:{        
            "prism": this.temp.prism,
            "cluster": this.temp.cluster,
            "startnow": this.temp.datatimepickerdisabled,
            "scheduledate": scheduledate,
            "pmtype": this.temp.pmtype==1?"poweroff":"poweron",
            "description": this.temp.description ?this.temp.description:"webpage entrance"
          },
          token: this.$store.getters.token
        }
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            CreateNTXPM(payload)
            .then(() => {
              this.$notify({
                title: 'Success',
                message: 'PM Created Successfully',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
            })
            .catch((error) => {
              this.dialogFormVisible  = false
              this.$notify({
                title: 'Error',
                message: 'Failed to create PM.',
                type: 'error',
                duration: 2000
              })
            })
          }
        })
        return 0
      },
      handleUpdate(row) {
        this.temp.prism = row.prism
        this.temp.id = row.id
        this.temp.cluster = row.cluster
        this.temp.description =  row.description
        console.log(row.pmtype=='poweroff')
        this.temp.pmtype =  row.pmtype=='poweroff'?'1':'2'
        if (row.startdate ==='startnow'){
          this.temp.timestamp  = new Date()
          this.temp.datatimepickerdisabled = true
          this.temp.startnow = '2'
        }
        else{
          let dt = new Date(row.startdate)
          this.temp.timestamp = new Date(dt.getTime() + 60*1000*dt.getTimezoneOffset())
          this.temp.datatimepickerdisabled = false
          this.temp.startnow = '1'
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      updatePM() {
        let dt = this.temp.timestamp
        let scheduledate =new Date( dt.getTime() - 60*1000*dt.getTimezoneOffset()) // add timezone math 
        let payload = {
          data:{        
            "id": this.temp.id,
            "prism": this.temp.prism,
            "cluster": this.temp.cluster,
            "startdate":this.temp.datatimepickerdisabled?'startnow':scheduledate,
            "pmtype": this.temp.pmtype==1?"poweroff":"poweron",
            "description": this.temp.description ?this.temp.description:"webpage entrance"
          },
          token: this.$store.getters.token
        }
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            UpdateNTXPM(payload)
            .then(() => {
              this.$notify({
                title: 'Success',
                message: ' PM Updated Successfully',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
            })
            .catch((error) => {
              console.log(error)
              console.log(error.response)
              this.dialogFormVisible = false
              this.$notify({
                title: 'Error',
                message: error.response.data.message,
                type: 'error',
                duration: 2000
              })
            })
          }
        })
      },
      downloadLogFile(){
        if (!this.selectedrow.detaillogpath){
          this.$notify({
                title: 'Ooooops',
                message: 'No log yet!',
                type: 'info',
                duration: 2000
              })
        }
        let payload = {
          data:{  id:this.selectedrow.id,
                  filepath:this.selectedrow.detaillogpath},
          token: this.$store.getters.token
        }
        DownloadNTXPMLog(payload)
        .then((response)=>{
          const href = URL.createObjectURL(response.data);
          // create "a" HTML element with href to file & click
          const link = document.createElement('a');
          link.href = href;
          link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
          document.body.appendChild(link);
          link.click();
          // clean up "a" element & remove ObjectURL
          document.body.removeChild(link);
          URL.revokeObjectURL(href);
        })
      },
      handleFetchBriefLog(row) {
        this.selectedrow = row
        this.logdata = row.logs
        this.dialogPvVisible = true
      },
      formatJson(filterVal) {
        return this.list.map(v => filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        }))
      },
      getSortClass: function(key) {
        const sort = this.listQuery.sort
        return sort === `+${key}` ? 'ascending' : 'descending'
      },
      showPwd() {
        if (this.addprismform.passwordType === 'password') {
          this.addprismform.passwordType = ''
        } else {
          this.addprismform.passwordType = 'password'
        }
        this.$nextTick(() => {
          this.$refs.addprismform.serviceaccountpassword.focus()
        })
      },
      add_prism(){
        let newsa = this.addprismform.newserviceaccount == '1'? false : true
        let sausername, sapassword
        if(newsa){
          sausername = this.addprismform.serviceaccountname
          sapassword = this.addprismform.serviceaccountpassword
        }
        let payload = {
          data:{        
            "fqdn": this.addprismform.fqdn,
            "tier": this.addprismform.tier,
            "newsa": newsa,
            "currentsa":this.addprismform.existingserviceaccount,
            "sausername":sausername,
            "sapassword":sapassword
          },
          token: this.$store.getters.token
        }
        console.log(payload)
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            AddPrism(payload)
            .then(() => {
              this.$notify({
                title: 'Success',
                message: 'Prism added.',
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.get_prism_list()
            })
            .catch((error) => {
              this.dialogFormVisible  = false
              this.$notify({
                title: 'Error',
                message: 'Failed to add Prism.',
                type: 'error',
                duration: 2000
              })
            })
          }
        })
        return 0
      }
    },
    beforeDestroy(){
      clearInterval( this.intervaljob )
    }
  }
  </script>
  <style scoped>
   .template-table span{
    
       font-size: 17px
  }
  .show-pwd {
      position: absolute;
      right: 10px;
      top: 7px;
      font-size: 16px;
      cursor: pointer;
      user-select: none;
    }
  </style>
import json
from sqlalchemy import func, literal
import werkzeug.exceptions as flaskex
import sqlalchemy.exc
# local file
from models.benchmark_models import ModelNtxBenchmark, ModelNtxBenchmarkSchema, \
                                    model_benchmark_mapping, model_schema_benchmark_mapping
from models.ntx_models import ModelPrismElement
from models.ntx_models_wh import ModelWarehousePrismElement
from business.generic.base_up_exception import BenchmarkGetFailed
from models.database import db


class Benchmark():
    def __init__(self):
        """
        Initializes the benchmark object by loading and serializing benchmark data.

        This constructor performs the following:    
        - Serializes all records from the `ModelNtxBenchmark` model using the `ModelNtxBenchmarkSchema`.
        - Assigns the serialized data to the `self.bmk` attribute.
        - Iterates through the `sub_bmk_names` attribute of the `ModelNtxBenchmark` model.
        - For each sub-benchmark name:
            - Retrieves the corresponding schema and model from `model_schema_benchmark_mapping` 
              and `model_benchmark_mapping`.
            - Serializes all records of the sub-benchmark model.
            - Dynamically sets an attribute on the instance with the name of the sub-benchmark 
              and assigns the serialized data to it.
        """
        bmk_schema = ModelNtxBenchmarkSchema(many=True)
        self.bmk = bmk_schema.dump(ModelNtxBenchmark.query.all())
        for sub_bmk_name in ModelNtxBenchmark().sub_bmk_names:
            sub_bmk_schema = model_schema_benchmark_mapping[sub_bmk_name](many=True)
            sub_bmk_model = model_benchmark_mapping[sub_bmk_name]()
            setattr(self, sub_bmk_name, sub_bmk_schema.dump(sub_bmk_model.query.all()))

    def get_bmk_list(self, is_full=True, **filters):
        """
        Retrieves a list of benchmarks (bmk) based on the provided filters and options.

        Args:
            is_full (bool, optional): Determines whether to include detailed benchmark information.
                - If True, retrieves benchmarks with additional sub-information.
                - If False, retrieves only the filtered benchmarks. Defaults to True.
            **filters: Arbitrary keyword arguments representing the filter criteria to apply.
                Each key-value pair is matched against the benchmark attributes.
                Accepted keys include 'id', 'name', 'facility', 'tier', and 'arch'.

        Returns:
            list: A list of benchmarks. If `is_full` is True, the list contains benchmarks
            with additional sub-information; otherwise, it contains only the filtered benchmarks.
        """
        def matches_filters(bmk, filters):
            for key, value in filters.items():
                if key in bmk and bmk[key] != value:
                    return False
            return True
        filtered_bmks = [bmk for bmk in self.bmk if matches_filters(bmk, filters)]
        if not is_full:
            return filtered_bmks
        bmk_with_sub_list = list()
        for _bmk in filtered_bmks:
            bmk_with_sub = self.get_bmk_by_id(bmk_id=_bmk['id'], real_fw_rules=True)
            bmk_with_sub_list.append(bmk_with_sub)
        return bmk_with_sub_list

    def get_bmk_by_id(self, bmk_id, real_fw_rules=False):
        bmk = next((item for item in self.bmk if item['id'] == bmk_id), None)
        if not bmk:
            raise BenchmarkGetFailed(bmk_id=bmk_id)
        res = {
            "id" : bmk['id'],
            "name" : bmk['name'],
            "facility" : bmk['facility'],
            "tier" : bmk['tier'],
            "arch" : bmk['arch'],
        }
        for _sub in ModelNtxBenchmark().sub_bmk_names:  # Use the sub benchmark names from the initialized object
            sub_bmk = None
            sub_bmk_data = getattr(self, _sub, None)  # Retrieve preloaded sub benchmark data from __init__
            if sub_bmk_id := bmk.get(_sub):  # If sub bmk id is not None, find the corresponding sub benchmark
                sub_bmk = next((item for item in sub_bmk_data if item['id'] == sub_bmk_id), None)
            if (not sub_bmk) or (not sub_bmk_id):
                res[_sub.split("bmk_")[1]] = None
                continue
            _dict_json = {}
            for _k in sub_bmk.keys():  # Remove the id field and process JSON fields
                if "_json" in _k:
                    _dict_json[_k.split("_json")[0]] = None
                    if sub_bmk[_k]:
                        _dict_json[_k.split("_json")[0]] = json.loads(sub_bmk[_k])
            res[_sub.split("bmk_")[1]] = sub_bmk | _dict_json
        if real_fw_rules:
            _rules = []
            for _rule in res['firewall_rules']['rules']:
                keys = _rule['endpoint']
                destinations = []
                _res = res
                for _k in keys:
                    _res = _res.get(_k)
                if type(_res) == str:
                    destinations.append(_res)
                if type(_res) == list:
                    destinations = _res
                for _d in destinations:
                    for _s in _rule['services']:
                        _rules.append({
                            "subject"       : _rule['subject'],
                            "source"        : _rule['source'],
                            "destination"   : _d,
                            "port"          : _s['port'],
                            "is_udp"        : _s['is_udp'],
                            "is_mandatory"  : _rule['is_mandatory']
                        })
            res['firewall_rules']['rules'] = _rules
        return res

    def get_bmk_list_with_vlan_config(self):
        """
        Retrieves a list of benchmark (BMK) configurations along with their associated VLAN configurations.

        This method iterates through the list of benchmarks (`self.bmk`) and matches each benchmark
        with its corresponding VLAN configuration from `self.bmk_vlan_config` (if available). The resulting
        list contains dictionaries with benchmark details and VLAN configuration details.

        Returns:
            list: A list of dictionaries, where each dictionary contains the following keys:
                - "id" (str): The ID of the benchmark.
                - "name" (str): The name of the benchmark.
                - "facility" (str): The facility associated with the benchmark.
                - "arch" (str): The architecture of the benchmark.
                - "tier" (str): The tier of the benchmark.
                - "oob_vlan" (str or None): The out-of-band VLAN configuration, if available.
                - "ahv_cvm_vlan" (str or None): The AHV CVM VLAN configuration, if available.
                - "pc_vlan" (str or None): The PC VLAN configuration, if available.
        """
        result = []
        for bmk in self.bmk:
            vlan_config = next(
                (vlan for vlan in getattr(self, 'bmk_vlan_config', []) if vlan['id'] == bmk.get('bmk_vlan_config')),
                None
            )
            result.append({
                "id": bmk['id'],
                "name": bmk['name'],
                "facility": bmk['facility'],
                "arch": bmk['arch'],
                "tier": bmk['tier'],
                "oob_vlan": vlan_config['oob_vlan'] if vlan_config else None,
                "ahv_cvm_vlan": vlan_config['ahv_cvm_vlan'] if vlan_config else None,
                "pc_vlan": vlan_config['pc_vlan'] if vlan_config else None
            })
        return result

    @staticmethod
    def get_bmk_brief_list():
        """
        Retrieves a brief list of benchmark 

        This method iterates through the list of benchmarks (`self.bmk`) and constructs a brief representation
        of each benchmark. The resulting list contains dictionaries with benchmark details.

        Returns:
            list: A list of dictionaries, where each dictionary contains the following keys:
                - "id" (str): The ID of the benchmark.
                - "name" (str): The name of the benchmark.
                - "facility" (str): The facility associated with the benchmark.
                - "arch" (str): The architecture of the benchmark.
                - "tier" (str): The tier of the benchmark.
                - "linked_cluster": The number of the clusters that are using this benchmark.
        """
        bmk =  ModelNtxBenchmarkSchema(many=True).dump(ModelNtxBenchmark.query.all())
        ret_relation = db.session.query(func.count(ModelPrismElement.id), ModelPrismElement.bmk_id).group_by(ModelPrismElement.bmk_id).all()
        #ret_relation [(17, None), (33, 1), (41, 2),], first item is the count of the cluster, second is the bmk_id
        #both of them are int.
        wh_relation = db.session.query(func.count(ModelWarehousePrismElement.id), ModelWarehousePrismElement.bmk_id).group_by(ModelWarehousePrismElement.bmk_id).all()
        _map = {}
        for _r in [r for r in (ret_relation + wh_relation) if r[1]]:
            if _r[1] not in _map.keys():
                _map[_r[1]] = _r[0]
            else:
                _map[_r[1]] += _r[0]
        #process the relation list,
        #_map will be like {1: 33, 2: 41}, bmk_id: cluster_number
        res = []
        for _bmk in bmk:
            res.append({
                "id" : _bmk['id'],
                "name" : _bmk['name'],
                "facility" : _bmk['facility'],
                "arch" : _bmk['arch'],
                "tier" : _bmk['tier'],
                "linked_cluster": 0 if _bmk['id'] not in _map.keys() else _map[_bmk['id']]
            })
        return sorted(res, key=lambda x: x['linked_cluster'], reverse=True)
    
    @staticmethod
    def get_bmk_backbone():
        """
        Retrieves the benchmark structure:

        Returns:
            dict: A dict that contains the following keys:
                - all the sub benchmark
                  - sub benchmark keys
        """
        backbone_structure = []
        for sub_bmk_name, sub_bmk_cls in model_benchmark_mapping.items():
            backbone_structure.append(
                {
                    "label": sub_bmk_name.replace("bmk_", ""),
                    "children": [{"label": _key} for _key in sub_bmk_cls.__table__.columns.keys()]
                }
            )
        return backbone_structure

    @staticmethod
    def get_beautified_bmk_by_id(bmk_id):
        """
        Retrieves the benchmark structure with real data.

        Args:
            bmk_id (int): The ID of the benchmark to retrieve.

        Returns:
            dict: A dict that contains the following keys:
                - id, name, facility, tier, arch of the master benchmark
                - all the sub benchmark
                  - sub benchmark keys
        """
        sub_models = [_mod for _ , _mod in model_benchmark_mapping.items()]
        base_query = db.session.query(ModelNtxBenchmark, *sub_models).filter(ModelNtxBenchmark.id == bmk_id)
        for bmk_name, bmk_model in model_benchmark_mapping.items():
            #apply a inner join to all the sub benchmarks
            base_query = base_query.join(bmk_model, getattr(ModelNtxBenchmark, bmk_name) == getattr(bmk_model, 'id'))
        try:
            bmk = base_query.one()
        except sqlalchemy.exc.NoResultFound as e:
            raise sqlalchemy.exc.NoResultFound(f"One of the sub benchmark of this main benchmark {bmk_id} was not found. Error message: {e}")
        # bmk is the result of the query, it's the benchmark and all of its sub benchmarks
        res = ModelNtxBenchmarkSchema().dump(bmk[0])
        # bmk[0] is always the benchmark, bmk[1] is the first sub benchmark, bmk[2] is the second sub benchmark...
        for _bmk in bmk:
            if isinstance(_bmk, ModelNtxBenchmark):
                continue
            _key = next((bmk_name for bmk_name, bmk_class in model_benchmark_mapping.items() 
                         if isinstance(_bmk, bmk_class)), None)
            # _key is the name of the sub benchmark: bmk_systems bmk_backup etc...
            res[_key] = model_schema_benchmark_mapping[_key]().dump(_bmk)
        return res
    
    @classmethod
    def generate_bmk_frontend_tree(cls, bmk_id):
        """
        Generates a benchmark structure with real data for the frontend tree.

        Args:
            bmk_id (int): The ID of the benchmark to generate.

        Returns:
            dict: A dictionary containing the generated tree with benchmark data.
        """
        _data = cls.get_beautified_bmk_by_id(bmk_id)
        tree_data = []
        # pre-process the data
        def _generate_tree(data: dict):
            temp = []
            for key, value in data.items():
                if isinstance(value, list):
                    if not isinstance(value[0], dict):
                        #if the item of the list is not a dict ... then the tree ends here
                        temp.append({
                            "label": f"{key}:{ ' , '.join([str(v) for v in value])}"
                        })
                        continue
                    #if the item of the list is dicts.. then _json need to have a sub tree
                    temp.append({
                        "label": key,
                        "children": [{
                                        "label": f"{i}", 
                                        "children": _generate_tree(value[i-1])
                                     } 
                                    for i in range(1, len(value)+1)]
                    })
                elif isinstance(value, dict):
                    # if the value is a list, convert it to a dict
                    temp.append({
                        "label": f"{key}",
                        "children": _generate_tree(value)
                    })
                else:
                    #weird, logic shouldn't come in here...

                    temp.append({
                        "label": f"{key}  :  {value}"
                    })
            return temp
        
        for label, value in _data.items():
            if label.startswith("bmk_"):
                # bmk is a node of the tree
                second_level_tree = []
                for second_label, second_value in value.items():
                    if second_label.endswith("_json"):
                        # if end with json, then it's a node of bmk tree
                        if not second_value:
                            # if the value is None, then the tree ends here
                            second_level_tree.append({
                                "label": f"{second_label}  :  None"
                            })
                            continue
                        second_value =  json.loads(second_value)
                        if isinstance(second_value, dict):
                            second_level_tree.append({
                                "label": second_label,
                                "children": _generate_tree(second_value)
                            })
                        elif isinstance(second_value, list):
                            #if the item of the list is not a dict ... then the tree ends here
                            if not isinstance(second_value[0], dict):
                                second_level_tree.append({
                                    "label": f"{second_label}:{ ' , '.join([str(v) for v in second_value])}"
                                })
                                continue
                            #if the item of the list is dicts.. then _json need to have a sub tree
                            second_level_tree.append({
                                "label": second_label,
                                "children": [{
                                                "label": f"{i}", 
                                                "children": _generate_tree(second_value[i-1])
                                             } 
                                            for i in range(1, len(second_value)+1)]
                            })
                    else:
                        second_level_tree.append({
                            "label": f"{second_label}  :  {second_value}"
                        })
                tree_data.append({
                    "label": label,
                    "children": second_level_tree
                    #value here must be a dict, convert it to a string to fit the function
                })
            else:
                tree_data.append({
                    "label": f"{label}  :  {value}"
                })
        return tree_data

    @staticmethod
    def get_linked_cluster(bmk_id):
        ret_query = db.session.query(ModelPrismElement).filter(
            ModelPrismElement.bmk_id == bmk_id).with_entities(
                ModelPrismElement.fqdn, ModelPrismElement.prism, literal('retail').label('facility_type'))
        # a query for retal
        wh_query = db.session.query(ModelWarehousePrismElement).filter(
            ModelWarehousePrismElement.bmk_id == bmk_id).with_entities(
                ModelWarehousePrismElement.fqdn, ModelWarehousePrismElement.prism, literal('warehouse').label('facility_type'))
        # union query 
        return [{"pe": clu[0],
                 "pc": clu[1],
                 "facility_type": clu[2]} 
                for clu in ret_query.union(wh_query).all()]
    
    @staticmethod
    def get_all_cluster():
        ret_query = db.session.query(ModelPrismElement).with_entities(
                ModelPrismElement.fqdn, ModelPrismElement.prism, literal('retail').label('facility_type'))
        # a query for retal
        wh_query = db.session.query(ModelWarehousePrismElement).with_entities(
                ModelWarehousePrismElement.fqdn, ModelWarehousePrismElement.prism, literal('warehouse').label('facility_type'))
        # union query 
        return [{"pe": clu[0],
                 "pc": clu[1],
                 "facility_type": clu[2]} 
                for clu in ret_query.union(wh_query).all()]


    @staticmethod
    def get_single_bmk_detail(bmk_id):
        """
        get sub benchmark label for frontend
        parameter: bmk_id (int)
        return: {
            "bmk_systems":{
                "label_option":[
                    "Retail_AP", "Retail_China", "Retail_EU"
                ],
                "label":"Retail_AP",
                "id":1
            }.....
        }
        """
        def beautify_label(label):
            return label.replace("bmk_", "").replace("_", " ").title()

        bmk = ModelNtxBenchmark.query.filter_by(id=bmk_id).one()
        res = dict()
        try:
            for _sub_bmk_name in ModelNtxBenchmark.sub_bmk_names:
                sub_bmk_model = model_benchmark_mapping[_sub_bmk_name]
                sub_bmk_label = sub_bmk_model.query.with_entities(
                    sub_bmk_model.id,
                    sub_bmk_model.index_label
                ).all()
                res[_sub_bmk_name] = {
                    "label_option": [{"id": label[0], "label": label[1]} for label in sub_bmk_label],
                }
                sub_bmk_entry = sub_bmk_model.query.filter_by(id=getattr(bmk, _sub_bmk_name)).one()
                res[_sub_bmk_name]['id'] = sub_bmk_entry.id
                res[_sub_bmk_name]['selected_label'] = sub_bmk_entry.id
                res[_sub_bmk_name]["frontend_label"] = beautify_label(_sub_bmk_name)
                res[_sub_bmk_name]["icon_label"] = _sub_bmk_name.lower()
            return res
        except sqlalchemy.exc.NoResultFound:
            raise sqlalchemy.exc.NoResultFound(f"Sub benchmark :{_sub_bmk_name} id {getattr(bmk, _sub_bmk_name)} not found")

    @staticmethod
    def update_bmk(bmk_id, detail):
        """
        update the master benchmark.
        1. the sub bmk id in master bmk table
        2. the bmk_id for selected PEs
        """
        bmk = ModelNtxBenchmark.query.filter_by(id=bmk_id).one()
        if not bmk:
            raise sqlalchemy.exc.NoResultFound(f"Benchmark with id {bmk_id} not found.")
        # update the master benchmark
        for key, value in detail['modified_bmk'].items():
            setattr(bmk, key, value)
        for cluster in detail['clusters']:
            (ModelPrismElement if cluster['facility_type'] == 'retail' else ModelWarehousePrismElement).query.filter_by(fqdn=cluster['pe']).filter_by(prism=cluster['pc']).update({
                "bmk_id": bmk.id
            })
        db.session.commit()
        return "benchmark has been updated."
    
    @staticmethod
    def get_sub_bmk_backbone():
        """
        get all the sub benchmark backbone:
        struct:
        sub_bmk_systems:[{"label":"system...","id":1}, {"label":"system_2","id":2},...]
        result is like:
        [
            {
                'label': 'bmk_systems', 
                'children': [
                    {'id': 1, 'label': 'Retail_AP'}, {'id': 2, 'label': 'Retail_China'}...
                ]
            }...
        ]
        """
        sub_bmk_backbone = list()
        for name, model in model_benchmark_mapping.items():
            # only id and index_label 
            sub_bmk_backbone.append({
                "label": name,
                "children": [
                    {"id": row[0], "key": f"{name}_{row[0]}", "label": row[1], "is_child": True}
                    for row in model.query.with_entities(model.id, model.index_label).all()
                ]
            })
        return sub_bmk_backbone

    @staticmethod
    def get_sub_bmk_detail(sub_bmk_type, sub_bmk_id):
        """
        Get the sub benchmark detail.
        return {
            "info": { 
                "type": "bmk_systems",
                "label": "Retail_AP",
                "main_bmk_usage":14,
                "cluster_useage":23
                },
            "detail": {
                details.....
            }
        }
        """
        bmk = model_benchmark_mapping[sub_bmk_type].query.filter_by(id=sub_bmk_id).one()
        model_schema_benchmark_mapping[sub_bmk_type]().dump(bmk)
        info = {
            "type": sub_bmk_type,
            "name": bmk.index_label,
            "main_bmk_usage": ModelNtxBenchmark.query.filter(getattr(ModelNtxBenchmark, sub_bmk_type) == bmk.id).count(),
            "cluster_usage": db.session.query(func.count(ModelPrismElement.id))
                                .join(ModelNtxBenchmark, ModelPrismElement.bmk_id == ModelNtxBenchmark.id)
                                .filter(getattr(ModelNtxBenchmark, sub_bmk_type) == bmk.id)
                                .scalar() +
                             db.session.query(func.count(ModelWarehousePrismElement.id))
                                .join(ModelNtxBenchmark, ModelWarehousePrismElement.bmk_id == ModelNtxBenchmark.id)
                                .filter(getattr(ModelNtxBenchmark, sub_bmk_type) == bmk.id)
                                .scalar()
        }
        return {
            "info": info,
            "detail": model_schema_benchmark_mapping[sub_bmk_type]().dump(bmk)
        }

    @staticmethod
    def update_sub_bmk(sub_bmk_type, sub_bmk_id, data):
        """
        Update the sub benchmark record with the specified type and ID.
        Parameters:
            sub_bmk_type: The sub benchmark type (e.g., 'bmk_systems')
            sub_bmk_id: The sub benchmark ID
            data: The fields and values to update, passed from the frontend (dict)
        """
        model = model_benchmark_mapping[sub_bmk_type]
        if model.query.filter_by(index_label=data['index_label']).all():
            raise flaskex.Conflict(f"Sub benchmark {sub_bmk_type} with index label {data['index_label']} already exists.")

        sub_bmk = model.query.filter_by(id=sub_bmk_id).one_or_none()
        if not sub_bmk:
            raise sqlalchemy.exc.NoResultFound(f"Sub benchmark {sub_bmk_type} id {sub_bmk_id} not found.    ")
        for key, value in data.items():
            setattr(sub_bmk, key, value)
        db.session.commit()
        return {"result": "Done"}
    
    @staticmethod
    def create_sub_bmk(sub_bmk_type, data):
        """
        Create a new sub benchmark record with the specified type.
        Parameters:
            sub_bmk_type: The sub benchmark type (e.g., 'bmk_systems')
        """
        model = model_benchmark_mapping[sub_bmk_type]
        if model.query.filter_by(index_label=data['index_label']).all():
            raise flaskex.Conflict(f"Sub benchmark {sub_bmk_type} with index label {data['index_label']} already exists.")
        sub_bmk = model(**data)
        db.session.add(sub_bmk)
        db.session.commit()
        return {"result": "Done"}
    
    @staticmethod
    def delete_sub_bmk(sub_bmk_type, sub_bmk_id):
        """
        Delete a sub benchmark.
        Parameters:
            sub_bmk_type: The sub benchmark type (e.g., 'bmk_systems')
            sub_bmk_id: The sub benchmark ID
        Returns:
            True if deleted, otherwise raise error.
        """
        model = model_benchmark_mapping[sub_bmk_type]
        sub_bmk = model.query.filter_by(id=sub_bmk_id).one_or_none()
        if not sub_bmk:
            raise flaskex.NotFound(f"Sub benchmark {sub_bmk_type} id {sub_bmk_id} not found.")
        if cluster_usage := db.session.query(func.count(ModelPrismElement.id)) \
                .join(ModelNtxBenchmark, ModelPrismElement.bmk_id == ModelNtxBenchmark.id) \
                .filter(getattr(ModelNtxBenchmark, sub_bmk_type) == sub_bmk_id).scalar() \
                + \
                db.session.query(func.count(ModelWarehousePrismElement.id)) \
                .join(ModelNtxBenchmark, ModelWarehousePrismElement.bmk_id == ModelNtxBenchmark.id) \
                .filter(getattr(ModelNtxBenchmark, sub_bmk_type) == sub_bmk_id).scalar():
            raise flaskex.Conflict(f"Sub benchmark {sub_bmk_type} id {sub_bmk_id} is still in use by {cluster_usage} clusters.")
        db.session.delete(sub_bmk)
        db.session.commit()
        return True
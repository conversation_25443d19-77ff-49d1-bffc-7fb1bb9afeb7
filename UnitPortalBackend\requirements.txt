APScheduler~=3.10.4
alembic==1.11.1
cryptography==41.0.1
Flask==2.3.2
flask_apispec==0.11.4
flask_apscheduler==1.12.4
flask_cors==3.0.10
flask_marshmallow==0.15.0
flask_migrate==4.0.4
Flask_RESTful==0.3.10
flask_sqlalchemy==3.0.5
jinja2==3.1.2
ldap3==2.9.1
marshmallow-sqlalchemy==0.29.0
ntnx-clustermgmt-py-client==4.0.1
ntnx-datapolicies-py-client==4.0.1
ntnx-networking-py-client==4.0.1
ntnx-prism-py-client==4.0.1
ntnx-volumes-py-client==4.0.1
pandas==2.0.3
paramiko==3.2.0
psutil==5.9.5
pycryptodome==3.18.0
pyodbc
python-dateutil==2.9.0.post0
pytz==2023.3
pyvmomi==8.0.1.0.1
#pywin32==306; platform_system=="Windows"
Requests==2.31.0
SQLAlchemy==2.0.17
setproctitle==1.3.3
urllib3==1.26.20
Werkzeug==2.3.6
webargs==8.3.0
apispec~=6.3.0
marshmallow~=3.20.1
xmltodict~=0.13.0
beautifulsoup4==4.12.2
flask_socketio==5.3.6
ping3==4.0.8
numpy<2.0.0
packaging>=23.0
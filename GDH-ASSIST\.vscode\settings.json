{
    "sqltools.connections": [
        {
            "mssqlOptions": {
                "appName": "SQLTools",
                "useUTC": true,
                "encrypt": true
            },
            "previewLimit": 50,
            "server": "*************",
            "port": 1433,
            "driver": "MSSQL",
            "name": "DPCDB",
            "username": "dpcdbadmin",
            "password": "****",
            "database": "DPCDB"
        }
    ],

    // Powershell script analysis settins
    "powershell.scriptAnalysis.settingsPath": "PSScriptAnalyzerSettings.psd1",
    "powershell.scriptAnalysis.enable": true,

    "[powershell]": {
        "editor.tabSize": 4,
        "editor.detectIndentation": false
    }
}
from business.generic.commonfunc import CommonRestCall


class FoundationCentral:
    def __init__(self, endpoint, username, password, logger):
        self.endpoint = f"https://{endpoint}:9440"
        self.rest = CommonRestCall(username=username, password=password, logger=logger, timeout=30)
        self.logger = logger

    def list_imaged_nodes(self):
        kwargs = {
            "url": f"{self.endpoint}/api/fc/v1/imaged_nodes/list",
            "method": "POST",
            "payload": {
                "filters": {}
            }
        }
        res = self.rest.call_restapi(**kwargs)
        return res

    def create_cluster(self, payload):
        kwargs = {
            "url": f"{self.endpoint}/api/fc/v1/imaged_clusters",
            "method": "POST",
            "payload": payload
        }
        res = self.rest.call_restapi(**kwargs)
        return res

    def list_imaged_clusters(self, archived=False):
        kwargs = {
            "url": f"{self.endpoint}/api/fc/v1/imaged_clusters/list",
            "method": "POST",
            "payload": {
                "filters": {
                    "archived": archived
                }
            }
        }
        res = self.rest.call_restapi(**kwargs)
        return res

    def get_cluster_detail(self, uuid):
        self.logger.info(f"Getting cluster detail... uuid: {uuid}")
        kwargs = {
            "url": f"{self.endpoint}/api/fc/v1/imaged_clusters/{uuid}",
            "method": "GET",
            "payload": {}
        }
        res = self.rest.call_restapi(**kwargs)
        return res

    def archive_cluster(self, cluster_uuid, archived=True):
        self.logger.info(f"Updating cluster... Set archive status to: {archived}")
        kwargs = {
            "url": f"{self.endpoint}/api/fc/v1/imaged_clusters/{cluster_uuid}",
            "method": "PUT",
            "payload": {
                "archived": archived
            }
        }
        res = self.rest.call_restapi(**kwargs)
        self.logger.info("Updated cluster completed.")
        return res

    def get_fc_api_keys(self):
        url = f"{self.endpoint}/api/fc/v1/api_keys/list"
        payload = {
            "key": "value"
        }
        fc_api_keys = self.rest.call_restapi(url=url, payload=payload, method='POST').json()
        return fc_api_keys['api_keys'][0]['api_key']
    

    def get_pe_multicluster(self):
        self.logger.info("check current status first")
        url = f"{self.endpoint}/PrismGateway/services/rest/v1/multicluster/cluster_external_state"
        current_status = self.rest.call_restapi(url=url).json()
        if not current_status:
            return False
        return True

    def join_cluster_to_pc(self, pc_data, pc_ip):
        url = f"{self.endpoint}/PrismGateway/services/rest/v1/multicluster/prism_central/register"
        payload = {
            "ipAddresses": [pc_ip],
            "username": pc_data['username'],
            "password": pc_data['secret'],
            "port": 9440
        }
        self.rest.call_restapi(url=url, method='POST', payload=payload)
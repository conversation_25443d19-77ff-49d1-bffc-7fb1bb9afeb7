<template>
    <div class="app-container" >
      <el-card  class="box-card-left">
          <div class="filter-container" style="line-height: 40px;">
            <el-row :gutter="5">
              <el-col :span="3">
                <el-button class="filter-item" type="primary" @click="handle_user_create_dialog">
                  Add a new User
                </el-button>
              </el-col>
              <el-col :span="6" :offset="9">
                <el-input v-model="listQuery.fuzzy" placeholder="Username fuzzy search" clearable class="filter-item" @keyup.enter.native="handle_search"  />
              </el-col>
              <el-col :span="3" >
                <el-button class="filter-item" type="warning" @click="handle_user_edit_dialog" style="float:right">
                  Edit
                </el-button>
              </el-col>
              <el-col :span="3">
                <el-button class="filter-item" type="danger" @click="handle_delete" style="float:right">
                  Delete
                </el-button>
              </el-col>
            </el-row> 
          </div>
        <el-table
          :key="tableKey"
          v-loading="listLoading"
          :data="userlist"
          border
          fit
          highlight-current-row
          style="width: 100%;"
          @sort-change="sortChange"
          @row-click="handle_row_click"
          class='user-table'
          ref='pctable'
        >
          <el-table-column label="User Name" class-name="status-col" min-width="10%" align="center" sortable="custom" prop="username" >
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.username }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Roles" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="roles" show-overflow-tooltip >
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.role_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Domain" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="domain" >
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.domain }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Last LogonDate" class-name="status-col" min-width="10%" align="center" sortable="custom" prop="lastlogondate" >
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.lastlogondate }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" />
      </el-card>
      <el-card  class="box-card-right">
          <div class="filter-container" style="line-height: 40px;">
            <el-row :gutter="5">
              <el-col :span="3">
                <el-button class="filter-item" type="primary" @click="handle_groupcreate_dialog">
                    Add New Group
                </el-button>
              </el-col>
              <el-col :span="3" :offset="14" >
                <el-button class="filter-item" type="warning" @click="handle_groupdeit_dialog" style="float:right">
                  Edit
                </el-button>
              </el-col>
              <el-col :span="4" >
                <el-button class="filter-item" type="danger" @click="handle_groupdelete" style="float:right">
                  Delete
                </el-button>
              </el-col>
            </el-row> 
          </div>
          <el-table
            :key="tableKey"
            v-loading="listLoading"
            :data="grouplist"
            border
            fit
            highlight-current-row
            style="width: 100%;"
            @sort-change="sortChange"
            @row-click="handle_row_click"
            class='user-table'
            ref='pctable'
          >
            <el-table-column label="Group Name" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="groupname" >
              <template slot-scope="{row}">
                <span class="bigger_font">{{ row.groupname }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Roles" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="roles" show-overflow-tooltip >
              <template slot-scope="{row}">
                <span class="bigger_font">{{ row.roles }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Domain" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="domain" >
              <template slot-scope="{row}">
                <span class="bigger_font">{{ row.domain }}</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total>0" :total="grouplisttotal" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" />
        <!-- </div> -->
      </el-card>
      <el-dialog 
        :title="'Add a New User'" 
        :visible.sync="new_user_dialog_form_visible" 
        style="width:80%;margin-left:10%"
        @close="handledialogClose">
        <!-- https://element.eleme.cn/#/zh-CN/component/transfer -->
        <el-form ref="new_user_form" :rules="rules" :model="new_user" label-position="right" label-width="18%" style="width: 100%; margin-left:15px;">
          <el-form-item label="User Name :"  style="width:90%" prop="name"> 
            <el-input v-model="new_user.name" placeholder="Please input"/>
          </el-form-item>
          <el-form-item label="Password :"  style="width:90%" v-show="new_user.domain=='local'" prop="password">  
            <el-input v-model="new_user.password" placeholder="Please input" type="password" show-password/>
          </el-form-item>
          <el-form-item label="Domain :"  style="width:90%" > 
            <el-radio v-model="new_user.domain" label="ikea">ikea.com</el-radio>
            <el-radio v-model="new_user.domain" label="local" disabled>local</el-radio>     
          </el-form-item>
          <el-form-item label="Roles :"  style="width:90%" prop="role" > 
            <div style="height:40vh;border: 1px solid #a6b5ba;overflow-y: auto;">
              <!-- box-shadow: 1px 1px 18px #888888; -->
              <el-checkbox-group v-model="new_user.check_group" >
                <el-checkbox 
                  style="width:36%;margin-left:5%;margin-top:1%" 
                  v-for     = "_role in role_list" 
                  :key      = "_role.id" 
                  :label    = "_role.id" 
                >
                {{_role.name}}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="new_user_dialog_form_visible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="add_user()">
            Confirm
          </el-button>
        </div>
      </el-dialog>
      <el-dialog 
        :title="'Add New Group'" 
        :visible.sync="newgroupdialogFormVisible" 
        style="width:1500px;margin-left:12%"
        @close="handledialogClose"
        :before-close="handledialogbeforeClose">
        <el-form ref="groupdataForm" :model="new_user" label-position="left" label-width="95px" style="width: 1000px; margin-left:15px;">
          <el-form-item label="Group Name :" label-width="105px" style="width:55%" > 
            <el-input v-model="new_user.new_groupname" placeholder="Please input" />
          </el-form-item>
          <el-form-item label="Privilages :"  style="width:50%" > 
          </el-form-item>
            <el-transfer
              filterable
              :titles="['Unselected', 'Selected']"
              :right-default-checked="[1]"
              filter-placeholder="Fuzzy search"
              v-model="lockrole"
              @change="handleChange"
              :data="transferdata">
            </el-transfer>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="newgroupdialogFormVisible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="add_group()">
            Confirm
          </el-button>
        </div>
      </el-dialog>
      <el-dialog 
        :title="'Edit User'" 
        :visible.sync="edit_user_dialog_form_visible" 
        style="width:80%;margin-left:10%"
        @close="handledialogClose">
        <el-form ref="edituserForm" label-position="left" label-width="18%" style="width: 100%; margin-left:15px;">
          <el-form-item label="User Name :"  style="width:90%" > 
            <el-input v-model="selectedrow.username" readonly/>
          </el-form-item>
          <el-form-item label="Roles :"  style="width:90%" prop="role" > 
            <div style="height:400px;border: 1px solid #a6b5ba">
              <!-- box-shadow: 1px 1px 18px #888888; -->
              <el-checkbox-group v-model="edit_user.check_group" >
                <el-checkbox 
                  style="width:36%;margin-left:5%;margin-top:1%" 
                  v-for     = "_role in role_list" 
                  :key      = "_role.id" 
                  :label    = "_role.id" 
                >
                {{_role.name}}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="edit_user_dialog_form_visible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="handle_user_edit()">
            Confirm
          </el-button>
        </div>
      </el-dialog>
      <el-dialog 
        :title="'Edit Group'" 
        :visible.sync="editgroupdialogFormVisible" 
        style="width:1500px;margin-left:12%"
        @close="handledialogClose"
        :before-close="handledialogbeforeClose">
        <el-form ref="editgroupForm" label-position="left" label-width="95px" style="width: 1000px; margin-left:15px;">
          <el-form-item label="Group Name :" label-width="105px" style="width:55%" > 
            <el-input v-model="selectedrow.groupname" readonly/>
          </el-form-item>
          <el-form-item label="Privilages :"  style="width:50%;" > 
          </el-form-item>
            <el-transfer
              filterable
              :titles="['Unselected', 'Selected']"
              :right-default-checked="[1]"
              filter-placeholder="Fuzzy search"
              v-model="lockrole"
              @change="handleChange"
              :data="transferdata">
            </el-transfer>
          <!-- </el-form-item> -->
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editgroupdialogFormVisible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="handle_groupedit()">
            Confirm
          </el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
    import {GetUsersList, Adduser, DeleteUser, EditUser, GetGroupList, AddGroup, EditGroup, DeleteGroup, GetUserRole, UpdateUserRole} from '@/api/user'
    import {GetRoleList} from '@/api/common'
    import waves from '@/directive/waves' // waves directive
    import { parseTime } from '@/utils'
    import Pagination from '@/components/Pagination' // secondary package based on el-pagination

    const domainoptions = [
      {
        value: 'ikea.com',
        label: 'ikea.com',
      },
      {
        value: 'local',
        label: 'local',
      },
    ]
    const rolesoptions = [
      {
        value: 'pmuser',
        label: 'PMUser',
      },
      {
        value: 'admin',
        label: 'Admin',
      },
    ]
    
    export default {
      name: 'UserTable',
      components: { Pagination },
      directives: { waves },
      data() {
        const role_validator =(rule, value, callback)=>{//check if there is at least 1 role been selected
          if(this.new_user.check_group.length==0){
            callback(new Error("At least select one role."))
          }
          else{
            callback()
          }
        }
        const password_validator =(rule, value, callback)=>{//check if there is at least 1 role been selected
          if(this.new_user.domain=='local'){
            console.log(this.new_user.password)
            if(this.new_user.password==undefined||this.new_user.password==null||this.new_user.password.length==0){
              callback(new Error("Password is required when creating local user."))
            }
            
          }
          callback()
        }
        const name_validator = (rule, value, callback)=>{//check if there is at least 1 role been selected
          if(value.match(/^[0-9a-zA-Z]+$/)==null){
            callback(new Error("Memo id only."))
          }
          callback()
        }
        return {
          new_user:{
            domain:"ikea",
            check_group:[],
            role_list:[],
            name:'',
            new_groupname:'',
            serviceaccountpassword:'',
            tier:'ikea.com',
            existinguseraccount:'',
            ifnewaccount:'1',
            selecteduser: '',
            // passwordType:'password',
          },
          edit_user:{
            check_group:[]
          },
          role_list:[],
          exitingaccountlist:[],
          tableKey: 0,
          list: null,
          filtered_list: null,
          group_filtered_list: null,
          userlist: null,
          grouplist: null,
          all_user_list: null,
          // total: 0,
          listLoading: true,
          dialogLoading: true,
          listQuery:{
            page: 1,
            limit: 20,
            username: '',
            status: '',
            sort: '+id',
            fuzzy:''
          },
          domainoptions,
          rolesoptions,
          sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
          selectedrow:'',
          edit_user_dialog_form_visible: false,
          new_user_dialog_form_visible: false,
          editgroupdialogFormVisible: false,
          newgroupdialogFormVisible: false,
          dialogPvVisible: false,
          logdata: [],
          rules: {
            role: { required: true, message: 'Role is required', validator:role_validator},
            name: { required: true, trigger: 'change', validator:name_validator},
            password:{required: true, message: 'Password is required when creating local user',  validator:password_validator}
          },
          intervaljob:'',
          transferdata: null,
          orgtransferdata: null,
          lockrole: [1],
          userdialog_right :[1],
          userdialog_left :[],
          userdialog_rolename :[],
          userselectedevent : false,
          
        }
      },
      computed: {
        total() {
          if(this.filtered_list){
            return this.filtered_list.length
          }
          else{
              return 0
          }
        },
        grouplisttotal() {
          if(this.group_filtered_list){
            return this.group_filtered_list.length
          }
          else{
              return 0
          }
        }
      },
      created() {
        this.get_user_list()
        this.get_role_list()
        this.get_group_list()
      },
      methods: {
        get_user_list() {
          //get the user list
          this.listLoading = true
          GetUsersList(this.$store.getters.token).then(response => {
            console.log(response.data)
            this.all_user_list = response.data
            this.exitingaccountlist = response.data
            this.filtered_list = this.all_user_list
            let page = this.listQuery.page
            let limit = this.listQuery.limit
            let start , end
            if(page*limit>=this.total){
              start = (page-1)*limit
              end = this.total
            }
            else{
              start = (page-1)*limit
              end = page * limit
            }
            this.userlist = this.filtered_list.slice(start,end)
            this.listLoading = false
          })
          
        },
        get_group_list() {
          //get the group list
          this.listLoading = true
          GetGroupList(this.$store.getters.token).then(response => {
            this.all_group_list = response.data
            this.exitingaccountlist = response.data
            this.group_filtered_list = this.all_group_list
            let page = this.listQuery.page
            let limit = this.listQuery.limit
            let start , end
            if(page*limit>=this.grouplisttotal){
              start = (page-1)*limit
              end = this.grouplisttotal
            }
            else{
              start = (page-1)*limit
              end = page * limit
            }
            this.grouplist = this.group_filtered_list.slice(start,end)
            this.listLoading = false
          })
          
        },
        get_single_user_role(){
          let payload = {
            user_id : this.selectedrow.id,
            token : this.$store.getters.token
          }
          return GetUserRole(payload).then(response => {
            console.log(response.data)
            return response.data
          }).catch(error =>{
            this.$message({
              type: 'error',
              message: 'Unable to load user role list.',
              duration: 5000
            });  
            return []
          })
        },
        get_role_list(){
          //get the role list
          this.listLoading = true
          GetRoleList(this.$store.getters.token).then(response => {
            this.role_list = response.data
            this.role_list.sort((a,b)=>a.name.localeCompare(b.name))
            // const data = []
            // for (let id in this.all_role_list) {
            //   data.push({
            //     key: this.all_role_list[id]['id'],
            //     label: this.all_role_list[id]['name']
            //   });
            //   this.userdialog_left.push(this.all_role_list[id]['id']);
            //   this.userdialog_rolename.push(this.all_role_list[id]['name']);
            // };
            // this.transferdata = data
            // this.orgtransferdata = data
            // return data;
          })
        },
        handleChange(value, direction, movedKeys) {
          //monitoring the transfer value
          this.userdialog_left.splice(0,1)
          // this.userdialog_right.push(1)
          let id
          if (direction === "right"){
            for(let indid in movedKeys){
              this.userdialog_right.push(this.all_role_list[movedKeys[indid]-1].id)
              this.userdialog_left.splice(this.userdialog_left.indexOf(movedKeys[indid]),1)
            }
          };
          if (direction === "left"){
            for(let indid in movedKeys){
              this.userdialog_right.splice(this.userdialog_right.indexOf(movedKeys[indid]),1)
              this.userdialog_left.push(this.all_role_list[movedKeys[indid]-1].id)
            }
          };
        },
        handledialogClose(){
          //set some vaule to be default once the dialog closed
          this.userdialog_right=[1],
          this.new_user.new_groupname='',
          this.new_user.new_username=''
          // this.get_role_list()
          // this.dialogLoading = true
        },
        handledialogbeforeClose(done) {
          this.$confirm('Are you sure to close ?')
            .then(_ => {
              done();
            })
            .catch(_ => {});
        },
        remove_duplicate(arr) {
          //remove the duplicated ones
          const newArr = []
          arr.forEach(item => {
            if (!newArr.includes(item)) {
              newArr.push(item)
            }
          })
          return newArr
        },
        set_page(){
          // set the page with the page number
          let page = this.listQuery.page
          let limit = this.listQuery.limit
          let start , end
          if(page*limit>=this.total){
            start = (page-1)*limit
            end = this.total 
          }
          else{
            start = (page-1)*limit
            end = page * limit
          }
          this.userlist = this.filtered_list.slice(start,end)
        },
        sortChange(data) {
          // sorted table column
        const { prop, order } = data
        if(order==null){
          this.sortChange({prop:'id',order:'ascending'})
          return 
        }
        let flag_num = order=="ascending" ? 1 : -1
        this.filtered_list.sort((item1,item2)=>(
          (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
        ))
        this.set_page()
      },
      handleFilter() {
        this.listQuery.page = 1
      },
      sortByID(order) {
        if (order === 'ascending') {
          this.listQuery.sort = '+id'
        } else {
          this.listQuery.sort = '-id'
        }
        this.handleFilter()
      },
      handle_user_create_dialog() {
        this.lockrole= [1]
        this.new_user_dialog_form_visible = true
        this.$nextTick(() => {
          this.$refs['new_user_form'].clearValidate()
        })
      },
      handle_groupcreate_dialog() {
        //open the create group dialog
        this.lockrole= [1]
        this.newgroupdialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['groupdataForm'].clearValidate()
        })
      },
      async handle_user_edit_dialog() {
        //open the edit user dialog
        if(!this.selectedrow){
          this.$message({
              type: 'warning',
              message: 'Select a user first.',
              duration: 5000
            });  
          return 
        }
        this.edit_user.check_group = await this.get_single_user_role()
        console.log("edit user checkgroup")
        console.log(this.edit_user.check_group)
        this.edit_user_dialog_form_visible = true
        this.$nextTick(() => {
          this.$refs['edituserForm'].clearValidate()
        })
      },
      handle_groupdeit_dialog() {
        //open the edit group dialog
        this.lockrole= [1]
        this.groupdialog_right=[]
        let currentrole_list = this.selectedrow.roles.split(';')
        for(let current_role in currentrole_list){
          let group_roleid = this.userdialog_rolename.indexOf(currentrole_list[current_role])
          // this.transferdata[user_roleid]
          this.lockrole.push(group_roleid+1)
          this.groupdialog_right.push(group_roleid+1)
        }
        this.editgroupdialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['editgroupForm'].clearValidate()
        })
      },
      handle_delete(){
        //delete the selected user
        let payload = {
          data:{id:this.selectedrow.id},
          token: this.$store.getters.token
        }
        this.$confirm('Deleting the User: '+this.selectedrow.username, 'Delete User', {
            confirmButtonText: 'YES',
            cancelButtonText: 'NO',
            type: 'danger'
          }).then(() => {
            DeleteUser(payload).then(()=>{
              this.$notify({
                    title: 'Success',
                    message: `Successfully deleted the User: ${this.selectedrow.username} .` ,
                    type: 'success',
                    duration: 2000
                  })
                  this.get_user_list()
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: `Failed to delete the User: ${this.selectedrow.username} .`
            });          
          });
      },
      handle_groupdelete(){
        //delete the selected user
        let payload = {
          data:{groupname:this.selectedrow.groupname},
          token: this.$store.getters.token
        }
        this.$confirm('Deleting the Group: '+this.selectedrow.groupname, 'Delete Group', {
            confirmButtonText: 'YES',
            cancelButtonText: 'NO',
            type: 'danger'
          }).then(() => {
            DeleteGroup(payload).then(()=>{
              this.$notify({
                    title: 'Success',
                    message: `Successfully deleted the Group: ${this.selectedrow.groupname} .` ,
                    type: 'success',
                    duration: 2000
                  })
                  this.get_group_list()
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: `Failed to delete the Group: ${this.selectedrow.groupname} .`
            });          
          });
      },
      handle_user_edit(){
        //edit the selected user
        let payload = {
          data:{
            "user_id":this.selectedrow.id,
            "role_ids": this.edit_user.check_group
          },
          token: this.$store.getters.token,
        }
        UpdateUserRole(payload).then(()=>{
          this.$notify({
                title: 'Success',
                message: `Successfully updated the User: ${this.selectedrow.username} .` ,
                type: 'success',
                duration: 2000
              })
              this.edit_user_dialog_form_visible = false
              this.get_user_list()
          }).catch(() => {
            this.$message({
              type: 'info',
              message: `Failed to update the User: ${this.selectedrow.username} .`
            });          
          });
      },
      handle_groupedit(){
        //edit the selected user
        let payload = {
          data:{
            "groupname":this.selectedrow.groupname,
            "role": this.userdialog_right
          },
          token: this.$store.getters.token,
          role: this.userdialog_right
        }
        EditGroup(payload).then(()=>{
          this.$notify({
                title: 'Success',
                message: `Successfully Edited the User: ${this.selectedrow.username} .` ,
                type: 'success',
                duration: 2000
              })
              this.editgroupdialogFormVisible = false
              this.get_group_list()
          }).catch(() => {
            this.$message({
              type: 'info',
              message: `Failed to Edited the User: ${this.selectedrow.username} .`
            });          
          });
        },
        handle_row_click(row,column,event){
          this.selectedrow = row
        },
        formatJson(filterVal) {
          return this.list.map(v => filterVal.map(j => {
            if (j === 'timestamp') {
              return parseTime(v[j])
            } else {
              return v[j]
            }
          }))
        },
        getSortClass: function(key) {
          const sort = this.listQuery.sort
          return sort === `+${key}` ? 'ascending' : 'descending'
        },
        add_user(){
          //create new user
          // let newsa = this.new_user.ifnewaccount == '1'? false : true
          this.$refs['new_user_form'].validate((valid)=>{
            if(!valid){
              return
            }
            let payload 
            if(this.new_user.domain=='local'){
              payload = {
                token: this.$store.getters.token,
                data : {

                }
              }
            }else{
              payload = {
                token: this.$store.getters.token,
                data :{
                  name: this.new_user.name,
                  role_ids:this.new_user.check_group
                }
              }
            }
            Adduser(payload)
              .then(() => {
                this.$notify({
                  title: 'Success',
                  message: `User  added successfully.`,
                  type: 'success',
                  duration: 5000
                })
                this.new_user_dialog_form_visible = false
                this.get_user_list()
              })
              .catch((error) => {
                let error_message
                if(error.response.data.message){
                  error_message = error.response.data.message
                }
                else if (error.response.data.errors){
                  error_message = error.response.data.errors.json._schema
                }
                this.new_user_dialog_form_visible  = false
                this.$notify({
                  title: 'Error',
                  message: `Failed to add User. Reason: ${error_message}`,
                  type: 'error',
                  duration: 5000
                })
              })
          })
        },
        add_group(){
          //create new group
          let newgroupname
          if(this.new_user.tier === "ikea.com"){
            let domaindefine = ((this.new_user.new_groupname).toLowerCase()).match("@ikea.com") ? true :false
            if(domaindefine){newgroupname = (this.new_user.new_groupname).toLowerCase()}else{newgroupname = (this.new_user.new_groupname).toLowerCase()+"@ikea.com"}
          }else{
            newgroupname = (this.new_user.new_groupname).toLowerCase()
          }
          let payload = {
            data:{        
              "role": this.userdialog_right,
              "tier": this.new_user.tier,
              "name": newgroupname
            },
            token: this.$store.getters.token
          }
          this.$refs['groupdataForm'].validate((valid) => {
            if (valid) {
              AddGroup(payload)
              .then(() => {
                this.$notify({
                  title: 'Success',
                  message: `Group: ${newgroupname} added successfully.`,
                  type: 'success',
                  duration: 2000
                })
                this.newgroupdialogFormVisible = false
                this.get_group_list()
              })
              .catch((error) => {
                this.newgroupdialogFormVisible  = false
                this.$notify({
                  title: 'Error',
                  message: `Failed to add Group ${newgroupname}.`,
                  type: 'error',
                  duration: 2000
                })
              })
            }
          })
          return 0
        },
        handle_search(){

          if(this.listQuery.fuzzy.trim().length){
            let temp_list = this.all_user_list
            let fuzzy_list = this.listQuery.fuzzy.trim().split(/\s+/)
            for(let fuzzy of fuzzy_list){
              fuzzy = fuzzy.toString().toLowerCase()
              temp_list = temp_list.filter((k)=>{
                if( k.username.toString().toLowerCase().search(fuzzy)!= -1){
                  return true
                }
              })
            }
            this.filtered_list = temp_list
          }
          this.set_page()
        }
      },
      beforeDestroy(){
        clearInterval( this.intervaljob )
      }
    }
  </script>
  <style scoped>
  .user-table span{
    font-size: 17px
  }

  .left-table,.right-table {
    width: 50%;
    float: left;
  }

  .el-main {
    /* background-color: #c996cc;
    color: #333; */
    /* width: 50%; */
    text-align: center;
    /* line-height: 260px; */
    border-style: solid;
    border-width: 1px;
    /* border-left-style:dotted; */
  }
  .el-row {
    margin-bottom: 20px;
  }

  .el-row:last-child {
    margin-bottom: 0;
  }

  .el-col {
    border-radius: 4px;
  }

  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }

  .transfer-footer {
    margin-left: 20px;
    padding: 6px 5px;
  }
  
  .box-card-left {
    width: 55%;
    max-width: 80%;
    float: left;
    margin-left: 30px auto;
    border-radius: 6px;
  }

  .box-card-right {
    width: 43%;
    float: left;
    max-width: 80%;
    margin-left: 15px;
    border-radius: 6px;
  }

  .el-transfer ::v-deep.el-transfer-panel {
    /* border: 1px solid #EBEEF5;
    border-radius: 4px;
    overflow: hidden;
    background: #FFF;
    display: inline-block;
    vertical-align: middle; */
    width: 280px;
    /* height: 300px;
    max-height: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative; */
    margin-left: 35px;
    /* margin-inline-end: auto; */
  }

  .el-transfer ::v-deep  .el-transfer__buttons{
    width: 25px;
    margin-left: -20px;
  }

  .el-transfer ::v-deep  .el-button+.el-button{
    margin-left: 0px;
  }
  /* .dashboard-editor-container {
    padding: 2px 22px 2px 22px;
    background-color: rgb(240, 242, 245);
    position: relative;
  } */
  </style>
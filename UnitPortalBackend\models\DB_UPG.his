########################################################
###                                                  ###
###                2023-08-23 Curry/<PERSON>ai            ###
###                                                  ###
########################################################
# upgrade role_simplivity, add 2 fields
	# view_host              = db.Column(db.String(10))
	# view_host_scope        = db.Column(db.String(8000))
# create table role_administration
# upgrade role , add 1 field
	# role_administration    = db.Column(db.Integer)
#SQL: execute below in your DB connecter.

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_simplivity') 
	and  name = 'view_host')
	
	BEGIN
		ALTER TABLE role_simplivity ADD view_host VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_simplivity') 
	and  name = 'view_host_scope')
	
	BEGIN
		ALTER TABLE role_simplivity ADD view_host_scope VARCHAR(8000) NULL;
	END
GO

IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('role_administration') )
	CREATE TABLE [dbo].[role_administration] (
  [id] int IDENTITY(1,1) NOT NULL,
  [view_user] varchar(10) COLLATE Latin1_General_CI_AS NULL,
  [view_user_scope] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  [view_role] varchar(10) COLLATE Latin1_General_CI_AS NULL,
  [view_role_scope] varchar(8000) COLLATE Latin1_General_CI_AS NULL
  )
GO

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role') 
	and  name = 'role_administration')
	
	BEGIN
		ALTER TABLE role ADD role_administration INTEGER NULL;
	END
GO


########################################################
###                                                  ###
###                2023-08-29 Curry /Shuai           ###
###                                                  ###
########################################################
#upgrade dh_retail_ntx_pc
    #set service_account to string, not int
#SQL: execute below in your DB connecter.

ALTER table dh_retail_ntx_pc alter COLUMN service_account varchar(50)


########################################################
###                                                  ###
###                2023-09-19 Curry/Shuai            ###
###                                                  ###
########################################################
# upgrade role_nutanix, add 6 fields
	# view_ahv               = db.Column(db.String(10))
	# view_ahv_scope         = db.Column(db.String(8000))
	# add_pc                 = db.Column(db.String(10))
    # add_pc_scope           = db.Column(db.String(8000))
    # remove_pc              = db.Column(db.String(10))
    # remove_pc_scope        = db.Column(db.String(8000))

#SQL: execute below in your DB connecter.

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_nutanix') 
	and  name = 'view_ahv')
	
	BEGIN
		ALTER TABLE role_nutanix ADD view_ahv VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_nutanix') 
	and  name = 'view_ahv_scope')
	
	BEGIN
		ALTER TABLE role_nutanix ADD view_ahv_scope VARCHAR(8000) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_nutanix') 
	and  name = 'add_pc')
	
	BEGIN
		ALTER TABLE role_nutanix ADD add_pc VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_nutanix') 
	and  name = 'add_pc_scope')
	
	BEGIN
		ALTER TABLE role_nutanix ADD add_pc_scope VARCHAR(8000) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_nutanix') 
	and  name = 'remove_pc')
	
	BEGIN
		ALTER TABLE role_nutanix ADD remove_pc VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_nutanix') 
	and  name = 'remove_pc_scope')
	
	BEGIN
		ALTER TABLE role_nutanix ADD remove_pc_scope VARCHAR(8000) NULL;
	END
GO

########################################################
###                                                  ###
###                2023-09-20 Curry                  ###
###                                                  ###
########################################################
# upgrade role_marketplace, add fields
   # abort_wl                   = db.Column(db.String(10))
   # abort_wl_scope             = db.Column(db.String(8000))
   # remove_wl                  = db.Column(db.String(10))
   # remove_wl_scope            = db.Column(db.String(8000))
   # create_template_wl         = db.Column(db.String(10))
   # create_template_wl_scope   = db.Column(db.String(8000))
   # create_custom_wl           = db.Column(db.String(10))
   # create_custom_wl_scope     = db.Column(db.String(8000))
   # create_template        	= db.Column(db.String(10))
   # create_template_scope  	= db.Column(db.String(8000))
   # edit_template          	= db.Column(db.String(10))
   # edit_template_scope    	= db.Column(db.String(8000))
   # remove_template        	= db.Column(db.String(10))
   # remove_template_scope  	= db.Column(db.String(8000))

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'create_template')
	
	BEGIN
		ALTER TABLE role_marketplace ADD create_template VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'create_template_scope')
	
	BEGIN
		ALTER TABLE role_marketplace ADD create_template_scope VARCHAR(8000) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'edit_template')
	
	BEGIN
		ALTER TABLE role_marketplace ADD edit_template VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'edit_template_scope')
	
	BEGIN
		ALTER TABLE role_marketplace ADD edit_template_scope VARCHAR(8000) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'remove_template')
	
	BEGIN
		ALTER TABLE role_marketplace ADD remove_template VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'remove_template_scope')
	
	BEGIN
		ALTER TABLE role_marketplace ADD remove_template_scope VARCHAR(8000) NULL;
	END
GO


IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'abort_wl')
	
	BEGIN
		ALTER TABLE role_marketplace ADD abort_wl VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'abort_wl_scope')
	
	BEGIN
		ALTER TABLE role_marketplace ADD abort_wl_scope VARCHAR(8000) NULL;
	END
GO

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'remove_wl')
	
	BEGIN
		ALTER TABLE role_marketplace ADD remove_wl VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'remove_wl_scope')
	
	BEGIN
		ALTER TABLE role_marketplace ADD remove_wl_scope VARCHAR(8000) NULL;
	END
GO

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'create_template_wl')
	
	BEGIN
		ALTER TABLE role_marketplace ADD create_template_wl VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'create_template_wl_scope')
	
	BEGIN
		ALTER TABLE role_marketplace ADD create_template_wl_scope VARCHAR(8000) NULL;
	END
GO

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'create_custom_wl')
	
	BEGIN
		ALTER TABLE role_marketplace ADD create_custom_wl VARCHAR(10) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('role_marketplace') 
	and  name = 'create_custom_wl_scope')
	
	BEGIN
		ALTER TABLE role_marketplace ADD create_custom_wl_scope VARCHAR(8000) NULL;
	END
GO

########################################################
###                                                  ###
###                2023-10-11 Curry                  ###
###                                                  ###
########################################################
# create table dh_retail_ntx_workload_network
    #id            = db.Column(db.Integer, primary_key=True)
    #vlan_id       = db.Column(db.Integer, unique=True)
    #vlan_name     = db.Column(db.String(255))
    #site_type     = db.Column(db.String(255))

IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_retail_ntx_workload_network') )
	CREATE TABLE [dbo].[dh_retail_ntx_workload_network] (
  [id] int IDENTITY(1,1) NOT NULL,
  [vlan_id] int ,
  [vlan_name] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [site_type] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  )
GO


########################################################
###                                                  ###
###     2023-10-10 Curry /Shuai /Zoey /Ray           ###
###                                                  ###
########################################################
#add roles in groups
    # roles               = db.Column(db.String(50))
#SQL: execute below in your DB connecter.

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('groups') 
	and  name = 'roles')
	
	BEGIN
		ALTER TABLE groups  ADD roles VARCHAR(50) NULL;
	END
GO

########################################################
###                                                  ###
###     2023-10-11 Shuai/Ray/Curry		             ###
###                                                  ###
########################################################
#add columns in dh_retail_ntx_pe/dh_retail_ntx_host
    #foundation_version   = db.Column(db.String(50))
    #lcm_version            = db.Column(db.String(50))
    #ncc_version            = db.Column(db.String(50))
    #ilo_version         = db.Column(db.String(50))
    #bios_version        = db.Column(db.String(50))
    #controller_version  = db.Column(db.String(50))
#SQL: execute below in your DB connecter
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe') 
	and  name = 'foundation_version')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe  ADD foundation_version VARCHAR(50) NULL;
	END
GO

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe') 
	and  name = 'lcm_version')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe  ADD lcm_version VARCHAR(50) NULL;
	END
GO

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe') 
	and  name = 'ncc_version')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe  ADD ncc_version VARCHAR(50) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'controller_model')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD controller_model VARCHAR(50) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'bios_version')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD bios_version VARCHAR(50) NULL;
	END
GO

IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'controller_version')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD controller_version VARCHAR(50) NULL;
	END
GO
########################################################
###                                                  ###
###     2023-10-17  Curry		                     ###
###                                                  ###
########################################################
#add columns in dh_retail_ntx_pe
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe') 
	and  name = 'spp_version')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe  ADD spp_version VARCHAR(255) NULL;
	END
GO

# create table dh_retail_ntx_automation_spp_lcm_task
    #id                 = db.Column(db.Integer , primary_key=True)
    #pe                 = db.Column(db.String(100))
    #pc                 = db.Column(db.String(100))
    #create_date        = db.Column(db.String(100))
    #status             = db.Column(db.String(100))
    #target_version     = db.Column(db.String(100))
    #next_jump_version  = db.Column(db.String(100))
    #pid                = db.Column(db.Integer)
    #ntx_task_id        = db.Column(db.String(100))
    #creater            = db.Column(db.String(100))
    #detaillogpath    = db.Column(db.String(255))

IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_retail_ntx_automation_spp_lcm_task') )
	CREATE TABLE [dbo].[dh_retail_ntx_automation_spp_lcm_task] (
  [id] int IDENTITY(1,1) NOT NULL,
  [pid] int ,
  [pe] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [pc] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [create_date] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [target_version] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [next_jump_version] varchar(100) COLLATE Latin1_General_CI_AS NULL,  
  [ntx_task_id] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [creater] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [detaillogpath] varchar(255) COLLATE Latin1_General_CI_AS NULL
  )
GO

# create table dh_retail_ntx_automation_spp_lcm_task_log
    #id = db.Column(db.Integer , primary_key=True)
    #task_type = db.Column(db.String(50))
    #log_date = db.Column(db.String(50))
    #severity = db.Column(db.String(50))
    #loginfo = db.Column(db.String(255))
    #task_id = db.Column(db.Integer)

IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_retail_ntx_automation_spp_lcm_task_log') )
	CREATE TABLE [dbo].[dh_retail_ntx_automation_spp_lcm_task_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [task_id] int ,
  [task_type] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [log_date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [severity] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [loginfo] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  )
########################################################
###                                                  ###
###     			2023-10-17 Ray		             ###
###                                                  ###
########################################################
#add columns in dh_retail_ntx_workload_image
    #alias  = db.Column(db.String(50))
#SQL: execute below in your DB connecter
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_workload_image') 
	and  name = 'alias')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_workload_image  ADD alias VARCHAR(50) NULL;
	END
GO
########################################################
###                                                  ###
###     	    2023-11-15 Curry		             ###
###                                                  ###
########################################################
#add columns in dh_retail_ntx_workload_template, dh_retail_ntx_workload_task
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_workload_template') 
	and  name = 'linux_payload')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_workload_template  ADD linux_payload VARCHAR(8000) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_workload_template') 
	and  name = 'app_package')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_workload_template  ADD app_package VARCHAR(255) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_workload_task') 
	and  name = 'linux_payload')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_workload_task  ADD linux_payload VARCHAR(8000) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_workload_task') 
	and  name = 'app_package')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_workload_task  ADD app_package VARCHAR(255) NULL;
	END
GO

/*
########################################################
###                                                  ###
###     	    2023-11-30 Curry		             ###
###                                                  ###
########################################################
# delete column create_template_wl, create_template_wl_scope, create_custom_wl, create_custom_wl_scope
# add column  create_wl,  create_wl_scope to role_marketplace
*/
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_template_wl'
)
  BEGIN
      alter table role_marketplace drop column create_template_wl;
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_template_wl_scope'
)
  BEGIN
      alter table role_marketplace drop column create_template_wl_scope;
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_custom_wl'
)
  BEGIN
	  EXEC sp_rename 'role_marketplace.create_custom_wl', 'create_wl', 'COLUMN';
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_custom_wl_scope'
)
  BEGIN
	  EXEC sp_rename 'role_marketplace.create_custom_wl_scope', 'create_wl_scope', 'COLUMN';
  END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic0_uuid')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_uuid VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic0_mac')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_mac VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic0_speed')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_speed VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic0_mtu')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_mtu VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic0_sw_device')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_sw_device VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic0_sw_port')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_sw_port VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic0_sw_vendor')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_sw_vendor VARCHAR(500) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic0_sw_vlan')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_sw_vlan VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic1_uuid')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_uuid VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic1_mac')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_mac VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic1_speed')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_speed VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic1_mtu')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_mtu VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic1_sw_device')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_sw_device VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic1_sw_port')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_sw_port VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic1_sw_vendor')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_sw_vendor VARCHAR(500) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'nic1_sw_vlan')

	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_sw_vlan VARCHAR(100) NULL;
	END
GO

########################################################
###                                                  ###
###     	    2023-12-05 YIWXU2		             ###
###                                                  ###
########################################################
# change column name in role_marketplace

IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_template_wl'
)
  BEGIN
      alter table role_marketplace drop column create_template_wl;
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_template_wl_scope'
)
  BEGIN
      alter table role_marketplace drop column create_template_wl_scope;
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_custom_wl'
)
  BEGIN
	  EXEC sp_rename 'role_marketplace.create_custom_wl', 'create_wl', 'COLUMN';
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_custom_wl_scope'
)
  BEGIN
	  EXEC sp_rename 'role_marketplace.create_custom_wl_scope', 'create_wl_scope', 'COLUMN';
  END
GO
########################################################
###                                                  ###
###     	       2023-12-17 Ray		             ###
###                                                  ###
########################################################
#add columns in dh_retail_ntx_pe
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'license_category')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD license_category VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'license_class')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD license_class VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'license_cores_capacity')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD license_cores_capacity VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'license_flash_capacity')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD license_flash_capacity VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'license_hdd_capacity')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD license_hdd_capacity VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'license_cores_licensed')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD license_cores_licensed VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'license_flash_licensed')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD license_flash_licensed VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'license_hdd_licensed')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD license_hdd_licensed VARCHAR(100) NULL;
	END
GO
/*
########################################################
###                                                  ###
###     	    2023-11-30 Curry		             ###
###                                                  ###
########################################################
# delete column create_template_wl, create_template_wl_scope, create_custom_wl, create_custom_wl_scope
# add column  create_wl,  create_wl_scope to role_marketplace
*/
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_template_wl'
)
  BEGIN
      alter table role_marketplace drop column create_template_wl;
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_template_wl_scope'
)
  BEGIN
      alter table role_marketplace drop column create_template_wl_scope;
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_custom_wl'
)
  BEGIN
	  EXEC sp_rename 'role_marketplace.create_custom_wl', 'create_wl', 'COLUMN';
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('role_marketplace')
	and  name = 'create_custom_wl_scope'
)
  BEGIN
	  EXEC sp_rename 'role_marketplace.create_custom_wl_scope', 'create_wl_scope', 'COLUMN';
  END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic0_uuid')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_uuid VARCHAR(100) NULL;

########################################################
###                                                  ###
###     			2023-12-19 Shtao8                ###
###                                                  ###
########################################################
# create table dh_retail_ntx_move_log
    #id = db.Column(db.Integer , primary_key=True)
    #tasktype = db.Column(db.String(50))
    #logdate = db.Column(db.String(50))
    #severity = db.Column(db.String(50))
    #loginfo = db.Column(db.String(255))
    #taskid = db.Column(db.Integer)
# create table dh_retail_ntx_move_task
	#id                 = db.Column(db.Integer , primary_key=True)
    #pe                 = db.Column(db.String(100))
    #cluster            = db.Column(db.String(100))
    #createdate        = db.Column(db.String(100))
    #status             = db.Column(db.String(100))
    #datasyncstatus   = db.Column(db.String(100))
    #planuuid          = db.Column(db.String(100))
    #pid                = db.Column(db.Integer)
    #creater            = db.Column(db.String(100))
    #detaillogpath    = db.Column(db.String(255))
    #tasktype           = db.Column(db.String(100))
# create column dh_retail_sli_cluster
	#stagepreparation = db.Column(db.String(50))
    #movedevop = db.Column(db.String(50))
    #stagecutover = db.Column(db.String(50))
    #moveip = db.Column(db.String(255))
    #planuuid = db.Column(db.String(255))
    #syncstatus = db.Column(db.String(255))
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_retail_ntx_move_log') )
	CREATE TABLE [dbo].[dh_retail_ntx_move_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [taskid] int ,
  [tasktype] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [logdate] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [severity] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [loginfo] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  )
  GO
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_retail_ntx_move_task') )
	CREATE TABLE [dbo].[dh_retail_ntx_move_task] (
  [id] int IDENTITY(1,1) NOT NULL,
  [pid] int ,
  [cluster] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [pe] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [createdate] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [datasyncstatus] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [planuuid] varchar(100) COLLATE Latin1_General_CI_AS NULL, 
  [creater] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [tasktype] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [detaillogpath] varchar(255) COLLATE Latin1_General_CI_AS NULL
  )
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_sli_cluster')
	and  name = 'movedevop')
	
	BEGIN
		ALTER TABLE dh_retail_sli_cluster ADD movedevop VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic0_mac')
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_mac VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic0_speed')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_speed VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic0_mtu')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_mtu VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic0_sw_device')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_sw_device VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic0_sw_port')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_sw_port VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic0_sw_vendor')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_sw_vendor VARCHAR(500) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic0_sw_vlan')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic0_sw_vlan VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic1_uuid')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_uuid VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic1_mac')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_mac VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic1_speed')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_speed VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic1_mtu')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_mtu VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic1_sw_device')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_sw_device VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic1_sw_port')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_sw_port VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic1_sw_vendor')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_sw_vendor VARCHAR(500) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_host') 
	and  name = 'nic1_sw_vlan')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_host  ADD nic1_sw_vlan VARCHAR(100) NULL;

	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_sli_cluster')
	and  name = 'stagepreparation')
	
	BEGIN
		ALTER TABLE dh_retail_sli_cluster ADD stagepreparation VARCHAR(255) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_sli_cluster')
	and  name = 'stagecutover')
	
	BEGIN
		ALTER TABLE dh_retail_sli_cluster ADD stagecutover VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_sli_cluster')
	and  name = 'moveip')
	
	BEGIN
		ALTER TABLE dh_retail_sli_cluster ADD moveip VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_sli_cluster')
	and  name = 'planuuid')
	
	BEGIN
		ALTER TABLE dh_retail_sli_cluster ADD planuuid VARCHAR(255) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_sli_cluster')
	and  name = 'syncstatus')
	
	BEGIN
		ALTER TABLE dh_retail_sli_cluster ADD syncstatus VARCHAR(100) NULL;
	END
GO


/*
########################################################
###                                                  ###
###     	    2023-12-26 Curry		             ###
###                                                  ###
########################################################
# create table dh_retail_ntx_automation_aos_lcm_task
*/
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_retail_ntx_automation_aos_lcm_task') )
	CREATE TABLE [dbo].[dh_retail_ntx_automation_aos_lcm_task] (
  [id] int IDENTITY(1,1) NOT NULL,
  [pid] int ,
  [pe] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [pc] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [create_date] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [target_aos_version] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [target_ahv_version] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [ntx_task_id] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [creater] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [detail_log_path] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  )

IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_retail_ntx_automation_aos_lcm_task_log') )
	CREATE TABLE [dbo].[dh_retail_ntx_automation_aos_lcm_task_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [task_id] int ,
  [task_type] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [log_date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [severity] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [log_info] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  )

  ALTER table dh_retail_ntx_automation_spp_lcm_task_log ALTER COLUMN log_info VARCHAR(8000)
  ALTER table dh_retail_ntx_pm_log ALTER COLUMN loginfo VARCHAR(8000)
  ALTER table dh_retail_ntx_rotate_password_log ALTER COLUMN log_info VARCHAR(8000)
  ALTER table dh_retail_ntx_workload_task_log ALTER COLUMN loginfo VARCHAR(8000)
  ALTER table dh_retail_sli_pm_log ALTER COLUMN loginfo VARCHAR(8000)

# create table dh_retail_ntx_rotate_password_log, dh_retail_ntx_rotate_password_task
*/
-- ----------------------------
-- Table structure for dh_retail_ntx_rotate_password_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_ntx_rotate_password_log]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_retail_ntx_rotate_password_log]
GO

CREATE TABLE [dbo].[dh_retail_ntx_rotate_password_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [task_type] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [log_date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [severity] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [log_info] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  [task_id] int NULL
)
GO

ALTER TABLE [dbo].[dh_retail_ntx_rotate_password_log] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Primary Key structure for table dh_retail_ntx_rotate_password_log
-- ----------------------------
ALTER TABLE [dbo].[dh_retail_ntx_rotate_password_log] ADD CONSTRAINT [PK__dh_retai__3213E83FA67D30C3_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_ntx_rotate_password_task]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_retail_ntx_rotate_password_task]
GO

CREATE TABLE [dbo].[dh_retail_ntx_rotate_password_task] (
  [id] int IDENTITY(1,1) NOT NULL,
  [prism] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [cluster] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [startdate] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [creater] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [createdate] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [description] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [pid] int NULL,
  [detaillogpath] varchar(255) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_retail_ntx_rotate_password_task] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Primary Key structure for table dh_retail_ntx_rotate_password_task
-- ----------------------------
ALTER TABLE [dbo].[dh_retail_ntx_rotate_password_task] ADD CONSTRAINT [PK__dh_retai__3213E83FD942012B_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

/*
########################################################
###                                                  ###
###     	    2024-01-08 YIWXU2		             ###
###                                                  ###
########################################################
change detail_log_path -> detaillogpath:
    dh_retail_ntx_automation_spp_lcm_task
    dh_retail_ntx_rotate_password_task
    dh_retail_ntx_automation_dsc_task
*/
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_spp_lcm_task')
	and  name = 'detail_log_path'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_spp_lcm_task.detail_log_path', 'detaillogpath', 'COLUMN';
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_rotate_password_task')
	and  name = 'detail_log_path'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_rotate_password_task.detail_log_path', 'detaillogpath', 'COLUMN';
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_dsc_task')
	and  name = 'detail_log_path'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_dsc_task.detail_log_path', 'detaillogpath', 'COLUMN';
  END
GO
/*
change log_info -> loginfo, log_date -> logdate:
    dh_retail_ntx_automation_spp_lcm_task_log
    dh_retail_ntx_rotate_password_log
    dh_retail_ntx_automation_dsc_task_log
*/
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_spp_lcm_task_log')
	and  name = 'log_info'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_spp_lcm_task_log.log_info', 'loginfo', 'COLUMN';
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_spp_lcm_task_log')
	and  name = 'log_date'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_spp_lcm_task_log.log_date', 'logdate', 'COLUMN';
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_rotate_password_log')
	and  name = 'log_info'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_rotate_password_log.log_info', 'loginfo', 'COLUMN';
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_rotate_password_log')
	and  name = 'log_date'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_rotate_password_log.log_date', 'logdate', 'COLUMN';
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_dsc_task_log')
	and  name = 'log_info'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_dsc_task_log.log_info', 'loginfo', 'COLUMN';
  END
GO
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_dsc_task_log')
	and  name = 'log_date'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_dsc_task_log.log_date', 'logdate', 'COLUMN';
  END
GO

/*
########################################################
###                                                  ###
###     	    2024-01-15 YIWXU2		             ###
###                                                  ###
########################################################
update `loginfo` length to 8000
*/
ALTER table dh_retail_ntx_automation_spp_lcm_task_log ALTER COLUMN loginfo VARCHAR(8000)
ALTER table dh_retail_ntx_pm_log ALTER COLUMN loginfo VARCHAR(8000)
ALTER table dh_retail_ntx_rotate_password_log ALTER COLUMN loginfo VARCHAR(8000)
ALTER table dh_retail_ntx_workload_task_log ALTER COLUMN loginfo VARCHAR(8000)
ALTER table dh_retail_sli_pm_log ALTER COLUMN loginfo VARCHAR(8000)

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_workload_template')
	and  name = 'owner_id')

	BEGIN
		ALTER TABLE dh_retail_ntx_workload_template ADD owner_id INT NULL;
	END
GO

########################################################
###                                                  ###
###                2024-03-05 Zoey                   ###
###                                                  ###
########################################################
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_workload_template')
	and  name = 'update_dns')

	BEGIN
		ALTER TABLE dh_retail_ntx_workload_template ADD update_dns BIT NOT NULL DEFAULT 1;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_workload_task')
	and  name = 'update_dns')

	BEGIN
		ALTER TABLE dh_retail_ntx_workload_task ADD update_dns BIT NULL DEFAULT 1;
	END
GO

/*
########################################################
###                                                  ###
###     	    2024-03-11 RAYGU1		             ###
###                                                  ###
########################################################
add column `cert_expiry_date` to the table 'dh_retail_ntx_pc'
*/
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pc')
	and  name = 'cert_expiry_date')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pc ADD cert_expiry_date VARCHAR(100) NULL;
	END
GO

/*
########################################################
###                                                  ###
###     	    2024-03-11 RAYGU1		             ###
###                                                  ###
########################################################
add column `cert_expiry_date` to the table 'dh_retail_ntx_pe'
*/
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'cert_expiry_date')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD cert_expiry_date VARCHAR(100) NULL;
	END
GO


########################################################
###                                                  ###
###                2024-03-13 Zoey                   ###
###                                                  ###
########################################################
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('users')
	and  name = 'memo_id')

	BEGIN
		ALTER TABLE users ADD memo_id VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('users')
	and  name = 'profile')

	BEGIN
		ALTER TABLE users ADD profile VARCHAR(1000) NULL;
	END
GO


/*
########################################################
###                                                  ###
###     	    2024-03-14 CURFU1		             ###
###                                                  ###
########################################################
create table role_lcm
add role_lcm field to table "role"
*/
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('role_lcm') )
	CREATE TABLE [dbo].[role_lcm] (
  [id] int IDENTITY(1,1) NOT NULL,
  [view_spp] varchar(10) COLLATE Latin1_General_CI_AS NULL,
  [view_spp_scope] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  [view_aos] varchar(10) COLLATE Latin1_General_CI_AS NULL,
  [view_aos_scope] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  [view_move] varchar(10) COLLATE Latin1_General_CI_AS NULL,
  [view_move_scope] varchar(8000) COLLATE Latin1_General_CI_AS NULL
  )
GO

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('role')
	and  name = 'role_lcm')

	BEGIN
		ALTER TABLE role ADD role_lcm INT NULL;
	END
GO

################################################################
###                                                          ###
###             	    2024-03-18 ZOEXU6		             ###
###  Create table `dsc`, `dsc_rbac` and insert initial data  ###
###                                                          ###
################################################################
-- ----------------------------
-- Table structure for dsc
-- ----------------------------
CREATE TABLE dbo.dsc (
	id int IDENTITY(1,1) NOT NULL,
	site_type varchar(255) COLLATE Latin1_General_CI_AS NULL,
	tier varchar(255) COLLATE Latin1_General_CI_AS NULL,
	cvm_ram_gb int NULL,
	pe_site_admin_group varchar(255) COLLATE Latin1_General_CI_AS NULL,
	pc_site_viewer_group varchar(255) COLLATE Latin1_General_CI_AS NULL,
	var_groups varchar(255) COLLATE Latin1_General_CI_AS NULL,
	sender_email varchar(255) COLLATE Latin1_General_CI_AS NULL,
	smtp_server varchar(255) COLLATE Latin1_General_CI_AS NULL,
	alert_receiver_email varchar(255) COLLATE Latin1_General_CI_AS NULL,
	smtp_security varchar(255) COLLATE Latin1_General_CI_AS NULL,
	storage_compression_enabled bit NULL,
	storage_compression_delay_sec int NULL,
	system_eula_name varchar(255) COLLATE Latin1_General_CI_AS NULL,
	system_eula_comp varchar(255) COLLATE Latin1_General_CI_AS NULL,
	system_eula_role varchar(255) COLLATE Latin1_General_CI_AS NULL,
	system_remote_diag_disable bit NULL,
	domain_controller_port varchar(255) COLLATE Latin1_General_CI_AS NULL,
	domain_group_search_mode varchar(255) COLLATE Latin1_General_CI_AS NULL
);

ALTER TABLE [dbo].[dsc] SET (LOCK_ESCALATION = TABLE)
GO

-- ----------------------------
-- Records of [dsc]
-- ----------------------------
INSERT INTO [dbo].[dsc] (tier, site_type, cvm_ram_gb, pe_site_admin_group, pc_site_viewer_group, var_groups, sender_email, smtp_server, alert_receiver_email, smtp_security,  storage_compression_enabled, storage_compression_delay_sec, system_eula_name, system_eula_comp, system_eula_role, system_remote_diag_disable, domain_controller_port, domain_group_search_mode)
VALUES (N'Production', N'store', N'32', N'NXadmin', N'NXView', N'filter_naming_convention_site_name', N'<EMAIL>', N'smtp-gw.ikea.com', N'<EMAIL>', N'NONE', N'1', N'0', N'Emil Nilsson', N'IKEA', N'Digital Technology Engineer', N'1', N'636', N'NON_RECURSIVE')
GO

INSERT INTO [dbo].[dsc] (tier, site_type, cvm_ram_gb, pe_site_admin_group, pc_site_viewer_group, var_groups, sender_email, smtp_server, alert_receiver_email, smtp_security,  storage_compression_enabled, storage_compression_delay_sec, system_eula_name, system_eula_comp, system_eula_role, system_remote_diag_disable, domain_controller_port, domain_group_search_mode)
VALUES (N'PreProduction', N'store', N'32', N'NXadmin', N'NXViewer', N'filter_naming_convention_site_name', N'<EMAIL>', N'smtp-gw.ikea.com', N'<EMAIL>', N'NONE', N'1', N'0', N'Emil Nilsson', N'IKEA', N'Digital Technology Engineer', N'1', N'636', N'NON_RECURSIVE')
GO

INSERT INTO [dbo].[dsc] (tier, site_type, cvm_ram_gb, pe_site_admin_group, pc_site_viewer_group, var_groups, sender_email, smtp_server, alert_receiver_email, smtp_security,  storage_compression_enabled, storage_compression_delay_sec, system_eula_name, system_eula_comp, system_eula_role, system_remote_diag_disable, domain_controller_port, domain_group_search_mode)
VALUES (N'DevTest', N'store', N'32', N'UG-DHRTadmin-CG@PPE-PP-SE-ELM', N'UG-NXview-CG@HBG-RET-SE-ELM', N'filter_naming_convention_site_name', N'<EMAIL>', N'smtp-gw.ikeadt.com', N'<EMAIL>', N'NONE', N'1', N'0', N'Emil Nilsson', N'IKEA', N'Digital Technology Engineer', N'1', N'636', N'NON_RECURSIVE')
GO
-- ----------------------------
-- Table structure for dsc_rbac
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dsc_rbac]') AND type IN ('U'))
	DROP TABLE [dbo].[dsc_rbac]
GO

CREATE TABLE [dbo].[dsc_rbac] (
  id int IDENTITY(1,1) NOT NULL,
  [name] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [value] varchar(255) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dsc_rbac] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of [dsc_rbac]
-- ----------------------------
INSERT INTO [dbo].[dsc_rbac] (name, value) VALUES (N'UG-NutanixSelfServiceUsers-CG@HBG-RET-SE-HBG-Dn', N'CN=UG-NutanixSelfServiceUsers-CG@HBG-RET-SE-HBG,OU=UserGroups,OU=HBG,OU=SE,OU=RET,OU=HBG,DC=ikea,DC=com')
GO

INSERT INTO [dbo].[dsc_rbac] (name, value) VALUES (N'NXadmin-Cn', N'NXadmin')
GO

INSERT INTO [dbo].[dsc_rbac] (name, value) VALUES (N'UG-NutanixSelfServiceAdmin-CG@HBG-RET-SE-HBG-Dn', N'CN=UG-NutanixSelfServiceAdmin-CG@HBG-RET-SE-HBG,OU=UserGroups,OU=HBG,OU=SE,OU=RET,OU=HBG,DC=ikea,DC=com')
GO

INSERT INTO [dbo].[dsc_rbac] (name, value) VALUES (N'NXadmin-Dn', N'CN=NXadmin,OU=SecureGroups,DC=ikea,DC=com')
GO

INSERT INTO [dbo].[dsc_rbac] (name, value) VALUES (N'UG-NutanixSelfServiceAdmin-CG@HBG-RET-SE-HBG-Cn', N'UG-NutanixSelfServiceAdmin-CG@HBG-RET-SE-HBG')
GO

INSERT INTO [dbo].[dsc_rbac] (name, value) VALUES (N'UG-NutanixSelfServiceUsers-CG@HBG-RET-SE-HBG-Cn', N'UG-NutanixSelfServiceUsers-CG@HBG-RET-SE-HBG')
GO

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'is_central_pe')

	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD is_central_pe bit NULL;
	END
GO

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'site_type')

	BEGIN
		ALTER TABLE dh_retail_ntx_pe ADD site_type varchar(255) NULL;
	END
GO
########################################################
###                                                  ###
###                2024-04-03 Shtao8                 ###
###                                                  ###
########################################################
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_sli_vms')
	and  name = 'guest_name')

	BEGIN
		ALTER TABLE dh_retail_sli_vms ADD guest_name VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_sli_vms')
	and  name = 'vm_id')

	BEGIN
		ALTER TABLE dh_retail_sli_vms ADD vm_id VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_sli_vms')
	and  name = 'status')

	BEGIN
		ALTER TABLE dh_retail_sli_vms ADD status VARCHAR(100) NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_sli_vms')
	and  name = 'vc_fqdn')

	BEGIN
		ALTER TABLE dh_retail_sli_vms ADD vc_fqdn VARCHAR(100) NULL;
	END
GO

################################################################
###                                                          ###
###             	    2024-04-10 ZOEXU6		             ###
###                                                          ###
###                                                          ###
################################################################
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_rotate_password_task')
	and  name = 'createdate'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_rotate_password_task.createdate', 'create_date', 'COLUMN';
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_rotate_password_task')
	and  name = 'prism'
)
  BEGIN
	  ALTER TABLE dh_retail_ntx_rotate_password_task DROP COLUMN prism;
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_rotate_password_task')
	and  name = 'startdate'
)
  BEGIN
	  ALTER TABLE dh_retail_ntx_rotate_password_task DROP COLUMN startdate;
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_rotate_password_task')
	and  name = 'createrinfo'
)
  BEGIN
	  ALTER TABLE dh_retail_ntx_rotate_password_task DROP COLUMN createrinfo;
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_rotate_password_task')
	and  name = 'cluster'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_rotate_password_task.cluster', 'pe', 'COLUMN';
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_renew_cert_task')
	and  name = 'pc'
)
  BEGIN
	  ALTER TABLE dh_retail_ntx_automation_renew_cert_task DROP COLUMN pc;
  END
GO

################################################################
###                                                          ###
###             	    2024-04-15 RAYGU1		             ###
###                                                          ###
###                                                          ###
################################################################
-- ----------------------------
-- Table structure for dh_retail_ntx_automation_renew_cert_task
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_ntx_automation_renew_cert_task]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_retail_ntx_automation_renew_cert_task]
GO

CREATE TABLE [dbo].[dh_retail_ntx_automation_renew_cert_task] (
  [id] int IDENTITY(1,1) NOT NULL,
  [pe] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [creater] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [create_date] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [pid] int NULL,
  [detail_log_path] varchar(255) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_retail_ntx_automation_renew_cert_task] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Primary Key structure for table dh_retail_ntx_automation_renew_cert_task
-- ----------------------------
ALTER TABLE [dbo].[dh_retail_ntx_automation_renew_cert_task] ADD CONSTRAINT [PK__Table_1__3213E83F57CF83CC] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = OFF, ALLOW_PAGE_LOCKS = OFF)  
ON [PRIMARY]
GO

-- ----------------------------
-- Table structure for dh_retail_ntx_automation_renew_cert_task_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_ntx_automation_renew_cert_task_log]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_retail_ntx_automation_renew_cert_task_log]
GO

CREATE TABLE [dbo].[dh_retail_ntx_automation_renew_cert_task_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [task_id] int NULL,
  [task_type] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [log_date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [severity] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [log_info] varchar(8000) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_retail_ntx_automation_renew_cert_task_log] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Primary Key structure for table dh_retail_ntx_automation_renew_cert_task_log
-- ----------------------------
ALTER TABLE [dbo].[dh_retail_ntx_automation_renew_cert_task_log] ADD CONSTRAINT [PK__Table_2__3213E83F38672D59] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = OFF, ALLOW_PAGE_LOCKS = OFF)  
ON [PRIMARY]
GO



BEGIN TRANSACTION

IF EXISTS (
    SELECT * FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_rotate_password_log')
)
BEGIN
    EXEC sp_rename 'dh_retail_ntx_rotate_password_log', 'dh_retail_ntx_automation_rotate_password_log'
END

IF EXISTS (
    SELECT * FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_rotate_password_task')
)
BEGIN
    EXEC sp_rename 'dh_retail_ntx_rotate_password_task', 'dh_retail_ntx_automation_rotate_password_task'
END

COMMIT TRANSACTION


################################################################
###                                                          ###
###             	      2024-04-22 CURFU1   		             ###
###                                                          ###
###                                                          ###
################################################################
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_retail_ntx_auto_maintenance') )
	CREATE TABLE [dbo].[dh_retail_ntx_auto_maintenance] (
  [id] int IDENTITY(1,1) NOT NULL,
  [pe_id] int NOT NULL,
  [pe_name] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [pc] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [lock_id] int NULL,
  [last_runtime] int NULL,
  [next_runtime] int NULL,
  [benchmark_id] int NULL,
  )
GO

IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_retail_ntx_auto_maintenance_lock') )
	CREATE TABLE [dbo].[dh_retail_ntx_auto_maintenance_lock] (
  [id] int IDENTITY(1,1) NOT NULL,
  [pe_id] int NOT NULL,
  [start_time] int NULL,
  [end_time] int NULL,
  [creater] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [description] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  )
GO

insert into dh_retail_ntx_auto_maintenance (pe_id,pe_name,lock_id,benchmark_id) select id as pe_id ,name as pe_name ,0 as lock_id ,0 as benchmark_id  from dh_retail_ntx_pe

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_renew_cert_task')
	and  name = 'detail_log_path'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_renew_cert_task.detail_log_path', 'detaillogpath', 'COLUMN';
  END
GO

################################################################
###                                                          ###
###             	      2024-05-10 RAYGU1   		             ###
###                                                          ###
###                                                          ###
################################################################
-- ----------------------------
-- Table structure for dh_wh_ntx_pc
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_wh_ntx_pc]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_wh_ntx_pc]
GO

CREATE TABLE [dbo].[dh_wh_ntx_pc] (
  [id] int IDENTITY(1,1) NOT NULL,
  [fqdn] varchar(50) COLLATE Latin1_General_CI_AS NOT NULL,
  [lcm_version] varchar(200) COLLATE Latin1_General_CI_AS NULL,
  [calm_version] varchar(200) COLLATE Latin1_General_CI_AS NULL,
  [cmu_version] varchar(200) COLLATE Latin1_General_CI_AS NULL,
  [epsilon_version] varchar(200) COLLATE Latin1_General_CI_AS NULL,
  [flowsecurity_Version] varchar(200) COLLATE Latin1_General_CI_AS NULL,
  [licensing_version] varchar(200) COLLATE Latin1_General_CI_AS NULL,
  [ncc_version] varchar(200) COLLATE Latin1_General_CI_AS NULL,
  [pc_version] varchar(200) COLLATE Latin1_General_CI_AS NULL,
  [uuid] varchar(200) COLLATE Latin1_General_CI_AS NULL,
  [cluster_number] int NULL,
  [ip] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [tier] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [domain] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [darksite] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [central_pe_fqdn] varchar(200) COLLATE Latin1_General_CI_AS NULL,
  [service_account] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [cert_expiry_date] varchar(100) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_wh_ntx_pc] SET (LOCK_ESCALATION = TABLE)
GO

-- ----------------------------
-- Uniques structure for table dh_wh_ntx_pc
-- ----------------------------
ALTER TABLE [dbo].[dh_wh_ntx_pc] ADD CONSTRAINT [UQ__dh_retai__33713419A8E72C30] UNIQUE NONCLUSTERED ([fqdn] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table dh_wh_ntx_pc
-- ----------------------------
ALTER TABLE [dbo].[dh_wh_ntx_pc] ADD CONSTRAINT [PK__dh_retai__3213E83F3362262D_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

-- ----------------------------
-- Table structure for dh_wh_ntx_pe
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_wh_ntx_pe]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_wh_ntx_pe]
GO

CREATE TABLE [dbo].[dh_wh_ntx_pe] (
  [id] int IDENTITY(1,1) NOT NULL,
  [name] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [fqdn] varchar(100) COLLATE Latin1_General_CI_AS NOT NULL,
  [country_code] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [site_code] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [node_number] int NULL,
  [vm_number] int NULL,
  [cvm_number] int NULL,
  [aos_version] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [ahv_version] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [uuid] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [remote_site] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [remote_backup_number] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [prism] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [timezone] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [total_memory] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [total_storage] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [total_cpu] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [pc_id] int NULL,
  [foundation_version] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [lcm_version] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [ncc_version] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [spp_version] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [license_category] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [license_class] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [license_cores_capacity] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [license_flash_capacity] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [license_hdd_capacity] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [license_cores_licensed] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [license_flash_licensed] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [license_hdd_licensed] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [cert_expiry_date] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [is_central_pe] bit NULL,
  [site_type] varchar(255) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_wh_ntx_pe] SET (LOCK_ESCALATION = TABLE)
GO

-- ----------------------------
-- Uniques structure for table dh_wh_ntx_pe
-- ----------------------------
ALTER TABLE [dbo].[dh_wh_ntx_pe] ADD CONSTRAINT [UQ__dh_retai__3371341990A6F93B] UNIQUE NONCLUSTERED ([fqdn] ASC)
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO


-- ----------------------------
-- Primary Key structure for table dh_wh_ntx_pe
-- ----------------------------
ALTER TABLE [dbo].[dh_wh_ntx_pe] ADD CONSTRAINT [PK__dh_retai__3213E83F68B26F09_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

-- ----------------------------
-- Add column 'backup_bandwidth' for table dh_wh_ntx_pe
-- ----------------------------
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'backup_bandwidth')

	BEGIN
		ALTER TABLE dh_retail_ntx_pe  ADD backup_bandwidth VARCHAR(50) NULL;
	END
GO

-- ----------------------------
-- Add column 'darksite_bandwidth' for table dh_wh_ntx_pe
-- ----------------------------
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'darksite_bandwidth')

	BEGIN
		ALTER TABLE dh_retail_ntx_pe  ADD darksite_bandwidth VARCHAR(50) NULL;
	END
GO

################################################################
###                                                          ###
###             	      2024-05-20 RAYGU1   		         ###
###                                                          ###
###                                                          ###
################################################################
-- ----------------------------
-- Table structure for dh_wh_ntx_host
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_wh_ntx_host]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_wh_ntx_host]
GO

CREATE TABLE [dbo].[dh_wh_ntx_host] (
  [id] int IDENTITY(1,1) NOT NULL,
  [name] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [uuid] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [pe_name] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [pe_fqdn] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [pe_uuid] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [disk_number] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [sn] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [model] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [memory] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [cpu_core_number] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [cpu_model] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [ahv_ip] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [cvm_ip] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [ipmi_ip] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [ipmi_version] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [pe_id] int NULL,
  [ilo_version] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [bios_version] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [controller_version] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [controller_model] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [nic0_uuid] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic0_mac] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic0_speed] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic0_mtu] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic0_sw_device] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic0_sw_port] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic0_sw_vendor] varchar(500) COLLATE Latin1_General_CI_AS NULL,
  [nic0_sw_vlan] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic1_uuid] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic1_mac] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic1_speed] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic1_mtu] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic1_sw_device] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic1_sw_port] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [nic1_sw_vendor] varchar(500) COLLATE Latin1_General_CI_AS NULL,
  [nic1_sw_vlan] varchar(100) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_wh_ntx_host] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Primary Key structure for table dh_wh_ntx_host
-- ----------------------------
ALTER TABLE [dbo].[dh_wh_ntx_host] ADD CONSTRAINT [PK__dh_retai__3213E83FF1B4091D_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

-- ----------------------------
-- Table structure for dh_wh_ntx_vm
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_wh_ntx_vm]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_wh_ntx_vm]
GO

CREATE TABLE [dbo].[dh_wh_ntx_vm] (
  [id] int IDENTITY(1,1) NOT NULL,
  [name] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [uuid] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [pe_name] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [pe_fqdn] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [pe_uuid] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [host_name] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [host_uuid] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [description] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [os] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [type] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [disk] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [memory] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [cpu_core] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [power_state] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [ip] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [boot_type] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [network_vlan] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [is_cvm] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [pe_id] int NULL
)
GO

ALTER TABLE [dbo].[dh_wh_ntx_vm] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Primary Key structure for table dh_wh_ntx_vm
-- ----------------------------
ALTER TABLE [dbo].[dh_wh_ntx_vm] ADD CONSTRAINT [PK__dh_retai__3213E83FFD0082FE_copy1] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO


-- ----------------------------------------
--                                       --
--          2024-05-23 ZOEXU6            --
--                                       --
-- ----------------------------------------
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_auto_maintenance_benchmark')
	and  name = 'snapshot_expire_interval')

BEGIN
    ALTER TABLE dh_retail_ntx_auto_maintenance_benchmark  ADD snapshot_expire_interval int NULL;
END
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_auto_maintenance_benchmark')
	and  name = 'nighttime_capability_percentage')

BEGIN
    ALTER TABLE dh_retail_ntx_auto_maintenance_benchmark  ADD nighttime_capability_percentage int NULL;
END
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_auto_maintenance_benchmark')
	and  name = 'daytime_capability_percentage')

BEGIN
    ALTER TABLE dh_retail_ntx_auto_maintenance_benchmark  ADD daytime_capability_percentage int NULL;
END
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_auto_maintenance_benchmark')
	and  name = 'rs_bw_policy_start_time')

BEGIN
    ALTER TABLE dh_retail_ntx_auto_maintenance_benchmark  ADD rs_bw_policy_start_time VARCHAR(100) NULL;
END
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_auto_maintenance_benchmark')
	and  name = 'rs_bw_policy_end_time')

BEGIN
    ALTER TABLE dh_retail_ntx_auto_maintenance_benchmark  ADD rs_bw_policy_end_time VARCHAR(100) NULL;
END
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_auto_maintenance_benchmark')
	and  name = 'rs_compression_enabled')

BEGIN
    ALTER TABLE dh_retail_ntx_auto_maintenance_benchmark  ADD rs_compression_enabled bit NULL;
END

-- ----------------------------------------
--                                       --
--          2024-06-04 ZOEXU6            --
--                                       --
-- ----------------------------------------

CREATE TABLE dh_retail_nutanix_auto_maintenance_schedule (
	pc varchar(100) COLLATE Latin1_General_CI_AS NOT NULL,
	timezone varchar(100) COLLATE Latin1_General_CI_AS NULL,
	[day] varchar(100) COLLATE Latin1_General_CI_AS NULL,
	[hour] int NULL,
	[minute] int NULL,
	interval_min int NULL,
	CONSTRAINT dh_retail_nutanix_auto_maintenance_schedule_pk PRIMARY KEY (pc)
);

-- ----------------------------------------
--                                       --
--          2024-06-20 RAYGU1            --
--                                       --
-- ----------------------------------------
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'remote_site_last')

BEGIN
    ALTER TABLE dh_retail_ntx_pe  ADD remote_site_last VARCHAR(100) NULL;

END

-- ----------------------------------------
--                                       --
--          2024-07-01 ZOEXU6            --
--                                       --
-- ----------------------------------------

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('role')
	and  name = 'description')

	BEGIN
		ALTER TABLE role ADD description VARCHAR(100) NULL;
	END
GO


IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_workload_task')
	and  name = 'task_type')

	BEGIN
		ALTER TABLE dh_retail_ntx_workload_task ADD task_type VARCHAR(30) NULL;
	END
GO
update dh_retail_ntx_workload_task set task_type='CREATE_WORKLOAD';

-- ----------------------------------------
--          2024-07-08 ZOEXU6            --
--                                       --
-- ----------------------------------------
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_workload_task')
	and  name = 'detaillogpath'
)
BEGIN
  EXEC sp_rename 'dh_retail_ntx_workload_task.detaillogpath', 'detail_log_path', 'COLUMN';
END

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_workload_task')
	and  name = 'createdate'
)
BEGIN
  EXEC sp_rename 'dh_retail_ntx_workload_task.createdate', 'create_date', 'COLUMN';
END


-- ----------------------------------------
--                                       --
--          2024-07-10 HUNHE            --
--                                       --
-- ----------------------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_ntx_new_cluster_task]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_retail_ntx_new_cluster_task]
GO

CREATE TABLE [dbo].[dh_retail_ntx_new_cluster_task] (
  [id] int IDENTITY(1,1) NOT NULL,
  [pe_name] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [pc_fqdn] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [ahv_subnet] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [benchmark_id] int NULL,
  [creater] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [create_date] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [stage] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [detail_log_path] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [scan_oob] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [selected_oob] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [ahv_cvm_info] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [foundation_central_task_id] varchar(1) COLLATE Latin1_General_CI_AS NULL,
  [pid] int NULL
)
GO

ALTER TABLE [dbo].[dh_retail_ntx_new_cluster_task] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Primary Key structure for table dh_retail_ntx_new_cluster_task
-- ----------------------------
ALTER TABLE [dbo].[dh_retail_ntx_new_cluster_task] ADD CONSTRAINT [PK__dh_retai__3213E83F5E7DB3BE] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = OFF, ALLOW_PAGE_LOCKS = OFF)
ON [PRIMARY]
GO


-- ----------------------------
-- Table structure for dh_retail_ntx_new_cluster_task_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_ntx_new_cluster_task_log]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_retail_ntx_new_cluster_task_log]
GO

CREATE TABLE [dbo].[dh_retail_ntx_new_cluster_task_log] (
  [id] int Identity(1,1) NOT NULL,
  [task_id] int NULL,
  [task_type] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [log_date] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [severity] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [log_info] varchar(255) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_retail_ntx_new_cluster_task_log] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Primary Key structure for table dh_retail_ntx_new_cluster_task_log
-- ----------------------------
ALTER TABLE [dbo].[dh_retail_ntx_new_cluster_task_log] ADD CONSTRAINT [PK__dh_retai__3213E83F03924708] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = OFF, ALLOW_PAGE_LOCKS = OFF)
ON [PRIMARY]
GO

-- ----------------------------------------
--          2024-07-18 RAYGU1            --
--                                       --
-- ----------------------------------------
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_aos_lcm_task')
	and  name = 'detaillogpath'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_aos_lcm_task.detaillogpath', 'detail_log_path', 'COLUMN';
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_aos_lcm_task_log')
	and  name = 'loginfo'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_aos_lcm_task_log.loginfo', 'log_info', 'COLUMN';
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_renew_cert_task')
	and  name = 'detaillogpath'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_renew_cert_task.detaillogpath', 'detail_log_path', 'COLUMN';
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_renew_cert_task_log')
	and  name = 'loginfo'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_renew_cert_task_log.loginfo', 'log_info', 'COLUMN';
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_spp_lcm_task')
	and  name = 'detaillogpath'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_spp_lcm_task.detaillogpath', 'detail_log_path', 'COLUMN';
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_spp_lcm_task_log')
	and  name = 'loginfo'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_spp_lcm_task_log.loginfo', 'log_info', 'COLUMN';
  END
GO

/*
change detaillogpath -> detail_log_path, loginfo -> log_info, logdate -> log_date:
	dh_retail_ntx_automation_aos_lcm_task
	dh_retail_ntx_automation_aos_lcm_task_log
	dh_retail_ntx_automation_renew_cert_task
	dh_retail_ntx_automation_renew_cert_task_log
	dh_retail_ntx_automation_spp_lcm_task
    dh_retail_ntx_automation_spp_lcm_task_log
*/

-- ----------------------------------------
--                                       --
--          2024-07-17 SHTAO8            --
--                                       --
-- ----------------------------------------

IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_compensation_gain') )
	CREATE TABLE [dbo].[dh_compensation_gain] (
  [id] int IDENTITY(1,1) NOT NULL,
  [start_date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [end_date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [username] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [reason] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [total_hours] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [times_1_hours] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [times_1p5_hours] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [times_2_hours] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [gain_hours] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [detail] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  )
GO
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_compensation_use') )
	CREATE TABLE [dbo].[dh_compensation_use] (
  [id] int IDENTITY(1,1) NOT NULL,
  [start_date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [end_date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [username] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [reason] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [use_hours] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [detail] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  )
GO
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('users')
	and  name = 'location')

BEGIN
	ALTER TABLE users add location VARCHAR(55) NULL;
END

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_aos_lcm_task_log')
	and  name = 'log_date'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_aos_lcm_task_log.log_date', 'logdate', 'COLUMN';
  END
GO

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_automation_aos_lcm_task_log')
	and  name = 'log_info'
)
  BEGIN
	  EXEC sp_rename 'dh_retail_ntx_automation_aos_lcm_task_log.log_info', 'loginfo', 'COLUMN';
  END
GO

-- ----------------------------------------
--          2024-07-17 ZOEXU6            --
--                                       --
-- ----------------------------------------
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task_log')
	and  name = 'stage'
)
BEGIN
  ALTER TABLE dh_retail_ntx_new_cluster_task_log  ADD stage INTEGER NULL;
END

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task')
	and  name = 'stage_status'
)
BEGIN
  ALTER TABLE dh_retail_ntx_new_cluster_task  ADD stage_status VARCHAR(50) NULL;
END

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task')
	and  name = 'ahv_cvm_subnet_id'
)
BEGIN
  ALTER TABLE dh_retail_ntx_new_cluster_task  ADD ahv_cvm_subnet_id VARCHAR(50) NULL;
END

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task')
	and  name = 'oob_subnet_id'
)
BEGIN
  ALTER TABLE dh_retail_ntx_new_cluster_task  ADD oob_subnet_id VARCHAR(50) NULL;
END


-- ----------------------------------------
--          2024-07-24 ZOEXU6            --
--                                       --
-- ----------------------------------------
-- add column "subnet_ip" into table `dh_retail_ntx_workload_task`
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_workload_task')
	and  name = 'subnet_ip'
)
BEGIN
  ALTER TABLE dh_retail_ntx_workload_task ADD subnet_ip VARCHAR(50) NULL;
END

-- ----------------------------------------
--          2024-07-30 ZOEXU6            --
--                                       --
-- ----------------------------------------
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task_log')
	and  name = 'log_info'
)
BEGIN
  ALTER TABLE dbo.dh_retail_ntx_new_cluster_task_log ALTER COLUMN log_info varchar(8000) COLLATE Latin1_General_CI_AS NULL;
END

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task')
	and  name = 'pe_ip'
)
BEGIN
  ALTER TABLE dh_retail_ntx_new_cluster_task  ADD pe_ip VARCHAR(255) NULL;
END

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task')
	and  name = 'ahv_cvm_oob_mapping'
)
BEGIN
  ALTER TABLE dh_retail_ntx_new_cluster_task  ADD ahv_cvm_oob_mapping VARCHAR(1000) NULL;
END
-- ----------------------------------------
--          2024-07-31 Hunter            --
--                                       --
-- ----------------------------------------


IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pc')
	and  name = 'oneview_region'
)
BEGIN
  ALTER TABLE dh_retail_ntx_pc  ADD oneview_region VARCHAR(50) NULL;
END


-- ----------------------------------------
--          2024-08-08 ZOEXU6            --
--                                       --
-- ----------------------------------------
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task')
	and  name = 'foundation_central_task_id'
)
BEGIN
    alter table dh_retail_ntx_new_cluster_task drop column foundation_central_task_id;
END

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task')
	and  name = 'cluster_uuid'
)
BEGIN
  ALTER TABLE dh_retail_ntx_new_cluster_task  ADD cluster_uuid VARCHAR(255) NULL;
END

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task')
	and  name = 'data_service_ip'
)
BEGIN
  ALTER TABLE dh_retail_ntx_new_cluster_task  ADD data_service_ip VARCHAR(255) NULL;
END


-- ----------------------------
-- Change data type for Nutanix PE/VM/HOST
-- ----------------------------
UPDATE [dbo].[dh_retail_ntx_pe] SET [total_memory]=NULL, [total_storage]=NULL, [total_cpu]=NULL
GO

UPDATE [dbo].[dh_retail_ntx_host] SET [disk_number]=NULL, [memory]=NULL, [cpu_core_number]=NULL
GO

UPDATE [dbo].[dh_retail_ntx_vm] SET [memory]=NULL, [cpu_core]=NULL
GO

ALTER TABLE [dbo].[dh_retail_ntx_pe]
ALTER COLUMN [total_memory] INT NULL

ALTER TABLE [dbo].[dh_retail_ntx_pe]
ALTER COLUMN [total_storage] INT NULL

ALTER TABLE [dbo].[dh_retail_ntx_pe]
ALTER COLUMN [total_cpu] INT NULL

ALTER TABLE [dbo].[dh_retail_ntx_host]
ALTER COLUMN [disk_number] INT NULL

ALTER TABLE [dbo].[dh_retail_ntx_host]
ALTER COLUMN [memory] INT NULL

ALTER TABLE [dbo].[dh_retail_ntx_host]
ALTER COLUMN [cpu_core_number] INT NULL

ALTER TABLE [dbo].[dh_retail_ntx_vm]
ALTER COLUMN [memory] INT NULL

ALTER TABLE [dbo].[dh_retail_ntx_vm]
ALTER COLUMN [cpu_core] INT NULL

UPDATE [dbo].[dh_wh_ntx_pe] SET [total_memory]=NULL, [total_storage]=NULL, [total_cpu]=NULL
GO

UPDATE [dbo].[dh_wh_ntx_host] SET [disk_number]=NULL, [memory]=NULL, [cpu_core_number]=NULL
GO

UPDATE [dbo].[dh_wh_ntx_vm] SET [memory]=NULL, [cpu_core]=NULL
GO

ALTER TABLE [dbo].[dh_wh_ntx_pe]
ALTER COLUMN [total_memory] INT NULL

ALTER TABLE [dbo].[dh_wh_ntx_pe]
ALTER COLUMN [total_storage] INT NULL

ALTER TABLE [dbo].[dh_wh_ntx_pe]
ALTER COLUMN [total_cpu] INT NULL

ALTER TABLE [dbo].[dh_wh_ntx_host]
ALTER COLUMN [disk_number] INT NULL

ALTER TABLE [dbo].[dh_wh_ntx_host]
ALTER COLUMN [memory] INT NULL

ALTER TABLE [dbo].[dh_wh_ntx_host]
ALTER COLUMN [cpu_core_number] INT NULL

ALTER TABLE [dbo].[dh_wh_ntx_vm]
ALTER COLUMN [memory] INT NULL

ALTER TABLE [dbo].[dh_wh_ntx_vm]
ALTER COLUMN [cpu_core] INT NULL


-- ----------------------------------------
--          2024-08-08 IVAN              --
--                                       --
-- ----------------------------------------
-- ----------------------------
-- Table structure for dh_incident_handler_data
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_incident_handler_data]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_incident_handler_data]
GO

CREATE TABLE [dbo].[dh_incident_handler_data] (
  [id] int IDENTITY(1,1) NOT NULL,
  [number] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [description] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [work_note] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [task_type] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [creation_date] varchar(255) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_incident_handler_data] SET (LOCK_ESCALATION = TABLE)
GO

-- ----------------------------
-- Table structure for dh_incident_handler_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_incident_handler_log]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_incident_handler_log]
GO

CREATE TABLE [dbo].[dh_incident_handler_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [task_type] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [severity] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [log_details] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [creation_time ] varchar(255) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_incident_handler_log] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------------------
--                                       --
--           2024-09-06 Ray              --
--                                       --
-- ----------------------------------------
-- add column "default_bandwidth_limit_mbps" into table `dh_retail_ntx_pe`
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'default_bandwidth_limit_mbps'
)
BEGIN
  ALTER TABLE dh_retail_ntx_pe ADD default_bandwidth_limit_mbps FLOAT(53) NULL;
END

GO

-- add column "remote_site_runtime" into table `dh_retail_ntx_pe`
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'remote_site_runtime'
)
BEGIN
  ALTER TABLE dh_retail_ntx_pe ADD remote_site_runtime VARCHAR(50) NULL;
END

GO

-- ----------------------------------------
--                                       --
--          2024-09-17 SHTAO8            --
--                                       --
-- ----------------------------------------

IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_wh_ntx_move') )
	CREATE TABLE [dbo].[dh_wh_ntx_move] (
  [id] int IDENTITY(1,1) NOT NULL,
  [cluster] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [pe] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [createdate] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [movedevop] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [stagepreparation] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [stagecutover] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [datasyncstatus] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  [moveip] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [planuuid] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  [syncstatus] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  )
GO
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_wh_ntx_move_log') )
	CREATE TABLE [dbo].[dh_wh_ntx_move_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [taskid] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [tasktype] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [logdate] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [severity] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [loginfo] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  )
GO
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_wh_ntx_move_task') )
	CREATE TABLE [dbo].[dh_wh_ntx_move_task] (
  [id] int IDENTITY(1,1) NOT NULL,
  [pid] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [cluster] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [pe] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [createdate] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [datasyncstatus] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [creater] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [tasktype] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [detaillogpath] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  )
GO
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_wh_vmware_vms') )
	CREATE TABLE [dbo].[dh_wh_vmware_vms] (
  [id] int IDENTITY(1,1) NOT NULL,
  [version] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [name] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [folder_id] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [folder] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [resourcepool_id] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [persistent_id] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [used_space_gb] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [provisioned_space_gb] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [memory_total_gb] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [harestart_priority] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [num_cpu] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [cores_persocket] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [haisolation_response] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [guest_id] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [host_id] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [vmhost_name] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [power_state] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [guest_name] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [vm_id] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [create_date] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [status] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  )

-- ----------------------------------------
--                                       --
--           2024-10-22 Ray              --
--                                       --
-- ----------------------------------------
-- add column "nic*_xxx" into table `dh_wh_host`
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic2_uuid'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic2_uuid VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic2_mac'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic2_mac VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic2_speed'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic2_speed VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic2_mtu'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic2_mtu VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic2_sw_device'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic2_sw_device VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic2_sw_port'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic2_sw_port VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic2_sw_vendor'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic2_sw_vendor VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic2_sw_vlan'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic2_sw_vlan VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic3_uuid'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic3_uuid VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic3_mac'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic3_mac VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic3_speed'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic3_speed VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic3_mtu'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic3_mtu VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic3_sw_device'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic3_sw_device VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic3_sw_port'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic3_sw_port VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic3_sw_vendor'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic3_sw_vendor VARCHAR(100) NULL;
END

GO

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'nic3_sw_vlan'
)
BEGIN
  ALTER TABLE dh_wh_ntx_host ADD nic3_sw_vlan VARCHAR(100) NULL;
END

GO


-- ----------------------------------------
--                                       --
--         2024-10-30 ZOEXU6             --
--                                       --
-- ----------------------------------------
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('role_administration')
	and  name = 'view_vault'
)
BEGIN
  ALTER TABLE role_administration ADD view_vault VARCHAR(100) NULL;
END

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('role_administration')
	and  name = 'view_vault_scope'
)
BEGIN
  ALTER TABLE role_administration ADD view_vault_scope VARCHAR(8000) NULL;
END


-- ----------------------------------------
--                                       --
--         2024-10-30 Hunter             --
--                                       --
-- ----------------------------------------


IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_ntx_vm_action]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_retail_ntx_vm_action]
GO

CREATE TABLE [dbo].[dh_retail_ntx_vm_action] (
  [id] int IDENTITY(1,1) NOT NULL,
  [pe] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [vm_name] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [action_type] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [user_name] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [time] varchar(100) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_retail_ntx_vm_action] SET (LOCK_ESCALATION = TABLE)
GO

-- ----------------------------------------
--                                       --
--         2024-11-26 Ivan             --
--                                       --
-- ----------------------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_incident_handler_error_log]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_incident_handler_error_log]
GO

CREATE TABLE [dbo].[dh_incident_handler_error_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [number] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [description] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [error] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [handle_date] varchar(255) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_incident_handler_error_log] SET (LOCK_ESCALATION = TABLE)
GO

-------------------------------------------

IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_incident_handler_log]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_incident_handler_log]
GO

CREATE TABLE [dbo].[dh_incident_handler_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [task_type] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [severity] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [log_details] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [creation_time ] varchar(255) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_incident_handler_log] SET (LOCK_ESCALATION = TABLE)
GO

-- ----------------------------
-- Table structure for dh_incident_handler_unresolved_cases
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_incident_handler_unresolved_cases]') AND type IN ('U'))
 DROP TABLE [dbo].[dh_incident_handler_unresolved_cases]
GO

CREATE TABLE [dbo].[dh_incident_handler_unresolved_cases] (
  [id] int IDENTITY(1,1) NOT NULL,
  [number] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [description] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [handle_date] nvarchar(255) COLLATE Latin1_General_CI_AS NULL
)
GO

ALTER TABLE [dbo].[dh_incident_handler_unresolved_cases] SET (LOCK_ESCALATION = TABLE)
GO
-- ------------------------------
--                             --
--          v1.5.0.0           --
--                             --
-- ------------------------------
-- ----------------------------------------
--                                       --
--          2024-10-22 SHTAO8            --
--                                       --
-- ----------------------------------------

IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_up_feedback') )
	CREATE TABLE [dbo].[dh_up_feedback] (
  [id] int IDENTITY(1,1) NOT NULL,
  [name] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [comment] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  [solution] varchar(8000) COLLATE Latin1_General_CI_AS NULL,
  [star] varchar(55) COLLATE Latin1_General_CI_AS NULL,
  [anonymous] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  )
GO
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_up_visit') )
	CREATE TABLE [dbo].[dh_up_visit] (
  [id] int IDENTITY(1,1) NOT NULL,
  [page_name] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  [date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  )
GO
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_up_api_utilization') )
	CREATE TABLE [dbo].[dh_up_api_utilization] (
  [id] int IDENTITY(1,1) NOT NULL,
  [api_path] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [times] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  )
GO
IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_up_api_utilization_log') )
	CREATE TABLE [dbo].[dh_up_api_utilization_log] (
  [id] int IDENTITY(1,1) NOT NULL,
  [api_path] varchar(255) COLLATE Latin1_General_CI_AS NULL,
  [date] varchar(50) COLLATE Latin1_General_CI_AS NULL,
  [caller] varchar(100) COLLATE Latin1_General_CI_AS NULL,
  )
GO
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('role_administration')
	and  name = 'view_comment_scope'
)
BEGIN
  ALTER TABLE role_administration ADD view_comment_scope  VARCHAR(50) NULL;
END

GO
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('role_administration')
	and  name = 'view_comment'
)
BEGIN
  ALTER TABLE role_administration ADD view_comment VARCHAR(50) NULL;
END

GO
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('role_administration')
	and  name = 'edit_comment_scope'
)
BEGIN
  ALTER TABLE role_administration ADD edit_comment_scope  VARCHAR(50) NULL;
END

GO
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('role_administration')
	and  name = 'edit_comment'
)
BEGIN
  ALTER TABLE role_administration ADD edit_comment VARCHAR(50) NULL;
END

GO
INSERT INTO [dbo].[role_administration]([view_user], [view_user_scope], [view_role], [view_role_scope], [view_vault], [view_vault_scope], [view_comment_scope], [view_comment], [edit_comment_scope], [edit_comment]) VALUES ( 'empty', NULL, 'empty', NULL, 'empty', NULL, NULL, 'full', NULL, 'full');
INSERT INTO [dbo].[role]( [name], [role_dashboard], [role_pm], [role_ntx], [role_sli], [role_mkt], [create_date], [modify_date], [role_administration], [role_lcm], [description]) VALUES ('handle_comment', NULL, NULL, NULL, NULL, NULL, '2024-12-03 10:21:59.293333', NULL, NULL, 24, NULL);

-- ------------------------------
--                             --
--          v1.5.1.0           --
--                             --
-- ------------------------------
/*
########################################################
###                                                  ###
###     	      2024-12-23 Ray		             ###
###                                                  ###
########################################################
# add column bmk_id to table 'dh_retail_ntx_pc' and 'dh_retail_ntx_pe'
*/
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pc') 
	and  name = 'bmk_id')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pc  ADD bmk_id INT NULL;
	END
GO
IF not EXISTS(
	select * from syscolumns 
	where id  = object_id('dh_retail_ntx_pe') 
	and  name = 'bmk_id')
	
	BEGIN
		ALTER TABLE dh_retail_ntx_pe  ADD bmk_id INT NULL;
	END
GO

-- ----------------------------------------
--                                       --
--          2024-12-25 zoexu6            --
--                                       --
-- ----------------------------------------
CREATE TABLE
  dh_wh_ntx_new_cluster_task (
    id int IDENTITY(1, 1) NOT NULL,
    pe_name varchar(255) NULL,
    pc_fqdn varchar(255) NULL,
    ahv_subnet varchar(255) NULL,
    benchmark_id int NULL,
    creater varchar(255) NULL,
    create_date varchar(255) NULL,
    stage varchar(255) NULL,
    status varchar(255) NULL,
    detail_log_path varchar(255) NULL,
    scan_oob varchar(255) NULL,
    selected_oob varchar(255) NULL,
    ahv_cvm_free_ip_list varchar(255) NULL,
    pid int NULL,
    stage_status varchar(50) NULL,
    ahv_cvm_subnet_id varchar(50) NULL,
    oob_subnet_id varchar(50) NULL,
    pe_ip varchar(100) NULL,
    ahv_cvm_oob_mapping varchar(1000) NULL,
    data_service_ip varchar(255) NULL,
    cluster_uuid varchar(255) NULL,
    is_metro_task bit NOT NULL,
    metro_task_id int NULL,
    room_type varchar(255) NULL,
  )
ALTER TABLE
  dh_wh_ntx_new_cluster_task
ADD
  CONSTRAINT PK_New_Cluster_Task PRIMARY KEY (id)

CREATE TABLE
  dh_wh_ntx_new_cluster_task_log (
    id int IDENTITY(1, 1) NOT NULL,
    task_id int NULL,
    task_type varchar(255) NULL,
    log_date varchar(255) NULL,
    severity varchar(255) NULL,
    log_info varchar(MAX) NULL,
    stage int NULL,
  )
ALTER TABLE
  dh_wh_ntx_new_cluster_task_log
ADD
  CONSTRAINT PK_New_Cluster_Task_Log PRIMARY KEY (id)


-- ----------------------------------------
--                                       --
--          2025-01-03 zoexu6            --
--                                       --
-- ----------------------------------------
ALTER TABLE dh_retail_ntx_new_cluster_task ALTER COLUMN stage INT;
ALTER TABLE dh_wh_ntx_new_cluster_task ALTER COLUMN stage INT;
ALTER TABLE dh_retail_ntx_new_cluster_task ALTER COLUMN scan_oob VARCHAR(2000);
ALTER TABLE dh_wh_ntx_new_cluster_task ALTER COLUMN scan_oob VARCHAR(2000);

-- ----------------------------------------
--                                       --
--          2025-01-17 Hunter            --
--                                       --
-- ----------------------------------------
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_new_cluster_task')
	and  name = 'step'
)
BEGIN
  ALTER TABLE dh_wh_ntx_new_cluster_task ADD step INT NULL;

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_new_cluster_task')
	and  name = 'step'
)
BEGIN
  ALTER TABLE dh_retail_ntx_new_cluster_task ADD step INT NULL;
END


-- ----------------------------------------
--                                       --
--          2025-01-20 Ray               --
--                                       --
-- ----------------------------------------
-- ----------------------------
-- Add column 'facility_type' for table 'dh_retail_nutanix_auto_maintenance_schedule'
-- ----------------------------

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_nutanix_auto_maintenance_schedule')
	and  name = 'facility_type'
)
BEGIN
  ALTER TABLE dh_retail_nutanix_auto_maintenance_schedule ADD facility_type VARCHAR(50) NULL;

-- ----------------------------
-- Re-initialize data for table 'dh_retail_nutanix_auto_maintenance_schedule'
-- ----------------------------
TRUNCATE TABLE dh_retail_nutanix_auto_maintenance_schedule
INSERT INTO [dh_retail_nutanix_auto_maintenance_schedule] ([day], [facility_type], [hour], [interval_min], [minute], [pc], [timezone]) VALUES ('thu,fri', 'retail', 23, 10, 0, 'ssp-apac-ntx.ikea.com', 'Asia/Tokyo');
INSERT INTO [dh_retail_nutanix_auto_maintenance_schedule] ([day], [facility_type], [hour], [interval_min], [minute], [pc], [timezone]) VALUES ('thu,fri', 'warehouse', 23, 10, 0, 'ssp-apac-wiab-ntx.ikea.com', 'Asia/Tokyo');
INSERT INTO [dh_retail_nutanix_auto_maintenance_schedule] ([day], [facility_type], [hour], [interval_min], [minute], [pc], [timezone]) VALUES ('mon,tue', 'retail', 23, 10, 0, 'ssp-china-ntx.ikea.com', 'Asia/Shanghai');
INSERT INTO [dh_retail_nutanix_auto_maintenance_schedule] ([day], [facility_type], [hour], [interval_min], [minute], [pc], [timezone]) VALUES ('mon,tue', 'warehouse', 23, 10, 0, 'ssp-china-wiab-ntx.ikea.com', 'Asia/Shanghai');
INSERT INTO [dh_retail_nutanix_auto_maintenance_schedule] ([day], [facility_type], [hour], [interval_min], [minute], [pc], [timezone]) VALUES ('tue,wed', 'retail', 20, 2, 0, 'ssp-eu1-ntx.ikea.com', 'Europe/Paris');
INSERT INTO [dh_retail_nutanix_auto_maintenance_schedule] ([day], [facility_type], [hour], [interval_min], [minute], [pc], [timezone]) VALUES ('tue,wed', 'retail', 21, 2, 0, 'ssp-eu-ntx.ikea.com', 'Europe/Paris');
INSERT INTO [dh_retail_nutanix_auto_maintenance_schedule] ([day], [facility_type], [hour], [interval_min], [minute], [pc], [timezone]) VALUES ('tue,wed', 'warehouse', 22, 2, 0, 'ssp-eu-wiab-ntx.ikea.com', 'Europe/Paris');
INSERT INTO [dh_retail_nutanix_auto_maintenance_schedule] ([day], [facility_type], [hour], [interval_min], [minute], [pc], [timezone]) VALUES ('wed,thu', 'retail', 23, 5, 0, 'ssp-na-ntx.ikea.com', 'America/Los_Angeles');
INSERT INTO [dh_retail_nutanix_auto_maintenance_schedule] ([day], [facility_type], [hour], [interval_min], [minute], [pc], [timezone]) VALUES ('wed,thu', 'warehouse', 23, 10, 0, 'ssp-na-wiab-ntx.ikea.com', 'America/Los_Angeles');
INSERT INTO [dh_retail_nutanix_auto_maintenance_schedule] ([day], [facility_type], [hour], [interval_min], [minute], [pc], [timezone]) VALUES ('mon,tue', 'retail', 23, 10, 0, 'ssp-ppe-ntx.ikea.com', 'Europe/Paris');

-- ----------------------------
-- Add column 'bmk_id' for table 'dh_wh_ntx_pc'
-- ----------------------------
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_pc')
	and  name = 'bmk_id')

	BEGIN
		ALTER TABLE dh_wh_ntx_pc ADD bmk_id int NULL;
	END
GO

-- ----------------------------
-- Add column 'bmk_id' for table 'dh_wh_ntx_pe'
-- ----------------------------
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_pe')
	and  name = 'bmk_id')

	BEGIN
		ALTER TABLE dh_wh_ntx_pe ADD bmk_id int NULL;
	END
GO

-- ----------------------------
-- Remove useless tables start with 'dh_retail_benchmark'
-- ----------------------------
-- Remove table 'dh_retail_benchmark'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark

-- Remove table 'dh_retail_benchmark_backup'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_backup]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_backup

-- Remove table 'dh_retail_benchmark_brand'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_brand]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_brand

-- Remove table 'dh_retail_benchmark_certificate'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_certificate]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_certificate

-- Remove table 'dh_retail_benchmark_cluster_network'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_cluster_network]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_cluster_network

-- Remove table 'dh_retail_benchmark_deployment'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_deployment]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_deployment

-- Remove table 'dh_retail_benchmark_desired_state'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_desired_state]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_desired_state

-- Remove table 'dh_retail_benchmark_endpoint'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_endpoint]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_endpoint

-- Remove table 'dh_retail_benchmark_eula'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_eula]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_eula

-- Remove table 'dh_retail_benchmark_firewall_rules'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_firewall_rules]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_firewall_rules

-- Remove table 'dh_retail_benchmark_group_mapping'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_group_mapping]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_group_mapping

-- Remove table 'dh_retail_benchmark_lcm_activity'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_lcm_activity]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_lcm_activity

-- Remove table 'dh_retail_benchmark_lcm_component'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_lcm_component]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_lcm_component

-- Remove table 'dh_retail_benchmark_lcm_version'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_lcm_version]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_lcm_version

-- Remove table 'dh_retail_benchmark_limits'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_limits]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_limits

-- Remove table 'dh_retail_benchmark_password_rotation'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_password_rotation]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_password_rotation

-- Remove table 'dh_retail_benchmark_pm_activity'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_pm_activity]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_pm_activity

-- Remove table 'dh_retail_benchmark_recipient'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_recipient]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_recipient

-- Remove table 'dh_retail_benchmark_scheduler'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_scheduler]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_scheduler

-- Remove table 'dh_retail_benchmark_site'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_site]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_site

-- Remove table 'dh_retail_benchmark_storage'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_storage]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_storage

-- Remove table 'dh_retail_benchmark_systems'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_systems]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_systems

-- Remove table 'dh_retail_benchmark_timer'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_timer]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_timer

-- Remove table 'dh_retail_benchmark_vault'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_vault]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_vault

-- Remove table 'dh_retail_benchmark_vlan_config'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_retail_benchmark_vlan_config]') AND type IN ('U'))
	DROP TABLE dh_retail_benchmark_vlan_config

-- Update fqdn of Wiab DT PC
UPDATE dh_wh_ntx_pc SET fqdn = 'wiab-dt-ntx.ikeadt.com' WHERE fqdn = 'dsse999-nxp000.ikeadt.com'
UPDATE dh_wh_ntx_pe SET prism = 'wiab-dt-ntx.ikeadt.com' WHERE prism = 'dsse999-nxp000.ikeadt.com'

-- ----------------------------------------
--                                       --
--          2025-01-21 zoexu6            --
--                                       --
-- ----------------------------------------
-- New column pe_name_a, pe_name_b, room_a_task_id, room_b_task_id for table `dh_wh_ntx_new_cluster_task`
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_new_cluster_task')
	and  name = 'pe_name_a'
)
BEGIN
  ALTER TABLE dh_wh_ntx_new_cluster_task ADD pe_name_a varchar(255) NULL;

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_new_cluster_task')
	and  name = 'pe_name_b'
)
BEGIN
  ALTER TABLE dh_wh_ntx_new_cluster_task ADD pe_name_b varchar(255) NULL;

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_new_cluster_task')
	and  name = 'room_a_task_id'
)
BEGIN
  ALTER TABLE dh_wh_ntx_new_cluster_task ADD room_a_task_id varchar(255) NULL;

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_new_cluster_task')
	and  name = 'room_b_task_id'
)
BEGIN
  ALTER TABLE dh_wh_ntx_new_cluster_task ADD room_b_task_id varchar(255) NULL;
-- ----------------------------------------
--                                       --
--          2025-01-21 zoexu6            --
--                                       --
-- ----------------------------------------
-- Complete DDL for table 'dh_retail_ntx_new_cluster_task'
DROP TABLE dh_retail_ntx_new_cluster_task;
CREATE TABLE dh_retail_ntx_new_cluster_task (
	id int IDENTITY(1,1) NOT NULL,
	pe_name varchar(255) COLLATE Latin1_General_CI_AS NULL,
	pc_fqdn varchar(255) COLLATE Latin1_General_CI_AS NULL,
	ahv_subnet varchar(255) COLLATE Latin1_General_CI_AS NULL,
	benchmark_id int NULL,
	creater varchar(255) COLLATE Latin1_General_CI_AS NULL,
	create_date varchar(255) COLLATE Latin1_General_CI_AS NULL,
	stage int NULL,
	status varchar(255) COLLATE Latin1_General_CI_AS NULL,
	detail_log_path varchar(255) COLLATE Latin1_General_CI_AS NULL,
	scan_oob varchar(255) COLLATE Latin1_General_CI_AS NULL,
	selected_oob varchar(255) COLLATE Latin1_General_CI_AS NULL,
	pid int NULL,
	stage_status varchar(50) COLLATE Latin1_General_CI_AS NULL,
	ahv_cvm_subnet_id varchar(50) COLLATE Latin1_General_CI_AS NULL,
	oob_subnet_id varchar(50) COLLATE Latin1_General_CI_AS NULL,
	pe_ip varchar(100) COLLATE Latin1_General_CI_AS NULL,
	ahv_cvm_oob_mapping varchar(1000) COLLATE Latin1_General_CI_AS NULL,
	data_service_ip varchar(255) COLLATE Latin1_General_CI_AS NULL,
	cluster_uuid varchar(255) COLLATE Latin1_General_CI_AS NULL,
	step int NULL,
	CONSTRAINT PK__dh_retai__3213E83F5E7DB3BE PRIMARY KEY (id)
);

-- Complete DDL for table 'dh_wh_ntx_new_cluster_task'
DROP TABLE dh_wh_ntx_new_cluster_task;
CREATE TABLE dh_wh_ntx_new_cluster_task (
	id int IDENTITY(1,1) NOT NULL,
	pe_name varchar(255) COLLATE Latin1_General_CI_AS NULL,
	pe_name_a varchar(255) COLLATE Latin1_General_CI_AS NULL,
	pe_name_b varchar(255) COLLATE Latin1_General_CI_AS NULL,
	pc_fqdn varchar(255) COLLATE Latin1_General_CI_AS NULL,
    is_metro_task bit NOT NULL,
	metro_task_id int NULL,
	room_a_task_id int NULL,
	room_b_task_id int NULL,
	room_type varchar(255) COLLATE Latin1_General_CI_AS NULL,
	ahv_subnet varchar(255) COLLATE Latin1_General_CI_AS NULL,
	benchmark_id int NULL,
	creater varchar(255) COLLATE Latin1_General_CI_AS NULL,
	create_date varchar(255) COLLATE Latin1_General_CI_AS NULL,
	stage int NULL,
	status varchar(255) COLLATE Latin1_General_CI_AS NULL,
	detail_log_path varchar(255) COLLATE Latin1_General_CI_AS NULL,
	scan_oob varchar(5000) COLLATE Latin1_General_CI_AS NULL,
	selected_oob varchar(255) COLLATE Latin1_General_CI_AS NULL,
	pid int NULL,
	stage_status varchar(50) COLLATE Latin1_General_CI_AS NULL,
	ahv_cvm_subnet_id varchar(50) COLLATE Latin1_General_CI_AS NULL,
	oob_subnet_id varchar(50) COLLATE Latin1_General_CI_AS NULL,
	pe_ip varchar(100) COLLATE Latin1_General_CI_AS NULL,
	ahv_cvm_oob_mapping varchar(5000) COLLATE Latin1_General_CI_AS NULL,
	data_service_ip varchar(255) COLLATE Latin1_General_CI_AS NULL,
	cluster_uuid varchar(255) COLLATE Latin1_General_CI_AS NULL,
	step int NULL,
	CONSTRAINT PK_New_Cluster_Task PRIMARY KEY (id)
);

-- ----------------------------------------
--                                       --
--          2025-01-20 EPJIA             --
--                                       --
-- ----------------------------------------

IF not EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_incident_analysis') )
    CREATE TABLE [dbo].[dh_incident_analysis] (
        [id] int IDENTITY(1,1) NOT NULL,
        [inc_num] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [type] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [site] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [model] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [assigned] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [creation_time] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [resolved_time] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [duration_time] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [timestamp] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [priority] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [human_check] varchar(50) COLLATE Latin1_General_CI_AS NULL,
        [auto_resolved] varchar(50) COLLATE Latin1_General_CI_AS NULL,
        [short_description] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        [note] varchar(255) COLLATE Latin1_General_CI_AS NULL,
        )

-- ----------------------------------------
--                                       --
--          2025-02-19 ZOEXU6            --
--                                       --
-- ----------------------------------------

IF NOT EXISTS (
    SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_metro_vg_task')
)
CREATE TABLE dh_metro_vg_task (
	id int IDENTITY(1,1) NOT NULL,
	pid int NULL,
	pe varchar(100) COLLATE Latin1_General_CI_AS NULL,
	prism varchar(255) COLLATE Latin1_General_CI_AS NULL,
    vm_names varchar(255) COLLATE Latin1_General_CI_AS NULL,
	create_date varchar(100) COLLATE Latin1_General_CI_AS NULL,
	status varchar(100) COLLATE Latin1_General_CI_AS NULL,
	creater varchar(100) COLLATE Latin1_General_CI_AS NULL,
	detail_log_path varchar(255) COLLATE Latin1_General_CI_AS NULL,
	CONSTRAINT dh_metro_vg_task_pk PRIMARY KEY (id)
);
IF NOT EXISTS (
    SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID('dh_metro_vg_task_log')
)
CREATE TABLE dh_metro_vg_task_log (
	id int IDENTITY(1,1) NOT NULL,
	log_date varchar(50) COLLATE Latin1_General_CI_AS NULL,
	severity varchar(50) COLLATE Latin1_General_CI_AS NULL,
	log_info varchar(MAX) COLLATE Latin1_General_CI_AS NULL,
	task_id int NULL,
	CONSTRAINT dh_metro_vg_task_log_pk PRIMARY KEY (id)
);

-- ----------------------------------------
--                                       --
--          2025-02-21 HUNHE             --
--                                       --
-- ----------------------------------------
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_pc')
	and  name = 'oneview_region'
)
BEGIN
  ALTER TABLE dh_wh_ntx_pc  ADD oneview_region VARCHAR(50) NULL;
END
-- ----------------------------------------
--                                       --
--          2025-03-12 RAYGU1            --
--                                       --
-- ----------------------------------------
IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pc')
	and  name = 'last_update')

BEGIN
    ALTER TABLE dh_retail_ntx_pc ADD last_update VARCHAR(50) NULL;
END

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'last_update')

BEGIN
    ALTER TABLE dh_retail_ntx_pe ADD last_update VARCHAR(50) NULL;
END

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_host')
	and  name = 'last_update')

BEGIN
    ALTER TABLE dh_retail_ntx_host ADD last_update VARCHAR(50) NULL;
END

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_vm')
	and  name = 'last_update')

BEGIN
    ALTER TABLE dh_retail_ntx_vm ADD last_update VARCHAR(50) NULL;
END

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_pc')
	and  name = 'last_update')

BEGIN
    ALTER TABLE dh_wh_ntx_pc ADD last_update VARCHAR(50) NULL;
END

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_pe')
	and  name = 'last_update')

BEGIN
    ALTER TABLE dh_wh_ntx_pe ADD last_update VARCHAR(50) NULL;
END

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_host')
	and  name = 'last_update')

BEGIN
    ALTER TABLE dh_wh_ntx_host ADD last_update VARCHAR(50) NULL;
END

IF not EXISTS(
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_vm')
	and  name = 'last_update')

BEGIN
    ALTER TABLE dh_wh_ntx_vm ADD last_update VARCHAR(50) NULL;
END


-- ----------------------------------------
--                                       --
--          2025-03-27 HUNHE            --
--                                       --
-- ----------------------------------------
IF NOT EXISTS (
    SELECT *
    FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_automation_aos_lcm_task')
    AND name = 'facility_type'
)
BEGIN
    ALTER TABLE dh_retail_ntx_automation_aos_lcm_task ADD facility_type VARCHAR(100) NULL;
-- ---------------------------------------------------------------------------
--                                                                          --
--          2025-03-17 ZOEXU6                                               --
--  Rename password, certificate, dsc table names to adapt Wiab             --
--                                                                          --
-- ---------------------------------------------------------------------------
IF EXISTS (
    SELECT * FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_automation_rotate_password_task')
)
BEGIN
    EXEC sp_rename 'dh_retail_ntx_automation_rotate_password_task', 'dh_ntx_automation_rotate_password_task'
END

IF EXISTS (
    SELECT * FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_automation_rotate_password_log')
)
BEGIN
    EXEC sp_rename 'dh_retail_ntx_automation_rotate_password_log', 'dh_ntx_automation_rotate_password_log'
END
--------------------------------------------------------------------------------
IF EXISTS (
    SELECT * FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_automation_renew_cert_task')
)
BEGIN
    EXEC sp_rename 'dh_retail_ntx_automation_renew_cert_task', 'dh_ntx_automation_renew_cert_task'
END

IF EXISTS (
    SELECT * FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_automation_renew_cert_task_log')
)
BEGIN
    EXEC sp_rename 'dh_retail_ntx_automation_renew_cert_task_log', 'dh_ntx_automation_renew_cert_task_log'
END
--------------------------------------------------------------------------------
IF EXISTS (
    SELECT * FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_automation_dsc_task')
)
BEGIN
    EXEC sp_rename 'dh_retail_ntx_automation_dsc_task', 'dh_ntx_automation_dsc_task'
END

IF EXISTS (
    SELECT * FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_automation_dsc_task_log')
)
BEGIN
    EXEC sp_rename 'dh_retail_ntx_automation_dsc_task_log', 'dh_ntx_automation_dsc_task_log'
END
--------------------------------------------------------------------------------
-- logdate -> log_date, loginfo -> log_info for table `dh_ntx_automation_dsc_task_log`
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_ntx_automation_dsc_task_log')
	and  name = 'logdate'
)
BEGIN
  EXEC sp_rename 'dh_ntx_automation_dsc_task_log.logdate', 'log_date', 'COLUMN';
END

IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_ntx_automation_dsc_task_log')
	and  name = 'loginfo'
)
BEGIN
  EXEC sp_rename 'dh_ntx_automation_dsc_task_log.loginfo', 'log_info', 'COLUMN';
END
--------------------------------------------------------------------------------
-- Add column `facility_type` to tables `dh_ntx_automation_rotate_password_task`, `dh_ntx_automation_renew_cert_task`, `dh_ntx_automation_dsc_task`
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_ntx_automation_rotate_password_task')
	and  name = 'facility_type'
)
BEGIN
  ALTER TABLE dh_ntx_automation_rotate_password_task ADD facility_type VARCHAR(100) NULL;
END

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_ntx_automation_renew_cert_task')
	and  name = 'facility_type'
)
BEGIN
  ALTER TABLE dh_ntx_automation_renew_cert_task ADD facility_type VARCHAR(100) NULL;
END

IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_ntx_automation_dsc_task')
	and  name = 'facility_type'
)
BEGIN
  ALTER TABLE dh_ntx_automation_dsc_task ADD facility_type VARCHAR(100) NULL;
END
--------------------------------------------------------------------------------
-- Remove table `dh_retail_ntx_auto_maintenance_benchmark`
IF EXISTS (
    SELECT * FROM sysobjects
    WHERE id = OBJECT_ID('dh_retail_ntx_auto_maintenance_benchmark')
)
BEGIN
    DROP TABLE dh_retail_ntx_auto_maintenance_benchmark
END
-- Remove column benchmark_id from `dh_retail_ntx_auto_maintenance`
IF EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_auto_maintenance')
	and  name = 'benchmark_id'
)
BEGIN
  ALTER TABLE dh_retail_ntx_auto_maintenance DROP COLUMN benchmark_id;
END

IF EXISTS (
    SELECT * FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_auto_maintenance')
)
BEGIN
    EXEC sp_rename 'dh_retail_ntx_auto_maintenance', 'dh_ntx_auto_maintenance'
END

-- ----------------------------------------
--                                       --
--          2025-04-01 ZOEXU6            --
--                                       --
-- ----------------------------------------
-- remove table `dh_retail_nutanix_auto_maintenance_schedule`
IF EXISTS (
    SELECT * FROM sysobjects
    WHERE id = OBJECT_ID('dh_retail_nutanix_auto_maintenance_schedule')
)
BEGIN
    DROP TABLE dh_retail_nutanix_auto_maintenance_schedule
END

-- ----------------------------------------
--                                       --
--           2025-04-21 Ray              --
--                                       --
-- ----------------------------------------
--     Update benchmark data for WIAB    --
-------------------------------------------

-- Update for group mapping
UPDATE dh_ntx_benchmark_group_mapping
SET px_site_viewer_users='UG-WIABviewer-CG@INF-NTX-SE-DS',
    pc_site_viewer_group='UG-WIABviewer-PE@INF-NTX-SE-DS'
WHERE index_label='Warehouse-default'

-- Update for ATM and LCM activity schedulers
UPDATE dh_ntx_benchmark_scheduler
SET atm_schedule_json='{"pc":"ssp-apac-wiab-ntx.ikea.com","time_zone":"Asia/Tokyo","days":"sat,sun","hour":23,"minute":0,"interval_min":10}',
    lcm_schedule_json='{"pc":"ssp-apac-wiab-ntx.ikea.com","time_zone":"Aisa/Tokuo","days":"sat,sun","hour":23,"minute":0,"interval_min":10}'
WHERE index_label='Warehouse_AP'

UPDATE dh_ntx_benchmark_scheduler
SET atm_schedule_json='{"pc":"ssp-china-wiab-ntx.ikea.com","time_zone":"Asia/Shanghai","days":"sat,sun","hour":23,"minute":0,"interval_min":10}',
    lcm_schedule_json='{"pc":"ssp-china-wiab-ntx.ikea.com","time_zone":"Asia/Shanghai","days":"sat,sun","hour":23,"minute":0,"interval_min":10}'
WHERE index_label='Warehouse_China'

UPDATE dh_ntx_benchmark_scheduler
SET atm_schedule_json='{"pc":"ssp-na-wiab-ntx.ikea.com","time_zone":"America/Los_Angeles","days":"sat,sun","hour":23,"minute":0,"interval_min":5}',
    lcm_schedule_json='{"pc":"ssp-na-wiab-ntx.ikea.com","time_zone":"America/Los_Angeles","days":"sat,sun","hour":23,"minute":0,"interval_min":5}'
WHERE index_label='Warehouse_NA'

UPDATE dh_ntx_benchmark_scheduler
SET atm_schedule_json='{"pc":"ssp-eu-wiab-ntx.ikea.com","time_zone":"Europe/Paris","days":"sat,sun","hour":20,"minute":0,"interval_min":2}',
    lcm_schedule_json='{"pc":"ssp-eu-wiab-ntx.ikea.com","time_zone":"Europe/Paris","days":"sat,sun","hour":20,"minute":0,"interval_min":2}'
WHERE index_label='Warehouse_EU'


UPDATE dh_ntx_benchmark_scheduler
SET atm_schedule_json='{"pc":"dsse999-nxp000.ikeadt.com","time_zone":"Europe/Paris","days":"sat,sun","hour":20,"minute":0,"interval_min":2}',
    lcm_schedule_json='{"pc":"dsse999-nxp000.ikeadt.com","time_zone":"Europe/Paris","days":"sat,sun","hour":20,"minute":0,"interval_min":2}'
WHERE index_label='Warehouse_DT'

-- Update for DNS settings for WIAB sites
UPDATE dh_ntx_benchmark_systems
SET dns_servers_json='["10.59.253.2","10.59.67.9"]'
WHERE index_label='SSP-EU-WIAB-NTX'

-- Update number of bond 0 NICs for WIAB cluster network
UPDATE dh_ntx_benchmark_cluster_network
SET bond_0_nics=2
WHERE index_label='25G Quad Active-Passive'

-- --------------------------------------------------- --
--                                                     --
--                  2025-04-29 Ray                     --
--                                                     --
-- --------------------------------------------------- --
-- Update benchmark_certificate for SSL policy changed --
---------------------------------------------------------

-- Update for template
UPDATE dh_ntx_benchmark_certificate
SET template='IKEAWebServerAutomated'
WHERE template='IKEAWebServer'

UPDATE dh_ntx_benchmark_certificate
SET template='IKEADTWebServerAutomated'
WHERE template='IKEADTWebServer'

UPDATE dh_ntx_benchmark_certificate
SET template='IKEAD2WebServerAutomated'
WHERE template='IKEAD2WebServer'

-- Update for renewal days before expiration
UPDATE dh_ntx_benchmark_certificate
SET max_renewal_days_before_expiry=38

-- ----------------------------------------
--                                       --
--           2025-05-07 Ray              --
--                                       --
-- ----------------------------------------
--   Update field lenth for VM tables    --
-------------------------------------------
-- Update for dh_retail_ntx_vm
ALTER TABLE dh_retail_ntx_vm ALTER COLUMN description VARCHAR(1000) NULL
ALTER TABLE dh_retail_ntx_vm ALTER COLUMN ip VARCHAR(1000) NULL
ALTER TABLE dh_retail_ntx_vm ALTER COLUMN disk VARCHAR(1000) NULL

-- Update for dh_wh_ntx_vm
ALTER TABLE dh_wh_ntx_vm ALTER COLUMN description VARCHAR(1000) NULL
ALTER TABLE dh_wh_ntx_vm ALTER COLUMN ip VARCHAR(1000) NULL
ALTER TABLE dh_wh_ntx_vm ALTER COLUMN disk VARCHAR(1000) NULL

-- ----------------------------------------
--                                       --
--          2025-05-6 HUNHE            --
--                                       --
-- ----------------------------------------
IF NOT EXISTS (
    SELECT *
    FROM syscolumns
    WHERE id = OBJECT_ID('dh_retail_ntx_automation_spp_lcm_task')
    AND name = 'facility_type'
)
BEGIN
    ALTER TABLE dh_retail_ntx_automation_spp_lcm_task ADD facility_type VARCHAR(100) NULL;

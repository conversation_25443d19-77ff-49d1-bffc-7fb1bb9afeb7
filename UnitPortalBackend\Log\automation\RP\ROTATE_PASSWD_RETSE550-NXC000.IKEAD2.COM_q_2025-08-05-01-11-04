2025-08-05 09:11:04,817 INFO Start to run the task.
2025-08-05 09:11:08,185 INFO Checking Maintenance Mode
2025-08-05 09:11:08,185 INFO Trying to SSH to the pe RETSE550-NXC000.
2025-08-05 09:11:08,185 INFO SSH connecting to RETSE550-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:11:11,154 INFO SSH connected to RETSE550-NXC000.IKEAD2.COM.
2025-08-05 09:11:11,155 INFO SSH Executing '/home/<USER>/prism/cli/ncli host list --json=pretty'.
2025-08-05 09:11:11,994 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:11:16,996 INFO stdout: b'{\n  "data" : [ {\n    "serviceVMId" : "00062f94-995f-1671-0d96-48df37c61670::4",\n    "uuid" : "af174dae-c27f-4590-8fa3-98f2ce5b138f",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "ZHZ2EQRH",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::51",\n        "diskUuid" : "b6084fad-edde-4eeb-8a22-4455164cf03e",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/ZHZ2EQRH",\n        "model" : "MB012000JWRTF",\n        "vendor" : "HP",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD2",\n        "targetFirmwareVersion" : "HPD2",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "ZHZ53FLW",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::50",\n        "diskUuid" : "7815fd72-7945-48e4-aefb-c2f2a53da09e",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/ZHZ53FLW",\n        "model" : "MB012000JWRTF",\n        "vendor" : "HP",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD2",\n        "targetFirmwareVersion" : "HPD2",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "BTYG01550C2C960CGN",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::49",\n        "diskUuid" : "b785b759-e2b4-497b-a929-3fc19aaf9ced",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/BTYG01550C2C960CGN",\n        "model" : "MK000960GWTTK",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG7",\n        "targetFirmwareVersion" : "HPG6",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "BTYG01550DAR960CGN",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::52",\n        "diskUuid" : "*************-4ecb-92e6-75fdb9216178",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/BTYG01550DAR960CGN",\n        "model" : "MK000960GWTTK",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG7",\n        "targetFirmwareVersion" : "HPG6",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSE550-NX7001",\n    "serviceVMExternalIP" : "*************",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "*************"\n    } ],\n    "oplogDiskPct" : 1.7,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "*************",\n    "hypervisorAddress" : "*************",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "*************",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "*************"\n    } ],\n    "managementServerName" : "*************",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ202301X8",\n    "blockSerial" : "CZ202301X8",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX360-4 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 10,\n    "numCpuThreads" : 20,\n    "numCpuSockets" : 1,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 24000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "Nutanix 20230302.103032",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 1,\n    "bootTimeInUsecs" : 1753156409668138,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062f94-995f-1671-0d96-48df37c61670",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "0",\n      "controller_num_iops" : "0",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "115269",\n      "controller_num_write_io" : "0",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "0",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "32",\n      "controller_total_io_size_kbytes" : "0",\n      "content_cache_hit_ppm" : "1000000",\n      "controller_num_io" : "0",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "99",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "0",\n      "num_io" : "1",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "0",\n      "hypervisor_num_received_bytes" : "382794269897",\n      "hypervisor_timespan_usecs" : "2274268423",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "0",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "332",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "0",\n      "controller_write_io_ppm" : "0",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "328819701556",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "0",\n      "hypervisor_memory_usage_ppm" : "116340",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "0",\n      "total_io_time_usecs" : "332",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "0",\n      "write_io_bandwidth_kBps" : "1",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "0",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "1",\n      "total_io_size_kbytes" : "16",\n      "io_bandwidth_kBps" : "1",\n      "content_cache_physical_memory_usage_bytes" : "5956139140",\n      "controller_timespan_usecs" : "10000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-59419748",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "1000000",\n      "controller_avg_write_io_latency_usecs" : "0",\n      "content_cache_logical_memory_usage_bytes" : "5896719392"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "2738561024",\n      "storage.capacity_bytes" : "24158950642638",\n      "storage.logical_usage_bytes" : "43899715584",\n      "storage_tier.das-sata.capacity_bytes" : "22883990477210",\n      "storage.free_bytes" : "24126577238990",\n      "storage_tier.ssd.usage_bytes" : "29634842624",\n      "storage_tier.ssd.capacity_bytes" : "1274960165428",\n      "storage_tier.das-sata.free_bytes" : "22881251916186",\n      "storage.usage_bytes" : "32373403648",\n      "storage_tier.ssd.free_bytes" : "1245325322804"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062f94-995f-1671-0d96-48df37c61670::5",\n    "uuid" : "7a1087dd-f7cd-4cbb-a0f2-3c3834f5f728",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "ZHZ3YM0F",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::41",\n        "diskUuid" : "3403965d-e531-488b-9a58-6ac6233aad01",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/ZHZ3YM0F",\n        "model" : "MB012000JWRTF",\n        "vendor" : "HP",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD2",\n        "targetFirmwareVersion" : "HPD2",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "ZHZ2SKF5",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::42",\n        "diskUuid" : "e2135272-ff35-45c3-af6c-8912aff336d3",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/ZHZ2SKF5",\n        "model" : "MB012000JWRTF",\n        "vendor" : "HP",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD2",\n        "targetFirmwareVersion" : "HPD2",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "BTYG01550B89960CGN",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::46",\n        "diskUuid" : "99505c15-a51c-40e7-bee7-fa02d0c71a61",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/BTYG01550B89960CGN",\n        "model" : "MK000960GWTTK",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG7",\n        "targetFirmwareVersion" : "HPG6",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "BTYG01550B95960CGN",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::43",\n        "diskUuid" : "ae3cc0b6-9b58-4538-b826-0ee73d7b6a33",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/BTYG01550B95960CGN",\n        "model" : "MK000960GWTTK",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG7",\n        "targetFirmwareVersion" : "HPG6",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSE550-NX7002",\n    "serviceVMExternalIP" : "*************",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "*************"\n    } ],\n    "oplogDiskPct" : 1.7,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "*************",\n    "hypervisorAddress" : "*************",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "*************",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "*************"\n    } ],\n    "managementServerName" : "*************",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ202301X3",\n    "blockSerial" : "CZ202301X3",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX360-4 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 10,\n    "numCpuThreads" : 20,\n    "numCpuSockets" : 1,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 24000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "Nutanix 20230302.103032",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 1,\n    "bootTimeInUsecs" : 1753155310981409,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062f94-995f-1671-0d96-48df37c61670",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "30000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "214285",\n      "controller_num_iops" : "0",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "122228",\n      "controller_num_write_io" : "0",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "0",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "13171",\n      "controller_total_io_size_kbytes" : "0",\n      "content_cache_hit_ppm" : "997798",\n      "controller_num_io" : "0",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "99",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "0",\n      "num_io" : "14",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "0",\n      "hypervisor_num_received_bytes" : "395080134245",\n      "hypervisor_timespan_usecs" : "30005387",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "31",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "4280",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "0",\n      "controller_write_io_ppm" : "0",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "401859884936",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "1",\n      "hypervisor_memory_usage_ppm" : "116507",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "0",\n      "total_io_time_usecs" : "59933",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "3",\n      "write_io_bandwidth_kBps" : "5",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "0",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "11",\n      "total_io_size_kbytes" : "183",\n      "io_bandwidth_kBps" : "6",\n      "content_cache_physical_memory_usage_bytes" : "5547006048",\n      "controller_timespan_usecs" : "10000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-54999256",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "785714",\n      "controller_avg_write_io_latency_usecs" : "0",\n      "content_cache_logical_memory_usage_bytes" : "5492006792"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "2320822272",\n      "storage.capacity_bytes" : "24158950642638",\n      "storage.logical_usage_bytes" : "22967500800",\n      "storage_tier.das-sata.capacity_bytes" : "22883990477210",\n      "storage.free_bytes" : "24145836516302",\n      "storage_tier.ssd.usage_bytes" : "10793304064",\n      "storage_tier.ssd.capacity_bytes" : "1274960165428",\n      "storage_tier.das-sata.free_bytes" : "22881669654938",\n      "storage.usage_bytes" : "13114126336",\n      "storage_tier.ssd.free_bytes" : "1264166861364"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062f94-995f-1671-0d96-48df37c61670::6",\n    "uuid" : "e1090b36-3e15-40f9-9e84-f1c9bf34e2b1",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "ZL001VKW",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::48",\n        "diskUuid" : "5155d402-ffed-4af1-a7b2-8da0206c5925",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/ZL001VKW",\n        "model" : "MB012000JWRTF",\n        "vendor" : "HP",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD2",\n        "targetFirmwareVersion" : "HPD2",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "ZL00199F",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::47",\n        "diskUuid" : "3ce037e2-07d5-44a1-a162-b97cdc862066",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/ZL00199F",\n        "model" : "MB012000JWRTF",\n        "vendor" : "HP",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD2",\n        "targetFirmwareVersion" : "HPD2",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "BTYG01550C4Y960CGN",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::44",\n        "diskUuid" : "72c8a9a6-a17c-4469-a1d7-46b870af6f97",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/BTYG01550C4Y960CGN",\n        "model" : "MK000960GWTTK",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG7",\n        "targetFirmwareVersion" : "HPG6",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "BTYG01550BXP960CGN",\n        "diskId" : "00062f94-995f-1671-0d96-48df37c61670::45",\n        "diskUuid" : "d119fe45-42dc-4d78-8b18-f02dd2c45629",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/BTYG01550BXP960CGN",\n        "model" : "MK000960GWTTK",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG7",\n        "targetFirmwareVersion" : "HPG6",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSE550-NX7003",\n    "serviceVMExternalIP" : "*************",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "*************"\n    } ],\n    "oplogDiskPct" : 1.7,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "*************",\n    "hypervisorAddress" : "*************",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "*************",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "*************"\n    } ],\n    "managementServerName" : "*************",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ202301X4",\n    "blockSerial" : "CZ202301X4",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX360-4 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 10,\n    "numCpuThreads" : 20,\n    "numCpuSockets" : 1,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 24000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "Nutanix 20230302.103032",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 1,\n    "bootTimeInUsecs" : 1753154144167257,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062f94-995f-1671-0d96-48df37c61670",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "0",\n      "controller_num_iops" : "0",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "141133",\n      "controller_num_write_io" : "0",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "0",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "16",\n      "controller_total_io_size_kbytes" : "0",\n      "content_cache_hit_ppm" : "1000000",\n      "controller_num_io" : "0",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "99",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "0",\n      "num_io" : "1",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "0",\n      "hypervisor_num_received_bytes" : "367363244967",\n      "hypervisor_timespan_usecs" : "30000000",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "0",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "684",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "0",\n      "controller_write_io_ppm" : "0",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "414505604247",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "0",\n      "hypervisor_memory_usage_ppm" : "116251",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "0",\n      "total_io_time_usecs" : "684",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "0",\n      "write_io_bandwidth_kBps" : "1",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "0",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "1",\n      "total_io_size_kbytes" : "16",\n      "io_bandwidth_kBps" : "1",\n      "content_cache_physical_memory_usage_bytes" : "7066582788",\n      "controller_timespan_usecs" : "20000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-70337180",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "1000000",\n      "controller_avg_write_io_latency_usecs" : "0",\n      "content_cache_logical_memory_usage_bytes" : "6996245608"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "2633871360",\n      "storage.capacity_bytes" : "24158950642638",\n      "storage.logical_usage_bytes" : "36683644928",\n      "storage_tier.das-sata.capacity_bytes" : "22883990477210",\n      "storage.free_bytes" : "24133092545486",\n      "storage_tier.ssd.usage_bytes" : "23224225792",\n      "storage_tier.ssd.capacity_bytes" : "1274960165428",\n      "storage_tier.das-sata.free_bytes" : "22881356605850",\n      "storage.usage_bytes" : "25858097152",\n      "storage_tier.ssd.free_bytes" : "1251735939636"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  } ],\n  "status" : 0\n}\n'
2025-08-05 09:11:17,061 INFO All good, no hosts are set as NCLI maintenance inside this cluster.
2025-08-05 09:11:17,062 INFO Trying to SSH to the pe RETSE550-NXC000.
2025-08-05 09:11:17,062 INFO SSH connecting to RETSE550-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:11:19,597 INFO SSH connected to RETSE550-NXC000.IKEAD2.COM.
2025-08-05 09:11:19,598 INFO SSH Executing '/usr/local/nutanix/bin/acli -o json host.list'.
2025-08-05 09:11:20,445 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:11:25,446 INFO stdout: b'{"data": [{"hypervisorAddress": "*************", "hypervisorDnsName": "*************", "uuid": "af174dae-c27f-4590-8fa3-98f2ce5b138f", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "*************"}, {"hypervisorAddress": "*************", "hypervisorDnsName": "*************", "uuid": "7a1087dd-f7cd-4cbb-a0f2-3c3834f5f728", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "*************"}, {"hypervisorAddress": "*************", "hypervisorDnsName": "*************", "uuid": "e1090b36-3e15-40f9-9e84-f1c9bf34e2b1", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "*************"}], "error": null, "status": 0}\n'
2025-08-05 09:11:25,447 INFO This seems a very old AOS version...
2025-08-05 09:11:25,447 INFO This seems a very old AOS version...
2025-08-05 09:11:25,447 INFO This seems a very old AOS version...
2025-08-05 09:11:25,460 INFO All good, no hosts are set as ACLI maintenance inside this cluster.
2025-08-05 09:11:25,475 INFO Checking CVM status
2025-08-05 09:11:26,065 INFO Trying to SSH to the RETSE550-NXC000.IKEAD2.COM.
2025-08-05 09:11:26,065 INFO First try with username/password.
2025-08-05 09:11:26,065 INFO SSH connecting to RETSE550-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:11:28,642 INFO SSH connected to RETSE550-NXC000.IKEAD2.COM.
2025-08-05 09:11:41,121 INFO Sending 'cluster status |grep -v UP' to the server.
2025-08-05 09:12:05,289 INFO CVM IP:************* Status:Up
2025-08-05 09:12:07,067 INFO CVM IP:************* Status:Up
2025-08-05 09:12:07,999 INFO CVM IP:************* Status:Up
2025-08-05 09:12:09,076 INFO Great, all CVM status are Up
2025-08-05 09:12:10,684 INFO Calling restapi, URL: https://retse550-nxc000.ikead2.com:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:12:10,684 INFO params: None
2025-08-05 09:12:10,684 INFO User: <EMAIL>
2025-08-05 09:12:10,684 INFO payload: None
2025-08-05 09:12:10,684 INFO files: None
2025-08-05 09:12:10,684 INFO timeout: 30
2025-08-05 09:12:11,989 INFO Getting host list from retse550-nxc000.
2025-08-05 09:12:11,990 INFO Got the host list from retse550-nxc000.
2025-08-05 09:12:11,990 INFO Got the host list.
2025-08-05 09:12:11,990 INFO Getting vault from IKEAD2.
2025-08-05 09:12:12,664 INFO Getting Site_Pe_Nutanix.
2025-08-05 09:12:13,308 INFO Got Site_Pe_Nutanix.
2025-08-05 09:12:13,308 INFO Getting Site_Pe_Admin.
2025-08-05 09:12:13,857 INFO Got Site_Pe_Admin.
2025-08-05 09:12:13,857 INFO Getting Site_Oob.
2025-08-05 09:12:14,359 INFO Got Site_Oob.
2025-08-05 09:12:14,359 INFO Getting Site_Ahv_Nutanix.
2025-08-05 09:12:14,923 INFO Got Site_Ahv_Nutanix.
2025-08-05 09:12:14,923 INFO Getting Site_Ahv_Root.
2025-08-05 09:12:15,478 INFO Got Site_Ahv_Root.
2025-08-05 09:12:15,478 INFO Getting Site_Gw_Priv_Key.
2025-08-05 09:12:15,943 INFO Got Site_Gw_Priv_Key.
2025-08-05 09:12:15,943 INFO Getting Site_Gw_Pub_Key.
2025-08-05 09:12:16,471 INFO Got Site_Gw_Pub_Key.
2025-08-05 09:12:16,472 INFO Getting Site_Pe_Svc.
2025-08-05 09:12:17,004 INFO Got Site_Pe_Svc.
2025-08-05 09:12:17,069 INFO Checking if cluster 'RETSE550-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-05 09:12:17,121 INFO Connecting to CVM ************* for AHV password updates
2025-08-05 09:12:17,122 INFO SSH connecting to *************, this is the '1' try.
2025-08-05 09:12:19,106 INFO SSH connected to ************* with SSHKEY.
2025-08-05 09:12:20,212 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 09:12:25,227 INFO Start reset AHV user nutanix password from CVM *************
2025-08-05 09:12:25,239 INFO unlocking nutanix account
2025-08-05 09:12:25,239 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 09:12:36,242 INFO Sending '*' to the server.
2025-08-05 09:12:51,774 INFO AHV User nutanix Password Update Success
2025-08-05 09:12:51,803 INFO Start reset AHV user root password from CVM *************
2025-08-05 09:12:51,824 INFO unlocking root account
2025-08-05 09:12:51,824 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 09:13:02,326 INFO Sending '*' to the server.
2025-08-05 09:13:17,839 INFO AHV User root Password Update Success
2025-08-05 09:13:17,856 INFO Connecting to CVM ************* for AHV password updates
2025-08-05 09:13:17,857 INFO SSH connecting to *************, this is the '1' try.
2025-08-05 09:13:19,834 INFO SSH connected to ************* with SSHKEY.
2025-08-05 09:13:20,955 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 09:13:25,968 INFO Start reset AHV user nutanix password from CVM *************
2025-08-05 09:13:25,981 INFO unlocking nutanix account
2025-08-05 09:13:25,981 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 09:13:36,983 INFO Sending '*' to the server.
2025-08-05 09:13:52,499 INFO AHV User nutanix Password Update Success
2025-08-05 09:13:52,509 INFO Start reset AHV user root password from CVM *************
2025-08-05 09:13:52,521 INFO unlocking root account
2025-08-05 09:13:52,522 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 09:14:03,023 INFO Sending '*' to the server.
2025-08-05 09:14:18,535 INFO AHV User root Password Update Success
2025-08-05 09:14:18,561 INFO Connecting to CVM ************* for AHV password updates
2025-08-05 09:14:18,562 INFO SSH connecting to *************, this is the '1' try.
2025-08-05 09:14:20,601 INFO SSH connected to ************* with SSHKEY.
2025-08-05 09:14:21,706 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 09:14:26,719 INFO Start reset AHV user nutanix password from CVM *************
2025-08-05 09:14:26,730 INFO unlocking nutanix account
2025-08-05 09:14:26,730 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 09:14:37,733 INFO Sending '*' to the server.
2025-08-05 09:14:53,251 INFO AHV User nutanix Password Update Success
2025-08-05 09:14:53,264 INFO Saving token to Vault... Username: nutanix, label: RETSE550-NXC000/Site_Ahv_Nutanix
2025-08-05 09:14:53,906 INFO Saving token completed.
2025-08-05 09:14:53,928 INFO Start reset AHV user root password from CVM *************
2025-08-05 09:14:53,937 INFO unlocking root account
2025-08-05 09:14:53,937 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 09:15:04,444 INFO Sending '*' to the server.
2025-08-05 09:15:19,955 INFO AHV User root Password Update Success
2025-08-05 09:15:19,969 INFO Saving token to Vault... Username: root, label: RETSE550-NXC000/Site_Ahv_Root
2025-08-05 09:15:20,671 INFO Saving token completed.
2025-08-05 09:15:20,694 INFO Starting to reset iLO password for user 'administrator' across all hosts.
2025-08-05 09:15:20,705 INFO Updating host RETSE550-NX7001 (*************)
2025-08-05 09:15:20,707 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-05 09:15:20,721 INFO Finding account URI for user 'administrator'.
2025-08-05 09:15:20,721 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 09:15:20,721 INFO params: None
2025-08-05 09:15:20,721 INFO User: administrator
2025-08-05 09:15:20,721 INFO payload: None
2025-08-05 09:15:20,721 INFO files: None
2025-08-05 09:15:20,721 INFO timeout: None
2025-08-05 09:15:21,894 INFO Got the response with OK
2025-08-05 09:15:21,894 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 09:15:21,894 INFO params: None
2025-08-05 09:15:21,894 INFO User: administrator
2025-08-05 09:15:21,894 INFO payload: None
2025-08-05 09:15:21,894 INFO files: None
2025-08-05 09:15:21,894 INFO timeout: None
2025-08-05 09:15:23,087 INFO Got the response with OK
2025-08-05 09:15:23,087 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 09:15:23,087 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 09:15:23,087 INFO params: None
2025-08-05 09:15:23,087 INFO User: administrator
2025-08-05 09:15:23,087 INFO payload: {'Password': '*****'}
2025-08-05 09:15:23,087 INFO files: None
2025-08-05 09:15:23,087 INFO timeout: None
2025-08-05 09:15:24,340 INFO ILO object updated successfully
2025-08-05 09:15:24,351 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-05 09:15:24,370 INFO Updating host RETSE550-NX7002 (*************)
2025-08-05 09:15:24,379 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-05 09:15:24,387 INFO Finding account URI for user 'administrator'.
2025-08-05 09:15:24,388 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 09:15:24,388 INFO params: None
2025-08-05 09:15:24,388 INFO User: administrator
2025-08-05 09:15:24,388 INFO payload: None
2025-08-05 09:15:24,388 INFO files: None
2025-08-05 09:15:24,388 INFO timeout: None
2025-08-05 09:15:25,559 INFO Got the response with OK
2025-08-05 09:15:25,560 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 09:15:25,560 INFO params: None
2025-08-05 09:15:25,561 INFO User: administrator
2025-08-05 09:15:25,561 INFO payload: None
2025-08-05 09:15:25,561 INFO files: None
2025-08-05 09:15:25,561 INFO timeout: None
2025-08-05 09:15:26,734 INFO Got the response with OK
2025-08-05 09:15:26,761 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 09:15:26,761 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 09:15:26,761 INFO params: None
2025-08-05 09:15:26,761 INFO User: administrator
2025-08-05 09:15:26,761 INFO payload: {'Password': '*****'}
2025-08-05 09:15:26,761 INFO files: None
2025-08-05 09:15:26,761 INFO timeout: None
2025-08-05 09:15:28,071 INFO ILO object updated successfully
2025-08-05 09:15:28,087 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-05 09:15:28,108 INFO Updating host RETSE550-NX7003 (*************)
2025-08-05 09:15:28,126 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-05 09:15:28,148 INFO Finding account URI for user 'administrator'.
2025-08-05 09:15:28,149 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 09:15:28,149 INFO params: None
2025-08-05 09:15:28,149 INFO User: administrator
2025-08-05 09:15:28,149 INFO payload: None
2025-08-05 09:15:28,149 INFO files: None
2025-08-05 09:15:28,149 INFO timeout: None
2025-08-05 09:15:30,053 INFO Got the response with OK
2025-08-05 09:15:30,068 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 09:15:30,068 INFO params: None
2025-08-05 09:15:30,068 INFO User: administrator
2025-08-05 09:15:30,068 INFO payload: None
2025-08-05 09:15:30,069 INFO files: None
2025-08-05 09:15:30,069 INFO timeout: None
2025-08-05 09:15:31,236 INFO Got the response with OK
2025-08-05 09:15:31,249 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 09:15:31,249 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 09:15:31,249 INFO params: None
2025-08-05 09:15:31,249 INFO User: administrator
2025-08-05 09:15:31,249 INFO payload: {'Password': '*****'}
2025-08-05 09:15:31,249 INFO files: None
2025-08-05 09:15:31,249 INFO timeout: None
2025-08-05 09:15:33,067 INFO ILO object updated successfully
2025-08-05 09:15:33,167 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-05 09:15:33,228 INFO Successfully updated password for 'administrator' on all hosts. Saving to Vault.
2025-08-05 09:15:33,274 INFO Saving token to Vault... Username: administrator, label: RETSE550-NXC000/Site_Oob
2025-08-05 09:15:33,979 INFO Saving token completed.
2025-08-05 09:15:33,986 INFO Start reset CVM Nutanix password
2025-08-05 09:15:34,003 INFO Attempting to reset password for user 'nutanix' on cluster associated with CVM RETSE550-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-05 09:15:34,003 INFO SSH connecting to RETSE550-NXC000.ikead2.com, this is the '1' try.
2025-08-05 09:15:36,520 INFO SSH connected to RETSE550-NXC000.ikead2.com.
2025-08-05 09:15:36,545 INFO Unlocking user 'nutanix' on all CVMs.
2025-08-05 09:15:36,545 INFO SSH Executing 'allssh sudo faillock --user nutanix --reset'.
2025-08-05 09:15:37,371 INFO Waiting for 10 seconds for the execution.
2025-08-05 09:15:47,372 INFO stdout: b''
2025-08-05 09:15:47,386 INFO Changing password for 'nutanix' on single node RETSE550-NXC000.ikead2.com.
2025-08-05 09:15:47,386 INFO SSH Executing '*****************************************************'.
2025-08-05 09:15:47,898 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:15:52,905 INFO stdout: b'Changing password for user nutanix.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 09:15:52,905 INFO Password change command sent. Verifying new password for 'nutanix' with a new SSH connection.
2025-08-05 09:15:57,915 INFO SSH connecting to RETSE550-NXC000.ikead2.com, this is the '1' try.
2025-08-05 09:16:00,782 INFO SSH connected to RETSE550-NXC000.ikead2.com.
2025-08-05 09:16:00,810 INFO Successfully reset and verified new password for user 'nutanix'.
2025-08-05 09:16:00,818 INFO Saving token to Vault... Username: nutanix, label: RETSE550-NXC000/Site_Pe_Nutanix
2025-08-05 09:16:01,520 INFO Saving token completed.
2025-08-05 09:16:02,067 INFO taking a 30S extra powernap
2025-08-05 09:16:32,077 INFO Start reset admin password
2025-08-05 09:16:32,086 INFO Attempting to reset password for user 'admin' on cluster associated with CVM RETSE550-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-05 09:16:32,086 INFO SSH connecting to RETSE550-NXC000.ikead2.com, this is the '1' try.
2025-08-05 09:16:34,600 INFO SSH connected to RETSE550-NXC000.ikead2.com.
2025-08-05 09:16:34,600 INFO Unlocking user 'admin' on all CVMs.
2025-08-05 09:16:34,600 INFO SSH Executing 'allssh sudo faillock --user admin --reset'.
2025-08-05 09:16:35,434 INFO Waiting for 10 seconds for the execution.
2025-08-05 09:16:45,443 INFO stdout: b''
2025-08-05 09:16:45,449 INFO Changing password for 'admin' on single node RETSE550-NXC000.ikead2.com.
2025-08-05 09:16:45,449 INFO SSH Executing '***************************************************'.
2025-08-05 09:16:45,966 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:16:50,977 INFO stdout: b'Changing password for user admin.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 09:16:50,987 INFO Password change command sent. Verifying new password for 'admin' with a new SSH connection.
2025-08-05 09:16:55,991 INFO SSH connecting to RETSE550-NXC000.ikead2.com, this is the '1' try.
2025-08-05 09:16:58,523 INFO SSH connected to RETSE550-NXC000.ikead2.com.
2025-08-05 09:16:58,535 INFO Successfully reset and verified new password for user 'admin'.
2025-08-05 09:16:58,539 INFO Saving token to Vault... Username: admin, label: RETSE550-NXC000/Site_Pe_Admin
2025-08-05 09:16:59,181 INFO Saving token completed.
2025-08-05 09:17:03,944 INFO Start reset 1-click-nutanix password for PE RETSE550-NXC000
2025-08-05 09:17:06,014 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-08-05 09:17:06,014 INFO params: None
2025-08-05 09:17:06,014 INFO User: admin
2025-08-05 09:17:06,014 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-08-05 09:17:06,014 INFO files: None
2025-08-05 09:17:06,015 INFO timeout: None
2025-08-05 09:17:07,651 INFO Saving token to Vault... Username: 1-click-nutanix, label: RETSE550-NXC000/Site_Pe_Svc
2025-08-05 09:17:08,296 INFO Saving token completed.
2025-08-05 09:17:08,312 INFO ****************************************************************************************************
2025-08-05 09:17:08,312 INFO *                                                                                                  *
2025-08-05 09:17:08,312 INFO *                                          Renew SSH key                                           *
2025-08-05 09:17:08,312 INFO *                                                                                                  *
2025-08-05 09:17:08,313 INFO ****************************************************************************************************
2025-08-05 09:17:09,316 INFO Check if ssh_key exist
2025-08-05 09:17:09,316 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: GET, headers: None
2025-08-05 09:17:09,316 INFO params: None
2025-08-05 09:17:09,316 INFO User: admin
2025-08-05 09:17:09,316 INFO payload: None
2025-08-05 09:17:09,316 INFO files: None
2025-08-05 09:17:09,316 INFO timeout: None
2025-08-05 09:17:10,769 INFO SSH key exist, we need replace it.
2025-08-05 09:17:10,769 INFO Deleting public key...
2025-08-05 09:17:10,769 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys/Gateway, method: DELETE, headers: None
2025-08-05 09:17:10,769 INFO params: None
2025-08-05 09:17:10,769 INFO User: admin
2025-08-05 09:17:10,769 INFO payload: None
2025-08-05 09:17:10,769 INFO files: None
2025-08-05 09:17:10,769 INFO timeout: None
2025-08-05 09:17:12,780 INFO Delete public key finished.
2025-08-05 09:17:12,789 INFO Generating ssh_key
2025-08-05 09:17:12,790 INFO Generating SSH key: ssh-keygen -t rsa -b 2048 -f c:\Dev\UnitPortalBackend\tmp\sshkey\RETSE550-NXC000_2025-08-05-01-17-09\prvkey -q -N "" -m PEM
2025-08-05 09:17:13,132 INFO Key pair generated: c:\Dev\UnitPortalBackend\tmp\sshkey\RETSE550-NXC000_2025-08-05-01-17-09\prvkey
2025-08-05 09:17:13,136 INFO Installing public key...
2025-08-05 09:17:13,136 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: POST, headers: None
2025-08-05 09:17:13,136 INFO params: None
2025-08-05 09:17:13,136 INFO User: admin
2025-08-05 09:17:13,136 INFO payload: {'name': 'Gateway', 'key': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC5K7tMZ4rQHtPOw08eIvWpROpyKRZaPcK7zvD40BPmEY0NxFt2yhhVr4mRsIbAxmfejOvVDzV0k8tuxqZEtTau1G/L3pqrMshyR3GUqYIG4ebX/PRi8OOkGDyCJ5RAkqGrXf+kyI1QiBtSXfGFA7Y2nNEFnQibD0P3M3mVkyKWr4WPJxbiNnXLGCHwBL6Udi6dDUuG6RL4/GQ4A++YxfREHzyCeQi+t1xeM6cVO9ZZq8nJrnN/TsEZTqYGnj/zhR5ktO13IccreyTjq7i8Nm2zmoD0KuUvjUoV3SdzRwvUFFI83adbUHHlaLM9OXZLf1vUKnUMiGoQI5KxXRaBsMhr ikea\\hunhe@ITCNSHG-NB0436'}
2025-08-05 09:17:13,136 INFO files: None
2025-08-05 09:17:13,136 INFO timeout: None
2025-08-05 09:17:14,772 INFO Install public key finished.
2025-08-05 09:17:14,782 INFO Saving token to Vault... Username: nutanix, label: RETSE550-NXC000/Site_Gw_Priv_Key
2025-08-05 09:17:15,433 INFO Saving token completed.
2025-08-05 09:17:15,443 INFO Saving token to Vault... Username: nutanix, label: RETSE550-NXC000/Site_Gw_Pub_Key
2025-08-05 09:17:16,084 INFO Saving token completed.
2025-08-05 09:17:16,116 INFO Task is in 'Done' status.

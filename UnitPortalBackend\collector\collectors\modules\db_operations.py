# Author: TiAke2
# Date: 2024-12
# Description: This module provides database operations for Nutanix hosts and Prism Central/Element management.

from typing import Callable, TYPE_CHECKING, Optional
import logging
from sqlalchemy import select
from datetime import datetime, timezone

from models.ntx_models import ModelRetailNutanixHost, ModelRetailNutanixHostSchema
from models.ntx_models import ModelRetailNutanixOneview, ModelRetailNutanixOneviewSchema
from models.ntx_models_wh import ModelWarehouseNutanixHost, ModelWarehouseNutanixHostSchema
from business.distributedhosting.nutanix.nutanix import  PrismElement, PrismCentral

if TYPE_CHECKING:
    from flask_sqlalchemy import SQLAlchemy

log = logging.getLogger(__file__)


# still need to make a instance of this class to pass logger argument

class DatabaseOperations:
    def __init__(self, logger = logging):
        # set logger to logger if provided, else use log, then set it to log
        global log  # pylint: disable=global-statement
        self.logging = logger if logger else log
        log = self.logging
   
    def get_dh_ntx_host(self, warehouse = False):
        if warehouse:
            dbmodel = ModelWarehouseNutanixHost
            schema = ModelWarehouseNutanixHostSchema(many = True)
        else:
            dbmodel = ModelRetailNutanixHost
            schema = ModelRetailNutanixHostSchema(many = True)
        host_list = []

        try:
            host_database_data = dbmodel.query.all() # all() returns a list, even there is only 1 entry inside
            # get all the host entry from db
            all_host_pretty = schema.dump(host_database_data) #  schema to make it json.
        except ValueError as e:
            raise Exception(f"Error in getting host data from db: {e} Should be that columns in db table dont match the schema")

        try:
            if not all_host_pretty:
                self.logging.info("No host data found in the database.")
                headers = False
                return headers, host_list
            headers = [key for key, _ in all_host_pretty[0].items() if key != "last_update"]
            for host_values in all_host_pretty:
                if warehouse:
                    list_host_values = [
                            host_values["id"], host_values["name"], host_values["sn"], host_values["pe_name"], host_values["pe_fqdn"], host_values["pe_uuid"], host_values["disk_number"],
                            host_values["uuid"], host_values["model"], host_values["memory"], host_values["cpu_core_number"], host_values["cpu_model"], host_values["ahv_ip"],
                            host_values["cvm_ip"], host_values["ipmi_ip"], host_values["ipmi_version"], host_values["status"], host_values["pe_id"], host_values["bios_version"],
                            host_values["controller_version"], host_values["controller_model"], host_values["nic0_uuid"], host_values["nic0_mac"], host_values["nic0_speed"],
                            host_values["nic0_mtu"], host_values["nic0_sw_device"], host_values["nic0_sw_port"], host_values["nic0_sw_vendor"], host_values["nic0_sw_vlan"],
                            host_values["nic1_uuid"], host_values["nic1_mac"], host_values["nic1_speed"], host_values["nic1_mtu"], host_values["nic1_sw_device"], host_values["nic1_sw_port"],
                            host_values["nic1_sw_vendor"], host_values["nic1_sw_vlan"],
                            host_values["nic2_uuid"], host_values["nic2_mac"], host_values["nic2_speed"], host_values["nic2_mtu"], host_values["nic2_sw_device"], host_values["nic2_sw_port"],
                            host_values["nic2_sw_vendor"], host_values["nic2_sw_vlan"],
                            host_values["nic3_uuid"], host_values["nic3_mac"], host_values["nic3_speed"], host_values["nic3_mtu"], host_values["nic3_sw_device"], host_values["nic3_sw_port"],
                            host_values["nic3_sw_vendor"], host_values["nic3_sw_vlan"]
                                       ]
                else:

                    list_host_values = [
                            host_values["id"], host_values["name"], host_values["sn"], host_values["pe_name"], host_values["pe_fqdn"], host_values["pe_uuid"], host_values["disk_number"],
                            host_values["uuid"], host_values["model"], host_values["memory"], host_values["cpu_core_number"], host_values["cpu_model"], host_values["ahv_ip"],
                            host_values["cvm_ip"], host_values["ipmi_ip"], host_values["ipmi_version"], host_values["status"], host_values["pe_id"], host_values["bios_version"],
                            host_values["controller_version"], host_values["controller_model"], host_values["nic0_uuid"], host_values["nic0_mac"], host_values["nic0_speed"],
                            host_values["nic0_mtu"], host_values["nic0_sw_device"], host_values["nic0_sw_port"], host_values["nic0_sw_vendor"], host_values["nic0_sw_vlan"],
                            host_values["nic1_uuid"], host_values["nic1_mac"], host_values["nic1_speed"], host_values["nic1_mtu"], host_values["nic1_sw_device"], host_values["nic1_sw_port"],
                            host_values["nic1_sw_vendor"], host_values["nic1_sw_vlan"]
                                        ]

                host_list.append(list_host_values)
        except Exception as e:
            raise Exception(f"Error in getting host data from db: {e}")

        return headers, host_list

    @staticmethod
    def get_dh_retail_ntx_pc() -> dict:
        pc_database_info = PrismCentral.get_retail_prism_list_from_db()
        return {pc["fqdn"]: pc for pc in pc_database_info}

    @staticmethod
    def get_dh_warehouse_ntx_pc() -> dict:
        pc_database_info = PrismCentral.get_warehouse_prism_list_from_db()
        return {pc["fqdn"]: pc for pc in pc_database_info}

    @staticmethod
    def get_dh_ntx_pc() -> dict:
        return DatabaseOperations.get_dh_retail_ntx_pc() | DatabaseOperations.get_dh_warehouse_ntx_pc()

    @staticmethod
    def _central_pe_inject_func(pc: dict) -> dict:
        pc["prism"] = pc.pop("fqdn")
        return pc

    @staticmethod
    def get_retail_central_pes():
        return DatabaseOperations._template_get(DatabaseOperations.get_dh_retail_ntx_pc, "central_pe_fqdn", DatabaseOperations._central_pe_inject_func)

    @staticmethod
    def get_warehouse_central_pes():
        return DatabaseOperations._template_get(DatabaseOperations.get_dh_warehouse_ntx_pc, "central_pe_fqdn", DatabaseOperations._central_pe_inject_func)

    @staticmethod
    def get_central_pes():
        return DatabaseOperations.get_retail_central_pes() | DatabaseOperations.get_warehouse_central_pes()


    def get_dh_server_oneview(self):
        _, one_region  = DatabaseOperations.get_oneview_sites_from_db()
        prism_central = PrismCentral(pc = None, logger = self.logging)
        pc_database_info = prism_central.get_prism_list_from_db()
        pc_fqdn_set = set()
        for pc in pc_database_info:
            if pc.get("oneview_region") in one_region:
                pc_fqdn_set.add(pc["fqdn"])
                one_region.remove(pc["oneview_region"])  # Remove the matched region from the list to avoid duplicates calls to the same region
        # Convert the set to a list
        oneview_fqdn_list = list(pc_fqdn_set)
        return oneview_fqdn_list

    @staticmethod
    def get_dh_retail_ntx_pe(**filter):
        return PrismElement.get_retail_pe_list_from_db(**filter)

    @staticmethod
    def get_dh_warehouse_ntx_pe(**filter):
        return PrismElement.get_wh_pe_list_from_db(**filter)

    @staticmethod
    def get_dh_ntx_pe(**filter):
        return PrismElement.get_pe_list_from_db(**filter)

    @staticmethod
    def get_oneview_sites_from_db():
        oneview_data = ModelRetailNutanixOneview.query.all()
        all_oneview_pretty = ModelRetailNutanixOneviewSchema(many=True).dump(oneview_data) #  schema to make it json.
        oneview_fqdn_set = {oneV["fqdn"] for oneV in all_oneview_pretty}
        oneview_region_set = {oneV["region"] for oneV in all_oneview_pretty}
        oneview_fqdn_list = list(oneview_fqdn_set)
        oneview_region_list = list(oneview_region_set)
        return oneview_fqdn_list, oneview_region_list

    @staticmethod
    def _get_unique_values_with_order(attributes):
        seen = set()
        unique_attributes = []
        for attr in attributes:
            if attr not in seen:
                unique_attributes.append(attr)
                seen.add(attr)
        return unique_attributes

    @staticmethod
    def get_column_id(model: "SQLAlchemy.Model", column_name: str) -> int:
        return [c.name for c in model.__table__.columns].index(column_name)

    @staticmethod
    def record_normalize(data: dict, model: "SQLAlchemy.Model"):
        for column in model.__table__.columns:
            if isinstance(column.type, str) and not data.get(column.name, ''):
                data[column.name] = 'NA'
            if isinstance(column.type, int) and not data.get(column.name, 0):
                data[column.name] = 0

    @staticmethod
    def update_db(data: dict, model: "SQLAlchemy.Model", column: "SQLAlchemy.Column", session):
        # Prepare lists for batch processing
        to_update = []
        to_insert = []

        def normalize_column(value):
            # Normalize the column value to lowercase if it's a string
            if isinstance(value, str):
                return value.lower()
            return value

        # Get all the fqdn, id column values from the model
        noted_fqdns = session.execute(select(column, model.id)).all()
        fqdns = [normalize_column(fqdn) for fqdn, _ in noted_fqdns]
        for entry in data.items():
            item = normalize_column(entry[0])
            if item not in fqdns:
                to_insert.append(model(**entry[1]))
            else:
                id_ = next(id for fqdn, id in noted_fqdns if normalize_column(fqdn) == item)
                to_update.append({"id": id_, **entry[1]})
        
        log.info("Updating table %s with %d updates and %d inserts", model.__tablename__, len(to_update), len(to_insert))

        try:
            # Perform batch update
            if to_update:
                session.bulk_update_mappings(model, to_update)

            # Perform batch insert
            if to_insert:
                session.bulk_save_objects(to_insert)  # Ensure these are model instances

            # Commit all changes in a single transaction
            session.commit()
            log.info("All changes have been committed")
        except Exception as e:
            session.rollback()
            log.error(f"Error updating database: {e}")
            raise Exception(f"Error updating database: {e}") from e
        
    @staticmethod
    def _template_get(getter_func: Callable[[], dict], index: str, inject_func: Optional[Callable[[dict], dict]] = None) -> dict:
        if not inject_func:
            def default_inject_func(x):
                return x
            inject_func = default_inject_func
        res = getter_func()
        result = {}
        for entry in res.values():
            result[entry[index]] = inject_func(entry)
        return result

    @staticmethod
    def get_dh_retail_ntx_vm(**filter):
        return PrismElement.get_retail_vm_list_from_db(**filter)

    @staticmethod
    def get_dh_warehouse_ntx_vm(**filter):
        return PrismElement.get_wh_vm_list_from_db(**filter)
            
        
    @staticmethod
    def update_db_last_update_column_only(data: dict, model: "SQLAlchemy.Model", column: "SQLAlchemy.Column",  session):
        # Update the last_update column for untouched entries in the database. very speedy way to do it
                
        # Get all id column values from the model
        noted_cols = session.execute(select(column, model.id)).all()
        
        col_to_id = {col: id for col, id in noted_cols}

        # Prepare the list of IDs to update based on the col in data.keys()
        entry_ids = [col_to_id[col] for col in data.keys() if col in col_to_id]

        last_update = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        log.info(f"{len(entry_ids)} entries are untouched in this update but will now get new time value for last_update column as well, updating them now")
        try:
            if entry_ids:
                session.query(model).filter(model.id.in_(entry_ids)).update(
                        {model.last_update: last_update }
                        )
                session.commit()
        except Exception as e:
            session.rollback()
            log.error(f"Error updating last_update column in database: {e}")
            raise Exception(f"Error updating last_update column in database: {e}") from e
        
        


import json
from business.nowit.incident_handler_config import IncHandlerCfg
from flask import jsonify
from models.database import db
from models.incident_models import IncidentHandlerAnalysis
from datetime import datetime
from sqlalchemy import cast, Date
import pandas as pd
from collections import Counter
import calendar


class Sqlquery:
    def __init__(self) -> None:
        pass

    @staticmethod
    def query_cases_by_dates_only(start_date, end_date):
        all_records = db.session.query(IncidentHandlerAnalysis).filter(
            cast(IncidentHandlerAnalysis.creation_time, Date) >= start_date.date(),
            cast(IncidentHandlerAnalysis.creation_time, Date) <= end_date.date(),
            IncidentHandlerAnalysis.id != 1
        ).all()
        return all_records

    @staticmethod
    def query_cases_by_model(model_name, start_date, end_date):
        all_records = db.session.query(IncidentHandlerAnalysis).filter(
            cast(IncidentHandlerAnalysis.creation_time, Date) >= start_date.date(),
            cast(IncidentHandlerAnalysis.creation_time, Date) <= end_date.date(),
            IncidentHandlerAnalysis.model == model_name,
            IncidentHandlerAnalysis.id != 1
        ).all()
        return all_records

    @staticmethod
    def query_cases_for_dates(start_date, end_date):
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
        siab_count = db.session.query(IncidentHandlerAnalysis).filter(
            cast(IncidentHandlerAnalysis.creation_time, Date) >= start_datetime.date(),
            cast(IncidentHandlerAnalysis.creation_time, Date) <= end_datetime.date(),
            IncidentHandlerAnalysis.type == 'SiaB',
            IncidentHandlerAnalysis.id != 1
        ).count()
        wtp_count = db.session.query(IncidentHandlerAnalysis).filter(
            cast(IncidentHandlerAnalysis.creation_time, Date) >= start_datetime.date(),
            cast(IncidentHandlerAnalysis.creation_time, Date) <= end_datetime.date(),
            IncidentHandlerAnalysis.type == 'WTP',
            IncidentHandlerAnalysis.id != 1
        ).count()
        wiab_count = db.session.query(IncidentHandlerAnalysis).filter(
            cast(IncidentHandlerAnalysis.creation_time, Date) >= start_datetime.date(),
            cast(IncidentHandlerAnalysis.creation_time, Date) <= end_datetime.date(),
            IncidentHandlerAnalysis.type == 'WiaB',
            IncidentHandlerAnalysis.id != 1
        ).count()
        total_count = siab_count + wtp_count + wiab_count
        return total_count, siab_count, wtp_count, wiab_count


class TransdataTools:
    def __init__(self) -> None:
        pass

    @staticmethod
    def cal_week_of_year(date):
        date_obj = datetime.strptime(date, '%Y-%m-%d')
        year, week, _ = date_obj.isocalendar()
        return year, week

    @staticmethod
    def timestamp_to_date(timestamp):
        cet_time = pd.to_datetime(timestamp, unit='s', utc=True)
        date_string = cet_time.strftime('%Y-%m-%d')
        return date_string

    @staticmethod
    def cal_week_date(year, week):
        """
            get week dates of the year
            input: year& week num
            output:start_date~end_date(Mon~Sun)
        """
        first_day_of_year = pd.Timestamp(year=year, month=1, day=1)
        # cal first week of Monday(ISO 8601)
        if first_day_of_year.weekday() <= 3:  # if first day is Mon to Thr
            first_week_start = first_day_of_year - pd.DateOffset(days=first_day_of_year.weekday())
        else:  # if first day is Fri to Sun
            first_week_start = first_day_of_year + pd.DateOffset(days=(7 - first_day_of_year.weekday()))
        # cal start and end date
        mon_date = first_week_start + pd.DateOffset(weeks=week - 1)
        sun_date = mon_date + pd.DateOffset(days=6)
        return str(mon_date.date()), str(sun_date.date())

    @staticmethod
    def cal_all_mon_sun_of_one_month(year, month):
        """
            cal all weeks Mon and Sun dates of the month
            return week list (week_num1,sun_date,mon_date;week_num2,sun_date,mon_date...)
        """
        month_calendar = calendar.monthcalendar(year, month)
        mondays_and_sundays = []
        for week in month_calendar:
            monday = week[0]
            sunday = week[6]

            if monday == 0:
                continue
            monday_date = datetime(year, month, monday).strftime('%Y-%m-%d')
            _, week_num = TransdataTools.cal_week_of_year(monday_date)

            if sunday == 0:
                next_month = month + 1 if month < 12 else 1
                next_year = year if month < 12 else year + 1
                sunday = week[6] = 7 - week.index(0)
                sunday_date = datetime(next_year, next_month, sunday).strftime('%Y-%m-%d')
            else:
                sunday_date = datetime(year, month, sunday).strftime('%Y-%m-%d')

            mondays_and_sundays.append({
                    'week':  week_num,
                    'monday': monday_date,
                    'sunday': sunday_date
                })
        return mondays_and_sundays

    @staticmethod
    def get_weeks_in_range(year1, week_num1, year2, week_num2):
        """
            get all Monday and Sunday date for above range
        """
        weeks_info = []
        start_year = year1
        start_week = week_num1
        end_year = year2
        end_week = week_num2
        for year in range(start_year, end_year + 1):
            for week in range(1, 54):
                if year == start_year and week < start_week:
                    continue
                if year == end_year and week > end_week:
                    continue
                monday, sunday = TransdataTools.cal_week_date(year, week)
                week_info = {
                    'year': year,
                    'week': week,
                    'monday': monday,
                    'sunday': sunday
                }
                weeks_info.append(week_info)
        return weeks_info

    @staticmethod
    def cal_cases_count_each_week(week_list):  # date type str
        """
            use for cal each week data
        """
        results = []
        for index, week_data in enumerate(week_list, start=1):
            start_date = week_data['monday']
            end_date = week_data['sunday']
            week_num = 'week' + str(week_data['week'])
            total_num, siab_num, wtp_num, wiab_num = Sqlquery.query_cases_for_dates(start_date, end_date)
            if total_num != 0:
                results.append({
                    "index": index,
                    "label": week_num,
                    "sum": total_num,
                    "siab": siab_num,
                    "wtp": wtp_num,
                    "wiab": wiab_num
                })
        final_result = {
            "date_column": results
        }
        data = jsonify(final_result).get_data(as_text=True)
        return data

    @staticmethod
    def cal_cases_count_one_week(week_num, start_date, end_date):  # date type str
        """
            use for cal one week data api
        """
        results = []
        total_num, siab_num, wtp_num, wiab_num = Sqlquery.query_cases_for_dates(start_date, end_date)
        if total_num != 0:
            results.append({
                "index": 1,
                "label": week_num,
                "sum": total_num,
                "siab": siab_num,
                "wtp": wtp_num,
                "wiab": wiab_num
            })
        final_result = {
            "date_column": results
        }
        data = jsonify(final_result).get_data(as_text=True)
        return data

    @staticmethod
    def cal_all_model_count(start_date_str, end_date_str):
        """
            calculate all models count
            output like SiaB:{models: counts} WiaB:{models: counts} WTP{models: counts}
        """
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        model_counts = {
            'siab': {v: 0 for v in IncHandlerCfg.MODEL_MAPPING.values() if v.startswith('SiaB-')},
            'wtp': {v: 0 for v in IncHandlerCfg.MODEL_MAPPING.values() if v.startswith('WTP-')},
            'wiab': {v: 0 for v in IncHandlerCfg.MODEL_MAPPING.values() if v.startswith('WiaB-')}
        }
        all_records = Sqlquery.query_cases_by_dates_only(start_date, end_date)
        for record in all_records:
            model_name = record.model
            if model_name == 'SiaB-Others':
                model_counts['siab']['SiaB-Others'] += 1
            elif model_name == 'WiaB-Others':
                model_counts['wiab']['WiaB-Others'] += 1
            elif model_name.startswith('SiaB-'):
                model_counts['siab'][model_name] += 1
            elif model_name.startswith('WTP-'):
                model_counts['wtp'][model_name] += 1
            elif model_name.startswith('WiaB-'):
                model_counts['wiab'][model_name] += 1
        result = jsonify({"model_column": model_counts})
        data = result.get_data(as_text=True)
        return data

    @staticmethod
    def cal_top_model_count(json_data, top_n=5):
        """
            cal top 5 models by self.cal_all_model_count()
        """
        data = json.loads(json_data)
        model_counts = Counter()
        for metrics in data["model_column"].values():
            # count model > 0 only
            model_counts.update({key: count for key, count in metrics.items() if count > 0})
        top_5_models = model_counts.most_common(top_n)
        sorted_models = sorted(top_5_models, key=lambda item: item[1])
        sorted_top_5_models = [{"model": model, "count": count} for model, count in sorted_models]
        results = jsonify({"top5_models": sorted_top_5_models})
        data = results.get_data(as_text=True)
        return data

    @staticmethod
    def merge_top5_data(final_result, start_date, end_date):
        """
            start_date,end_date + cal_all_model_count --> all model count
            update start_date,end_date to DB
            all model count + cal_all_model_count --> top5 models
            final_result merge top5 models --> merged json data for chart
        """
        model_count_json = TransdataTools.cal_all_model_count(start_date, end_date)
        top5_models = TransdataTools.cal_top_model_count(json_data=model_count_json, top_n=5)
        model_count_data = json.loads(top5_models)
        result = json.loads(final_result)
        merged_data = {**result, **model_count_data}
        merged_json = jsonify(merged_data)
        data = merged_json.get_data(as_text=True)
        return data


class IncidentTransdata:
    def __init__(self) -> None:
        pass

    @staticmethod
    def get_download_sheet(start_timestamp, end_timestamp):
        """
            --API download incident all models sheet
        """
        start_date = TransdataTools.timestamp_to_date(start_timestamp)
        end_date = TransdataTools.timestamp_to_date(end_timestamp)
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
        model_table = []
        try:
            all_records = Sqlquery.query_cases_by_dates_only(start_datetime, end_datetime)
            for _, record in enumerate(all_records):
                model_table.append({
                    "model_name": record.model,
                    "inc_num": record.inc_num,
                    "priority": record.priority,
                    "site": record.site,
                    "short_description": record.short_description,
                    "assignee": record.assigned,
                    "creation_time": record.creation_time,
                    "duration_time": record.duration_time,
                    "resolved_time": record.resolved_time
                })
            df = pd.DataFrame(model_table)
            sorted_df = df.sort_values(by='model_name')
            return sorted_df
        except Exception:
            return None

    @staticmethod
    def get_inc_info_sheet(start_timestamp, end_timestamp, model_name):
        """
            --API get sheet table--
            1.get start,end date from db
            2.search data by start,end date and model_name
        """
        start_date_str = TransdataTools.timestamp_to_date(start_timestamp)
        end_date_str = TransdataTools.timestamp_to_date(end_timestamp)
        model_table = []
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        all_records = Sqlquery.query_cases_by_model(model_name, start_date, end_date)
        for index, record in enumerate(all_records, start=1):
            model_table.append({
                "index": index,
                "inc_num": record.inc_num,
                "priority": record.priority,
                "site": record.site,
                "short_description": record.short_description,
                "assignee": record.assigned,
                "creation_time": record.creation_time,
                "duration_time": record.duration_time,
                "resolved_time": record.resolved_time
            })
        results = jsonify(model_table)
        data = results.get_data(as_text=True)
        return data

    @staticmethod
    def get_chart_by_custom(start_timestamp, end_timestamp):
        """
            --API for custom weeks--
            1.year1,month1,year2,month2 + get_weeks_in_range --> week list
            2.week_list + cal_cases_count_each_week --> date_column(result_data)
            3.week_list + cal_start_end_date --> start_date, end_date
            4.result_data,start_date,end_date + merge_top5_data --> date_column+top5 models
        """
        start_date = TransdataTools.timestamp_to_date(start_timestamp)
        end_date = TransdataTools.timestamp_to_date(end_timestamp)
        year1, week_num1 = TransdataTools.cal_week_of_year(start_date)
        year2, week_num2 = TransdataTools.cal_week_of_year(end_date)
        all_week_dates = TransdataTools.get_weeks_in_range(year1, week_num1, year2, week_num2)
        result_data = TransdataTools.cal_cases_count_each_week(all_week_dates)
        final_data = TransdataTools.merge_top5_data(result_data, start_date, end_date)
        return final_data

2025-08-01 11:22:47,060 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 11:22:47,060 INFO params: None
2025-08-01 11:22:47,060 INFO User: <EMAIL>
2025-08-01 11:22:47,062 INFO payload: None
2025-08-01 11:22:47,062 INFO files: None
2025-08-01 11:22:47,062 INFO timeout: 30
2025-08-01 11:22:48,894 INFO Getting host list from RETSEELM-NXC000.
2025-08-01 11:22:48,894 INFO Got the host list from RETSEELM-NXC000.
2025-08-01 11:22:48,894 INFO Got the host list.
2025-08-01 11:22:48,894 INFO Getting vault from IKEAD2.
2025-08-01 11:22:49,518 INFO Getting Site_Pe_Nutanix.
2025-08-01 11:22:50,028 INFO Got Site_Pe_Nutanix.
2025-08-01 11:22:50,029 INFO Getting Site_Pe_Admin.
2025-08-01 11:22:50,494 INFO Got Site_Pe_Admin.
2025-08-01 11:22:50,494 INFO Getting Site_Oob.
2025-08-01 11:22:51,022 INFO Got Site_Oob.
2025-08-01 11:22:51,023 INFO Getting Site_Ahv_Nutanix.
2025-08-01 11:22:51,509 INFO Got Site_Ahv_Nutanix.
2025-08-01 11:22:51,510 INFO Getting Site_Ahv_Root.
2025-08-01 11:22:51,998 INFO Got Site_Ahv_Root.
2025-08-01 11:22:51,999 INFO Getting Site_Gw_Priv_Key.
2025-08-01 11:22:52,506 INFO Got Site_Gw_Priv_Key.
2025-08-01 11:22:52,506 INFO Getting Site_Gw_Pub_Key.
2025-08-01 11:22:52,985 INFO Got Site_Gw_Pub_Key.
2025-08-01 11:22:52,985 INFO Getting Site_Pe_Svc.
2025-08-01 11:22:53,501 INFO Got Site_Pe_Svc.
2025-08-01 11:22:53,521 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 11:22:53,521 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-01 11:22:53,521 INFO params: None
2025-08-01 11:22:53,521 INFO User: <EMAIL>
2025-08-01 11:22:53,521 INFO payload: None
2025-08-01 11:22:53,522 INFO files: None
2025-08-01 11:22:53,522 INFO timeout: 30
2025-08-01 11:22:54,866 INFO Got the VM list from RETSEELM-NXC000.
2025-08-01 11:22:54,919 INFO ****************************************************************************************************
2025-08-01 11:22:54,919 INFO *                                                                                                  *
2025-08-01 11:22:54,919 INFO *                                    Shutting down the cluster.                                    *
2025-08-01 11:22:54,919 INFO *                                                                                                  *
2025-08-01 11:22:54,919 INFO ****************************************************************************************************
2025-08-01 11:22:54,919 INFO SSH connecting to the CVM.
2025-08-01 11:22:54,919 INFO SSH connecting to ***********, this is the '1' try.
2025-08-01 11:22:57,422 INFO SSH connected to ***********.
2025-08-01 11:23:04,042 INFO Sending 'cluster stop' to the server.
2025-08-01 11:23:08,919 INFO Receiving the output .
2025-08-01 11:23:08,919 INFO Received the output: #SSH OUTPUT START#.
2025-08-01 11:23:08,919 INFO [1;23r[23;80H
[22;46Hcluster stop[1;24r[23;1H[1;23r[23;80H


[20;1H2025-08-01 03:23:08,466Z INFO MainThread zookeeper_session.py:272 cluster is attempting to connect to Zookeeper (unestablished session (object 0x7f74e4d3fd00)), host port list zk3:9876,zk1:9876,zk2:9876[1;24r[23;1H[1;23r[23;80H


[20;1H2025-08-01 03:23:08,466Z INFO MainThread patterns.py:63 Creating a new instance for ZookeeperSession[('client_id', None), ('connection_timeout', None), ('host_port_list', 'zk3:9876,zk1:9876,zk2:9876'), ('use_zk_mt', None)][1;24r[24;1H[7m[61] 0:python3.9*                        "ntnx-cz20240j8s-a-cvm" 05:23 01-Aug-25[m[23;1H[1;23r[23;80H


[20;1H2025-08-01 03:23:08,469Z INFO Dummy-1 zookeeper_session.py:933 ZK session establishment complete, session 0x19840c579d26470 (object 0x7f74e4d3fd00), negotiated timeout=20 secs[1;24r[23;1H[1;23r[23;80H

[21;1H2025-08-01 03:23:08,482Z INFO MainThread cluster:3607 Executing action stop on SVMs ***********,***********,***********[1;24r[23;1H[1;23r[23;80H

[21;1H2025-08-01 03:23:08,483Z WARNING MainThread genesis_utils.py:406 Deprecated: use util.cluster.info.get_node_uuid() instead[1;24r[23;1H[1;23r[23;80H

[21;1H2025-08-01 03:23:08,483Z INFO MainThread patterns.py:65 Retrieved an existing instance for ZookeeperSession[1;24r[23;1H[1;23r[23;80H




[18;1H2025-08-01 03:23:08,498Z INFO MainThread cluster:3654 [20;1H***** CLUSTER NAME *****

RETSEELM-NXC000[1;24r[23;1H[1;23r[23;80H
[22;1HThis operation will stop the Nutanix storage services and any VMs using Nutanix storage will become unavailable. Do you want to proceed? (I agree/[N]): [1;24r[23;73H
2025-08-01 11:23:08,919 INFO #SSH OUTPUT END#
2025-08-01 11:23:23,566 INFO Sending 'I agree' to the server.

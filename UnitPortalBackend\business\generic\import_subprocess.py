import multiprocess
import subprocess
import time
import ctypes
import fetch_WindowsTaskName_And_Kill
import check_FileExists_BackupFile


class clsrun_backend_services_verifyservices:
    # Global declaration and assign values.
    global dis_path
    global src_folder
    global app_pid

    def __init__(self):
        self.dis_path = 'C:\\UnitPortalBackend-main'
        self.src_folder = 'C:\\UnitPortal_Backup\\'

    def run_command(self, command):
        try:
            # Run the command and capture the output
            # process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            result = subprocess.run(command, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            return result.returncode

        except subprocess.CalledProcessError as e:
            # print(f"An error occurred: {e}")
            return (f"Error executing command: {e}\n")

    def run_command_in_process(self, command):
        # Create a Process instance

        try:
            process = multiprocess.Process(target=self.run_command, args=(command,))
            print("Starting the App.py process...")
            # Start the process
            # print(dir(process))
            process.start()
            time.sleep(10)
            # print(process.pid)
            self.app_pid = process.pid
            if process.is_alive():
                print("Main process waiting for the Sub_App process to complete...")
                # time.sleep(10)
                print("Starting the windows_service.py install...")
                # cmd1 = 'python \"C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\UnitPortalBackend\\windows_service.py install\"'
                cmd1 = 'python "C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\UnitPortalBackend\\windows_service.py" install'
                ctypes.windll.shell32.ShellExecuteW(None, "runas", "cmd.exe", f"/c {cmd1}", None, 1)
                # install_output = ctypes.windll.shell32.ShellExecuteW(None, "runas", "cmd.exe", f"/c {cmd1}", None, 1)
                time.sleep(10)
                # print(install_output)
                print("windows_service.py installation Sucessfull...")
                print("Starting the windows_service.py start...")
                cmd2 = 'python "C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\UnitPortalBackend\\windows_service.py" start'
                ctypes.windll.shell32.ShellExecuteW(None, "runas", "cmd.exe", f"/c {cmd2}", None, 1)
                time.sleep(10)
                # print(run_output)
                print("windows_service.py started sucessfully..")
                # break
                print("Kill the app.py process id")
                objkill = fetch_WindowsTaskName_And_Kill.clsfetch_WindowsTask_Name_And_Kill()
                objkill. kill_service_process_pid(self.app_pid)
                print("All the Process completed.")

            elif (process.exitcode == 0):
                print(f"An error occurred: while running app.py.. kill app pid")
                objkill = fetch_WindowsTaskName_And_Kill.clsfetch_WindowsTask_Name_And_Kill()
                objkill.kill_service_process_pid(self.app_pid)
                print(f"revoke the function to install latest backup code!!")
                objbackup = check_FileExists_BackupFile.clscheck_Files_Backup_Files()
                objbackup.unzip_latest_zip(self.src_folder, self.dis_path)
                print("error occurred: while running services the process unsucessfull!!.")

        except Exception as e:
            print(f"An error occurred: {e}")
            print(f"An error occurred: while running services.. kill app pid")
            objkill = fetch_WindowsTaskName_And_Kill.clsfetch_WindowsTask_Name_And_Kill()
            objkill.kill_service_process_pid(self.app_pid)
            print("error occurred: while running services the process unsucessfull!!.")


if __name__ == "__main__":
    # Example command to run
    # command_to_run = 'python \"C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\UnitPortalBackend\\app.py\"'
    command_to_run = 'python "C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\UnitPortalBackend\\app.py"'
    # Calling Class object to invoke the methods/defination.
    funinvokeServices = clsrun_backend_services_verifyservices()
    # Run the command in a separate process
    funinvokeServices.run_command_in_process(command_to_run)

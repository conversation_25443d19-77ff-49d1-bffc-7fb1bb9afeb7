from marshmallow import Schema, fields
from swagger.common_schema import LogSchema
from models.pm_models import ModelSLIPMLogSchema

class CreateSliPmRequestSchema(Schema):
    cluster = fields.Str(required=True)
    description = fields.Str(required=True, default='webpage entrance')
    pmtype = fields.Str(required=True)
    scheduledate = fields.Str()
    startnow = fields.Bool()
    vcenter = fields.Str(required=True)

class ListSLIVmRequestSchema(Schema):
    country_code = fields.Str(required=True)
    site_code    = fields.Str(required=True)
    cluster      = fields.Str(required=True)
    vc           = fields.Str(required=True)

class SLIVmSchema(Schema):
    name = fields.Str()
    state = fields.Str()
    is_controller_vm = fields.Bool()

class CreateSliPmResponseSchema(Schema):
    cluster = fields.Str()
    createdate = fields.Str()
    creater = fields.Str()
    createrinfo = fields.Str()
    description = fields.Str()
    detaillogpath = fields.Str()
    id = fields.Int()
    pid = fields.Int()
    pmtype = fields.Str()
    startdate = fields.Str()
    status = fields.Str()
    vcenter = fields.Str()


class UpdateSliPmRequestSchema(Schema):
    vcenter = fields.Str(required=True)
    cluster = fields.Str(required=True)
    startdate = fields.Str()
    id = fields.Int(required=True)
    pmtype = fields.Str(required=True)
    description = fields.Str(required=True)


class AbortSliPmRequestSchema(Schema):
    cluster = fields.Str()
    createdate = fields.Str()
    creater = fields.Str()
    createrinfo = fields.Str()
    description = fields.Str()
    detaillogpath = fields.Str(required=True)
    id = fields.Int(required=True)
    latestlog = fields.Nested(LogSchema)
    logs = fields.List(fields.Nested(LogSchema))
    pid = fields.Int(required=True)
    pmtype = fields.Str()
    prism = fields.Str()
    startdate = fields.Str()
    status = fields.Str()

class GetSliPmTaskInfoResponseSchema(Schema):
    id = fields.Int()
    prism = fields.Str()
    cluster = fields.Str()
    startdate = fields.DateTime()
    pmtype = fields.Str()
    status = fields.Str()
    creater = fields.Str()
    createdate = fields.DateTime()
    createrinfo = fields.Str()
    description = fields.Str()
    pid = fields.Int()
    detaillogpath = fields.Str()
    logs = fields.List(fields.Nested(ModelSLIPMLogSchema))

class AbortSliPmResponseSchema(Schema):
    message = fields.Str()


class DeleteSliPmRequestSchema(Schema):
    id = fields.Int(required=True)


class DeleteSliPmResponseSchema(Schema):
    # message = fields.Str()
    pass

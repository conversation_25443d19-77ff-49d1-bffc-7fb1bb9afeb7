
from marshmallow import Schema, fields, validate, EXCLUDE, validates_schema, ValidationError

from models.workload_models import ModelWorkloadTemplate
from static.WORKLOAD_SPEC import WorkloadSpec
from swagger.workload_schema import validate_disk_sum, CreateWorkloadRequestSchema, WorkloadSchema


class CreateTemplateRequestSchema(WorkloadSchema):
    name = fields.Str(required=True)
    naming_convention = fields.Str(required=True)
    description = fields.Str(allow_none=True)
    image_name = fields.Str(allow_none=True)
    cpu = fields.Int(strict=True, required=True)
    cpu_core = fields.Int(strict=True, required=True)
    memory = fields.Int(strict=True, required=True)
    disk = fields.List(
        fields.Int(strict=True, validate=validate.Range(0, 2000)),
        validate=validate_disk_sum,
        required=True
    )
    vlan_id = fields.Int(strict=True, required=True)
    image_id = fields.Int(strict=True, required=True)

    @validates_schema
    def validate(self, data, **kwargs):
        self.validate_cpu(data)
        self.validate_image_existence(data)
        self.validate_image_and_workload_type(data)
        self.validate_vlan_id_existence_in_db(data)
        if data.get(WorkloadSpec.PACKAGES):
            self.validate_supported_packages(data)
        if data.get(WorkloadSpec.LINUX_PAYLOAD):
            self.validate_linux_payload_json(data)


class DeleteTemplateRequestSchema(Schema):
    template_ids = fields.List(fields.Int(strict=True,))

    @validates_schema
    def validate_template(self, data, **kwargs):
        template_ids = data.get("template_ids")
        existing_templates = ModelWorkloadTemplate.query.filter(ModelWorkloadTemplate.id.in_(template_ids)).all()
        existing_ids = [item.id for item in existing_templates]
        if len(existing_ids) != len(template_ids):
            ids = [i for i in template_ids if i not in existing_ids]
            raise ValidationError(f"Can't find existing template with id: {ids}!")


class UpdateTemplateRequestSchema(WorkloadSchema):
    template_id = fields.Int(required=True)

    def validate_cpu(self, data):
        template_id = data[WorkloadSpec.TEMPLATE_ID]
        if not data.get(WorkloadSpec.CPU):
            data[WorkloadSpec.CPU] = ModelWorkloadTemplate.query.filter_by(id=template_id).one().cpu
        if not data.get(WorkloadSpec.CPU_CORE):
            data[WorkloadSpec.CPU_CORE] = ModelWorkloadTemplate.query.filter_by(id=template_id).one().cpu_core
        super().validate_cpu(data)

    def validate_image_and_workload_type(self, data):
        template_id = data[WorkloadSpec.TEMPLATE_ID]
        if not data.get(WorkloadSpec.WORKLOAD_TYPE):
            data[WorkloadSpec.WORKLOAD_TYPE] = ModelWorkloadTemplate.query.filter_by(id=template_id).one().workload_type
        if not data.get(WorkloadSpec.IMAGE_ID):
            data[WorkloadSpec.IMAGE_ID] = ModelWorkloadTemplate.query.filter_by(id=template_id).one().image_id
        super().validate_image_and_workload_type(data)

    @validates_schema
    def validate(self, data, **kwargs):
        self.validate_template_existence(data)
        if data.get(WorkloadSpec.CPU) or data.get(WorkloadSpec.CPU_CORE):
            self.validate_cpu(data)
        if data.get(WorkloadSpec.IMAGE_ID):
            self.validate_image_existence(data)
        if data.get(WorkloadSpec.IMAGE_ID) or data.get(WorkloadSpec.WORKLOAD_TYPE):
            self.validate_image_and_workload_type(data)
        if data.get(WorkloadSpec.VLAN_ID):
            self.validate_vlan_id_existence_in_db(data)
        if data.get(WorkloadSpec.PACKAGES):
            self.validate_supported_packages(data)
        if data.get(WorkloadSpec.LINUX_PAYLOAD):
            self.validate_linux_payload_json(data)

-- ----------------------------
-- Table structure for incident_reminder_user
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[incident_reminder_user]') AND type IN ('U'))
	DROP TABLE [dbo].[incident_reminder_user]

CREATE TABLE incident_reminder_user (
	id int IDENTITY(1,1) NOT NULL,
	name nvarchar(255) NULL,
	email_address nvarchar(255) NULL,
	email_address_shortdescription nvarchar(255) NULL,
	region nvarchar(255) NULL,
);

-- ----------------------------
-- Primary Key structure for incident_reminder_user
-- ----------------------------

ALTER TABLE [dbo].[incident_reminder_user] ADD CONSTRAINT [PK__dh_ntx__3213E1831K65801C] PRIMARY KEY ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = OFF, ALLOW_PAGE_LOCKS = OFF)
ON [PRIMARY]

-- ----------------------------
-- Records of [incident_reminder_user]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[incident_reminder_user] ON

INSERT INTO [dbo].[incident_reminder_user] ([id], [name], [email_address], [email_address_shortdescription], [region]) VALUES 
	(1, 'ivfan', '<EMAIL>', 'Ivan Fan', 'CN'),
    (2, 'sanoj', '<EMAIL>', 'Jonas Karlsson', 'EU'),
    (3, 'witry', '<EMAIL>', 'William Tryon', 'US'),
    (4, 'yupen', '<EMAIL>', 'YuWei Peng', 'CN'),
    (5, 'gllg', '<EMAIL>', 'Patrick Gallagher', 'US'),
    (6, 'shcor9', '<EMAIL>', 'Shawn Cordeíro', 'EU'),
    (7, 'iswan2', '<EMAIL>', 'Isacc Wang', 'CN'),
    (8, 'husha3', '<EMAIL>', 'Hussain Shariff', 'EU'),
    (9, 'raygu1', '<EMAIL>', 'Ray Gu', 'CN'),
    (10, 'jawan36', '<EMAIL>', 'Jackie Wang', 'CN');

INSERT INTO [dbo].[incident_reminder_user] ([id], [name], [email_address], [email_address_shortdescription], [region]) VALUES
    (11, 'dowi', '<EMAIL>', 'Donna Wilson', 'US'),
    (12, 'hunhe', '<EMAIL>', 'Hunter He', 'CN'),
    (13, 'mipen20', '<EMAIL>', 'Miya Peng', 'CN'),
    (14, 'pmil', '<EMAIL>', 'Peter Milanovic', 'EU'),
    (15, 'smde', '<EMAIL>', 'Salvatore DeJohn', 'US'),
    (16, 'rocli4', '<EMAIL>', 'Rocky Li', 'CN'),
    (17, 'shtao8', '<EMAIL>', 'Shuai Tao', 'CN'),
    (18, 'yawan135', '<EMAIL>', 'Wang Yan', 'EU'),
    (19, 'epjia', '<EMAIL>', 'Ephraim Jiang', 'CN'),
    (20, 'gache17', '<EMAIL>', 'Gangadathan Cherukunnu Kalathil', 'EU');

SET IDENTITY_INSERT [dbo].[incident_reminder_user] OFF
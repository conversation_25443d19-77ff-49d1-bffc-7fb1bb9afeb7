{"Tasks": [{"General": {"Name": "Run-Job-BackupDpcDatabase", "Description": "Backup up the database"}, "Triggers": {"CalendarTrigger": {"StartBoundary": "2023-01-01T00:00:00", "Enabled": true, "ScheduleByDay": {"DaysInterval": 1}}}, "Settings": {"IdleSettings": {"WaitTimeout": "PT1H"}}}, {"General": {"Name": "Run-Job-RetailNtxState", "Description": "Fetch and push metric data of PEs to DB backend"}, "Triggers": {"CalendarTrigger": {"StartBoundary": "2023-01-01T09:00:00", "Enabled": true, "ScheduleByDay": {"DaysInterval": 1}}}, "Settings": {"IdleSettings": {"WaitTimeout": "PT1H"}}}, {"General": {"Name": "Run-Job-RetailNtxSizing", "Description": "Fetch and push sizing data of PEs to DB backend"}, "Triggers": {"CalendarTrigger": {"StartBoundary": "2023-01-01T10:00:00", "Enabled": true, "ScheduleByDay": {"DaysInterval": 1}}}, "Settings": {"IdleSettings": {"WaitTimeout": "PT1H"}}}, {"General": {"Name": "Run-Job-SLICluster-v01", "Description": "Fetch and push sizing data of SLI Clusters and Hosts to DB backend"}, "Triggers": {"CalendarTrigger": {"StartBoundary": "2023-01-01T02:00:00", "Enabled": true, "ScheduleByDay": {"DaysInterval": 1}}}, "Settings": {"IdleSettings": {"WaitTimeout": "PT1H"}}}, {"General": {"Name": "Run-Job-SLIVMs-v01", "Description": "Fetch and push sizing data of SLI VMs to DB backend"}, "Triggers": {"CalendarTrigger": {"StartBoundary": "2023-01-01T03:00:00", "Enabled": true, "ScheduleByDay": {"DaysInterval": 1}}}, "Settings": {"IdleSettings": {"WaitTimeout": "PT1H"}}}]}
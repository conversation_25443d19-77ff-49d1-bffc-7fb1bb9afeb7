#installed modules
import json
from flask_restful import Resource# type: ignore 
import werkzeug.exceptions as flaskex
from flask import abort, request, Response
from flask_apispec.views import MethodResource
from flask_apispec import marshal_with, use_kwargs, doc

#local files
from business.distributedhosting.platform import Platform
from business.authentication.authentication import ServiceAccount
from business.authentication.tokenvalidation import PrivilegeValidation
from swagger.platform_schema import CheckSiteExistenceRequestSchema, CheckSiteExistenceResponseSchema
from .route import route


@route('/api/v1/siteexistence')
class RestfulSiteExistenceRealTime(MethodResource, Resource):
    @doc(description="Check Site Existence Real Time", tags=['View Resources'])
    @use_kwargs(CheckSiteExistenceRequestSchema, location='json')
    @marshal_with(CheckSiteExistenceResponseSchema, code=200)
    @PrivilegeValidation(privilege={"role_pm": "create_ntx_pm"})    # TODO:?
    def post(self, *args, **kwargs): # pylint: disable=W0613
        try:
            data = request.get_json(force=True)
            platform = Platform()
            res, msg = platform.check_site_existence(country_code= data['country_code'], site_code= data['site_code'])# type: ignore 
            if res:
                return Response(json.dumps(msg), status=200, mimetype='application/json')
            raise Exception({'code': 500, 'message': msg})
        except flaskex.BadRequest :
            abort(400, "Please make sure that you have a valid payload.")
        except Exception as e:
            if e.args:
                if type(e.args[0]) == dict:
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
                    else:
                        abort(500, e)
                else:
                    abort(500, e)       
            else:
                abort(500, e)


@route('/api/v1/siteexistence_db')
class RestfulSiteExistenceFromDb(MethodResource, Resource):
    @doc(description="Check Site Existence From Database", tags=['View Resources'])
    @use_kwargs(CheckSiteExistenceRequestSchema, location='json')
    @marshal_with(CheckSiteExistenceResponseSchema, code=200)
    @PrivilegeValidation(privilege={"role_pm": "create_ntx_pm"})    # TODO:?
    def post(self, *args, **kwargs): # pylint: disable=W0613
        try:
            data = request.get_json(force=True)
            platform = Platform()
            res = platform.check_site_existence_in_db(country_code=data['country_code'], site_code=data['site_code'])
            return Response(json.dumps(res), status=200, mimetype='application/json')
        except Exception as e:
            raise flaskex.InternalServerError(str(e))


@route('/api/v1/sa/list')
class RestfulSAList(Resource):
    def get(self):
        try:
            sa_list = ServiceAccount.get_service_account_list()
            return [{
                'id': sa['id'],
                'username': sa['username'],
                'usage': sa['usage'],
            } for sa in sa_list]
        except flaskex.BadRequest as badrequest:
            abort(400, f"Client error, {str(badrequest)}")
        except Exception as e:
            if e.args:
                if type(e.args[0]) == dict:
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
                    else:
                        abort(500, e)
                else:
                    abort(500, e)       
            else:
                abort(500, e)


@route(['/api/v1/sa', '/api/v1/sa/<sa_id>'])
class RestfulSa(Resource):
    @PrivilegeValidation(privilege={"role_administration": "view_role"})
    # @use_kwargs()       # TODO
    def post(self):
        param = request.get_json(force=True)
        ServiceAccount.create_service_account(**param)

    @PrivilegeValidation(privilege={"role_administration": "view_role"})
    # @use_kwargs()       # TODO
    def delete(self, sa_id):
        ServiceAccount.delete_service_account(sa_id)

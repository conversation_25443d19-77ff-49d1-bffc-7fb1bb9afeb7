from business.distributedhosting.nutanix.task_status import NewClusterTaskStatus

stage_scope = {
    2: {
        "func": "stage2_detect_available_nodes",
        "pause_after_stage": True,
        "post_status": NewClusterTaskStatus.WAITING_USER_INPUT,
    },
    3: {
        "func": "stage3_pre_check",
    },
    4: {
        "func": "stage4_create_vault_secret",
    },
    5: {
        "func": "stage5_upgrade_foundation"
    },
    6: {
        "func": "stage6_deploy_cluster",
        "post_status": NewClusterTaskStatus.DEPLOYING_CLUSTER,
    },
    7: {
        "func": "stage7_configure_cluster"
    },
    8: {
        "func": "stage8_rotate_password"
    },
    9: {
        "func": "stage9_configure_oob"
    }
}
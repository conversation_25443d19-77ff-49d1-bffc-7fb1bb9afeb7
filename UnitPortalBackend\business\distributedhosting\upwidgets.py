import datetime
from flask import jsonify
from functools import wraps
from flask import request

from models.database import db
from sqlalchemy import func, or_
from models.auth_models import ModelUser
from business.generic.commonfunc import  get_request_token
from models.workload_models import ModelWorkloadTask
from models.ntx_models_wh import  Model<PERSON><PERSON><PERSON>VETask
from models.pm_models import ModelNTXPMTask, ModelSLIPMTask
from models.up_widget_models import ModelUPFeedback, ModelUPFeedbackSchema, ModelUPAPIUtilization, ModelUPAPIUtilizationSchema, ModelUPVisit, \
    ModelUPAPIUtilizationLog
from models.atm_models import ModelRetailNutanixAutomationSPPLCMTask, ModelRetailNutanixAutomationAOSLCMTask, ModelNtxAutomationDscTask, \
    ModelNtxAutomationRenewCertificateTask, ModelNtxAutomationRotatePasswordTask

class ConsumerFeedback( ):
    def __init__(self, token=None) -> None:
        self.token = token
        
    
    def creat_feedback_record(self, comments, star, anonymous):
        task = dict()
        user = ModelUser.query.filter_by(token=self.token).first()
        
        task["name"] = user.username
        task["star"] = star
        task["comment"] = comments
        task["anonymous"] = anonymous
        task["date"] = datetime.datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
        comment = ModelUPFeedback(**task)
        db.session.add(comment)
        db.session.commit()
        
    def get_comments(self):
        user = ModelUser.query.filter_by(token=self.token).first()
        comments = ModelUPFeedback.query.order_by(ModelUPFeedback.date.desc()).all()
        _res = []
        _commentschema = ModelUPFeedbackSchema()
        for comment in comments:
            _comment = _commentschema.dump(comment)
            if _comment['name'] == user.username:
                _comment['if_namematched'] = True
            else:
                _comment['if_namematched'] = False
            if _comment['anonymous']:
                _comment['name'] = 'Anonymous'
            _res.append(_comment)
        return jsonify(_res)
    
    def update_comments(self, data):
        id = data['id'] 
        __comment__ = ModelUPFeedback.query.filter_by(id=id).first()
        __comment__.comment = data['comment']
        __comment__.solution = data['solution']
       
        db.session.commit()


class WebPageTracer( ):
    
    def __init__(self, token=None) -> None:
        self.token = token
    
    def webpagecounter(self, pagename):
        page = dict()
        page["page_name"] = pagename
        page["date"] = datetime.datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
        apiutilization = ModelUPVisit(**page)
        db.session.add(apiutilization)
        db.session.commit()

    def get_webcount_list(self, starttime=None, endtime=None):
        try:
            query = ModelUPVisit.query
            if starttime and endtime:
                query = query.filter(
                    ModelUPVisit.date >= starttime,
                    ModelUPVisit.date <= endtime
                )
            traffic = {
                'dashboard': ['Dashboard'],
                'pm': ['NutanixPM', 'SimpliVityPM'],
                'workload': ['Workload', 'Template'],
                'maintenance': ['Auto maintenance', 'Toolbox', 'NTX Move WH', 'AOS', 'LCM',],
            }
            traffic_counts = {key: 0 for key in traffic.keys()}
            visits = query.with_entities(ModelUPVisit.page_name).all()
            page_names = [visit.page_name for visit in visits]
            for page_name in page_names:
                for key, values in traffic.items():
                    if page_name in values: 
                        traffic_counts[key] += 1 
            return jsonify(traffic_counts)
        except Exception as e:
            return str(e)


class TasksCounter():
    def __init__(self, token=None) -> None:
        self.token = token

    def task_counter(self, starttime=None, endtime=None):
        counts = {}
        tasks_list = {
            "pm": [ModelNTXPMTask, ModelSLIPMTask],
            "workload": [ModelWorkloadTask],
            "automation": [
                ModelWHMOVETask,
                ModelNtxAutomationDscTask,
                ModelRetailNutanixAutomationSPPLCMTask,
                ModelRetailNutanixAutomationAOSLCMTask,
                ModelNtxAutomationRenewCertificateTask,
                ModelNtxAutomationRotatePasswordTask
            ],
        }
        try:
            for key, model_names in tasks_list.items():
                total_count = 0 
                for model_name in model_names:
                    query = db.session.query(func.count(model_name.id))
                    date_field = None
                    if hasattr(model_name, "createdate"):
                        date_field = model_name.createdate
                    elif hasattr(model_name, "create_date"):
                        date_field = model_name.create_date
                    if starttime and endtime and date_field is not None:
                        query = query.filter(
                            date_field >= starttime,
                            date_field <= endtime
                        )
                    count = query.scalar()
                    total_count += count
                counts[key] = total_count
            return counts
        except :
            return None 


class ApiUtilization():
    def __init__(self, token=None) -> None:
        self.token = token

    def get_apiutil_list(self):
        try:
            apis = ModelUPAPIUtilization.query.all()
            apischema = ModelUPAPIUtilizationSchema(many=True)
            apiutillist = apischema.dump(apis)
            return jsonify(apiutillist)
        except Exception as e:
            return str(e)


class ApiUtilizationLog():
    def __init__(self, token=None) -> None:
        self.token = token

    def get_apiutillog_list(self, ismore=False, starttime=None, endtime=None):
        try:
            query = ModelUPAPIUtilizationLog.query
            if starttime and endtime:
                query = query.filter(
                    ModelUPAPIUtilizationLog.date >= starttime,
                    ModelUPAPIUtilizationLog.date <= endtime
                )
            if ismore:
                apis = (
                    query.with_entities(
                        ModelUPAPIUtilizationLog.api_path,
                        func.count(ModelUPAPIUtilizationLog.id).label('count')
                    )
                    .group_by(ModelUPAPIUtilizationLog.api_path)
                    .order_by(func.count(ModelUPAPIUtilizationLog.id).desc())
                    .all()
                )
                api_path_counts = {api_path: count for api_path, count in apis}
                return jsonify(api_path_counts)
            api_paths = [
                "/api/v1/login",
                "/api/v1/pm",
                "/api/v1/ntx/workload",
                "/api/v1/ntx/automation"
            ]
            filter_conditions = or_(
                *(ModelUPAPIUtilizationLog.api_path.like(f'%{path}%') for path in api_paths)
            )
            apis = (
                query.with_entities(
                    ModelUPAPIUtilizationLog.api_path,
                    func.count(ModelUPAPIUtilizationLog.id).label('count')
                )
                .filter(filter_conditions)
                .group_by(ModelUPAPIUtilizationLog.api_path)
                .all()
            )
            api_path_counts = {path: 0 for path in api_paths}
            for api_path, count in apis:
                for path in api_paths:
                    if path in api_path:
                        api_path_counts[path] += count
            return jsonify(api_path_counts)
        except Exception as e:
            return str(e)
    
# def counter(url):
#     def outter(func):
#         @wraps(func)
#         def wrapper(*args, **kwargs):
#             print(url)
#             print(":::::::")
#             # ModelUPAPIUtilization.query.filter_by(api_path)
#             wrapper.count = wrapper.count + 1
#             print('method: %s, count: %s' % (func.__name__, wrapper.count))
#             print(dir(func))
#             return func(*args, **kwargs)
#         wrapper.count = 0
#         return wrapper
#     return outter


class APITracer(object):
    def __init__(self):
        pass
        # self.path = path

    def __call__(self, func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if api := ModelUPAPIUtilization.query.filter_by(api_path=request.path).first():
                api.times = int(api.times) + 1
                db.session.commit()
            else:
                api = dict()                
                api["api_path"] = request.path
                api["times"] = 1
                apiutilization = ModelUPAPIUtilization(**api)
                db.session.add(apiutilization)
                db.session.commit()
                
            return func(*args, **kwargs)
        wrapper.count = 0
        return wrapper


class APILogTracer(object):
    def __init__(self):
        pass
        # self.path = path

    def __call__(self, func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            api = dict()
            if request.authorization.username:
                api["caller"] = request.authorization.username
            else:
                token = get_request_token()
                user = ModelUser.query.filter_by(token=token).first()
                api["caller"] = user.username
            api["api_path"] = request.path
            api["date"] = datetime.datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
            _api = ModelUPAPIUtilizationLog(**api)
            db.session.add(_api)
            db.session.commit()
                
            return func(*args, **kwargs)
        # wrapper.count = 0
        return wrapper
    

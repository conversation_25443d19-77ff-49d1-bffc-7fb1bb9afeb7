from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.generic.base_up_exception import GetPeFailed
from models.toolbox_models import ModelToolboxTask, ModelToolboxTaskSchema, ModelToolboxTaskLog, ModelToolboxTaskLogSchema
from business.distributedhosting.nutanix.nutanix import NutanixCLI
from models.ntx_models import ModelPrismElement, ModelPrismCentral
from models.ntx_models_wh import ModelWarehousePrismElement, ModelWarehousePrismCentral
import static.SETTINGS as SETTING


class UnlockAccountTask(BaseUpTask):
    TASK_TYPE = "UNLOCK_ACCOUNT"
    LOG_DIR = SETTING.UNLOCK_ACCOUNT_LOG_PATH
    LOG_TYPE = "UNLOCK_ACCOUNT"

    def __init__(self, payload=None):
        super().__init__(
            task_model=ModelToolboxTask,
            task_model_schema=ModelToolboxTaskSchema,
            log_model=ModelToolboxTaskLog,
            log_model_schema=ModelToolboxTaskLogSchema,
            payload=payload
        )
        self.pe_name = payload.get("pe_name", "").upper() if payload else None
        self.task_identifier = self.pe_name
        self.task_info = {
            "pe_name": self.pe_name,
            "task_type": self.TASK_TYPE
        }
        self.task_duplicated_kwargs = {
            "pe_name": self.pe_name,
            "task_type": self.TASK_TYPE
        }

    def task_process(self):
        """
        Implement the task_process method required by the base class
        This will be called by BaseUpTask.start_task
        """
        self.ilg.write(f"Starting to unlock account for PE: {self.pe_name}", severity="info")
        self.ilg.write("Getting PE information", severity="info")
        pe_model_warehouse = ModelWarehousePrismElement.query.filter_by(name=self.pe_name).first()
        if pe_model_warehouse:
            pe_model_class = ModelWarehousePrismElement
            pc_model_class = ModelWarehousePrismCentral
            pe_fqdn = pe_model_warehouse.fqdn
            pc_fqdn = pe_model_warehouse.prism
            self.ilg.write(f"Found PE in warehouse: {pe_fqdn}, PC: {pc_fqdn}", severity="info")
        else:
            pe_model_retail = ModelPrismElement.query.filter_by(name=self.pe_name).first()
            if pe_model_retail:
                pe_model_class = ModelPrismElement
                pc_model_class = ModelPrismCentral
                pe_fqdn = pe_model_retail.fqdn
                pc_fqdn = pe_model_retail.prism
                self.ilg.write(f"Found PE in retail: {pe_fqdn}, PC: {pc_fqdn}", severity="info")
            else:
                raise GetPeFailed(self.pe_name)

        self.ilg.write("Executing account unlock operation", severity="info")
        self.ilg.write("Connecting to Nutanix cluster...", severity="info")
        cli = NutanixCLI(pe=pe_fqdn, pc=pc_fqdn, logger=self.logger)
        self.ilg.write("Unlocking accounts...", severity="info")
        cli.unlock_account(prism_name=self.pe_name, pc_model_class=pc_model_class, pe_model_class=pe_model_class)
        self.ilg.write("Account unlocked successfully", severity="info")

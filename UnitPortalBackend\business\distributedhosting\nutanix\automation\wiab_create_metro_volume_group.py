import time
import ntnx_clustermgmt_py_client
import ntnx_datapolicies_py_client
import ntnx_networking_py_client
import ntnx_prism_py_client
import ntnx_volumes_py_client

from business.authentication.authentication import Vault
from business.distributedhosting.nutanix.automation.metro_vg_exception import SubnetNameDuplicatedOnNtx, CreateVolumeGroupFailed
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.distributedhosting.nutanix.base_up_task_property import BaseUpTaskProperty
from business.distributedhosting.nutanix.pc_components import RestProtectionRules, RestRecoveryPlans
from business.generic.base_up_exception import VaultGetSecretFailed
from models.metro_vg_models import ModelMetroVGTask, ModelMetroVGTaskSchema, ModelMetroVGTaskLog, ModelMetroVGTaskLogSchema
from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
from static.SETTINGS import METRO_VG_LOG_PATH


class WiaBCreateMetroVolumeGroup(BaseUpTask):
    LOG_DIR = METRO_VG_LOG_PATH
    TASK_TYPE = "CREATE_VG"

    def __init__(self, payload):
        super().__init__(
            ModelMetroVGTask, ModelMetroVGTaskSchema, ModelMetroVGTaskLog, ModelMetroVGTaskLogSchema, payload
        )
        self.payload = payload
        self.pe_prefix = payload.get("pe").upper().split('-')[0]
        self.pc_svc_account = self.init_svc_account()
        self.task_info = {
            BaseUpTaskProperty.PE:          payload.get("pe"),
            'prism':                        payload.get("prism"),
            'vm_names':                     ','.join(payload.get("vm_names")),
        }
        self.task_duplicated_kwargs = {"pe": payload.get("pe")}        # TODO: vm_names?
        self.task_identifier = payload.get("pe")
        self.room_a_cluster, self.room_b_cluster = None, None
        self.prism_client = None

    def init_svc_account(self):
        def get_central_pe_name():
            prism = self.payload.get("prism")
            central_pe_fqdn = ModelWarehousePrismCentral.query.filter_by(fqdn=prism).first().central_pe_fqdn
            central_pe_name = ModelWarehousePrismElement.query.filter_by(fqdn=central_pe_fqdn).first().name
            return central_pe_name
        vault = Vault(tier=ModelWarehousePrismCentral.query.filter_by(fqdn=self.payload.get("prism")).first().tier)
        central_pe_name = get_central_pe_name()
        pc_svc_label = f"{central_pe_name.upper()}/Site_Pc_Svc"
        res, data = vault.get_secret(pc_svc_label)
        if not res:
            raise VaultGetSecretFailed(pc_svc_label)
        pc_svc_account = {"username": data["username"], "password": data["secret"]}
        return pc_svc_account

    def init_api_client_config(self, config):
        config.host = self.payload.get("prism")
        config.port = 9440
        config.username = self.pc_svc_account['username']
        config.password = self.pc_svc_account['password']
        config.verify_ssl = True                            # Set to False in Test env
        return config

    def init_ntx_api_clients(self):
        self.prism_client = ntnx_prism_py_client.ApiClient(self.init_api_client_config(ntnx_prism_py_client.Configuration()))

    def task_process(self):
        """Reference: https://confluence.build.ingka.ikea.com/pages/viewpage.action?spaceKey=TECHNOLOGY&title=SOP+of+Wiab+Cluster+initial+configuration+before+cutover"""
        self.room_a_cluster, self.room_b_cluster = self.init_room_info()
        self.init_ntx_api_clients()
        self.create_subnets()
        category_1_id = self.create_categories()
        self.create_protection_policy()
        self.create_recovery_plan()
        self.create_volume_group(category_1_id)

    def init_room_info(self):
        room_a_cluster, room_b_cluster = None, None
        api = ntnx_clustermgmt_py_client.ClustersApi(ntnx_clustermgmt_py_client.ApiClient(self.init_api_client_config(ntnx_clustermgmt_py_client.Configuration())))
        clusters = api.list_clusters(_filter=f"contains(name,'{self.pe_prefix}')")
        for c in clusters.data:
            if "NXC000" in c.name:
                room_a_cluster = c
            elif "NXC001" in c.name:
                room_b_cluster = c
        return room_a_cluster, room_b_cluster

    def create_subnets(self):
        self.logger.title("Configure Subnets")
        required_vlan = {
            104: f"{self.pe_prefix}-104-VLAN-104-Servers",      # e.g. DSGB390-104-VLAN-104-Servers
            157: f"{self.pe_prefix}-157-Nutanix-Mgmnt",         # e.g. DSGB390-157-Nutanix-Mgmnt
            166: f"{self.pe_prefix}-166-Heartbeat",             # e.g. DSGB390-166-Heartbeat
        }
        api = ntnx_networking_py_client.SubnetsApi(ntnx_networking_py_client.ApiClient(self.init_api_client_config(ntnx_networking_py_client.Configuration())))
        self.create_subnets_for_single_room(api, self.room_a_cluster, required_vlan)
        self.create_subnets_for_single_room(api, self.room_b_cluster, required_vlan)

    def create_subnets_for_single_room(self, api, cluster, required_vlan):
        self.logger.info(f"Creating subnets for {cluster.name}...")
        cluster_ext_id = cluster.ext_id
        for network_id, subnet_name in required_vlan.items():
            self.logger.info(f"Checking subnet existence: {subnet_name}")
            existing = api.list_subnets(
                _filter=f"name eq '{subnet_name}' and clusterReference eq '{cluster_ext_id}'"
            )
            if existing.metadata.total_available_results > 1:
                raise SubnetNameDuplicatedOnNtx(subnet_name)
            if existing.metadata.total_available_results == 1:
                self.logger.warning(f"Subnet '{subnet_name}' already exists, skip this one.")
                continue
            self.logger.info(f"Creating subnet {subnet_name}...")
            res = api.create_subnet({
                "name": subnet_name,
                "clusterReference": cluster_ext_id,
                "networkId": network_id,
                "subnetType": "VLAN"
            })
            task_status = self.get_prism_task_status(res.data.ext_id)
            if task_status != "SUCCEEDED":
                self.logger.warning(f"Failed to create subnet '{subnet_name}', task status: {task_status}")
                continue
            self.logger.info(f"Subnet '{subnet_name}' created successfully.")

    def create_categories(self):
        self.logger.title("Configure Categories")
        required_category_keys = [self.pe_prefix, f"{self.pe_prefix}_Client_Failover"]
        api = ntnx_prism_py_client.CategoriesApi(self.prism_client)
        for key in required_category_keys:
            self.logger.info(f"Checking category existence: {key}")
            existing_categories = api.list_categories(_filter=f"key eq '{key}' and value eq 'Metro'")
            if existing_categories.metadata.total_available_results > 0:
                # If category already exists, NTX will return ERROR
                self.logger.warning(f"Category '{key}' already exists, skip this one.")
                continue
            api.create_category({"key": key, "value": "Metro"})
            self.logger.info("Category created successfully.")
        category_1_id = api.list_categories(_filter=f"key eq '{self.pe_prefix}' and value eq 'Metro'").data[0].ext_id
        return category_1_id

    def create_protection_policy(self):
        self.logger.title("Configure Protection Policy")
        api_client = ntnx_datapolicies_py_client.ApiClient(self.init_api_client_config(ntnx_datapolicies_py_client.Configuration()))
        api = ntnx_datapolicies_py_client.ProtectionPoliciesApi(api_client)
        rest_pr = RestProtectionRules(self.payload.get("prism"), self.pc_svc_account, self.logger)
        pc_uuid = ntnx_prism_py_client.DomainManagerApi(self.prism_client).list_domain_managers().data[0].ext_id
        required_protection_policies = [
            {
                "policy_name": f"{self.pe_prefix}_Metro_Protection_Policy",
                "category": {
                    self.pe_prefix: ["Metro"]
                },
            },
            {
                "policy_name": f"{self.pe_prefix}_VM_Metro_Protection_Policy",
                "category": {
                    f"{self.pe_prefix}_Client_Failover": ["Metro"]
                },
            }
        ]
        for policy in required_protection_policies:
            self.logger.info(f"Checking policy existence: {policy['policy_name']}")
            existing = api.list_protection_policies(_filter=f"name eq '{policy['policy_name']}'%27'")
            if existing.metadata.total_available_results > 0:
                self.logger.warning(f"Policy '{policy['policy_name']}' already exists, skip this one.")
                continue
            create_payload = {
                "spec": {
                    "name": policy["policy_name"],
                    "description": self.pe_prefix,
                    "resources": {
                        "ordered_availability_zone_list": [
                            # primary_location: DSxxxxx-NXC000, recovery_location: DSxxxxx-NXC001
                            {
                                "availability_zone_url": pc_uuid,
                                "cluster_uuid": self.room_a_cluster.ext_id
                            },
                            {
                                "availability_zone_url": pc_uuid,
                                "cluster_uuid": self.room_b_cluster.ext_id
                            },
                        ],
                        "availability_zone_connectivity_list": [
                            {
                                "source_availability_zone_index": 0,
                                "destination_availability_zone_index": 1,
                                "snapshot_schedule_list": [
                                    {
                                        "recovery_point_objective_secs": 0,
                                        "auto_suspend_timeout_secs": 10
                                    }
                                ]
                            },
                            {
                                "source_availability_zone_index": 1,
                                "destination_availability_zone_index": 0,
                                "snapshot_schedule_list": [
                                    {
                                        "recovery_point_objective_secs": 0,
                                        "auto_suspend_timeout_secs": 10
                                    }
                                ]
                            }
                        ],
                        "category_filter": {
                            "params": policy["category"]
                        }
                    }
                },
                "metadata": {
                    "kind": "protection_rule"
                }
            }
            res = rest_pr.create_protection_rules(create_payload)
            task_uuid = res["status"]["execution_context"]["task_uuid"]
            time.sleep(5)
            task_detail = rest_pr.get_task_by_uuid(task_uuid)
            if task_detail["status"] == "FAILED":
                self.logger.warning(f"Failed to create protection rules, error detail: {task_detail['error_detail']}")
                continue
            self.logger.info("Policy created successfully.")

    def create_recovery_plan(self):
        network_name = f"{self.pe_prefix}-104-VLAN-104-Servers"
        self.logger.title("Configure Recovery Plan")
        required_plan_name = [f"{self.pe_prefix}_Metro_Recovery_Plan", f"{self.pe_prefix}_VM_Recovery_Plan"]
        rest = RestRecoveryPlans(self.payload.get("prism"), self.pc_svc_account, self.logger)
        pc_uuid = ntnx_prism_py_client.DomainManagerApi(self.prism_client).list_domain_managers().data[0].ext_id
        for plan_name in required_plan_name:
            self.logger.info(f"Checking plan existence: {plan_name}")
            existing = rest.list_recovery_plans()
            if existing["metadata"]["total_matches"] > 0:
                self.logger.warning(f"Recovery Plan '{plan_name}' already exists, skip this one.")
                continue
            payload = {
                "spec": {
                    "name": plan_name,
                    "resources": {
                        "stage_list": [],
                        "parameters": {
                            "primary_location_index": 0,
                            "availability_zone_list": [
                                {
                                    "availability_zone_url": pc_uuid,
                                    "cluster_reference_list": [
                                        {
                                            "kind": "cluster",
                                            "name": self.room_a_cluster.name,
                                            "uuid": self.room_a_cluster.ext_id
                                        }
                                    ]
                                },
                                {
                                    "availability_zone_url": pc_uuid,
                                    "cluster_reference_list": [
                                        {
                                            "kind": "cluster",
                                            "name": self.room_b_cluster.name,
                                            "uuid": self.room_b_cluster.ext_id
                                        }
                                    ]
                                }
                            ],
                            "witness_configuration_list": [
                                {
                                    "witness_address": pc_uuid,
                                    "witness_failover_timeout_secs": 30
                                }
                            ],
                            "data_service_ip_mapping_list": [],
                            "network_mapping_list": [
                                {
                                    "availability_zone_network_mapping_list": [
                                        {
                                            "recovery_network": {
                                                "name": network_name
                                            },
                                            "test_network": {
                                                "name": network_name
                                            },
                                            "availability_zone_url": pc_uuid,
                                            "cluster_reference_list": [
                                                {
                                                    "kind": "cluster",
                                                    "name": self.room_a_cluster.name,
                                                    "uuid": self.room_a_cluster.ext_id
                                                }
                                            ]
                                        },
                                        {
                                            "recovery_network": {
                                                "name": network_name
                                            },
                                            "test_network": {
                                                "name": network_name
                                            },
                                            "availability_zone_url": pc_uuid,
                                            "cluster_reference_list": [
                                                {
                                                    "kind": "cluster",
                                                    "name": self.room_b_cluster.name,
                                                    "uuid": self.room_b_cluster.ext_id
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                },
                "metadata": {
                    "kind": "recovery_plan",
                }
            }
            res = rest.create_recovery_plans(payload)
            task_uuid = res["status"]["execution_context"]["task_uuid"]
            time.sleep(5)
            task_detail = rest.get_task_by_uuid(task_uuid)
            if task_detail["status"] == "FAILED":
                self.logger.warning(f"Failed to create recovery plan, error detail: {task_detail['error_detail']}")
                continue
            self.logger.info("Plan created successfully.")

    def create_volume_group(self, category_ext_id):
        self.logger.title("Configure volume group")
        vm_names = self.payload.get("vm_names")     # [DSSE998-NT6000, DSSE998-NT6001]
        api_client = ntnx_volumes_py_client.ApiClient(self.init_api_client_config(ntnx_volumes_py_client.Configuration()))
        api = ntnx_volumes_py_client.VolumeGroupsApi(api_client)
        vm_suffixes = [_.split('-')[-1] for _ in vm_names]
        vg_name = f"{self.pe_prefix}_{vm_suffixes[0]}_{vm_suffixes[1]}_VG"  # e.g. DSSE998_NT6000_NT6001_VG
        existing_vgs = api.list_volume_groups(_filter=f"name eq '{vg_name}'")
        if existing_vgs.metadata.total_available_results > 0:
            self.logger.warning(f"Volume group '{vg_name}' already exists, skip this one.")
        else:
            payload = {
                "name": vg_name,
                "clusterReference": self.room_a_cluster.ext_id,
                "sharingStatus": "SHARED"
            }
            api.create_volume_group(payload)
            time.sleep(5) # sleep for 5 secs
            existing_vgs = api.list_volume_groups(_filter=f"name eq '{vg_name}'")
            if existing_vgs.metadata.total_available_results == 0 or not existing_vgs.data:
                raise CreateVolumeGroupFailed(vg_name)
            self.logger.info(f"Volume group '{vg_name}' created successfully.")
        self.logger.info(f"Associate category '{category_ext_id}' to volume group '{vg_name}'...")
        api.associate_category(
            existing_vgs.data[0].ext_id, {
                "categories": [
                    {
                        "extId": category_ext_id,
                        "entityType": "CATEGORY"
                    }
                ]
            }
        )
        self.logger.info("Category associated successfully.")

    def get_prism_task_status(self, ext_id):
        api = ntnx_prism_py_client.TasksApi(self.prism_client)
        task = api.get_task_by_id(ext_id)
        return task.data.status

<template>
  <div>
  <el-card class="box-card" >
    <div slot="header">
        <div style="font-size:25px">
            Nutanix
        </div>
    </div>
  <el-row :gutter="40" class="panel-group">
    <el-col :lg="4" class="card-panel-col">
      <div class="card-panel" @click="jumpto('/nutanix/prism')">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="prism" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description" >
          <div class="card-panel-text">
            PC
          </div>
          <count-to :start-val="0" :end-val="ntx_pc_number" :duration="2600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :lg="4" class="card-panel-col">
      <div class="card-panel" @click="jumpto('/nutanix/pe')">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="ntx_cluster" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            PE
          </div>
          <count-to :start-val="0" :end-val="ntx_pe_number" :duration="2600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :lg="4" class="card-panel-col">
      <div class="card-panel" @click="jumpto('/nutanix/ahv')">
        <div class="card-panel-icon-wrapper icon-message">
          <svg-icon icon-class="node" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            Host
          </div>
          <count-to :start-val="0" :end-val="ntx_host_number" :duration="3000" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :lg="4" class="card-panel-col">
      <div class="card-panel" @click="jumpto('/nutanix/vm')">
        <div class="card-panel-icon-wrapper icon-money">
          <svg-icon icon-class="ntx_vm" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            VM
          </div>
          <count-to :start-val="0" :end-val="ntx_vm_number" :duration="3200" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :lg="4" class="card-panel-col">
      <div class="card-panel">
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="CPU" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            CPU
          </div>
          <count-to :start-val="0" :end-val="ntx_cpu_number" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :lg="4" class="card-panel-col">
      <div class="card-panel" >
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="ntx_memory" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            Memory(TB)
          </div>
          <count-to :start-val="0" :end-val="ntx_ram_number" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
  </el-row>
</el-card>

<el-card  class="box-card">
  <div slot="header">
    <div style="font-size:25px">
      SimpliVity
    </div>
  </div>
  <el-row :gutter="40" class="panel-group">
    <el-col  :lg="4" class="card-panel-col">
      <div class="card-panel" @click="jumpto('/simplivity/vc')">
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="vcenter" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            vCenter
          </div>
          <count-to :start-val="0" :end-val="sli_vc_number" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col  :lg="4" class="card-panel-col">
      <div class="card-panel" @click="jumpto('/simplivity/cluster')">
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="vccluster" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            Cluster
          </div>
          <count-to :start-val="0" :end-val="sli_cluster_number" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col  :lg="4" class="card-panel-col">
      <div class="card-panel" @click="jumpto('/simplivity/slihosts')" >
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="vc_node" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            Host
          </div>
          <count-to :start-val="0" :end-val="sli_host_number" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col  :lg="4" class="card-panel-col">
      <div class="card-panel" @click="jumpto('/simplivity/slivm')">
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="vc_vm" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            VM
          </div>
          <count-to :start-val="0" :end-val="sli_vm_number" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col  :lg="4" class="card-panel-col">
      <div class="card-panel" >
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="vc_cpu" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            CPU
          </div>
          <count-to :start-val="0" :end-val="sli_cpu_number" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col  :lg="4" class="card-panel-col">
      <div class="card-panel" >
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="vc_memory" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            Memory(TB)
          </div>
          <count-to :start-val="0" :end-val="sli_ram_number" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
  </el-row>
</el-card>
</div>
</template>

<script>
import CountTo from 'vue-count-to'
import { GetDashboardInfo} from '@/api/nutanix'
export default {
  components: {
    CountTo
  },
  methods: {
    jumpto(_path) {
      this.$router.push({
      path:_path,
      query: {
        //Ensure that each click, query is not the same
        //to ensure that refresh the view
        // t: +new Date()
      }
      })
    },
    get_dashboard_info(){
      GetDashboardInfo(this.$store.getters.token).then(response => {
        if (response.status == 200){
          this.ntx_pc_number = response.data.ntx.pc
          this.ntx_pe_number = response.data.ntx.pe
          this.ntx_host_number = response.data.ntx.host
          this.ntx_vm_number = response.data.ntx.vm
          this.ntx_cpu_number = response.data.ntx.cpu
          this.ntx_ram_number = response.data.ntx.memory
          this.sli_vc_number = response.data.sli.vCenter
          this.sli_cluster_number = response.data.sli.cluster
          this.sli_host_number = response.data.sli.host
          this.sli_vm_number = response.data.sli.vm
          this.sli_cpu_number = response.data.sli.cpu
          this.sli_ram_number = response.data.sli.memory
        }
      })
    }
  },
  created() {
    this.get_dashboard_info()
  },
  data() {
    return {
      ntx_pc_number      : 0,
      ntx_pe_number      : 0,
      ntx_host_number    : 0,
      ntx_vm_number      : 0,
      ntx_cpu_number     : 0,
      ntx_ram_number     : 0,
      sli_vc_number      : 0,
      sli_cluster_number : 0,
      sli_host_number    : 0,
      sli_vm_number      : 0,
      sli_cpu_number     : 0,
      sli_ram_number     : 0,
    }
  },
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 8px;

  .card-panel-col {
    margin-bottom: 4px;
  }

  .card-panel {
    
    border-radius: 15px;
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 5px 0px 10px 5px rgba(0, 0, 0, .1);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;
      width:110px;
      position:relative;
      .card-panel-text {
        line-height: 25px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 22px;
        margin-bottom: 12px;
        text-align: center;
      }

      .card-panel-num {
        // border:1px blue solid;
        // font-size: 20px;
        // text-align: center;
        line-height: 25px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 22px;
        text-align: center;
        position:absolute;
        margin:  auto;
        left:0;
        right:0;
      }
    }
  }
}
.box-card {
  width: 100%;
  max-width: 100%;
  margin: 10px auto;
  border-radius: 6px;
}
// @media (max-width:550px) {
//   .card-panel-description {
//     display: none;
//   }

//   .card-panel-icon-wrapper {
//     float: none !important;
//     width: 100%;
//     height: 100%;
//     margin: 0 !important;

//     .svg-icon {
//       display: block;
//       margin: 14px auto !important;
//       float: none !important;
//     }
//   }
// }
</style>

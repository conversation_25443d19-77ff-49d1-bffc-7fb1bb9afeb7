# Author: TiAke2
# Date: 2024-11
# Description: This script defines a HostCollector class that collects and processes host data from various sources and compares it with existing data in a database.
# It uses asynchronous processing with threads to handle multiple tasks concurrently and includes methods for data merging, updating, and inserting into the database.
# is onboard for both wh and retail.
import asyncio
import pandas as pd
from collector.collectors.modules.db_operations import DatabaseOperations
from collector.collectors.modules.api_calls import APICallsHostCollector
from collector.collectors.modules.multi_job import AsynchronousProcessing
from collector.collectors.base_collector import BaseCollector
import numpy as np
import time
from datetime import datetime, timezone
from models.ntx_models import ModelRetailNutanixHost
from models.ntx_models_wh import ModelWarehouseNutanixHost
from models.database import db


class HostCollector(BaseCollector):
    oneview_data_cache = None  # Class variable to cache OneView data

    def __init__(self, sa=None, facility_type = "retail") -> None:
        super().__init__(sa, facility_type)
        # self.facility_type from basecollector. used to name log file
        # self.logger from basecollector. Used to log to specific file
        self.database_operations = DatabaseOperations(logger=self.logger)

    @classmethod
    def reset_oneview_data_cache(cls):
        cls.oneview_data_cache = None

    async def _async_method_pc(self, pc_fqdn_list, fqdn_oneview):
        processes = AsynchronousProcessing(thread = True)
        api_call = APICallsHostCollector(logger = self.logger)

        # host
        for pc in pc_fqdn_list:
            processes.add_task("host", api_call.get_host_data, pc)

        # Add tasks for OneView data collection if not already cached
        if not HostCollector.oneview_data_cache:
            self.logger.info("oneview data not found in cache. Will collect and reuse later if needed!")
            for pc in fqdn_oneview:
                processes.add_task("oneview", api_call.get_oneview_data, pc) # If warehouse dont share same oneview instance as retail, this will need to be added but it does atm
                
        await processes.execute_asynchronous_tasks()

        api_results = processes.get_results()
        
        try:
            if not api_results["host"]:
                return False, False

        except Exception:
            return False, False

        host_data = [item for host in api_results["host"] if host for item in host]  
        # Cache OneView data if not already cached
        if not HostCollector.oneview_data_cache:
            try :  
                oneview_data = [item for oneview in api_results["oneview"] if oneview for item in oneview]
            except Exception:
                oneview_data = []
            HostCollector.oneview_data_cache = oneview_data
        else:
            self.logger.info("oneview data found in cache. Will reuse for other facility type and save a lot of time whoop whoop!")
            oneview_data = HostCollector.oneview_data_cache
        return host_data, oneview_data


    async def _async_method_nic(self, host_data, warehouse = False):
        processes = AsynchronousProcessing(thread=True)
        api_call_nic = APICallsHostCollector(logger=self.logger)
        
        headers = HostCollector.headers_index(warehouse = warehouse)
        for host in host_data:
            processes.add_task("nic", api_call_nic.get_nic_data, host[headers["pe_fqdn"]], host[headers["pe_name"]], host[headers["uuid"]])

        await processes.execute_asynchronous_tasks()
        api_results = processes.get_results()
        nic_data = {uuid: values for host in api_results["nic"] for uuid, values in host.items()}
        return nic_data


    def collect(self, warehouse = False): # set to True if wh data is to be collected
        self.logger.title("HostCollector{}".format("Retail" if not warehouse else "Warehouse"))
        self.logger.info("Starting HostCollector for {}".format("Retail" if not warehouse else "Warehouse"))
        start_time = time.time()
        
        if warehouse:
            pc_fqdn_list = self.database_operations.get_dh_warehouse_ntx_pc()
        else:
            pc_fqdn_list = self.database_operations.get_dh_retail_ntx_pc()
        if not pc_fqdn_list:
            self.logger.error("No data from db is collected for Host collector! PC data is empty.")

        fqdn_oneview = self.database_operations.get_dh_server_oneview()# what about warehouse here

        # Collect from pc for host and oneview
        host_data, oneview_data = asyncio.run(self._async_method_pc(pc_fqdn_list, fqdn_oneview))
        if not host_data:
            self.logger.error("No data from pc is collected for Host collector! Host data is empty.")
            return False
        # create a oneview dict which gets linked to a host via SN
        oneview_hardware_data_dict = self._create_oneview_hardware_dict(oneview_data)

        if warehouse:
            pe_database_info = self.database_operations.get_dh_warehouse_ntx_pe()
        else:
            pe_database_info = self.database_operations.get_dh_retail_ntx_pe()
        cluster_information_dict_from_db = {pe["uuid"]: [pe["name"], pe["fqdn"], pe["id"]] for pe in pe_database_info}
        #match cluster data and oneview with host data.

        for host in host_data:
           # sn = host[headers["sn"]]
           #     # server_hardware = oneview_hardware_data_dict[sn] # Will be added if new url for oneview is better. also found individual url per server that can account for the missing data

            self._match_and_insert_data(host, cluster_information_dict_from_db, oneview_hardware_data_dict, warehouse = warehouse)

        #collect and add nic data to host data
        nic_results = asyncio.run(self._async_method_nic(host_data, warehouse = warehouse))
        self._add_nic_data(host_data, nic_results, warehouse = warehouse)

        # Need to skip host from pc that fails??
        # If so take them out from the db_list so they will be skipped in "compare and update db"
        # Not implemented!!

        #merge old host data with new.
        headers_db, stored_host_data_from_db = self.database_operations.get_dh_ntx_host(warehouse = warehouse)

        if not headers_db: # wont have an id etc
            headers_db = list(HostCollector.headers_index(warehouse = warehouse).keys())
        else: # will have id
            headers = list(HostCollector.headers_index(warehouse = warehouse).keys())
            headers.insert(0, "id")
            headers_db = [val for val in headers if val in headers_db] # guarantee order of headers from db
        headers_collect = HostCollector.headers_index(warehouse = warehouse)
        merged_host_list_df = self._merge_host_data(stored_host_data_from_db, headers_db, host_data, headers_collect)

        #compare values & update db
        self._compare_data_and_update_db(merged_host_list_df, headers_db, warehouse = warehouse)
        
        end_time = time.time()
        total_time = end_time - start_time
        self.logger.info(f"HostCollector for {'warehouse' if warehouse else 'retail'} finished in {total_time:.2f} seconds")
        return True

    def _create_oneview_hardware_dict(self, oneview_data):
        oneview_clean_data = {}
        for server in oneview_data:
            serial_number = server.get("serialNumber", None)
            if serial_number is None:
                continue
            try:
                ipmi_version = server["mpModel"] + " " + server["mpFirmwareVersion"]
            except:
                ipmi_version = None
            try:
                bios_version = server.get("romVersion", None)
            except:
                bios_version = None
            try:
                version = next((item.get("FirmwareVersion", {}).get("Current", {}).get("VersionString") for item in server.get("subResources", {}).get("LocalStorage", {}).get("data", []) if item.get("AdapterType") == "SmartArray"), None)
            except:
                version = None
            try:
                model = next((item.get("Model") for item in server.get("subResources", {}).get("LocalStorage", {}).get("data", []) if item.get("AdapterType") == "SmartArray"), None)
            except:
                model = None
            data_list = [ipmi_version, bios_version, version, model]
            oneview_clean_data[serial_number] = data_list
        return oneview_clean_data

    #def _create_oneview_hardware_dict_new_not_used(self, oneview_data): # if url for oneveiw data changes use this one
        '''oneview_clean_data_new = {} 
        for server in oneview_data:
            
            serial_number = server["attributes"].get("serialNumber", None)
            if not serial_number:
                continue
            
            try:
                ipmi_version = server["attributes"]["mpModel"] + " " + server["attributes"]["mpFirmwareVersion"]
                if ipmi_version == "Unknown":
                    ipmi_version = None
            except:
                ipmi_version = None
            try:
                bios_version = server.get("attributes", {}).get("romVersion", None)
                if bios_version == "Unknown":
                    bios_version = None
            except:
                bios_version = None
            
            smart_array_index = 0
            try:
                look_alike_model = None
                dict = server["multiAttributes"].keys()
                for key in dict:
                    if "Smart Array" in key:
                        look_alike_model = key
                        break
                component_names = server["multiAttributes"]["componentNames"]
                model = None
                for i in component_names:
                    if "Smart Array" in i:
                        if look_alike_model and i in look_alike_model:
                            model = i
                            break
                        else:
                            model = i
                    smart_array_index += 1
                if model == "Unknown":
                    model = None
            except:
                model = None
            try:
                if model == "Unknown" or model == None:
                    version = None
                else:    
                    version = server["multiAttributes"]["componentVersions"]
                    version = version[smart_array_index]
                    if version == "Unknown":
                        version = None
            except:
                version = None
            data_list_new = [ipmi_version, bios_version, version, model]
            oneview_clean_data_new[serial_number] = data_list_new
            if None in data_list_new:
                
                try:
                    uri = True, server["uri"]
                    
                    oneview_clean_data_new[serial_number] = uri
                except:
                    continue'''

    def _match_and_insert_data(self, host, cluster_information_dict_from_db, oneview_clean_data, warehouse = False):
        header_indexes = HostCollector.headers_index(warehouse = warehouse) # So far only used for inserting values
        try:
            pe_uuid = host[2]
            cluster_info = cluster_information_dict_from_db.get(pe_uuid, None)

            if cluster_info:
                pe_name = cluster_info[0]
                pe_fqdn = cluster_info[1]
                pe_id = cluster_info[2]
            else:
                pe_name = "NA"
                pe_fqdn = "NA"
                pe_id = None
            host.insert(header_indexes["pe_name"], pe_name)
            host.insert(header_indexes["pe_fqdn"], pe_fqdn)
        except Exception:
            host.insert(header_indexes["pe_name"], "NA")
            host.insert(header_indexes["pe_fqdn"], "NA")
            pe_id = None

        try:
            host_oneview_data = oneview_clean_data[host[header_indexes["sn"]]]
        except KeyError:
            host_oneview_data = ["NA", "NA", "NA", "NA"]

        host.insert(header_indexes["ipmi_version"], host_oneview_data[0])
        host.insert(header_indexes["status"], "Running")
        host.insert(header_indexes["pe_id"], pe_id)
        host.insert(header_indexes["bios_version"], host_oneview_data[1])
        host.insert(header_indexes["controller_version"], host_oneview_data[2])
        host.insert(header_indexes["controller_model"], host_oneview_data[3])

    def _compare_data_and_update_db(self, merged_list_df, headers_db, warehouse = False):
        # Collect new entries and updates
        new_entries = []
        updates = []
        activate = []
        unreachable_hosts = []
        untouched_hosts = []
        rows_to_remove_indexes = []
        new_entries_count = 0
        empty = False

        #collect new entries      
        for index, row in merged_list_df.iloc[::-1].iterrows():
            try:
                if pd.isna(row["id"]): # if fail, empty db
                    new_host_dict = self._convert_host_values_list_to_dict(row, headers_db, entry = True, warehouse=warehouse)
                    new_entries.append(new_host_dict)
                    rows_to_remove_indexes.append(index)
                    new_entries_count += 1
                    self.logger.info(f'New host found! Host name: {row["name_new"]}.')
                else:
                    break # exit loops since the rest will already be in db
            except: # takes over if db is empty
                empty  = True
                new_host_dict = self._convert_host_values_list_to_dict(row, headers_db, entry = True, warehouse=warehouse, empty_db = True)
                new_entries.append(new_host_dict)
                rows_to_remove_indexes.append(index)
                new_entries_count += 1
        if empty:
            self.logger.info("Empty host db found! All hosts will be added as new entries.")        
        merged_list_df.drop(rows_to_remove_indexes, inplace=True) # take out new entries from org list

        #collect updates for existing hosts in db
        if not merged_list_df.empty:
            for _, row in merged_list_df.iterrows():
                pd.set_option('display.max_rows', len(row))

                old_keys = [key for key in row.index if key.endswith('old')] # for getting old values
                new_keys = [key for key in row.index if key.endswith('new')] # for getting new values
                old_values_series = row.loc[old_keys]
                new_values_series = row.loc[new_keys]
                old_values_series = old_values_series.rename(lambda x: x.replace('_old', ''))# restore col name
                new_values_series.index = old_values_series.index # match col name

                # Ensure consistent data types for comparison
                old_values_series = old_values_series.astype(str)
                new_values_series = new_values_series.astype(str)

                def _format_differences(differences):
                    formatted_diff = []
                    for index, row in differences.iterrows():
                        formatted_diff.append(f"Column '{index}': old value = {row['self']}, new value = {row['other']}")
                    return "\n".join(formatted_diff)

                # If host status is unreachable and status is changed to running. Update values
                if row["status_new"] == "Running" and row["status_old"] != "Running":
                    changed_host_dict = self._convert_host_values_list_to_dict(row, headers_db, warehouse=warehouse)

                    changed_host_dict["id"] = row["id"]
                    changed_host_dict["sn"] = row["sn"]
                    activate.append(changed_host_dict)
                    differences = old_values_series.compare(new_values_series)
                    differences = _format_differences(differences)
                    self.logger.info(f'Differences in reactivated host: {row["name_new"]} id: {row["id"]}. The difference is: [{differences}]')
                    continue

                # if host is not collected. deem that host Unreachable if not already done
                elif row["status_new"] != "Running" and row["status_old"] != "Unreachable":
                    unreachable_dict = {"sn": row["sn"], "status": "Unreachable", "id": row["id"]}
                    unreachable_hosts.append(unreachable_dict)
                    self.logger.info(f'Host that will be set to unreachable found! Host name: {row["name_old"]} id: {row["id"]}.')
                    continue


                # if host is collected and both status = Running and values in db dont match. change them.
                elif not old_values_series.equals(new_values_series) and row["status_new"] == "Running" and row["status_old"] == "Running":
                    changed_host_dict = self._convert_host_values_list_to_dict(row, headers_db, warehouse=warehouse)
                    changed_host_dict["id"] = row["id"]
                    changed_host_dict["sn"] = row["sn"]
                    
                    
                    updates.append(changed_host_dict)
                    differences = old_values_series.compare(new_values_series)
                    differences = _format_differences(differences)
                    self.logger.info(f'Differences in host: {row["name_new"]} id: {row["id"]}. The difference is: [{differences}]')
                    continue
                else:
                    lu_dict = {"id": row["id"], "sn": row["sn"]} # last update                   
                    untouched_hosts.append(lu_dict)
                    continue

        # Perform batch insert and update
        time_st = time.time()
        new_entries_dict = {item["sn"]: item for item in new_entries}
        updates_dict = {item["sn"]: item for item in updates}
        activate_dict = {item["sn"]: item for item in activate}
        unreachable_hosts_dict = {item["sn"]: item for item in unreachable_hosts}
        untouched_hosts_dict = {item["sn"]: item for item in untouched_hosts}
        if new_entries:
            self.database_operations.update_db(new_entries_dict, ModelRetailNutanixHost if not warehouse else ModelWarehouseNutanixHost, ModelRetailNutanixHost.sn if not warehouse else ModelWarehouseNutanixHost.sn, db.session)
            self.logger.info(f"{new_entries_count} new hosts added to the database.")
        if updates:
            self.database_operations.update_db(updates_dict, ModelRetailNutanixHost if not warehouse else ModelWarehouseNutanixHost, ModelRetailNutanixHost.sn if not warehouse else ModelWarehouseNutanixHost.sn, db.session)
            self.logger.info(f"{len(updates)} hosts updated in the database.")
        if activate:
            self.database_operations.update_db(activate_dict, ModelRetailNutanixHost if not warehouse else ModelWarehouseNutanixHost, ModelRetailNutanixHost.sn if not warehouse else ModelWarehouseNutanixHost.sn, db.session)
            self.logger.info(f"{len(activate)} hosts activated in the database.")
        if unreachable_hosts:
            self.database_operations.update_db(unreachable_hosts_dict, ModelRetailNutanixHost if not warehouse else ModelWarehouseNutanixHost, ModelRetailNutanixHost.sn if not warehouse else ModelWarehouseNutanixHost.sn, db.session)
            self.logger.info(f"{len(unreachable_hosts)} hosts set to unreachable in the database.")
        if untouched_hosts:
            self.database_operations.update_db_last_update_column_only(untouched_hosts_dict, ModelRetailNutanixHost if not warehouse else ModelWarehouseNutanixHost, ModelRetailNutanixHost.sn if not warehouse else ModelWarehouseNutanixHost.sn, db.session) # update last_update column for hosts that are untouched
            self.logger.info(f"Last update column updated for {len(untouched_hosts)}.")
        time_end = time.time()
        if warehouse:
            self.logger.info(f"Time to update dh_wh_ntx_host: {time_end - time_st}")
        else:
            self.logger.info(f"Time to update dh_retail_ntx_host: {time_end - time_st}")


    def _convert_host_values_list_to_dict(self, values_pd_list, headers_db, entry = False, empty_db = False, warehouse = False):
        values_list = values_pd_list.tolist()
        if warehouse:
            new_values_depending_on_warehouse_or_retail = 51
        else:
            new_values_depending_on_warehouse_or_retail = 35
        start_index = new_values_depending_on_warehouse_or_retail
        if entry and empty_db:
            values_list = values_list[start_index:] 
            entry_dict = {}
            for head, value in zip(headers_db, values_list):
                entry_dict[head] = value


        elif entry and not empty_db:
            headers = headers_db[1:] # dont need id for new hosts
            entry_dict = {}

            entry_dict[headers[0]] = values_list[start_index + 2] # name_new
            entry_dict[headers[1]] = values_list[2] # sn

            for head, value in zip(headers[2:], values_list[start_index + 3:]): 
                entry_dict[head] = value


        else: # Update existing host 
            entry_dict = {}
            if values_list[1] != values_list[start_index + 2]: # hostname can change like every value but sn
                entry_dict[headers_db[1]] = values_list[start_index + 2] # name_new
                self.logger.info(f"Host {values_list[1]} has changed name. Old name: {values_list[1]}, New name: {values_list[start_index + 2]}") # + 1 new values, + 1 id
            else:
                entry_dict[headers_db[1]] = values_list[1] 
            headers = headers_db[3:] # dont need id/sn for existing hosts, and name is set already above

            for head, value in zip(headers, values_list[start_index + 3:]): # skip id name and sn, starts from pe_name
                entry_dict[head] = value

        last_update = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        entry_dict["last_update"] = last_update #  only new entries and host that had change of values 
        return entry_dict        


    def _merge_host_data(self, old_data, headers_db, new_data, headers_collect):
        headers_column = headers_collect.keys()
        old_data_df = pd.DataFrame(old_data, columns=headers_db, dtype=object)
        new_data_df = pd.DataFrame(new_data, columns=headers_column, dtype=object)

        merged_data_df = old_data_df.merge(new_data_df, on = "sn", how = "outer", suffixes = ("_old", "_new"))
        merged_data_df = merged_data_df.replace({np.nan: None})

        return merged_data_df

    def _add_nic_data(self, host_data_list, nic_data_dict, warehouse=False):
        headers = HostCollector.headers_index(warehouse=warehouse)
        if warehouse:
            for host in host_data_list:
                nic_host_data = nic_data_dict.get(host[headers["uuid"]], None)
                start = headers["nic0_uuid"]
                if nic_host_data:
                    nic_values = list(nic_host_data.values())
                    nic_count = len(nic_values)
                    if nic_count <= 32: # fever than 4 NIC cards
                        host[start:start + nic_count] = nic_values
                        host[start + nic_count:start + 32] = [None] * (32 - nic_count)
                    else:   # equal or more than 4 NICs. In cases where more than 4 NICs are collected, the last 4 are the ones we want. I hope :)
                        host[start:start + 32] = nic_values[-32:]
                else:
                    host[start:start + 32] = ["NA"] * 32
        else:
            for host in host_data_list:

                nic_host_data = nic_data_dict.get(host[headers["uuid"]], None)
                start = headers["nic0_uuid"]
                if nic_host_data:
                    nic_values = list(nic_host_data.values())
                    nic_count = len(nic_values)
                    if nic_count <= 16: # fever than 2 NIC cards
                        host[start:start + nic_count] = nic_values
                        host[start + nic_count:start + 16] = [None] * (16 - nic_count)
                    else: # equal or more than 2 NICs. In cases where more than 2 NICs are collected, the last 2 are the ones we want. I hope :)
                        host[start:start + 16] = nic_values[-16:]
                else:
                    host[start:start + 16] = ["NA"] * 16





    @staticmethod
    def headers_index(warehouse = False): # dont change the order of these 
        if warehouse:
            headers = {"name": 0, "sn": 1, "pe_name": 2, "pe_fqdn": 3, "pe_uuid": 4, "disk_number": 5, "uuid": 6, "model": 7, "memory": 8, "cpu_core_number": 9, "cpu_model": 10, "ahv_ip": 11, "cvm_ip": 12, "ipmi_ip": 13, "ipmi_version": 14, "status": 15, "pe_id": 16, "bios_version": 17, "controller_version": 18, "controller_model": 19, 
    "nic0_uuid": 20, "nic0_mac": 21, "nic0_speed": 22, "nic0_mtu": 23, "nic0_sw_device": 24, "nic0_sw_port": 25, "nic0_sw_vendor": 26, "nic0_sw_vlan": 27, 
    "nic1_uuid": 28, "nic1_mac": 29, "nic1_speed": 30, "nic1_mtu": 31, "nic1_sw_device": 32, "nic1_sw_port": 33, "nic1_sw_vendor": 34, "nic1_sw_vlan": 35,
    "nic2_uuid": 36, "nic2_mac": 37, "nic2_speed": 38, "nic2_mtu": 39, "nic2_sw_device": 40, "nic2_sw_port": 41, "nic2_sw_vendor": 42, "nic2_sw_vlan": 43,
    "nic3_uuid": 44, "nic3_mac": 45, "nic3_speed": 46, "nic3_mtu": 47, "nic3_sw_device": 48, "nic3_sw_port": 49, "nic3_sw_vendor": 50, "nic3_sw_vlan": 51
    }
        else:
            headers = {"name": 0, "sn": 1, "pe_name": 2, "pe_fqdn": 3, "pe_uuid": 4, "disk_number": 5, "uuid": 6, "model": 7, "memory": 8, "cpu_core_number": 9, "cpu_model": 10, "ahv_ip": 11, "cvm_ip": 12, "ipmi_ip": 13, "ipmi_version": 14, "status": 15, "pe_id": 16, "bios_version": 17, "controller_version": 18, "controller_model": 19,
    "nic0_uuid": 20, "nic0_mac": 21, "nic0_speed": 22, "nic0_mtu": 23, "nic0_sw_device": 24, "nic0_sw_port": 25, "nic0_sw_vendor": 26, "nic0_sw_vlan": 27,
    "nic1_uuid": 28, "nic1_mac": 29, "nic1_speed": 30, "nic1_mtu": 31, "nic1_sw_device": 32, "nic1_sw_port": 33, "nic1_sw_vendor": 34, "nic1_sw_vlan": 35
    }
        return headers
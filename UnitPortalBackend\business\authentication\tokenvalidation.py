import logging
from flask import abort, request

from business.authentication.authentication import User, UserRole
from models.auth_models import ModelUser
from models.pm_models import ModelNTXPMTask, ModelSLIPMTask
import static.SETTINGS as SETTING


def pmtokencheck(func):
    # decorator for token check
    def _pmtokencheck(self):
        # get headers
        if headers := request.headers.getlist('Authorization'):
            headers = headers[0].lower()
            token = headers.replace("bearer", "").strip()
            logging.info(f'Checking if token {token} is valid.')
            user = ModelUser.query.filter_by(token=token).first()
            if user:
                if user.role in SETTING.ROLE_FOR_PM:
                    logging.info('Token exists and is valid.')
                    return func(self)
                logging.error('Token exists but is not valid(Not enough access).')
                abort(400, "User doesn't have enough access to this!")
            else:
                logging.error('Token doesn''t exist.')
                abort(400, "Token doesn't exist.")
        else:
            logging.error('Token not found in the request.')
            abort(401, 'I need a token to process!')
    return _pmtokencheck


def admintokencheck(func):

    # decorator for token check
    def _admintokencheck(self):
        # get headers
        if headers := request.headers.getlist('Authorization'):
            headers = headers[0].lower()
            token = headers.replace("bearer", "").strip()
            logging.info(f'Checking if token {token} is valid.')
            user = ModelUser.query.filter_by(token=token).first()
            if user:
                if user.role in SETTING.ROLE_FOR_PM:
                    logging.info('Token exists and is valid.')
                    return func(self)
                logging.error('Token exists but is not valid(Not enough access).')
                abort(400, "User doesn't have enough access to this!")
            else:
                logging.error('Token doesn''t exist.')
                abort(400, "Token doesn't exist.")
        else:
            logging.error('Token not found in the request.')
            abort(401, 'I need a token to process!')
    return _admintokencheck


class PrivilegeValidation:
    def __init__(self, privilege, pm_type="ntx") -> None:
        """
        e.g.
        @PrivilegeValidation(privilege={"role_ntx": "view_vm"})
        """
        self.required_permission = privilege
        self.pm_model = ModelNTXPMTask if pm_type == "ntx" else ModelSLIPMTask
        self.user = None

    @staticmethod
    def get_user_from_token():
        """
        validate token from headers and get user from database
        """
        auth_headers = request.headers.getlist('Authorization')
        if not auth_headers:
            logging.error('Authorization not found in the request.')
            abort(401, 'Missing authorization in the request!')
        auth_header = auth_headers[0].lower()
        token = auth_header.replace("bearer", "").strip()
        logging.info('Checking if token is valid.')
        if not token:
            logging.error('Token not found in the request.')
            abort(401, 'Missing token in the request header!')
        res, tmp = User().get_role_by_token(token)
        if not res:
            error_message = tmp
            abort(401, error_message)
        user = tmp
        return user

    @staticmethod
    def validate_authorization():
        if 'swagger' in request.url or 'flask-apispec' in request.url or request.method == 'OPTIONS':
            return
        if not request.authorization:
            abort(401)
        if request.endpoint == 'distributedhosting.restfullogin__api_v1_login':
            # Login don't use token
            return
        logging.info("Start to validate token...")
        PrivilegeValidation.get_user_from_token()
        logging.info("Token validation completed.")

    def __call__(self, func):
        from functools import wraps

        @wraps(func)
        def tokencheck(*args, **kwargs):
            self.user = self.get_user_from_token()
            logging.info(f"Checking user {self.user.username} permission of {self.required_permission}...")
            user_privileges = UserRole(self.user.id).get_all_privileges()
            required_role = list(self.required_permission.keys())[0]            # e.g. role_mkt
            required_sub_role = self.required_permission[required_role]         # e.g. create_wl
            if not user_privileges.get(required_role):
                abort(403, f"User doesn't have access to role '{required_role}' -> '{required_sub_role}'!")
            if user_privileges[required_role][required_sub_role] == "full":
                logging.info("Access allowed.")
            elif user_privileges[required_role][required_sub_role] == "part":
                logging.info("User has part of the permission. Verifying...")
                scope = user_privileges[required_role][f"{required_sub_role}_scope"]
                self.validate_scope_by_role(required_role, required_sub_role, scope)
            else:
                logging.error('Token exists but is not valid(Not enough access).')
                abort(403, f"User doesn't have access to role '{required_role}' -> '{required_sub_role}'!")
            return func(*args, **kwargs)

        return tokencheck

    def validate_scope_by_role(self, role, operation, merged_scope):
        if role == "role_pm":
            self.validate_scope_pm(operation, merged_scope)
        elif role == "role_mkt":
            self.validate_scope_marketplace(operation)

    def validate_scope_pm(self, operation, merged_scope):
        body = request.get_json(force=True)
        pc = body.get("prism")
        pe = body.get("cluster")
        if pc.upper() not in merged_scope or pe.upper() not in merged_scope.get(pc):
            abort(403, f"User is not authorized to do '{operation}' on prism '{pc}', cluster '{pe}'")
        logging.info(f"Congratulations! User has access to do '{operation}' on prism '{pc}', cluster '{pe}'")

    def validate_remove_wl_scope(self):
        pass

    def validate_scope_marketplace(self, operation):
        if operation == "create_wl":
            logging.info("User has part of the permission, will continue to verify permission while schema check...")
            # self.validate_create_wl_scope(operation, scope)
        elif operation == "remove_wl":
            self.validate_remove_wl_scope()

    # def validate_scope(self, role, operation, user_privileges):
    #     body = request.get_json(force=True)
    #     cluster = body.get("cluster")
    #     scope_key = f"{operation}_scope"
    #     scope = user_privileges.get(role).get(scope_key)
    #     if not self.is_cluster_valid(cluster, scope):
    #         abort(401, f"The requested resource is not in the scope: {scope}.")

    # def is_cluster_valid(self, cluster, scope):
    #     # Check if user requested cluster meets below requirements:
    #     # 1. cluster name in "available_clusters"
    #     # 2. cluster name matches "available_clusters_pattern"
    #     # 3. if scope restricts that user only have access to self created tasks:
    #     #       check if the task is created by user self
    #     if scope and scope.get("available_clusters") and cluster in scope.get("available_clusters"):
    #         return True
    #     elif scope and scope.get("available_clusters_pattern"):
    #         available_clusters_pattern = scope.get("available_clusters_pattern")
    #         for pattern in available_clusters_pattern:
    #             if re.search(pattern, cluster):
    #                 return True
    #     elif scope and scope.get("only_self_created_clusters") and cluster in scope.get("only_self_created_clusters"):
    #         return self.is_task_self_created()
    #     else:
    #         abort(401, f"User doesn't have access to do requested operation on cluster '{cluster}'.")

    # def is_task_self_created(self):
    #     body = request.get_json(force=True)
    #     task_id = body.get("id")
    #     task = self.pm_model().query.filter_by(id=task_id).one()
    #     # TODO: user creater_id instead of creater
    #     return task.creater == self.user.username

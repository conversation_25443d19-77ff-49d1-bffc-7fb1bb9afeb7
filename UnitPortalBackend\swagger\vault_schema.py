from marshmallow import Schema, fields, validate


class GetVaultDataSchema(Schema):
    labels = fields.List(fields.Str(), required=True)
    sa = fields.Str()           # Use a specific service account saved in database table `dh_service_account`
    engine = fields.Str()
    namespace = fields.Str(validate=validate.OneOf([
        "dist-host-retail/dhprod",
        "dist-host-retail/dhppe",
        "dist-host-retail/dhdt",
        "dist-host-retail/ikead2"
    ]))
    url = fields.Str()

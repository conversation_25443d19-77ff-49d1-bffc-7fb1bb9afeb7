2025-08-01 16:57:55,305 INFO Start to run the task.
2025-08-01 16:57:58,790 INFO Checking Maintenance Mode
2025-08-01 16:57:58,790 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-08-01 16:57:58,791 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-01 16:58:01,323 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 16:58:01,324 INFO SSH Executing '/home/<USER>/prism/cli/ncli host list --json=pretty'.
2025-08-01 16:58:02,158 INFO Waiting for 5 seconds for the execution.
2025-08-01 16:58:07,168 INFO stdout: b'{\n  "data" : [ {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::4",\n    "uuid" : "8a276a5a-9cd0-4702-8ed9-5699d07c192e",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH967RD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::60",\n        "diskUuid" : "ba87f2bb-2824-44e3-8240-75c32c2c4e80",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH967RD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH99N2D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::51",\n        "diskUuid" : "35ea81b9-7cfb-4924-9b45-f07a4cd6fc3f",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N2D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH7B71D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::53",\n        "diskUuid" : "65a27c83-885e-4672-be27-05971598814b",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7B71D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9726D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::61",\n        "diskUuid" : "f4390867-7871-494b-8fd2-65dfa19de512",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9726D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH9B3ED",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::58",\n        "diskUuid" : "9979a224-9d9e-42bc-a282-b1af4e2f9acd",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B3ED",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9421D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::52",\n        "diskUuid" : "01e76618-04df-4259-a761-7e0bab50887b",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9421D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH8DKKD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::56",\n        "diskUuid" : "e31d9325-c534-4c9e-a67e-65081ebff024",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8DKKD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98J3D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::57",\n        "diskUuid" : "c61322d0-febf-45eb-8293-8bb50ef57e95",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98J3D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307893",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::55",\n        "diskUuid" : "d55987bc-a972-43e0-8c49-380c1fed8ff0",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307893",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307888",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::54",\n        "diskUuid" : "514be4f9-c6dc-4f16-805f-8372704d5f1d",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307888",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7001",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8S",\n    "blockSerial" : "CZ20240J8S",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753432724206906,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "20000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "133333",\n      "controller_num_iops" : "76",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "583",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "296057",\n      "controller_num_write_io" : "2294",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "2750781",\n      "controller_total_read_io_size_kbytes" : "4",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "435",\n      "content_cache_num_lookups" : "2225",\n      "controller_total_io_size_kbytes" : "21384",\n      "content_cache_hit_ppm" : "657977",\n      "controller_num_io" : "2295",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "96",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1198",\n      "num_io" : "15",\n      "controller_num_read_io" : "1",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "712",\n      "hypervisor_num_received_bytes" : "2585073734117",\n      "hypervisor_timespan_usecs" : "29753239",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "19",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "2433",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "712",\n      "controller_write_io_ppm" : "999564",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "1936121263144",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "0",\n      "hypervisor_memory_usage_ppm" : "412539",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "76",\n      "total_io_time_usecs" : "36509",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "4",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "2",\n      "write_io_bandwidth_kBps" : "7",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "583",\n      "num_write_io" : "13",\n      "total_io_size_kbytes" : "159",\n      "io_bandwidth_kBps" : "7",\n      "content_cache_physical_memory_usage_bytes" : "2458047612",\n      "controller_timespan_usecs" : "30000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-84369532",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "866666",\n      "controller_avg_write_io_latency_usecs" : "1198",\n      "content_cache_logical_memory_usage_bytes" : "2373678080"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "6649413632",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "865674436608",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97633821822568",\n      "storage_tier.ssd.usage_bytes" : "************",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91529312495208",\n      "storage.usage_bytes" : "************",\n      "storage_tier.ssd.free_bytes" : "6104509327360"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::5",\n    "uuid" : "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH9453D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::63",\n        "diskUuid" : "55b9bef0-e64e-4b83-b56d-07424ac5a4fa",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9453D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH9B0JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::69",\n        "diskUuid" : "87299dda-79ad-4648-a18d-867837bdb061",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B0JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH947JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::66",\n        "diskUuid" : "21accc04-9438-4b1e-a735-121e6651e2c1",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH947JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9B2GD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::68",\n        "diskUuid" : "8fcec93c-f3a5-4a88-a18f-ad5a75a3d36e",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B2GD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH95WYD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::64",\n        "diskUuid" : "190548f4-f43c-40fd-8e0d-c805d972bf31",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH95WYD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9984D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::67",\n        "diskUuid" : "f73b0d65-6966-4e81-92fb-7815e0986aea",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9984D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH99N1D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::70",\n        "diskUuid" : "ebe3c979-d24f-46fb-9f2d-06b675e7f957",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N1D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98WLD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::65",\n        "diskUuid" : "3b10236d-18ef-48d1-b777-8f9f6b64ced8",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98WLD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N200095",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::72",\n        "diskUuid" : "4ce56fa0-31fa-45aa-9740-39f6561ab0e2",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N200095",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307878",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::71",\n        "diskUuid" : "064c52a5-ac8a-4f7f-ae98-967f225ddb32",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307878",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7002",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********30",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********30"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8R",\n    "blockSerial" : "CZ20240J8R",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753430337664248,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "30000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "500000",\n      "controller_num_iops" : "67",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "254705",\n      "controller_num_write_io" : "2038",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "2993384",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "260",\n      "controller_total_io_size_kbytes" : "19280",\n      "content_cache_hit_ppm" : "957692",\n      "controller_num_io" : "2038",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "95",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1468",\n      "num_io" : "4",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "642",\n      "hypervisor_num_received_bytes" : "2915954137130",\n      "hypervisor_timespan_usecs" : "29914302",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "8",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "155",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "642",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "1964003049870",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "0",\n      "hypervisor_memory_usage_ppm" : "412461",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "67",\n      "total_io_time_usecs" : "623",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "2",\n      "write_io_bandwidth_kBps" : "0",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "2",\n      "total_io_size_kbytes" : "24",\n      "io_bandwidth_kBps" : "0",\n      "content_cache_physical_memory_usage_bytes" : "1929818256",\n      "controller_timespan_usecs" : "30000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-82134952",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "500000",\n      "controller_avg_write_io_latency_usecs" : "1468",\n      "content_cache_logical_memory_usage_bytes" : "1847683304"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "6765035520",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "915915325440",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97579920883304",\n      "storage_tier.ssd.usage_bytes" : "************",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91529196873320",\n      "storage.usage_bytes" : "************",\n      "storage_tier.ssd.free_bytes" : "6050724009984"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::6",\n    "uuid" : "34ff0abd-9dee-4e7d-9481-4716d64569b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH96JDD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::41",\n        "diskUuid" : "2c172a78-3626-4861-bfff-98d6d79fef49",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH96JDD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH995TD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::43",\n        "diskUuid" : "94de93d2-5e8f-44d7-85d7-c414177670a7",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH995TD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH991DD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::46",\n        "diskUuid" : "4d3bffc9-fa0d-4ff3-bd95-436cf68ab516",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH991DD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9825D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::45",\n        "diskUuid" : "ea324991-7bb3-49a9-84f6-7c30e1c938a5",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9825D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH8XYHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::48",\n        "diskUuid" : "5f14642e-c45b-4efc-b5ee-95c532192c22",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8XYHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH99MHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::44",\n        "diskUuid" : "5758e233-4edc-4045-8446-0625e9ba05c0",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99MHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH9ADHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::47",\n        "diskUuid" : "dbeb0a0e-ff11-4b98-a2b7-a45bad3407fd",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9ADHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH7XGHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::42",\n        "diskUuid" : "08dba0c0-cd98-4d75-a131-a60323ba43b5",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7XGHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307864",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::50",\n        "diskUuid" : "39e3bde6-2802-4ad2-825c-a0d61ae9fb35",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307864",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307881",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::49",\n        "diskUuid" : "a032c48d-3866-4fd6-88af-1f6a25fe5ac3",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307881",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7003",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********31",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********31"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8Q",\n    "blockSerial" : "CZ20240J8Q",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 4,\n    "bootTimeInUsecs" : 1753435095317913,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "30000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "437500",\n      "controller_num_iops" : "484",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "435",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "241538",\n      "controller_num_write_io" : "14544",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "9257232",\n      "controller_total_read_io_size_kbytes" : "4",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "68",\n      "content_cache_num_lookups" : "426",\n      "controller_total_io_size_kbytes" : "151240",\n      "content_cache_hit_ppm" : "964788",\n      "controller_num_io" : "14545",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "96",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "636",\n      "num_io" : "16",\n      "controller_num_read_io" : "1",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "5041",\n      "hypervisor_num_received_bytes" : "1894458796311",\n      "hypervisor_timespan_usecs" : "29879895",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "44",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "4137",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "5041",\n      "controller_write_io_ppm" : "999931",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "3206311051747",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "1",\n      "hypervisor_memory_usage_ppm" : "509800",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "484",\n      "total_io_time_usecs" : "66202",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "4",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "7",\n      "write_io_bandwidth_kBps" : "2",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "10",\n      "controller_avg_read_io_latency_usecs" : "435",\n      "num_write_io" : "9",\n      "total_io_size_kbytes" : "128",\n      "io_bandwidth_kBps" : "4",\n      "content_cache_physical_memory_usage_bytes" : "2549974692",\n      "controller_timespan_usecs" : "30000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-83255372",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "562500",\n      "controller_avg_write_io_latency_usecs" : "636",\n      "content_cache_logical_memory_usage_bytes" : "2466719320"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "5846810624",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "1059449217024",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97426369046120",\n      "storage_tier.ssd.usage_bytes" : "822464184320",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91530115098216",\n      "storage.usage_bytes" : "828310994944",\n      "storage_tier.ssd.free_bytes" : "5896253947904"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  } ],\n  "status" : 0\n}\n'
2025-08-01 16:58:07,187 INFO All good, no hosts are set as NCLI maintenance inside this cluster.
2025-08-01 16:58:07,187 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-08-01 16:58:07,187 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-01 16:58:09,711 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 16:58:09,711 INFO SSH Executing '/usr/local/nutanix/bin/acli -o json host.list'.
2025-08-01 16:58:10,542 INFO Waiting for 5 seconds for the execution.
2025-08-01 16:58:15,543 INFO stdout: b'{"data": [{"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "8a276a5a-9cd0-4702-8ed9-5699d07c192e", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "34ff0abd-9dee-4e7d-9481-4716d64569b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}], "error": null, "status": 0}\n'
2025-08-01 16:58:15,544 INFO This seems a very old AOS version...
2025-08-01 16:58:15,544 INFO This seems a very old AOS version...
2025-08-01 16:58:15,544 INFO This seems a very old AOS version...
2025-08-01 16:58:15,556 INFO All good, no hosts are set as ACLI maintenance inside this cluster.
2025-08-01 16:58:15,569 INFO Checking CVM status
2025-08-01 16:58:16,046 INFO Trying to SSH to the RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 16:58:16,047 INFO First try with username/password.
2025-08-01 16:58:16,047 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-01 16:58:18,585 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 16:58:24,666 INFO Sending 'cluster status |grep -v UP' to the server.
2025-08-01 16:58:40,669 INFO CVM IP:*********** Status:Up
2025-08-01 16:58:40,669 INFO CVM IP:*********** Status:Up
2025-08-01 16:58:40,669 INFO CVM IP:*********** Status:Up
2025-08-01 16:58:40,669 INFO Great, all CVM status are Up
2025-08-01 16:58:40,699 INFO Calling restapi, URL: https://retseelm-nxc000.ikead2.com:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 16:58:40,699 INFO params: None
2025-08-01 16:58:40,699 INFO User: <EMAIL>
2025-08-01 16:58:40,701 INFO payload: None
2025-08-01 16:58:40,701 INFO files: None
2025-08-01 16:58:40,701 INFO timeout: 30
2025-08-01 16:58:42,443 INFO Getting host list from retseelm-nxc000.
2025-08-01 16:58:42,443 INFO Got the host list from retseelm-nxc000.
2025-08-01 16:58:42,443 INFO Got the host list.
2025-08-01 16:58:42,444 INFO Getting vault from IKEAD2.
2025-08-01 16:58:43,112 INFO Getting Site_Pe_Nutanix.
2025-08-01 16:58:43,998 INFO Got Site_Pe_Nutanix.
2025-08-01 16:58:43,998 INFO Getting Site_Pe_Admin.
2025-08-01 16:58:44,525 INFO Got Site_Pe_Admin.
2025-08-01 16:58:44,525 INFO Getting Site_Oob.
2025-08-01 16:58:45,049 INFO Got Site_Oob.
2025-08-01 16:58:45,049 INFO Getting Site_Ahv_Nutanix.
2025-08-01 16:58:45,568 INFO Got Site_Ahv_Nutanix.
2025-08-01 16:58:45,568 INFO Getting Site_Ahv_Root.
2025-08-01 16:58:46,093 INFO Got Site_Ahv_Root.
2025-08-01 16:58:46,093 INFO Getting Site_Gw_Priv_Key.
2025-08-01 16:58:46,564 INFO Got Site_Gw_Priv_Key.
2025-08-01 16:58:46,564 INFO Getting Site_Gw_Pub_Key.
2025-08-01 16:58:47,044 INFO Got Site_Gw_Pub_Key.
2025-08-01 16:58:47,044 INFO Getting Site_Pe_Svc.
2025-08-01 16:58:47,516 INFO Got Site_Pe_Svc.
2025-08-01 16:58:47,529 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-01 16:59:08,514 INFO Start reset CVM Nutanix password
2025-08-01 16:59:10,209 INFO Attempting to reset password for user 'nutanix' on cluster associated with CVM RETSEELM-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-01 16:59:12,017 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-01 16:59:14,534 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-01 16:59:28,695 INFO Unlocking user 'nutanix' on all CVMs.
2025-08-01 16:59:30,446 INFO SSH Executing 'allssh sudo faillock --user nutanix --reset'.
2025-08-01 16:59:30,975 INFO Waiting for 10 seconds for the execution.
2025-08-01 16:59:40,976 INFO stdout: b''
2025-08-01 16:59:42,452 INFO Changing password for 'nutanix' on single node RETSEELM-NXC000.ikead2.com.
2025-08-01 16:59:47,839 INFO SSH Executing '*****************************************************'.
2025-08-01 16:59:48,364 INFO Waiting for 5 seconds for the execution.
2025-08-01 16:59:53,365 INFO stdout: b'Changing password for user nutanix.\npasswd: all authentication tokens updated successfully.\n'
2025-08-01 17:00:12,022 INFO Password change command sent. Verifying new password for 'nutanix' with a new SSH connection.
2025-08-01 17:00:26,031 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-01 17:00:28,600 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-01 17:00:36,955 INFO Successfully reset and verified new password for user 'nutanix'.
2025-08-01 17:00:38,363 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Pe_Nutanix
2025-08-01 17:00:39,075 INFO Saving token completed.
2025-08-01 17:00:45,420 INFO taking a 30S extra powernap
2025-08-01 17:01:18,502 INFO Start reset admin password
2025-08-01 17:01:20,001 INFO Attempting to reset password for user 'admin' on cluster associated with CVM RETSEELM-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-01 17:01:21,732 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-01 17:01:24,275 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-01 17:01:28,534 INFO Unlocking user 'admin' on all CVMs.
2025-08-01 17:01:32,099 INFO SSH Executing 'allssh sudo faillock --user admin --reset'.
2025-08-01 17:01:32,619 INFO Waiting for 10 seconds for the execution.
2025-08-01 17:01:42,621 INFO stdout: b''
2025-08-01 17:01:43,677 INFO Changing password for 'admin' on single node RETSEELM-NXC000.ikead2.com.
2025-08-01 17:01:45,070 INFO SSH Executing '***************************************************'.
2025-08-01 17:01:45,588 INFO Waiting for 5 seconds for the execution.
2025-08-01 17:01:50,590 INFO stdout: b'Changing password for user admin.\npasswd: all authentication tokens updated successfully.\n'
2025-08-01 17:02:11,079 INFO Password change command sent. Verifying new password for 'admin' with a new SSH connection.
2025-08-01 17:02:27,106 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-01 17:02:29,611 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-01 17:02:42,997 INFO Successfully reset and verified new password for user 'admin'.
2025-08-01 17:02:44,305 INFO Saving token to Vault... Username: admin, label: RETSEELM-NXC000/Site_Pe_Admin
2025-08-01 17:02:44,991 INFO Saving token completed.
2025-08-01 17:04:39,663 INFO This is a central PE, start to reset PCVM password...
2025-08-01 17:05:21,511 INFO Resetting password for Site_Pc_Nutanix...
2025-08-01 17:05:29,453 INFO taking a 30S extra powernap
2025-08-01 17:06:10,540 INFO Start reset PCVM Site_Pc_Nutanix password
2025-08-01 17:06:12,148 INFO Attempting to reset password for user 'nutanix' on cluster associated with CVM ***********2, authenticating as 'nutanix'.
2025-08-01 17:06:13,732 INFO SSH connecting to ***********2, this is the '1' try.
2025-08-01 17:06:16,203 INFO SSH connected to ***********2.
2025-08-01 17:06:20,610 INFO Unlocking user 'nutanix' on all CVMs.
2025-08-01 17:06:21,985 INFO SSH Executing 'allssh sudo faillock --user nutanix --reset'.
2025-08-01 17:06:22,517 INFO Waiting for 10 seconds for the execution.
2025-08-01 17:06:32,517 INFO stdout: b''
2025-08-01 17:06:36,887 INFO Changing password for 'nutanix' on single node ***********2.
2025-08-01 17:06:38,711 INFO SSH Executing '*****************************************************'.
2025-08-01 17:06:39,246 INFO Waiting for 5 seconds for the execution.
2025-08-01 17:06:44,247 INFO stdout: b'Changing password for user nutanix.\npasswd: all authentication tokens updated successfully.\n'
2025-08-01 17:18:42,459 INFO Password change command sent. Verifying new password for 'nutanix' with a new SSH connection.
2025-08-01 17:18:50,459 INFO SSH connecting to ***********2, this is the '1' try.
2025-08-01 17:18:52,955 INFO SSH connected to ***********2.
2025-08-01 17:18:58,302 INFO Successfully reset and verified new password for user 'nutanix'.
2025-08-01 17:19:08,661 INFO Resetting password for Site_Pc_Admin...
2025-08-01 17:31:27,334 INFO taking a 30S extra powernap
2025-08-01 17:32:02,525 INFO Start reset PCVM Site_Pc_Admin password
2025-08-01 17:32:40,187 ERROR Task failed. Detail: ['Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\base_up_task.py", line 88, in start_task\n    self.task_process()\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\password_relate.py", line 158, in task_process\n    self.rotate_process()\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\password_relate.py", line 353, in rotate_process\n    self.logger.info(f\'This is centrol PE, Start reset 1-click-nutanix password for Pc {self.pc}\')\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\password_relate.py", line 145, in reset_passwd_pcvm\n    self.reset_passwd_cvm(self.pcip, data[\'username\'], data[\'secret\'], targetdata[\'username\'], new_passwd, public_key)\n                                     ~~~~^^^^^^^^^^^^\n', "TypeError: string indices must be integers, not 'str'\n"]

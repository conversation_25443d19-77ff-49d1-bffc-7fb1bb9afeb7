# Python Script
# Created by : <PERSON><PERSON>
# 3-Nov-2023
# <PERSON>ript to Check files exists and backup them.
# copy latest files from temp folder to main folder.

import os
import shutil
import datetime
import os.path
import zipfile


class clscheck_Files_Backup_Files:
    # Global declaration and assign values.
    global today
    global weekday

    def __init__(self):

        self.arc_formats = shutil.get_archive_formats()
        self.today = datetime.datetime.now()
        # Action to get current date and time
        self.current_datetime = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        self.str_current_datetime = str(self.current_datetime)
        self.archive_name = 'UnitPortalBackend_' + self.str_current_datetime
        self.directory_path = 'C:\\UnitPortalBackend-main\\'
        self.destination_folder = 'C:\\UnitPortal_Backup\\'
        self.target = 'C:\\UnitPortalfiles_Archived'
        self.temp_folder = 'C:\\D_Temp'
        self.all_files = []
        self.backup_files = []
        self.file_names = ['vars.json', 'DB_CONN.py', 'dhupapiikeacom.key', 'dhupapiikeacom.pem']

    # Return all the sub directories files from the path.
    def get_all_files_in_subdirectories(self, directory):

        for root, dirs, files in os.walk(directory):
            for file_name in files:
                file_path = os.path.join(root, file_name)
                self.all_files.append(file_path)

        return self.all_files

    # Function to remove all files and folders in a directory
    def remove_files_and_folders(self, directory):

        for item in os.listdir(directory):
            item_path = os.path.join(directory, item)
            if os.path.isfile(item_path):
                os.remove(item_path)
            elif os.path.isdir(item_path):
                shutil.rmtree(item_path)

    def copy_missing_files(self, temp_folder, main_folder, file_names):
        try:
            for root, _, files in os.walk(temp_folder):
                for file in files:
                    if file in file_names:

                        source_file_path = os.path.join(root, file)
                        # destination_file_path = os.path.join(main_folder, file)
                        if (file == file_names[0]):

                            shutil.copy2(source_file_path, main_folder + 'dpcdata\\ntx\\')
                        elif (file == file_names[1]):
                            shutil.copy2(source_file_path, main_folder + 'static\\')
                        elif ((file == file_names[2]) or (file == file_names[3])):
                            shutil.copy2(source_file_path, main_folder)

            print(f"Copied '{temp_folder}' to '{main_folder}'")

        except OSError as err:
            print("Error: % s" % err)
            # error caused if the source was not a directory

    # Action to backup files and archive from the backend main floder with date and time.
    def Backup_files(self):

        self.backup_files = self.get_all_files_in_subdirectories(
            self.directory_path)

        if os.path.exists(self.target):
            shutil.rmtree(self.target)
            shutil.copytree(self.directory_path, self.target)

        # Create the destination folder if it doesn't exist
        if not os.path.exists(self.destination_folder):
            os.makedirs(self.destination_folder)

        # Zip the source folder
        source_folder = self.target
        archive_path = os.path.join(
            self.destination_folder, f'{self.archive_name}.zip')
        with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for foldername, subfolders, filenames in os.walk(source_folder):
                for filename in filenames:
                    file_path = os.path.join(foldername, filename)
                    archive_file_path = os.path.relpath(file_path, source_folder)
                    zipf.write(file_path, archive_file_path)

        print("data from source file " + self.directory_path + " has been Zipped.")
        # Action to clear the Log data from Archive folder.
        self.remove_files_and_folders(self.target)

    # Action to rollback files from latest backup zip file from backup folder.
    def unzip_latest_zip(self, src_path, dest_path):
        # Get a list of all files in the source path
        all_files = os.listdir(src_path)

        # Filter only zip files
        zip_files = [file for file in all_files if file.endswith('.zip')]
        # print(zip_files)
        if not zip_files:
            print("No zip files found in the specified path.")
            return
        else:
            # Find the latest zip file based on modification time
            latest_zip = max(zip_files, key=lambda file: os.path.getmtime(os.path.join(src_path, file)))
            # print(latest_zip)
            # Create a full path for the latest zip file
            zip_path = os.path.join(src_path, latest_zip)

            # Create a temporary directory for extracting the contents
            temp_dir = os.path.join(src_path, 'temp_extract')
            os.makedirs(temp_dir, exist_ok=True)

        try:
            # Extract the contents of the zip file to the temporary directory
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)

            # Copy the contents from the temporary directory to the destination path
            shutil.copytree(temp_dir, dest_path, dirs_exist_ok=True)

            print(f"Successfully extracted and copied files from {latest_zip} to {dest_path}")
        except Exception as e:
            print(f"An error occurred: {e}")
        finally:
            # Clean up: Remove the temporary directory
            shutil.rmtree(temp_dir, ignore_errors=True)


# Calling Class object to invoke the methods/defination.
# funTaskArchive = check_Files_Backup_Files()
# # funTaskArchive.copy_missing_files(funTaskArchive.temp_folder, funTaskArchive.directory_path, funTaskArchive.file_names)
# # funTaskArchive.Backup_files()
# dis_path = 'C:\\UnitPortalBackend-main'
# src_folder = 'C:\\UnitPortal_Backup\\'
# funTaskArchive.unzip_latest_zip(src_folder, dis_path)
# # funTaskArchive.remove_files_and_folders()

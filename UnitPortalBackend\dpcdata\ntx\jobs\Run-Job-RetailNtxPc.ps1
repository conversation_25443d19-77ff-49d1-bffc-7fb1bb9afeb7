$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Launch-Job(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $LastUpdate       = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        #Rolling call PCs
        $DhPCs | Foreach-Object -ThrottleLimit 10 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We're now working on '$($_.fqdn)'" -DumpFile $using:DumpFile
            $PC         = $_
            $DictUpdate = $using:CollectionUpdate
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -PWord (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication" -DumpFile $using:DumpFile
                return
            }
            if ($LcmFwCall = Rest-Genesis-Get-LcmFw -Fqdn $PC.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "LCM framework of '$($PC.fqdn)' is available" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling LCM framework" -DumpFile $using:DumpFile
                return
            }
            $LcmApiVer = 'v4'
            switch ($LcmFwCall.semantic_version) {
                '*******' {
                    $LcmApiVer = 'v1'
                }
                '2.5.0.2' {
                    $LcmApiVer = 'v4'
                }
            }
            if ($LcmCall = & $('Rest-Lcm-' + $LcmApiVer + '-Get-LcmEntity') -Fqdn $PC.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "LCM inventory of '$($PC.fqdn)' is available" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling LCM to get inventory" -DumpFile $using:DumpFile
            }
            if ($PrismCall1 = Rest-Prism-v1-Get-Cluster -Fqdn $PC.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "PC profile of '$($PC.fqdn)' is available" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to get PC" -DumpFile $using:DumpFile
                return
            }
            if ($PrismCall2 = Rest-Prism-v1-List-Cluster -Fqdn $PC.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "PE list of '$($PC.fqdn)' is available" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to list PEs" -DumpFile $using:DumpFile
                return
            }
            if ($PrismCall3 = Rest-Prism-v3-List-PcVm -Fqdn $PC.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "PC VMs profile of '$($PC.fqdn)' are available" -DumpFile $using:DumpFile
                $CentrlPeId = $PrismCall3.cluster_reference.uuid | Get-Unique
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to list PC VMs" -DumpFile $using:DumpFile
                return
            }
            if ($PrismCall4 = Rest-Prism-v3-Get-Cluster -Fqdn $PC.fqdn -Uuid $CentrlPeId -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "Central PE of '$($PC.fqdn)' is available" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to get central PE" -DumpFile $using:DumpFile
                return
            }
            if ($PrismCall5 = Rest-Prism-v1-Get-Keys -Fqdn $PC.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "Certificate information of '$($PC.fqdn)' is available" -DumpFile $using:DumpFile
                $CertExpiryDate = Convert-NtxCertExpiryDate -ExpiryDate $PrismCall5.expiryDate
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to get certificate information" -DumpFile $using:DumpFile
            }
            $R = [PSCustomObject]@{
                'fqdn'                 = ''
                'lcm_version'          = ''
                'calm_version'         = ''
                'cmu_version'          = ''
                'epsilon_version'      = ''
                'flowsecurity_version' = ''
                'licensing_version'    = ''
                'ncc_version'          = ''
                'pc_version'           = ''
                'uuid'                 = ''
                'cluster_number'       = ''
                'ip'                   = ''
                'central_pe_fqdn'      = ''
                'cert_expiry_date'     = ''
                'last_update'          = $using:LastUpdate
            }
            $R.fqdn           = $PC.fqdn.ToLower()
            $R.lcm_version    = if ($LcmFwCall.semantic_version) {$LcmFwCall.semantic_version}else {$LcmFwCall.version}
            switch ($LcmFwCall.semantic_version) {
                '*******' {
                    $R.calm_version         = ($LcmCall | Where-Object {$_.entity_model -eq 'Calm'}).version | Get-Unique
                    $R.cmu_version          = ($LcmCall | Where-Object {$_.entity_model -eq 'Cluster Maintenance Utilities'}).version | Get-Unique
                    $R.epsilon_version      = ($LcmCall | Where-Object {$_.entity_model -eq 'Epsilon'}).version | Get-Unique
                    $R.flowsecurity_version = ($LcmCall | Where-Object {$_.entity_model -eq 'Flow Security'}).version | Get-Unique
                    $R.licensing_version    = ($LcmCall | Where-Object {$_.entity_model -eq 'Licensing'}).version | Get-Unique
                    $R.ncc_version          = ($LcmCall | Where-Object {$_.entity_model -eq 'NCC'}).version
                    $R.pc_version           = ($LcmCall | Where-Object {$_.entity_model -eq 'PC'}).version
                }
                '2.5.0.2' {
                    $R.calm_version         = ($LcmCall | Where-Object {$_.entityModel -eq 'Calm'}).version | Get-Unique
                    $R.cmu_version          = ($LcmCall | Where-Object {$_.entityModel -eq 'Cluster Maintenance Utilities'}).version | Get-Unique
                    $R.epsilon_version      = ($LcmCall | Where-Object {$_.entityModel -eq 'Epsilon'}).version | Get-Unique
                    $R.flowsecurity_version = ($LcmCall | Where-Object {$_.entityModel -eq 'Flow Security'}).version | Get-Unique
                    $R.licensing_version    = ($LcmCall | Where-Object {$_.entityModel -eq 'Licensing'}).version | Get-Unique
                }
                '2.6.2' {
                    $R.calm_version         = ($LcmCall | Where-Object {$_.entityModel -eq 'Calm'}).version | Get-Unique
                    $R.cmu_version          = ($LcmCall | Where-Object {$_.entityModel -eq 'Cluster Maintenance Utilities'}).version | Get-Unique
                    $R.epsilon_version      = ($LcmCall | Where-Object {$_.entityModel -eq 'Epsilon'}).version | Get-Unique
                    $R.flowsecurity_version = ($LcmCall | Where-Object {$_.entityModel -eq 'Flow Network Security PC'}).version | Get-Unique
                    $R.licensing_version    = ($LcmCall | Where-Object {$_.entityModel -eq 'Licensing'}).version | Get-Unique
                }
                default {
                    $R.calm_version         = ($LcmCall | Where-Object {$_.entityModel -eq 'Calm'}).version | Get-Unique
                    $R.cmu_version          = ($LcmCall | Where-Object {$_.entityModel -eq 'Cluster Maintenance Utilities'}).version | Get-Unique
                    $R.epsilon_version      = ($LcmCall | Where-Object {$_.entityModel -eq 'Epsilon'}).version | Get-Unique
                    $R.flowsecurity_version = ($LcmCall | Where-Object {$_.entityModel -eq 'Flow Network Security PC'}).version | Get-Unique
                    $R.licensing_version    = ($LcmCall | Where-Object {$_.entityModel -eq 'Licensing'}).version | Get-Unique
                }
            }
            $R.uuid             = $PrismCall1.uuid
            $R.cluster_number   = $PrismCall2.entities.count
            $R.ip               = $PrismCall1.clusterExternalIPAddress
            $R.pc_version       = $PrismCall1.version
            $R.ncc_version      = $PrismCall1.nccVersion
            $R.central_pe_fqdn  = $PrismCall4.spec.name.ToLower() + '.' + $PC.domain
            $R.cert_expiry_date = $CertExpiryDate
            $DictUpdate.Add($R)
        }
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pc]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxPc-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job
# installed modules
import json
import logging
import os
import sys
import traceback
from flask import jsonify, request, Response, current_app, abort
from flask_restful import Resource
from flask_apispec import marshal_with, use_kwargs, doc
from flask_apispec.views import MethodResource
import werkzeug.exceptions as flaskex
# local files
from business.authentication.tokenvalidation import admintokencheck, PrivilegeValidation
from business.generic.commonfunc import FileDownloader
from business.generic.vm_action import VmAction
from business.generic.commonfunc import get_request_token
from business.distributedhosting.nutanix.nutanix import  NutanixOperation, PrismCentral, PrismElement, AllPrism, Ntx_Move
from business.distributedhosting.nutanix.ntx_move import MOVE
from business.distributedhosting.nutanix_wh.ntx_move_wh import MoveWh
from models.database import db
from models.ntx_models import ModelPrismCentral, ModelPrismElementSchema
from swagger.prism_schema import ListClusterVmRequestSchema, ListPeVmRequestSchema, PeVmSchema, UpdateHostRequestSchema, UpdatePeRequestSchema, UnlockAccountSchema

from models.atm_models import ModelNtxAutomationRotatePasswordTask, ModelNtxAutomationRotatePasswordTaskSchema, \
    ModelNtxAutomationRenewCertificateTask, ModelNtxAutomationRenewCertificateTaskSchema, ModelNtxAutomationDscTask, ModelNtxAutomationDscTaskSchema
from .route import route
from business.distributedhosting.nutanix.toolbox.unlock_account_task import UnlockAccountTask


@route('/api/v1/ntx/prism/list')
class RestfulPCList(Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_pc"})
    def get(self):
        try:
            res = PrismCentral.get_prism_list_from_db()
            return jsonify(res)
        except Exception:
            abort(500, "Internal error")

    def post(self, pc_id):
        pc = request.json
        prism = ModelPrismCentral.query.filter(ModelPrismCentral.id == pc_id).update(pc)
        db.session.commit()
        return prism


@route('/api/v1/ntx/prism')
class RestfulAddPC(Resource):
    @admintokencheck
    def post(self):
        try:
            data = request.get_json(force=True)
            if data['newsa']:
                sa = data['sa']
            else:
                sa = 2
            payload = {
                'fqdn': (data['fqdn']).lower(),
                'service_account': sa,
                'tier': data['tier']
            }
            pc = PrismCentral(pc='add')
            res, msg = pc.add_prism_to_db(payload)
            if res:
                pass
            else:
                abort(400, msg)
        except flaskex.BadRequest:
            abort(400, "Please make sure that you have a valid payload.")
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
                    else:
                        abort(500, e)
                else:
                    abort(500, e)
            else:
                abort(500, e)


@route('/api/v1/ntx/prism/refresh')
class RestfulPCRefresh(Resource):
    def get(self):
        try:
            # allprism = AllPrism()
            # res = allprism.refresh_all_prism()
            return 1
        except Exception:
            abort(500, "Internal error")


class RestfulUserAuthentication(Resource):
    pass


@route('/api/v1/ntx/pe/vm/list')
class RestfulPEVMList(MethodResource, Resource):
    @doc(description="List PE VMs", tags=['View Resources'])
    @use_kwargs(ListPeVmRequestSchema, location='json', apply=False)
    @marshal_with(PeVmSchema(many=True), code=200)
    @PrivilegeValidation(privilege={"role_ntx": "view_vm"})
    def post(self):
        """
        Note:
            This one is dynamically get from nutanix,
            Another /api/v1/ntx/cluster/vm/list: is from database
        """
        try:
            data = request.get_json(force=True)
            if 'pe' not in list(data.keys()):  # type: ignore
                raise Exception({'code': 400, 'message': 'Missing field [pe].'})
            pe = PrismElement(pe=data['pe'])  # type: ignore
            res, vm_list = pe.get_vm_list()
            if res:
                return Response(json.dumps(vm_list), status=200, mimetype='application/json')
            abort(500, vm_list)
        except flaskex.BadRequest:
            abort(400, "Please make sure that you have a valid payload.")
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
                    else:
                        abort(500, e)
                else:
                    abort(500, e)
            else:
                abort(500, e)


@route('/api/v1/ntx/pe/list')
class RestfulPEList(Resource, MethodResource):
    @doc(description="Get PE list from database, not a real-time response", tags=['View Resources'])
    @marshal_with(ModelPrismElementSchema, code=200)
    @PrivilegeValidation(privilege={"role_ntx": "view_pe"})
    def get(self):
        try:
            download = request.args.get('download', type=bool)
            page = request.args.get('page', type=int)
            limit = request.args.get('limit', type=int)
            res = PrismElement.get_pe_list_from_db(page=page, limit=limit)
            if download:
                file_path = FileDownloader(data=res).download()
                logging.info(f"Downloading file {file_path}...")

                # res = send_file(file_path, as_attachment=True)

                # @after_this_request
                # def delete_tmp_file(response):
                #     time.sleep(5)
                #     if os.path.exists(file_path):
                #         os.remove(file_path)
                #     return response

                def gene():
                    with open(file_path) as f:
                        yield from f
                    os.remove(file_path)

                res = current_app.response_class(gene(), mimetype='text/csv')
                res.headers.set('Content-Disposition', 'attachment', filename='data.csv')
                return res
            return jsonify(res)
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/pe/correspondence')
class RestfulPCPECORRESPONDENCE(Resource):
    def get(self):
        try:
            country_type = request.args.get("country", type=bool)
            return PrismElement.get_pc_pe_correspondence_from_db(country=country_type)
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/pe/cvmlist')
class RestfulSinglePECVMs(Resource):
    def post(self):
        try:
            data = request.get_json(force=True)
            if 'pe' not in list(data.keys()):  # type: ignore
                raise Exception({'code': 400, 'message': 'Missing field [pe].'})
            _pe = PrismElement(pe=data['pe'])
            res = _pe.get_cvmlist_singlepe_from_db()
            return res
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/vm/list')
class RestfulVMList(Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_vm"})
    def get(self):
        download = request.args.get('download', type=bool)
        page = request.args.get('page', type=int)
        limit = request.args.get('limit', type=int)
        try:
            res = AllPrism.get_vm_list(page=page, limit=limit)
            res = json.loads(res.data)
            pe_filter = request.args.get('pe', type=str)
            if pe_filter:
                res = [vm for vm in res if vm['pe_name'] == pe_filter]
            if not download:
                return jsonify(res)

            file_path = FileDownloader(data=res).download()
            logging.info(f"Downloading file {file_path}...")

            # res = send_file(file_path)
            def gene():
                with open(file_path) as f:
                    yield from f
                os.remove(file_path)

            res = current_app.response_class(gene(), mimetype='text/csv')
            res.headers.set('Content-Disposition', 'attachment', filename='data.csv')
            return res
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/cluster/vm/list')
class RestfulClusterVMList(Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_vm"})
    @use_kwargs(ListClusterVmRequestSchema, location='json')
    def post(self, **kwargs):  # pylint: disable=W0613
        """
        Note:
            This one is get from database,
            Another /api/v1/ntx/pe/vm/list: is from Nutanix
        """
        try:
            data = request.get_json(force=True)
            filter = {
                'pe_fqdn': data['pe'],
            }
            if 'include_cvm' in data and not data['include_cvm']:
                filter['is_cvm'] = False
            if 'is_available' in data:
                filter['status'] = 'Available' if data['is_available'] else 'Unavailable'
            result = PrismElement.get_vm_list_from_db(**filter)
            return Response(json.dumps(result), status=200, mimetype='application/json')
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/host')
class RestfulNtxHost(Resource):
    def get(self):
        # return jsonify(NtxHost().get_all_host_info_from_db())
        try:
            download = request.args.get('download', type=bool)
            page = request.args.get('page', type=int)
            limit = request.args.get('limit', type=int)
            res = PrismElement.get_ahv_list_from_db(page=page, limit=limit)
            if download:
                file_path = FileDownloader(data=json.loads(res.data)).download()
                logging.info(f"Downloading file {file_path}...")

                def gene():
                    with open(file_path) as f:
                        yield from f
                    os.remove(file_path)

                res = current_app.response_class(gene(), mimetype='text/csv')
                res.headers.set('Content-Disposition', 'attachment', filename='data.csv')
                return res
            return res
        except Exception:
            abort(500, "Internal error")
    
    @PrivilegeValidation(privilege={"role_ntx": "add_pc"})
    @use_kwargs(UpdateHostRequestSchema(), location='json')
    def put(self, **kwargs):   # pylint: disable=W0613
        data = request.get_json(force=True)
        uuid = data.get('uuid')
        # Find the Host by calling NutanixOperation class with uuid
        host = NutanixOperation.get_host_by_uuid_from_db(uuid=uuid, return_facility_type=False)
        # Update the PE details using other keys except fqdn
        update_payload = {key: value for key, value in data.items() if key != 'uuid'}
        for key, value in update_payload.items():
            setattr(host, key, value)
        db.session.commit()
        return jsonify({"message": "Host updated successfully."})


@route('/api/v1/ntx/move_pre')
class RestfulNtxMove(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        # return jsonify(NtxHost().get_all_host_info_from_db())
        try:
            token = get_request_token()
            if param := request.get_json(force=True):
                for _key in ['vc', 'sli_cluster', 'ntx_cluster', 'gst_password', 'gst_username', 'vm_list', 'tasktype']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) #raise if pm details not given
            else:
                raise Exception({'code': 400, 'message': 'Missing fields'}) #raise if pm details not given
            # data = request.get_json(force=True)

            move = MOVE(vc=param['vc'], sli_cluster=param['sli_cluster'], ntx_cluster=param['ntx_cluster'], token=token, vm_list=param['vm_list'], tasktype=param['tasktype'], gst_password=param['gst_password'], gst_username=param['gst_username'])
            res, mes = move.create_task()
            result = {'result': res, 'message': mes}
            return Response(json.dumps(result), status=200, mimetype='application/json')
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/move_creation')
class RestfulNtxMoveCreation(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        # return jsonify(NtxHost().get_all_host_info_from_db())
        try:
            token = get_request_token()
            if param := request.get_json(force=True):
                for _key in ['sli_cluster', 'ntx_cluster', 'tasktype']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) #raise if pm details not given
            else:
                raise Exception({'code': 400, 'message': 'Missing fields'}) #raise if pm details not given
            move = MOVE(sli_cluster=param['sli_cluster'], ntx_cluster=param['ntx_cluster'], tasktype=param['tasktype'], token=token)
            res, mes = move.create_task()
            result = {'result': res, 'message': mes}
            return Response(json.dumps(result), status=200, mimetype='application/json')
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/move_cutover')
class RestfulNtxMoveMigration(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        # return jsonify(NtxHost().get_all_host_info_from_db())
        try:
            token = get_request_token()
            if param := request.get_json(force=True):
                for _key in ['vc', 'sli_cluster', 'ntx_cluster', 'tasktype']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) #raise if pm details not given
            else:
                raise Exception({'code': 400, 'message': 'Missing fields'}) #raise if pm details not given
            move = MOVE(vc=param['vc'], sli_cluster=param['sli_cluster'], ntx_cluster=param['ntx_cluster'], tasktype=param['tasktype'], token=token)
            res, mes = move.create_task()
            result = {'result': res, 'message': mes}
            return Response(json.dumps(result), status=200, mimetype='application/json')
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/move_vm')
class RestfulGetMovevms(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        try:
            data = request.get_json(force=True)
            vm_list = data['vm_list']
            Ntx_Move(pe="RETCN581-NXC000").get_vm_moveinfos(vm_list=vm_list)
            return Response(json.dumps('task started'), status=200, mimetype='application/json')
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/move_clusterlist')
class RestfulGetMoveclusters(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def get(self):
        try:
            clusterinfo = Ntx_Move().get_clusterslist_move()
            return clusterinfo
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/move_tasks')
class RestfulNtxMoveTasks(Resource):
    # @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def get(self):
        try:
            taskinfo = Ntx_Move().get_tasks_move()
            return taskinfo
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/dsctasks')
class RestfulNtxDSCTasks(Resource):
    # @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def get(self):
        try:
            dsctasks = ModelNtxAutomationDscTask.query.all()
            dsctasksschma = ModelNtxAutomationDscTaskSchema(many=True)
            _dsctasks = dsctasksschma.dump(dsctasks)
            return jsonify(_dsctasks)
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/certasks')
class RestfulCERTasks(Resource):
    # @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def get(self):
        try:
            certasks = ModelNtxAutomationRenewCertificateTask.query.all()
            certasksschma = ModelNtxAutomationRenewCertificateTaskSchema(many=True)
            _certasks = certasksschma.dump(certasks)
            return jsonify(_certasks)
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/pwerotate/tasks')
class RestfulPWRotateTasks(Resource):
    # @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def get(self):
        try:
            pwretasks = ModelNtxAutomationRotatePasswordTask.query.all()
            pwretaskstasksschma = ModelNtxAutomationRotatePasswordTaskSchema(many=True)
            _pwretaskstasks = pwretaskstasksschma.dump(pwretasks)
            return jsonify(_pwretaskstasks)
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/getvlan')
class RestfulGetVlan(Resource):
    def post(self):
        try:
            data = request.get_json(force=True)
            ntx_cluster = data["ntxcluster"]
            _pe = PrismElement(pe=ntx_cluster)
            clusterinfo = _pe.get_network_by_vlan_id(vlan_id=104)
            return clusterinfo
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/sli/vms/nt')
class RestfulGetSliNTVMs(Resource):
    def post(self):
        try:
            data = request.get_json(force=True)
            cluster_name = data['sli_cluster']
            vc = data['vc']
            clusterinfo = Ntx_Move().get_sli_nt_vms(vc, cluster_name)
            return clusterinfo
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/addvmpd')
class RestfulAddVMsPD(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        try:
            data = request.get_json(force=True)
            uuid = data['uuid']
            pe = data['pe']
            clusterinfo = Ntx_Move(pe=pe)._add_vm_to_ntx_pd_process(vm_uuid=uuid)
            return clusterinfo
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/vmacp')
class RestfulVMsACP(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        try:
            data = request.get_json(force=True)
            pe = data['pe']
            clusterinfo = Ntx_Move(pe=pe).update_acp()
            return clusterinfo
        except Exception:
            abort(500, "Internal error")


class RestfulPBRService(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        try:
            data = request.get_json(force=True)
            host = data['host']
            clusterinfo = Ntx_Move().start_pbrservice_status(host=host)
            return clusterinfo
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/wh/ntx/move_brief_log')
class RestfulGetMoveLog(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        try:
            param = request.get_json(force=True)
            movewh = Ntx_Move(pe=param['pe'])
            return movewh.get_move_brief_log(), 200
        except:
            error = str(repr(traceback.format_exception(sys.exc_info())))
            return error, 500


@route('/api/v1/ntx/move_creation_wh')
class RestfulNtxMoveCreationWH(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        try:
            token = get_request_token()
            if param := request.get_json(force=True):
                for _key in ['site', 'ntx_cluster', 'tasktype']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) #raise if pm details not given
            else:
                raise Exception({'code': 400, 'message': 'Missing fields'}) #raise if pm details not given
            movewh = MoveWh(site=param['site'], ntx_cluster=param['ntx_cluster'], tasktype=param['tasktype'], token=token)
            res, mes = movewh.create_task()
            result = {'result': res, 'message': mes}
            return Response(json.dumps(result), status=200, mimetype='application/json')
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/wh/ntx/move_cutover')
class RestfulNtxMoveMigrationWH(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        # return jsonify(NtxHost().get_all_host_info_from_db())
        try:
            token = get_request_token()
            if param := request.get_json(force=True):
                for _key in ['vc', 'sli_cluster', 'ntx_cluster', 'tasktype']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) #raise if pm details not given
            else:
                raise Exception({'code': 400, 'message': 'Missing fields'}) #raise if pm details not given
            move = MOVE(vc=param['vc'], sli_cluster=param['sli_cluster'], ntx_cluster=param['ntx_cluster'], tasktype=param['tasktype'], token=token)
            res, mes = move.create_task()
            result = {'result': res, 'message': mes}
            return Response(json.dumps(result), status=200, mimetype='application/json')
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/wh/ntx/move_pre')
class RestfulNtxMoveWH(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        try:
            token = get_request_token()
            if param := request.get_json(force=True):
                for _key in ['site', 'ntx_cluster', 'tasktype']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) #raise if pm details not given
            else:
                raise Exception({'code': 400, 'message': 'Missing fields'}) #raise if pm details not given
            # data = request.get_json(force=True)
            movewh = MoveWh(site=param['site'], ntx_cluster=param['ntx_cluster'], tasktype=param['tasktype'], token=token)
            res, mes = movewh.create_task()
            result = {'result': res, 'message': mes}
            return Response(json.dumps(result), status=200, mimetype='application/json')
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/ntx/move_whclusterlist')
class RestfulGetMoveWHClusters(Resource):
    def get(self):
        try:
            clusterinfo = Ntx_Move().get_whclusterslist_move()
            return clusterinfo
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/wh/vms/list')
class RestfulGetWHVMs(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_move"})
    def post(self):
        try:
            data = request.get_json(force=True)
            cluster_name = data['cluster']
            # clusterinfo=Ntx_Move(sli_sa=whsa).get_wh_vms(vc='itseelm-bb4441.ikea.com', cluster_name=cluster_name)
            clusterinfo = Ntx_Move().get_wh_vms(cluster_name)
            return clusterinfo
        except Exception:
            abort(500, "Internal error")


#TODO Need assign role to this
class RestfulVmAction(Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_vm"})
    def post(self):
        data = request.get_json(force=True)
        action = data['action']
        vmaction = VmAction(data['pe'], data['vmname'])
        vmaction.vm_power_action(action)


class RestfulCreateRecoryPoint(Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_vm"})
    def post(self):
        data = request.get_json(force=True)
        vmaction = VmAction(data['pe'], data['vmname'])
        return vmaction.create_recovery_point()


class RestfulCheckRecoveryPoint(Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_vm"})
    def post(self):
        data = request.get_json(force=True)
        vmaction = VmAction(data['pe'], data['vmname'])
        return vmaction.check_recovery_point()


@route('/api/v1/ntx/unlock_account')
class RestfulUnlockAccount(MethodResource, Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_pe"})
    @doc(description="Unlock Nutanix cluster accounts", tags=['Toolbox'])
    @use_kwargs(UnlockAccountSchema, location='json')
    def post(self, pe_name):
        try:
            task = UnlockAccountTask(payload={'pe_name': pe_name})
            task_id = task.run()
            return {"message": f'Account unlock task started with ID: {task_id}', "status": True}, 200
                
        except Exception as e:
            return {"message": f'Failed to start unlock account task: {str(e)}', "status": False}, 500


@route('/api/v1/ntx/pe')
class RestfulPE(Resource):
    def get(self):
        pass
    
    def post(self):
        pass

    @PrivilegeValidation(privilege={"role_ntx": "add_pc"})
    @use_kwargs(UpdatePeRequestSchema(), location='json')
    def put(self, **kwargs):   # pylint: disable=W0613
        data = request.get_json(force=True)
        fqdn = data.get('fqdn')
        # Find the PE by calling NutanixOperation class with fqdn
        pe = NutanixOperation.get_pe_by_fqdn_from_db(fqdn=fqdn)
        # Update the PE details using other keys except fqdn
        update_payload = {key: value for key, value in data.items() if key != 'fqdn'}
        for key, value in update_payload.items():
            setattr(pe, key, value)
        db.session.commit()
        return jsonify({"message": "PE updated successfully."})

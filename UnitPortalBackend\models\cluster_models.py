import marshmallow
import json
from business.distributedhosting.nutanix.cluster.cluster_specs import ClusterSpec
from models.base_task_models import ModelBaseTask, ModelBaseTaskLog
from models.database import db, ma


class ModelClusterTask(ModelBaseTask):
    __tablename__ = 'dh_retail_ntx_new_cluster_task'
    pe_name                        = db.Column(db.String(255))
    pc_fqdn                        = db.Column(db.String(255))
    ahv_subnet                     = db.Column(db.String(255))
    benchmark_id                   = db.Column(db.Integer)
    stage                          = db.Column(db.Integer)
    stage_status                   = db.Column(db.String(50))
    scan_oob                       = db.Column(db.String(2000))
    selected_oob                   = db.Column(db.String(255))
    cluster_uuid                   = db.Column(db.String(255))
    ahv_cvm_subnet_id              = db.Column(db.String(255))
    oob_subnet_id                  = db.Column(db.String(255))
    pe_ip                          = db.Column(db.String(255))
    data_service_ip                = db.Column(db.String(255))
    ahv_cvm_oob_mapping            = db.Column(db.String(1000))
    step                           = db.Column(db.Integer)


class ModelClusterTaskSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelClusterTask
        load_instance = True

    @marshmallow.post_dump
    def patch_result(self, data, many, **kwargs): # pylint: disable=unused-argument
        """Represent translated languages as simple strings."""
        if data.get(ClusterSpec.SCAN_OOB):
            data[ClusterSpec.SCAN_OOB] = json.loads(data[ClusterSpec.SCAN_OOB])
        if data.get(ClusterSpec.SELECTED_OOB):
            data[ClusterSpec.SELECTED_OOB] = data[ClusterSpec.SELECTED_OOB].split(",")
        return data


class ModelClusterTaskLog(ModelBaseTaskLog):
    __tablename__ = 'dh_retail_ntx_new_cluster_task_log'
    stage = db.Column(db.Integer)


class ModelClusterTaskLogSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelClusterTaskLog
        load_instance = True


class ModelWhClusterTask(ModelBaseTask):
    __tablename__ = 'dh_wh_ntx_new_cluster_task'
    pe_name                        = db.Column(db.String(255))
    pe_name_a                      = db.Column(db.String(255))
    pe_name_b                      = db.Column(db.String(255))
    pc_fqdn                        = db.Column(db.String(255))
    is_metro_task                  = db.Column(db.Boolean)
    metro_task_id                  = db.Column(db.Integer)
    room_a_task_id                 = db.Column(db.Integer)
    room_b_task_id                 = db.Column(db.Integer)
    room_type                      = db.Column(db.String(255))
    ahv_subnet                     = db.Column(db.String(255))
    benchmark_id                   = db.Column(db.Integer)
    stage                          = db.Column(db.Integer)
    stage_status                   = db.Column(db.String(50))
    scan_oob                       = db.Column(db.String(5000))
    selected_oob                   = db.Column(db.String(255))
    cluster_uuid                   = db.Column(db.String(255))
    ahv_cvm_subnet_id              = db.Column(db.String(255))
    oob_subnet_id                  = db.Column(db.String(255))
    pe_ip                          = db.Column(db.String(255))
    data_service_ip                = db.Column(db.String(255))
    ahv_cvm_oob_mapping            = db.Column(db.String(5000))
    step                           = db.Column(db.Integer)


class ModelWhClusterTaskSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWhClusterTask
        load_instance = True

    @marshmallow.post_dump
    def patch_result(self, data, many, **kwargs): # pylint: disable=unused-argument
        """Represent translated languages as simple strings."""
        if data.get(ClusterSpec.SCAN_OOB):
            data[ClusterSpec.SCAN_OOB] = json.loads(data[ClusterSpec.SCAN_OOB])
        if data.get(ClusterSpec.SELECTED_OOB):
            data[ClusterSpec.SELECTED_OOB] = data[ClusterSpec.SELECTED_OOB].split(",")
        return data


class ModelWhClusterTaskLog(ModelBaseTaskLog):
    __tablename__ = 'dh_wh_ntx_new_cluster_task_log'
    stage = db.Column(db.Integer)


class ModelWhClusterTaskLogSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWhClusterTaskLog
        load_instance = True

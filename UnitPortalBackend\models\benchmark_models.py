from models.database import db, ma
from sqlalchemy.ext.hybrid import hybrid_property


class ModelNtxBenchmark(db.Model):
    __tablename__           = 'dh_ntx_benchmark'
    id                      = db.Column(db.Integer, primary_key=True)
    name                    = db.Column(db.String(255))
    facility                = db.Column(db.String(255))
    arch                    = db.Column(db.String(255))
    tier                    = db.Column(db.String(255))
    bmk_systems             = db.Column(db.Integer)
    bmk_backup              = db.Column(db.Integer)
    bmk_certificate         = db.Column(db.Integer)
    bmk_cluster_network     = db.Column(db.Integer)
    bmk_deployment          = db.Column(db.Integer)
    bmk_desire_state        = db.Column(db.Integer)
    bmk_endpoint            = db.Column(db.Integer)
    bmk_eula                = db.Column(db.Integer)
    bmk_group_mapping       = db.Column(db.Integer)
    bmk_lcm_activity        = db.Column(db.Integer)
    bmk_lcm_component       = db.Column(db.Integer)
    bmk_lcm_version         = db.Column(db.Integer)
    bmk_password_rotation   = db.Column(db.Integer)
    bmk_pm_activity         = db.Column(db.Integer)
    bmk_recipient           = db.Column(db.Integer)
    bmk_scheduler           = db.Column(db.Integer)
    bmk_storage             = db.Column(db.Integer)
    bmk_limits              = db.Column(db.Integer)
    bmk_timer               = db.Column(db.Integer)
    bmk_vault               = db.Column(db.Integer)
    bmk_vlan_config         = db.Column(db.Integer)
    bmk_brand               = db.Column(db.Integer)
    bmk_site                = db.Column(db.Integer)
    bmk_firewall_rules      = db.Column(db.Integer)
    bmk_atm_step            = db.Column(db.Integer)

    @hybrid_property
    def sub_bmk_names(self):
        return [_ for _ in ModelNtxBenchmark.__table__.columns.keys() if _.startswith("bmk_")]


class ModelNtxBenchmarkSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmark
        load_instance = True


class ModelNtxBenchmarkSystems(db.Model):
    __tablename__               = 'dh_ntx_benchmark_systems'
    id                          = db.Column(db.Integer, primary_key=True)
    index_label                 = db.Column(db.String(255))
    pc_cluster_ip               = db.Column(db.String(255))
    dns_servers_json            = db.Column(db.String(8000))
    ntp_servers_json            = db.Column(db.String(8000))
    dark_site_json              = db.Column(db.String(8000))
    self_service_name           = db.Column(db.String(255))
    ldap_domain                 = db.Column(db.String(255))
    ldap_domain_host            = db.Column(db.String(255))
    dns_zone                    = db.Column(db.String(255))
    dc_witness_ip               = db.Column(db.String(255))
    dc_witness_max_latency      = db.Column(db.String(255))
    dc_witness_name             = db.Column(db.String(255))
    site_proxy_name             = db.Column(db.String(255))
    site_proxy_ip               = db.Column(db.String(255))
    site_proxy_port             = db.Column(db.String(255))
    site_proxy_extra_white_list = db.Column(db.String(255))
    region                      = db.Column(db.String(255))
    oneview                     = db.Column(db.String(255))
    oneview_scope               = db.Column(db.String(255))
    timezone                    = db.Column(db.String(255))
    central_pe                  = db.Column(db.String(255))
    central_pe_ip               = db.Column(db.String(255))


class ModelNtxBenchmarkSystemsSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkSystems
        load_instance = True


class ModelNtxBenchmarkBackup(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_backup'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    rs_bw_policy_start_time         = db.Column(db.String(255))
    rs_bw_policy_end_time           = db.Column(db.String(255))
    day_capability_percentage       = db.Column(db.Integer)
    night_capability_percentage     = db.Column(db.Integer)
    rs_compression_enabled          = db.Column(db.Integer)
    snapshot_expire_interval_days   = db.Column(db.Integer)
    gold_schedules_json             = db.Column(db.String(8000))
    silver_schedules_json           = db.Column(db.String(8000))


class ModelNtxBenchmarkBackupSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkBackup
        load_instance = True


class ModelNtxBenchmarkCertificate(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_certificate'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    organization_name               = db.Column(db.String(255))
    template                        = db.Column(db.String(255))
    ou_name                         = db.Column(db.String(255))
    chain                           = db.Column(db.String(8000))
    authority                       = db.Column(db.String(255))
    cert_owner_group                = db.Column(db.String(255))
    email_contact                   = db.Column(db.String(255))
    max_renewal_days_before_expiry  = db.Column(db.Integer)
    service_account                 = db.Column(db.String(255))


class ModelNtxBenchmarkCertificateSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkCertificate
        load_instance = True


class ModelNtxBenchmarkClusterNetwork(db.Model):
    __tablename__   = 'dh_ntx_benchmark_cluster_network'
    id              = db.Column(db.Integer, primary_key=True)
    index_label     = db.Column(db.String(255))
    bond_0_nics     = db.Column(db.Integer)
    bond_0_speed    = db.Column(db.Integer)
    bond_0_type     = db.Column(db.String(255))
    bond_1_nics     = db.Column(db.Integer)
    bond_1_speed    = db.Column(db.Integer)
    bond_1_type     = db.Column(db.String(255))
    ahv_cvm_native  = db.Column(db.Integer)


class ModelNtxBenchmarkClusterNetworkSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkClusterNetwork
        load_instance = True


class ModelNtxBenchmarkDeployment(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_deployment'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    install_pc                      = db.Column(db.Integer)
    join_pc                         = db.Column(db.Integer)
    join_self                       = db.Column(db.Integer)
    install_gateway                 = db.Column(db.Integer)
    oob_network_address             = db.Column(db.String(255))
    cvm_ram_gb                      = db.Column(db.Integer)
    is_partof_metro                 = db.Column(db.Integer)
    is_pci_dss                      = db.Column(db.Integer)
    remote_diag_disable             = db.Column(db.Boolean)


class ModelNtxBenchmarkDeploymentSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkDeployment
        load_instance = True


class ModelNtxBenchmarkDesireState(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_desire_state'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    config_items_json               = db.Column(db.String(8000))


class ModelNtxBenchmarkDesireStateSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkDesireState
        load_instance = True


class ModelNtxBenchmarkEndpoint(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_endpoint'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    endpoints_json                  = db.Column(db.String(8000))


class ModelNtxBenchmarkEndpointSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkEndpoint
        load_instance = True


class ModelNtxBenchmarkEula(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_eula'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    name                            = db.Column(db.String(255))
    company                         = db.Column(db.String(255))
    role                            = db.Column(db.String(255))


class ModelNtxBenchmarkEulaSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkEula
        load_instance = True


class ModelNtxBenchmarkFirewallRules(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_firewall_rules'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    rules_json                      = db.Column(db.String(8000))


class ModelNtxBenchmarkFirewallRulesSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkFirewallRules
        load_instance = True


class ModelNtxBenchmarkGroupMapping(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_group_mapping'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    pe_site_admin_group             = db.Column(db.String(255))
    px_site_viewer_users            = db.Column(db.String(255))
    pc_site_viewer_group            = db.Column(db.String(255))
    portal_site_admin_group         = db.Column(db.String(255))
    portal_site_user_group          = db.Column(db.String(255))
    ilo_ad_group                    = db.Column(db.String(255))
    ilo_join_cred                   = db.Column(db.String(255))
    roles_json                      = db.Column(db.String(8000))
    var_groups                      = db.Column(db.String(255))
    domain_group_search_mode        = db.Column(db.String(255))


class ModelNtxBenchmarkGroupMappingSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkGroupMapping
        load_instance = True


class ModelNtxBenchmarkLcmActivity(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_lcm_activity'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    allowed_start                   = db.Column(db.Integer)
    allowed_end                     = db.Column(db.Integer)
    aos_upgrade_timeout_sec         = db.Column(db.Integer)
    aos_upgrade_tasks_limit         = db.Column(db.Integer)
    spp_upgrade_timeout_sec         = db.Column(db.Integer)
    spp_upgrade_tasks_limit         = db.Column(db.Integer)


class ModelNtxBenchmarkLcmActivitySchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkLcmActivity
        load_instance = True


class ModelNtxBenchmarkLcmComponent(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_lcm_component'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    components_json                 = db.Column(db.String(8000))


class ModelNtxBenchmarkLcmComponentSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkLcmComponent
        load_instance = True


class ModelNtxBenchmarkLcmVersion(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_lcm_version'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    lcm_version                    = db.Column(db.String(255))
    aos_version                     = db.Column(db.String(255))
    aos_binary                      = db.Column(db.String(255)) 
    aos_metadata                    = db.Column(db.String(255))
    ahv_version                     = db.Column(db.String(255))
    ahv_binary                      = db.Column(db.String(255))
    foundation_version              = db.Column(db.String(255))
    foundation_binary               = db.Column(db.String(255))
    spp_lowest_version              = db.Column(db.String(255))
    spp_step_version                = db.Column(db.String(255))
    spp_base_version                = db.Column(db.String(255))
    spp_latest_version              = db.Column(db.String(255))
    pc_version                      = db.Column(db.String(255))
    pc_binary                       = db.Column(db.String(255))
    pc_metadata                     = db.Column(db.String(255))


class ModelNtxBenchmarkLcmVersionSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkLcmVersion
        load_instance = True


class ModelNtxBenchmarkPasswordRotation(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_password_rotation'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    rotation_policy_json            = db.Column(db.String(8000))


class ModelNtxBenchmarkPasswordRotationSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkPasswordRotation
        load_instance = True


class ModelNtxBenchmarkPmActivity(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_pm_activity'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    graceful_shutdown_sleep_sec     = db.Column(db.Integer)
    hard_shutdown_sleep_sec         = db.Column(db.Integer)
    cluster_shutdown_sleep_sec      = db.Column(db.Integer)
    cluster_startup_sleep_sec       = db.Column(db.Integer)
    ahv_shutdown_sleep_sec          = db.Column(db.Integer)
    ahv_startup_sleep_sec           = db.Column(db.Integer)
    host_state_loop_sleep_sec       = db.Column(db.Integer)
    query_oob_interval_sec          = db.Column(db.Integer)
    query_cvm_interval_sec          = db.Column(db.Integer)
    cvm_startup_sleep_sec           = db.Column(db.Integer)
    firewall_startup_sleep_sec      = db.Column(db.Integer)
    host_state_loop_times           = db.Column(db.Integer)
    start_cluster_loop_times        = db.Column(db.Integer)
    action_json                     = db.Column(db.String(8000))
    server_start_sequence_json      = db.Column(db.String(8000))


class ModelNtxBenchmarkPmActivitySchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkPmActivity
        load_instance = True


class ModelNtxBenchmarkRecipient(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_recipient'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    site_hands_email                = db.Column(db.String(255))
    alert_receiver_email            = db.Column(db.String(255))
    smtp_port                       = db.Column(db.Integer)
    smtp_server                     = db.Column(db.String(255))
    sender_email                    = db.Column(db.String(255))
    smtp_security                   = db.Column(db.String(255))
    code_receiver_email             = db.Column(db.String(255))


class ModelNtxBenchmarkRecipientSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkRecipient
        load_instance = True


class ModelNtxBenchmarkScheduler(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_scheduler'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    atm_schedule_json               = db.Column(db.String(8000))
    lcm_schedule_json               = db.Column(db.String(8000))


class ModelNtxBenchmarkSchedulerSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkScheduler
        load_instance = True


class ModelNtxBenchmarkStorage(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_storage'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    container_rules_json            = db.Column(db.String(8000))


class ModelNtxBenchmarkStorageSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkStorage
        load_instance = True


class ModelNtxBenchmarkLimits(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_limits'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    max_pc_latency                  = db.Column(db.Integer)
    max_witness_latency             = db.Column(db.Integer)


class ModelNtxBenchmarkLimitsSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkLimits
        load_instance = True


class ModelNtxBenchmarkTimer(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_timer'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    timers_json                     = db.Column(db.String(8000))


class ModelNtxBenchmarkTimerSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkTimer
        load_instance = True


class ModelNtxBenchmarkVault(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_vault'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    engine                          = db.Column(db.String(255))
    master_namespace                = db.Column(db.String(255))
    tier_namespace                  = db.Column(db.String(255))
    site_labels_json                = db.Column(db.String(8000))
    central_labels_json             = db.Column(db.String(8000))
    dc_labels_json                  = db.Column(db.String(8000))
    new_cluster_labels_json         = db.Column(db.String(8000))
    service_account                 = db.Column(db.String(255))


class ModelNtxBenchmarkVaultSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkVault
        load_instance = True


class ModelNtxBenchmarkVlanConfig(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_vlan_config'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    ahv_cvm_vlan                    = db.Column(db.Integer)
    oob_vlan                        = db.Column(db.Integer)
    pc_vlan                         = db.Column(db.Integer)


class ModelNtxBenchmarkVlanConfigSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkVlanConfig
        load_instance = True


class ModelNtxBenchmarkBrand(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_brand'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    brands_json                     = db.Column(db.String(8000))


class ModelNtxBenchmarkBrandSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkBrand
        load_instance = True


class ModelNtxBenchmarkSite(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_site'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    site_bu_json                    = db.Column(db.String(8000))


class ModelNtxBenchmarkSiteSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkSite
        load_instance = True


class ModelNtxBenchmarkAutoMaintenanceStep(db.Model):
    __tablename__                   = 'dh_ntx_benchmark_atm_step'
    id                              = db.Column(db.Integer, primary_key=True)
    index_label                     = db.Column(db.String(255))
    steps_json                     = db.Column(db.String(8000))


class ModelNtxBenchmarkAutoMaintenanceStepSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelNtxBenchmarkAutoMaintenanceStep
        load_instance = True


model_benchmark_mapping = {
    "bmk_systems"             : ModelNtxBenchmarkSystems,
    "bmk_backup"              : ModelNtxBenchmarkBackup,
    "bmk_certificate"         : ModelNtxBenchmarkCertificate,
    "bmk_cluster_network"     : ModelNtxBenchmarkClusterNetwork,
    "bmk_deployment"          : ModelNtxBenchmarkDeployment,
    "bmk_desire_state"        : ModelNtxBenchmarkDesireState,
    "bmk_endpoint"            : ModelNtxBenchmarkEndpoint,
    "bmk_eula"                : ModelNtxBenchmarkEula,
    "bmk_firewall_rules"      : ModelNtxBenchmarkFirewallRules,
    "bmk_group_mapping"       : ModelNtxBenchmarkGroupMapping,
    "bmk_lcm_activity"        : ModelNtxBenchmarkLcmActivity,
    "bmk_lcm_component"       : ModelNtxBenchmarkLcmComponent,
    "bmk_lcm_version"         : ModelNtxBenchmarkLcmVersion,
    "bmk_password_rotation"   : ModelNtxBenchmarkPasswordRotation,
    "bmk_pm_activity"         : ModelNtxBenchmarkPmActivity,
    "bmk_recipient"           : ModelNtxBenchmarkRecipient,
    "bmk_scheduler"           : ModelNtxBenchmarkScheduler,
    "bmk_storage"             : ModelNtxBenchmarkStorage,
    "bmk_limits"              : ModelNtxBenchmarkLimits,
    "bmk_timer"               : ModelNtxBenchmarkTimer,
    "bmk_vault"               : ModelNtxBenchmarkVault,
    "bmk_vlan_config"         : ModelNtxBenchmarkVlanConfig,
    "bmk_brand"               : ModelNtxBenchmarkBrand,
    "bmk_site"                : ModelNtxBenchmarkSite,
    "bmk_atm_step"            : ModelNtxBenchmarkAutoMaintenanceStep
}


model_schema_benchmark_mapping = {
    "bmk_systems"             : ModelNtxBenchmarkSystemsSchema,
    "bmk_backup"              : ModelNtxBenchmarkBackupSchema,
    "bmk_certificate"         : ModelNtxBenchmarkCertificateSchema,
    "bmk_cluster_network"     : ModelNtxBenchmarkClusterNetworkSchema,
    "bmk_deployment"          : ModelNtxBenchmarkDeploymentSchema,
    "bmk_desire_state"        : ModelNtxBenchmarkDesireStateSchema,
    "bmk_endpoint"            : ModelNtxBenchmarkEndpointSchema,
    "bmk_eula"                : ModelNtxBenchmarkEulaSchema,
    "bmk_firewall_rules"      : ModelNtxBenchmarkFirewallRulesSchema,
    "bmk_group_mapping"       : ModelNtxBenchmarkGroupMappingSchema,
    "bmk_lcm_activity"        : ModelNtxBenchmarkLcmActivitySchema,
    "bmk_lcm_component"       : ModelNtxBenchmarkLcmComponentSchema,
    "bmk_lcm_version"         : ModelNtxBenchmarkLcmVersionSchema,
    "bmk_password_rotation"   : ModelNtxBenchmarkPasswordRotationSchema,
    "bmk_pm_activity"         : ModelNtxBenchmarkPmActivitySchema,
    "bmk_recipient"           : ModelNtxBenchmarkRecipientSchema,
    "bmk_scheduler"           : ModelNtxBenchmarkSchedulerSchema,
    "bmk_storage"             : ModelNtxBenchmarkStorageSchema,
    "bmk_limits"              : ModelNtxBenchmarkLimitsSchema,
    "bmk_timer"               : ModelNtxBenchmarkTimerSchema,
    "bmk_vault"               : ModelNtxBenchmarkVaultSchema,
    "bmk_vlan_config"         : ModelNtxBenchmarkVlanConfigSchema,
    "bmk_brand"               : ModelNtxBenchmarkBrandSchema,
    "bmk_site"                : ModelNtxBenchmarkSiteSchema,
    "bmk_atm_step"            : ModelNtxBenchmarkAutoMaintenanceStepSchema
}
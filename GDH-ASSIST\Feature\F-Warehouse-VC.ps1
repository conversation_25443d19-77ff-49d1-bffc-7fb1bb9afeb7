function Get-WarehouseVcHost(){
    param(
        [string]
        $Cluster
    )
    $Results    = @{}
    $Successful = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Skipped    = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Vars       = Read-Var
    $VCAddress  = $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Name + `
                  "." + `
                  $Vars.Infrastructure.Warehouse.ESXI.VCenter.Prod.Domain
    $VCUsername = $Vars.GstAccount.username
    $VCPassword = $Vars.GstAccount.password

    $Session = $(Rest-VC-Get-Session -VCAddress $VCAddress `
                                     -VCUsername $VCUsername `
                                     -VCPassword $VCPassword).value
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We have obtain the session $Session"
    $Clusters = $(Rest-VC-List-Cluster -VCAddress $VCAddress `
                                       -Session $Session).value
    if ($Cluster) {
        $Clusters = $Clusters | Where-Object {$_.name -match $Cluster}
    }
    $Clusters | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $ClusterID  = $_.cluster
        $Cluster    = $_.name
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on the cluster $Cluster, the cluster id is $ClusterID"
        $Hosts = $(Rest-VC-List-Host -VCAddress $using:VCAddress `
                                     -Session $using:Session `
                                     -Clusters $ClusterID).value
        $DictSuccessful = $using:Successful
        $DictSkipped    = $using:Skipped
        if ($Hosts) {
            $Hosts | ForEach-Object {
                $Result = [PSCustomObject]@{
                    'cluster_id'       = $ClusterID
                    'cluster'          = $Cluster
                    'host_id'          = $_.host
                    'host'             = $_.name
                    'connection_state' = $_.connection_state
                    'power_state'      = $_.power_state
                }
                $DictSuccessful.Add($Result)
            }
        }else {
            $DictSkipped.Add(
                [PSCustomObject]@{
                'cluster_id' = $ClusterID
                'cluster'    = $Cluster
            })
        }
    } -ThrottleLimit 250
    $Results.Successful = $Successful
    $Results.Skipped    = $Skipped
    return $Results
}

function Confirm-WarehouseVcHostLogin(){
    param(
        [string]
        $Cluster
    )
    $Results    = @{}
    $Successful = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Failed     = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Skipped    = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $VCHosts    = $(Get-WarehouseVcHost).Successful
    if ($Cluster) {
        $VCHosts = $VCHosts | Where-Object {$_.cluster -match $Cluster}
    }
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We need to validate $($VCHosts.Count) hosts"
    if ($VCHosts) {
        $VCHosts | ForEach-Object -Parallel {
            Import-Module GDH-ASSIST
            $DictSuccessful = $using:Successful
            $DictFailed     = $using:Failed
            $DictSkipped    = $using:Skipped
            if (!($_.power_state) -or !('CONNECTED' -eq $_.connection_state)) {
                $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'UNREACHABLE'
                $DictSkipped.Add($_)
                Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "We skip the host $($_.host)"
                return
            }
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Try to login with the default password vmwarevmware1!"
            try {
                $VCHostSession = New-SSHSession -ComputerName $_.host `
                                                -Credential $(New-Object System.Management.Automation.PSCredential ('root', $('vmwarevmware1!' | ConvertTo-SecureString -AsPlainText -Force))) `
                                                -AcceptKey:$true `
                                                -ErrorAction Ignore
            }catch {
                Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "It is unable to login with the default password"
            }
            if ($VCHostSession) {
                $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'PASSWORD UNCHANGED'
                $DictFailed.Add($_)
                $VCHostSession.Disconnect()
                Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The default password of $($_.cluster) is unchanged"
                return
            }
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Try to login with the password stored in the Vault"
            $Secret = Get-SecretForWtp -Cluster $($_.cluster) -Display:$true
            if (!($Secret)) {
                $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'VAULT FAULT'
                $DictFailed.Add($_)
                Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The secret of the cluster $($_.cluster) does not exist"
                return
            }
            try {
                $VCHostSession = New-SSHSession -ComputerName $_.host `
                                                -Credential $(New-Object System.Management.Automation.PSCredential ($Secret.Username, $($Secret.Password | ConvertTo-SecureString -AsPlainText -Force))) `
                                                -AcceptKey:$true `
                                                -ErrorAction Ignore
            }catch {
                $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'UNKNOWN'
                $DictFailed.Add($_)
                Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message $_
                return
            }
            if ($VCHostSession) {
                $DictSuccessful.Add($_)
                $VCHostSession.Disconnect()
                Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The password of $($_.cluster) stored in the Vault is correct"
            }else {
                $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'LOGIN FAILED'
                $DictFailed.Add($_)
                Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The password of $($_.cluster) stored in the Vault is incorrect"
            }
        } -ThrottleLimit 100
    }
    $Results.Successful = $Successful
    $Results.Failed     = $Failed
    $Results.Skipped    = $Skipped
    return $Results
}
from marshmallow import Schema, fields


class CreateMetroVGSchema(Schema):
    pe = fields.Str(required=True)
    prism = fields.Str(required=True)
    vm_names = fields.List(fields.Str(), required=True)


class ListMetroVGSchema(Schema):
    pe = fields.Str(required=True)


class AddMetroVGDiskSchema(Schema):
    vg = fields.Str(required=True)
    disk_size = fields.Int(required=True)
    pe = fields.Str(required=True)


class AddMetroIscsiClientSchema(Schema):
    primary_ip = fields.Str(required=True)
    secondary_ip = fields.Str(required=True)
    vg_ext_id = fields.Str(required=True)
    pe = fields.Str(required=True)


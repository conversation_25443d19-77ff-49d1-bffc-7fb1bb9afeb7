import sqlalchemy
from marshmallow import Schema, fields, validate, validates_schema, ValidationError

from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.automation.seamless_lcm_specs import SeamlessLcmSpec
from business.distributedhosting.nutanix.base_up_task_property import BaseUpTaskProperty
from business.distributedhosting.nutanix.task_status import SeamlessLcmPlannedPEStatus, SeamlessLcmPlanStatus
from models.atm_models import ModelNutanixSeamlessLcmPlans, ModelNutanixSeamlessLcmPlannedPEs, \
    ModelRetailNutanixAutomationAOSLCMTask, ModelRetailNutanixAutomationSPPLCMTask
from models.database import db
from models.models import Model<PERSON>ountryCode
from models.ntx_models import ModelPrismCentral


class CreateSeamlessLcmPlanSchema(Schema):
    cluster_list = fields.Dict(required=True)       # TODO: nested, validate pe fqdn
    desired_plan_date = fields.Str(required=True)          # TODO: validate in ISO 8601 format!
    max_count = fields.Int(required=True)
    interval = fields.Int(required=True)    # minutes
    daily_report = fields.Boolean(required=True, default=False)
    execution_sequence = fields.Str(validate=validate.OneOf(["PC", "COUNTRY", "PE"]))
    lcm_type = fields.Str(required=True, validate=validate.OneOf(["AOS", "SPP"]))
    facility_type = fields.Str(required=True, validate=validate.OneOf([FacilityType.RETAIL, FacilityType.WAREHOUSE]))

    @validates_schema
    def validate_it(self, data, **kwargs):  # pylint: disable=unused-argument
        planned_pcs = list(data.get(SeamlessLcmSpec.CLUSTER_LIST).keys())
        all_pcs = [fqdn for (fqdn,) in ModelPrismCentral.query.with_entities(ModelPrismCentral.fqdn).all()]
        if not all(item in all_pcs for item in planned_pcs):
            raise ValidationError(f"Please choose PC from {all_pcs}!")
        self.validate_no_existing_lcm_task(data)

    def validate_pe(self, data, **kwargs):  # pylint: disable=unused-argument
        # NOT YET IMPLEMENTED
        _all_pcs = ModelPrismCentral.query(ModelPrismCentral.fqdn).all()
        clusters = data.get(SeamlessLcmSpec.CLUSTER_LIST)
        for pc, _ in clusters.items():
            for country_code, _pe_list in _.items():
                country_info = self.validate_country_code_exists(country_code)
                self.validate_country_is_in_pc(pc, country_info)
                # validate_pe_is_in_country
                # validate_

    def validate_country_code_exists(self, country_code):
        try:
            info = ModelCountryCode.query.filter_by(country_code=country_code).one()
            return info
        except sqlalchemy.exc.NoResultFound:
            raise ValidationError(f"Country code '{country_code}' doesn't exist in database!")

    def validate_country_is_in_pc(self, pc, country_info):
        if country_info.retail_pc != pc:
            raise ValidationError(f"Country '{country_info.country_code}' doesn't belong to PC '{pc}'!")

    def validate_pe_is_in_country(self, pe, country_info):
        pass

    def validate_no_existing_lcm_task(self, data):
        pes = [
            cluster
            for pc in data[SeamlessLcmSpec.CLUSTER_LIST].values()
            for country in pc.values()
            for cluster in country
        ]
        existing_aos_tasks = db.session.query(ModelRetailNutanixAutomationAOSLCMTask.pe).filter(
            ModelRetailNutanixAutomationAOSLCMTask.pe.in_(pes),
            ModelRetailNutanixAutomationAOSLCMTask.status.in_(SeamlessLcmPlannedPEStatus.IN_PROGRESS_STATUSES)
        ).all()
        existing_spp_tasks = db.session.query(ModelRetailNutanixAutomationSPPLCMTask.pe).filter(
            ModelRetailNutanixAutomationSPPLCMTask.pe.in_(pes),
            ModelRetailNutanixAutomationSPPLCMTask.status.in_(SeamlessLcmPlannedPEStatus.IN_PROGRESS_STATUSES)
        ).all()
        if existing_aos_tasks or existing_spp_tasks:
            raise ValidationError(
                "Following clusters already have ongoing LCM tasks: "
                f"{[_[0] for _ in existing_aos_tasks] if existing_aos_tasks else []}, "
                f"{[_[0] for _ in existing_spp_tasks] if existing_spp_tasks else []}. "
                "Please wait for them to finish before creating a new plan, or cancel them manually."
            )


class UpdateSeamlessLcmPlanRouteSchema(Schema):
    plan_id = fields.Int(required=True, validate=lambda x: x > 0)

    @validates_schema
    def validate_plan_id_exists(self, data, **kwargs):  # pylint: disable=unused-argument
        plan_id = data.get(SeamlessLcmSpec.PLAN_ID)
        plan = ModelNutanixSeamlessLcmPlans.query.filter_by(id=plan_id).one_or_none()
        if not plan:
            raise ValidationError(f"Plan with ID {plan_id} doesn't exist!")

    @validates_schema
    def validate_plan_status(self, data, **kwargs):  # pylint: disable=unused-argument
        plan_id = data.get(SeamlessLcmSpec.PLAN_ID)
        plan = ModelNutanixSeamlessLcmPlans.query.filter_by(id=plan_id).one()
        if plan.status in (SeamlessLcmPlanStatus.CANCELED, SeamlessLcmPlanStatus.COMPLETED):
            raise ValidationError(f"Current plan status '{plan.status}' is not allowed to update.")


class CancelSeamlessLcmPlanSchema(Schema):
    plan_id = fields.Int(required=True)

    @validates_schema
    def validate_plan_status(self, data, **kwargs):  # pylint: disable=unused-argument
        plan_id = data.get(SeamlessLcmSpec.PLAN_ID)
        plan = ModelNutanixSeamlessLcmPlans.query.filter_by(id=plan_id).one()
        if plan.status not in (SeamlessLcmPlanStatus.PLANNED, SeamlessLcmPlanStatus.IN_PROGRESS):
            raise ValidationError(f"Current plan status '{plan.status}' is not allowed to cancel.")


class PlannedPeSchema(Schema):
    plan_id = fields.Int(required=True)
    pe_fqdn = fields.Str(required=True)
    lcm_type = fields.Str(required=True, validate=validate.OneOf(["AOS", "SPP"]))
    facility_type = fields.Str(required=True, validate=validate.OneOf([FacilityType.RETAIL, FacilityType.WAREHOUSE]))

    def validate_pe_exists(self, data):     # pylint: disable=unused-argument
        try:
            ModelNutanixSeamlessLcmPlannedPEs.query.filter_by(
                plan_id=data.get(SeamlessLcmSpec.PLAN_ID),
                pe_fqdn=data.get(BaseUpTaskProperty.PE_FQDN),
                facility_type=data.get(BaseUpTaskProperty.FACILITY_TYPE),
                lcm_type=data.get(SeamlessLcmSpec.LCM_TYPE)
            ).one()
        except sqlalchemy.exc.NoResultFound:
            raise ValidationError(f"PE '{data.get(BaseUpTaskProperty.PE_FQDN)}' is not planned yet.")

    def validate_pe_status(self, data, valid_statuses=None):     # pylint: disable=unused-argument
        if valid_statuses is None:
            valid_statuses = [SeamlessLcmPlannedPEStatus.PLANNED]
        pe_fqdn = data.get(BaseUpTaskProperty.PE_FQDN)
        pe_current_status = ModelNutanixSeamlessLcmPlannedPEs.query.filter(
            ModelNutanixSeamlessLcmPlannedPEs.plan_id == data.get(SeamlessLcmSpec.PLAN_ID),
            ModelNutanixSeamlessLcmPlannedPEs.pe_fqdn == pe_fqdn,
            ModelNutanixSeamlessLcmPlannedPEs.lcm_type == data.get(SeamlessLcmSpec.LCM_TYPE)
        ).one().status
        if pe_current_status not in valid_statuses:
            raise ValidationError(f"PE '{pe_fqdn}' is in '{pe_current_status}' status, unable to update/cancel.")

    @validates_schema
    def validate_it(self, data, **kwargs):          # pylint: disable=unused-argument
        self.validate_pe_exists(data)
        self.validate_pe_status(data)


class CancelSeamlessLcmPlannedPeSchema(PlannedPeSchema):
    pass


class UpdateSeamlessLcmPlannedPeSchema(PlannedPeSchema):
    planned_date = fields.Str(required=True)

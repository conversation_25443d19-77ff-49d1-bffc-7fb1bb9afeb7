import json
import re
import threading
import time
from datetime import timedelta
import os
import uuid
from collections import defaultdict
from datetime import datetime
from ping3 import ping
import requests
import random

from werkzeug.exceptions import InternalServerError, BadRequest
from decimal import Decimal
from functools import wraps


from business.authentication.authentication import ServiceAccount, Vault
from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.automation.desired_state_config import DscStorageConfig, DscPulse, DscAuthConfig
from business.distributedhosting.nutanix.cluster.stage_scope import stage_scope
from business.loggings.loggings import IntegratedLogger, DBLogging
from business.distributedhosting.nutanix.automation.password_relate import PasswordRotate
from business.distributedhosting.nutanix.automation.renew_certificates import RenewCertificates
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.distributedhosting.nutanix.base_up_task_property import BaseUpTaskProperty
from business.distributedhosting.nutanix.cluster.cluster_specs import ClusterSpec, ClusterSpecWh
from business.distributedhosting.nutanix.cluster.create_cluster_exception import ConnectToSourceFailed, FCNodesNotReady, \
    IpamAhvCvmGatewayUnreachable, IpamLackingAhvCvmFreeIps, IpamLackingDefaultAhvCvmDhcpServers, FCClusterIsExist, \
    IpOrNameAlreadyInUse, NodeInterfaceLcapSettingWrong, NodeInterfaceWrong, FCDeployClusterFailed, \
    IpamParentSubnetIsZero, NodeInterfaceLinkConnectedToSameNetworkAdapter, ScanOobFailed, ErgontaskCheckFailed, BandwidthCheckFailed, \
    GetFCAPIKeyFailed, DnsNtpUnreachable, CreateVaultSecretFailed, JoinClusterToPCFailed, ConnectToPEFailed, CreateSvcUserFailed, \
    GetUserListFromCVMFailed, GrantClusterAdminRoleFailed, GrantUserAdminRoleFailed, ClusterCreationTimeout, FailedToGetCurrentUIConfiguration, FailedToUpdateUIConfiguration
from business.distributedhosting.nutanix.cluster.lldpcheck import LLDPNetworkAnalyzer
from business.distributedhosting.nutanix.foundation_central import FoundationCentral
from business.distributedhosting.nutanix.pe_components import RestCluster
from business.distributedhosting.nutanix.task_status import TaskStatus, NewClusterTaskStatus
from business.distributedhosting.nutanix.nutanix import Prism
from business.generic.base_up_exception import SSHFailed
from business.generic.commonfunc import SSHConnect, OneView, Redfish, setup_common_logger, NutanixAPI, terminate_process_by_id
from business.generic.ipam import Ipam
from business.generic.ipam_api import IpamAPI
from business.generic.ipam_exception import VlanMissingUnderSubnet
from models.cluster_models import ModelClusterTask, ModelClusterTaskSchema, ModelClusterTaskLog, \
    ModelClusterTaskLogSchema, ModelWhClusterTask, ModelWhClusterTaskSchema, ModelWhClusterTaskLog, \
    ModelWhClusterTaskLogSchema
from models.ntx_models import ModelPrismCentral, ModelPrismElement, ModelRetailNutanixOneview
from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
from static.SETTINGS import CLUSTER_LOG_PATH
from models.database import db
from business.benchmark.benchmark import Benchmark
from business.distributedhosting.nutanix.automation.automation import Automation


def update_step(func=None, step=None):
    if not hasattr(update_step, 'stage_steps'):
        update_step.stage_steps = {}

    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            if step is None:
                update_step.current_step += 1
                current_step = update_step.current_step
            else:
                current_step = step
                update_step.current_step = step

            self.set_task_property(ClusterSpec.STEP, str(current_step))
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                self.set_task_status(TaskStatus.ERROR)
                self.set_task_property(ClusterSpec.STAGE_STATUS, TaskStatus.ERROR)
                self.ilg.write(f"Step {current_step} failed: {e}", severity="error")
                raise e
        return wrapper

    if func is None:
        return decorator
    return decorator(func)


class CreateClusterTask(BaseUpTask):
    LOG_DIR = CLUSTER_LOG_PATH
    LOG_TYPE = "NTX_CLUSTER"
    TASK_TYPE = "CREATE_CLUSTER"
    EXISTING_TASK_ALLOWED = True
    TASK_ONGOING_STATUS = BaseUpTask.TASK_ONGOING_STATUS + [NewClusterTaskStatus.SUB_TASKS_TRIGGERED]
    FINAL_STATUS = BaseUpTask.FINAL_STATUS + [NewClusterTaskStatus.DEPLOYING_CLUSTER, NewClusterTaskStatus.WAITING_USER_INPUT]

    def __init__(self, payload, facility_type):
        if facility_type == FacilityType.RETAIL:
            super().__init__(
                ModelClusterTask, ModelClusterTaskSchema, ModelClusterTaskLog, ModelClusterTaskLogSchema, payload)
        else:
            super().__init__(
                ModelWhClusterTask, ModelWhClusterTaskSchema, ModelWhClusterTaskLog, ModelWhClusterTaskLogSchema, payload)
        if self.payload.get(ClusterSpec.STAGE) == 2:
            self.task_info = {
                ClusterSpec.PC_FQDN: self.payload[ClusterSpec.PC_FQDN],
                ClusterSpec.PE_NAME: self.payload[ClusterSpec.PE_NAME],
            }
        self.task_duplicated_kwargs = {
            ClusterSpec.PE_NAME: self.payload[ClusterSpec.PE_NAME],
        }
        if facility_type == FacilityType.RETAIL:
            self._init_ret_task_info()
        elif facility_type == FacilityType.WAREHOUSE:
            self._init_wh_task_info()
        self.facility_type = facility_type
        self.password_rotator = None

    def _init_password_rotator(self):
        if not self.password_rotator:
            pe_fqdn = self.model_pe.query.filter_by(name=self.task.pe_name).first().fqdn
            self.password_rotator = PasswordRotate(pe_fqdn, self.facility_type, self.task.pc_fqdn)
            self.password_rotator.ilg = self.ilg
            self.password_rotator.logger = self.logger
            self.password_rotator.db_logger = self.db_logger

    def _init_ret_task_info(self):
        self.task_identifier = self.payload[ClusterSpec.PE_NAME]

    def _init_wh_task_info(self):
        match self.payload.get(ClusterSpec.STAGE):
            case 2:
                self.task_info[ClusterSpecWh.IS_METRO_TASK] = True
                self.task_info[ClusterSpecWh.PE_NAME_A] = self.payload.get(ClusterSpecWh.PE_NAME_A)
                self.task_info[ClusterSpecWh.PE_NAME_B] = self.payload.get(ClusterSpecWh.PE_NAME_B)
                self.task_identifier = f"METRO_{self.payload[ClusterSpecWh.PE_NAME_A]}_{self.payload[ClusterSpecWh.PE_NAME_B]}"
            case 3:
                inherit_task_info = [
                    ClusterSpec.PC_FQDN, ClusterSpec.AHV_SUBNET, ClusterSpec.BENCHMARK_ID,
                    ClusterSpec.SCAN_OOB, ClusterSpec.AHV_CVM_SUBNET_ID, ClusterSpec.OOB_SUBNET_ID,
                    ClusterSpecWh.PE_NAME_A, ClusterSpecWh.PE_NAME_B
                ]
                metro_task = ModelWhClusterTask.query.filter_by(id=self.payload.get(ClusterSpecWh.METRO_TASK_ID)).first()
                for spec in inherit_task_info:
                    self.task_info[spec] = getattr(metro_task, spec)
                self.task_info[ClusterSpecWh.IS_METRO_TASK] = False
                self.task_info[ClusterSpec.STAGE] = self.payload.get(ClusterSpec.STAGE)
                self.task_info[ClusterSpecWh.METRO_TASK_ID] = self.payload.get(ClusterSpecWh.METRO_TASK_ID)
                self.task_info[ClusterSpecWh.ROOM_TYPE] = self.payload.get(ClusterSpecWh.ROOM_TYPE)
                if self.task_info[ClusterSpecWh.ROOM_TYPE] == "A":
                    self.task_info[ClusterSpec.PE_NAME] = self.task_info[ClusterSpecWh.PE_NAME_A]
                elif self.task_info[ClusterSpecWh.ROOM_TYPE] == "B":
                    self.task_info[ClusterSpec.PE_NAME] = self.task_info[ClusterSpecWh.PE_NAME_B]
                self.task_identifier = self.task_info[ClusterSpec.PE_NAME]

    def start_task(self):
        super().start_task()
        # After current sub-task finished (in self.FINAL_STATUS), check another sub-task's status and set the metro-task's status
        if self.facility_type == FacilityType.WAREHOUSE and not self.task.is_metro_task and self.task.stage >= 3:
            another_room_task = self._get_another_room_task()
            metro_task = ModelWhClusterTask.query.filter_by(id=self.task.metro_task_id).first()
            statuses = [self.task.status, another_room_task.status]
            if TaskStatus.ERROR in statuses:
                metro_task.status = NewClusterTaskStatus.SUB_TASKS_ERROR
            elif {NewClusterTaskStatus.DEPLOYING_CLUSTER, TaskStatus.IN_PROGRESS, NewClusterTaskStatus.WAITING_USER_INPUT} & set(statuses):
                metro_task.status = NewClusterTaskStatus.SUB_TASKS_TRIGGERED
            db.session.commit()

    def _get_another_room_task(self):
        metro_task = ModelWhClusterTask.query.filter_by(id=self.task.metro_task_id).first()
        another_room_task_id = metro_task.room_a_task_id if self.task.room_type == "B" else metro_task.room_b_task_id
        another_room_task = ModelWhClusterTask.query.filter_by(id=another_room_task_id).first()
        return another_room_task

    def task_process(self):
        ipam_sa = ServiceAccount(ServiceAccount.NUTANIX_PM).get_service_account()
        self.rest_ipam = IpamAPI(username=ipam_sa['username'], password=ipam_sa['password'], logger=self.logger)
        if self.facility_type == FacilityType.RETAIL:
            self.model_pc = ModelPrismCentral
            self.model_pe = ModelPrismElement
        elif self.facility_type == FacilityType.WAREHOUSE:
            self.model_pc = ModelWarehousePrismCentral
            self.model_pe = ModelWarehousePrismElement
        self.tier = self.model_pc.query.filter_by(fqdn=self.task.pc_fqdn).first().tier
        start_stage = self.payload[ClusterSpec.STAGE]
        for i in range(start_stage, max(stage_scope) + 1):
            if not stage_scope.get(i):
                self.logger.warning(f"Stage '{i}' is not in use, skip it...")
                continue
            func_info = stage_scope[i]
            self._run_single_stage(i, func_info.get("func"))
            self.set_task_property(BaseUpTaskProperty.STATUS, func_info.get("post_status", TaskStatus.IN_PROGRESS))
            if func_info.get("pause_after_stage"):
                return

    def _run_single_stage(self, stage, stage_function):
        if self.facility_type == FacilityType.WAREHOUSE and not self.task.is_metro_task and self.task.stage >= 3:
            self._handle_trigger_wh_sub_task()
        try:
            update_step.current_step = 0
            self.set_task_property(ClusterSpec.STAGE, stage)
            self.ilg.db_lg.lasting_columns = {ClusterSpec.STAGE: stage}
            getattr(self, stage_function)()
            self.set_task_property(ClusterSpec.STAGE_STATUS, TaskStatus.DONE)
        except Exception as e:
            self.set_task_property(ClusterSpec.STAGE_STATUS, TaskStatus.ERROR)
            raise e

    def _handle_trigger_wh_sub_task(self):
        # Make sure when triggering sub-task, the metro task status set to SUB_TASKS_TRIGGERED and stage set to 3
        metro_task = ModelWhClusterTask.query.filter_by(id=self.task.metro_task_id).first()
        metro_task.status = NewClusterTaskStatus.SUB_TASKS_TRIGGERED
        metro_task.stage = 3
        if self.task.room_type == "A":
            metro_task.room_a_task_id = self.task.id
        elif self.task.room_type == "B":
            time.sleep(5)
            metro_task.room_b_task_id = self.task.id
        db.session.commit()
        metro_logger = setup_common_logger(str(uuid.uuid4()), metro_task.detail_log_path)
        metro_db_logger = DBLogging(logdir=self.LOG_DIR, taskid=metro_task.id, logtype=self.LOG_TYPE, log_model=self.log_model)
        metro_ilg = IntegratedLogger(file_lg=metro_logger, db_lg=metro_db_logger)
        metro_ilg.write(f"Sub-task triggered, task id: {self.task.id}", severity="info")

    def stage2_detect_available_nodes(self):
        """
        1. Check AHV/CVM subnet gateway connectivity
        2. Scan and list out OOB DHCP IPs with default username/password
        Required inputs:
        - ahv_subnet
        - benchmark_id
        Properties stored into database after this stage:
        - ahv_subnet
        - benchmark_id
        - scan_oob
        - ahv_cvm_subnet_id
        - oob_subnet_id
        """
        self.ilg.write("Detect available nodes", severity="title")

        def _check_ahv_cvm_gateway_connectivity(ahv_ipam_obj):
            self.ilg.write("Checking AHV/CVM vlan gateway connectivity...")
            if ahv_ipam_obj.get('ip_class_parameters'):
                ahv_cvm_gw = Ipam.get_value_from_class_parameters("gateway", ahv_ipam_obj['ip_class_parameters'])
            elif ahv_ipam_obj.get('subnet_class_parameters'):
                ahv_cvm_gw = Ipam.get_value_from_class_parameters("gateway", ahv_ipam_obj['subnet_class_parameters'])
            response = ping(ahv_cvm_gw)
            if response is None or response is False:
                raise IpamAhvCvmGatewayUnreachable(ahv_cvm_gw)
            self.ilg.write(f"AHV/CVM vlan gateway: {ahv_cvm_gw} is able to connect.")

        ahv_vlan_ip = self.payload[ClusterSpec.AHV_SUBNET]
        self.set_task_property(ClusterSpec.AHV_SUBNET, ahv_vlan_ip)
        benchmark_id = self.payload[ClusterSpec.BENCHMARK_ID]
        self.set_task_property(ClusterSpec.BENCHMARK_ID, benchmark_id)
        ahv_ipam_obj = self.rest_ipam.ip_address_list(hostaddr=ahv_vlan_ip).json()[0]
        self.set_task_property(ClusterSpec.AHV_CVM_SUBNET_ID, ahv_ipam_obj["subnet_id"])
        _check_ahv_cvm_gateway_connectivity(ahv_ipam_obj)
        ips_under_oob_subnet = self.get_oob_subnet_ips(ahv_ipam_obj)
        oob_data = self.scan_oob(ips_under_oob_subnet)
        if self.facility_type == FacilityType.WAREHOUSE:
            ahv_cvm_info_instance = LLDPNetworkAnalyzer(self)
            oob_data = ahv_cvm_info_instance.get_ahv_cvm_info(oob_data)
        self.set_task_property(ClusterSpec.SCAN_OOB, json.dumps(oob_data))

    def get_oob_subnet_ips(self, ahv_ipam_obj):
        oob_vlan_id = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('vlan_config').get('oob_vlan')
        parent_subnet_name = ahv_ipam_obj["parent_subnet_name"]
        parent_subnet_id = ahv_ipam_obj["parent_subnet_id"]
        if int(parent_subnet_id) == 0:
            raise IpamParentSubnetIsZero(ahv_ipam_obj["subnet_id"])
        subnets_under_parent_subnet = self.rest_ipam.ip_block_subnet_list(parent_subnet_id=parent_subnet_id).json()
        oob_subnet = Ipam.find_subnet_by_vlan_id(oob_vlan_id, subnets_under_parent_subnet)
        if not oob_subnet:
            self.ilg.write(
                f"Can't find vlan {oob_vlan_id} under parent subnet {parent_subnet_id}. ", severity="warning")
            self.ilg.write("Maybe there are different segments... Try to find vlan by parent subnet name...")
            subnets_under_parent_subnet = self.rest_ipam.ip_block_subnet_list(parent_subnet_name=parent_subnet_name).json()
            oob_subnet = Ipam.find_subnet_by_vlan_id(oob_vlan_id, subnets_under_parent_subnet)
            if not oob_subnet:
                raise VlanMissingUnderSubnet(oob_vlan_id, parent_subnet_name)
        self.set_task_property(ClusterSpec.OOB_SUBNET_ID, oob_subnet["subnet_id"])
        return Ipam.calculate_all_ips_under_subnet(oob_subnet["start_hostaddr"], oob_subnet["subnet_size"])

    def scan_oob(self, ips_under_oob_subnet):
        self.logger.info("Scanning OOB IPs...")
        oob_list = []
        ilo_default_label = Benchmark().get_bmk_by_id(self.task.benchmark_id)["vault"]["new_cluster_labels"]['Site_Oob']
        ilo_default_sa = ServiceAccount(ilo_default_label).get_service_account()

        def _login_oob_thread(ip, username, password):
            self.init_flask_app()
            try:
                redfish = Redfish(ip, username, password, retry=1)
                redfish.get_ilo_token()
                _res, sn = redfish.get_ilo_state()
                oob_dict = {'ip': ip, 'sn': sn['SerialNumber']}
                oob_list.append(oob_dict)
                self.logger.info(f"Found OOBs: {oob_list}")
            except Exception:
                self.logger.warning(f"Login '{ip}' with default account failed. It's not a default iLO server.")
                pass

        thread_pool = list()
        self.logger.info(f"Trying to find oob between {ips_under_oob_subnet[0]} -- {ips_under_oob_subnet[-1]}...")
        for ip in ips_under_oob_subnet:
            t = threading.Thread(
                target=_login_oob_thread,
                kwargs={'ip': ip, 'username': ilo_default_sa["username"], 'password': ilo_default_sa["password"]}
            )
            t.start()
            thread_pool.append(t)
        for _t in thread_pool:
            _t.join()
        self.ilg.write(f"OOB IPs: {oob_list}")
        if not oob_list:
            raise ScanOobFailed()
        return oob_list

    def stage3_pre_check(self):
        """
        1. Find free static IPs for AHV/CVM and check if the amount meets expectation
        2. Scan AHV and CVM DHCP IPs with default username/password
        3. Find free static IPs for OOB
        Required inputs:
        - selected_oob
        Properties stored into database after this stage:
        - selected_oob
        - pe_ip
        - ahv_cvm_oob_mapping: used in stage 6
        step:
        1. check if we have enough free IPs
        2. test firewall endpoints
        3. check if the static IPs obtained from IPAM are already in use
        4. check if the node interface config is correct
        5. check if there are any error tasks on the PC.
        6. darksite test
        7. check foundation central api_key
        8. check if the cluster exists
        9. check dns and ntp is reachable
        """

        self.ilg.write("Pre-check", severity="title")
        self.check_ahv_cvm_dhcp_and_free_ip()
        self.test_firewall()
        self.check_cluster_ip_addresses_and_name_availability()
        self.check_node_interface_config()
        self.ssh_test_px_ergon_crashloop()
        self.darksite_test()
        self.check_fc_api_keys()
        self.check_if_cluster_exists()
        self.checking_dns_ntp()

    def check_ahv_cvm_dhcp_and_free_ip_for_wh(self):
        ahv_ipam_obj = self.rest_ipam.ip_address_list(hostaddr=self.task.ahv_subnet).json()[0]
        cvm_dhcps = self._get_dhcps_by_ssh(ahv_ipam_obj, 'Site_Pe_Nutanix', machine_type='CVM')
        return cvm_dhcps

    @update_step(step=1)
    #step 1 check IPAM if we have enough free IPs
    def check_ahv_cvm_dhcp_and_free_ip(self):
        selected_oob = self.payload[ClusterSpec.SELECTED_OOB]
        self.set_task_property(ClusterSpec.SELECTED_OOB, ",".join(selected_oob))
        host_num = len(selected_oob)
        expected_free_ip_num = 2 * host_num + 2
        is_another_room_deploying = False
        if self.facility_type == FacilityType.WAREHOUSE:
            another_room_task = self._get_another_room_task()
            if not another_room_task or another_room_task.stage < 6:
                # warehouse has two rooms, so we need to double the host_num
                # e.g. expected_free_ip_num = 20
                expected_free_ip_num *= 2
            else:
                is_another_room_deploying = True
                self.ilg.write("Due to another room is already deploying, only check one room's IP amount...")
        ahv_ipam_obj = self.rest_ipam.ip_address_list(hostaddr=self.task.ahv_subnet).json()[0]
        ahv_cvm_free_ip_list = self.check_if_free_ips_are_enough(ahv_ipam_obj, expected_free_ip_num)
        ahv_dhcps, cvm_dhcps = self.check_if_enough_default_ahv_cvm(ahv_ipam_obj)
        self.save_ahv_cvm_info_to_db(ahv_cvm_free_ip_list, host_num, expected_free_ip_num, ahv_dhcps, cvm_dhcps, is_another_room_deploying)

    def check_if_free_ips_are_enough(self, ahv_ipam_obj, expected_free_ip_num):
        self.ilg.write("Start to check if there is enough free IP under AHV subnet...")
        res = self.rest_ipam.ip_find_free_address(ahv_ipam_obj["subnet_id"], expected_free_ip_num).json()
        ahv_cvm_free_ip_list = [_["hostaddr"] for _ in res]
        if len(ahv_cvm_free_ip_list) < expected_free_ip_num:
            raise IpamLackingAhvCvmFreeIps(ahv_ipam_obj["subnet_id"], expected_free_ip_num, len(ahv_cvm_free_ip_list))
        self.ilg.write(f"Check done. Free IPs to use: {ahv_cvm_free_ip_list}")
        return ahv_cvm_free_ip_list

    def bulk_ssh(self, ips, username, password, machine_type=''):
        result = []

        def _thread(ip, username, password):
            ssh = SSHConnect(ip, username, password, retry=1)
            can_login, _ = ssh.connect(timeout=3)
            if can_login:
                msg = (f"'{ip}' logged in with user '{username}'. "
                       f"machine_type: {machine_type if machine_type else 'Not provided'}")
                self.logger.info(msg)
                result.append(ip)

        thread_pool = list()
        for ip in ips:
            thread = threading.Thread(
                target=_thread,
                kwargs={'ip': ip, 'username': username, 'password': password})
            thread.start()
            thread_pool.append(thread)
        for _t in thread_pool:
            _t.join()

        return result

    def _get_dhcps_by_ssh(self, ahv_ipam_obj, sa_label, machine_type):
        default_labels = Benchmark().get_bmk_by_id(self.task.benchmark_id)["vault"]["new_cluster_labels"]
        default_sa = ServiceAccount(default_labels[sa_label]).get_service_account()
        ips = Ipam.calculate_all_ips_under_subnet(ahv_ipam_obj["subnet_start_hostaddr"], ahv_ipam_obj["subnet_size"])
        dhcps = self.bulk_ssh(ips, default_sa["username"], default_sa["password"], machine_type)
        return dhcps

    def check_if_enough_default_ahv_cvm(self, ahv_ipam_obj):
        self.ilg.write("Check if enough AHV and CVM servers can be logon by default user")
        if self.facility_type == FacilityType.WAREHOUSE:
            scan_oob_data = json.loads(self.task.scan_oob)
            selected_oob = self.task.selected_oob.split(",")
            # Find corresponding cvm_ip and ahv_ip based on selected_oob
            ahv_dhcps = []
            cvm_dhcps = []
            for oob_ip in selected_oob:
                # Find matching record in scan_oob
                matched_record = next((record for record in scan_oob_data if record.get('ipmi_ip') == oob_ip), None)
                if matched_record:
                    if matched_record.get('cvm_ip'):
                        cvm_dhcps.append(matched_record['cvm_ip'])
                    if matched_record.get('host_ip'):
                        ahv_dhcps.append(matched_record['host_ip'])
        else:
            ahv_dhcps = self._get_dhcps_by_ssh(ahv_ipam_obj, 'Site_Ahv_Root', machine_type='AHV')
            self.logger.info(f"Found AHV DHCP: {ahv_dhcps}")
            cvm_dhcps = self._get_dhcps_by_ssh(ahv_ipam_obj, 'Site_Pe_Nutanix', machine_type='CVM')
            cvm_dhcps = [ip for ip in cvm_dhcps if ip not in ahv_dhcps]
            self.logger.info(f"Found CVM DHCP: {cvm_dhcps}")

        actual_alive_server_num = len(cvm_dhcps + ahv_dhcps)
        selected_oob = self.task.selected_oob.split(",")
        host_num = len(selected_oob)
        # if self.facility_type == FacilityType.WAREHOUSE:
        #     host_num *= 2
        expected_alive_servers_num = 2 * host_num
        self.ilg.write(
            f"There are {actual_alive_server_num} IPs under AHV/CVM network that can be logon by default user.")
        if actual_alive_server_num < expected_alive_servers_num:
            raise IpamLackingDefaultAhvCvmDhcpServers(
                ahv_ipam_obj["subnet_id"], expected_alive_servers_num, actual_alive_server_num)
        self.ilg.write("Check Done.")
        return ahv_dhcps, cvm_dhcps

    def save_ahv_cvm_info_to_db(self, ahv_cvm_free_ip_list, host_num, expected_free_ip_num, ahv_dhcps, cvm_dhcps, is_another_room_deploying):
        res = self.rest_ipam.ip_find_free_address(self.task.oob_subnet_id).json()
        oob_free_ip_list = [_["hostaddr"] for _ in res][:host_num]
        selected_oob = self.task.selected_oob.split(",")
        ahv_cvm_oob_mapping = []
        # for SiaB, or WiaB's room A
        ahv_cvm_free_ip_to_use = ahv_cvm_free_ip_list[:expected_free_ip_num]
        if self.facility_type == FacilityType.WAREHOUSE and self.task.room_type == "B" and not is_another_room_deploying:
            # for WiaB's room B, we need to use the second half of the free IPs
            ahv_cvm_free_ip_to_use = ahv_cvm_free_ip_list[(host_num * 2 + 2):(expected_free_ip_num)]
            oob_free_ip_list = [_["hostaddr"] for _ in res][host_num:(host_num * 2)]
        ahv_cvm_free_ip_list_iter = iter(ahv_cvm_free_ip_to_use)

        number = self.task.pe_name.split('-')[1].split('.')[0].lower().split('nxc00')[1]  # nx7?01
        for i in range(host_num):
            ahv_hostname = f"{self.task.pe_name.split('-')[0]}-nx7{number}0{i + 1}".lower()
            ahv_cvm_oob_mapping.append(
                {
                    "static": {
                        "ahv": {
                            "hostname": ahv_hostname,
                            "ip": next(ahv_cvm_free_ip_list_iter)
                        },
                        "cvm": {
                            "hostname": f"{ahv_hostname}cvm",
                            "ip": next(ahv_cvm_free_ip_list_iter)
                        },
                        "oob": {
                            "hostname": f"{ahv_hostname}oob",
                            "ip": oob_free_ip_list[i]
                        }
                    },
                    "dhcp": {
                        "ahv": ahv_dhcps[i],
                        "cvm": cvm_dhcps[i],
                        "oob": selected_oob[i]
                    }
                }
            )
        self.set_task_property(ClusterSpec.PE_IP, next(ahv_cvm_free_ip_list_iter))
        self.set_task_property(ClusterSpec.DATA_SERVICE_IP, next(ahv_cvm_free_ip_list_iter))
        self.set_task_property(ClusterSpec.AHV_CVM_OOB_MAPPING, json.dumps(ahv_cvm_oob_mapping))

    @update_step#Stage1-3>Test 14
    #step 5 Check if there are any error tasks on the PC.
    def ssh_test_px_ergon_crashloop(self):
        self.ilg.write("Check PC ergon task", severity="title")
        central_pe = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('central_pe')
        res, data = Vault(tier=self.tier).get_secret(f"{central_pe}/Site_Pc_Nutanix")
        ssh = SSHConnect(self.task.pc_fqdn, username=data["username"], password=data["secret"], logger=self.logger)
        res, _ = ssh.connect()
        if not res:
            raise InternalServerError("connect to pc failed.")        # Execute command and get output
        stdout = ssh.exec_command('tail -n 30 /home/<USER>/data/logs/ergon.FATAL')
        output = stdout.decode()
        output = output.split('\n')
        current_time = datetime.utcnow()
        errors = False
        for logline in output:
            if re.search(r"ERROR|exited with status:|StartServiceMonitor: Child", logline):
                errors = True
                date_str = re.match(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}", logline).group()
                date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                timespan = current_time - date_obj
                total_hours = round(timespan.total_seconds() / 3600)
                if total_hours >= 2:
                    self.ilg.write(f"This logline was written {total_hours} hours ago..")
                else:
                    self.ilg.write(f"This logline was written {timespan.total_seconds() / 60:.0f} minutes ago")
                    raise ErgontaskCheckFailed(f"{timespan.total_seconds() / 60:.0f}")
        if not errors:
            self.ilg.write("Ergon Memory is stable.")

    @update_step#Stage1-3>Test 17
    #step 6 darksite test
    def darksite_test(self):
        self.ilg.write("Darksite Test", severity="title")
        systems_data = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get("systems").get("dark_site_json")
        dark_site_url = f"http://{json.loads(systems_data)['endpoint']}"
        fw_path = json.loads(systems_data)['dir']['fw']
        lcm_data = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get("lcm_version")
        main_vars = {
                "DcxPayloadUrl": dark_site_url,
                "DarkSiteFWPath": fw_path,
                "Metadata": {
                    "AosFinalMeta": lcm_data.get("aos_metadata"),
                    "LoggingDir": f"{os.getcwd()}/Log/cluster"
                }
            }
        try:
            url = f"{main_vars['DcxPayloadUrl']}/{main_vars['DarkSiteFWPath']}/{main_vars['Metadata']['AosFinalMeta']}"
            # local_file_path = f"{main_vars['Metadata']['LoggingDir']}/DarkSiteTest2.json"

            response = requests.get(url, timeout=30)
            response.raise_for_status() # If the request was successful, the status code will be 200
            self.ilg.write(f"Successfully accessed: {url}")
        except Exception:
            self.ilg.write(f"Failed to download DarkSiteFWPath: {main_vars['DarkSiteFWPath']} from {main_vars['DcxPayloadUrl']}")

        #########start test bandwidth, makesure we can get default username and password from DB
        new_cluster_labels = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('vault').get('new_cluster_labels')
        sa = ServiceAccount(usage=(new_cluster_labels.get('Site_Pe_Nutanix'))).get_service_account()
        ahv_cvm_oob_mapping = json.loads(self.task.ahv_cvm_oob_mapping)
        cvm_dhcp_list = [_["dhcp"]["cvm"] for _ in ahv_cvm_oob_mapping]
        ssh = SSHConnect(host=cvm_dhcp_list[0], username=sa['username'], password=sa['password'], logger=self.logger)
        res, _ = ssh.connect()
        if not res:
            raise InternalServerError("connect to cvm failed.")
        ssh.invoke_shell()
        url = f"{dark_site_url}/1CN/dummy2.tar"
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            command = f"wget --tries=1 --timeout={300} '{url}' -O dummy2.tar"
            ssh.send_command(command)
            _output = ssh.receive_output()
            count = 0
            while count < 6:
                if "saved" in _output:
                    self.ilg.write("File download completed")
                    break
                else:
                    time.sleep(10)
                    _output += ssh.receive_output()
                    count += 1
                    
            if count == 6 and "saved" not in _output:
                retry_count += 1
                self.ilg.write(f"Download attempt {retry_count} timed out, retrying...")
                continue
                
            lines = _output.split("\n")
            saved_lines = [line for line in lines if "saved" in line]
            if saved_lines:
                speed = Decimal(re.sub(r"\(", "", saved_lines[0].split()[2].replace(")", "")))
                if speed >= 5:
                    self.ilg.write("Great, this will work with current site bandwidth settings.")
                    break
                else:
                    retry_count += 1
                    self.ilg.write(f"Download speed {speed} too low on attempt {retry_count}, retrying...")
            else:
                retry_count += 1
                self.ilg.write(f"Could not find download speed in output on attempt {retry_count}, retrying...")
                
        if retry_count == max_retries:
            raise BandwidthCheckFailed()

    @update_step#Stage1-3>Test 19
    #step 7 check foundation central api_key
    def check_fc_api_keys(self):
        vault = Vault(tier=self.tier)
        central_pe = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('central_pe')
        _res, pc_data = vault.get_secret(f"{central_pe}/Site_Pc_Admin")
        self.ilg.write("Pulling Foundation Central API Keys.")
        pc_ip = Benchmark().get_bmk_by_id(self.task.benchmark_id).get("systems").get("pc_cluster_ip")
        rest_fc = FoundationCentral(endpoint=pc_ip, username=pc_data['username'],  password=pc_data['secret'], logger=self.logger)
        fc_api_key = rest_fc.get_fc_api_keys()
        if not fc_api_key:
            raise GetFCAPIKeyFailed()
        return fc_api_key

    @update_step#Stage1-3>Test 20
    #step 8 check if the cluster exists
    def check_if_cluster_exists(self):
        res = self._get_cluster_detail_by_name()
        if res:
            raise FCClusterIsExist()
        self.ilg.write("Cluster does not exist, proceeding with creation.")

    @update_step#Stage1-3>Test 22
    #step 9 check dns and ntp is reachable
    def checking_dns_ntp(self):
        self.ilg.write("Checking DNS and NTP", severity="title")
        def _ssh_ping(ssh, target_ip):
            try:
                # Execute the ping command on the remote host
                command = f"ping -c 1 -w 5 {target_ip}"
                output = ssh.exec_command(command, wait=10)
                if "1 packets transmitted, 1 received" in output.decode("utf-8"):
                    return True
                return False
            except:
                return False

        usage = Benchmark().get_bmk_by_id(self.task.benchmark_id)["vault"]["new_cluster_labels"]["Site_Pe_Nutanix"]
        sa = ServiceAccount(usage=usage).get_service_account()
        ahv_cvm_oob_mapping = json.loads(self.task.ahv_cvm_oob_mapping)
        cvm_ip = ahv_cvm_oob_mapping[0]["dhcp"]["cvm"]
        ssh = SSHConnect(host=cvm_ip, username=sa['username'], password=sa['password'], logger=self.logger)
        self.ilg.write("Let's ssh to CVM then check DNS and NTP!")
        res, _ = ssh.connect()
        if not res:
            raise SSHFailed(cvm_ip)
        self.ilg.write(f"Great, ssh to CVM {cvm_ip} is successful, let's check DNS and NTP.")
        ahv_cvm_subnet = self.rest_ipam.get_subnets_by_subnet_id(self.task.ahv_cvm_subnet_id).json()[0]
        ahv_cvm_dns = [
            Ipam.get_value_from_class_parameters("ikea_infra_dns_1", ahv_cvm_subnet["subnet_class_parameters"]),
            Ipam.get_value_from_class_parameters("ikea_infra_dns_2", ahv_cvm_subnet["subnet_class_parameters"])]
        self.ilg.write("Starting DNS check for AHV CVM VLAN...")
        for dns in ahv_cvm_dns:
            response = _ssh_ping(ssh, dns)
            if not response:
                raise DnsNtpUnreachable(dns)
            self.ilg.write(f"cvm_dns {dns}  is reachable")
        self.ilg.write("Completed DNS check for AHV CVM VLAN.")
        oob_subnet = self.rest_ipam.get_subnets_by_subnet_id(self.task.oob_subnet_id).json()[0]
        oob_dns = [
            Ipam.get_value_from_class_parameters("ikea_dns_server", oob_subnet["subnet_class_parameters"]),
            Ipam.get_value_from_class_parameters("ikea_dns_server_2", oob_subnet["subnet_class_parameters"])]
        self.ilg.write("Starting DNS check for OOB VLAN...")
        for dns in oob_dns:
            response = _ssh_ping(ssh, dns)
            if not response:
                raise DnsNtpUnreachable(dns)
            self.ilg.write(f"OOB_DNS {dns} is reachable")
        self.ilg.write("Completed DNS check for OOB VLAN.")
        self.ilg.write("Starting NTP server check")
        ntp_servers = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get("systems").get('ntp_servers')
        for ntp in ntp_servers:
            response = _ssh_ping(ssh, ntp)
            if not response:
                self.ilg.write(f"Warning: NTP server {ntp} is unreachable", severity="warning")
                continue
            self.ilg.write(f"NTP {ntp} is reachable")
        self.ilg.write("Completed NTP check for Cluster")
        self.ilg.write("DNS and NTP check completed successfully.")

    def stage4_create_vault_secret(self):
        self.ilg.write("Create_vault_secret", severity="title")
        vault = Vault(tier=self.tier)
        labels = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get("vault").get("new_cluster_labels")
        for key, item in labels.items():
            self.ilg.write(f"writting {item}")
            label = f"{self.task.pe_name.upper()}/{key}"
            _sa = ServiceAccount(usage=item).get_service_account()
            description = f"Password for {item}"
            res, _data = vault.set_vault_password(_sa['username'], _sa['password'], label, description=description)
            if not res:
                raise CreateVaultSecretFailed(item)
            self.ilg.write(f"Add {item} done.")

    def stage5_upgrade_foundation(self):
        self.ilg.write("Upgrade Foundation", severity="title")
        vault = Vault(tier=self.tier)
        benchmark = Benchmark().get_bmk_by_id(self.task.benchmark_id)
        target_version = benchmark.get('lcm_version').get('foundation_version')
        fnd_filename = benchmark.get('lcm_version').get('foundation_binary')
        pc_ip = benchmark.get("systems", {}).get("pc_cluster_ip")
        darksite_url = self.model_pc.query.filter_by(fqdn=self.task.pc_fqdn).first().darksite
        fnd_download_path = f"http://{darksite_url}/1CN/{fnd_filename}"
        central_pe = benchmark.get('systems').get('central_pe')
        _res, pc_data = vault.get_secret(f"{central_pe}/Site_Pc_Svc")
        rest_fc = FoundationCentral(endpoint=pc_ip, username=pc_data['username'],  password=pc_data['secret'], logger=self.logger)
        fc_api_key = rest_fc.get_fc_api_keys()
        if not fc_api_key:
            raise GetFCAPIKeyFailed()
        ahv_cvm_subnet = self.rest_ipam.get_subnets_by_subnet_id(self.task.ahv_cvm_subnet_id).json()[0]
        dnsserver = Ipam.get_value_from_class_parameters("ikea_dns_server", ahv_cvm_subnet["subnet_class_parameters"])
        
        def _thread(cvm, data, index, pc_ip, fc_api_key, target_version, fnd_filename, fnd_download_path, dnsserver):
            self.init_flask_app()
            logs = []
            self.set_task_property(ClusterSpec.STEP, index)
            ssh = SSHConnect(cvm, data['username'], data['secret'], logger=self.logger)
            self.update_fc_for_cvm(ssh, logs, pc_ip, fc_api_key)
            self.update_resolvconf(ssh, logs, dnsserver)
            res, _ = ssh.connect()
            ssh.invoke_shell()
            if not res:
                raise InternalServerError("connect to cvm failed.")
            logs.append("Let's restart foundation service, will use 60s")
            command = 'genesis stop foundation && genesis restart'
            ssh.send_command(command)
            time.sleep(60)
            ssh.send_command("~/foundation/bin/foundation_service restart")
            time.sleep(20)
            version = self.check_foundation_version(ssh)
            if version < target_version:
                logs.append(f"The FND version on {cvm} is too low and needs to be upgraded.")
                logs.append(f"Download FND file for {cvm} first.")
                self.download_foundation_file(ssh, logs, fnd_download_path)
                logs.append(f"Start upgrade FND for {cvm}")
                self.upgrade_foundation(ssh, logs, target_version, fnd_filename)

            else:
                logs.append(f"{cvm} foundation version is already on or above {target_version}")
            log_queue.put((index, logs))

        self.ilg.write("Check cvm current FND version", severity="title")
        vault = Vault(tier=self.tier)
        _res, data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Nutanix")
        ahv_cvm_oob_mapping = json.loads(self.task.ahv_cvm_oob_mapping)
        cvm_dhcp_list = [_["dhcp"]["cvm"] for _ in ahv_cvm_oob_mapping]

        from queue import Queue
        log_queue = Queue()
        thread_pool = []

        for index, cvm in enumerate(cvm_dhcp_list, 1):
            self.ilg.write(f"Start upgrade FND for {cvm}, this may take 5 minutes.")
            thread = threading.Thread(target=_thread, kwargs={'cvm': cvm, 'data': data, 'index': index, 'pc_ip': pc_ip, 'fc_api_key': fc_api_key, 'target_version': target_version, 'fnd_filename': fnd_filename, 'fnd_download_path': fnd_download_path, 'dnsserver': dnsserver})
            thread.start()
            thread_pool.append(thread)
        for _t in thread_pool:
            _t.join()
        
        logs_by_index = {}
        while not log_queue.empty():
            index, logs = log_queue.get()
            logs_by_index[index] = logs

        for index in sorted(logs_by_index.keys()):
            self.ilg.write(f"=== Logs for CVM #{index} ===", severity="title")
            for log in logs_by_index[index]:
                self.ilg.write(log)

    def download_foundation_file(self, ssh, logs, fnd_download_path):
        command = "cd ~;mkdir foundation_updates;cd foundation_updates"
        ssh.send_command(command)
        ssh.send_command(f"wget {fnd_download_path}")
        logs.append("Sending download Api call, this may take a few minutes.")
        _output = ""
        count = 0
        while count < 8:
            if "saved" in _output:
                logs.append("File download completed")
                return
            time.sleep(30)
            _output += ssh.receive_output()
            count += 1
        raise InternalServerError("download filed. Please check with Admin")

    def upgrade_foundation(self, ssh, logs, target_version, fnd_filename):
        ssh.send_command("~/foundation/bin/foundation_service start")
        time.sleep(10)
        command = f"curl http://localhost:8000/foundation/auto_update_foundation?tar_file=/home/<USER>/foundation_updates/{fnd_filename}"
        ssh.send_command(command)
        logs.append("Started the foundation upgrade, let's wait!")
        time.sleep(60)
        count = 0
        while count < 10:
            version = self.check_foundation_version(ssh, if_restart=count >= 3)
            if version == target_version:
                logs.append("Great, FND upgrade success.")
                break
            else:
                logs.append("Waiting for FND upgrade to complete")
                time.sleep(20)
            count += 1
        else:
            logs.append("Looks FND upgrade not successfully after 5 mins, maybe it's still in upgrade process, you can wait more 5mins and check it again.")
            if version != target_version:
                raise InternalServerError("Upgrade failed, please check with Admin")

    def check_foundation_version(self, ssh, if_restart=False):
        if if_restart:
            ssh.send_command("~/foundation/bin/foundation_service restart")
            time.sleep(15)
        # ssh.send_command("tail -f /home/<USER>/foundation_updates/upgrade.log")
        ssh.send_command("curl localhost:8000/foundation/version")
        time.sleep(5)
        output = ssh.receive_output()
        if version_match := re.search(r'(\d+(\.\d+)*)(?=nutanix@)', output):
            return version_match.group(1)
        return 99999

    def update_fc_for_cvm(self, ssh, logs, pc_ip, fc_api_key):
        res, _ = ssh.connect()
        if not res:
            raise InternalServerError("connect to cvm failed.")
        completed = False
        count = 0
        while count < 6 and not completed:
            count += 1
            cycle = random.randint(1000, 9999)

            # Backup foundation_central.json if it exists
            ssh.exec_command(f"sudo cp /etc/nutanix/foundation_central.json /etc/nutanix/foundation_central.bak-{cycle} 2>/dev/null || true")

            # Check if foundation_central.json exists
            stdout = ssh.exec_command("sudo test -f /etc/nutanix/foundation_central.json && echo 'exists' || echo 'not exists'")
            file_exists = stdout.decode().strip() == 'exists'

            if file_exists:
                logs.append("Checking Current Foundation Central Settings")
                stdout = ssh.exec_command("sudo cat /etc/nutanix/foundation_central.json")
                if stdout:
                    foundation_central = json.loads(stdout)
                else:
                    logs.append("Error reading foundation_central.json")
                    foundation_central = {}
            else:
                logs.append("foundation_central.json does not exist, will create new file")
                foundation_central = {}

            if foundation_central.get('api_key') != fc_api_key or foundation_central.get('fc_ip') != pc_ip:
                logs.append("Updating AutoDC API Key and Host.")
                
                new_config = {
                    "fc_ip": pc_ip,
                    "api_key": fc_api_key
                }
                
                # Create or update file
                ssh.exec_command(f"echo '{json.dumps(new_config)}' | sudo tee /etc/nutanix/foundation_central.json")
                
                # Set file permissions
                ssh.exec_command("sudo chmod 644 /etc/nutanix/foundation_central.json")
                # ssh.exec_command("sudo chown root:root /etc/nutanix/foundation_central.json")
                
                logs.append("File permissions set for foundation_central.json")
            
            else:
                logs.append(f"API Key: '{foundation_central.get('api_key')}'")
                logs.append(f"FC IP: '{foundation_central.get('fc_ip')}'")
                completed = True
                logs.append("Proceeding...")

        # Adjust heartbeat settings
        heartbeat = poll = register = 5
        
        logs.append(f"Setting intervals to '{heartbeat}' minutes")
        
        # Backup foundation_settings.json if it exists
        ssh.exec_command(f"sudo cp /home/<USER>/foundation/config/foundation_settings.json /etc/nutanix/foundation_settings.bak-{cycle} 2>/dev/null || true")
        
        # Check if foundation_settings.json exists and modify it
        stdout = ssh.exec_command("sudo test -f /home/<USER>/foundation/config/foundation_settings.json && echo 'exists' || echo 'not exists'")
        if stdout.decode().strip() == 'exists':
            stdout = ssh.exec_command("sudo cat /home/<USER>/foundation/config/foundation_settings.json")
            if stdout:
                settings = json.loads(stdout)
                
                if 'fc_settings' not in settings:
                    settings['fc_settings'] = {}
                
                settings['fc_settings']['heartbeat_interval_mins'] = heartbeat
                settings['fc_settings']['intent_poll_interval_mins'] = poll
                settings['fc_settings']['register_interval_mins'] = register
                
                ssh.exec_command(f"echo '{json.dumps(settings, indent=2)}' | sudo tee /home/<USER>/foundation/config/foundation_settings.json")
                
                # Set file permissions for foundation_settings.json
                ssh.exec_command("sudo chmod 644 /home/<USER>/foundation/config/foundation_settings.json")
                # ssh.exec_command("sudo chown root:root /home/<USER>/foundation/config/foundation_settings.json")
                
                logs.append("File permissions set for foundation_settings.json")
            else:
                logs.append("Error reading foundation_settings.json")
        else:
            logs.append("foundation_settings.json does not exist, skipping heartbeat settings update")

    def update_resolvconf(self, ssh, logs, dnsserver):
        logs.append("Update resolv.conf") 
        logs.append("Change attr 'i' first, will change it back after we update dns.")
        command = f'sudo chattr -i /etc/resolv.conf;sudo sh -c "echo nameserver {dnsserver} > /etc/resolv.conf";sudo chattr +i /etc/resolv.conf'
        res, _ = ssh.connect()
        if not res:
            raise InternalServerError("connect to cvm failed.")
        stdout = ssh.exec_command(command, wait=10)
        if stdout is False:
            logs.append("Failed to update resolv.conf")
            raise InternalServerError("Failed to execute command.")
        logs.append("Successfully updated resolv.conf")
        logs.append(f"Command output: {stdout}")

    def stage6_deploy_cluster(self):
        """
        Required inputs:
        -
        Properties stored into database after this stage:
        - pe_ip (for monitor)
        """
        self.ilg.write("Deploy cluster", severity="title")
        pc_fqdn = self.task.pc_fqdn
        vault = Vault(tier=self.tier)
        central_pe = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('central_pe')
        _res, pc_data = vault.get_secret(f"{central_pe}/Site_Pc_Svc")
        self.rest_fc = FoundationCentral(endpoint=pc_fqdn, username=pc_data['username'], password=pc_data['secret'], logger=self.logger)
        self.ahv_cvm_subnet = self.rest_ipam.get_subnets_by_subnet_id(self.task.ahv_cvm_subnet_id).json()[0]
        self.ahv_cvm_gw = Ipam.get_value_from_class_parameters("gateway", self.ahv_cvm_subnet["subnet_class_parameters"])
        self.oob_subnet = self.rest_ipam.get_subnets_by_subnet_id(self.task.oob_subnet_id).json()[0]
        self.oob_gw = Ipam.get_value_from_class_parameters("gateway", self.oob_subnet["subnet_class_parameters"])
        self.ahv_cvm_oob_mapping = json.loads(self.task.ahv_cvm_oob_mapping)
        self.ahv_dhcp_list = [_["dhcp"]["ahv"] for _ in self.ahv_cvm_oob_mapping]
        self.cvm_dhcp_list = [_["dhcp"]["cvm"] for _ in self.ahv_cvm_oob_mapping]
        self.selected_oob = self.task.selected_oob.split(",")  # in db, it's stored as a string
        nodes = self._wait_ready_nodes()
        payload = self._prepare_create_cluster_payload(nodes)
        self._assign_static_ips_on_ipam()
        self.set_task_property(ClusterSpec.STEP, "4")   # Create cluster step
        imaged_cluster_uuid = self.rest_fc.create_cluster(payload).json()["imaged_cluster_uuid"]
        self.set_task_property(ClusterSpec.CLUSTER_UUID, imaged_cluster_uuid)
        self.check_cluster_status()

    @update_step()
    def _prepare_create_cluster_payload(self, node_list):
        # prepare network info
        ahv_cvm_dns = [
            Ipam.get_value_from_class_parameters("ikea_infra_dns_1", self.ahv_cvm_subnet["subnet_class_parameters"]),
            Ipam.get_value_from_class_parameters("ikea_infra_dns_2", self.ahv_cvm_subnet["subnet_class_parameters"])
        ]
        ahv_cvm_ntp = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get("systems").get('ntp_servers')
        ahv_cvm_netmask = Ipam.calculate_netmask(self.ahv_cvm_subnet)
        oob_netmask = Ipam.calculate_netmask(self.oob_subnet)

        darksite_url = self.model_pc.query.filter_by(fqdn=self.task.pc_fqdn).first().darksite
        aos_initial_payload = Benchmark().get_bmk_by_id(self.task.benchmark_id).get('lcm_version').get('aos_binary')
        ahv_binary = Benchmark().get_bmk_by_id(self.task.benchmark_id).get('lcm_version').get('ahv_binary')
        aos_version = Benchmark().get_bmk_by_id(self.task.benchmark_id).get('lcm_version').get('aos_version')
        aos_package_url = f"http://{darksite_url}/1CN/{aos_initial_payload}"
        base_payload = {
            "cluster_name": self.task.pe_name,
            "common_network_settings": {
                "cvm_dns_servers": ahv_cvm_dns,
                "cvm_ntp_servers": ahv_cvm_ntp,
                "hypervisor_dns_servers": ahv_cvm_dns
            },
            "redundancy_factor": 2,
            "nodes_list": [],
            "cluster_external_ip": self.task.pe_ip,
            "aos_package_url": aos_package_url
            }
        if aos_version:
            major_version, minor_version = map(int, aos_version.split('.')[:2])
            if major_version > 6 or (major_version == 6 and minor_version >= 10):
                hypervisor_iso_url = f"http://{darksite_url}/1CN/{ahv_binary}"
                base_payload["hypervisor_isos"] = [
                    {
                        "hypervisor_type": "kvm",
                        "url": hypervisor_iso_url
                    }
                ]
        # prepare nodes_list
        node_object = []
        index = 0
        for node in node_list:
            if node["hypervisor_gateway"] != self.ahv_cvm_gw:
                self.logger.info(f"CVM {node['cvm_ip']} does not belong to this site.")
                continue
            current_mapping = self.ahv_cvm_oob_mapping[index]
            node_config = {
                # TODO: check warehouse payload
                "cvm_gateway": self.ahv_cvm_gw,
                "cvm_netmask": ahv_cvm_netmask,
                "cvm_ip": current_mapping["static"]["cvm"]["ip"],                # static
                "hypervisor_gateway": self.ahv_cvm_gw,
                "hypervisor_netmask": ahv_cvm_netmask,
                "hypervisor_ip": current_mapping["static"]["ahv"]["ip"],         # static
                "hypervisor_hostname": current_mapping["static"]["ahv"]["hostname"].upper(),
                "imaged_node_uuid": node["imaged_node_uuid"],
                "use_existing_network_settings": False,
                "image_now": len(self.selected_oob) != 1,
                "hypervisor_type": "kvm",
                "cvm_ram_gb": Benchmark().get_bmk_by_id(self.task.benchmark_id).get('deployment').get('cvm_ram_gb'),
                "ipmi_gateway": self.oob_gw,
                "ipmi_netmask": oob_netmask,
                "ipmi_ip": current_mapping["dhcp"]["oob"]           # dhcp
            }
            node_object.append(node_config)
            index += 1
        base_payload["nodes_list"] = node_object
        return base_payload

    @update_step(step=1)
    def _wait_ready_nodes(self):
        for _i in range(10):
            imaged_nodes = self.rest_fc.list_imaged_nodes().json()["imaged_nodes"]
            ready_nodes = []
            for node in imaged_nodes:
                if node["available"] \
                        and ((node["cvm_gateway"] == self.ahv_cvm_gw and node["cvm_ip"] in self.cvm_dhcp_list) \
                        or (node["hypervisor_gateway"] == self.ahv_cvm_gw and node["hypervisor_ip"] in self.ahv_dhcp_list)):
                    ready_nodes.append(node)
            if len(ready_nodes) == len(self.selected_oob):
                self.ilg.write(f"We have {len(ready_nodes)} Nodes Ready on the SDWAN Network {len(self.selected_oob)}")
                return ready_nodes
            self.ilg.write(
                "Please check if CVMips in Cluster match the CVM ips reported into foundation central",
                severity="warning")
            time.sleep(30)
        raise FCNodesNotReady()

    @update_step()
    def _assign_static_ips_on_ipam(self):
        domain = Benchmark().get_bmk_by_id(self.task.benchmark_id).get('systems').get('ldap_domain')
        fqdn = f"{self.task.pe_name.lower()}.{domain}"
        r = self.rest_ipam.ip_add(
            self.task.pe_ip, self.ahv_cvm_subnet["site_id"], fqdn, self.task.pe_name
        )
        if r.status_code == 400:
            if not self.payload.get(ClusterSpec.RESUME):
                raise BadRequest(f"Assign {self.task.pe_ip} to IPAM failed, reason: {r.json()}")
            self.ilg.write(f"{self.task.pe_ip} already assigned with {fqdn}, skipping assignment.", severity="warning")
        number = self.task.pe_name.split('-')[1].split('.')[0].lower().split('nxc00')[1]    # nxc00?
        ds_fqdn = f"{self.task.pe_name.lower().split('-')[0]}-nxd00{number}.{domain}"
        r = self.rest_ipam.ip_add(
            self.task.data_service_ip, self.ahv_cvm_subnet["site_id"], ds_fqdn, self.task.pe_name
        )
        if r.status_code == 400:
            if not self.payload.get(ClusterSpec.RESUME):
                raise BadRequest(f"Assign {self.task.data_service_ip} to IPAM failed, reason: {r.json()}")
            self.ilg.write(
                f"{self.task.data_service_ip} already assigned with {ds_fqdn}, skipping assignment.", "warning"
            )
        for m in self.ahv_cvm_oob_mapping:
            for _, info in m["static"].items():
                site_id = self.oob_subnet["site_id"] if "oob" in info["hostname"].lower() else self.ahv_cvm_subnet["site_id"]
                hostname = f"{info['hostname'].lower()}.{domain}"
                self.rest_ipam.ip_add(info["ip"], site_id, hostname, self.task.pe_name)
                if r.status_code == 400:
                    if not self.payload.get(ClusterSpec.RESUME):
                        raise BadRequest(f"Assign {info['ip']} to IPAM failed, reason: {r.json()}")
                    self.ilg.write(f"{info['ip']} already assigned with {hostname}, skipping assignment.", "warning")

    @update_step(step=5)
    def check_cluster_status(self, max_checks=36):
        def _check_nodes_status():
            if not cluster["cluster_status"].get("node_progress_details"):
                self.ilg.write("Node progress details is not shown yet...")
                required_percent.append(None)
                return
            for node in cluster["cluster_status"]["node_progress_details"]:
                node_uuid = node['imaged_node_uuid']
                self.ilg.write(f"Node {node_uuid} progress: {node['percent_complete']}%, status: {node['status']}")
                if "fatal" in node['status'].lower() or 'error' in node['status'].lower():
                    self.logger.warning(f"Cluster deployment failed due to node {node_uuid} status: {node['status']}")
                    self.set_task_property(ClusterSpec.STAGE_STATUS, TaskStatus.ERROR)
                    raise FCDeployClusterFailed()
                required_percent.append(node['percent_complete'])

        self.ilg.write("Checking cluster status", severity="title")
        for _ in range(max_checks):
            cluster = self.rest_fc.get_cluster_detail(self.task.cluster_uuid).json()
            required_percent = []
            cluster_percent = cluster["cluster_status"].get("aggregate_percent_complete")
            if not cluster_percent:
                self.ilg.write("Aggregate complete percentage is not shown yet...")
            else:
                self.ilg.write(f"Overall Deployment status: {cluster_percent}%")
            required_percent.append(cluster_percent)
            cluster_progress_details = cluster["cluster_status"].get("cluster_progress_details")
            config_percent = None
            if not cluster_progress_details:
                self.ilg.write("Cluster progress details is not shown yet...")
            else:
                if not cluster_progress_details.get("status"):
                    self.ilg.write("Cluster status is not shown yet...")
                else:
                    status = cluster_progress_details.get('status')
                    self.ilg.write(f"Current state: {status}")
                if not cluster_progress_details.get("percent_complete"):
                    self.ilg.write("Cluster config percentage is not shown yet...")
                else:
                    config_percent = cluster_progress_details.get("percent_complete")
                    self.ilg.write(f"Cluster config percentage: {config_percent}%")
            required_percent.append(config_percent)
            _check_nodes_status()
            if all([x == 100 for x in required_percent]):
                self.ilg.write("CLUSTER DEPLOYMENT COMPLETED")
                self.set_task_property(ClusterSpec.STAGE_STATUS, TaskStatus.DONE)
                self.logger.info("Cluster deployment completed.")
                self.set_task_status(TaskStatus.DONE)
                return
            self.ilg.write("Cluster deployment still ongoing... Take your time.")
            next_check_time = datetime.now() + timedelta(minutes=20)
            self.ilg.write(f"Next status check will be at {next_check_time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.set_task_property(ClusterSpec.STAGE_STATUS, NewClusterTaskStatus.DEPLOYING_CLUSTER)
            time.sleep(60 * 20)
        raise ClusterCreationTimeout()

    def stage7_configure_cluster(self):
        """
        step:
        1 reset ntx admin password
        2 create service account
        3 auth config
        4 config eula
        5 join the cluster to pc
        6 update pe data service ip
        7 create certificate for PE
        8 config darksite
        9 config storage container
        10 config login screen
        """
        vault = Vault(tier=self.tier)
        self.reset_ntx_admin_password_factory(vault)
        self.create_service_account(vault)
        self.auth_config(vault)
        self.config_eula()
        self.pc_deploy_join()
        self.update_pe_data_service_ip(vault)
        self.config_darksite()
        self.configure_storage_container()
        self.rest_update_px_logon_banner(vault)

    @update_step
    #step 2 auth config
    def auth_config(self, vault):
        try:
            self.ilg.write("Starting Authentication Configuration", severity="title")
            domain = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('ldap_domain')
            pe_fqdn = f"{self.task.pe_name}.{domain}"
            res, password = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Admin")
            sa = {"username": password["username"], "password": password["secret"]}
            atm = Automation(pc=self.task.pc_fqdn, pe=pe_fqdn, sa=sa, logger=self.logger)
            res, _ = atm.reauth_pe_ad()
            if not res:
                self.ilg.write(f"Warning: Failed to re-auth AD for {pe_fqdn}", severity="warning")
                return
            self.ilg.write("AD re-authentication successful, configuring role mapping...")
            dsc = DscAuthConfig(pe=pe_fqdn, logger=self.logger, db_logger=self.db_logger, facility_type=self.facility_type)
            dsc.configure_role_mapping()
            self.ilg.write("Authentication configuration completed successfully")
        except Exception as e:
            self.ilg.write(f"Warning: Auth config encountered an error: {str(e)}", severity="warning")
            self.ilg.write('Main process will continue, please contact administrator to check re-authentication')

    @update_step
    #step 5 join the cluster to pc
    def pc_deploy_join(self):
        self.ilg.write("start join the cluster to pc", severity="title")
        res = self.get_pe_multicluster()
        if res:
            self.ilg.write("Cluster is already joined")
        else:
            self.ilg.write("Let's join the cluster to pc")
            self.join_cluster_to_pc()
            res = self.get_pe_multicluster()
            if res:
                self.ilg.write("Cluster join to PC completed")
            else:
                raise JoinClusterToPCFailed()

    @update_step
    #step 8 config darksite
    def config_darksite(self):
        self.ilg.write("check current config first")
        pc_instance = self.model_pc.query.filter_by(fqdn=self.task.pc_fqdn).first()
        domain = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('ldap_domain')
        pe_fqdn = f"{self.task.pe_name}.{domain}"
        darksite = pc_instance.darksite
        vault = Vault(tier=self.tier)
        _, data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Admin")
        sa = {"username": data['username'], "password": data['secret']}
        p_e = Prism(fqdn=pe_fqdn, sa=sa, logger=self.logger)
        lcm_config = p_e.get_lcm_config()
        if not lcm_config['is_darksite']:
            p_e.set_lcm_darksite(darksite_url=f"http://{darksite}/release")
        else:
            self.ilg.write("Darksite is already configured.")

    def get_pe_multicluster(self):
        self.ilg.write("check current status first")
        vault = Vault(tier=self.tier)
        _res, data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Admin")
        rest_fc = FoundationCentral(endpoint=self.task.pe_ip, username=data['username'], password=data['secret'], logger=self.logger)
        return rest_fc.get_pe_multicluster()

    def join_cluster_to_pc(self):
        self.ilg.write("Adding Multi Cluster State", severity="title")
        vault = Vault(tier=self.tier)
        central_pe = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('central_pe')
        _res, pc_data = vault.get_secret(f"{central_pe}/Site_Pc_Admin")
        _res, data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Svc")
        rest_fc = FoundationCentral(endpoint=self.task.pe_ip, username=data['username'], password=data['secret'], logger=self.logger)
        pc_ip = Benchmark().get_bmk_by_id(self.task.benchmark_id).get("systems").get("pc_cluster_ip")
        rest_fc.join_cluster_to_pc(pc_data=pc_data, pc_ip=pc_ip)
        self.ilg.write("Waiting for 2 minutes for the cluster to be joined to PC")
        time.sleep(120)

    @update_step(step=1)
    #step 1 reset ntx admin password
    def reset_ntx_admin_password_factory(self, vault):
        self.ilg.write("Reset Admin Password For PE", severity="title")
        _res, data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Nutanix")
        _newpe = self.model_pe.query.filter_by(name=self.task.pe_name).first()
        if not _newpe:
            domain = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('ldap_domain')
            _newpe = self.model_pe(fqdn=f"{self.task.pe_name}.{domain}".lower(), name=self.task.pe_name, prism=self.task.pc_fqdn, bmk_id=self.task.benchmark_id)
            db.session.add(_newpe)
            db.session.commit()
        self._init_password_rotator()
        new_pass = self.password_rotator.generate_newpassword()
        self.password_rotator.reset_passwd_cvm(
            ssh_ip=self.task.pe_ip, 
            ssh_user=data['username'], 
            ssh_pass=data['secret'], 
            target_user="admin", 
            new_pass=new_pass, 
            public_key=None
        )
        vault.set_vault_password(
            username="admin", 
            password=new_pass, 
            label=f"{self.task.pe_name.upper()}/Site_Pe_Admin", 
            description="admin password for Nutanix cluster"
        )

    @update_step
    #step 3 create service account
    def create_service_account(self, vault):
        self.ilg.write("Create Service Account for PE", severity="title")
        res, data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Nutanix")
        svc = ServiceAccount(usage=(Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('vault').get('new_cluster_labels').get('Site_Pe_Svc'))).get_service_account()['username'] #DB中SVC label对应的value即为Username
        self._init_password_rotator()
        new_pass = self.password_rotator.generate_newpassword()
        ssh = SSHConnect(host=self.task.pe_ip, username=data['username'], password=data['secret'], logger=self.logger)
        res, _ = ssh.connect()
        if not res:
            raise ConnectToPEFailed(self.task.pe_name)
        command = "/home/<USER>/prism/cli/ncli user list --json=pretty"
        stdout = ssh.exec_command(command, wait=10)
        stdout = json.loads(stdout)
        if not stdout['data'][0]['profile']['username']:
            raise GetUserListFromCVMFailed()
        users_info = []
        for user in stdout['data']:
            users_info.append(user['profile']['username'])
        if svc not in users_info:
            self.ilg.write("svc user not found, creating svc user")
            command = f"/home/<USER>/prism/cli/ncli user create user-name={svc} user-password='{new_pass}' first-name=\"Service\" last-name=\"Account\" email-id='<EMAIL>' --json=pretty"
            stdout = ssh.exec_command(command, wait=10)
            stdout = json.loads(stdout)
            if stdout['status'] != 0:
                raise CreateSvcUserFailed()
            self.ilg.write("svc user created")
            vault.set_vault_password(username=svc, password=new_pass, label=f"{self.task.pe_name.upper()}/Site_Pe_Svc", description="SVC password for Nutanix cluster")
        self.ilg.write('svc user existed')
        # Start checking roles and create roles
        self.ilg.write('Checking Roles')
        command = "/home/<USER>/prism/cli/ncli user list --json=pretty"
        stdout = ssh.exec_command(command, wait=10)
        stdout = json.loads(stdout)
        svc_role = None
        for user in stdout['data']:
            if user['profile']['username'] == svc:
                svc_role = user['roles']
                break
        if svc_role is None or ("ROLE_CLUSTER_ADMIN" not in svc_role) or ("ROLE_USER_ADMIN" not in svc_role):
            self.ilg.write('svc user role not found, creating svc user role')
            self.ilg.write(f'Updating Groups for user: {svc} granting ROLE_CLUSTER_ADMIN using NCLI')
            command = f"/home/<USER>/prism/cli/ncli user grant-cluster-admin-role user-name={svc} --json=pretty"
            stdout = ssh.exec_command(command, wait=10)
            stdout = json.loads(stdout)
            if stdout['status'] != 0:
                raise GrantClusterAdminRoleFailed()
            self.ilg.write(f'Updating Groups for user: {svc} granting ROLE_USER_ADMIN using NCLI')
            command = f"/home/<USER>/prism/cli/ncli user grant-user-admin-role user-name={svc} --json=pretty"
            stdout = ssh.exec_command(command, wait=10)
            stdout = json.loads(stdout)
            if stdout['status'] != 0:
                raise GrantUserAdminRoleFailed()
        self.ilg.write('Service Account Already has the correct privileges.')

    @update_step
    #step 9 config storage container
    def configure_storage_container(self):
        self.ilg.write("Configuring storage containers...")
        domain = Benchmark().get_bmk_by_id(self.task.benchmark_id).get('systems').get('ldap_domain')
        sc = DscStorageConfig(f"{self.task.pe_name}.{domain}", self.logger, self.db_logger, self.facility_type)
        containers = sc.rest_sc.list_storage_containers()["entities"]
        sc.configure_compression(containers)

    @update_step
    #step 4 config eula
    def config_eula(self):
        domain = Benchmark().get_bmk_by_id(self.task.benchmark_id).get('systems').get('ldap_domain')
        pl = DscPulse(pe=f"{self.task.pe_name}.{domain}", logger=self.logger, db_logger=self.db_logger, facility_type=self.facility_type)
        pl.configure_eula()
        pl.configure_pulse()
    

    @update_step
    #step 6 update pe data service ip
    def update_pe_data_service_ip(self, vault):
        self.ilg.write("Update data service IP", severity="title")
        _res, data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Admin")  # TODO
        sa = {"username": data["username"], "password": data["secret"]}
        rest = RestCluster(pe=self.task.pe_name, logger=self.logger, sa=sa)
        rest.update_cluster({
            "clusterUuid": self.task.cluster_uuid,
            "genericDTO": {
                "clusterExternalDataServicesIPAddress": self.task.data_service_ip,
            },
            "operation": "EDIT"
        })
        self.ilg.write("Update data service IP: Done")

    @update_step
    def rest_update_px_logon_banner(self, vault, px_mode='Pe', fade_in_html_color="#FFF333", fade_out_html_color="#3358FF") -> None:
        """
        Changes the Prism Logon Banner. Update or Add.
        """
        name = self.task.pe_name
        domain = Benchmark().get_bmk_by_id(self.task.benchmark_id).get('systems').get('ldap_domain')
        central_pe = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('central_pe')
        _res, data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Admin")
        _res, pc_data = vault.get_secret(f"{central_pe}/Site_Pc_Admin")
        self.rest_pe = NutanixAPI(pe=self.task.pe_name, username=data['username'], password=data['secret'], logger=self.logger)
        self.rest_pc = NutanixAPI(pe=self.task.pc_fqdn, username=pc_data['username'], password=pc_data['secret'], logger=self.logger)
        
        self.ilg.write("Pulling Current UI Customization")
        res, mes = self.rest_pe.call_pe_get(request_url='/application/system_data')
        if not res:
            raise FailedToGetCurrentUIConfiguration()
        pattern = r'CUSTOM[_\s]*LOGIN[_\s]*SCREEN'
        already_applied = any(re.search(pattern, str(item.get('type', '')), re.IGNORECASE) for item in mes)
        method = "PUT" if already_applied else "POST"
        self.ilg.write(f"Login screen configuration {'already exists' if already_applied else 'not found'}, using {method} mode")
        if px_mode == "Pc":
            login_screen_title = f"Use UPN based {domain} credentails"
            login_screen_product_title = "Self - Service - IKEA"
        else:
            login_screen_title = f"Use UPN based {domain} credentails"
            login_screen_product_title = f"PE - {name}"

        payload = [
            {
                "type": "custom_login_screen",
                "key": "title",
                "value": login_screen_title
            },
            {
                "type": "custom_login_screen",
                "key": "product_title",
                "value": login_screen_product_title
            },
            {
                "type": "UI_CONFIG",
                "username": "system_data",
                "key": "disable_2048",
                "value": True
            },
            {
                "type": "UI_CONFIG",
                "key": "autoLogoutGlobal",
                "value": 900000
            }
        ]

        # Add fade in color if specified
        if fade_in_html_color and not any(x in fade_in_html_color.upper() for x in ["NONE", "NA", "N/A", "DEFAULT"]):
            payload.append({
                "type": "custom_login_screen",
                "key": "color_in",
                "value": fade_in_html_color
            })

        # Add fade out color if specified
        if fade_out_html_color and not any(x in fade_out_html_color.upper() for x in ["NONE", "NA", "N/A", "DEFAULT"]):
            payload.append({
                "type": "custom_login_screen",
                "key": "color_out",
                "value": fade_out_html_color
            })

        for item in payload:
            self.ilg.write(f"Applying UI Customization '{item['key']}' using value '{item['value']}'")
            if method == "PUT":
                res, mes = self.rest_pe.call_pe_put(request_url="/application/system_data", payload=item)
            else:
                res, mes = self.rest_pe.call_pe_post(request_url="/application/system_data", payload=item)
            if not res:
                raise FailedToUpdateUIConfiguration(mes)

    @update_step(step=1)
    def stage8_rotate_password(self):
        self.ilg.write("Rotate password for PE", severity="title")
        _newpe = self.model_pe.query.filter_by(name=self.task.pe_name).first()
        if not _newpe:
            domain = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('ldap_domain')
            _newpe = self.model_pe(fqdn=f"{self.task.pe_name}.{domain}", name=self.task.pe_name, prism=self.task.pc_fqdn)
            db.session.add(_newpe)
            db.session.commit()
        self._init_password_rotator()
        self.password_rotator.rotate_process()

    def stage9_configure_oob(self):
        self.ilg.write("Configure OOB", severity="title")
        domain = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('ldap_domain')
        vault = Vault(tier=self.tier)
        res, data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Admin")
        sa = {"username": data['username'],  "password": data['secret']}
        prism = Prism(self.task.pe_name, sa=sa, domain=domain, logger=self.logger)
        _, oob_data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Oob")
        ilo_user_name, ilo_password = oob_data['username'], oob_data['secret']
        res, pe_data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Pe_Nutanix")
        oneview_endpoint = ModelRetailNutanixOneview.query.filter_by(region = self.model_pc.query.filter_by(fqdn=self.task.pc_fqdn).first().oneview_region).first().fqdn
        oneview = OneView(
            self.task.pc_fqdn, self.task.pe_name,
            Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('vault').get('dc_labels').get('hpe_oneview'),
            facility_type=self.facility_type, vault=vault
        )
        self.ilg.write(f"We are using {oneview_endpoint} to configure OOB")
        self.ilg.write("Get oneview scope and servers")
        oneview_scope = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('oneview_scope')
        res, myscope = oneview.get_oneview_scopes(oneview_scope)
        self.ilg.write(f"oneview_scope is {oneview_scope}, myscope is {myscope}")
        oneview_servers = oneview.get_oneview_servers_hardware()
        serial_to_server = {server['attributes']['serialNumber']: server for server in oneview_servers}
        hosts = sorted(prism.get_host().json().get('entities'), key=lambda host: host['ipmiAddress'])
        _res, ahv_data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Ahv_Root")
        oob_subnet = self.rest_ipam.get_subnets_by_subnet_id(self.task.oob_subnet_id).json()[0]
        oob_gw = Ipam.get_value_from_class_parameters("gateway", oob_subnet["subnet_class_parameters"])
        oob_dns = [
            Ipam.get_value_from_class_parameters("ikea_dns_server", oob_subnet["subnet_class_parameters"]),
            Ipam.get_value_from_class_parameters("ikea_dns_server_2", oob_subnet["subnet_class_parameters"])
        ]
        oob_netmask = Ipam.calculate_netmask(oob_subnet)
        bmk_res = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id)
        pe_fqdn = self.model_pe.query.filter_by(name=self.task.pe_name).first().fqdn
        renew_cert = RenewCertificates(
            pe=pe_fqdn,
            facility_type=self.facility_type
        )
        renew_cert.logger = self.logger
        self.ilg.write("Config OOB one by one")
        self.set_task_property(ClusterSpec.STEP, "1")     # step index, config oob 1by1
        for host in hosts:
            self.ilg.write(f"Configuring OOB {host['name']}", severity="title")
            for _ in range(2):
                try:
                    oob_newip = self.config_oob_ip(host, oob_gw, oob_netmask)
                except Exception as e:
                    self.ilg.write(f"Error occurred: {e}. Retrying config_oob_ip...")
                    continue
                try:
                    self.config_single_ilo(host, oob_newip, ilo_user_name, ilo_password, bmk_res, oob_dns, domain)
                    break
                except Exception as e:
                    ssh = SSHConnect(host['hypervisorAddress'], ahv_data['username'], ahv_data['secret'], logger=self.logger)
                    res, _ = ssh.connect()
                    if not res:
                        raise ConnectToSourceFailed({host['hypervisorAddress']})
                    ssh.exec_command("ipmitool bmc reset cold")
                    self.ilg.write("take a 1 minutes power nap.")
                    time.sleep(60)
                    self.logger.error(f"Error configuring OOB for host {host['name']}: {e}")
                    self.logger.warning("but it will not affect other hosts, continue to next host...")
                    continue

        ssh = SSHConnect(host=self.task.pe_ip, username=pe_data['username'], password=pe_data['secret'])
        res, _ = ssh.connect()
        if not res:
            raise SSHFailed(self.task.pe_ip)
        ssh.invoke_shell()
        self.ilg.write("Let's restart the genesis service, this will take a while...")
        ssh.send_command('cluster restart_genesis')
        time.sleep(100)
        self.ilg.write("Back from restart genesis, let's start to renew certificate.")
        hosts = sorted(prism.get_host().json().get('entities'), key=lambda host: host['ipmiAddress'])
        self.set_task_property(ClusterSpec.STEP, "2") #renew cert
        renew_cert.renew_certificate()
        self.ilg.write("Start adding hosts to OneView", severity="title")
        self.set_task_property(ClusterSpec.STEP, "3") #start add oneview
        for host in hosts:
            self.ilg.write(f"Adding {host['name']} to OneView", severity="title")
            try:
                self.add_oob_to_oneview(host, f"{host['name']}OOB", 
                    host['ipmiAddress'],
                    ilo_user_name, 
                    ilo_password,  
                    oneview, 
                    myscope, 
                    serial_to_server, 
                    domain
                )
                self.ilg.write(f"Successfully added {host['name']} to OneView")
            except Exception as e:
                self.logger.error(f"Failed to add {host['name']} to OneView: {e}")
                self.logger.warning("but it will not affect other hosts, continue to next host...")
                continue 

    def config_oob_ip(self, host, oob_gw, oob_netmask):
        # Configure static IP
        oob_name = f"{host['name']}OOB"
        oob_ip = host['ipmiAddress']
        self.ilg.write(f"Start config static IP for {oob_name}.")
        oob_ips = json.loads(self.task.ahv_cvm_oob_mapping)
        for data in oob_ips:
            if oob_name.upper() == data['static']['oob']['hostname'].upper():
                if oob_ip == data['static']['oob']['ip']:
                    return oob_ip
                self.ilg.write(f"Configure OOB Static IP for {oob_name}", severity="title")
                self.config_oob_static_ip_via_ahv(host['hypervisorAddress'], data['static']['oob']['ip'], oob_netmask, oob_gw)
                for _ in range(6):
                    if ping(data['static']['oob']['ip']):
                        break
                    time.sleep(20)
                else:
                    self.ilg.write(f"Failed to ping OOB IP: {data['static']['oob']['ip']} after 2mins .")
                return data['static']['oob']['ip']

    def config_single_ilo(self, host, oob_newip, ilo_user_name, ilo_password, bmk_res, oob_dns, domain):
        vault = Vault(tier=self.tier)
        oob_name = f"{host['name']}OOB"
        #Set server name and DNS name
        self.ilg.write(f"Setting server name and DNS name for {oob_name}", severity="title")
        redfish = Redfish(oob_newip, ilo_user_name, ilo_password, logger=self.logger)
        current_servername = redfish.get_ilo_hostname()
        if not current_servername or current_servername != oob_name:
            redfish.set_ilo_servername(oob_name)
        current_ilo_dnsname = redfish.get_ilo_dnsname()
        if not current_ilo_dnsname or current_ilo_dnsname != oob_name:
            redfish.update_ilo_dnsname(oob_name)

        # Check if LDAP group exists
        res, data = redfish.get_ilo_object(request_url="AccountService")
        remote_group = bmk_res.get('group_mapping').get('pe_site_admin_group')
        if res and data['LDAP']['RemoteRoleMapping'][0]['RemoteGroup'] == remote_group:
            self.ilg.write("Group Already exists.")
        else:
            self.ilg.write("Ilo Group is missing, adding construct.")
            redfish.setad_create_group(remote_group)

        # Check LDAP binding and set if necessary
        ad_query_label = bmk_res.get('vault').get('dc_labels').get('ad_query_upn')
        res, vault_data = vault.get_secret(f"{ad_query_label}")
        ilo_join_username = vault_data['username'].split('@')[0]
        if (
            res and data['LDAP']['ServiceAddresses'][0] != domain or 
            data['LDAP']['ServiceEnabled'] != True or 
            data['LDAP']['Authentication']['Username'] != ilo_join_username
        ):
            res, sec = vault.get_secret(bmk_res.get('vault').get('dc_labels').get('ad_query'))
            redfish.set_ad_ldap(sec, domain)
        else:
            self.logger.info("LDAP Binding already in desired state.")

        # Set AD roles if necessary
        role_id = redfish.get_role_id(remote_group_to_find=remote_group)
        _res, data = redfish.get_ilo_object(request_url=f"AccountService/Roles/{role_id}")

        desired_oem_priv = [
            "RemoteConsolePriv", "VirtualMediaPriv", "VirtualPowerAndResetPriv", 
            "HostBIOSConfigPriv", "HostNICConfigPriv", "HostStorageConfigPriv"
        ]
        desired_assigned_priv = ["Login", "ConfigureSelf", "ConfigureManager", "ConfigureUsers"]

        compare_oem = set(data["OemPrivileges"]) != set(desired_oem_priv)
        compare_assigned = set(data["AssignedPrivileges"]) != set(desired_assigned_priv)

        if compare_oem or compare_assigned:
            redfish.set_ad_roles(roleid=role_id)
        else:
            self.ilg.write("Privileges are already correct.")

        # Configure ilo DNS
        current_ilo_dns = redfish.get_ilo_dns()
        current_ilo_dns_set = set(current_ilo_dns)
        # Check if any element in static_name_servers is not in current_ilo_dns
        if any(server not in current_ilo_dns_set for server in oob_dns):
            redfish.set_ilo_dns(oob_dns)
        else:
            self.ilg.write("DNS is already correct.")
        redfish.ilo_reset()
        self.ilg.write("Start ILO reset, Waiting for OOB to come back up.")
        time.sleep(60)

    def add_oob_to_oneview(self, host, oob_name, oob_newip, ilo_user_name, ilo_password, oneview, myscope, serial_to_server, domain):
        self.ilg.write(f"Starting OneView part for {oob_name}", severity="title")
        server = serial_to_server.get(host['serial'])

        if server:
            if server.get('scopeUris') and server['scopeUris'][0] and server['scopeUris'][0] != myscope:
                self.ilg.write("This server is not inside my scope, attempting move.")
            elif server['name'].upper() != oob_name.upper()+"."+domain.upper():
                self.ilg.write("This server name is not matching a server listed inside my scope.")
            else:
                self.ilg.write("This server is already inside my scope!")
                return

            remove = True
            add = True
        else:
            self.ilg.write("All Good and Fresh, lets add this server.")
            add = True
            remove = False
        if remove:
            self.ilg.write("Lets remove this beast first.")
            oneview.remove_oneview_server(server['attributes']['uuid'])
            self.ilg.write("Waiting 120 seconds for OneView to catch up.")
            time.sleep(120)
        if add:
            max_retries = 3
            for retry in range(max_retries):
                oneview.add_oneview_server(oob_newip, myscope, ilo_user_name, ilo_password)
                self.ilg.write(f"Attempt {retry + 1}: {oob_name} added to OneView, waiting 120 seconds to check the result.")
                time.sleep(120)
                redfish = Redfish(oob_newip, ilo_user_name, ilo_password, logger=self.logger)
                _res, ilo_data = redfish.get_ilo_state()
                res = oneview.get_server_from_oneview_by_uuid(ilo_data['UUID'])
                if res and res.ok:
                    self.ilg.write(f"added {oob_name} to OneView successfully.")
                    break
                else:
                    if retry < max_retries - 1:
                        self.ilg.write(f"Attempt {retry + 1} failed to add {oob_name} to OneView, retrying...", severity="warning")
                    else:
                        self.ilg.write(f"All attempts to add {oob_name} to OneView failed, please check the logs.", severity="error")

    def config_oob_static_ip_via_ahv(self, host, ip, ipmimask, ipmigw):
        vault = Vault(tier=self.tier)
        _res, data = vault.get_secret(f"{self.task.pe_name.upper()}/Site_Ahv_Root")
        ssh = SSHConnect(host=host, username=data['username'], password=data['secret'], logger=self.logger)
        res, _ = ssh.connect()
        if not res:
            raise ConnectToSourceFailed(host)
        self.ilg.write("Setting Correct IPMI Mode:'static'")
        ssh.exec_command("ipmitool lan set 1 ipsrc static")
        ssh.exec_command(f"ipmitool lan set 1 ipaddr {ip}")
        ssh.exec_command(f"ipmitool lan set 1 netmask {ipmimask}")
        ssh.exec_command(f"ipmitool lan set 1 defgw ipaddr {ipmigw}")
        ssh.exec_command("ipmitool bmc reset cold")
        time.sleep(60)
        self.ilg.write(f"change oob ip to static mode done for host {host}")

    @update_step
    #step 3  #Check if the static IPs obtained from IPAM are already in use
    def check_cluster_ip_addresses_and_name_availability(self):
        def _thread(destination):
            # self.logger.info(f"Checking {destination}...")
            if ping(destination):
                self.logger.warning(f"{destination} is already in use!")
                existing_list.append(destination)
            self.logger.info(f"{destination} is available to use.")

        self.ilg.write("Check Cluster IP Addresses and Name Availability", severity="title")
        existing_list = []
        ahv_cvm_oob_mapping = json.loads(self.task.ahv_cvm_oob_mapping)
        thread_pool = []
        for item in ahv_cvm_oob_mapping:
            for _server, info in item["static"].items():
                for _key, value in info.items():
                    thread = threading.Thread(target=_thread, kwargs={'destination': value})
                    thread.start()
                    thread_pool.append(thread)
        for _t in thread_pool:
            _t.join()
        if existing_list:
            raise IpOrNameAlreadyInUse(existing_list)
        self.ilg.write("Check completed.")

    @update_step
    #step 4 check if the node interface config is correct
    def check_node_interface_config(self):
        self.ilg.write("Check Node Interface Config", severity="title")
        usage = Benchmark().get_bmk_by_id(self.task.benchmark_id)["vault"]["new_cluster_labels"]["Site_Pe_Nutanix"]
        sa = ServiceAccount(usage=usage).get_service_account()
        cluster_network_bmk = Benchmark().get_bmk_by_id(self.task.benchmark_id)["cluster_network"]
        expected_lacp_status = cluster_network_bmk["bond_0_type"] != "Active-Passive"
        expected_interfaces = {
            cluster_network_bmk["bond_0_speed"]: cluster_network_bmk["bond_0_nics"]
        }

        ahv_cvm_oob_mapping = json.loads(self.task.ahv_cvm_oob_mapping)
        cvm_ips = [_["dhcp"]["cvm"] for _ in ahv_cvm_oob_mapping]
        for ip in cvm_ips:
            _conn = SSHConnect(ip, sa["username"], sa["password"], logger=self.logger)
            can_connect, _ssh = _conn.connect()
            if not can_connect:
                raise SSHFailed(ip)
            self.logger.info(f"Checking br0 uplinks for {ip}...")
            self._check_node_interface_config__check_interface_status(ip, _conn, expected_lacp_status)
            self._check_node_interface_config__check_interface_amount_and_speed(ip, _conn, expected_interfaces)
        self.ilg.write("Check completed.")

    def _check_node_interface_config__check_interface_status(self, ip, _conn, expected_lacp_status):
        self.logger.info(f"Checking br0 Lacp for {ip}......")
        stdout = _conn.exec_command(
            "/usr/local/nutanix/cluster/bin/manage_ovs --bridge_name br0 show_uplinks | grep lacp", wait=15
            )  # stdout = b'    lacp: off\n    lacp-fallback: false\n    lacp_speed: slow\n    lacp_status: off\n'
        actual_lacp_status = "off" not in stdout.decode("utf-8")
        self.logger.info(f"Discovered Lacp settings: {actual_lacp_status}, expected: {expected_lacp_status}")
        if actual_lacp_status != expected_lacp_status:
            raise NodeInterfaceLcapSettingWrong()
        self.logger.info("LACP settings are correct!")

    def _check_node_interface_config__check_interface_amount_and_speed(self, ip, _conn, expected_interfaces):
        def _wh_specific_check():
            self.logger.info("Checking interface link for warehouse...")
            linked_bridges = []
            for line in lines:
                name, _mode, link, _speed = line[1], line[2], line[3], line[4]
                if link.lower() == 'true':
                    linked_bridges.append(name)
            if ("eth0" in linked_bridges and "eth1" in linked_bridges) or \
                    ("eth2" in linked_bridges and "eth3" in linked_bridges):
                raise NodeInterfaceLinkConnectedToSameNetworkAdapter(linked_bridges)
            self.logger.info("Warehouse check done.")

        self.logger.info(f"IP: {ip}, checking interface amount and speed...")
        stdout = _conn.exec_command("/usr/local/nutanix/cluster/bin/manage_ovs show_interfaces", wait=15)
        lines = re.findall(r'((.*)\s+(\d+)\s+(True|False)\s+(\d+|None).*)', stdout.decode('utf-8'))
        self.logger.info(f"Detected {len(lines)} interfaces inside this bridge.")

        actual_interfaces = defaultdict(int)
        for line in lines:
            _name, _mode, link, speed = line[1], line[2], line[3], line[4]
            if link and speed.lower() != "none":
                speed = int(speed)
                actual_interfaces[speed] += 1
        error = False
        for interface, counter in actual_interfaces.items():
            expected_counter = expected_interfaces.get(interface, 0)
            self.logger.info(f"Detected {interface} interface count: {counter}, expected: {expected_counter}")
            if counter != expected_counter:
                error = True
                self.logger.error("Interface doesn't match!")
        if error:
            raise NodeInterfaceWrong()
        if self.facility_type == "warehouse":
            _wh_specific_check()

    @update_step
    #step 2 test firewall endpoints
    def test_firewall(self):
        unconnectable_services = []

        def _test_ip_thread(
            source, username, password, destination, port, is_udp, is_mandatory
        ):
            self.init_flask_app()
            self.logger.info(
                f"Testing firewall... Source: {source}; Destination: {destination}; Port: {port}; Username: {username}"
            )
            success_pattern = "Ncat: Connected to"
            _conn = SSHConnect(source, username, password, logger=self.logger, retry=3)
            res, _ = _conn.connect(timeout=3)
            if not res:
                self.ilg.write(f"Can't connect to {source}, ignore.", severity="warning")
            _conn.invoke_shell()
            _conn.send_command(
                f"ncat {destination} {port} {'--udp' if is_udp else ''} --verbose"
            )
            time.sleep(5)
            output = _conn.receive_output()
            if success_pattern not in output:
                if is_mandatory:
                    unconnectable_services.append(
                        {"destination": destination, "port": port, "is_udp": is_udp}
                    )
                else:
                    self.ilg.write(
                        f"Can't connect to '{destination}:{port}{'(UDP)' if is_udp else ''}! But ignore."
                    )

        rules = Benchmark().get_bmk_by_id(self.task.benchmark_id, real_fw_rules=True)['firewall_rules']['rules']
        """
        e.g. rules = [
            {
                "subject": "Central_Pe",
                "source": "Site",
                "endpoint": [
                    "systems",
                    "central_pe_ip"
                ],
                "services": [
                    {
                        "port": 2020,
                        "is_udp": false
                    },
                    {
                        "port": 9440,
                        "is_udp": false
                    },
                    {
                        "port": 161,
                        "is_udp": true
                    }
                ],
                "is_mandatory": true
            },
            ...
        ]
        """
        default_labels = Benchmark().get_bmk_by_id(self.task.benchmark_id)["vault"]["new_cluster_labels"]
        ahv_default_sa = ServiceAccount(default_labels["Site_Ahv_Root"]).get_service_account()

        ahv_cvm_oob_mapping = json.loads(self.task.ahv_cvm_oob_mapping)
        ahv = [_["dhcp"]["ahv"] for _ in ahv_cvm_oob_mapping][0]
        # ahv = "************"
        thread_pool = list()
        for rule in rules:
            thread = threading.Thread(
                target=_test_ip_thread,
                kwargs={
                    "source": ahv,
                    "username": ahv_default_sa["username"],
                    "password": ahv_default_sa["password"],
                    "destination": rule['destination'],
                    "port": rule["port"],
                    "is_udp": rule["is_udp"],
                    "is_mandatory": rule["is_mandatory"],
                },
            )
            thread.start()
            thread_pool.append(thread)
        for _t in thread_pool:
            _t.join()
        if unconnectable_services:
            self.ilg.write(f"Warning: The following services are unreachable: {unconnectable_services}", severity="warning")

    def _get_cluster_detail_by_ip(self):
        vault = Vault(tier=self.tier)
        central_pe = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('central_pe')
        _res, pc_data = vault.get_secret(f"{central_pe}/Site_Pc_Admin")
        rest_fc = FoundationCentral(endpoint=self.task.pc_fqdn, username=pc_data['username'], password=pc_data['secret'], logger=self.logger)
        cluster_list = rest_fc.list_imaged_clusters().json()["imaged_clusters"]
        cluster = None
        cluster_uuid = None
        for c in cluster_list:
            if c["cluster_external_ip"] == self.task.pe_ip:
                if not cluster or c["created_timestamp"] > cluster["created_timestamp"]:
                    cluster = c
                    cluster_uuid = c["imaged_cluster_uuid"]
        if not cluster_uuid:
            self.ilg.write("Can't find imaged cluster, return.", severity="warning")
            return
        return cluster

    def _get_cluster_detail_by_name(self):
        vault = Vault(tier=self.tier)
        central_pe = Benchmark().get_bmk_by_id(bmk_id=self.task.benchmark_id).get('systems').get('central_pe')
        _res, pc_data = vault.get_secret(f"{central_pe}/Site_Pc_Admin")
        rest_fc = FoundationCentral(endpoint=self.task.pc_fqdn, username=pc_data['username'], password=pc_data['secret'], logger=self.logger)
        cluster_list = rest_fc.list_imaged_clusters().json()["imaged_clusters"]
        cluster = None
        cluster_name = None
        for c in cluster_list:
            if c["cluster_name"] == self.task.pe_name:
                if not cluster or c["created_timestamp"] > cluster["created_timestamp"]:
                    cluster = c
                    cluster_name = c["cluster_name"]
        if not cluster_name:
            self.ilg.write("Can't find imaged cluster, return.", severity="warning")
            return
        return cluster


    def abort_task(self, user, abort_metro_task=False):
        """
        Aborts task(s) based on facility type and user choice:
        - For WIAB (warehouse): aborts sub-task and optionally metro task
        - For SIAB (retail): aborts single task only
        
        Args:
            user: Username of the person initiating the abort
            abort_metro_task: Boolean indicating whether to abort the metro task as well
        """
        try:
            if not self.task.id:
                return False, "No task ID found"

            # Handle WIAB (warehouse) case
            if self.facility_type == FacilityType.WAREHOUSE and self.task.metro_task_id:
                # Abort sub-task first
                sub_task_success, sub_task_message = terminate_process_by_id(self.task.pid)
                if sub_task_success:
                    self.task.status = TaskStatus.ABORT
                    self.ilg.write(f"Sub-task {self.task.id} was successfully aborted by user {user}. PID: {self.task.pid}")
                else:
                    self.ilg.write(f"Failed to abort sub-task {self.task.id}. Error: {sub_task_message}")

                # Abort metro task if requested
                metro_task_success = True
                metro_task_message = ""
                if abort_metro_task:
                    metro_task = ModelWhClusterTask.query.get(self.task.metro_task_id)
                    if metro_task and metro_task.pid:
                        metro_task_success, metro_task_message = terminate_process_by_id(metro_task.pid)
                        if metro_task_success:
                            metro_task.status = TaskStatus.ABORT
                            self.ilg.write(f"Metro task {metro_task.id} was successfully aborted by user {user}. PID: {metro_task.pid}")
                        else:
                            self.ilg.write(f"Failed to abort metro task {metro_task.id}. Error: {metro_task_message}")

                db.session.commit()
                if sub_task_success and metro_task_success:
                    return True, "Tasks aborted successfully"
                if not sub_task_success:
                    return False, f"Failed to abort sub-task: {sub_task_message}"
                return False, f"Sub-task aborted but metro task failed: {metro_task_message}"

            # Handle SIAB (retail) case or non-metro WIAB task
            success, message = terminate_process_by_id(self.task.pid)
            if success:
                self.task.status = TaskStatus.ABORT
                self.ilg.write(f"Task {self.task.id} was successfully aborted by user {user}. PID: {self.task.pid}")
                db.session.commit()
                return True, "Task aborted successfully"
            return False, f"Failed to abort task: {message}"

        except Exception as e:
            error_msg = f"Error while aborting task: {str(e)}"
            self.ilg.write(error_msg)
            return False, error_msg
            

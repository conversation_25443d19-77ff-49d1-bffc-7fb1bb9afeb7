class Endpoint(): 
    ENDPOINTS = {
        "IKEADT" : {
            "THORS": "https://thorshammereu.ikea.com/api/runmethod"
        },
        "PRODUCTION" : {
            "THORS": "https://thorshammereu.ikea.com/api/runmethod"
        },
        "PREPRODUCTION":{
            "THORS": "https://thorshammereu.ikea.com/api/runmethod"
        }
    }
    def __init__(self) -> None: 
        pass
    
    @classmethod
    def get(cls, endpoint, tier="production"): 
        return cls.ENDPOINTS[tier.upper()][endpoint.upper()]

print(Endpoint.get("thors","production"))
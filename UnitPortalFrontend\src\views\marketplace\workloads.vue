<template>
  <div class="app-container" style="height:100%;width:100%">
    <div class="filter-container">
      <el-row :gutter="5" >
        <el-col :span="3" >
          <el-button class="filter-item"  type="success" @click="handle_create" v-show="this.priv.role_mkt.create_wl!='empty'">
            Create
          </el-button>
        </el-col>
        <el-col :span="2" :offset="8" >
          <el-checkbox checked style="margin-left:5%;margin-top:9%" v-model="listQuery.hide_duplicates" @change="filter_wl_list"/>
              <div style="width:70%;float:left;margin-left:5%;margin-top:10%">
                <span>Hide duplicates</span>
              </div>
        </el-col>
        <el-col :span="4"  >
          <el-select size="large"
            v-model="filter.selected_pc" multiple collapse-tags placeholder="Filter the PC" style="width:100%;" >
            <el-option v-for="item in filter.pc_list" :key="item" :label="item" :value="item" style="font-size: large;"/>
          </el-select>
        </el-col>

        <el-col :span="3" >
          <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large" @keyup.enter.native="filter_wl_list" />
        </el-col>

        <el-col :span="2" style='float:right;' ><!--:offset="17" -->
          <el-button class="filter-item"  type="danger" size="large" style="float:right;width:70%" @click="handle_delete">
            Clean Up
          </el-button>
        </el-col>

        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="filter_wl_list">
            Search
          </el-button>
        </el-col>


      </el-row>
    </div>
    Hover you mouse on ID to see SPECS
    <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="current_list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
        @sort-change="sortChange"
        @row-click="handle_row_click"
        class='template-table'
        ref='workload_table'
      >
  
        <!-- <el-table-column label="ID" min-width="3%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column> -->
        <el-table-column type="selection" width="35" />
        <el-table-column label="ID" min-width="3%" align="center" >
          <template slot-scope="{row}">
            <el-popover placement="right" trigger="hover" title="SPECS" width=400>
              <div>
                Prism:{{ row.pc }}<br>
                Cluster:{{ row.pe }}<br>
                CPU:{{ row.cpu }}<br>
                Core:{{ row.cpu_core }}<br>
                Memory:{{ row.memory }}<br>
                Disk:{{ row.disk }}<br>
                Vlan:{{ row.vlan_id }}<br>
                Template:{{ row.template_id }}<br>
                Linux_payload:{{ row.linux_payload }}<br>
              </div>
              <span slot="reference">{{ row.id }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="VM name"  min-width="8%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="Cluster" class-name="status-col" min-width="10%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.pe }}</span>
          </template>
        </el-table-column>
<!-- 
        <el-table-column label="VM Type" min-width="5%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.workload_type }}</span>
          </template>
        </el-table-column> -->
        
        <el-table-column label="Image" class-name="status-col" min-width="7%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.image_name }}</span>
          </template>
        </el-table-column>

        <el-table-column  label="VlanID" min-width="5%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.vlan_id}}</span>
          </template>
        </el-table-column>

        <el-table-column label="Latest log(click to show all logs)" align="center" min-width="18%">
        <template slot-scope="{row}">
          <span>{{ row.latestlog.loginfo |logFilter}}</span><span class="link-type"  @click="handleFetchBriefLog(row)">     More</span>
        </template>
      </el-table-column>
      
        <el-table-column label="Status" class-name="status-col" min-width="6%" align="center" >
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusFilter">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
        <el-table-column  label="Creater" min-width="5%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.creater}}</span>
          </template>
        </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" />
  
    <el-dialog id="modal" :visible.sync="dialogFormVisible" style="width:100%;  margin-left:2.5%; margin-top:-5%;" :close-on-click-modal="false" @opened="resize_vm_cart">
      <div style="width:100%;text-align: center;font-size:35px;">Create VM</div>
      <div style="border-top: 0.1px solid rgb(185, 182, 182);width:100%;height:0;margin-top:1%"></div>
      <div style="height:100%;width:100%;">
        <div  style="width:60%;margin-top: 2%;float:left;border-right: 1px solid rgb(241, 234, 234)">
          <div ref="vm_shopping_view">
            <el-form ref="vm_form" :rules="rules" label-position="left" :model="config" style="width: 100%; ">

              <div class="label">
                Loacation
              </div>

              <el-form-item  prop="pc" class="form_item"> 
                <div class="form-label"><span>Prism</span></div>
                <el-select v-model="config.pc" placeholder="Prism" class="filter-item" filterable style="width:68%" @change="clear_pe_selection">
                  <el-option v-for="item in prismOptions" :key="item.key" :label="item.display_name+'('+item.key+')'" :value="item.display_name" />
                </el-select>
              </el-form-item>

              <el-form-item prop="pe" class="form_item" >
                <div class="form-label"><span>Cluster</span></div>
                <el-select v-model="config.pe" class="filter-item" placeholder="Cluster" filterable style="width:68%" @change="change_vm_name">
                  <el-option v-for="item in peTypeKeyValue[config.pc]" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>

              <div class="label" >
                <span>Basic specs</span>
                <div style="clear:both"></div>
              </div>

              <el-form-item class="form_item">
                <div class="form-label"><span>source </span></div>
                <el-radio v-model="config.source" label="template" @change="select_template" v-show="show_template">Template</el-radio>              
                <el-select v-model="config.template_id" class="filter-item" style="width:27%;margin-right:3%" @change="select_template" v-show="show_template">
                  <el-option v-for="item in templateOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
                <el-radio v-model="config.source" label="customize" v-show="show_customize">Customize</el-radio>
              </el-form-item>

              <el-form-item  prop="name" class="form_item">
                <div class="form-label"><span>Name</span></div>
                <el-input @change="load_9423_template" v-model="config.name" maxlength="40" placeholder="VM name" show-word-limit type="text" style="width:68%" :disabled="config.source=='template'"/>
              </el-form-item>

              <!-- <el-form-item class="form_item" style="border:1px solid red">
                <div class="form-label"><span>OS </span></div>
                <el-radio v-model="config.workload_type" label="windows" :disabled="config.source=='template'">Windows</el-radio>
                <el-radio v-model="config.workload_type" label="linux" :disabled="config.source=='template'">Linux</el-radio>
                <el-radio v-model="config.workload_type" label="network" :disabled="config.source=='template'">Network appliance</el-radio>
              </el-form-item> -->

              <div  class="form_item"> 
                <el-form-item  style="float:left; width:66%;">
                  <div class="form-label_os"><span>OS </span></div>
                  <el-radio v-model="config.workload_type" label="windows" :disabled="config.source=='template'">Windows</el-radio>
                  <el-radio v-model="config.workload_type" label="linux" :disabled="config.source=='template'">Linux</el-radio>
                  <el-radio v-model="config.workload_type" label="network" :disabled="config.source=='template'">Network appliance</el-radio>
                </el-form-item>

                <el-form-item  prop="skip_sizing"  style="float:left;width:25%;"> 
                  <el-checkbox
                  v-model="config.skip_sizing"
                  :disabled="disable_skip_sizing">
                    skip sizing
                  </el-checkbox>
                </el-form-item>

                <div style="clear:both"></div>

              </div>

              <el-form-item  prop="image_id" class="form_item" > 
                <div class="form-label"><span>Image</span></div>
                <el-select v-model="config.image_id" placeholder="Image" class="filter-item" :style="{'width':config.workload_type=='linux'?'44%':'68%'}"  :disabled="config.source=='template'">
                  <el-option v-for="item in imageOptions[config.workload_type]" :key="item.key" :label="'ID:'+item.key + ' Name:'+item.display_name" :value="item.key" />
                </el-select>
                <el-popover
                  placement="top-start"
                  :hidden="config.workload_type!='linux'" 
                  width="650"
                  trigger="click">
                  <el-button slot="reference" icon="el-icon-edit" style="margin-left:3%" @click="load_9423_template"  >
                    payload of 9423
                  </el-button>
                  <div>
                    <div style="margin-bottom: 5px;"> 
                      <span style="font-size: 23px">Tower payload</span><el-button type="primary" style="float:right;" :disabled = "config.source=='template'" @click="use_default_payload">use default payload</el-button>
                      <div style="clear:both"></div>
                    </div>
                    
                    <el-input
                      v-model="config.payload_9423"
                      size="large"
                      :rows="21"
                      type="textarea"
                      placeholder="Please input"
                      :disabled="config.source=='template'"
                      style="float:right"
                    />
                  </div>

                </el-popover>
              </el-form-item>

              <div  class="form_item"> 
                <el-form-item  prop="cpu"   style="float:left; width:35%;"> 
                  <div class="form-label_cpu"><span>CPU</span></div>
                  <el-input v-model="config.cpu" placeholder="CPU"  style="width:42%;" :disabled="config.source=='template'"/>
                </el-form-item>
              
                <el-form-item  prop="cpu_core" style="float:left; width:30%;"> 
                  <div class="form-label_core" style="margin-left:10%;width:27%"><span>Cores</span></div>
                  <el-input v-model="config.cpu_core" placeholder="Cores" style="width:50%" :disabled="config.source=='template'"/>
                </el-form-item>
                <el-form-item  prop="memory"  style="float:left;width:30%;"> 
                  <div class="form-label_half_right"style="margin-left:3%;width:23%"><span>Ram</span></div>
                  <el-input v-model="config.memory" placeholder="Ram"  style="width:50%;" :disabled="config.source=='template'"/>
                </el-form-item>

                <div style="clear:both"></div>
              </div>

              <div  class="form_item"> 

                <el-form-item  prop="disk" style="float:left;width:50%;"> 
                  <div class="form-label_half_left"><span>Disk</span></div>
                  <el-input v-model="config.disk" placeholder="Seprate by ',' (GB)" style="width:60%" :disabled="config.source=='template'"/>
                </el-form-item>

                <el-form-item  prop="dns"  style="float:left;width:45%;"> 
                  <el-checkbox v-model="config.dns" style="float:left;margin-left:15%" :disabled="config.source=='template'"/>
                  <div style="width:60%;float:left;margin-left:5%"><span>Register DNS in IPAM</span></div>
                </el-form-item>

                <div style="clear:both"></div>

              </div>

              <div  class="form_item"> 

                <el-form-item  prop="vlan_id"  style="float:left;width:34%;"> 
                  <div class="form-label_half_left_small" style="width:30%;"><span>Vlan</span></div>
                  <el-input v-model="config.vlan_id" placeholder="Vlan"  style="width:30%;" :disabled="config.source=='template'"/>
                </el-form-item>
                           
                <el-form-item  prop="subnet"  style="float:left;width:30%"> 
                  <div class="form-label_subnet" style="margin-left:5%;width:25%"><span>Subnet</span></div>
                  <el-input v-model="config.subnet" placeholder="Use ahv subnet"  style="width:65%;" :disabled="config.source=='template'">

                  </el-input>
                </el-form-item>

                <el-form-item  prop="user_specified_ip"  style="float:left;width:30%" v-show="config.workload_type=='linux'"> 
                  <div class="form-label_subnet" style="margin-left:5%;width:15%"><span>IP</span></div>
                  <el-input v-model="config.user_specified_ip" placeholder="Decide it for me"  style="width:61%;" :disabled="config.source=='template'">

                  </el-input>
                </el-form-item>

                <!-- <el-form-item  prop="skip_sizing"  style="float:left;width:25%;border:1px solid green"> 
                  <el-checkbox
                  v-model="config.skip_sizing"
                  :disabled="disable_skip_sizing">
                    skip sizing
                  </el-checkbox>
                </el-form-item> -->
                <div style="clear:both"></div>
              </div>

              <el-form-item  prop="app_package" class="form_item">
                <div class="form-label"><span>Package</span></div>

                <el-select 
                  v-model="config.selected_app_package" 
                  filterable 
                  placeholder="Please select" 
                  style="width: 68%" 
                  multiple 
                  :disabled="config.source=='template'">
                  <el-option v-for="item in config.app_package_list" :key="item" :label="item" :value="item" style="font-size: large;"/>
                </el-select>
              
              </el-form-item>
              </el-form>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="add_to_cart" style="float: right;margin-right: 60px;">
              Add to cart
            </el-button>
          </div>
        </div>
        <div  style="width:38%;margin-top: 2%;float:left;">
          <div :style="'height:'+ vm_cart_height " style="overflow-y:auto;margin-bottom: 20px;">
            <div class="dh-card is-always-shadow" v-for="(vm,index) in cart">
            <div  class="dh-card-header ">
                <div >
                  
                  <div class="vm-name " v-on:click="vm.display = !vm.display;">
                    {{vm.name}}
                  </div>

                  <div class="btn-groups">
                    <el-button class="dh-btn" type="danger" @click="remove_from_cart(vm)">x</el-button> 
                  </div>
                  
                  <div style="clear:both"></div>
                </div>

            </div>
            <div class="collapse" v-collapsible="vm.display">
              <div class="collapse-whole"> prism: {{ vm.pc }}</div>
              <div class="collapse-whole"> cluster: {{ vm.pe }}</div>
              <div class="collapse-left"> cpu: {{ vm.cpu }}</div><div class="collapse-right"> cpu core: {{ vm.cpu_core }}</div>
              <div class="collapse-left"> ram: {{ vm.memory }}</div><div class="collapse-right"> vlan: {{ vm.vlan_id }}</div>
              <div class="collapse-whole"> image ID: {{ vm.image_id }}</div>
              <div class="collapse-whole"> register dns in ipam: {{ vm.update_dns }}</div>
              <div class="collapse-left"> os: {{ vm.workload_type }}</div><div class="collapse-right"> disk: {{ vm.disk }}</div>
              <div style="clear:both"></div>
            </div>
            </div>
          </div>
          <div slot="footer" class="dialog-footer">
            <div style="overflow-y:auto;">
            <el-button type="success" @click="create_vm" style="float: right;margin-right: 10px;">
              Install
            </el-button>
            </div>
          </div>
        </div>
        <div style="clear:both"></div>
      </div>
    </el-dialog>

  
    <el-dialog  :visible.sync="dialogPvVisible" :title="'Workload Log(brief)'" >

      <el-table :data="logdata" border fit highlight-current-row style="width: 100%" max-height="500" >
        <el-table-column prop="key" label="log date"  min-width="25%" >
          <template slot-scope="{row}">
            <span :class="row.severity=='error'?'log_table_error':''">{{ row.logdate }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pv" label="log info" min-width="55%"  >
          <template slot-scope="{row}">
            <span :class="row.severity=='error'?'log_table_error':''">{{ row.loginfo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pv" label="log severity"  min-width="10%"  >
          <template slot-scope="{row}">
            <span :class="row.severity=='error'?'log_table_error':''">{{ row.severity }}</span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" @click="downloadLogFile()">Download Detail Log</el-button>
        <el-button type="primary" @click="dialogPvVisible = false">OK</el-button>

      </span>
    </el-dialog>
  </div>
</template>

  <script>
  import {GetWorkloadInfo, GetWorkloadTaskList, CreateWorkload, GetAppPackageList,
     GetPrismList, GetPCPECorrespondence, Download ,CleanUpVM, GetWorkloadTaskLogs} from '@/api/nutanix'
  import waves from '@/directive/waves' // waves directive
  import { parseTime } from '@/utils'
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination

  import {fetch_domain, fetch_env} from '@/utils/commonfunc'
  
  export default {
    name: 'WorkloadTable',
    components: { Pagination },
    directives: { waves },
    filters: {
      statusFilter(status) {
        let st = status.toLowerCase();
        const statusMap = {
          'not started': 'info',
          'done': 'success',
          'error': 'danger',
          'in progress':'primary'
        }
        return statusMap[st]
      },
      logFilter(log) {
        let showing_log
        try{
            if(log.search(/\[.*\]/)==0){
              let log_array = log.replace(/\[(.*)\]/,'$1')
              let last_log = log_array.split("\\n',")
              showing_log = last_log[last_log.length-1]
            }
            else{
              showing_log = log
            }
            if(showing_log.length > 50){
              return showing_log.slice(0,50) + "..."
            }
            return showing_log
        }
        catch{
          return log
        }
      },
    },
    data(){
      const disk_validator =(rule, value, callback)=>{//磁盘大小的验证方法
        if(value==null || value==undefined){
          callback(new Error("Disk is required."))
          return
        }
        if(value.toString().match(/^[0-9]+$|[0-9]+(,[0-9]+)+/)){
          let disk_list = value.split(",")
          for (let _d of disk_list){
            if(parseInt(_d) > 10240){
              callback(new Error("You need a disk lager than 10TB?"))
            }
          }
          callback()
        }
        else if( value.trim()==''){
          callback(new Error("Disk is required."))
        }
        else{
          callback(new Error("Format should be '100,50,100'."))
        }
      }

      const vlan_validator =(rule, value, callback)=>{//vlan合规性的验证方法
        if(value==null || value==undefined){
          callback(new Error("Vlan is required."))
          return
        }
        if(value.toString().match(/^[0-9]+$/)){
          callback()
        }
        else{
          callback(new Error("Only numbers allowed."))
        }
      }

      const name_validator = (rule, value, callback) =>{//vm name validator
        if(value==null || value==undefined){
          callback(new Error("Name is required."))
          return
        }
        if(value.length > 40){
          callback(new Error("Name is too long."))
        }
        else{
          if (/^[a-zA-Z0-9]+[a-zA-Z0-9\-]*[a-zA-Z0-9]$/.test(value))
          {
            callback()
          }
          callback(new Error(`Hey buddy, our naming check is pretty loosen, a-z A-Z 0-9 are allowed, but you failed?! What kind of name is "${value}" :??`))
        }
      }
      const ip_validator = (rule, value, callback) =>{//vm name validator
        if(value==null || value==undefined||value==""){
          callback()
        }
        
        if (/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(value)){
          callback()
        }
        callback(new Error("Not a valid ip address"))

      }
      return {
        displayDiv: false,  
        priv:null,
        prismtier:[
          'Production',
          'PreProduction',
          'IKEADT',
          'IKEAD2'
        ],
        cart:[],
        filtered_list:null,
        current_list: null,
        tableKey: 0,
        list: null,
        workload_task_list : null,
        filter:{
          pc_list:[],
          selected_pc:[],
          fuzzy_string:"",
        },
        prismlist: null,
        listLoading: true,
        listQuery: {
          hide_duplicates:null,
          page: 1,
          limit: 20,
          cluster: '',
          prism: '',
          status: '',
          sort: '+id'
        },
        statusToShowEditButton:['Not Started'],
        statusToShowAbortButton:['In Progress'],
        statusToShowDeleteButton:['Not Started','Done','Error','Aborted'],
        statusOptions: ['Not Started','In Progress','Done','Error','Aborted'],
        prismOptions:[],
        peTypeKeyValue:{},
        imageOptions:{},
        templateOptions:[],
        sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
        // statusOptions: ['published', 'draft', 'deleted'],
        ShowCreationDate: false,
        config:{
          pc                    : null,
          pe                    : null,
          name                  : null,
          workload_type         : "windows",
          image_id              : null,
          cpu                   : null,
          cpu_core              : null,
          vlan_id               : null,
          dns                   : true,
          memory                : null,
          disk                  : null,
          description           : null,
          display               : false,
          boot_mode             : 'LEGACY',
          oscustomization_id    : '0',
          source                : 'customize',
          template_id           : null,
          naming_convention     : null,
          payload_9423_template : '',
          payload_9423          : null,
          app_package_list      : [],
          selected_app_package  : [],
          subnet                : "",
          user_specified_ip     : "",
        },
        selectedrow:'',
        dialogFormVisible: false,
        dialogPvVisible: false,
        logdata: [],
        rules: {
          pc: { required: true, message: 'Prism is required', trigger: 'change' },
          pe: { required: true, message: 'Cluster is required', trigger: 'submit'},
          name: {required: true, trigger: 'change', validator:name_validator},
          image_id: {required: true, message: 'Image is required', trigger: 'change' },
          cpu: {required: true, message: 'Cpu is required', trigger: 'change' },
          cpu_core: {required: true, message: 'Core is required', trigger: 'change' },
          memory: {required: true, message: 'Memory is required', trigger: 'change' },
          disk: {required: true, trigger: 'change', validator:disk_validator},
          vlan_id: {required: true, trigger: 'change', validator:vlan_validator},
          subnet: {required: true, trigger: 'change', validator:ip_validator},
          // timestamp: { type: 'date', required: true , trigger: 'change' , validator:validateTime}
        },
        downloadLoading: false,
        intervaljob:'',
        vm_cart_height:'631px',
      }
    },
    computed:{
      total() {
        if(this.filtered_list){
          return this.filtered_list.length
        }
        else{
            return 0
        }
      },
      show_customize(){
        if(this.priv.role_mkt.create_wl == 'full'){
          return true 
        }
        if(this.priv.role_mkt.create_wl == 'empty'){
          return false
        }
        if(this.priv.role_mkt.create_wl == 'part'){
          if(this.priv.role_mkt.create_wl_scope.custom_settings.length==0){
            return false
          }
          else{
            return true
          }
        }
      },
      show_template(){
        if(this.priv.role_mkt.create_wl == 'full'){
          return true 
        }
        if(this.priv.role_mkt.create_wl == 'empty'){
          return false
        }
        if(this.priv.role_mkt.create_wl == 'part'){
          if(this.priv.role_mkt.create_wl_scope.template_ids.length==0){
            return false
          }
          else{
            return true
          }
        }
      },
      disable_skip_sizing(){
        console.log(this.priv.role_mkt)
        if(this.priv.role_mkt.create_wl == "full"){
          return false 
        }
        if(!!this.priv.role_mkt.create_wl_scope.skip_sizing){
          return false 
        }
        return true
      }
    },
    created() {
      this.render_elements()
      this.getPCList()
      this.getPEList()
      this.get_workload_task_list()
      this.get_workload_info()
      this.init_priv()
    },
    methods: {    
      render_elements(){
        console.log(this.$store.getters)
      }, 
      init_priv(){
        this.priv = this.$store.getters.all_privilege
      },
      getPCList(){
        GetPrismList(this.$store.getters.token).then(response => {
          for(let pc of response['data']){
            let k
            if(pc['fqdn'].match(/.+?-(.+)-.*/)){
              k = RegExp.$1
            }
            else{
              k = pc['fqdn'].match(/(.+?)(\..+)/)[1]
            }
            this.prismOptions.push({"key": k.toUpperCase(), "display_name":pc['fqdn'].toUpperCase()})
          }
          })
      },
      getPEList(){
        GetPCPECorrespondence(this.$store.getters.token).then(response => {
          this.peoptions = response['data']
          this.peTypeKeyValue = this.peoptions.reduce((acc, cur) => {
            acc[cur.pc] = cur.pe.sort()
            return acc
          }, {})
        })
      },
      filter_wl_list(){
        //根据过滤条件筛选表格显示内容
        //screen the table as per filters
        this.listQuery.page = 1
        let temp_list

        //filter selected pc first.
        if (this.filter.selected_pc.length){
          //No filter, so select all
          temp_list = this.workload_task_list.filter((item)=>{
            if(item.pc!=null && item.pc!=undefined){
              return this.filter.selected_pc.includes(item['pc'].toLowerCase())
            }
          })
          this.filtered_list = temp_list
        }
        else{
          this.filtered_list = this.workload_task_list
        }
        // do the duplicate now
        if(this.listQuery.hide_duplicates){
          temp_list = []
          this.filtered_list.map((l)=>{
            if(!temp_list.map(t=>t.name).includes(l.name)){
              temp_list.push(l)
            }
          })
          this.filtered_list = temp_list
        }
        // do the fuzzy search now
        if(this.filter.fuzzy_string.trim().length){
          let temp_list = this.filtered_list
          let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
          //去除空格 并以空格分割成数组
          //remove space, and split into array by space 
          for(let fuzzy of fuzzy_list){
            fuzzy = fuzzy.toString().toLowerCase()
            temp_list = temp_list.filter((k)=>{
              let combined_string = (k.id?k.id.toString().toLowerCase():'') + (k.pc?k.pc.toString().toLowerCase():'')+
                                    (k.pe?k.pe.toString().toLowerCase():'')+
                                    (k.status?k.status.toString().toLowerCase():'')+
                                    (k.vlan_id?k.vlan_id.toString().toLowerCase():'')+
                                    (k.name?k.name.toString().toLowerCase():'')+
                                    (k.creater?k.creater.toString().toLowerCase():'')
              if( combined_string.search(fuzzy)!= -1){
                return true
              }
            })
          }
          this.filtered_list = temp_list
        }

        this.set_page()
      },
      validate_ip(ipaddress) {  
        if (/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(ipaddress)) {  
          return (true)  
        }  
        return (false)  
      },
      set_page(){
        // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total 
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)
      },
      get_workload_info(){//get the information regarding this page, not single workload
        GetWorkloadInfo(this.$store.getters.token).then(response => {
          let data = response.data
          let image = data.image
          this.templateOptions = data.template
          
          this.templateOptions.sort((item1, item2)=> item1.name.toUpperCase() > item2.name.toUpperCase()?1:-1)
          if(this.priv.role_mkt.create_wl=='part'&&this.priv.role_mkt.create_wl_scope.template_ids.length!=0){
            console.log(data.template)
            console.log(this.priv.role_mkt.create_wl_scope.template_ids)
            this.templateOptions = data.template.filter((_t)=>{
              if(this.priv.role_mkt.create_wl_scope.template_ids.includes(_t.id)){
                return true
              }
            })
            console.log(this.templateOptions)
          }
          let windows = []; let linux= []; let network = []
          for(let im of image){
            if (im.os_type == 'windows'){
              windows.push({'key':im.id,'display_name':im.alias})
              continue
            }
            if (im.os_type == 'linux'){
              linux.push({'key':im.id,'display_name':im.alias})
            }
            if (im.os_type == 'network'){
              network.push({'key':im.id,'display_name':im.alias})
            }
          }
          this.imageOptions['windows'] = windows
          this.imageOptions['linux'] = linux
          this.imageOptions['network'] = network
        })
        GetAppPackageList(this.$store.getters.token).then(response => {
          this.config.app_package_list = response.data
        })
        
      },
      get_workload_task_list(){
        this.listLoading = true
        GetWorkloadTaskList(this.$store.getters.token).then(response => {
          this.workload_task_list = response.data
          console.log(this.workload_task_list)
          this.filtered_list = this.workload_task_list 

          this.workload_task_list.sort((item1,item2)=>{       
              return (item1.id > item2.id ) ? -1 : ((item1.id  < item2.id ) ? 1 : 0)
          })
          this.listLoading = false
          this.workload_task_list.forEach(e => {
            if(e.pc!=undefined && e.pc!=null && e.pc.trim()!=""){
              if(!this.filter.pc_list.includes(e.pc.toLowerCase())){
                this.filter.pc_list.push(e.pc.toLowerCase())
              }
            }
          });
          this.filter_wl_list()
          // let page = this.listQuery.page
          // let limit = this.listQuery.limit
          // let start , end
          // if(page*limit>=this.total){
          //   start = (page-1)*limit
          //   end = this.total
          // }
          // else{
          //   start = (page-1)*limit
          //   end = page * limit
          // }
          // this.current_list = this.filtered_list.slice(start,end)
          })
      },
      handleFilter() {
        this.listQuery.page = 1
      },
      sortChange(data) {
        const { prop, order } = data
        if (prop === 'id') {
          this.sortByID(order)
        }
      },
      sortByID(order) {
        if (order === 'ascending') {
          this.listQuery.sort = '+id'
        } else {
          this.listQuery.sort = '-id'
        }
        this.handleFilter()
      },
      resetTemp() {
        let localtime = new Date()
        let utctime =new Date( localtime.getTime() + 60*1000*localtime.getTimezoneOffset())
      },
      handle_create() {
        // this.resetTemp()
        this.dialogFormVisible = true
        // this.$nextTick(() => {
        //   this.$refs['dataForm'].clearValidate()
        // })
      
        // CreateWorkloadTask(this.$store.getters.token)
      },
      handle_delete(){
        if(this.$refs.workload_table.selection.length){
          let payload = {
            data:this.$refs.workload_table.selection.map(
              (_task)=>{
                return {"task_id":_task.id, force:false}
              }
            ),
            token:this.$store.getters.token
          }
          CleanUpVM(payload)
          .then(
            (response)=>{
              for(let res of response.data){
                if (res.success){
                  this.$notify({
                      title: res.task_id + ' Success',
                      message: res.message,
                      type: 'success',
                      duration: 10000
                    })
                }
                else{
                  this.$notify({
                      title: res.task_id +  ' Error',
                      message: res.message,
                      type: 'error',
                      duration: 10000
                    })
                }
              }
              this.get_workload_task_list()
            }
            
          )
          .catch((error)=>{
              this.$notify({
                  title: 'Error',
                  message: 'Failed to clean up VMs.',
                  type: 'error',
                  duration: 5000
                })
          })
        }else{
          this.$notify({
              title: 'No task selected',
              message: 'At least select 1 task to delete!',
              type: 'warning',
              duration: 4000
            })
        }
      },
      handle_row_click(row,column,event){
        this.selectedrow = row
      },
      add_to_cart(){
        this.$refs['vm_form'].validate((valid)=>{
          //as the rules, validate the form, to see if there is anything not valid
          if(valid){
            for(let item of this.cart){
              if  (this.config.name == item.name &&
                  this.config.prism == item.prism &&
                  this.config.cluster == item.cluster
              ){
                this.$message({
                  type: 'error',
                  message: 'Dupicate VM name in the same cluster.'
                });
                return
              }
            }
            //modify string to int
            let wl_config = {
              boot_mode         : this.config.boot_mode,
              cpu               : parseInt(this.config.cpu),
              cpu_core          : parseInt(this.config.cpu_core),
              description       : this.config.description,
              disk              : this.config.disk.split(',').map(t=>{return parseInt(t)}),
              update_dns        : this.config.dns,
              image_id          : parseInt(this.config.image_id),
              memory            : parseInt(this.config.memory),
              name              : this.config.name,
              pc                : this.config.pc,
              pe                : this.config.pe,
              vlan_id           : parseInt(this.config.vlan_id),
              workload_type     : this.config.workload_type,
              display           : false,
              linux_payload     : this.config.payload_9423,
              app_package       : this.config.selected_app_package,
              subnet_ip         : this.config.subnet,
              skip_sizing       : this.config.skip_sizing,
              user_specified_ip : this.config.user_specified_ip
            }
            if(this.config.source == 'template'){
              wl_config['template_id'] = this.config.template_id
            }
            this.cart.push(Object.assign({},wl_config)) // need to use deep copy here, Object.assign()
          }
        })
      },
      remove_from_cart(vm){
        this.cart = this.cart.filter((item)=>{
          return (item.prism != vm.prism ||
            item.cluster != vm.cluster ||
            item.name != vm.name)
        })
      },
      create_vm(){
        let payload = {
          data:this.cart,
          token:this.$store.getters.token
        }
        console.log(payload)
        CreateWorkload(payload)
        .then((response)=>{
          for (let vm of response['data']){
            if(vm['flag']){
              this.$notify({
                title: vm["name"],
                message: vm["message"],
                type: 'success',
                duration: 5000
              })
            }
            else{
              this.$notify({
                title: vm["name"],
                message: vm["message"],
                type: 'error',
                duration: 5000
              })
            }
          }
          this.get_workload_task_list()
        })
        .catch((error)=>{
            this.$notify({
                title: 'Error',
                message: 'VM task failed',
                type: 'error',
                duration: 5000
              })
        })
        this.dialogFormVisible = false

      },
      handleFetchBriefLog(row) {
        this.selectedrow = row
        let payload = {
          token: this.$store.getters.token,
          data:{
            task_id : row.id
          }
        }
        GetWorkloadTaskLogs(payload).then(response=>{
          this.logdata = response.data
          this.dialogPvVisible = true
        })
      },
      formatJson(filterVal) {
        return this.list.map(v => filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        }))
      },
      getSortClass: function(key) {
        const sort = this.listQuery.sort
        return sort === `+${key}` ? 'ascending' : 'descending'
      },
      clear_pe_selection(){
        this.config.pe = null
      },
      change_vm_name(){
        if(this.config.pe==null||this.config.pe==undefined){
          return
        }
        if(this.config.source=='customize'){
          return
        }
        let bu_code, country_code, site_code, cluster_code, five_digit_site_code
        cluster_code = this.config.pe.split("-")[0]
        if(cluster_code.length == 8){
          if(cluster_code.search(/\d{5}/) != -1){
            //if cluster code has 5 digits, like RES12852
            bu_code = cluster_code[0]
            country_code = cluster_code.substring(1,3)
            site_code = cluster_code.substring(3,8)
          }else{
            //regular cluster code, like RETCN856-nxc000
            bu_code = cluster_code.substring(0,3)
            country_code = cluster_code.substring(3,5)
            site_code = cluster_code.substring(5,8)
          }
        }
        else if(cluster_code.length == 7){
          if(cluster_code.search(/\D\d{3}\D/) != -1){
            //3 digits , like itcn001
            bu_code = cluster_code.substring(0,3)
            country_code = cluster_code.substring(3,5)
            site_code = cluster_code.substring(5,7)
          }
          else{
            if(cluster_code.search(/\D\d{4}\D/)){
            // 4 digits, like rse1015
              bu_code = cluster_code[0]
              country_code = cluster_code.substring(1,3)
              site_code = cluster_code.substring(3,7)
            }
            else{
              //7 char, ITCNCHN ..
              bu_code = cluster_code.substring(0,2)
              country_code = cluster_code.substring(2,4)
              site_code = cluster_code.substring(4,7)
            }

          }
        }
        five_digit_site_code = this.complete_to_five_digits(site_code, 5)
        this.config.name = this.config.naming_convention
                          .replace("{bucode}", bu_code)
                          .replace("{countrycode}", country_code)
                          .replace("{sitecode}", site_code)
                          .replace("{clustercode}", cluster_code)
                          .replace("{5_digit_sitecode}", five_digit_site_code)
        if(this.config.workload_type=='linux'){
          this.load_9423_template()
        }
      },
      select_template(){
        let _template = this.templateOptions.find(({id})=> id===this.config.template_id)
        if(_template == undefined|| _template == null){
          this.$message({
              type: 'danger',
              message: "Don't forget to pick a template."
            });
          return
        }
        this.config.source = 'template'
        this.config.cpu = _template.cpu
        this.config.cpu_core = _template.cpu_core
        this.config.vlan_id = _template.vlan_id
        this.config.name = _template.naming_convention
        this.config.image_id = _template.image_id
        this.config.memory = _template.memory
        this.config.disk = _template.disk
        this.config.workload_type = _template.workload_type
        this.config.naming_convention = _template.naming_convention
        this.config.payload_9423_template = _template.linux_payload||this.config.payload_9423_template
        if(_template.app_package!=null && _template.app_package!=undefined){
          this.config.selected_app_package = _template.app_package.split(',')
        }
        else{
          this.config.selected_app_package = []
        }
        this.config.dns = _template.update_dns
        this.change_vm_name()
      },
      load_9423_template(){
        if(this.config.workload_type=='linux'){
          // start to load payload , change vm_name, change domain
          let temp_name = this.config.payload_9423_template
          if(this.config.name!=null&&this.config.name!=undefined){
            temp_name = this.config.payload_9423_template
                          .replace("@{vm_name}@", this.config.name.toLowerCase())
                          .replace("@{domain}@", fetch_domain(this.config.pc))
                          .replace("@{vm_fqdn}@", this.config.name.toLowerCase() + fetch_domain(this.config.pc))
                          .replace("@{ansible_env}@", fetch_env(this.config.pc))
          }
          let ugly_json = JSON.parse(temp_name)   
          let pretty = JSON.stringify(ugly_json, undefined, 4);
          this.config.payload_9423 = pretty
        }
      },
      use_default_payload(){
        let default_payload = '{ "extra_var": {"ikea_hosts": [{"name": "@{vm_name}@","hostgroup":"PROD/@{rhel_version}@/DIST","variables":{ "patch_responsible_email": "<EMAIL>"}}]}}'
        let _image = this.imageOptions.linux.find((e)=>e.key == this.config.image_id)
        if(_image == undefined){
          this.$notify({
                title: "Error",
                message: "Are you sure you selected a linux image?",
                type: 'error',
                duration: 5000
              })
          return 
        }
        let _image_name = _image.display_name
        let _image_type = ""
        switch(true){
          case !!_image_name.match(/rhel8/i):
            _image_type="RHEL8"
            break
          case !!_image_name.match(/rhel9/i):
            _image_type="RHEL9"
            break
          case !!_image_name.match(/rhel7/i):
            _image_type="RHEL7"
            break
          default:
            this.$notify({
                title: "Error",
                message: "not RHEL7, not RHEL8, not RHEL9 ??",
                type: 'error',
                duration: 5000
              })
            return
        }
        default_payload = default_payload.replace("@{vm_name}@", this.config.name)
        default_payload = default_payload.replace("@{rhel_version}@", _image_type)
        let ugly_json = JSON.parse(default_payload)   
        let pretty = JSON.stringify(ugly_json, undefined, 4);
        this.config.payload_9423 = pretty
        
      },
      downloadLogFile(){
      if (!this.selectedrow.detail_log_path){
        this.$notify({
              title: 'Ooooops',
              message: 'No log yet!',
              type: 'info',
              duration: 2000
            })
      }
      let payload = {
        data:{  id:this.selectedrow.id,
                filepath:this.selectedrow.detail_log_path},
        token: this.$store.getters.token
      }
      Download(payload)
      .then((response)=>{
        const href = URL.createObjectURL(response.data);
        // create "a" HTML element with href to file & click
        const link = document.createElement('a');
        link.href = href;
        link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
        document.body.appendChild(link);
        link.click();
        // clean up "a" element & remove ObjectURL
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      })
      },
      resize_vm_cart(){
        this.vm_cart_height = this.$refs.vm_shopping_view.clientHeight+'px'
      },
      complete_to_five_digits(num, digit_count=5) {
        // 将数字转换为字符串
        let numStr = num.toString();

        // 如果长度小于5，前面补零
        while (numStr.length < digit_count) {
            numStr = '0' + numStr;
        }
      
        return numStr;
      }
    },
    beforeDestroy(){
      clearInterval( this.intervaljob )
    }
  }
  </script>
  <style scoped>
  .template-table span{
    
       font-size: 17px
  }
  .show-pwd {
      position: absolute;
      right: 10px;
      top: 7px;
      font-size: 16px;
      cursor: pointer;
      user-select: none;
  }

  .label{
    margin-left:2%;
    margin-bottom:3%;
    width:96%;
    color:rgb(43, 35, 33);
    font-size:large;
    /* border-bottom:0.5px solid rgb(235, 230, 230) */
  }
  .form-label{
    color:gray;
    width:10%;
    float:left;
    margin-left:10%;
  }
  .form-label_os{
    color:gray;
    width:15%;
    float:left;
    margin-left:15%;
  }
  .form-label_half_left{
    color:gray;
    width:20%;
    float:left;
    margin-left:20%;
  }
  .form-label_half_left_small{
    color:gray;
    width:30%;
    float:left;
    margin-left:29%;
  }
  .form-label_cpu{
    color:gray;
    float:left;
    margin-left:28%;
    width:29.5%
  }
  .form-label_core{
    color:gray;
    float:left;
    margin-left:10%;
    width:27%
  }
  .form-label_subnet{
    color:gray;
    float:left;
    margin-left:13%;
    width:18%
  }
  .form-label_half_right{
    color:gray;
    width:20%;
    float:left;
    margin-left:5%
  }
  .form_item{
    margin-left:1%;
  }
  body, html{
   height:100%
  }
  .el-input-group__prepend {
    background-color: #1890ff;
  }
  </style>

  <style lang="scss" >

    #modal{
      .el-dialog{
        width:70%
      }
      .el-dialog__header{
        padding:0px;
      }
      .el-dialog__body {
        padding: 10px 10px;
        width: 900
      }
    }
    .el-form-item__error{
      margin-left:10%
    }
    .form-label_half_left ~ .el-form-item__error{
      margin-left:20%
    }
    .form-label_cpu ~ .el-form-item__error{
      margin-left:28%
    }
    .form-label_subnet ~ .el-form-item__error{
      margin-left:13%
    }
    .form-label_half_right ~ .el-form-item__error{
      margin-left:5%
    }
    .is-always-shadow {
      box-shadow: 1px 1px 3px 0.5px rgba(0, 0, 0, 0.1);
    } 
    .dh-card{
      border-radius: 4px;
      border: 1px solid #e6ebf5;
      background-color: #ffffff;
      overflow: hidden;
      color: #303133;
      transition: 0.3s;
      margin-left:3%;
      margin-bottom: 10px;
      width:96%;
    }

    .dh-card-header {
      border-radius: 4px;
      border: 1px solid #e6ebf5;
      background-color: #ffffff;
      overflow: hidden;
      color: #303133;
      transition: 0.3s;
      height:40px;
      width:100%;
      cursor:pointer;
    }
    .vm-name{
      width:92%;
      height:40px;
      float:left;
      font-size: large;
      text-align: center;
      padding:5px
    }
    .btn-groups{
      width:8%;
      height:40px;
      float:right;
    }
    .dh-btn{
      float:right;
      height:40px;
      border-radius: 0px;
    }
    .collapse-whole{
      float:left;
      margin-left:2%;
      width:100%
    }
    .collapse-left{
      float:left;
      margin-left:2%;
      width:40%
    }
    .collapse-right{
      float:right;
      margin-right:6%;
      width:50%
  }

  .log_table_error{
      color: red
    }
   </style>
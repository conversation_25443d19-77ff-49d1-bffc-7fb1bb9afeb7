$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Launch-Job(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhWhNtxPc -Vars $Vars
            $DhPEs            = Select-DhWhNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CollectionL      = Select-DhWhNtxHost -Vars $Vars
            $CollectionR      = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $CollectionInsert = @()
            $CollectionUpdate = @()
            $LastUpdate       = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        $DhPCs | Foreach-Object -ThrottleLimit 10 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We're now working on '$($_.fqdn)'" -DumpFile $using:DumpFile
            $PC         = $_
            $DictR      = $using:CollectionR
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -PWord (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication" -DumpFile $using:DumpFile
                return
            }
            if ($PrismCall1 = Rest-Prism-v3-List-Host -Fqdn $PC.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "$($PrismCall1.count) hosts are available in '$($PC.Fqdn)'" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling '$($PC.Fqdn)' for list hosts" -DumpFile $using:DumpFile
                continue
            }
            foreach ($H in $PrismCall1) {
                if ($H.status.name) {
                    $PE = $using:DhPEs | Where-Object {$_.uuid -eq $H.status.cluster_reference.uuid}
                    $R  = [PSCustomObject]@{
                        'name'            = $H.status.name.ToUpper()
                        'uuid'            = $H.metadata.uuid
                        'pe_name'         = $PE.name
                        'pe_fqdn'         = $PE.fqdn
                        'pe_uuid'         = $PE.uuid
                        'disk_number'     = $H.status.resources.host_disks_reference_list.Length
                        'sn'              = $H.status.resources.serial_number
                        'model'           = $H.status.resources.block.block_model
                        'memory'          = [int][Math]::Ceiling($H.status.resources.memory_capacity_mib / 1024)
                        'cpu_core_number' = $H.status.resources.num_cpu_cores
                        'cpu_model'       = $H.status.resources.cpu_model
                        'ahv_ip'          = $H.status.resources.hypervisor.ip
                        'cvm_ip'          = $H.status.resources.controller_vm.ip
                        'ipmi_ip'         = $H.status.resources.ipmi.ip
                        'pe_id'           = $PE.id
                        'status'          = 'Running'
                        'last_update'     = $using:LastUpdate
                    }
                    $DictR.Add($R)
                }
            }
        }
        if (0 -eq $CollectionR.Count) {
            Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "There is no data need to deal with. Exit" -DumpFile $DumpFile
            Exit 0
        }
        $CollectionR | ForEach-Object {
            if ($_.sn -notin $CollectionL.sn) {
                $CollectionInsert += $_
            }else {
                $CollectionUpdate += $_
            }
        }
        $CollectionL | ForEach-Object {
            if ($_.sn -notin $CollectionR.sn) {
                $L = [PSCustomObject]@{
                    'name'            = $_.name
                    'uuid'            = $_.uuid
                    'pe_name'         = $_.pe_name
                    'pe_fqdn'         = $_.pe_fqdn
                    'pe_uuid'         = $_.pe_uuid
                    'disk_number'     = $_.disk_number
                    'sn'              = $_.sn
                    'model'           = $_.model
                    'memory'          = $_.memory
                    'cpu_core_number' = $_.cpu_core_number
                    'cpu_model'       = $_.cpu_model
                    'ahv_ip'          = $_.ahv_ip
                    'cvm_ip'          = $_.cvm_ip
                    'ipmi_ip'         = $_.ipmi_ip
                    'ipmi_version'    = $_.ipmi_version
                    'pe_id'           = $_.pe_id
                    'status'          = 'Decommissioned'
                    'last_update'     = $LastUpdate
                }
                $CollectionUpdate += $L
            }
        }
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_wh_ntx_host]" -DumpFile $DumpFile
        Insert-Table-DhWhNtxHost -Vars $Vars -Collection $CollectionInsert
        Update-Table-DhWhNtxHost-BySn -Vars $Vars -Collection $CollectionUpdate
    }    
}
Launch-Job
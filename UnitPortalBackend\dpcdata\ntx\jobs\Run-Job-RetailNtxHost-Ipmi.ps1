$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Launch-Job(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        try {
            $Vars             = Load-Vars
            $DhHosts          = Select-DhRetailNtxHost -Vars $Vars
            $DhOneViews       = Select-DhOneView -Vars $Vars
            $Ipmis            = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $CollectionUpdate = @()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        #Rolling call each OneView
        $DhOneViews | Foreach-Object -ThrottleLimit 5 -Parallel {
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on $($_.fqdn)" -DumpFile $using:DumpFile
            $OneView   = $_
            $DictIpmis = $using:Ipmis
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage "oneview_call" | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account" -DumpFile $using:DumpFile
                return
            }
            
            if ($OneViewCall1 = Rest-OneView-List-Server -Fqdn $OneView.fqdn -Username $SvcAccount.username -PWord (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted) -Domain 'ikea.com') {
                $OneViewCall1 = $OneViewCall1 | Where-Object {$_.state -ne "Unmanaged"}
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "'$($OneViewCall1.count)' IPMI profiles are available '$($OneView.fqdn)'" -DumpFile $using:DumpFile
                $OneViewCall1 | Foreach-Object {
                    $Ipmi = [PSCustomObject]@{
                        'sn'               = $_.serialNumber
                        'ipmi_version'     = $_.mpModel + " " + $_.mpFirmwareVersion
                        'bios_version'     = $_.romVersion
                        'smart_controller' = [PSCUstomObject]@{
                            'model'   = ($_.subResources.LocalStorage.data | where-object {$_.AdapterType -eq "SmartArray"} | Select-Object -First 1).Model
                            'version' = ($_.subResources.LocalStorage.data | where-object {$_.AdapterType -eq "SmartArray"} | Select-Object -First 1).FirmwareVersion.Current.VersionString
                        }
                    }
                    $DictIpmis.Add($Ipmi)
                }
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling '$($OneView.fqdn)' for list IPMIs" -DumpFile $using:DumpFile
                return
            }
        }
        foreach ($H in $DhHosts) {
            $L = [PSCustomObject]@{
                'name'               = $H.name
                'ipmi_version'       = "NA"
                'bios_version'       = "NA"
                'controller_version' = "NA"
                'controller_model'   = "NA"
            }
            if ($Ipmi = $Ipmis | Where-Object {$_.sn -eq $H.sn}) {
                $L.ipmi_version       = if($null -ne $Ipmi.ipmi_version) {$Ipmi.ipmi_version} else {"NA"}
                $L.bios_version       = if($null -ne $Ipmi.bios_version) {$Ipmi.bios_version} else {"NA"}
                $L.controller_version = if($null -ne $Ipmi.smart_controller.version) {$Ipmi.smart_controller.version} else {"NA"}
                $L.controller_model   = if($null -ne $Ipmi.smart_controller.model) {$Ipmi.smart_controller.model} else {"NA"}
            }
            $CollectionUpdate += $L
        }
    } 
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_host]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxHost-ByName -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job
from .database import db, ma


class ModelDHCompensationGain(db.Model):
    __tablename__      = 'dh_compensation_gain'
    id                 = db.Column(db.Integer , primary_key=True)
    start_date         = db.Column(db.String(50))
    end_date           = db.Column(db.String(50))
    username           = db.Column(db.String(200))
    reason             = db.Column(db.String(255))
    total_hours        = db.Column(db.String(50))
    times_1_hours      = db.Column(db.String(50)) 
    times_1p5_hours    = db.Column(db.String(50)) 
    times_2_hours      = db.Column(db.String(50)) 
    gain_hours         = db.Column(db.String(50)) 
    detail             = db.Column(db.String(255))


class ModelDHCompensationGainSchema(ma.Schema):
    class Meta:
        fields = ('id', 'start_date', 'end_date', 'username', 'reason', 'total_hours', 'times_1_hours', 'times_1p5_hours', 'times_2_hours', 'gain_hours', 'detail')


class ModelDHCompensationUse(db.Model):
    __tablename__      = 'dh_compensation_use'
    id                 = db.Column(db.Integer , primary_key=True)
    start_date         = db.Column(db.String(50))
    end_date           = db.Column(db.String(50))
    username           = db.Column(db.String(200))
    reason             = db.Column(db.String(255))
    use_hours        = db.Column(db.String(50)) 
    detail             = db.Column(db.String(255))


class ModelDHCompensationUseSchema(ma.Schema):
    class Meta:
        fields = ('id', 'start_date', 'end_date', 'username', 'reason', 'use_hours', 'detail')
# pylint: disable=unused-argument
# installed modules
import sys
import uuid
import json
import logging
import traceback
import sqlalchemy.exc
import werkzeug.exceptions as flaskex
from sqlalchemy import or_
from marshmallow import fields
from flask_restful import Resource
# sys.path.insert(0,'..')
# sys.path.insert(0,'../..')
from flask_apispec.views import MethodResource
from flask import abort, request, Response, jsonify
from flask_apispec import marshal_with, doc, use_kwargs

# local files
from models.database import db

from business.authentication.group import Group
from business.generic.commonfunc import compare_time_diff
from business.authentication.tokenvalidation import PrivilegeValidation
from models.auth_models import ModelRole, ModelUser, ModelUserGroupRoleMapping, ModelGroup
from business.authentication.authentication import LdapAdmin, User, UserRole, Role, UserGroupRoleMapping
from swagger.rbac_role_schema import DeleteRbacRoleRequestSchema, UpdateRbacRoleRequestSchema, CreateRbacRoleRequestSchema
from swagger.authentication_schema import LoginResponseSchema, CreateUserRequestSchema, DeleteUserRequestSchema, UpdateUserRoleRequestSchema
from .route import route


@route("/api/v1/login")
class RestfulLogin(MethodResource, Resource):
    @doc(description="Login API", tags=["Authentication"])
    @use_kwargs(
        {"Authorization": fields.Str(required=True, description="Basic Auth, (username, password)")},
        location="headers",
        apply=False,
    )
    @marshal_with(LoginResponseSchema, code=200)
    def post(self):
        username, password = request.authorization.username, request.authorization.password
        logging.info(f"User {username} is trying to login.")
        if not (username and password):
            raise flaskex.Unauthorized('username or password not defined.')
        user = User()
        try:
            user_info = ModelUser.query.filter(or_(ModelUser.username == username, ModelUser.memo_id == username)).one()
        except sqlalchemy.exc.NoResultFound:
            raise flaskex.BadRequest(f"Can't find user with username or memoid {username} in unit portal database!")
        except sqlalchemy.exc.MultipleResultsFound:
            raise flaskex.BadRequest(f"Found multiple user with username or memoid {username} in unit portal database!")
        if user_info.domain == "ikea.com":
            if not "@ikea.com" in username:     # user using memo id to login
                username += "@ikea.com"
            ldapadmin = LdapAdmin(username, password)
            res = ldapadmin.login()
            if not res:
                raise flaskex.Unauthorized("Wrong password!")
        else:                                   # local account.
            user.validate_local_user(username, password)
        logging.info("User login succeeded.")

        if not user_info.token or compare_time_diff(user_info.lastlogondate):
            #if token doesn't exist  or  token invalid
            logging.info("Updating user token...")
            token = str(uuid.uuid4())
            user_info = user.update_token_to_user(uid=user_info.id, token=token)
        user_role = UserRole(user_info.id)
        privilege = user_role.get_frontend_privilege()
        all_privilege = user_role.get_all_privileges()
        js = {
            'roles': user_info.role.split(','),
            'token': user_info.token,
            'name': user_info.username,
            'privilege': privilege,
            'all_privilege': all_privilege
        }
        return Response(json.dumps(js), status=200, mimetype='application/json')


@route("/api/v1/role")
class RestfulRole(Resource):
    def post(self):
        try:
            token = request.get_json(force=True)
            if not token:
                raise Exception({'code': 404, 'message': 'token cannot be empty.'})
            user = User()
            res, usr = user.get_role_by_token(token['token'])       # TODO:get token from header instead of payload
            if res and usr:
                user_role = UserRole(usr.id)
                privilege = user_role.get_frontend_privilege()
                all_privilege = user_role.get_all_privileges()
                js = {'roles': usr.role.split(','), 'token': token['token'], 'name': usr.username, 'privilege': privilege, 'all_privilege': all_privilege}
                return Response(json.dumps(js), status=200, mimetype='application/json')
            raise Exception({'code': 400, 'message': usr})
        except flaskex.BadRequest:
            abort(400, "Please make sure that you have a valid payload.")
        except Exception as e:
            if e.args:
                if type(e.args[0]) == dict:
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
                    else:
                        abort(500, e)
                else:
                    abort(500, e)
            else:
                abort(500, e)


@route("/api/v1/role/list")
class RestfulRoleList(Resource):
    def get(self):
        role = Role()
        role_list = role.get_role_list()
        mapping = UserGroupRoleMapping(1) # just a random init param, can be any intger
        mapping_list = mapping.get_mapping_list()
        res = list()
        for role in role_list:
            role['assigned_user'] = len([map for map in mapping_list if map['role_id'] == role['id']])
            res.append(role)
        return jsonify(res)


@route("/api/v1/user/list")
class RestfulUserList(Resource):
    def get(self):
        try:
            users = User()
            return jsonify(users.get_user_info_from_db())
        except Exception as e:
            abort(500, f'Failed to get user list, error:{str(e)}')


@route(["/api/v1/rbac_role", "/api/v1/rbac_role/<role_id>"])
class RestfulRbacRole(MethodResource, Resource):
    @doc(description="Create RBAC role", tags=["Admin"])
    @PrivilegeValidation(privilege={"role_administration": "view_role"})
    @use_kwargs(CreateRbacRoleRequestSchema(), location="json")
    def post(self, *args, **kwargs):  # pylint: disable=W0613
        """Create a new RBAC role"""
        param = request.get_json(force=True)
        Role().create_rbac_role(param)

    @doc(description="Update RBAC role", tags=["Admin"])
    @PrivilegeValidation(privilege={"role_administration": "view_role"})
    @use_kwargs(UpdateRbacRoleRequestSchema(), location="json")
    def put(self, *args, **kwargs):  # pylint: disable=W0613
        """
        Update an existing RBAC role, including its sub-roles (overwrite)
        """
        body = request.get_json(force=True)
        role_id = body.get("role_id", None)
        description = body.get("description", None)
        privilege = body["privilege"]
        Role().update_rbac_role(role_id, description, privilege)
        return {"message": "success"}, 200

    @doc(description="Delete RBAC role", tags=["Admin"])
    @PrivilegeValidation(privilege={"role_administration": "view_role"})
    @use_kwargs(DeleteRbacRoleRequestSchema(), location="json")
    def delete(self, *args, **kwargs):  # pylint: disable=W0613
        """Delete an existing RBAC role"""
        body = request.get_json(force=True)
        role_id = body["role_id"]
        ModelRole().delete_a_role(role_id)

    @doc(description="Get RBAC role", tags=["Admin"])
    @PrivilegeValidation(privilege={"role_administration": "view_role"})
    def get(self, role_id):
        """Get an existing RBAC role and all the sub roles"""
        try:
            return Role().get_rbac_role(role_id), 200
        except sqlalchemy.exc.NoResultFound:
            abort(500, 'Cannot find a role with this id.')
        except Exception:
            abort(500, f'Failed to get the role, error:{str(repr(traceback.format_exception(sys.exception())))}')


@route("/api/v1/groups/role")
class RestfulGroupsRole(Resource):

    # deprecated
    # def post(self):
    #     # Assign role for given group and its subgroups
    #     body = request.get_json(force=True)
    #     try:
    #         GroupRoleMapping(group_name=body["group_name"]).add_mappings_for_group(role_ids=body["role_ids"])
    #         return {"message": "success"}, 200
    #     except Exception as e:
    #         msg = f"Failed to add mappings for group {body['group_name']}"
    #         logging.error(msg)
    #         logging.error(str(e))
    #         return {"message": msg, "error_message": str(e)}, 500

    # def put(self):
    #     """update roles of a group (overwrite)"""
    #     body = request.get_json(True)
    #     group_name = body["group_name"]
    #     role_ids = body["role_ids"]
    #     try:
    #         GroupRoleMapping(group_name=group_name).update_group_role_mapping(role_ids)
    #         return {"message": "success"}, 200
    #     except Exception as e:
    #         msg = f"Failed to add mappings for group {body['group_name']}"
    #         logging.error(msg)
    #         logging.error(str(e))
    #         return {"message": msg, "error_message": str(e)}, 500

    def delete(self):
        # delete role for a group
        pass


@route("/api/v1/user/role")
class RestfulUserRbacRole(MethodResource, Resource):
    def get(self):
        """
        Get all assigned roles of the requested user
        request param:
            user_name: <EMAIL>
        request example:
            curl 'localhost:5888/api/v1/user/role?user_name=gstyiwxu2%40ikea.com'
        """
        user_name = request.args.get("user_name")
        user_id = ModelUser().query.filter_by(username=user_name).one().id
        user_role = UserRole(user_id)
        return user_role.get_all_privileges()

    # def post(self):
    #     """
    #     Assign roles to a user
    #     """
    #     body = request.get_json(True)
    #     user_id = body["user_id"]
    #     role_ids = body["role_ids"]
    #     ModelUserGroupRoleMapping().assign_role_for_user(user_id, role_ids)
    #     return {"message": "success"}, 200
    #
    # def delete(self):
    #     """
    #     Delete roles from a user
    #     e.g.
    #     curl --location --request DELETE 'localhost:5888/api/v1/user/role' \
    #     --header 'Content-Type: application/json' \
    #     --header 'Authorization: Basic xxx' \
    #     --data '{
    #         "user_id": 12,
    #         "role_ids": [1,2,3,4]
    #     }'
    #     """
    #     body = request.get_json(True)
    #     user_id = body["user_id"]
    #     role_ids = body["role_ids"]
    #     ModelUserGroupRoleMapping().unassign_role_from_user(user_id, role_ids)
    #     return {"message": "success"}, 200

    @doc(description="Update user roles", tags=["Admin"])
    @PrivilegeValidation(privilege={"role_administration": "view_role"})
    @use_kwargs(UpdateUserRoleRequestSchema(), location="json")
    def put(self, *args, **kwargs):  # pylint: disable=W0613
        """Edit a user's role"""
        body = request.get_json(True)
        user_id = body["user_id"]
        role_ids = body["role_ids"]
        UserRole().update_user_roles(user_id, role_ids)


@route(["/api/v1/user", "/api/v1/user/<user_id>"])
class RestfulUser(MethodResource, Resource):

    @doc(description="Get user info", tags=["Admin"])
    @PrivilegeValidation(privilege={"role_administration": "view_user"})
    def get(self, user_id):
        """Create a single user"""
        if not user_id:
            logging.warning("User id not provided.")
            return "User id not provided.", 400
        roles = ModelUserGroupRoleMapping.query \
            .filter(ModelUserGroupRoleMapping.user_id == user_id) \
            .filter(ModelUserGroupRoleMapping.group_id == 0).all()
        return [role.role_id for role in roles], 200

    @doc(description="Create user", tags=["Admin"])
    @PrivilegeValidation(privilege={"role_administration": "view_user"})
    @use_kwargs(CreateUserRequestSchema(), location="json")
    def post(self, *args, **kwargs):  # pylint: disable=W0613
        """Create a single user"""
        body = request.get_json(force=True)
        username = body["name"]
        role_ids = body.get("role_ids")
        new_user = User().create_new_user(username)
        if not role_ids:
            logging.warning("User is not given an initial role.")
            return
        ModelUserGroupRoleMapping().assign_role_for_user(new_user.id, role_ids)

    @doc(description="Delete user", tags=["Admin"])
    @PrivilegeValidation(privilege={"role_administration": "view_user"})
    @use_kwargs(DeleteUserRequestSchema(), location="json")
    def delete(self, *args, **kwargs):  # pylint: disable=W0613
        """delete a single user"""
        body = request.get_json(force=True)
        user_id = body['id']
        ModelUser().delete_user(user_id=user_id)
        return {"message": "success"}, 200
            
    # def put(self):
    #     """update a user"""
    #     body = request.get_json(force=True)
    #     username = body['username']
    #     role_id = body["role"]
    #     try:
    #         ModelUser().put(user_name=username,roleid= role_id, is_single_transaction=False)
    #         db.session.commit()
    #         return {"message": f"Successfully updated the user {username}"}, 200
    #     except Exception as e:
    #         db.session.rollback()
    #         logging.error(f"Delete user failed. Reason: {e.with_traceback(sys.exc_info()[2])}")
    #         return {"message": "Delete user failed.", "error_message": str(e)}, 500


@route("/api/v1/group")
class RestfulGroup(Resource):
    def get(self):
        """Get group list"""
        try:
            groups = Group()
            return jsonify(groups.get_group_info_from_db())
        except Exception as e:
            abort(500, f'Failed to get group list, error:{str(e)}')

    @PrivilegeValidation(privilege={"role_administration": "view_user"})
    @use_kwargs(CreateUserRequestSchema(), location="json")
    def post(self):
        """Create a group with initial role"""
        body = request.get_json(force=True)
        group_name = body["name"]
        role_ids = body["role_ids"]
        Group().create_group(group_name, role_ids)
        return {"message": "success"}, 200

    @PrivilegeValidation(privilege={"role_administration": "view_user"})
    @use_kwargs(DeleteUserRequestSchema(), location="json")
    def delete(self):
        """delete a group"""
        body = request.get_json(force=True)
        groupname = body['payload']['groupname']
        try:
            # ModelUserGroupRoleMapping().delete_mappings_by_user(group_name=groupname)
            ModelGroup().delete(group_name=groupname, is_single_transaction=False)
            db.session.commit()
            return {"message": "success"}, 200
        except Exception as e:
            db.session.rollback()
            logging.error(f"Delete group failed. Reason: {e.with_traceback(sys.exc_info()[2])}")
            return {"message": "Delete group failed.", "error_message": str(e)}, 500

    def put(self):
        """update a group"""
        body = request.get_json(force=True)
        groupname = body['groupname']
        role_id = body["role"]
        try:
            ModelGroup().put(group_name = groupname, roleid= role_id, is_single_transaction=False)
            db.session.commit()
            return {"message": f"Successfully updated the group {groupname}"}, 200
        except Exception as e:
            db.session.rollback()
            logging.error(f"Delete group failed. Reason: {e.with_traceback(sys.exc_info()[2])}")
            return {"message": "Delete group failed.", "error_message": str(e)}, 500

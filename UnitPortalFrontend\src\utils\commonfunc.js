export function fetch_domain(pc){
    if(pc.match(/.*ikeadt.com.*/i)){
      return '.ikeadt.com'
    }
    if(pc.match(/.*ikead2.com.*/i)){
      return '.ikead2.com'
    }
    return ".ikea.com"
  } 

export function fetch_env(pc){
    if(pc.match(/.*ikeadt.com.*/i)){
      return 'TEST'
    }
    if(pc.match(/.*ikead2.com.*/i)){
      return 'DEV'
    }
    return "PROD"
  }

export function customized_search(str, search){
  console.log(str, search);
  
  
  if(!str){
    return false
  }
  if(["number","string"].includes(typeof(str))){
    // check if str is either a number or a string
    return str.toString().toLowerCase().search(search) != -1
  }
  return false
}
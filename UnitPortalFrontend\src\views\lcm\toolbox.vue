<template>
  <div>


  <div class="toolbox_panel" v-show="view.toolbox_panel_view">
    <el-card class="box-card" >
      <div slot="header">
          <div style="font-size:25px">
              VM related
          </div>
      </div>
      <el-row :gutter="40" class="panel-group">
        <el-col :lg="5" class="card-panel-col">
          <div class="card-panel" >
            <div class="btn_sibling" style="border-bottom: 1px solid grey; overflow: hidden;height:68%">
              <div class="card-panel-icon-wrapper icon-people">
                <svg-icon icon-class="cleanup" class-name="card-panel-icon" />
              </div>
              <div class="card-panel-description">
                <div class="card-panel-text">
                  VM cleanup
                </div>
              </div>
            </div>
            <div style="clear: both;"></div>
            <div style="position: absolute;bottom: 0;width:100%;height:32%;">

            <el-button-group style="width:101%;height:100%">
              <el-button type="danger" style="height:100%;width:50%;border-radius: 0px;" @click="show_vm_cleanup_modal = true">Execute</el-button>
              <el-button type="primary" style="height:100%;width:50%;border-radius: 0px;" @click="change_view('vm_cleanup_table_view')">Details</el-button>
            </el-button-group>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="box-card" style="margin-top: 20px">
      <div slot="header">
          <div style="font-size:25px">
              Account related
          </div>
      </div>
      <el-row :gutter="40" class="panel-group">
        <el-col :lg="5" class="card-panel-col">
          <div class="card-panel" >
            <div class="btn_sibling" style="border-bottom: 1px solid grey; overflow: hidden;height:68%">
              <div class="card-panel-icon-wrapper icon-people">
                <svg-icon icon-class="unlock" class-name="card-panel-icon" />
              </div>
              <div class="card-panel-description">
                <div class="card-panel-text">
                  Unlock Account
                </div>
              </div>
            </div>
            <div style="clear: both;"></div>
            <div style="position: absolute;bottom: 0;width:100%;height:32%;">
              <el-button-group style="width:101%;height:100%">
                <el-button type="danger" style="height:100%;width:50%;border-radius: 0px;" @click="show_unlock_account_modal = true">Execute</el-button>
                <el-button type="primary" style="height:100%;width:50%;border-radius: 0px;" @click="change_view('unlock_account_table_view')">Details</el-button>
              </el-button-group>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>


  <div v-show="view.vm_cleanup_table_view">
    <div style="margin:15px"> 
      <el-button @click="change_view('toolbox_panel_view')" type="primary"> back</el-button>
    </div>

    <div style="margin:15px"> 
      <el-table
        border
        fit
        highlight-current-row
        style="width: 100%;"
        :data = "vm_task_list"
      >
      <el-table-column label="ID" prop="id"  align="center" min-width="8%" >
        <template slot-scope="{row}">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Prism" min-width="10%" align="center" >
        <template slot-scope="{row}">
          <span>{{ row.pe }}</span>
        </template>
      </el-table-column>

      <el-table-column label="executor" min-width="8%" align="center">
        <template slot-scope="{row}">
          <span>{{ row.operator }}</span>
        </template>
      </el-table-column>


      <el-table-column label="Latest log" min-width="12%" align="center">
        <template slot-scope="{row}">
          <span>{{ row.latestlog.log_info || row.latestlog.loginfo }}</span><span class="link-type" @click="handle_cleanup_brief_log(row)">     More</span>
        </template>
      </el-table-column>

      <el-table-column label="Start Date(UTC)" min-width="10%" align="center">
        <template slot-scope="{row}">
          <span>{{ row.create_date }}</span>
        </template>
      </el-table-column>

      <el-table-column label="status" min-width="6%" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusFilter">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    </div>
  </div>

  <!-- VM cleanup dialog -->
  <el-dialog
   :visible.sync="show_vm_cleanup_modal" 
   :title="'VM Cleanup'" 
   style="width:60%;margin-left:20%" 
   :close-on-click-modal="false"
   >
      <el-form 
        ref="vm_cleanup_form" 
        label-position="left" 
        style="width: 100%;"
        :rules="vm_cleanup_form_rules"
        :model="vm_cleanup_form"

        >
          <el-form-item  prop="pc" >
            <div class="form-label"><span>Prism</span></div> 
            <el-select v-model="vm_cleanup_form.pc" style="width:100%" filterable>
              <el-option v-for="item in pc_list" :key="item" :value="item" :label="item" />
            </el-select>
          </el-form-item>
          
          <el-form-item  prop="pe" >  
            <div class="form-label"><span>PE</span></div>
            <el-select 
              v-model="vm_cleanup_form.pe" 
              style="width:100%" 
              filterable
              @change="load_vm_list">
              <el-option v-for="item in pe_pc_correspondence_list[vm_cleanup_form['pc']]" :key="item" :value="item" :label="item" />
            </el-select>
          </el-form-item>

          <el-form-item  prop="vm" >  
            <div class="form-label"><span style="margin-right:10%">VM</span>  
              <el-switch
                v-model="vm_cleanup_form.vm_input_type"
                size="large"
                active-text="Manually input"
                inactive-text="Select from DB"
                style="width:35%;float:right;min-width: 250px"
              />
            </div>

            <el-select 
              v-model="vm_cleanup_form.vm" 
              style="width:100%" 
              filterable 
              :loading="vm_cleanup_form.loading"
              loading-text="Reading VM list from DB"
              v-show="!vm_cleanup_form.vm_input_type">
              <el-option v-for="item in vm_list" :key="item.name" :value="item.name" :label="item.name"/>
            </el-select>

            <el-input v-model="vm_cleanup_form.vm" clearable v-show="vm_cleanup_form.vm_input_type"/>
          </el-form-item>
        </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="danger" @click="validate_vm_cleanup_form">Cleanup !</el-button>
      </span>
  </el-dialog>  
  <!-- VM cleanup confirm dialog -->
  <el-dialog 
    :visible.sync="show_vm_cleanup_confirm_modal" 
    :title="'VM Cleanup Confirm'" 
    style="width:30%;margin-left:35%;margin-top:5%"
    :close-on-click-modal="false"
     >
      <el-form 
        ref="vm_cleanup_confim_form" 
        label-position="left" 
        style="width: 100%;"
        :rules="vm_cleanup_confirm_form_rules"
        :model="vm_cleanup_form">
          <el-form-item prop="vm_confirm">  
            <div class="form-label"><span>Please type <span style="color:red">{{ vm_cleanup_form.vm }}</span> to confirm</span></div> 
            <el-input 
              v-model="vm_cleanup_form.vm_confirm" 
              clearable 
              @paste.native.capture.prevent='disable_copy_paste'
              @copy.native.capture.prevent='disable_copy_paste'
              :class="{'apply-shake':css.shake_now}"
              />
          </el-form-item>
        </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button type="danger" @click="cleanup_vm" >Confirm !</el-button>
      </span>
  </el-dialog>
  <!-- VM cleanup task brief log dialog -->
  <el-dialog :visible.sync="show_vm_cleanup_detail_log_modal" :title="'Workload cleanup log(brief)'" >

    <el-table :data="workload_cleanup_task_log" 
      border 
      fit 
      highlight-current-row 
      style="width: 100%" 
      max-height="500" 
    >
      <el-table-column prop="key" label="log date"  min-width="25%"  style="background-color: red;">
        <template slot-scope="{row}"  style="background-color: red;">
          <span :style="{'color':pick_log_color(row)}">{{ row.logdate }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="pv" label="log info" min-width="55%"  >
        <template slot-scope="{row}">
          <span :style="{'color':pick_log_color(row)}">{{ row.loginfo }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="pv" label="log severity"  min-width="10%"  >
        <template slot-scope="{row}">
          <span :style="{'color':pick_log_color(row)}">{{ row.severity }}</span>
        </template>
      </el-table-column>
    </el-table>

    <span slot="footer" class="dialog-footer">
      <el-button type="warning" @click="downloadLogFile()">Download Detail Log</el-button>
      <el-button type="primary" @click="show_vm_cleanup_detail_log_modal = false">OK</el-button>

    </span>
  </el-dialog>

  <div v-show="view.unlock_account_table_view">
    <div style="margin:15px"> 
      <el-button @click="change_view('toolbox_panel_view')" type="primary">back</el-button>
    </div>
    <el-table
      :data="unlock_account_task_list"
      border
      style="width: 100%"
      @row-click="handle_unlock_account_row_click">
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="Operator" min-width="10%" align="center">
        <template slot-scope="{row}">
          <span>{{ row.creater }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Latest log" min-width="12%" align="center">
        <template slot-scope="{row}">
          <span>{{ row.latestlog ? row.latestlog.log_info : '' }}</span>
          <span class="link-type" @click="handle_unlock_account_brief_log(row)"> More</span>
        </template>
      </el-table-column>
      <el-table-column label="Date(UTC)" min-width="10%" align="center">
        <template slot-scope="{row}">
          <span>{{ row.create_date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Status" min-width="6%" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusFilter">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>


  <el-dialog
    :visible.sync="show_unlock_account_modal" 
    :title="'Unlock Account'" 
    style="width:60%;margin-left:20%" 
    :close-on-click-modal="false">
    <el-form 
      ref="unlock_account_form" 
      label-position="left" 
      style="width: 100%;"
      :rules="unlock_account_form_rules"
      :model="unlock_account_form">
      <el-form-item prop="pe_name">  
        <div class="form-label">
          <span>PE Name</span>
          <el-tooltip 
            class="item" 
            effect="dark" 
            content="Currently unlocks CVM's admin, nutanix and 1-click-nutanix accounts. AHV account unlocking will be supported in the future." 
            placement="top">
            <span style="margin-left: 5px; cursor: pointer; font-size: 16px;">💡</span>
          </el-tooltip>
        </div> 
        <el-input 
          v-model="unlock_account_form.pe_name" 
          clearable 
          placeholder="e.g. RETCN888-NXC000" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="danger" @click="validate_unlock_account_form">Unlock!</el-button>
    </span>
  </el-dialog>

  <el-dialog 
    :visible.sync="show_unlock_account_detail_log_modal" 
    :title="'Account unlock log(brief)'">
    <el-table
      :data="unlock_account_task_log"
      border
      style="width: 100%">
      <el-table-column label="Time" prop="time" align="center" width="180" />
      <el-table-column label="Severity" prop="severity" align="center" width="100">
        <template slot-scope="{row}">
          <span :style="{color: pick_log_color(row)}">{{ row.severity }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Message" prop="message" align="left" />
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button type="warning" @click="downloadUnlockLogFile()">Download Detail Log</el-button>
      <el-button type="primary" @click="show_unlock_account_detail_log_modal = false">OK</el-button>
    </span>
  </el-dialog>
</div>
</template>
  
  <script>
  import { GetPCPECorrespondence, GetClusterVMList, GetCleanupWorkloadTaskList, CleanupWorkload, GetWorkloadTaskLogs, Download, GetUnlockAccountTaskList, GetUnlockAccountTaskLogs, UnlockAccount } from '@/api/nutanix'
  import waves from '@/directive/waves' // waves directive
  import { parseTime } from '@/utils'
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination

  export default {
    name: 'Toolbox',
    components: { Pagination },
    directives: { waves },
    filters: {
    statusFilter(status) {
        let st = status.toLowerCase();
        const statusMap = {
          'not started': 'info',
          'done': 'success',
          'error': 'danger',
          'in progress':'primary',
          'aborted':'warning'
        }
        return statusMap[st]
      }
    },
    data() {
      const vm_name_validator = (rule, value, callback) => {
        if(this.vm_cleanup_form.vm == this.vm_cleanup_form.vm_confirm){
          callback()
        }
        else{
          callback(new Error("Please confirm the vm name."))
        }
      }
      
      const load_cleanup_workload_list = () => {
        GetCleanupWorkloadTaskList().then(
          response => {
            console.log(response)
            this.vm_task_list = response.data
          }
        )
      }
      
      const load_unlock_account_task_list = () => {
        GetUnlockAccountTaskList().then(
          response => {
            console.log(response)
            this.unlock_account_task_list = response.data
          }
        )
      }
      
      const pe_name_validator = (rule, value, callback) => {
        if (!value) {
          callback(new Error('Please enter a PE name'))
          return
        }
        
        if (!value.toUpperCase().includes('NXC')) {
          callback(new Error("PE Name like 'RETCN888-NXC000'"))
          return
        }
        
        callback()
      }
      
      return {
        workload_cleanup_task_log:[],
        cleanup_task_selected_row:'',
        view:{
          toolbox_panel_view : true,
          vm_cleanup_table_view : false,
          unlock_account_table_view: false,
        },
        view_execution:{
          vm_cleanup_table_view:[load_cleanup_workload_list],
          toolbox_panel_view:[],
          unlock_account_table_view: [load_unlock_account_task_list],
        },
        show_vm_cleanup_modal:false,
        show_vm_cleanup_confirm_modal:false,
        show_vm_cleanup_detail_log_modal:false,
        vm_cleanup_form:{
          pc:'',
          pe:'',
          vm:'',
          vm_confirm:'',
          vm_input_type:false,
          loading:true,
        },
        pc_list:[],
        vm_list:[],
        vm_task_list:[],
        pe_pc_correspondence_list:[],
        vm_cleanup_form_rules:{
          pc:{ required: true, message: 'Please select a Prism', trigger: 'blur' },
          pe:{ required: true, message: 'Please select a PE', trigger: 'blur' },
          vm:{ required: true, message: 'Please select a VM', trigger: 'blur' },
        },
        vm_cleanup_confirm_form_rules:{
          vm_confirm:{ required: true, message:'Please key in the VM name.', trigger: 'blur', validator:vm_name_validator}
        },
        css:{
          shake_now:false
        },
        unlock_account_task_log: [],
        unlock_account_task_list: [],
        unlock_account_selected_row: '',
        show_unlock_account_modal: false,
        show_unlock_account_detail_log_modal: false,
        unlock_account_form: {
          pe_name: '',
        },
        unlock_account_form_rules: {
          pe_name: { required: true, validator: pe_name_validator, trigger: 'blur' },
        },
      }
    },
    created(){

      this.load_pc_pe_list()
    },
    computed: {

    },
    methods: {
      load_pc_pe_list(){
        GetPCPECorrespondence().then(response => { 
          console.log(response)

          response['data'].map(cor=>{
            this.pe_pc_correspondence_list[cor['pc']] = cor['pe']
          })
          this.pc_list = Object.keys(this.pe_pc_correspondence_list)
        })
      },
      load_vm_list(){
        GetClusterVMList({pe:this.vm_cleanup_form.pe, include_cvm:false, is_available:true}).then(
          response => {
            this.vm_cleanup_form.loading = true
            this.vm_list = response.data.sort((a, b) => a.name.localeCompare(b.name))
            this.vm_cleanup_form.loading = false
          }
        )
      },
      disable_copy_paste(){
        console.log("disable_copy_paste")
      },
      validate_vm_cleanup_form(){
        console.log('validating vm cleanup form')
        this.$refs['vm_cleanup_form'].validate(
          (valid)=>{
            this.show_vm_cleanup_confirm_modal = valid
          }
        )
      },
      cleanup_vm(){
        this.$refs.vm_cleanup_confim_form.validate((valid)=>{
          if(valid){
            let payload = [{
              name : this.vm_cleanup_form.vm,
              pe : this.vm_cleanup_form.pe
            }]
            console.log(payload)
            CleanupWorkload(payload).then(
              response =>{
                this.$notify({
                  title: 'Task started',
                  message: response.data.message||"task started",
                  type: 'success',
                  duration: 2000
                })
                this.vm_cleanup_form.pc=''
                this.vm_cleanup_form.pe=''
                this.vm_cleanup_form.vm=''
                this.vm_cleanup_form.vm_confirm=''
              }
            ).catch(
              error =>{
                this.$notify({
                  title: 'Task failed',
                  message: error.response.data.message,
                  type: 'error',
                  duration: 10000
                })
              }
            ).finally(
              ()=>{
                this.show_vm_cleanup_modal=false
                this.show_vm_cleanup_confirm_modal=false
              }

            )
          }
          else{
            this.css.shake_now = true
            setTimeout(()=>{
              this.css.shake_now = false;
            }, 1000)
          }
        })

      },
      change_view(view){
        //change view from toolbox to detail logs，or the other way around
        Object.keys(this.view).forEach(
          key =>{
              this.view[key] = key == view
          }
        )
        this.view_execution[view].map(
          func =>{
            func()
          }
        )
      },
      handle_cleanup_brief_log(row) {
        this.selectedrow = row
        let payload = {
          token: this.$store.getters.token,
          data:{
            task_id : row.id
          }
        }
        GetWorkloadTaskLogs(payload).then(response=>{
          this.workload_cleanup_task_log = response.data
          this.show_vm_cleanup_detail_log_modal = true
        })
      },
      pick_log_color(row){
        let _map = {
          'error':'red',
          'warning':'orange'
        }
        if(Object.keys(_map).includes(row.severity)){
          return _map[row.severity]
        }
      },
      downloadLogFile(){
        if (!this.selectedrow.detail_log_path){
          this.$notify({
                title: 'Ooooops',
                message: 'No log yet!',
                type: 'info',
                duration: 2000
              })
        }
        let payload = {
          data:{  id:this.selectedrow.id,
                  filepath:this.selectedrow.detail_log_path},
          token: this.$store.getters.token
        }
        Download(payload)
        .then((response)=>{
          const href = URL.createObjectURL(response.data);
          // create "a" HTML element with href to file & click
          const link = document.createElement('a');
          link.href = href;
          link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
          document.body.appendChild(link);
          link.click();
          // clean up "a" element & remove ObjectURL
          document.body.removeChild(link);
          URL.revokeObjectURL(href);
        })
      },
      validate_unlock_account_form() {
        console.log('validating unlock account form')
        this.$refs['unlock_account_form'].validate(
          (valid) => {
            if (valid) {
              this.unlock_account()
            }
          }
        )
      },
      unlock_account() {
        let payload = {
          pe_name: this.unlock_account_form.pe_name
        }
        console.log(payload)
        // Call the unlock account API
        UnlockAccount(payload).then(
          response => {
            this.$notify({
              title: 'Success',
              message: response.data.message || "Account unlock started",
              type: 'success',
              duration: 2000
            })
            this.unlock_account_form.pe_name = ''
            this.show_unlock_account_modal = false
          }
        ).catch(
          error => {
            this.$notify({
              title: 'Task failed',
              message: error.response.data.message,
              type: 'error',
              duration: 10000
            })
          }
        )
      },
      handle_unlock_account_row_click(row) {
        this.unlock_account_selected_row = row
      },
      handle_unlock_account_brief_log(row) {
        this.unlock_account_selected_row = row
        let payload = {
          token: this.$store.getters.token,
          data: {
            task_id: row.id
          }
        }
        GetUnlockAccountTaskLogs(payload).then(response => {
          this.unlock_account_task_log = response.data
          this.show_unlock_account_detail_log_modal = true
        })
      },
      downloadUnlockLogFile() {
        if (!this.unlock_account_selected_row.detail_log_path) {
          this.$notify({
            title: 'Ooooops',
            message: 'No log yet!',
            type: 'info',
            duration: 2000
          })
          return
        }
        
        let payload = {
          data: {
            id: this.unlock_account_selected_row.id,
            filepath: this.unlock_account_selected_row.detail_log_path
          },
          token: this.$store.getters.token
        }
        
        Download(payload)
          .then((response) => {
            const href = URL.createObjectURL(response.data);
            const link = document.createElement('a');
            link.href = href;
            link.setAttribute('download', (payload.data.filepath.split("\\").at(-1) + '.log'));
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(href);
          })
      },
    }
  }
  </script>

<style lang="scss" scoped>
.toolbox_panel{
    padding: 0.3% 0.5px 0.3% 0.5%;
    // background-color: #353536;
    height:30%;
    width:100%;
    position: absolute;
}
.box-card {
  width: 100%;
  max-width: 100%;
  height:100%;
  border-radius: 6px;
}

.panel-group {
  height:70%;
  width: 99%;
  position: absolute;
  margin-left: 100px;
  .card-panel-col {
    margin-bottom: 4px;
  }

  .card-panel {
    
    border-radius: 10px;
    height: 60%;
    width: 18%;
    cursor: pointer;
    font-size: 12px;
    position: absolute;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 5px 0px 10px 5px rgba(0, 0, 0, .1);
    border-color: rgba(0, 0, 0, .05);
    &:hover {
      // background: #edcfcf;
      box-shadow: 5px 0px 10px 5px rgba(122, 186, 110, 0.4);
    }

    .card-panel-icon-wrapper {
      float: left;
      height: 70%;
      width:35%;
      // margin: 10px 0 14px 14px;
      // padding: 16px;
      transition: all 0.38s ease-out;
      // border-radius: 6px;
      position: absolute
      
    }

    .card-panel-icon {
      float: left;
      font-size: 55px;
      margin:auto;
      top:0;
      left: 0;
      bottom: 0;
      right: 0;
      position:absolute
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      // margin-top: 15%;
      width:65%;
      height:70%;
      position:absolute;
      margin-left: 35%;
      .card-panel-text {
        height: 30%;
        line-height: 25px;
        color: rgb(118, 116, 116);
        margin: auto;
        top:0;
        bottom: 0;
        width:100%;
        font-size: 22px;
        text-align: center;
        position: absolute;
      }

      .card-panel-num {
        // border:1px blue solid;
        // font-size: 20px;
        // text-align: center;
        line-height: 25px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 22px;
        text-align: center;
        position:absolute;
        margin:  auto;
        left:0;
        right:0;
      }
    }
  }
}
  .form-label{
    color:rgb(4, 4, 4);
    width:100%;
    float:left;
    font-weight: 200;
    font-size: larger;
  }

  .form-label_half_left{
    color:gray;
    width:20%;
    float:left;
    margin-left:20%;
  }

  .form-label_half_right{
    color:gray;
    width:20%;
    float:left;
    margin-left:5%
  }
  .stage_loading {
      // border: 1px solid #f3f3f3;
      border-top: 7px solid #1e64d4;
      border-radius: 50%;
      display: inline-block;
      animation: spin 3s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .apply-shake{
    animation: shake 1s ease-in-out;
  }

  @keyframes shake{
    10%,90%{
      transform: translate3d(-1px, 0, 0);
    };
    20%,80%{
      transform: translate3d(4px, 0, 0);
    };
    30%,50%,70%{
      transform: translate3d(-6px, 0, 0);
    };
    40%,60%{
      transform: translate3d(8px, 0, 0);
    }
  }

</style>

$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Get-NtxControlPanel(){
    Param (
        [string] $Fqdn,
        [string] $Uuid,
                 $Auth
    )
    if ($Uuid) {
        if ($PrismCall = Rest-Prism-v3-Get-App -Fqdn $Fqdn -Uuid $Uuid -Auth $Auth) {
            $ControlPanel = [PSCustomObject]@{
                'Name'              = $PrismCall.status.name
                'State'             = $PrismCall.status.state
                'Uuid'              = $PrismCall.status.uuid
                'Cluster'           = $PrismCall.metadata.project_reference.name
                'SiteNameCode'      = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AB_SiteNameCode"}).value
                'AhvSubnet'         = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AD_SiteSubnetId"}).value
                'SiteType'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AG_SiteProfile"}).value
                'RemoteSite'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "ZZZ_REPLTarget"}).value
                'BackupBandwidth'   = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AH_SiteBackupBandwidth"}).value
                'DarksiteBandwidth' = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AH_SiteDarkSiteBandwidth"}).value
                'Timezone'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AJ_SiteTimeZone"}).value
                'Latitude'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AK_SiteLatitude"}).value
                'Longtitude'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AK_SiteLongitude"}).value
                'AosVersion'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AN_SiteAosVersion"}).value
                'ConfigVersion'     = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "Z_ReleaseCustomer"}).value
                'CoreFwVersion'     = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "Z_ReleaseFramework"}).value
            }
            return $ControlPanel
        }
    }else {
        $ControlPanels = @()
        if ($PrismCall = Rest-Prism-v3-List-App -Fqdn $Fqdn -Filter "name==.*CPL.*;_state!=deleted" -Auth $Auth) {
            $PrismCall | ForEach-Object {
                $ControlPanel = [PSCustomObject]@{
                    'Name'  = $_.status.name
                    'Uuid'  = $_.status.uuid
                    'Prism' = $_.metadata.project_reference.name
                }
                $ControlPanels += $ControlPanel
            }
        }
        return $ControlPanels
    }
}
function Launch-Job-PeCert(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Load data from the table dh_retail_ntx_pe
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhPEs.Count)' PEs need to update" -DumpFile $DumpFile
        #Rolling call PEs exist in the table
        $DhPEs | Foreach-Object -ThrottleLimit 50 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $using:DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $PE         = $_
            $UpdateDict = $using:CollectionUpdate
            $PC         = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $CertMap = [PSCustomObject]@{
                'fqdn'             = $PE.fqdn
                'cert_expiry_date' = "NA"
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We'll call Prism Api towards '$($PE.name)' for getting certificate status" -DumpFile $using:DumpFile
            if ($PrismCall = Rest-Prism-v1-Get-Keys -Fqdn $PE.fqdn -Auth $Auth) {
                $CertMap.cert_expiry_date = Convert-NtxCertExpiryDate -ExpiryDate $PrismCall.expiryDate
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "The certificate status of $($PE.name) is available" -DumpFile $DumpFile
            }
            $UpdateDict.Add($CertMap)
        }
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pe]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxPe-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
function Launch-Job-PeLcm(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Load data from the table dh_retail_ntx_pe
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhPEs.Count)' PEs need to update" -DumpFile $DumpFile
        #Rolling call PEs exist in the table
        $DhPEs | Foreach-Object -ThrottleLimit 50 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $using:DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $PE         = $_
            $UpdateDict = $using:CollectionUpdate
            $PC         = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $LcmMap = [PSCustomObject]@{
                'fqdn'               = $PE.fqdn
                'foundation_version' = "NA"
                'lcm_version'        = "NA"
                'spp_version'        = "NA"
            }
            $LcmApiVer = 'v4'
            if ($LcmFwCall = Rest-Genesis-Get-LcmFw -Fqdn $PE.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "LCM version of '$($PE.name)' is '$($LcmFwCall.semantic_version)'" -DumpFile $using:DumpFile
                $LcmMap.lcm_version = $LcmFwCall.semantic_version
            }
            switch ($LcmMap.lcm_version) {
                '2.4.5.2' {
                    $LcmApiVer = 'v1'
                }
                '2.5.0.2' {
                    $LcmApiVer = 'v4'
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We'll call LCM Api '$($LcmApiVer)' towards '$($PE.name)' for getting Foundation version" -DumpFile $using:DumpFile
            if ($LcmCall = & $('Rest-Lcm-' + $LcmApiVer + '-Get-LcmEntity') -Fqdn $PE.fqdn -Auth $Auth) {
                switch ($LcmApiVer) {
                    'v1' {
                        $LcmMap.foundation_version = ($LcmCall | Where-Object {$_.entity_model -eq "Foundation"}).version | Sort-Object | Get-Unique
                        $LcmMap.spp_version        = ($LcmCall | Where-Object {$_.entity_class -eq "SPP"}).version | Sort-Object | Get-Unique
                    }
                    'v4' {
                        $LcmMap.foundation_version = ($LcmCall | Where-Object {$_.entityModel -eq "Foundation"}).version | Sort-Object | Get-Unique
                        $LcmMap.spp_version        = ($LcmCall | Where-Object {$_.entityClass -eq "SPP"}).version | Sort-Object | Get-Unique
                    }
                }
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "The Foundation version of $($PE.name) is $($LcmMap.foundation_version)" -DumpFile $DumpFile
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "The SPP version of $($PE.name) is $($LcmMap.spp_version)" -DumpFile $DumpFile
            }
            $UpdateDict.Add($LcmMap)
        } -UseNewRunspace
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pe]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxPe-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
function Launch-Job-PeLicense(){
    Begin {
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Load data from the table dh_retail_ntx_pe
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $LicensedCategory = @("STARTER", "PRO", "ULTIMATE")
            $LicenseCategory  = $LicensedCategory + "No_License"
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhPEs.Count)' PEs need to update" -DumpFile $DumpFile
        #Rolling call PEs exist in the table
        $DhPEs | Foreach-Object -ThrottleLimit 50 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $using:DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $PE         = $_
            $UpdateDict = $using:CollectionUpdate
            $PC         = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for '$($PE.name)'" -DumpFile $DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            #Construct a data model which stores data needs to be updated into the table
            $LicenseMap = [PSCustomObject]@{
                'fqdn'                   = $PE.fqdn
                'license_category'       = "Unknown"
                'license_class'          = "Unknown"
                'license_cores_capacity' = "0"
                'license_flash_capacity' = "0 Tib"
                'license_hdd_capacity'   = "0 Tib"
                'license_cores_licensed' = "NA"
                'license_flash_licensed' = "NA"
                'license_hdd_licensed'   = "NA"
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Calling '$($PE.fqdn)' for getting license details" -DumpFile $using:DumpFile
            if ($PrismCall1 = Rest-Prism-v1-Get-License -Fqdn $PE.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Got the license details of '$($PE.fqdn)'" -DumpFile $using:DumpFile
                if ($PrismCall1.licenseDTO) {
                    $LicenseMap.license_category       =      $PrismCall1.licenseDTO.category
                    $LicenseMap.license_class          =      $PrismCall1.licenseDTO.licenseClass
                    $LicenseMap.license_cores_capacity = "" + [int]$PrismCall1.licenseDTO.capacity.num_cores
                    $LicenseMap.license_flash_capacity = "" + [double]$PrismCall1.licenseDTO.capacity.ssd_size_tebibytes + " Tib"
                    $LicenseMap.license_hdd_capacity   = "" + [double]$PrismCall1.licenseDTO.capacity.hdd_size_tebibytes + " Tib"
                    $LicenseMap.license_cores_licensed = "" + [int]$PrismCall1.licenseDTO.capacityLicensed.num_cores
                    $LicenseMap.license_flash_licensed = "" + [double]$PrismCall1.licenseDTO.capacityLicensed.ssd_size_tebibytes + " Tib"
                    $LicenseMap.license_hdd_licensed   = "" + [double]$PrismCall1.licenseDTO.capacityLicensed.hdd_size_tebibytes + " Tib"
                }elseif ($PrismCall1.licenseInfoDTO) {
                    if ($PrismCall1.licenseInfoDTO.pcDetailsDTO.licenses) {
                        $LicenseMap.license_category = $PrismCall1.licenseInfoDTO.pcDetailsDTO.licenses.category | Sort-Object | Get-Unique
                        $LicenseMap.license_category = $LicenseMap.license_category -join ", "
                        $LicenseMap.license_class    = $PrismCall1.licenseInfoDTO.pcDetailsDTO.licenses.type | Sort-Object | Get-Unique
                        $LicenseMap.license_class    = $LicenseMap.license_class -join ", "
                        [int]$Cores = 0
                        $PrismCall1.licenseInfoDTO.pcDetailsDTO.licenses | ForEach-Object {
                            if ($_.meter -eq "CORES") {
                                $Cores += [int]$_.quantity
                            }
                        }
                        $LicenseMap.license_cores_capacity = "" + $Cores
                        [double]$Flash = 0
                        $PrismCall1.licenseInfoDTO.pcDetailsDTO.licenses | ForEach-Object {
                            if ($_.meter -eq "FLASH") {
                                $Flash += [double]$_.quantity
                            }
                        }
                        $LicenseMap.license_flash_capacity = "" + $Flash + " Tib"
                        [double]$Hdd = 0
                        $PrismCall1.licenseInfoDTO.pcDetailsDTO.licenses | ForEach-Object {
                            if ($_.meter -eq "TIB") {
                                $Hdd += [double]$_.quantity
                            }
                        }
                        $LicenseMap.license_hdd_capacity   = "" + $Hdd + " Tib"
                        # $LicenseMap.license_cores_licensed = "" + $Cores
                        # $LicenseMap.license_flash_licensed = "" + $Flash
                        # $LicenseMap.license_hdd_licensed   = "" + $Hdd
                    }
                }else {
                    $LicenseMap.license_category       =      $PrismCall1.category
                    $LicenseMap.license_class          =      $PrismCall1.licenseClass
                    $LicenseMap.license_cores_capacity = "" + [int]$PrismCall1.capacity.num_cores
                    $LicenseMap.license_flash_capacity = "" + [double]$PrismCall1.capacity.ssd_size_tebibytes + " Tib"
                    $LicenseMap.license_hdd_capacity   = "" + [double]$PrismCall1.capacity.hdd_size_tebibytes + " Tib"
                    $LicenseMap.license_cores_licensed = "" + [int]$PrismCall1.capacityLicensed.num_cores
                    $LicenseMap.license_flash_licensed = "" + [double]$PrismCall1.capacityLicensed.ssd_size_tebibytes + " Tib"
                    $LicenseMap.license_hdd_licensed   = "" + [double]$PrismCall1.capacityLicensed.hdd_size_tebibytes + " Tib"
                }
            }else {
                Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to get the license details of '$($PE.fqdn)'" -DumpFile $using:DumpFile
                if ($PE.license_category.ToString() -in $using:LicenseCategory) {
                    Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to fetching license details of '$($PE.fqdn)' in present, it was available what is '$($PE.license_category)', we will keep instead of update it" -DumpFile $using:DumpFile
                    return
                }
            }
            $UpdateDict.Add($LicenseMap)
        } -UseNewRunspace
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pe]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxPe-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
function Launch-Job-PeRs(){
    Begin {
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Load data from the table dh_retail_ntx_pe
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhPEs.Count)' PEs need to update" -DumpFile $DumpFile
        $DhPEs | Foreach-Object -ThrottleLimit 50 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $using:DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $PE         = $_
            $UpdateDict = $using:CollectionUpdate
            $PC         = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for $($PE.name)" -DumpFile $DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for $($PE.name)" -DumpFile $using:DumpFile
                return
            }
            #Construct a data model which stores data needs to be updated into the table
            $RsMap = [PSCustomObject]@{
                'fqdn'                          = $PE.fqdn
                # 'remote_site'                   = if ($PE.remote_site) { $PE.remote_site }else { "NA" }
                'remote_backup_number'          = 0
                'remote_site_last'              = $PE.remote_site_last
                'default_bandwidth_limit_mbps'  = 0
                'remote_site_runtime'           = if ($PE.remote_site_runtime) {$PE.remote_site_runtime}else {"NA"}
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We loaded $($PE.name) owns runtime remote site is $($RsMap.remote_site_runtime) and remote site in last is $($RsMap.remote_site_last) from database" -DumpFile $using:DumpFile
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Calling $($PE.fqdn) to get the protection domain" -DumpFile $using:DumpFile
            $PrismCall1 = $(Rest-Prism-v1-List-ProtectionDomain -Fqdn $PE.fqdn -Auth $Auth) | Where-Object {$_.name -match $PE.name}
            if ($PE.node_number -eq "1") {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "$($PE.name) is the single node cluster" -DumpFile $using:DumpFile
                $PrismCall1 = $PrismCall1 | Where-Object {$_.name -match 'Silver_CCG'} | Select-Object -First 1
            }else {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "$($PE.name) has $($PE.node_number) nodes" -DumpFile $using:DumpFile
                $PrismCall1 = $PrismCall1 | Where-Object {$_.name -match 'Gold_CCG'} | Select-Object -First 1
            }
            #Validate the cluster has the Gold protection domain and the PD contains remote site settings, otherwise, the cluster has no remote backup
            if (!$PrismCall1 -or !($PrismCall1.remoteSiteNames)) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "$($PE.name) has 0 remote site settings, we'll confirm the last remote site settings" -DumpFile $using:DumpFile
                if ($RsMap.remote_site_runtime -ne "NA") {
                    Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We detected change is remote site settings of $($PE.name) has changed from $($RsMap.remote_site_runtime) to NA" -DumpFile $using:DumpFile
                    $RsMap.remote_site_last     = $RsMap.remote_site_runtime
                    $RsMap.remote_site_runtime  = "NA"
                }
                $UpdateDict.Add($RsMap)
                return
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Remote site settings of $($PE.name) is available" -DumpFile $using:DumpFile
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Calling $($PE.fqdn) to get the remote snapshots" -DumpFile $using:DumpFile
            #Convert name of each remote sites (e.g. RS_RETUS051-NXC000) to cluster name (e.g. RETUS051-NXC000)
            $RsName = $PrismCall1.remoteSiteNames[0]
            $Rs     = $RsName.Split('RS_')[1]
            if ($RsMap.remote_site_runtime -ne "NA") {
                $RsMap.remote_site_last = $RsMap.remote_site_runtime
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We got remote site settings of $($PE.fqdn) is $($Rs) from the environment" -DumpFile $using:DumpFile
            if ($RsMap.remote_site_runtime -ne $Rs) {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We detected change is remote site settings of $($PE.name) has changed from $($RsMap.remote_site_runtime) to $($Rs)" -DumpFile $using:DumpFile
            }
            $RsMap.remote_site_runtime = $Rs
            $PrismCall2 = Rest-Prism-v1-List-RsDrSnapShot -Fqdn $PE.fqdn -Auth $Auth
            $RsMap.remote_backup_number = ($PrismCall2.entities | Where-Object {$_.protectionDomainName -match $PE.name}).count
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "$($PE.name) has $($RsMap.remote_backup_number) remote snapshots" -DumpFile $using:DumpFile
            $PrismCall3 = Rest-Prism-v1-Get-RemoteSite -Fqdn $PE.fqdn -RsName $RsName -Auth $Auth
            $RsMap.default_bandwidth_limit_mbps = $PrismCall3.bandwidthPolicy.defaultBandwidthLimit / [Math]::Pow(1000, 2)
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We got default bandwidth limit of $($RsMap.default_bandwidth_limit_mbps) Mbps from $($PE.fqdn)" -DumpFile $using:DumpFile
            $UpdateDict.Add($RsMap)
        } -UseNewRunspace
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pe]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxPe-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
function Launch-Job-PeControlPanel(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        #Grab the function definition of Get-NtxControlPanel to a variable, it will be used in the parallized loop
        $GetNtxControlPanel = ${function:Get-NtxControlPanel}.ToString()
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Load data from the table dh_retail_ntx_pe, and filter out the decommissioned sites
        #Create an empty array $CpCollection that stores control panels come from Prism Central
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CpCollection     = @()
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhPEs.Count)' PEs need to update" -DumpFile $DumpFile
        #foreach ($PC in $DhPCs | Where-Object {$_.tier -eq "Production"}) {
        foreach ($PC in $DhPCs) {
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now getting CPLs from '$($PC.Fqdn)'" -DumpFile $DumpFile
            #Get the service account and authentication string for calling PC to get the list of control panels
            $SvcAccount = Select-DhServiceAccount -Vars $Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for '$($PC.name)'" -DumpFile $DumpFile
                continue
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for '$($PC.name)'" -DumpFile $DumpFile
                continue
            }
            #Returned control panels are stored into the array $CpCollection
            $CpCollection += Get-NtxControlPanel -Fqdn $PC.fqdn -Auth $Auth
        }
        $DhPEs | Foreach-Object -ThrottleLimit 20 -Parallel {
            $Global:DumpFile = $using:DumpFile
            #Import functions defined in other modules
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $using:DumpFile -Value $Message
                    Exit 0
                }
            }
            #Import function defined out of the parallel loop
            ${function:Get-NtxControlPanel} = $using:GetNtxControlPanel
            #Get the timezone variable from control panel for each PEs
            #Locate corresponded Prism Central
            #Get the service account and authentication string for REST call
            #Set the default timezone by reference the Prism Central for deal with the case of when the timezone value of PE is not available
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $PE              = $_
            $PC              = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $UpdateDict      = $using:CollectionUpdate
            $SvcAccount      = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            $DefaultTimeZone = if ($PC.fqdn -match "ssp-eu-ntx") {
                "UTC+02:00"
            }elseif ($PC.fqdn -match "ssp-apac-ntx") {
                "UTC+08:00"
            }elseif ($PC.fqdn -match "ssp-china-ntx") {
                "UTC+08:00"
            }elseif ($PC.fqdn -match "ssp-na-ntx") {
                "UTC-05:00"
            }elseif ($PC.fqdn -match "ssp-russia-ntx") {
                "UTC+04:00"
            }elseif ($PC.fqdn -match "ssp-ppe-ntx") {
                "UTC+02:00"
            }else {
                "UTC+00:00"
            }
            #Create a object stores the mapping of PE and timezone
            $CplMap = [PSCustomObject]@{
                'fqdn'               = $PE.fqdn
                'timezone'           = $DefaultTimeZone
                'backup_bandwidth'   = "NA"
                'darksite_bandwidth' = "NA"
            }
            #We can skip to get the timezone when the PE is not a production environment and take the default timezone
            if ($PC.tier -ne "Production") {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "The PE '$($PE.name)' is a non-production environment, let's skip" -DumpFile $using:DumpFile
                $UpdateDict.Add($CplMap)
                return
            }
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            #Find the control panel of current PE, and get its UUID
            $Cp = $using:CpCollection | Where-Object {$_.Name -match $PE.name} | Select-Object -First 1
            if (!$Cp) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "It's unable to get control panel for '$($PE.name)'" -DumpFile $using:DumpFile
                $UpdateDict.Add($CplMap)
                return
            }
            Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "We've got control panel in brief for '$($PE.name)'" -DumpFile $using:DumpFile
            #Get the details of control panel through REST call with the UUID
            $PrismCall       = Get-NtxControlPanel -Fqdn $PC.fqdn -Uuid $Cp.Uuid -Auth $Auth
            if ($PrismCall.Timezone) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We've get the details of control panel for '$($PE.name)'" -DumpFile $using:DumpFile
                $CplMap.timezone = $PrismCall.Timezone
            }
            if ($PrismCall.BackupBandwidth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We've get the details of control panel for '$($PE.name)'" -DumpFile $using:DumpFile
                $CplMap.backup_bandwidth = $PrismCall.BackupBandwidth
            }
            if ($PrismCall.DarksiteBandwidth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We've get the details of control panel for '$($PE.name)'" -DumpFile $using:DumpFile
                $CplMap.darksite_bandwidth = $PrismCall.DarksiteBandwidth
            }
            $UpdateDict.Add($CplMap)
        }
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pe]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxPe-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job-PeCert
Launch-Job-PeLcm
Launch-Job-PeLicense
Launch-Job-PeRs
Launch-Job-PeControlPanel
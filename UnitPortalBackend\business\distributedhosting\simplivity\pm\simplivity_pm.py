import logging
import re
import uuid
from datetime import datetime

from flask import Flask

import static.SETTINGS as SETTING
from business.authentication.authentication import ServiceAccount
from business.distributedhosting.simplivity.simplivity import SimplivityvCenter
from business.generic.commonfunc import create_file, setup_logger
from business.loggings.loggings import DBLogging
from models.database import db
from models.pm_models import ModelSLIPMTask
from models.sli_models import ModelSLICluster
from business.generic.commonfunc import DBConfig


def start_simplivity_pm(task_id):      # noqa
    logging.info(f"Simplivity PM {task_id} started.")
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    db.init_app(app)  # db is in models.py
    app.app_context().push()  # context definition

    ########################## main logic started ###########################
    task = ModelSLIPMTask.query.filter_by(id=task_id).first()
    task.status = "In Progress"
    cluster, creater, vc, pmtype = task.cluster, task.creater, task.vcenter, task.pmtype
    lg = DBLogging(logdir=SETTING.SLI_LOG_PATH, taskid=task_id, logtype="SLI_PM")  # create logging instance
    simplivity = SimplivityPM(vc=task.vcenter, cluster=task.cluster, usage="simplivity_pm", l_g=lg)  # type: ignore
    db.session.commit()  # change the task status to inprogress
    # if cluster != "RETCN888":# just for test
    #     return
    print(simplivity.vc)
    pmstatus_flag = True

    try:
        ################### create dedicated log file #######################
        #                                                                   #
        #                      Step 1 : create logs                         #
        #                                                                   #
        #####################################################################
        if local_log_path := create_file(
                filepath=SETTING.SLI_LOG_PATH,
                filename=f'{cluster}_{creater}_{datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}'):
            logging.info(f"Local log file created, path: {local_log_path}.")
            lg.write_pm_log(loginfo=f'Local log file created, path: {local_log_path}.', logseverity='info',
                            taskid=task_id)
            task.detaillogpath = local_log_path
            db.session.commit()
        else:
            raise Exception('Failed to create local log file, PM will be ended now.')
        logger = setup_logger(str(uuid.uuid4()),
                              local_log_path)  # for function/class using, logging into the specific file
        sli_pm = SimplivityPM(task.vcenter, cluster=task.cluster, usage="simplivity_pm", logger=logger,
                              l_g=lg)  # type:ignore
        #####################################################################
        #                                                                   #
        #                  Step 2 : Check if cluster exists                 #
        #                                                                   #
        #####################################################################
        lg.write_pm_log(loginfo='Check if cluster exists.', logseverity='info', taskid=task_id)
        res, mes = sli_pm.if_slicluster_exists()
        if not res:  # if cluster not exist, then abort the pm
            raise Exception(mes)
        if isinstance(mes, list):
            if len(mes) > 1:  # the code shouldn't go here, if it's here, then something is really wrong
                raise Exception(f'we found more than 1 cluster named {task.cluster}, abort.')

        #####################################################################
        #                                                                   #
        #                    Step 3 : PM Part                               #
        #                                                                   #
        #####################################################################
        vc = SimplivityvCenter(vc=simplivity.vc, username=simplivity.sa['username'], password=simplivity.sa['password'],
                               taskid=task_id, l_g=lg, logger=logger)  # create VC instance and pass instance lg
        logging.info(f"Connecting the VC: {simplivity.vc}.")
        lg.write_pm_log(loginfo=f'Connecting the VC: {simplivity.vc}.', taskid=task_id, logseverity='info')
        if vc.connect():  # vc connetion
            logging.info(f"Successfully Connected the VC: {simplivity.vc}.")
            lg.write_pm_log(loginfo=f'Successfully Connected the VC: {simplivity.vc}.', taskid=task_id,
                            logseverity='info')
            cluster_info = ModelSLICluster.query.filter_by(name=cluster).first()
            master_ovcip = (cluster_info.master_ovc).split('-', 1)[1].replace('-', '.')
            slave_ovcip = (cluster_info.slave_ovc).split('-', 1)[1].replace('-', '.')
            # the power off part
            if pmtype == "poweroff":
                start_time = datetime.now()  # set an start time point
                logging.info("Shutting down the standard VMs...")
                lg.write_pm_log(loginfo='Shutting down the standard VMs...', taskid=task_id, logseverity='info')
                vmoperator = vc.vmoperator(cluster_name=cluster,
                                           vm_status="off")  # shut down all the vms except the ovc
                if vmoperator:
                    logging.info("Successfully Shut down the standard VMs...")
                    lg.write_pm_log(loginfo='Successfully Shut down the standard VMs...', taskid=task_id,
                                    logseverity='info')
                    logging.info("Shutting down the Slave OVC...")
                    lg.write_pm_log(loginfo='Shutting down the Slave OVC...', taskid=task_id, logseverity='info')
                    slaveovc_shutdown = vc.ovc_shutdown(hostname=cluster_info.slave_host, ovcadd=slave_ovcip,
                                                        ovc_name=cluster_info.slave_ovc,
                                                        clustername=cluster)  # shut down the slaveovc
                    if slaveovc_shutdown:
                        logging.info("Successfully Shut down the Slave OVC...")
                        lg.write_pm_log(loginfo='Successfully Shut down the Slave OVC...', taskid=task_id,
                                        logseverity='info')
                        lg.write_pm_log(loginfo='Shutting down the Master OVC...', taskid=task_id, logseverity='info')
                        logging.info("Shutting down the Master OVC...")
                        masterovc_shutdown = vc.ovc_shutdown(hostname=cluster_info.master_host, ovcadd=master_ovcip,
                                                             ovc_name=cluster_info.master_ovc,
                                                             clustername=cluster)  # shut down the masterovc
                        if masterovc_shutdown:
                            logging.info("Successfully Shut down the Master OVC...")
                            lg.write_pm_log(loginfo='Successfully Shut down the Master OVC...', taskid=task_id,
                                            logseverity='info')
                            lg.write_pm_log(loginfo='Enabling the Maintenance Mode on both hosts...', taskid=task_id,
                                            logseverity='info')
                            logging.info("Enabling the Maintenance Mode on both hosts...")
                            vc.control_maintenancemode(cluster_name=cluster,
                                                       mode_status="on")  # enter the maintenance mode
                            logging.info("Disabling the HA...")
                            lg.write_pm_log(loginfo='Disabling the HA...', taskid=task_id, logseverity='info')
                            vc.controlha(cluster_name=cluster, ha_status="Disabled")  # disable the HA
                            logging.info("Successfully Disabled the HA.")
                            lg.write_pm_log(loginfo='Shuting down the hosts...', taskid=task_id, logseverity='info')
                            logging.info("Shuting down the hosts...")
                            hosts_off = vc.hosts_off(cluster_name=cluster)  # shut down the hosts
                            if hosts_off:
                                end_time = datetime.now()
                                usage_time = (end_time - start_time).total_seconds()
                                logging.info("Successfully Shut down the hosts.")
                                # lg.write_pm_log(loginfo=f'SSuccessfully Shut down the hosts.' ,taskid = task_id ,logseverity='info' )      # noqa
                                lg.write_pm_log(
                                    loginfo='Successfully completed PM-poweroff in {str(round(usage_time / 60, 2))} mins!',      # noqa
                                    taskid=task_id, logseverity='info')
                                logging.info("Successfully completed PM-poweroff in {str(round(usage_time / 60, 2))} mins!")
                            else:
                                lg.write_pm_log(loginfo='Unexpected ERROR duing the Host poweroff, please check!',
                                                taskid=task_id, logseverity='info')
                                logging.error("Unexpected ERROR duing the Host poweroff, please check!")
                                pmstatus_flag = False
                        else:
                            pmstatus_flag = False
                            logging.error("Unexpected ERROR duing the Master OVC poweroff, please check!")
                            lg.write_pm_log(loginfo='Unexpected ERROR duing the Master OVC: , please check!',
                                            taskid=task_id, logseverity='info')
                    else:
                        pmstatus_flag = False
                        logging.error("Unexpected ERROR duing the Slave OVC poweroff, please check!")
                        lg.write_pm_log(loginfo='Unexpected ERROR duing the Slave OVC: , please check!',
                                        taskid=task_id, logseverity='info')
                else:
                    pmstatus_flag = False
                    logging.error("Unexpected ERROR duing the  VMs poweroff, please check!")
                    lg.write_pm_log(loginfo='Unexpected ERROR duing the VMs , please check!', taskid=task_id,
                                    logseverity='info')
            # the power on part
            if pmtype == "poweron":
                simplivity_ilo = SimplivityPM(vc=task.vcenter, cluster=task.cluster, usage="simplivity_ilo", l_g=lg)
                start_time = datetime.now()  # set an start time point
                logging.info("Verifying the hosts status...")
                lg.write_pm_log(loginfo='Verifying the hosts status...', taskid=task_id, logseverity='info')
                hosts_status_verify = vc.hosts_status_verify(
                    cluster_name=cluster)  # check the ilo and host network connection
                if hosts_status_verify:
                    logging.info("Powering on the Hosts...")
                    lg.write_pm_log(loginfo='Powering on the Hosts...', taskid=task_id, logseverity='info')
                    hosts_on = vc.hosts_on(cluster_name=cluster, ilo_user=simplivity_ilo.sa['username'],
                                           ilo_password=simplivity_ilo.sa['password'])  # power on hosts
                    if hosts_on:
                        logging.info("Exiting the Maintenance Mode on both hosts...")
                        lg.write_pm_log(loginfo='Exiting the Maintenance Mode on both hosts...', taskid=task_id,
                                        logseverity='info')
                        exit_maintenancemode = vc.control_maintenancemode(      # noqa  # pylint: disable=W0612
                            cluster_name=cluster, mode_status="off")    # exit from maintenance mode
                        # if exit_maintenancemode:
                        logging.info("Powering on the Master OVC...")
                        lg.write_pm_log(loginfo='Powering on the Master OVC...', taskid=task_id, logseverity='info')
                        masterovc_poweron = vc.ovc_poweron(cluster_name=cluster,
                                                           ovc_name=cluster_info.master_ovc)  # power on the masterovc
                        if masterovc_poweron:
                            logging.info("Powering on the Slave OVC...")
                            lg.write_pm_log(loginfo='Powering on the Slave OVC...', taskid=task_id, logseverity='info')
                            slaveovc_poweron = vc.ovc_poweron(cluster_name=cluster,
                                                              ovc_name=cluster_info.slave_ovc)  # power on the slaveovc
                            if slaveovc_poweron:
                                logging.info("Powering on the VMs...")
                                lg.write_pm_log(loginfo='Powering on the VMs...', taskid=task_id, logseverity='info')
                                vmoperator = vc.vmoperator(cluster_name=cluster, vm_status="on")  # poweron the vms
                                if vmoperator:
                                    logging.info("Enabling the HA...")
                                    lg.write_pm_log(loginfo='Enabling the HA...', taskid=task_id, logseverity='info')
                                    vc.controlha(cluster_name=cluster, ha_status="Enable")  # enable the HA
                                    end_time = datetime.now()
                                    usage_time = (end_time - start_time).total_seconds()
                                    logging.info(f"The PM-poweron completed with {str(round(usage_time / 60, 2))} mins!")
                                    lg.write_pm_log(
                                        loginfo=f'The PM-poweron completed with {str(round(usage_time / 60, 2))}mins!',
                                        taskid=task_id, logseverity='info')
                                else:
                                    pmstatus_flag = False
                                    lg.write_pm_log(loginfo='Error during the VMs up, please contact administrator.', taskid=task_id, logseverity='error')
                            else:
                                pmstatus_flag = False
                                logging.error("Unexpected ERROR duing the Slave OVC poweron, please check!")
                                lg.write_pm_log(loginfo='Unexpected ERROR duing the Slave OVC poweron, please check!',
                                                taskid=task_id, logseverity='info')
                        else:
                            pmstatus_flag = False
                            logging.error("Unexpected ERROR duing the Master OVC poweron, please check!")
                            lg.write_pm_log(loginfo='Unexpected ERROR duing the Master OVC poweron, please check!',
                                            taskid=task_id, logseverity='info')
                        # else:
                        #     logging.error(f"Unexpected ERROR duing the Maintenance_Mode exiting, please check!")
                        #     print("Unexpected ERROR duing the Maintenance_Mode exiting, please check! ")
                        #     lg.write_pm_log(loginfo=f'Unexpected ERROR duing the Maintenance_Mode exiting, please check!' ,taskid = task_id ,logseverity='info')     # noqa
                    else:
                        pmstatus_flag = False
                        logging.error("Unexpected ERROR duing the Hosts poweron, please check!")
                        lg.write_pm_log(loginfo='Unexpected ERROR duing the Hosts poweron, please check!',
                                        taskid=task_id, logseverity='info')
                else:
                    pmstatus_flag = False
                    logging.error("Hosts are still unreachable, please check!")
                    lg.write_pm_log(loginfo='Hosts are still unreachable, please check!', taskid=task_id,
                                    logseverity='info')
        else:
            pmstatus_flag = False
            lg.write_pm_log(loginfo=f' Got an error when connecting to vCenter : {simplivity.vc}', taskid=task_id,
                            logseverity='error')
    except Exception as e:
        max_length = len(str(e)) if len(str(e)) < 255 else 255
        lg.write_pm_log(loginfo=f'{str(e)[0:max_length - 1]}', taskid=task_id, logseverity='error')
        task = ModelSLIPMTask.query.filter_by(id=task_id).first()
        task.status = "Error"
        db.session.commit()
    if not pmstatus_flag:
        task = ModelSLIPMTask.query.filter_by(id=task_id).first()
        task.status = "Error"  # close the task
        db.session.commit()
    else:
        task = ModelSLIPMTask.query.filter_by(id=task_id).first()
        task.status = "Done"  # close the task
        db.session.commit()


class SimplivityPM():
    def __init__(self, vc, usage, cluster=None, logger=logging, l_g=None):
        self.vc = vc.lower()
        self.cluster = cluster.upper()
        self.logger = logger
        self.usage = usage
        self.l_g = l_g
        # self.logger.info(f"Getting service account for usage:(nutanix_pm)")
        _sa = ServiceAccount(usage=self.usage)
        self.sa = _sa.get_service_account()
        # self.logger.info(f"the service account name is {self.sa['username']}")

    def if_slicluster_exists(self):
        # pass
        vclist = (self.vc).split(",")
        for vcname in vclist:
            matched_cluster = {}
            if not (self.sa['username'] and self.sa['password']):
                self.logger.error("Failed to start the PM due to lack of service account or password.")
                return False, "cannnot find service account or the password."
            vc = SimplivityvCenter(vc=vcname, username=self.sa['username'], password=self.sa['password'], l_g=self.l_g)
            self.logger.info("Trying to connect to vc.")
            vc.connect()
            self.logger.info("Getting the cluster list from vc.")
            existing_flag = False
            if vc:
                try:
                    if vc.get_cluster(clustername=self.cluster):
                        existing_flag = True
                        matched_cluster['cluster'] = self.cluster
                        matched_cluster['vc'] = vcname
                        break
                except Exception:
                    pass
                # return existing_flag , (self.cluster if existing_flag else "Didn't find the cluster in the list.")
            else:
                self.logger.error("Could not connect to vc.")
                return False, 'Erorr, Could not connect to vc.'
        return existing_flag, (matched_cluster if existing_flag else "Cluster not existed!")

    def check_slivm_list(self):
        # vclist = (self.vc).split(",")
        try:
            # for vcname in vclist:
            if not (self.sa['username'] and self.sa['password']):
                self.logger.error("Failed to start the PM due to lack of service account or password.")
                return False, "cannnot find service account or the password."
            vc = SimplivityvCenter(vc=self.vc, username=self.sa['username'], password=self.sa['password'], l_g=self.l_g)
            vc.connect()
            vms = vc.get_vm(clustername=self.cluster)
            ovcs = [{"name": vm.name, "state": vm.runtime.powerState, "is_controller_vm": "True"} for vm in vms if
                    re.match("OmniStackVC*", vm.name)]
            vms_standard = [{"name": vm.name, "state": vm.runtime.powerState, "is_controller_vm": "false"} for vm in
                            vms]
            vm_list = vms_standard + ovcs
            return True, vm_list
        except Exception as e:
            self.logger.error('Got error when generating the VM list')
            return False, e

import logging

import sqlalchemy
import werkzeug.exceptions as flaskex
from models.workload_models import ModelWorkloadNetworkSchema, ModelWorkloadNetwork


class WorkloadNetwork():
    def __init__(self) -> None:
        pass

    def get_network_list(self):
        try:
            _network_schema = ModelWorkloadNetworkSchema(many=True)
            _network_list = ModelWorkloadNetwork().query.all()
            return _network_schema.dump(_network_list)
        except Exception as e:
            logging.error(str(e))
            return None

    def get_network_by_vlan_id(self, vlan_id):
        network = ModelWorkloadNetwork.query.filter_by(vlan_id=vlan_id).scalar()
        if not network:
            logging.info(f"The vlan {vlan_id} does not exist in the database, we will create a new one")
            network = ModelWorkloadNetwork.create_new_network(vlan_id=vlan_id, vlan_name='Undefine')
        return ModelWorkloadNetworkSchema().dump(network)

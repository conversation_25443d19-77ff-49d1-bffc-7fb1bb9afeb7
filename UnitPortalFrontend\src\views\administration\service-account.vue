<template>
  <div class="app-container" style="height:100%;width:100%">
    <div>
      <el-button class="filter-item"  type="primary" @click="handle_create" v-show="this.$store.getters.all_privilege.role_ntx.view_user!='empty'">
        Create New Service Account
      </el-button>
      <el-button style='float:right' class="filter-item"  type="danger" @click="handle_delete" v-show="this.$store.getters.all_privilege.role_ntx.view_user!='empty'">
        Delete
      </el-button>
    </div>
    <div>
      <el-table
        :data="saList"
        border
        fit
        highlight-current-row
        style="width: 100%"
        class='template-table'
        ref='saTable'
        @row-click="handle_row_click"
        @cell-mouse-enter="handleCellHover"
        @cell-mouse-leave="handleCellLeave"
        
      > 
      
        <el-table-column prop="id" label="ID" width="180" class="cell-centent">
          <template slot-scope="{row}" >
            <div :class="{ 'hover-cell': hoveredCell === row.id }">{{ row.id }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="Name" width="180">
          <template slot-scope="{row}">
            <div :class="{ 'hover-cell': hoveredCell === row.id }">{{ row.username }}</div>
          </template>
        </el-table-column> 
        <el-table-column prop="usage" label="Usage" width="180">
          <template slot-scope="{row}">
            <div :class="{ 'hover-cell': hoveredCell === row.id }">{{ row.usage }}</div>
          </template>
        </el-table-column> 
      </el-table>
    </div>
    <div>
      <el-dialog :title="'Create new service account'" :visible.sync="dialogFormVisible" style="width:1500px;margin-left:12%" >
        <div>
          <el-form ref="dataForm" :rules="rules" :model="addSaForm" label-position="left" label-width="125px" style="width: 600px; margin-left:50px;">
          <el-form-item label="User Name" style="width:70%" > 
            <el-input v-model="addSaForm.username" placeholder="Please input"/>
          </el-form-item>
          <el-form-item label="Password" style="width:70%">
            <el-input  v-model=addSaForm.password :type="addSaForm.revealPwd ? null : 'password'" placeholder="Please input"/>
            <span class="show-pwd" @click="addSaForm.revealPwd=!addSaForm.revealPwd" >
              <svg-icon :icon-class="addSaForm.revealPwd ? 'eye-open' : 'eye'" />
            </span>
          </el-form-item>
          <el-form-item label="Usage" style="width:70%">
            <el-input v-model="addSaForm.usage" placeholder="Please input"/>
          </el-form-item>
        </el-form>
        </div>
        
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="addSa()">
            Confirm
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
  
<script>
  import {GetSAList, CreateSA, DeleteSA} from '@/api/common'
  import waves from '@/directive/waves' // waves directive
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination

  // arr to obj, such as { CN : "China", US : "USA" }
  // const calendarTypeKeyValue = prismoptions.reduce((acc, cur) => {
  //   acc[cur.key] = cur.display_name
  //   return acc
  // }, {})

  export default {
    name: 'saTable',
    components: { Pagination },
    directives: { waves },
    data() {
      return {
        saList: null,
        addSaForm: {
          username: '',
          password: '',
          usage: '',
          revealPwd: false
        },
        rules: {
          username: { required: true, message: 'Username is required', trigger: 'change' },
          password: { required: true, message: 'password is required', trigger: 'change' },
          usage: { required: true, message: 'usgae is required', trigger: 'change' },
        },
        dialogFormVisible: false,
        selectedrow: '',
        hoveredCell: ''
      }
    },
    created() {
      // this.listLoading = true
      this.getSaList()
    },
    methods: {
      getSaList() {
        GetSAList(this.$store.getters.token).then(response => {
          this.saList = response.data
        })
      },
      handle_create() {
        // this.resetTemp()
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      handle_delete() {
        this.$confirm('You sure??', '?!', {
          confirmButtonText: 'YES',
          cancelButtonText: 'NO',
          type: 'danger'
        }).then(() => {
          this.deleteSA()
        });
      },
      handleCellHover(row) {
        console.log("hover...")
        this.hoveredCell = row.id;
        console.log(this.hoveredCell)
        console.log(row.id)
      },
      handleCellLeave(row) {
        this.hoveredCell = null;
      },
      handle_row_click(row) {
        console.log("Role selected: " + row)
        this.selectedrow = row
      },
      addSa() {
        let payload = {
          "username": this.addSaForm.username,
          "password": this.addSaForm.password,
          "usage":this.addSaForm.usage,
        }
        CreateSA(payload).then(() => {
          this.$notify({
            title: 'Success',
            message: 'SA added.',
            type: 'success',
            duration: 2000
          })
          this.dialogFormVisible = false
          this.get_prism_list()
        })
        .catch((error) => {
          this.dialogFormVisible  = false
          this.$notify({
            title: 'Error',
            message: 'Failed to create SA.',
            type: 'error',
            duration: 2000
          })
        })
      },
      deleteSA() {
        console.log("selection:" + this.selectedrow.id)
        let saId = this.selectedrow.id
        console.log(saId)
        DeleteSA(saId).then(() => {
          this.$notify({
            title: 'Success',
            message: 'SA deleted.',
            type: 'success',
            duration: 2000
          })
          this.get_prism_list()
        })
        .catch((error) => {
          this.$notify({
            title: 'Error',
            message: 'Failed to delete SA.',
            type: 'error',
            duration: 2000
          })
        })
      }
    },
    beforeDestroy(){
      //
    }
  }
</script>

<style>
  .hover-cell { cursor: pointer; /* 鼠标悬停时显示为手指 */ }
  .cell-centent {
    width: 100%;
    height: 100%;
    display: flex;
  }
</style>
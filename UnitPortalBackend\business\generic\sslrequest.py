import ipaddress
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa

class SslRequest:
    def generate_private_key(prv_file=None):
        try:
            private_key = rsa.generate_private_key(
                public_exponent = 65537,
                key_size = 2048,
                backend = default_backend()
            )
            with open(prv_file, "wb") as prv_key:
                prv_key.write(
                    private_key.private_bytes(
                        encoding = serialization.Encoding.PEM,
                        format = serialization.PrivateFormat.PKCS8,
                        encryption_algorithm = serialization.NoEncryption()
                    )
                )
            return private_key
        except Exception as e:
            return False

# def generate_public_key(prv_file=None, pub_file=None, **kwargs):
#     subject = x509.Name(
#         [
#             x509.NameAttribute(NameOID.COUNTRY_NAME, kwargs["country"]),
#             x509.NameAttribute(
#                 NameOID.STATE_OR_PROVINCE_NAME, kwargs["state"]
#             ),
#             x509.NameAttribute(NameOID.LOCALITY_NAME, kwargs["locality"]),
#             x509.NameAttribute(NameOID.ORGANIZATION_NAME, kwargs["org"]),
#             x509.NameAttribute(NameOID.COMMON_NAME, kwargs["hostname"]),
#         ]
#     )

    def generate_csr(prv_key=None, csr_file=None, **kwargs):
        try:
            subject = x509.Name(
                [
                    x509.NameAttribute(NameOID.ORGANIZATION_NAME, kwargs["org"]),
                    x509.NameAttribute(NameOID.ORGANIZATIONAL_UNIT_NAME, kwargs["ou"]),
                    x509.NameAttribute(NameOID.COUNTRY_NAME, kwargs["country"]),
                    x509.NameAttribute(NameOID.COMMON_NAME, kwargs["cn"])
                ]
            )
            alt_names = []
            for dns_name in kwargs.get('dns_names'):
                alt_names.append(x509.DNSName(dns_name))
            for ip_addr in kwargs.get('ip_addrs'):
                alt_names.append(x509.IPAddress(ipaddress.IPv4Address(ip_addr)))
            san = x509.SubjectAlternativeName(alt_names)
            builder = (
                x509.CertificateSigningRequestBuilder()
                .subject_name(subject)
                .add_extension(san, critical=False)
            )
            csr = builder.sign(prv_key, hashes.SHA256(), default_backend())
            with open(csr_file, "wb") as csr_key:
                csr_key.write(csr.public_bytes(serialization.Encoding.PEM))
            return csr
        except Exception as e:
            return False
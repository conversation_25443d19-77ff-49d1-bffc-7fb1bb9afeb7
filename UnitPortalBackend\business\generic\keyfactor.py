# installed module
import base64
import json
import logging
import random
import re
import string
import traceback
import sys
import time
import functools
import requests
import datetime

from datetime import timezone
from flask import jsonify
from jinja2 import Template
import werkzeug.exceptions as flaskex
# local file
import static.SETTINGS as SETTING
from base_path import application_path
from business.authentication.authentication import ServiceAccount
from business.generic.commonfunc import CommonRestCall

class KeyFactorAPI:
    def __init__(self, fqdn=None, username=None, password=None, logger=logging, retry=5) -> None:
        self.api_endpoint = f"https://{fqdn}/"
        if not (username and password):
            return False, "Missing authentication for KeyFactor"
        self.username = username
        self.password = password
        self.logger = logger
        self.retry = retry
        self.rest = CommonRestCall(self.username, self.password, logger=self.logger)

    def call_keyfactor_get():
        pass
    
    def call_keyfactor_post(self, request_url, payload):
        url = f"{self.api_endpoint}{request_url}"
        headers = {
            "Content-Type": "application/json",
            "x-keyfactor-requested-with": "APIClient",
            "x-keyfactor-api-version": "1",
            "x-certificateformat": "PEM"
        }
        try:
            res = self.rest.call_restapi(url=url, method="POST", headers=headers, payload=payload)
            if res.status_code in [200, 201, 202]:
                return True, res.json()
            else:
                return False, res.json()
        except Exception as e:
            return False, str(repr(traceback.format_exception(sys.exception())))
    
    def enroll_certificate_by_csr(self, csr_str=None, template=None, cert_authority=None, cert_ownergroup=None, email_contact=None, include_chain=False):
        request_url = "KeyfactorApi/Enrollment/CSR"
        json = {
            "CSR": csr_str,
            "IncludeChain": include_chain,
            "CertificateAuthority": cert_authority,
            "Timestamp": datetime.datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
            "Template": template,
            "Metadata": {
                "Certificate_Owner_Group": cert_ownergroup,
                "Email-Contact1": email_contact
            }
        }
        res, data = self.call_keyfactor_post(request_url=request_url, payload=json)
        if res and data:
            return True, data['CertificateInformation']['Certificates'][0]
        else:
            self.logger.error(f"Failed to generate the certificate, reason is {data}")
            return False, data
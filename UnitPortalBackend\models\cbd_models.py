from models.database import db, ma


class ModelDhCbd(db.Model):
    __tablename__           = 'dh_cbd'
    id                      = db.Column(db.String(100), primary_key=True)
    bu_type                 = db.Column(db.String(100))
    bu_code                 = db.Column(db.String(100))
    name                    = db.Column(db.String(100))
    street                  = db.Column(db.String(100))
    postal_code             = db.Column(db.String(100))
    city                    = db.Column(db.String(100))
    country_code            = db.Column(db.String(100))
    time_zone               = db.Column(db.String(100))
    latitude                = db.Column(db.Float)
    longitude               = db.Column(db.Float)


class ModelCbdSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelDhCbd
        load_instance = True
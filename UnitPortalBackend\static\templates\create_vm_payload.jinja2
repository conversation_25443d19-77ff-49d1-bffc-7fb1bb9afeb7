{
  "spec": {
    "name": "{{ vm_name }}",
    "resources": {
      "memory_size_mib": {{ memory_size_mib }},
      "num_vcpus_per_socket": {{ num_vcpus_per_socket }},
      "num_sockets": {{ num_sockets }},
      "boot_config": {
        "boot_device": {
            "disk_address": {
                "device_index": 0,
                "adapter_type": "SCSI"
            }
        },
        "boot_type": "{{ boot_type }}"
      },
      {% if (workload_type == 'linux') or (workload_type == 'network') %}
      "machine_type": "PC",
      {% else %}
      "machine_type": "Q35",
      {% endif %}
      "disk_list": [
          {% for disk_size in disks %}
          {
            "disk_size_bytes": {{ disk_size }},
            "device_properties": {
              "device_type": "DISK",
              "disk_address": {
                "device_index": {{ loop.index }},       {# index start from 1 #}
                "adapter_type": "SCSI"
              }
            }
          },
          {% endfor %}
          {
            "disk_size_bytes": {{ system_disk_size }},
            "device_properties": {
              "device_type": "DISK",
              "disk_address": {
                "device_index": 0,
                "adapter_type": "SCSI"
              }
            },
            "data_source_reference": {
                "kind": "image",
                "uuid": "{{ image_uuid }}"
            }
          }
      ],
      {% if workload_type == 'linux' -%}
      "guest_customization": {
        "cloud_init": {
            "user_data": "{{ user_data }}"
        }
      },
      {%- endif %}
      "nic_list": [
        {
            "subnet_reference": {
                "kind": "subnet",
                "uuid": "{{ network_uuid }}"
            }
        }
      ]
    },
    "cluster_reference": {
      "kind": "cluster",
      "uuid": "{{ cluster_uuid }}"
    }
  },
  "metadata": {
    "kind": "vm"
  }
}
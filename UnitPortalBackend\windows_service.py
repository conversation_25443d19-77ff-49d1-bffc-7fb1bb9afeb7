# pylint: skip-file
import logging
import win32serviceutil
import win32service
import win32event
from app import prepare_app, run_app
from business.distributedhosting.scheduler.atm_scheduler import auto_maintenance_scheduler, atm_scheduler
from business.distributedhosting.scheduler.scheduler_config import SchedulerConfig
from business.distributedhosting.scheduler.seamless_lcm_scheduler import seamless_lcm_scheduler
from scheduler import add_scheduler



class UnitPortalBackendService(win32serviceutil.ServiceFramework):
    _svc_name_ = "UnitPortalBackend"
    _svc_display_name_ = "Unit Portal Backend"
    _svc_description_ = "Start the Unit Portal Backend program once the system boots."

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)

    def SvcStop(self):
        logging.info("Stopping windows service...")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        # TODO: Currently service will stuck at "Stopping".
        raise RuntimeError('Shutdown')
        self.ReportServiceStatus(win32service.SERVICE_STOPPED)

    def SvcDoRun(self):
        add_scheduler()
        app = prepare_app()
        app.config.from_object(SchedulerConfig())
        atm_scheduler.init_app(app)
        auto_maintenance_scheduler.start()
        seamless_lcm_scheduler.start()
        run_app(app)


if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(UnitPortalBackendService)
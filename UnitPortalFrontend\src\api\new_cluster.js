import { endpoint } from './endpoint'
import request from '@/utils/request'

export function CreateNewRetailCluster(payload) {
  let res =  request.post(`${endpoint}/ntx/automation/new_cluster/retail`, payload)
  return res
}

export function CreateNewMetroCluster(payload) {
  let res =  request.post(`${endpoint}/ntx/automation/new_cluster/warehouse`, payload)
  return res
}

export function GetNewClusterTaskList(facility_type) {
  let res =  request.get(`${endpoint}/ntx/automation/new_cluster/${facility_type}`)
  return res
}

export function GetNewClusterTaskDetail(facility_type, task_id){
  let res =  request.get(`${endpoint}/ntx/automation/new_cluster_task/${facility_type}/${task_id}`)
  return res
}

export function GetNewClusterLog(facility_type, task_id){
  let res =  request.get(`${endpoint}/ntx/automation/new_cluster/log/${facility_type}/${task_id}`)
  return res
}

export function AbortClusterSubTask(facility_type, task_id, data) {
  return request({
    url: `${endpoint}/ntx/automation/new_cluster/abort/${facility_type}/${task_id}`,
    method: 'post',
    data: data
  })
}

<!-- HTML for the Swagger UI -->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Unit Portal API Documentation</title>
    <link rel="stylesheet" type="text/css"
        href="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.1.0/swagger-ui.css">
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }

        *,
        *:before,
        *:after {
            box-sizing: inherit;
        }

        body {
            margin: 0;
            background: #fafafa;
        }

        #auth-container {
            position: fixed;
            top: 0;
            right: 150px;
            padding: 10px;
            z-index: 1;
            display: flex;
            gap: 20px;
        }

        .auth-form {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .auth-form input {
            margin: 0 5px;
            padding: 5px;
        }

        .auth-form button {
            background: #4990e2;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 4px;
        }

        .auth-form button:hover {
            background: #357abd;
        }
    </style>
</head>

<body>
    <!-- Add authentication forms -->
    <!-- <div id="auth-container">
        <div class="auth-form">
            <input type="text" id="username" placeholder="Username">
            <input type="password" id="password" placeholder="Password">
            <button onclick="loginAndSetToken()">Login</button>
        </div>
    </div> -->
    <div id="swagger-ui"></div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.1.0/swagger-ui-bundle.js"> </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.1.0/swagger-ui-standalone-preset.js"> </script>
    <script>
        let swaggerUI;

        window.onload = function () {
            swaggerUI = SwaggerUIBundle({
                url: "{{ url_for('flask-apispec.swagger-json') }}",
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                onComplete: function () {
                    // Add event listener for the Authorize button click
                    const authorizeButton = document.querySelector('.authorize');
                    if (authorizeButton) {
                        authorizeButton.addEventListener('click', function () {
                            // Wait for the modal to appear
                            setTimeout(() => {
                                const basicAuthInput = document.querySelector('input[type="password"]');
                                if (basicAuthInput) {
                                    const basicAuthWrapper = basicAuthInput.closest('.auth-container');
                                    if (basicAuthWrapper) {
                                        // Add a note about Basic Auth usage
                                        const note = document.createElement('div');
                                        note.style.color = '#707070';
                                        note.style.fontSize = '13px';
                                        note.style.marginTop = '5px';
                                        note.innerHTML = 'Note: Basic Auth is only for the login endpoint. After login, use the Bearer token for all other endpoints.';
                                        basicAuthWrapper.appendChild(note);
                                    }
                                }
                            }, 100);
                        });
                    }
                }
            });
            window.ui = swaggerUI;

            // Add interceptor for Basic Auth login
            const originalAuthorize = swaggerUI.authActions.authorize;
            swaggerUI.authActions.authorize = async function (auth) {
                // Check if this is a Basic Auth login attempt
                if (auth.BasicAuth && auth.BasicAuth.value) {
                    try {
                        // Create Basic Auth token from username and password
                        const credentials = auth.BasicAuth.value;
                        const basicAuth = btoa(`${credentials.username}:${credentials.password}`);

                        // Make the login request
                        const response = await fetch('/api/v1/login', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Basic ${basicAuth}`
                            }
                        });

                        if (!response.ok) {
                            throw new Error(response.status === 401 ? 'Invalid credentials' : 'Login failed');
                        }

                        const data = await response.json();

                        // Set the Bearer token after successful login
                        auth = {
                            Bearer: {
                                name: "Bearer",
                                schema: {
                                    type: "apiKey",
                                    in: "header",
                                    name: "Authorization",
                                },
                                value: `Bearer ${data.token}`
                            }
                        };

                        alert('Login successful! Bearer token has been set for all endpoints.');
                    } catch (error) {
                        alert('Login failed: ' + error.message);
                        return;
                    }
                }

                // Call the original authorize function with potentially modified auth object
                return originalAuthorize.call(swaggerUI.authActions, auth);
            };
        }
    </script>
</body>

</html>
<template>
  <div class="app-container">
    <el-container>
      <el-header style="height: 12vh; padding:6%">
        <span style="font-size: 50px;font-weight: bolder;" >Welcome to Seamless LCM</span>
      </el-header>
      <el-main style=" padding: 4% 6% 0 6%; height: 50vh;">
        <el-row :gutter="35" >
          <el-col :span="12" >
            <el-card :body-style="{ display:'flex', flexDirection: 'column', height:'100%',overflow: 'hidden'}">
              <el-container>
                <el-header>
                  <span style="font-size: 36px;font-weight: bolder;">SPP LCM</span>
                </el-header>
                <el-main style="height: 100%;">
                  <el-row :gutter="1" type="flex" style="padding: 6%;">
                  <el-col :span="8" >
                    <svg-icon icon-class="node" class-name="card-panel-icon"  />
                  </el-col>
                  <el-col  :span="8">
                    <div style="padding: 14px;  line-height: 20px;">
                      <span style="font-size: 16px; font-family: fantasy;">Click here to explore the SPP LCM, you can either manually fire a SPP LCM for a cluster or create a LCM scheduler</span>
                    </div>
                  </el-col>
                  <el-col  :span="8">
                    <svg-icon icon-class="right_arrow" class-name="card-arrow-icon" style="margin-right: 0; " @click="jumpto('/lcm/underdevelopment')"/>
                  </el-col>
                  </el-row>
                </el-main>
              </el-container>
              <!-- <img src="~examples/assets/images/hamburger.png" class="image"> -->
              <!-- <el-container> -->   
              <!-- </el-container> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 626 626" width="478" height="478" preserveAspectRatio="xMidYMid meet" style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;"><defs><clipPath id="__lottie_element_2"><rect width="626" height="626" x="0" y="0"/></clipPath></defs><g clip-path="url(#__lottie_element_2)"><g transform="matrix(1,0,0,1,313,313)" opacity="1" style="display: block;"><g opacity="1" transform="matrix(1,0,0,1,0,0)"><path stroke-linecap="round" stroke-linejoin="round" fill-opacity="0" stroke="rgb(74,144,226)" stroke-opacity="1" stroke-width="50" d=" M92.5,-86.5 C92.5,-86.5 188.5,13 188.5,13 C188.5,13 87.5,118 87.5,118"/></g></g><g transform="matrix(1,0,0,1,194,313)" opacity="0.66" style="display: block;"><g opacity="1" transform="matrix(1,0,0,1,0,0)"><path stroke-linecap="round" stroke-linejoin="round" fill-opacity="0" stroke="rgb(74,144,226)" stroke-opacity="1" stroke-width="50" d=" M92.5,-86.5 C92.5,-86.5 188.5,13 188.5,13 C188.5,13 87.5,118 87.5,118"/></g></g><g transform="matrix(1,0,0,1,73,313)" opacity="0.33" style="display: block;"><g opacity="1" transform="matrix(1,0,0,1,0,0)"><path stroke-linecap="round" stroke-linejoin="round" fill-opacity="0" stroke="rgb(74,144,226)" stroke-opacity="1" stroke-width="50" d=" M92.5,-86.5 C92.5,-86.5 188.5,13 188.5,13 C188.5,13 87.5,118 87.5,118"/></g></g></g></svg>-->     
            </el-card>
          </el-col>
          <el-col :span="12" >
            <el-card :body-style="{ display:'flex', flexDirection: 'column', height:'100%',overflow: 'hidden'}">
              <el-container>
                <el-header>
                  <span style="font-size: 36px;font-weight: bolder;">AOS/AHV LCM</span>
                </el-header>
                <el-main style="height: 100%;">
                  <el-row :gutter="1" type="flex" style="padding: 6%;">
                    <el-col :span="8" >
                        <svg-icon icon-class="prism" class-name="card-panel-icon" />
                    </el-col>
                    <el-col  :span="8">
                        <div style="padding: 14px;  line-height: 20px;">
                          <span style="font-size: 16px; font-family: fantasy;">Click here to explore the AHV/AOS LCM, you can either manually fire a AHV/AOS LCM for a cluster or create a LCM scheduler</span>
                        </div>
                    </el-col>
                    <el-col  :span="8">
                      <svg-icon icon-class="right_arrow" class-name="card-arrow-icon" style="margin-right: 0;"@click="jumpto('/lcm/lcm_plans')"/>
                    </el-col>
                  </el-row>
                </el-main>
              </el-container>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
      <el-footer style="height: 17vh;">
        <div style="width:15%; float:left;margin-left: 2%;"><h3>Product Type</h3></div>
        <div style="width:42%; float:left;margin-left: 2%;"><h3>Firmware Version</h3></div>
        <div style="float:left;"><h3>AOS Version</h3></div>
        <div class="link-type" style="float:right;" @click="jumpto('slcm_detail')">More</div>
        <el-table
          :data="versiondata"
          style="width: 100%"
          :show-header="false"
          :row-class-name="trowStyle">
          <el-table-column label="Product Type" align="center">
            <el-table-column
              prop="facility_type"  min-width="8%" align="center" show-overflow-tooltip>
            </el-table-column>
          </el-table-column>
          <el-table-column label="Firmware Version" align="center">
            <el-table-column
              prop="sppvers" min-width="6%" align="center">
            </el-table-column>
            <el-table-column
              prop="sppstatistic"  min-width="16%" align="center" show-overflow-tooltip>
            </el-table-column>
          </el-table-column>
          
          <el-table-column label="AOS Version" align="center">
            <el-table-column
              prop="aosvers"  min-width="6%" align="center">
            </el-table-column>
            <el-table-column
              prop="aosstatistic"  min-width="16%" align="center" show-overflow-tooltip>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import { GetTargetVersion} from  '@/api/automation'
import { GetPCPECorrespondence_Lcm} from '@/api/nutanix'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  name: 'SLCM',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      siab_aos_targetversion: null,
      wiab_aos_targetversion: null,
      versiondata:[
        {facility_type: 'Siab', sppvers:'2023.***********',sppstatistic:'133/190 EU:33/180 CN:33/180 AP:33/180 NA:33/180', aosvers:'AOS 6.5.6',aosstatistic:''},
        {facility_type: 'Wiab', sppvers:'2023.***********',sppstatistic:'133/190 EU:33/180 CN:33/180 AP:33/180 NA:33/180', aosvers:'AOS 6.5.5',aosstatistic:'133/190 EU:33/180 CN:33/180 AP:33/180 NA:33/180'},
        // {aosvers:'AOS 6.5.6',aosstatistic:'133/190 EU:33/180 CN:33/180 AP:33/180 NA:33/180'},
        // {aosvers:'AOS 6.5.5',aosstatistic:'133/190 EU:33/180 CN:33/180 AP:33/180 NA:33/180'}
      ]
    }
  },
  computed: {
    aosVersionCount() {
      return this.data.pe.reduce((count, region) => {
        return count + region.name.filter(item => item.aos_version == this.siab_aos_targetversion).length;
      }, 0);
    },
  },
  created() {
    this.load_pc_pe_list();
    this.get_targetversion();
  },
  methods: {
    trowStyle(data) {  
      const row = data.row  
      return row.allCount === row.buckleQuantity ? 'green-row' : 'red-row'  
      },
    jumpto(_path) {
      this.$router.push({
      path:_path,
      })
    },
    get_targetversion() {
      GetTargetVersion(this.$store.getters.token).then(response => {
        
          // this.temp.targetversion = response.data
          // console.log(this.temp.targetversion)
          response.data.forEach((item)=>{
            if(item.index_label=='default'){
              if(item.aos_version){
                this.siab_aos_targetversion = item.aos_version
              }else{
              alert("No Siab AOS target version found, please check the target version configuration.")
              }
            }
            if(item.index_label=='Warehouse'){
              if(item.aos_version){
                this.wiab_aos_targetversion = item.aos_version
              }else{
                alert("No Wiab AOS target version found, please check the target version configuration.")
              }
            }
          })
      })
    },
    load_pc_pe_list(){
      GetPCPECorrespondence_Lcm().then(response => { 
        // console.log(response)
        this.pes_list = response.data.filter(item => item.pc !== "ssp-ppe-ntx.ikea.com" && item.pc !== "ssp-russia-ntx.ikea.com" && item.pc !== "ssp-dhd2-ntx.ikead2.com" && item.pc !== "ssp-dt-ntx.ikeadt.com")
        console.log(this.pes_list)
        this.handle_verCounts(this.pes_list)
      })
    },
    handle_verCounts(data) {
      let totaldata =[]
      data.forEach(d =>{
        let aosCount = 0; 
        let totalCount = 0; 
        d.pe.forEach(region => {
          const names = region.name;
          totalCount += names.length; 
          aosCount += names.filter(item => item.aos_version === this.siab_aos_targetversion).length;
        });
        totaldata.push({
            pc: d.pc.split('-')[1].toUpperCase(), 
            matchedaosCount: aosCount,
            totalCount: totalCount
          });
      })
      
      this.versiondata[0].aosvers= "AOS " + this.siab_aos_targetversion
      let status = totaldata.map(item => {
        return  item.pc + " "+ item.matchedaosCount + "/" + item.totalCount
      }).join(";  ");
      console.log(status)
      this.versiondata[0].aosstatistic = status
    },
  }
}
</script>

<style lang="scss" scoped>
    
    .panel-group {
      height:70%;
      width: 99%;
      position: absolute;
      margin-left: 100px;
      .card-panel-col {
        margin-bottom: 4px;
      }
  
    }
    * {
      transition: 1s;
    }

    .box-card {
        height: 88vh;
      }
      .clearfix:before,
      .clearfix:after {
          display: table;
          content: "";
      }
      
      .clearfix:after {
          clear: both
      }
      .el-header{
        color: #333;
        text-align: center;
        
      }
      .el-footer{
        background-color: rgba(192, 189, 189, 0.578);
        color: #333;
        //text-align: center;
        height: 16vh;
        // line-height: 60px;
        // padding: 4%;
      }
      .el-main {
        color: #333;
        text-align: center;
      }
      body > .el-container {
        margin-bottom: 1px;
        height: 100%;
        
      }
      .el-card{
        height: 40vh;
        
      }
    .card-panel-icon {
      text-align: center;
      font-size: 14vh;
      padding: 4%;
    }
    .card-arrow-icon {
      text-align: center;
      font-size: 12vh;
      padding: 4%;
      cursor: pointer;
      &:hover {
        transition: transform 0.25s;
        transform: scale(1.4)
      }
    }
    ::v-deep .green-row {  
      background-color: rgba(192, 189, 189, 0.578) !important;  
    }  
    ::v-deep .red-row {  
      background-color: #F56C6C !important;  
    } 
</style>  
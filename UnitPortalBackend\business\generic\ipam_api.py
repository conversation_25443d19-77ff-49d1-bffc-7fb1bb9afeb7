import logging
import re

import requests

import static.SETTINGS as SETTING
from business.generic.commonfunc import CommonRestCall


class IpamAPI:
    def __init__(self,
                 username,
                 password,
                 endpoint=SETTING.IPAM["ENDPOINT"],
                 logger=logging,
                 retry=5):
        self.endpoint = endpoint
        self.retry = retry
        self.username = username
        self.password = password
        self.logger = logger if logger else logging
        self.rest = CommonRestCall(username=username, password=password, logger=logger, timeout=30)

    def query(self, request_url, **filters):
        i = 0
        while i < self.retry:
            try:
                url = f"https://{self.endpoint}/rest/{request_url}"
                param = {"WHERE": ""}
                for key in filters:
                    if param['WHERE']:
                        param['WHERE'] += f" and {key}='{filters[key]}'"
                    else:
                        param['WHERE'] += f"{key}='{filters[key]}'"
                print(param)
                output = requests.get(url=url, auth=(self.username, self.password), params=param, verify=False)
                print(output)
                return output
            except Exception as e:
                i += 1
                self.logger.warning(f"We have some issue when querying ipam, {str(e)}")

    def get_ipam_object_by_fqdn(self, fqdn: str) -> requests.Response:
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_address_list?WHERE=name='{fqdn}'",
            "method": "GET",
        }
        res = self.rest.call_restapi(**kwargs)
        return res

    def get_subnets_by_parent_subnet_name(self, parent_subnet_name: str) -> requests.Response:
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_block_subnet_list?WHERE=parent_subnet_name='{parent_subnet_name}'",
            "method": "GET",
        }
        return self.rest.call_restapi(**kwargs)

    def get_subnets_by_subnet_id(self, subnet_id):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_block_subnet_list?WHERE=subnet_id%3D%27{subnet_id}%27",
            "method": "GET"
        }
        return self.rest.call_restapi(**kwargs)

    def ip_block_subnet_list(self, parent_subnet_id=None, parent_subnet_name=None, subnet_id=None, start_hostaddr=None):
        url = f"https://{self.endpoint}/rest/ip_block_subnet_list"
        if parent_subnet_id:
            url += f"?WHERE=parent_subnet_id='{parent_subnet_id}'"
        elif parent_subnet_name:
            url += f"?WHERE=parent_subnet_name='{parent_subnet_name}'"
        elif subnet_id:
            url += f"?WHERE=subnet_id%3D%27{subnet_id}%27"
        elif start_hostaddr:
            url += f"?WHERE=start_hostaddr%3D%27{start_hostaddr}%27"
        kwargs = {
            "url": url,
            "method": "GET"
        }
        return self.rest.call_restapi(**kwargs)

    def dns_rr_add(self, rr_type, fqdn, value, dns_name):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/dns_rr_add?rr_name={fqdn}&rr_type={rr_type}&value1={value}&dns_name={dns_name}",
            "method": "POST"
        }
        return self.rest.call_restapi(**kwargs)

    def dns_rr_count(self, rr_type, rr_full_name):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/dns_rr_count?WHERE=rr_type='{rr_type}' and rr_full_name='{rr_full_name}'",
            "method": "GET"
        }
        return self.rest.call_restapi(**kwargs)

    def dns_rr_list(self, rr_type, rr_full_name):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/dns_rr_list?WHERE=rr_type='{rr_type}' and rr_full_name='{rr_full_name}'",
            "method": "GET"
        }
        return self.rest.call_restapi(**kwargs)

    def ip_find_free_address(self, subnet_id: str, max_find=30) -> requests.Response:
        kwargs = {
            "url": f"https://{self.endpoint}/rpc/ip_find_free_address",
            "method": "GET",
            "params": {"subnet_id": subnet_id, "max_find": max_find}
        }
        return self.rest.call_restapi(**kwargs)

    def ip_add(self, hostaddr, site_id, fqdn, pe, description=""):
        # fqdn      retse995-nt8888.ikea.com
        # vm_name   retse995-nt8888
        def _get_server_type():
            server_type = "BlackBox"
            if re.match(r'.*-NT[0-9].*', fqdn, re.IGNORECASE):
                server_type = "Windows"
            elif re.match(r'.*-LX[0-9].*|lip|pip|SAP', fqdn, re.IGNORECASE):
                server_type = "Linux"
            elif re.match(r'.*-NX[0-9].*', fqdn, re.IGNORECASE):
                server_type = "Nutanix"
            return server_type

        def _get_ip_statement():
            if re.match(r'.*-NXC', fqdn, re.IGNORECASE):
                ip_statement = "This is the Nutanix Cluster Record."
            elif re.match(r'.*-NXD', fqdn, re.IGNORECASE):
                ip_statement = f"This is the Nutanix Data Services IP Record for cluster :{pe}"
            elif re.match(r'.*-NXP', fqdn, re.IGNORECASE):
                ip_statement = f"This is the Nutanix Prism Central Record for cluster :{pe}"
            elif re.match(r'OOB', fqdn, re.IGNORECASE):
                ip_statement = f"This is an OOB record that belongs with Nutanix cluster :{pe}"
            elif re.match(r'CVM', fqdn, re.IGNORECASE):
                ip_statement = f"This is an CVM record that belongs with Nutanix cluster :{pe}"
            elif re.match(r'.*-NX7', fqdn, re.IGNORECASE):
                ip_statement = f"This is an AHV record that belongs with Nutanix cluster :{pe}"
            else:
                ip_statement = f"{description} This is a VM initially installed on Nutanix cluster :{pe}."
            return ip_statement

        server_type = _get_server_type()
        ip_statement = _get_ip_statement()
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_add",
            "method": "POST",
            "params": {
                "hostaddr": hostaddr,
                "site_id": int(site_id),
                "name": fqdn.lower(),
                "add_flag": "new_only",
                "ip_name_class": "IKEA/Server_Distribute",
                "ip_class_parameters": f"hostname={fqdn.split('.')[0].upper()}&dns_update=1&__eip_dns_update_inheritance_property=set&ikea_server_type={server_type}&ikea_network_ip_statement={ip_statement}&ikea_in_pci=0"        # TODO: 没生效？
            },
            "verify": False,
            # "retry": 1
        }
        self.logger.info(f"kwargs: {kwargs}")
        kwargs["auth"] = (self.username, self.password)
        return requests.request(**kwargs)

    def ip_edit(self, ip_id, ip_class_parameters: str):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_add",
            "method": "PUT",
            "params": {
                "ip_id": ip_id,
                "ip_class_parameters": ip_class_parameters
            }
        }
        return self.rest.call_restapi(**kwargs)

    def ip_alias_add(self, ip_id: str, alias: str) -> requests.Response:
        # Can only add 1 alias once
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_alias_add",
            "method": "POST",
            "params": {
                "ip_name": alias,
                "ip_id": ip_id,
                "add_flag": "new_only",
            }
        }
        return self.rest.call_restapi(**kwargs)

    def ip_alias_count(self, ip_id):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_alias_count",
            "method": "GET",
            "params": {
                "ip_id": ip_id
            }
        }
        return self.rest.call_restapi(**kwargs)

    def ip_alias_list(self, ip_id):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_alias_list",
            "method": "GET",
            "params": {
                "ip_id": ip_id
            }
        }
        return self.rest.call_restapi(**kwargs)

    def ip_address_list(self, name=None, subnet_id=None, name_pattern=None, hostaddr=None):
        if name:
            url = f"https://{self.endpoint}/rest/ip_address_list?WHERE=name='{name}'"
        if subnet_id:
            url = f"https://{self.endpoint}/rest/ip_address_list?WHERE=subnet_id='{subnet_id}'"
        if name_pattern:
            url = f"https://{self.endpoint}/rest/ip_address_list?WHERE=name LIKE '{name_pattern}'"
        if hostaddr:
            url = f"https://{self.endpoint}/rest/ip_address_list?WHERE=hostaddr='{hostaddr}'"
        kwargs = {
            "url": url,
            "method": "GET",
        }
        return self.rest.call_restapi(**kwargs)

    def ip_address_info(self, ip_id):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_address_info?ip_id={ip_id}",
            "method": "GET",
        }
        return self.rest.call_restapi(**kwargs)

    def ip_delete(self, ip_id=None, hostaddr=None, site_id=None):
        self.logger.info(f"Start to delete ip {hostaddr} on Ipam.")
        url = f"https://{self.endpoint}/rest/ip_delete?"
        if ip_id:
            url += f"ip_id={ip_id}"
        elif hostaddr and site_id:
            url += f"hostaddr={hostaddr}&site_id={site_id}"
        kwargs = {
            "url": url,
            "method": "DELETE"
        }
        self.logger.info("Delete IP completed.")
        return self.rest.call_restapi(**kwargs)

    def dns_server_list(self):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/dns_server_list",
            "method": "GET",
        }
        return self.rest.call_restapi(**kwargs)

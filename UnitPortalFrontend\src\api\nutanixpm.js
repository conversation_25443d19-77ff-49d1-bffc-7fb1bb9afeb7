import axios from 'axios'
import { endpoint } from './endpoint'
import request from '@/utils/request'

export function GetPMTaskList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/pmtasklist`,config)
  return res
}
export function GetNTXPMTasks(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/pmtasks`,config)
  return res
}

export function CreateNTXPM(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param.token}
  };
  let res =  request.post(`${endpoint}/ntx/pm/create`,param.data,config)
  console.log(res)
  return res
}

export function UpdateNTXPM(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param.token}
  };
  let res =  request.post(`${endpoint}/ntx/pm/update`,param.data,config)
  return res
}

export function AbortNTXPM(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res = request.post(`${endpoint}/ntx/pm/abort`,param.data,config)
  return res
}

export function DeleteNTXPM(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res = request.post(`${endpoint}/ntx/pm/delete`,param.data,config)
  return res
}

export function DownloadNTXPMLog(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token},
    responseType:'blob'
  }
  let res = request.post(`${endpoint}/download`,param.data,config)
  return res
}

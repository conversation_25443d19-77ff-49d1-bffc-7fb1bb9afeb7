import logging
from ipaddress import IPv4Network

from flask import abort
from ping3 import ping
from requests import HTTPError

from business.authentication.authentication import ServiceAccount
from business.generic.ipam_api import IpamAPI
from business.generic.ipam_exception import VlanMissingUnderSubnet, ErrorFindingAddress, NoIpAvailableUnderSubnet, \
    FailedToAssignIp, IpNotUnderVlan, IpOccupied


class Ipam:
    def __init__(self, sa=None, logger=logging):
        _sa = sa if sa else ServiceAccount(usage="nutanix_pm").get_service_account()
        self.rest_ipam = IpamAPI(username=_sa['username'], password=_sa['password'], logger=logger)
        self.logger = logger

    def assign_ip(self, vlan_id, pe, fqdn, alias_list=[], subnet_ip=None, user_specified_ip=None):
        self.logger.info(f"Start to assign ip for {fqdn}...")
        subnet = None
        if subnet_ip or user_specified_ip:
            subnet_ip = user_specified_ip if user_specified_ip else subnet_ip
            self.logger.info(f"Start to find subnet by IP {subnet_ip} on IPAM...")
            subnet = self.rest_ipam.ip_address_list(hostaddr=subnet_ip).json()[0]
            if not subnet:
                raise ErrorFindingAddress(subnet_ip)
        else:
            self.logger.info(f"Start to find parent subnet by fqdn '{pe}' on IPAM...")
            ipam_object = self.rest_ipam.ip_address_list(name=pe)
            if not ipam_object.content:
                raise ErrorFindingAddress(pe)
            self.logger.info(f"ipam_object: {ipam_object}")
            parent_subnet_name = ipam_object.json()[0]["parent_subnet_name"]  # assume 1 fqdn only mapping to 1 ipam object
            self.logger.info(f"Parent subnet name: {parent_subnet_name}")
            subnets = self.rest_ipam.get_subnets_by_parent_subnet_name(parent_subnet_name).json()
            for item in subnets:
                if int(item["vlmvlan_vlan_id"]) == vlan_id:
                    subnet = item
                    break
            if not subnet:
                raise VlanMissingUnderSubnet(vlan_id, parent_subnet_name)
        subnet_id = subnet["subnet_id"]
        site_id = subnet["site_id"]
        self.logger.info(f"Finding free address in subnet id: {subnet_id}")
        ip_id, ip_to_add = self._assign_ip(subnet_id, site_id, fqdn, pe, user_specified_ip)
        for alias in alias_list:
            self.add_alias(ip_id, alias)
        self.logger.info(f"IP {ip_to_add} assigned to {fqdn} successfully. ip_id is {ip_id}")
        return ip_to_add, ip_id

    def _assign_ip(self, subnet_id, site_id, fqdn, pe, user_specified_ip=None):
        self.logger.info("Assigning ip...")
        ip_to_add = user_specified_ip
        for _ in range(5):
            if not user_specified_ip:
                ip_to_add = self._get_available_ip(subnet_id)
            try:
                res = self.rest_ipam.ip_add(ip_to_add, site_id, fqdn, pe)
                res.raise_for_status()
                self.logger.debug(f"res.json(): {res.json()}")
                ip_id = res.json()[0]["ret_oid"]
                return ip_id, ip_to_add
            except HTTPError as e:
                self.logger.warning(f"Ipam responded Error: {e}")
                self.logger.warning("Assign IP failed, is there another VM scrambling for the same IP at the moment?")
                self.logger.warning("Trying to assign another IP... ")
        raise FailedToAssignIp(ip_to_add, fqdn)

    def _get_available_ip(self, subnet_id):
        free_ips = self.rest_ipam.ip_find_free_address(subnet_id)
        if not free_ips.content:
            abort(500, f"No free ip under subnet id: {subnet_id}!")
        for ip in free_ips.json():
            ip_to_add = ip["hostaddr"]
            self.logger.info(f"Checking if {ip_to_add} is already in use...")
            if not ping(ip_to_add):
                self.logger.info(f"IP {ip_to_add} is able to use.")
                return ip_to_add
            self.logger.info(f"IP {ip_to_add} is not able to use.")
        raise NoIpAvailableUnderSubnet(subnet_id)

    def add_alias(self, ip_id, alias):
        self.logger.info(f"Start to add alias {alias} to ip {ip_id}...")
        self.rest_ipam.ip_alias_add(ip_id, alias)
        self.logger.info("Finished to add alias.")

    def check_ip_is_under_vlan(self, ip, vlan_id):
        self.logger.info(f"Checking if {ip} is under vlan {vlan_id}...")
        subnet_id = self.rest_ipam.ip_address_list(hostaddr=ip).json()[0]["subnet_id"]
        expected = self.rest_ipam.ip_block_subnet_list(subnet_id=subnet_id).json()[0]["vlmvlan_vlan_id"]
        if int(expected) != int(vlan_id):
            raise IpNotUnderVlan(ip, vlan_id)

    def check_ip_is_occupied(self, ip):
        self.logger.info(f"Checking if {ip} is occupied...")
        res = self.rest_ipam.ip_address_list(hostaddr=ip)
        if res.json()[0].get("name"):
            raise IpOccupied(ip, res.json()[0].get("name"))

    def if_vmrecord_existed_ipam(self, fqdn):
        self.logger.info(f"Start to check if {fqdn} existed in IPAM...")
        ipam_object = self.rest_ipam.get_ipam_object_by_fqdn(fqdn)
        if not ipam_object.content:
            return False
        if not [vm for vm in ipam_object.json() if vm["pool_name"] != "DHCP"]:
            # ignore the DHCP IPs
            return False
        return True

    def get_ip_address_info(self, ip_id):
        return self.rest_ipam.ip_address_info(ip_id).json()

    def get_ip_class_parameters_dict(self, ip_id):
        ip_class_parameters_list = self.rest_ipam.ip_address_info(ip_id).json()[0]['ip_class_parameters'].split('&')
        param_dict = {}
        for _ in ip_class_parameters_list:
            param_dict[_.split("=")[0]] = _.split("=")[1]
        return param_dict

    def get_hostaddr_by_name(self, name):
        res = self.rest_ipam.ip_address_list(name)
        if res.status_code == 204:
            return None
        for ip_info in res.json():
            if ip_info.get("name").lower() == name.lower():
                return ip_info.get("hostaddr")
        return None

    def cleanup_fqdn(self, fqdn):
        hostaddr = self.get_hostaddr_by_name(fqdn)
        if not hostaddr:
            self.logger.warning(f"Can't find fqdn {fqdn} on Ipam, is it already gone?")
            return
        res = self.rest_ipam.ip_delete(hostaddr=hostaddr, site_id=2)
        if res.status_code == 204:
            self.logger.warning("IPAM response return 204, seems this IP doesn't exist on IPAM... But let's ignore.")

    @staticmethod
    def get_value_from_class_parameters(key, class_parameters: str):
        class_parameters_list = class_parameters.split('&')
        param_dict = {}
        for _ in class_parameters_list:
            param_dict[_.split("=")[0]] = _.split("=")[1]
        return param_dict[key]

    @staticmethod
    def calculate_subnet_mask(subnet_size):
        mapping = {
            "4": "***************",
            "8": "***************",
            "16": "***************",
            "32": "***************",
            "64": "***************",
            "128": "***************",
            "256": "*************",
            "512": "*************",
            "1024": "*************",
            "2048": "*************",
            "4096": "*************",
            "8192": "*************",
            "16384": "*************",
            "32768": "*************",
            "65536": "***********",
            "131072": "***********",
            "262144": "255.252.0.0",
            "524288": "255.248.0.0",
            "1048576": "255.240.0.0",
            "2097152": "255.224.0.0",
            "4194304": "255.192.0.0",
            "8388608": "255.128.0.0",
            "16777216": "255.0.0.0",
            "33554432": "254.0.0.0",
            "67108864": "252.0.0.0",
            "134217728": "248.0.0.0",
            "268435456": "240.0.0.0",
            "536870912": "224.0.0.0",
            "1073741824": "192.0.0.0",
            "2147483648": "128.0.0.0",
            "4294967296": "0.0.0.0"
        }
        return mapping[subnet_size]

    @staticmethod
    def calculate_cidr(subnet_size):
        """e.g. subnet_size = 64 -> cidr: xxx.xxx.xxx.xxx/26"""
        mapping = {
            "4": "30",
            "8": "29",
            "16": "28",
            "32": "27",
            "64": "26",
            "128": "25",
            "256": "24",
            "512": "23",
            "1024": "22",
            "2048": "21",
            "4096": "20",
            "8192": "19",
            "16384": "18",
            "32768": "17",
            "65536": "16",
            "131072": "15",
            "262144": "14",
            "524288": "13",
            "1048576": "12",
            "2097152": "11",
            "4194304": "10",
            "8388608": "9",
            "16777216": "8",
            "33554432": "7",
            "67108864": "6",
            "134217728": "5",
            "268435456": "4",
            "536870912": "3",
            "1073741824": "2",
            "2147483648": "4",
            "4294967296": "0"
        }
        return mapping[subnet_size]

    @staticmethod
    def calculate_netmask(subnet):
        cidr = Ipam.calculate_cidr(subnet['subnet_size'])
        network = IPv4Network(f"{subnet['start_hostaddr']}/{cidr}")
        return network.netmask.compressed

    @staticmethod
    def calculate_all_ips_under_subnet(start_hostaddr, subnet_size):
        cidr = Ipam.calculate_cidr(subnet_size)
        return [str(addr) for addr in IPv4Network(f'{start_hostaddr}/{cidr}')]  # e.g. IPv4Network('************/28')

    @staticmethod
    def find_subnet_by_vlan_id(vlan_id, subnets_under_parent_subnet):
        for s in subnets_under_parent_subnet:
            if int(s["vlmvlan_vlan_id"]) == int(vlan_id):
                return s
        return None

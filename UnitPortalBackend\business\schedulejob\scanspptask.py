from flask import Flask
import logging
from business.generic.commonfunc import DBConfig
from models.models import db
from business.distributedhosting.nutanix.automation.lcm import LCM, AosLCM


def scan_spp_task():
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    app.app_context().push()
    db.init_app(app)
    logging.info("Scaning SPP tasks............")
    with app.app_context(): # app.app_context().push()  same.
        lcm = LCM()
        lcm.check_task_status()
        db.session.remove()
        db.engine.dispose()
        db.session.close()


def scan_aos_task():
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    app.app_context().push()
    db.init_app(app)
    logging.info("Scaning AOS tasks............")
    # with app.app_context(): # app.app_context().push()  same.
    #     aos_lcm = AosLCM()
    #     aos_lcm.monitor()
    #     db.session.remove()
    #     db.engine.dispose()
    #     db.session.close()
    aos_lcm = AosLCM()
    aos_lcm.monitor()
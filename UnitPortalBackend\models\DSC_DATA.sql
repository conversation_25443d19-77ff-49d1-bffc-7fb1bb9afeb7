-- ----------------------------
-- 2024-05-06 ZOEXU6
-- Initialize the data for dsc benchmark
-- ----------------------------
TRUNCATE TABLE dh_retail_ntx_auto_maintenance

insert dh_retail_ntx_auto_maintenance (pe_id,pe_name,pc,lock_id)
select id as pe_id ,name as pe_name ,prism as pc, 0 as lock_id
from dh_retail_ntx_pe as pe
where pe.status = 'Running' and
NOT EXISTS (
    SELECT 1
    FROM dh_retail_ntx_auto_maintenance as atm
    WHERE atm.pe_id = pe.id
);

TRUNCATE TABLE dh_retail_nutanix_auto_maintenance_schedule
INSERT INTO [dbo].[dh_retail_nutanix_auto_maintenance_schedule]([pc], [timezone], [day], [hour], [minute], [interval_min]) VALUES ('ssp-apac-ntx.ikea.com', 'Asia/Tokyo', 'thu,fri', 23, 0, 10);
INSERT INTO [dbo].[dh_retail_nutanix_auto_maintenance_schedule]([pc], [timezone], [day], [hour], [minute], [interval_min]) VALUES ('ssp-china-ntx.ikea.com', 'Asia/Shanghai', 'mon,tue', 23, 0, 10);
INSERT INTO [dbo].[dh_retail_nutanix_auto_maintenance_schedule]([pc], [timezone], [day], [hour], [minute], [interval_min]) VALUES ('ssp-eu1-ntx.ikea.com', 'Europe/Paris', 'tue,wed', 20, 0, 2);
INSERT INTO [dbo].[dh_retail_nutanix_auto_maintenance_schedule]([pc], [timezone], [day], [hour], [minute], [interval_min]) VALUES ('ssp-eu-ntx.ikea.com', 'Europe/Paris', 'tue,wed', 21, 0, 2);
INSERT INTO [dbo].[dh_retail_nutanix_auto_maintenance_schedule]([pc], [timezone], [day], [hour], [minute], [interval_min]) VALUES ('ssp-na-ntx.ikea.com', 'America/Los_Angeles', 'wed,thu', 23, 0, 5);
INSERT INTO [dbo].[dh_retail_nutanix_auto_maintenance_schedule]([pc], [timezone], [day], [hour], [minute], [interval_min]) VALUES ('ssp-ppe-ntx.ikea.com', 'Europe/Paris', 'mon,tue', 23, 0, 10);

from business.generic.base_up_exception import BaseUpException


class ClusterException(BaseUpException):
    def __init__(self, msg):
        super().__init__(msg)


class FCClusterIsExist(ClusterException):
    def __init__(self, msg=None):
        if not msg:
            msg = "Cluster already exists!"
        super().__init__(msg)


class FCDeployClusterFailed(ClusterException):
    def __init__(self, msg=None):
        if not msg:
            msg = "Fatal error detected in FC deployment."
        super().__init__(msg)


class FCNodesNotReady(ClusterException):
    def __init__(self, msg=None):
        if not msg:
            msg = "Can't find enough nodes to deploy cluster on FC!"
        super().__init__(msg)


class ClusterCreationTimeout(ClusterException):
    def __init__(self, msg=None):
        if not msg:
            msg = "Cluster creation did not complete within the expected time 2 hours."
        super().__init__(msg)


class FailedToGetCurrentUIConfiguration(ClusterException):
    def __init__(self, msg=None):
        if not msg:
            msg = "Failed to pull current UI customization."
        super().__init__(msg)


class FailedToUpdateUIConfiguration(ClusterException):
    def __init__(self, msg=None):
        if not msg:
            msg = "Failed to update UI customization."
        super().__init__(msg)


class IpamAhvCvmGatewayUnreachable(ClusterException):
    def __init__(self, ahv_cvm_gw):
        super().__init__(f"Gateway {ahv_cvm_gw} is not reachable! Please check!")


class IpamLackingAhvCvmFreeIps(ClusterException):
    def __init__(self, subnet_id, expected, actual):
        super().__init__(
            f"Expected free IPs under AHV/CVM network (subnet_id: {subnet_id}): >= {expected}, "
            f"Found free IPs: {actual}")


class IpamLackingDefaultAhvCvmDhcpServers(ClusterException):
    def __init__(self, subnet_id, expected, actual):
        super().__init__(
            f"Expected default AHV/CVM DHCP servers under subnet (subnet_id: {subnet_id}): >= {expected}, "
            f"Actual servers: {actual}")


class IpamParentSubnetIsZero(ClusterException):
    def __init__(self, subnet_id):
        super().__init__(f"Detected parent subnet of subnet {subnet_id} is 0, please provide a valid subnet.")


class IpOrNameAlreadyInUse(ClusterException):
    def __init__(self, in_use_list):
        super().__init__(f"Following IP or DNS name already in use: {in_use_list}")


class NodeInterfaceLcapSettingWrong(ClusterException):
    def __init__(self):
        super().__init__("LACP settings are wrong.")


class NodeInterfaceWrong(ClusterException):
    def __init__(self):
        super().__init__("Node interface configured wrong.")


class ScanOobFailed(ClusterException):
    def __init__(self):
        super().__init__("Can not find any available OOB IP, please check with your administrator")


class ErgontaskCheckFailed(ClusterException):
    def __init__(self, time):
        super().__init__(f"This logline was written {time} minutes ago")


class BandwidthCheckFailed(ClusterException):
    def __init__(self):
        super().__init__("Site bandwidth is not meeting requirements after multiple attempts")


class GetFCAPIKeyFailed(ClusterException):
    def __init__(self):
        super().__init__("Could not find API Keys in output")


class DnsNtpUnreachable(ClusterException):
    def __init__(self, dns):
        super().__init__(f"{dns} is unreachable")


class CreateVaultSecretFailed(ClusterException):
    def __init__(self, item):
        super().__init__(f"Failed to add {item}.")


class JoinClusterToPCFailed(ClusterException):
    def __init__(self):
        super().__init__("Cluster join to PC failed")


class ConnectToPEFailed(ClusterException):
    def __init__(self, pe_name):
        super().__init__(f"Failed to connect to {pe_name}!")


class CreateSvcUserFailed(ClusterException):
    def __init__(self):
        super().__init__("Failed to create svc user")


class GetUserListFromCVMFailed(ClusterException):
    def __init__(self):
        super().__init__("Failed to get user list from CVM")


class GrantClusterAdminRoleFailed(ClusterException):
    def __init__(self):
        super().__init__("Failed to grant cluster admin role")


class GrantUserAdminRoleFailed(ClusterException):
    def __init__(self):
        super().__init__("Failed to grant user admin role")


class ConnectToSourceFailed(ClusterException):
    def __init__(self, source):
        super().__init__(f"Failed to connect to {source}.")


class NodeNotMatchCvm(ClusterException):
    def __init__(self, cvm_dhcps_count, scan_oob_data_count):
        super().__init__(f"Found {cvm_dhcps_count} CVMs, but {scan_oob_data_count} OOBs! Sometimes you will find the CVMs are not matched with the OOBs, please try start stage1 again!")


class NodeInterfaceLinkConnectedToSameNetworkAdapter(ClusterException):
    def __init__(self, links):
        super().__init__(f"Both link {links} connected to same network adapter, please check!")


class TestFirewallFailed(ClusterException):
    def __init__(self, services):
        super().__init__(f"Can't connect to mandatory services {services}!")


function Invoke-Vault-API(){
    param(
        [string]                                     $Endpoint,
        [string]                                     $RequestURI,
        [object]                                     $Headers,
        [object]                                     $Body,
        [string] [ValidateSet("GET", "POST", "PUT")] $Method,
        [int]                                        $MaxTry = 3,
        [int]                                        $TimeoutSec = 30
    )
    if ($MaxTry) {
        $Payload = @{
            'Uri'        = "https://$($Endpoint)/$($RequestURI)"
            'Method'     = $Method
            'Headers'    = $Headers
            'TimeoutSec' = $TimeoutSec
        }
        if($Body){
            $Payload['Body'] = $Body
        }
        try {
            return Invoke-RestMethod @Payload
        }
        catch {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling $Endpoint for $RequestURI. Cause: $_ Retry in 5 seconds"
            Start-Sleep 5
            return Invoke-Vault-API -Endpoint $Endpoint `
                                    -RequestURI $RequestURI `
                                    -Headers $Headers `
                                    -Body $Body `
                                    -Method $Method `
                                    -MaxTry $($MaxTry - 1) `
                                    -TimeoutSec $($TimeoutSec + 5)
        }
    }else {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling $Endpoint for $RequestURI."
        return $null
    }
}
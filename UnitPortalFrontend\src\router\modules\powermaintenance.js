/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const pmRouter = {
  path: '/pm',
  component: Layout,
  redirect: '/pm/nutanix',
  alwaysShow: true, // will always show the root menu
  // name: 'Table',
  meta: {
    title: 'Power Maintenance',
    icon: 'el-icon-switch-button',
    roles: ['admin','pmuser','superadmin'],
    privilege:"role_pm"
  },
  children: [
    {
      path: 'nutanix',
      component: () => import('@/views/pm/nutanix'),
      name: 'NutanixPM',
      meta: { title: 'Nutanix PM' , roles: ['admin','pmuser','superadmin'],privilege:"view_ntx_pm"}
    },
    // {
    //   path: 'sli',
    //   component: () => import('@/views/pm/sli'),
    //   name: 'SimpliVityPM',
    //   meta: { title: 'SimpliVity PM' , roles: ['admin','pmuser','superadmin'],privilege:"view_sli_pm"}
    // }
  ]
}
export default pmRouter


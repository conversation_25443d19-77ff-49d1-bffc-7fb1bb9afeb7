from abc import ABC, abstractmethod
import logging
import threading
from business.authentication.authentication import ServiceAccount
from business.distributedhosting.nutanix.nutanix import PrismCentral
from business.authentication.authentication import Vault
from collector.collectors.modules.api_calls import MetaCache
from collector.collectors.modules.multi_job import AsynchronousProcessing
from business.generic.commonfunc import setup_common_logger, create_file
import datetime
import static.SETTINGS as SETTING


class BaseCollector(ABC):
    def __init__(self, sa=None, facility_type = "retail") -> None:
        #service account
        if sa:
            #default is none, if caller specifically input the sa, then use it, if not, then we use our service account
            self.sa = sa
        else:
            self.sa =  ServiceAccount(ServiceAccount.NUTANIX_PM).get_service_account()
        self.pc_tier_relation = PrismCentral.get_pc_tier_relationship()
        self.cache = MetaCache()
        self.lock = threading.Lock()
        self.facility_type = facility_type
        self.setup_logging()
        # Use self.logger from setup_logging in children class
        self.processes = AsynchronousProcessing(thread=True)
        #{"ssp-eu-ntx.ikea.com":"PRODUCTION", "ssp-ppe-ntx.ikea.com":"PREPRODUCTION"...}    
        
    
    def setup_logging(self):
        # Common logging setup (can be overridden in subclasses)
        collector_log_path = create_file(filepath=SETTING.DATA_FETCH_LOG_PATH,
                                          filename=f'{self.__class__.__name__}_{self.facility_type}_{datetime.datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}.log')  # noqa
                                          
        if collector_log_path:    
            logging.info(f"Local log file created for collector, path: {collector_log_path}.")
            self.logger = setup_common_logger(__name__, log_file = collector_log_path)
            # Configure logging for request library
            #self._request_config() TODO
        else:
            logging.error(f"Failed to create local log file for collector, path: {collector_log_path}.")
            self.logger = logging.getLogger(__name__)
        
        
    def restore_logging(self):
        # Restore the original logging configuration TODO
        self.logger = logging.getLogger(__name__)
        print(f"Restored logger: {self.logger}")

    def _request_config(self): # Not used atm TODO
        # Enable logging for `urllib3` (used by `requests`)
        urllib3_logger = logging.getLogger('urllib3')
        urllib3_logger.handlers = self.logger.handlers
        urllib3_logger.propagate = False
        # Enable logging for `requests`
        requests_logger = logging.getLogger('requests.packages.urllib3')
        requests_logger.handlers = self.logger.handlers
        requests_logger.propagate = False

    @abstractmethod
    def collect(self):
        pass

        
    def get_vault_instance(self, pe):
        # pe is what we get from PE table
        # {'id': 456, 'name': 'RETJP697-NXC000', 'fqdn': 'retjp697-nxc000.ikea.com', 'prism':'ssp-apac-ntx.ikea.com', 'tier':"PRODUCTION"...}
        prism = pe.get('prism', None)
        if not prism:
            raise Exception(f"This pe <{pe['name']}> entry has no 'prism', please check the db data.")
        
        tier = self.pc_tier_relation[prism.upper()]
        if not tier:
            raise Exception(f"This pe <{pe['name']}> entry has no 'tier', please check the db data.")
        
        if tier.upper() in self.vault_instance.keys():
            return self.vault_instance[tier.upper()]

        vault = Vault(tier=self.pc_tier_relation[prism.upper()])
        self.vault_instance[tier.upper()] = vault
        return vault
    
    def get_pe_svc_account(self, pe, vault):
        # vault has to be an instance of Class Vault
        res, account = vault.get_secret(f"{pe['name'].upper()}/Site_Pe_Svc")
        # 1-click-nutanix ,not  nutanix, admin
        if not (res and account):
            raise Exception("Failed to get 1-click-nutanix account for this PE from vault.")
        return account
# pylint: disable=invalid-name
import os
import socket
from base_path import application_path
LDAP_DOMAIN_CONTROLLER = "ldaps://**********:636" if socket.gethostname() == 'NTXSEELM-NT3000' else "ldaps://************:636"

STATUS = {
    "INPROGRESS": "in progress",
    "DONE": "done",
    "ERROR": "error",
    "NOTSTARTED": "not started",
}

API_URL = {
    "lcm_entity": "/api/lcm/v4.0.a1/resources/entities",
    "lcm_config": "/api/lcm/v4.0.a1/resources/config"
}

LCM = {
    "epsilon": "Epsilon",
    "calm": "Calm",
    "cmu": "Cluster Maintenance Utilities",
    "flowsecurity": "Flow Security",
    "licensing": "Licensing",
    "ncc": "NCC",
    "pc": "PC",
    "aos_upgrade_timeout_hours": 24,
    "aos_upgrade_tasks_limit": 50
}
LCM_VERSION = {
    "lcm": "2.7.1",
    "foundation": "5.4.2",
    "base_spp": "2023.***********",
    "latest_spp": "2023.***********",
    "transitional_spp": "2023.***********",
    "bearable_spp": "2023.***********",
    "aos": "6.10",
    "aos_binary": "nutanix_installer_package-release-fraser-6.10-stable-45706f65299ffde905da43b6e7d98478d071b7b9-x86_64.tar.gz",
    "aos_metadata": "generated-nutanix_installer_package-release-fraser-6.10-stable-45706f65299ffde905da43b6e7d98478d071b7b9-x86_64-metadata.json",
    "ahv": "20230302.102001",
    "PcPayloadVersion": "pc.2022.6.0.5",
    "PcMeta": "generated-pc.2022.6.0.5-279ffd67a3d647834a40ed90af23a3161553b04a-metadata.json",
    "PcPayload": "pc.2022.6.0.5-279ffd67a3d647834a40ed90af23a3161553b04a-x86_64.tar.gz"
}

LOG_PATH = os.path.join(application_path, "Log")
LOG_ARCHIVE_FOLDER_PATH = os.path.join(application_path, "Log", "flask_main_archive")
MAIN_LOG_FILE_PATH = os.path.join(application_path, "Log", "flask_main")
SLI_LOG_PATH = os.path.join(application_path, "Log", "sli")
NTX_LOG_PATH = os.path.join(application_path, "Log", "ntx")
WORKLOAD_LOG_PATH = os.path.join(application_path, "Log", "workload")
AUTOMATION_PATH = os.path.join(application_path, "Log", "automation")
MOVE_LOG_PATH = os.path.join(application_path, "Log", "move")
SPP_LCM_LOG_PATH = os.path.join(application_path, "Log", "automation", "SPP")
AOS_LCM_LOG_PATH = os.path.join(application_path, "Log", "automation", "AOS")
ROTATE_PASSWD_LOG_PATH = os.path.join(application_path, "Log", "automation", "RP")
MAINTENANCE_LOG_PATH = os.path.join(application_path, "Log", "automation", "MAINTENANCE")
AUTOMATION_LOG_PATH = os.path.join(application_path, "Log", "automation")
DSC_LOG_PATH = os.path.join(application_path, "Log", "automation", "DSC")
CLUSTER_LOG_PATH = os.path.join(application_path, "Log", "cluster")
METRO_VG_LOG_PATH = os.path.join(application_path, "Log", "automation", "METRO_VG")
CSV_TMP_PATH = os.path.join(application_path, "tmp")
ROLE_FOR_PM = ['admin', 'pmuser', 'superadmin']
ROLE_FOR_ADMIN = ['admin', 'superadmin']
RENEW_CERT_LOG_PATH = os.path.join(application_path, "Log", "automation", "RENEWCERT")
CERT_TMP_PATH = os.path.join(application_path, "tmp", "cert")
SSH_KEY_PATH = os.path.join(application_path, "tmp", "sshkey")
VAULT_PASSWD_PATH = os.path.join(application_path, "ikea", "dpc-vault")
VAULT_PASSWD_HOME_PATH = os.path.join(application_path, "ikea")
VM_ACTION_LOG_PATH = os.path.join(application_path, "Log", "workload", "vm_action")
DATA_FETCH_LOG_PATH = os.path.join(application_path, "Log", "data_fetch")
INCIDENT_LOG_PATH = os.path.join(application_path, "Log", "incidentHandler")
UNLOCK_ACCOUNT_LOG_PATH = os.path.join(application_path, "Log", "UnlockAccount")

ALLOWED_DOWNLOAD_PATH_LIST = [
    os.path.normcase(SLI_LOG_PATH), 
    os.path.normcase(NTX_LOG_PATH), 
    os.path.normcase(MOVE_LOG_PATH), 
    os.path.normcase(WORKLOAD_LOG_PATH), 
    os.path.normcase(SPP_LCM_LOG_PATH), 
    os.path.normcase(RENEW_CERT_LOG_PATH),
    os.path.normcase(AOS_LCM_LOG_PATH), 
    os.path.normcase(ROTATE_PASSWD_LOG_PATH), 
    os.path.normcase(DSC_LOG_PATH),
    os.path.normcase(UNLOCK_ACCOUNT_LOG_PATH)
]

PM = {
    "GRACEFUL_SHUTDOWN_SLEEP_TIME": 180,
    "HARD_SHUTDOWN_SLEEP_TIME": 120,
    "CLUSTER_SHUTDOWN_SLEEP_TIME": 180,
    "CLUSTER_START_UP_SLEEP_TIME": 120,
    "AHV_SHUTDOWN_SLEEP_TIME": 120,
    "AHV_POWEREDON_SLEEP_TIME": 360,
    "HOST_STATE_LOOP_SLEEP_TIME": 120,
    "QUERY_OOB_INTERVAL_TIME": 180,
    "QUERY_CVM_INTERVAL_TIME": 10,
    "CVM_POWEREDON_SLEEP_TIME": 300,
    "FIREWALL_POWEREDON_SLEEP_TIME": 300,
    "HOST_STATE_LOOP_TIMES": 8,
    "START_CLUSTER_LOOP_TIMES": 5,
    "ACTION": {
        "on": "poweron",
        "off": "poweroff"
    },
    "FIRST_START_SERVER_REGEX": ".*-PL000(1|2).*"
}

NEW_PWD_LENGTH = 16

PE_VAULT_LABEL = {
    "cvm_nutanix": "Site_Pe_Nutanix",
    "cvm_admin": "Site_Pe_Admin",
    "oob_admin": "Site_Oob",
    "ahv_nutanix": "Site_Ahv_Nutanix",
    "ahv_root": "Site_Ahv_Root",
    "Site_Gw_Priv_Key": "Site_Gw_Priv_Key",
    "Site_Gw_Pub_Key": "Site_Gw_Pub_Key",
    "Site_Pe_Svc": "Site_Pe_Svc"
}

PC_VAULT_LABEL = {
    "Site_Pc_Nutanix": "Site_Pc_Nutanix",
    "Site_Pc_Admin": "Site_Pc_Admin"
}

PC_VAULT_LOCAL_LABEL = {
    "Site_Pc_Svc": "Site_Pc_Svc"
}

CENTRAL_PE_VAULT_LABEL = dict(PE_VAULT_LABEL, pc_nutanix="Site_Pc_Nutanix")

VAULT = {
    "URL": "vault-prod.build.ingka.ikea.com",
    "PRODUCTION": {
        "ENGINE": "NutanixClusters",
        "NAMESPACE": "dist-host-retail/dhprod"
    },
    "PREPRODUCTION": {
        "ENGINE": "NutanixClusters",
        "NAMESPACE": "dist-host-retail/dhppe"
    },
    "IKEADT": {
        "ENGINE": "NutanixClusters",
        "NAMESPACE": "dist-host-retail/dhdt"
    },
    "IKEAD2": {
        "ENGINE": "NutanixClusters",
        "NAMESPACE": "dist-host-retail/ikead2"
    },
    "WAREHOUSE": {
        "ENGINE": "NutanixClusters",
        "NAMESPACE": "digital-warehouse/dhprod"
    },
    "WAREHOUSEDT": {
        "ENGINE": "NutanixClusters",
        "NAMESPACE": "digital-warehouse/dhdt"
    }
}

IPAM = {
    "ENDPOINT": "IPAM.IKEA.COM"
}

THORS_HAMMER = {
    "ENDPOINT": "https://thorshammereu.ikea.com/api/runmethod"
}

DARK_SITE = {
    "ENDPOINT": "http://darksite/1CN/",
    "URL": "http://darksite/release"
}

SITE_SSL = {
    "FQDN": "ikea.keyfactorpki.com",
    "PRODUCTION": {
        "ORGANIZATION_NAME": "IKEA",
        "TEMPLATE": "IKEAWebServer",
        "OU_Name": "Infrastructure Engineering",
        "CHAIN": "-----BEGIN CERTIFICATE-----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\nQA7CiHx7eY/qoqZ+gjL8/hqwuX+2+8mSODK2dlzYHHNSIH+MAx64lIQrsg==\n-----END CERTIFICATE-----",
        "CERT_AUTHORITY": "IKEA Issuing CA V4",
        "CERT_OWNER_GROUP": "DPC_Siab",
        "EMAIL_CONTACT": "<EMAIL>",
        "MAX_RENEWAL_DAYS_BEFORE_EXPIRY": 60
    },
    "PREPRODUCTION": {
        "ORGANIZATION_NAME": "IKEA",
        "TEMPLATE": "IKEAWebServer",
        "OU_Name": "Infrastructure Engineering",
        "CHAIN": "-----BEGIN CERTIFICATE-----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\nQA7CiHx7eY/qoqZ+gjL8/hqwuX+2+8mSODK2dlzYHHNSIH+MAx64lIQrsg==\n-----END CERTIFICATE-----",
        "CERT_AUTHORITY": "IKEA Issuing CA V4",
        "CERT_OWNER_GROUP": "DPC_Siab",
        "EMAIL_CONTACT": "<EMAIL>",
        "MAX_RENEWAL_DAYS_BEFORE_EXPIRY": 60
    },
    "IKEADT": {
        "ORGANIZATION_NAME": "IKEA",
        "TEMPLATE": "IKEADTWebServer",
        "OU_Name": "Infrastructure Engineering",
        "CHAIN": "-----BEGIN CERTIFICATE-----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\n-----END CERTIFICATE-----",
        "CERT_AUTHORITY": "IKEADT Issuing CA V4",
        "CERT_OWNER_GROUP": "DPC_Siab",
        "EMAIL_CONTACT": "<EMAIL>",
        "MAX_RENEWAL_DAYS_BEFORE_EXPIRY": 60
    },
    "IKEAD2": {
        "ORGANIZATION_NAME": "IKEA",
        "TEMPLATE": "IKEAD2WebServer",
        "OU_Name": "Infrastructure Engineering",
        "CHAIN": "-----BEGIN CERTIFICATE-----\nMIIGWzCCBEOgAwIBAgIURcBuAJUrxZx7jP+Vn6NMDgrgWxYwDQYJKoZIhvcNAQEL\nBQAwSDELMAkGA1UEBhMCTkwxHjAcBgNVBAoMFUludGVyIElLRUEgU3lzdGVtcyBC\nVjEZMBcGA1UEAwwQSUlTQlYgUm9vdCBDQSB2NDAeFw0yMTAxMTQxNDIyMTRaFw0z\nMTAxMTIxNDIyMTNaMEwxEzARBgoJkiaJk/IsZAEZFgNjb20xFjAUBgoJkiaJk/Is\nZAEZFgZpa2VhZDIxHTAbBgNVBAMMFElLRUFEMiBJc3N1aW5nIENBIHY0MIICIjAN\nBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAvstfMe8j24sB6uhHE23dzGNDJr/o\nQqW34pgtUJV/vSS5OYfKic9mc/Yug1HtcSbEoiON1oLk5UME1ut0tkwqrq+Iuqku\nTMH5DHHI/d8okiWpsR00PRgkDNDFGTL1jcmpog7s+q2yqNm6u6CwabCmDhA2Sbmi\nR1h/9YO/GK7suhoo/f95o+ara3d+0Uv7EeH9+UGTPZUDBzxcE/KVQZkQ82odFPL2\nIyEvtvoKdBDjY1mP0+JYFI4uWh9/CA1xRAt+CPpXhA6aL2jQuH1zI+P+U7ZLLxjd\nXvK9IxRppwgbsWn5WeneCOVotvSqF/sSKjwHA5g0f/lBQv+lPraSIj0R7tgLnXu8\nwA2PkrGi/TT1CvEXqgzyhIJfKz0KiE2GIMFunG348mWpdDWqlcczrbhkIQIPn1lH\niO9XaAkvui3Nx9wlB9Pl2xDYNi8MV/rluWlPJBoM4GD9SzY4w/SKY1n++O0ghjdf\nQyTShJgkvpkdgOs40MgiHGnibcsKi1GQhSDklIVEV06+1pqTVeDQCcgVl3FsEWG/\nql4viFNcandwrfQJSJg4WDtMI3qDW2xbFouAyXXIRBk/da9HN86HZAJt8s2wwCSZ\nPJbq01lYYF5cG6GgAC0lyIemb6BC1ikk5OKAj0DTYpWbCQkjwsW4sK8xH7kr22bq\nYOgbNhL7LVnBkmECAwEAAaOCATcwggEzMBIGA1UdEwEB/wQIMAYBAf8CAQAwHwYD\nVR0jBBgwFoAUtJb3jE5QxJs+G2s3JqUNNJ8A1xwwgYgGCCsGAQUFBwEBBHwwejBF\nBggrBgEFBQcwAoY5aHR0cDovL2NhY2VydC1jZXJ0cGtpLnh3cC5pa2VhLmNvbS9y\nb290L0lJU0JWUm9vdENBdjQuY3J0MDEGCCsGAQUFBzABhiVodHRwOi8vb2NzcC1j\nZXJ0cGtpLnh3cC5pa2VhLmNvbS9vY3NwMEIGA1UdHwQ7MDkwN6A1oDOGMWh0dHA6\nLy9jcmwtY2VydHBraS54d3AuaWtlYS5jb20vSUlTQlZSb290Q0F2NC5jcmwwHQYD\nVR0OBBYEFN3VL+O9KAusC/PR3TV9NZ6QfQDJMA4GA1UdDwEB/wQEAwIBhjANBgkq\nhkiG9w0BAQsFAAOCAgEAB/gh1jC7Bu17SIxhA4CJWhHKFsyOPDWO57UiVtVcNQPr\nCM4xWQs1ARJ1hBBy0qUJM3GiPqUu0UJ2cSU365TrefOGfe4KRjHf2FdBuUnvWzPQ\nElSM324NPUZAF+jqcDsLS6C9EJfHjZC+LbjgPGWhK42GwGSwnMp/gE1EeG0+iq39\nC+LLzeLC7sw13CzW63xDgu/AlooM15x2y+mDm7y6cdAqD5qKRwn7/jF1YFklpBiA\n+L73yrzBtdWnZ6OKaPlyVAlRdiGwp8VJ/lnq3LbdZZzgnZnCWQ8Zmo0aCUUgTaVj\n/xBC/QGLAGorvcr5A6SFp8dhR0pvgB1cr9prHuP8z3dqDYDJOv2oqDDInZwEkq5w\nVPrPQdn2EScaV3zNOteqoVlUwEogv9EFfDD5YwrxwssbTl5mWcLvKVt2LESVz1ai\n6ecD5T1T8YRM0W1Z3upLrHxOq8a/5jL0O5dtApvwDRI8QJFBCQMKkqJnyOJvfnL+\nU9HJ+MtS7yttk/dtgIzya2cuoyk8zPuRn9VAHwl1eNyAaqKgIiLVy/1jckZssF1i\ng7JTy5AUJeBF8fbchwz/Ucmfg1yHe+kGYeoZYGgWtKF49Ai389w1ImUoJu4MVlH6\n7EOhGMMvh0dojmY4TB7CdwBQeBK0pwW7lXEV1M1hOlk5xotibxDgtPSd+hL5lnE=\n-----END CERTIFICATE-----\n",
        "CERT_AUTHORITY": "IKEAD2 Issuing CA V4",
        "CERT_OWNER_GROUP": "DPC_Siab",
        "EMAIL_CONTACT": "<EMAIL>",
        "MAX_RENEWAL_DAYS_BEFORE_EXPIRY": 60
    },
    "WAREHOUSE": {
        "ORGANIZATION_NAME": "IKEA",
        "TEMPLATE": "IKEAWebServer",
        "OU_Name": "Infrastructure Engineering",
        "CHAIN": "-----BEGIN CERTIFICATE-----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\nQA7CiHx7eY/qoqZ+gjL8/hqwuX+2+8mSODK2dlzYHHNSIH+MAx64lIQrsg==\n-----END CERTIFICATE-----",
        "CERT_AUTHORITY": "IKEA Issuing CA V4",
        "CERT_OWNER_GROUP": "DPC_Siab",
        "EMAIL_CONTACT": "<EMAIL>",
        "MAX_RENEWAL_DAYS_BEFORE_EXPIRY": 60
    },
    "WAREHOUSEDT": {
        "ORGANIZATION_NAME": "IKEA",
        "TEMPLATE": "IKEADTWebServer",
        "OU_Name": "Infrastructure Engineering",
        "CHAIN": "-----BEGIN CERTIFICATE-----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\n-----END CERTIFICATE-----",
        "CERT_AUTHORITY": "IKEADT Issuing CA V4",
        "CERT_OWNER_GROUP": "DPC_Siab",
        "EMAIL_CONTACT": "<EMAIL>",
        "MAX_RENEWAL_DAYS_BEFORE_EXPIRY": 60
    }
}

SIZING = {
    "CPU_RATIO": 4,
    "STORAGE_RESILIENCE": 0.95,
    "RESILIENCE_MAX": 0.9,
    "EXCEPTION_LIST": [
        'retcn888-nxc000.ikea.com',
        'retse999-nxc000.ikea.com',
        'retse995-nxc000.ikea.com'
    ]
}



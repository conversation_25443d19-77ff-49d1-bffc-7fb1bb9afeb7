/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const mktRouter = {
  path: '/marketplace',
  component: Layout,
  redirect: '/marketplace/templates',
  // name: 'Table',
  meta: {
    title: 'Marketplace',
    icon: 'market',
    roles: ['superadmin','pmuser'],
    privilege:"role_mkt"
  },
  children: [
    {
      path: 'templates',
      component: () => import('@/views/marketplace/templates'),
      name: 'Template',
      meta: { title: 'Template' , roles: ['admin','pmuser','superadmin'],privilege:"view_template"}
    },
    {
      path: 'workloads',
      component: () => import('@/views/marketplace/workloads'),
      name: 'Workload',
      meta: { title: 'Workload' , roles: ['admin','pmuser','superadmin'],privilege:"view_wl"}
    }
  ]
}
export default mktRouter

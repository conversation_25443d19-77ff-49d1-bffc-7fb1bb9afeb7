#
# Module manifest for module 'GDH-ASSIST'
#
# Generated by: IKEA\RAYGU1
# Cocontributors = @(IKEA\JAWAN36)
#
# Generated on: 2024-06-13
#

@{

# Script module or binary module file associated with this manifest.
ModuleToProcess = 'GDH-ASSIST.psm1'

# Version number of this module.
ModuleVersion = '1.2.4'

# Supported PSEditions
PowerShellVersion = '7.0'

# CompatiblePSEditions = @()

# ID used to uniquely identify this module
GUID = 'a04db6e9-071c-4ee5-93b4-d882a86d418c'

# Author of this module
Author = 'IKEA\RAYGU1, IKEA\JAWAN36'

# Company or vendor of this module
CompanyName = 'IKEA'

# Copyright statement for this module
Copyright = '(c) IKEA\RAYGU1. All rights reserved.'

# Description of the functionality provided by this module
# Description = ''

# Minimum version of the PowerShell engine required by this module
# PowerShellVersion = ''

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# DotNetFrameworkVersion = ''

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
# RequiredModules = @()

# Assemblies that must be loaded prior to importing this module
# RequiredAssemblies = @()

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
# ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
# TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
# FormatsToProcess = @()

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = @(
    'Add-NtnxRemoteSite',
    'Add-NtnxVmToProtectionDomain',
    'Create-NtnxNetwork',
    'Confirm-NtnxReplicationChain',
    'Confirm-RetailNtnxBios',
    'Confirm-WarehouseVcHostLogin',
    'Confirm-WarehouseVcIloLogin',
    'Confirm-WarehouseVcIloName',
    'Connect-NtnxSsh',
    'Convert-BytesTo',
    'ConvertTo-StandardPrism',
    'Delete-NtnxImage',
    'Delete-NtnxRemoteSite',
    'Enable-WarehouseVcIloRemoteSyslog',
    'Find-RetailNtxPc',
    'Find-RetailVc',
    'Get-Base64Auth',
    'Get-FunctionName',
    'Get-GstAccountFromPam',
    'Get-NetworkRegionalSettings',
    'Get-NtnxCluster',
    'Get-NtnxClusterAverageMetric',
    'Get-NtnxClusterList',
    'Get-NtnxClusterStorageMetric',
    'Get-NtnxControlPanel',
    'Get-NtnxImage',
    'Get-NtnxLicense',
    'Get-NtnxNetwork',
    'Get-NtnxNic',
    'Get-NtnxNode',
    'Get-NtnxNodeBios',
    'Get-NtnxNodeFirmware',
    'Get-NtnxNodePowerState',
    'Get-NtnxProtectionDomain',
    'Get-NtnxRemoteSite',
    'Get-NtnxReplicationChain',
    'Get-NtnxSppInfo',
    'Get-NtnxTask',
    'Get-NtnxVg',
    'Get-NtnxVm',
    'Get-NtnxVmDisk',
    'Get-NtTimeZone',
    'Get-SliVM',
    'Get-SecretForSiaB',
    'Get-SecretForWtp',
    'Get-SecretForWiaB',
    'Get-UpToken',
    'Get-VaultSecret',
    'Get-VaultSecViaUp'
    'Get-WarehouseVcHost',
    'Get-WarehouseVcHostBmc',
    'Get-WarehouseVcIloRemoteSyslog',
    'Install-AndImportModule',
    'Measure-NtnxCapacity',
    'Measure-NtnxCpuUsageRatio',
    'Read-GstAccount',
    'Read-Var',
    'Read-VaultToken',
    'Register-CertByCsr',
    'Register-CertInPKi3',
    'Remove-NtnxVmDisk',
    'Remove-NtnxVmFromProtectionDomain',
    'Reset-NtnxPcTrust',
    'Reset-NtnxProtectionDomain',
    'Reset-WarehouseVcHostBmc',
    'Resolve-CountryCode',
    'Resolve-VmCategory',
    'Restart-NtnxNode',
    'Restart-EachNtnxNode',
    'Restart-EachNtnxNodeBmc',
    'Set-NtnxVmPowerState',
    'Set-WarehouseVcIloDnsServer',
    'Set-WarehouseVcIloFqdn',
    'Ssh-Prism-ResetPcTrust',
    'Ssh-Prism-StartVm',
    'Ssh-Prism-UpdateVmMemory',
    'Start-NtnxNodeViaIlo',
    'Start-NtnxVM',
    'Stop-NtnxNodeViaIlo',
    'Test-AdCredential',
    'Test-VaultToken',
    'Update-GstAccount',
    'Update-IloNtpServer',
    'Update-NsbNameInNtnx',
    'Update-NtnxRemoteSite',
    'Update-PaloVmMemory',
    'Update-ReplicationBandwidth',
    'Update-VaultToken',
    'Write-ConsoleLog'
)

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
VariablesToExport = '*'

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = @()

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        # Tags = @()

        # A URL to the license for this module.
        # LicenseUri = ''

        # A URL to the main website for this project.
        # ProjectUri = ''

        # A URL to an icon representing this module.
        # IconUri = ''

        # ReleaseNotes of this module
        # ReleaseNotes = ''

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable

} # End of PrivateData hashtable

# HelpInfo URI of this module
# HelpInfoURI = ''

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}


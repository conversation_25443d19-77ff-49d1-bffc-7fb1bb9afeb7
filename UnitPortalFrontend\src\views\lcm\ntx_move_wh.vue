<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="5" >
        <el-col :span="3" >
        <el-dropdown split-button type="primary" @click="handle_move_development">
          Actions
          <el-dropdown-menu slot="dropdown" placement="right">
            <el-dropdown-item @click.native="handle_move_development()">Move Dep</el-dropdown-item>
            <el-dropdown-item @click.native="handle_move_stage1()">Preparation </el-dropdown-item>
            <el-dropdown-item @click.native="cutover()">Cutover</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        </el-col>
        <el-col :span="4"  :offset="11" >
          <el-select    size="large"
            v-model="filter.selected_vcluster" multiple collapse-tags placeholder="Filter the VCluster" style="width:100%;" >

            <el-option v-for="item in filter.vc_list" :key="item" :label="item" :value="item" style="font-size: large;"/>

          </el-select>
        </el-col>
        <el-col :span="4" >
          <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large"  @keyup.enter.native="filter_cluster_list"/>
        </el-col>
        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large"  @click="filter_cluster_list">
            Search
          </el-button>
        </el-col>
    </el-row>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="current_list" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange"  @row-click="handle_row_click">
      <el-table-column label="ID" prop="id" sortable="custom" align="center" min-width="2%" >
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="VCluter" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="cluter">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.cluster.toUpperCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column label="PE_A" min-width="8%" align="center" sortable="custom" prop="pe_a">
        <template slot-scope="{row}">
          <span class="bigger_font clickable" @click="show_brief_log(row,ispea=true)">{{ row.pe_a|pea_filter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="PE_B" min-width="8%" align="center" sortable="custom" prop="pe_b">
        <template slot-scope="{row}">
          <span class="bigger_font clickable" @click="show_brief_log(row ,ispea=false)">{{ row.pe_b|peb_filter }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Move IP A" min-width="6%" align="center" sortable="custom" prop="moveip">
        <template slot-scope="{row}">
          <span class="bigger_font"  v-if="row.moveipa!=null" ><a :href="'https://' + row.moveipa" target="_blank" >{{ row.moveipa|moveip_filter }}</a></span><span class="bigger_font" v-else>{{ row.moveipa|moveip_filter}}</span>
        </template>
      </el-table-column>
      <el-table-column label="Move IP B" min-width="6%" align="center" sortable="custom" prop="moveip">
        <template slot-scope="{row}">
          <span class="bigger_font"  v-if="row.moveipb!=null" ><a :href="'https://' + row.moveipb" target="_blank" >{{ row.moveipb|moveip_filter }}</a></span><span class="bigger_font" v-else>{{ row.moveipb|moveip_filter}}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="Latest log" min-width="13%" align="center" sortable="custom" prop="logs">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.logs|log_filter }}</span><span class="link-type" v-if="row.logs!=null"  @click="show_brief_log(row)">  More</span>
        </template>
      </el-table-column> -->

      <el-table-column label="MoveStatus" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="movedevop" >
        <template slot-scope="{row}">
          <el-tag :type="row.movedevop | status_filter">
            {{ row.movedevop }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="Stage1_Pre" min-width="4%" align="center" sortable="custom" prop="stagepreparation" show-overflow-tooltip>
        <template slot-scope="{row}">
          <!-- <el-tag :type="row.stagepreparation | status_filter" v-popover:popover4>
            {{ row.stagepreparation }}
          </el-tag> -->
          <el-popover
            ref="popover4"
            placement="left"
            width="600"
            content="Click for detail"
            title="Click for detail"
            trigger="click">
            <el-table :data="filter.datasyncstatus">
              <el-table-column type="expand" v-if="filter.planstatus" >
                <template slot-scope="{row}"  >
                  <!-- <p>{{ row.vm }}</p> -->
                  <!-- <p v-for="item in props.row['vm']" :key="item.index">{{ item.vmname}}:{{ item.percentage }}</p> -->
                  <el-table :data="row.vm" style="width: 400" class="two-list" :show-header=false @row-click="handle_row_click">
                    <el-table-column prop="vmname" label="VM_Name"></el-table-column>
                    <el-table-column prop="percentage" label="Percentage"></el-table-column>
                    <el-table-column prop="status" label="VMStatus">
                      <template slot-scope="{row}">
                        <!-- SourcePreparation/DataSeeding -->
                        <el-tag :type="row.status==='ReadyToCutover' ? 'success' :row.status==='Completed'? 'success' :(row.status==='Failed'? 'danger':'warning') " size="medium">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column  label="Plan" prop="name"></el-table-column>
              <el-table-column  label="Status" prop="status" ></el-table-column>
              <!-- <el-table-column width="300" property="id" label="address"></el-table-column> -->
            </el-table>
            <div slot="reference" class="name-wrapper">
              <el-tooltip content="Click for detail" placement="top">
                <el-tag :type="row.stagepreparation | status_filter" size="medium">{{ row.stagepreparation }}</el-tag>
              </el-tooltip>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <!-- <el-table-column label="Stage2" min-width="5%" align="center" sortable="custom" prop="stagecutover">
        <template slot-scope="{row}">
          <el-tag :type="row.stagecutover | status_filter">
            {{ row.stagecutover }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="Operations"  min-width="5%" align="center" >
        <template slot-scope="{row}">
          <el-dropdown split-button type="primary" >
            Actions
            <el-dropdown-menu slot="dropdown" placement="right" >
              <el-dropdown-item :disabled= row.pe_a|ifaction_filter @click.native="handle_move_development(row)">Move Dep</el-dropdown-item>
              <el-dropdown-item :disabled= row.pe_a|ifaction_filter @click.native="handle_move_stage1(row)">Preparation </el-dropdown-item>
              <!-- <el-dropdown-item :disabled= row.pe_a|ifaction_filter @click.native="cutover(row)">Cutover</el-dropdown-item> -->
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" /> 
      <el-dialog :visible.sync="dialogPvVisible" :title="'MOVE Log(brief)'"  :close-on-click-modal="false">

        <el-table :data="logdata" border fit highlight-current-row style="width: 100%" height="500" >
          <el-table-column prop="key" label="log date"  min-width="25%" >
            <template slot-scope="{row}">
              <span>{{ row.logdate }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="pv" label="log info" min-width="55%"  >
            <template slot-scope="{row}">
              <span>{{ row.loginfo }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="pv" label="log severity"  min-width="10%"  >
            <template slot-scope="{row}">
              <span>{{ row.severity }}</span>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button type="warning" @click="download_log_file()">Download Detail Log</el-button>
          <el-button type="primary" @click="dialogPvVisible = false">OK</el-button>
          
        </span>
      </el-dialog>
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="mdev_dialogFormVisible" width="30%" @close="handledialogClose" :close-on-click-modal="false">
      <el-form ref="dataForm"  :model="temp" label-position="left" label-width="auto" style="width: 600px; margin-left:50px;padding-top: 2%;display: inline;">
        <el-form-item label="vCluster" prop="cluster" >
          <el-input v-model="selectedrow.cluster" readonly style="width:35%;margin: auto;"/>
        </el-form-item>
        <!-- <el-form-item label="NTX Cluster" prop="ntx_cluster" >
          <el-input v-model="selectedrow.pe_a" readonly style="width:auto;margin: auto"/>
        </el-form-item> -->
        <el-form-item label="NTX Cluster" prop="ntx_cluster" >
              <!-- <el-input v-model="selectedrow.pe" readonly style="width:auto;margin: auto"/> -->
              <el-select v-model="filter.selected_cluster" placeholder="Select the plan type" clearable style="width:55%;margin:auto" >
                <el-option v-for="item in [selectedrow.pe_a,selectedrow.pe_b]" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
      </el-form>
          <span>Notes: <p style="font-size: 12px">MOVE VM builing; PowerOn; Change the PW.</p></span>
      <div slot="footer" class="dialog-footer">
        <el-button @click="mdev_dialogFormVisible = false">
          Cancel
        </el-button>
        <el-button type="primary" @click="new_movevm()">
          Confirm
        </el-button>
      </div>
    </el-dialog>
    <el-dialog :title="textMap[dialogStatus]" v-if="stage1_dialogFormVisible" :visible.sync="stage1_dialogFormVisible" @close="handledialogClose" :close-on-click-modal="false" width="30%" style="display:flexbox">
      <el-form ref="plandataForm" :model="temp" label-position="left" label-width="auto" style="width: 600px; margin-left:20%;padding-top: 2%;display: inline;">
        <el-form-item label="Site Name" prop="cluster" >
          <el-input v-model="selectedrow.cluster" readonly style="width:35%;margin: auto;"/>
        </el-form-item>
        <el-form-item label="NTX Cluster" prop="ntx_cluster" >
          <!-- <el-input v-model="selectedrow.pe" readonly style="width:auto;margin: auto"/> -->
          <el-select v-model="filter.selected_cluster" placeholder="Select the plan type" clearable style="width:55%;margin:auto" >
            <el-option v-for="item in [selectedrow.pe_a,selectedrow.pe_b]" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <!-- <el-row :gutter="2">
          <el-col :span="12">
            <el-form-item label="Username" prop="gst_username" >
              <el-input v-model="this.$store.getters.name" placeholder="Please input move username" style="width:auto;margin: auto"/>
            </el-form-item>
          </el-col>
          <el-col :span="12" >
            <el-form-item label="Password" prop="gst_password">
              <el-input v-model="temp.gst_password" type="password"  placeholder="Please input move password" style="width:auto;margin: auto"/>
            </el-form-item>
          </el-col>
        </el-row> -->
        <!-- <el-row :gutter="2">
          <el-col :span="12" >
            <el-form-item label="NTX Cluster" prop="ntx_cluster" >
              <el-select v-model="filter.selected_cluster" placeholder="Select the plan type" clearable style="width:auto;margin:auto" >
                <el-option v-for="item in [selectedrow.pe_a,selectedrow.pe_b]" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>    -->
          <!-- <el-col :span="12">
            <el-form-item label="Plan Type" prop="plantype" >
              <el-select v-model="filter.selected_plan" placeholder="Select the plan type" clearable style="width:auto;margin:auto" >
                <el-option v-for="item in migration_plans" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>   -->
        <!-- </el-row>
        <el-row :gutter="2">
          <el-col :span="12">
            <el-form-item label="Site Name" prop="cluster" >
              <el-input v-model="selectedrow.cluster" readonly style="width:auto;margin: auto;"/>
            </el-form-item>
          </el-col>
          
        </el-row> -->
        <!-- <el-form-item label="VM List :"  style="width:50%" > 
        </el-form-item>
          <el-transfer
            ref="plantransfer"
            filterable
            :right-default-checked="orgtransferdata"
            :titles="['Unselected', 'Selected']"
            filter-placeholder="Fuzzy search"
            v-model="lockrole"
            @change="handleChange"
            :data="transferdata">
          </el-transfer> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="stage1_dialogFormVisible = false">
          Cancel
        </el-button>
        <el-button type="primary" @click="create_migration_plan()">
          Confirm
        </el-button>
      </div>
    </el-dialog>
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="stage2_dialogFormVisible" width="30%" @close="handledialogClose" :close-on-click-modal="false">
      <el-form ref="dataForm"  :model="temp" label-position="left" label-width="auto" style="width: 600px; margin-left:50px;padding-top: 2%;display: inline;">
        <el-form-item label="vCluster" prop="cluster" >
          <el-input v-model="selectedrow.name" readonly style="width:auto;margin: auto;"/>
        </el-form-item>
        <el-form-item label="NTX Cluster" prop="ntx_cluster" >
          <el-input v-model="selectedrow.pe" readonly style="width:auto;margin: auto"/>
        </el-form-item>
      </el-form>
          <span>Notes: <p style="font-size: 12px"> Migrate VMs from SLI to NTX; Power off and Rename the old VM; Disconnect the NIC; Add VM to PD.</p></span>
      <div slot="footer" class="dialog-footer">
        <el-button @click="stage2_dialogFormVisible = false">
          Cancel
        </el-button>
        <el-button type="primary" @click="start_cutover()">
          Confirm
        </el-button>
      </div>
    </el-dialog>
    <!-- <el-popover
      ref="popover4"
      placement="right"
      width="400"
      trigger="click"
      :visible="popover4show">
      <el-table :data="gridData">
        <el-table-column width="150" property="date" label="date"></el-table-column>
        <el-table-column width="100" property="name" label="name"></el-table-column>
        <el-table-column width="300" property="address" label="address"></el-table-column>
      </el-table>
    </el-popover> -->

  </div>
</template>

<script>
import { DownloadMoveDetailLog, Get_WHClusterlist_Move, Get_WH_VMs, New_WHMove_VM, Start_Cutover ,Create_Migration_Plan_WH, GetWHMoveBriefLog} from '@/api/automation'
import { GetVCClusterCorrespondence} from '@/api/simplivity'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import { string } from 'clipboard'
import { parseGeoJson } from 'echarts/lib/export'

export default {
  name: 'MOVETable',
  components: { Pagination },
  directives: { waves },
  filters: {
    move_status_filter(movedev){
      if(movedev=='Error'){
        return 'danger'
      }
      if(movedev=='Done'){
        return 'success'
      }
      if(movedev=='In Progress'){
        return 'warning'
      }
    },
    log_filter(log){
      if(log==null){
        return "No log yet"
      }
      else{
        return log[0]['loginfo']
      }
    },
    moveip_filter(moveip){
      if(moveip==null){
        return "No IP yet"
      }
      else {
        return moveip
      }
    },
    pea_filter(pe_a){
      if (pe_a == null) {
        return "Not ready yet"
      }
      else {
        return pe_a
      }
    },
    peb_filter(pe_b){
      if (pe_b == null) {
        return "Not ready yet"
      }
      else {
        return pe_b
      }
    },
    ifaction_filter(pe) {
      if (pe == null) {
        return true
      }
      else {
        return false
      }
    },
    status_filter(status) {
      if(status==null){
        return 'primary'
      }else{
        if(status=='Done'){
          return 'success'
        }
        else if(status=='Error'){
          return 'danger'
        }
        else if (status=='In Progress'){
          return 'warning'
        }
        else {
          return status
        }
      }
    }
  },
  data() {
    const validateTime =(rule, value, callback)=>{
      if(this.temp.datatimepickerdisabled){
        callback()
      }
      let currentdate = new Date()
      let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
      if (value < utctime){
        callback(new Error('Schedule date must be later then now.'))
      }else{
        let currnettime = utctime.getTime()
        let scheduletime = value.getTime()
        let timediff = scheduletime-currnettime
        if(timediff/1000/60 < 5){
          callback(new Error('Schedule date is too close from now.'))
        }else{
          callback()
        }
      }
      callback()
    }
    return {
      tableKey: 0,
      all_sliclu_list: null,
      filtered_list: null,
      current_list: null,
      page_list: null,
      whvms_list: null,
      status_list: [],
      textMap: {
        plan: 'Stage2 - New Migration Plan ',
        move: 'Stage1 - NEW MOVE',
        cutover:'Stage2 - Cut Over'
      },
      filter:{
        vc_list:[],
        selected_vcluster:[],
        fuzzy_string: "",
        selected_plan: [],
        selected_cluster: '',
        datasyncstatus: [],
        planstatus:false,
      },
      tempstatus: [ 
        { 'name': 'NA', 'status': 'NA', 'vm': 'NA' }
      ],
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: '',
        status: '',
        sort: '+id'
      },
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      // statusOptions: ['published', 'draft', 'deleted'],
      ShowCreationDate: false,
      temp: {
        id: '',
        timestamp: new Date(),
        cluster:'',
        status: '',
        startnow: 1 ,
        datatimepickerdisabled:false,
        description: '',
        gst_username: this.$store.getters.name,
        gst_password: '',
        ntx_clusterlist: []
      },
      selectedrow:'',
      dialogFormVisible: false,
      mdev_dialogFormVisible: false,
      stage1_dialogFormVisible: false,
      stage2_dialogFormVisible: false,
      dialogStatus: '',
      dialogPvVisible: false,
      popover4show: false,
      logdata: [],
      // rules: {
      //   cluster: [{ required: true, message: 'cluster is required', trigger: 'change' }],
      //   ntx_cluster: [{ required: true, message: 'ntx cluster is required', trigger: 'change' }],
      //   gst_password: [{ required: true, message: 'move_pw is required', trigger: 'change' }],
      //   gst_username: [{ required: true, message: 'move_user is required', trigger: 'change' }],
      //   timestamp: [{ type: 'date', required: true , trigger: 'change' , validator:validateTime}]
      // },
      transferdata: null,
      orgtransferdata: null,
      lockrole: [],
      vmdialog_right :[],
      vmdialog_left :[],
      selectedvm_list: [],
      intervaljob: '',
      migration_plans:['RoomA_Standalone','RoomB_Standalone','RoomA_LX_node','RoomB_LX_node','RoomA_Win_node', 'RoomB_Win_node']
    }
  },
  computed: {
    total() {
      if(this.filtered_list){
        return this.filtered_list.length
      }
      else{
          return 0
      }
    }
  },
  created() {
    // this.get_filter_data()
    this.get_clusters_list()
    this.intervaljob = setInterval(()=>{
      this.get_clusters_list()
    },80000)
  },
  methods: {
    show_brief_log(row,ispea){
      let pe = ispea ? row.pe_a : row.pe_b
      let payload = {
        token: this.$store.getters.token,
        data:{
          pe: pe
        }
      }
      GetWHMoveBriefLog(payload).then(
        response=>{
          this.logdata  = response.data
          console.log(this.logdata)
          this.dialogPvVisible = true
        }
      ).catch(
          (error)=>{
            this.$notify({
            title   : 'Error',
            message : "Failed to get the brief log, error: " + (error.response?.request?.response || error.response?.data?.message || "Unknown error"),
            type    : 'error',
            duration: 5000
          })
        }
      )
    },
    get_filter_data(){
      GetVCClusterCorrespondence(this.$store.getters.token).then(
        response =>{
          let vcoptions = response['data']
          this.VCTypeKeyValue = vcoptions.reduce((acc, cur) => {
          acc[cur.vc] = cur.cluster.sort()
          return acc
        }, {})
        this.filter.vc_list = Object.keys(this.VCTypeKeyValue)
        }
      )
    },
    get_clusters_list() {
      this.listLoading = true
      Get_WHClusterlist_Move(this.$store.getters.token).then(response => {
        this.all_sliclu_list = response.data
        this.filtered_list = this.all_sliclu_list
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start, end)
        this.listLoading = false
        let all_vc_list = this.all_sliclu_list.map((obj, index) => { return obj['prism'] })
        this.filter.vc_list = (this.remove_duplicate(all_vc_list)).sort()
        this.filter_cluster_list()
      })
      
    },
    handle_move_development() {
      this.dialogStatus = 'move'
      this.mdev_dialogFormVisible = true
    },
    handle_move_stage1(row) {
      this.transferdata = []
      this.vmdialog_right = []
      this.lockrole = []
      this.dialogStatus = 'plan'
      this.stage1_dialogFormVisible = true
      this.Get_WH_VMs(row)
      this.$nextTick(() => {
            this.$refs['plandataForm'].clearValidate()
        })
    },
    handle_move_stage2() {
      this.dialogStatus = 'cutover'
      this.stage2_dialogFormVisible = true
    },
    cutover() {
      this.$alert('Be patient. The cutover part is in progress... ', 'Title', {
        confirmButtonText: 'OK',
      });
    },
    handleChange(value, direction, movedKeys) {
      // let vm_list = []
      let id
      if (direction === "right"){
        for (let indid in movedKeys) {
          this.vmdialog_left.splice(this.vmdialog_left.indexOf(String(movedKeys[indid]-1)), 1)
          this.vmdialog_right.push(this.whvms_list[movedKeys[indid] - 1])
        }
      };
      if (direction === "left"){
        for(let indid in movedKeys){
          this.vmdialog_right.splice(this.vmdialog_right.indexOf(movedKeys[indid]),1)
          this.vmdialog_left.push(this.whvms_list[movedKeys[indid]-1])
        }
      };
    },
    handledialogClose() {
      this.vmdialog_right=null,
      this.temp.gst_password = '',
      this.transferdata = [],
      this.filter.selected_cluster=this.selectedrow.pe_a
    },
    handledialogbeforeClose(done) {
      this.$confirm('Are you sure to close ?')
        .then(_ => {
          done();
          // this.get_clusters_list()
        })
        .catch(_ => {});
    },
    handle_row_click(row,column,event){
      this.selectedrow = row
      this.filter.selected_cluster=this.selectedrow.pe_a
      // this.get_rowstatus(row.id)
      this.filter.planstatus= this.selectedrow.datasyncstatus? true:false
      this.filter.datasyncstatus = this.selectedrow.datasyncstatus? eval('(' + this.selectedrow.datasyncstatus + ')'): this.tempstatus
      
    },
    remove_duplicate(arr) {
      const newArr = []
      arr.forEach(item => {
        if (!newArr.includes(item)) {
          newArr.push(item)
        }
      })
      return newArr
    },
    set_page(){
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start, end)
    },
    filter_cluster_list(){
      //screen the table as per filters
      this.listQuery.page = 1
      let temp_list
      if (this.filter.selected_vcluster.length){
        //No filter, so select all
        temp_list = this.all_sliclu_list.filter((item)=>{
          return this.filter.selected_vcluster.includes(item['prism'])
        })
        this.filtered_list = temp_list
      }
      else{
        this.filtered_list = this.all_sliclu_list
      }
      if(this.filter.fuzzy_string.trim().length){
        let temp_list = this.filtered_list
        let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
        //remove space, and split into array by space 
        for(let fuzzy of fuzzy_list){
              fuzzy = fuzzy.toString().toLowerCase()
              temp_list = temp_list.filter((k)=>{
              if( k.cluster.toString().toLowerCase().search(fuzzy)!= -1
              ){
                return true
              }
            })
            }
        this.filtered_list = temp_list
      }
      this.set_page()
    },
    sortChange(data) {
      const { prop, order } = data
      if(order==null){
        this.sortChange({prop:'id',order:'ascending'})
        return 
      }
      let flag_num = order=="ascending" ? 1 : -1
      this.filtered_list.sort((item1,item2)=>{
        let prop1 = item1[prop]?item1[prop]:''
        let prop2 = item2[prop]?item2[prop]:''        
        return (prop1 > prop2) ? flag_num*1 : ((prop1 < prop2) ? flag_num*-1 : 0)
    })
      this.set_page()
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    get_rowstatus(rowid) {
    this.status_list=[]
    this.status_list.push(this.current_list[rowid-1])
    this.popover4show=true
    },
    Get_WH_VMs(row){
        //get the role list
      this.listLoading = true,
      this.temp.gst_password = ''
      let payload = {
        data:{  
          "cluster": row.cluster
        },
        token: this.$store.getters.token
      }
          Get_WH_VMs(payload).then(response => {
            this.whvms_list = response.data
            const data = []
            let i=1
            for (let id in this.whvms_list) {
              data.push({
                key: i++,
                label: this.whvms_list[id]
              });
              this.vmdialog_left.push(id);
            };
            this.transferdata = data
            // this.lockrole = []
            this.orgtransferdata = data
            this.dialogStatus = 'plan'
            this.stage1_dialogFormVisible = true
            this.listLoading = false
        //     this.$nextTick(() => {
        //     this.$refs['userdataForm'].clearValidate()
        // })
            // return data;
          })
    },
    new_movevm() {
      //get the role list
      this.mdev_dialogFormVisible = false
      // this.listLoading = true
      // let vm_list = []
      // for (vmid in this.vmdialog_right) {
      //   vm_list.push(this.vmdialog_right[vmid])
      // }
      let payload = {
        data: {
          "ntx_cluster": this.filter.selected_cluster,
          "site": this.selectedrow.cluster,
          "tasktype": "new_move"
        },
        token: this.$store.getters.token
      }
      New_WHMove_VM(payload).then(response => {
        if (response.data.result) {
          this.$notify({
            title: 'Success',
            message: `Successed to create New Move VM task.`,
            type: 'success',
            duration: 2000
          })
        }
        else{
          this.$notify({
              title: 'OOOOOps....',
              message: response.data.message,
              type: 'error',
              duration: 10000
          })
        }
          this.mdev_dialogFormVisible = false
        })
        .catch((error) => {
          this.mdev_dialogFormVisible = false
          this.$notify({
            title: 'Error',
            message: error.response.data.message,
            type: 'error',
            duration: 2000
          })
        })
      },
    create_migration_plan() {
      //get the role list
      this.stage1_dialogFormVisible = false
      let payload = {
        data: {
          "ntx_cluster": this.filter.selected_cluster,
          "site": this.selectedrow.cluster,
          // "gst_username": this.temp.gst_username,
          // "gst_password": this.temp.gst_password,
          // "vm_list": this.vmdialog_right,
          "tasktype": "preparation",
          // "plantype": this.filter.selected_plan
        },
        token: this.$store.getters.token
      }
      // if (this.vmdialog_right == null || this.vmdialog_right.length == 0) {
      //   this.$alert("Please Select the VMs that need to move ! ", "Alert", {
      //     confirmButtonText: "Confirm",
      //   });
      // } else if (this.temp.gst_password == null || this.temp.gst_password.length == 0
      //             || this.filter.selected_plan == null || this.filter.selected_plan.length == 0) {
      //     this.$alert(" Fields missing, please check the GST PW/Plan Type ! ", "Alert", {
      //     confirmButtonText: "Confirm",
      //   });
      // }
      // else {
        Create_Migration_Plan_WH(payload)
          .then(() => {
            this.$notify({
              title: 'Success',
              message: `Successed to create the migration plan.`,
              type: 'success',
              duration: 2000
            })
            this.stage1_dialogFormVisible = false
          })
          .catch((error) => {
            this.stage1_dialogFormVisible = false
            this.$notify({
              title: 'Error',
              message: error.response.data.message,
              type: 'error',
              duration: 2000
            })
          })
      // }
    },
    start_cutover() {
      //get the role list
      this.stage1_dialogFormVisible = false
      let payload = {
        data: {
          "ntx_cluster": this.selectedrow.pe,
          "cluster": this.selectedrow.name,
          "vc": this.selectedrow.cluster,
          "tasktype": "cutover"
        },
        token: this.$store.getters.token
      }
      // if (this.vmdialog_right == null || this.vmdialog_right.length == 0) {
      //   this.$alert("Please Select the VMs that need to move ! ", "Alert", {
      //     confirmButtonText: "Confirm",
      //   });
      // } else if (this.temp.gst_password == null || this.temp.gst_password.length == 0) {
      //   this.$alert(" GST Account missing, Please don't get distracted ! ", "Alert", {
      //     confirmButtonText: "Confirm",
      //   });
      // }
      // else {
      Start_Cutover(payload)
        .then(() => {
          this.$notify({
            title: 'Success',
            message: `Successed to start cutover task.`,
            type: 'success',
            duration: 2000
          })
          this.stage2_dialogFormVisible = false
        })
        .catch((error) => {
          this.stage2_dialogFormVisible = false
          this.$notify({
            title: 'Error',
            message: error.response.data.message,
            type: 'error',
            duration: 2000
          })
        })
      // }
    },
    download_log_file(){
      let payload = {
        data:{  id:this.selectedrow.latest_taskid,
                filepath:this.selectedrow.detail_log_path},
        token: this.$store.getters.token
      }
      DownloadMoveDetailLog(payload)
      .then((response)=>{
        const href = URL.createObjectURL(response.data);
        // create "a" HTML element with href to file & click
        const link = document.createElement('a');
        link.href = href;
        link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
        document.body.appendChild(link);
        link.click();
        // clean up "a" element & remove ObjectURL
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      })
    }
  },
  beforeDestroy(){
    clearInterval( this.intervaljob )
  }
}
</script>
<style lang="scss" scoped>
    .bigger_font {
      font-size: 16px;
    }
    .el-transfer ::v-deep.el-transfer-panel {
    width: 240px;
    margin-left: 35px;
  }
  .el-transfer ::v-deep  .el-transfer__buttons{
    width: 25px;
    margin-left: -20px;
  }
  .el-transfer ::v-deep  .el-button+.el-button{
    margin-left: 0px;
  }
  .clickable:hover {
    cursor: pointer;
    text-decoration: underline;
  }
</style>  
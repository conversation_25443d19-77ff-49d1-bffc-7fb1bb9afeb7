from marshmallow import Schema, fields, validates_schema, ValidationError, EXCLUDE

from models.auth_models import <PERSON><PERSON><PERSON>, ModelUserGroupRoleMapping


class RoleDashBoardSchema(Schema):
    view = fields.Str()


class RolePmSchema(Schema):
    view_ntx_pm = fields.Str()
    create_ntx_pm = fields.Str()
    abort_ntx_pm = fields.Str()
    delete_ntx_pm = fields.Str()
    view_sli_pm = fields.Str()
    create_sli_pm = fields.Str()
    abort_sli_pm = fields.Str()
    delete_sli_pm = fields.Str()

    class Meta:
        unknown = EXCLUDE

class RoleNtxSchema(Schema):
    view_pc = fields.Str()
    view_pe = fields.Str()
    view_ahv = fields.Str()
    view_vm = fields.Str()
    add_pc = fields.Str()
    remove_pc = fields.Str()
    class Meta:
        unknown = EXCLUDE

class RoleSliSchema(Schema):
    view_vc = fields.Str()
    view_cluster = fields.Str()
    view_host = fields.Str()
    view_vm = fields.Str()
    class Meta:
        unknown = EXCLUDE


class RoleMktSchema(Schema):
    view_wl = fields.Str()
    create_wl = fields.Str()
    abort_wl = fields.Str()
    remove_wl = fields.Str()
    view_template = fields.Str()
    create_template = fields.Str()
    edit_template = fields.Str()
    remove_template = fields.Str()
    class Meta:
        unknown = EXCLUDE

class RoleAdministrationSchema(Schema):
    view_user = fields.Str()
    view_role = fields.Str()
    class Meta:
        unknown = EXCLUDE
class RoleLCMSchema(Schema):
    view_spp = fields.Str()
    view_aos = fields.Str()
    view_move = fields.Str()
    class Meta:
        unknown = EXCLUDE    
class PrivilegeSchema(Schema):
    role_dashboard = fields.Nested(RoleDashBoardSchema)
    role_pm = fields.Nested(RolePmSchema)
    role_ntx = fields.Nested(RoleNtxSchema)
    role_sli = fields.Nested(RoleSliSchema)
    role_mkt = fields.Nested(RoleMktSchema)
    role_administration = fields.Nested(RoleAdministrationSchema)
    role_lcm = fields.Nested(RoleLCMSchema)


class CreateRbacRoleRequestSchema(Schema):
    name = fields.Str(required=True)
    description = fields.Str(allow_none=True)
    privilege = fields.Nested(PrivilegeSchema, required=True)

    class Meta:
        unknown = EXCLUDE

    @validates_schema
    def validate(self, data, **kwargs):
        name = data.get("name")
        role = ModelRole.query.filter_by(name=name).all()
        if role:
            raise ValidationError(f"Role name '{name}' already existed.")


class UpdateRbacRoleRequestSchema(Schema):
    role_id = fields.Int(required=True)
    description = fields.Str(allow_none=True)
    privilege = fields.Nested(PrivilegeSchema)

    class Meta:
        unknown = EXCLUDE

    @validates_schema
    def validate(self, data, **kwargs):
        role_id = data.get("role_id")
        role = ModelRole.query.filter_by(id=role_id).scalar()
        if not role:
            raise ValidationError(f"Role with id {role_id} doesn't exist!")


class DeleteRbacRoleRequestSchema(Schema):
    role_id = fields.Int(required=True)

    def validate_role_existence(self, role_id):
        role = ModelRole.query.filter_by(id=role_id).scalar()
        if not role:
            raise ValidationError(f"Role with id {role_id} doesn't exist!")

    def validate_role_in_use(self, role_id):
        in_use_roles = ModelUserGroupRoleMapping().query.filter_by(role_id=role_id).all()
        if in_use_roles:
            raise ValidationError("This role is currently assigned to some users, please remove first.")

    @validates_schema
    def validate(self, data, **kwargs):
        role_id = data.get("role_id")
        self.validate_role_existence(role_id)
        self.validate_role_in_use(role_id)
<template>
  <div>
    <div class="toolbox_panel">
      <el-card class="box-card title-background" style="height:14vh;box-shadow: 5px 0px 10px 5px rgba(0, 0, 0, .1);">
        <div style="width:90%;height:9vh;margin-top: 2vh;">
          <div style="margin-left:25%">
            <h1 style="font-family:Times New Roman, Times, serif;font-size: 5vh;width:54%;margin-bottom: 0px;margin-top:0px;float:left;color: white;">GDH Incident Handler</h1>
            <div style="float:left; color: white; margin-top:1%">
              <div> Status: <span style="color: greenyellow; font-weight:bold">Running</span></div>
              <div style="margin-top:4%">Updated on: <span style="color:greenyellow; font-weight:bold">{{ current_time }}</span><span style="color:red; font-size:12px">(CET)</span></div>
            </div>
            <div style="clear:both"></div>
          </div>
          <div style="font-size: 14px;color: white; width:44%;margin-left:25%;margin-top:0px">A crazy incident terminator who hunts unassigned incidents in GDH queue every 30 minutes.</div>
        </div>
      </el-card>

      <el-card class="box-card unassigned-background" style="height:8vh;box-shadow: 5px 0px 10px 5px rgba(0, 0, 0, .1);margin-top:0.5vh;width:22.3%;float:left;margin-right:5px;position:relative">
        <div style="font-size: x-large;position:absolute;top:0;bottom: 0;margin:auto;color: white; height:3vh">
          Unassigned incidents
        </div>
        <div style="font-size: x-large;width:20%;float:right;position:absolute;top:0;bottom: 0;right:0;color: yellow; margin:auto;height:3vh">
          {{ get_gdh_unassigned_incidents }}
        </div>
      </el-card>

      <el-card class="box-card active-background" style="height:8vh;box-shadow: 5px 0px 10px 5px rgba(0, 0, 0, .1);margin-top:0.5vh;width:22.3%;float:left;margin-left:0px;margin-right:5px;position:relative">
        <div style="font-size: x-large;position:absolute;top:0;bottom: 0;margin:auto;color: white; height:3vh">
          Active Incidents
        </div>
        <div style="font-size: x-large;width:20%;float:right;position:absolute;top:0;bottom: 0;right:0;color: yellow; margin:auto;height:3vh">
          {{ get_gdh_active_incidents }}
        </div>
      </el-card>

      <el-card class="box-card today-background" style="height:8vh;box-shadow: 5px 0px 10px 5px rgba(0, 0, 0, .1);margin-top:0.5vh;width:22.3%;float:left;margin-left:0px;margin-right:5px;position:relative">
        <div style="font-size: x-large;position:absolute;top:0;bottom: 0;margin:auto;color: white; height:3vh">
          Incidents solved today
        </div>
        <div style="font-size: x-large;width:20%;float:right;position:absolute;top:0;bottom: 0;right:0;color: greenyellow; margin:auto;height:3vh">
          {{ get_today_resolved_incidents }}
        </div>
      </el-card>

      <el-card class="box-card total-background" style="height:8vh;box-shadow: 5px 0px 10px 5px rgba(0, 0, 0, .1);margin-top:0.5vh;width:22.3%;float:left;margin-left:0px;margin-right:5px;position:relative">
        <div style="font-size: x-large;position:absolute;top:0;bottom: 0;margin:auto;color: white; height:3vh">
          Incidents solved in total
        </div>
        <div style="font-size: x-large;width:20%;float:right;position:absolute;top:0;bottom: 0;right:0;color: brown; margin:auto;height:3vh">
          {{ get_total_resolved_incidents }}
        </div>
      </el-card>

      <div style="clear:both"></div>

      <el-card class="box-card-left-side  chart-background" style="height:50vh;box-shadow: 5px 0px 10px 5px rgba(0, 0, 0, .1);margin-top: 0.5vh;width:90%">
        <div style="width:45%;float:left">
          <div class="auto_analysis">Auto-Analysis</div> <!-- 修改此处 -->
          <bar-chart 
            :bar_data_dict = "bar_data_dict"
            :date_payload = "date_payload"
            :top_five_incident_list="top_five_incident_list"
            @change_table_data="change_table_data"
            />
        </div>
        <div style="width:55%;float:right;height:39vh;">
          <div class="line_button_group">
            <el-button type="primary" size="large" style="margin-right: 100px;float:right" @click="downloadanalysisdata">Export</el-button>

            <div class="demo-date-picker">
              <div class="block">
                <span class="demonstration">Date Range (CET)</span>
                <el-date-picker
                  ref="dp"
                  v-model="date_selection"
                  type="daterange"
                  range-separator="to"
                  start-placeholder="Start data(CET)"
                  end-placeholder="End date(CET)"
                  :picker-options="picker_options"
                  @change="fetch_chart_data_from_change"
                  :disabled="fetching_data"
                />

              </div>
          </div>

          </div> <!-- 修改此处 -->
          <line-chart 
            :line_data="line_data"
            />
        </div>
      </el-card>

      <el-card class="box-card-left-side line-background" style="margin-bottom: 100px ;overflow-y:scroll;height:36vh;box-shadow: 5px 0px 10px 5px rgba(0, 0, 0, .1);margin-top: 0.5vh;width: 90%">
        <div style="margin-bottom: 10px;">
          <el-input
            v-model="search_query"
            placeholder="Search..."
            clearable
            style="width: 300px;"
          />
        </div>
        <el-table 
          :data="filtered_incidents" 
          border 
          fit 
          highlight-current-row 
          style="width: 100%;" 
          @sort-change="sortChange">


          <el-table-column label="Assignee" min-width="10%" align="center" sortable="custom" prop="assignee">
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.assignee }}</span>
            </template>
          </el-table-column>

          <el-table-column label="creation time" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="creation_time">
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.creation_time }}</span>
            </template>
          </el-table-column>

          <el-table-column label="duration_time" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="duration_time">
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.duration_time }}</span>
            </template>
          </el-table-column>

          <el-table-column label="inc_num" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="inc_num">
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.inc_num }}</span>
            </template>
          </el-table-column>

          <el-table-column label="priority" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="priority">
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.priority }}</span>
            </template>
          </el-table-column>

          <el-table-column label="resolved_time" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="resolved_time">
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.resolved_time }}</span>
            </template>
          </el-table-column>

          <el-table-column label="site" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="site">
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.site }}</span>
            </template>
          </el-table-column>

          <el-table-column label="short_description" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="short_description">
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.short_description }}</span>
            </template>
          </el-table-column>


        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
import { 
  GetActiveIncidents,
  GetUnassignedIncidents,
  GetTotalResolvedIncidents,
  GetTodayResolvedIncidents,
  GetIncidentSummary,
  DownloadAnalysisData
 } from '@/api/nutanix';
import {
  GetIncidentChartData,
  GetIncidentList,
  GetIncidentTopFiveModel
} from '@/api/incident'
import waves from '@/directive/waves'; // waves directive
import LineChart from './incident-charts/LineChart';
import BarChart from './incident-charts/BarChart';
import { start } from 'nprogress';
Date.prototype.addDays   = function(d){return new Date(this.valueOf()+864E5*d);};
Date.prototype.addMinutes   = function(minutes){return new Date(this.valueOf()+6E4*minutes);};
Date.prototype.addMonths = function(m){
  let d = new Date(this.valueOf())
  d.setMonth(d.getMonth()+ m )
  return d
};
console.log(new Date().getTimezoneOffset())
export default {
  name: 'Toolbox',
  components: {  LineChart, BarChart },
  directives: { waves },
  filters: {
    statusFilter(status) {
      let st = status.toLowerCase();
      const statusMap = {
        'not started': 'info',
        'done': 'success',
        'error': 'danger',
        'in progress': 'primary',
        'aborted': 'warning'
      };
      return statusMap[st];
    }
  },
  data() {


    return {
      top_five_incident_list:[],
      get_gdh_unassigned_incidents: '',
      get_gdh_active_incidents: '',
      get_total_resolved_incidents:'',
      get_today_resolved_incidents:'',
      current_time: '',
      fetching_data:false,
      bar_type:[],
      bar_data:[],
      bar_data_dict:{},
      line_label:[],
      line_siab_data:[],
      line_wiab_data:[],
      line_wtp_data:[],
      line_total_data:[],
      line_data:{},
      date_selection: [ new Date().addMonths(-1),new Date()],
      date_payload:{
        start_time: new Date().addMonths(-1),
        end_time: new Date()
      },
      // default_time: [new Date()],
      picker_options: {
            shortcuts:[
            {
              text: 'last week',
              onClick:(picker)=> {
                let actual_start = new Date(); // actual is the actual time we are going to send
                let showing_start = new Date();// the time that shows in the GUI, we convert it to CET
                let current_offset = showing_start.getTimezoneOffset() //the number of offset minutes, for CN it's -480, for CET it's -60
                //we need to set the time to CET though,
                showing_start = showing_start.addMinutes(current_offset + 60)

                // picker.$emit('pick', [start.addDays(-7), start]);
                this.date_selection = [showing_start.addDays(-7), showing_start]
                this.data_payload.start_time = actual_start.addDays(-7)
                this.data_payload.end_time = actual_start
                this.fetch_chart_data()
              }
            },
            {
              text: 'last 2 weeks',
              onClick:(picker)=>  {
                let actual_start = new Date(); // actual is the actual time we are going to send
                let showing_start = new Date();
                let current_offset = showing_start.getTimezoneOffset()
                showing_start = showing_start.addMinutes(current_offset + 60)

                this.date_selection = [showing_start.addDays(-14), showing_start]
                this.data_payload.start_time = actual_start.addDays(-14)
                this.data_payload.end_time = actual_start
                this.fetch_chart_data()
              }
            },
            {
              text: 'last month',
              onClick:(picker)=> {
                let actual_start = new Date(); // actual is the actual time we are going to send
                let showing_start = new Date();
                let current_offset = showing_start.getTimezoneOffset()
                showing_start = showing_start.addMinutes(current_offset + 60)
                
                this.date_selection = [showing_start.addMonths(-1), showing_start]
                this.data_payload.start_time = actual_start.addMonths(-1)
                this.data_payload.end_time = actual_start
                this.fetch_chart_data()
              }
            },
          ]
      },
      search_query:""
    };
  },
  created() {
    // this.get_gdh_today_resolved_incidents();
    // this.get_gdh_total_resolved_incidents();
    // this.get_active_incidents();
    // this.get_unassigned_incidents();
    this.get_gdh_incidents_summary();
    this.updateCurrentTime();
    this.fetch_chart_data(new Date().addMonths(-1),new Date());


    // setInterval(this.updateCurrentTime, 1000 * 60);
  },
  mounted() {
  },
  computed: {
    filtered_incidents() {
    if (!this.search_query) {
      return this.top_five_incident_list; // 如果没有搜索关键字，返回完整数据
    }

    let temp_list = this.top_five_incident_list
    let fuzzy_list = this.search_query.trim().split(/\s+/)
    //去除空格 并以空格分割成数组
    //remove space, and split into array by space 
    for(let fuzzy of fuzzy_list){
      fuzzy = fuzzy.toString().toLowerCase()
      temp_list = temp_list.filter((k)=>{
        let combined_string = (k.assignee?k.assignee.toString().toLowerCase():'') + 
                              (k.creation_time?k.creation_time.toString().toLowerCase():'')+
                              (k.duration_time?k.duration_time.toString().toLowerCase():'')+
                              (k.inc_num?k.inc_num.toString().toLowerCase():'')+
                              (k.priority?k.priority.toString().toLowerCase():'')+
                              (k.resolved_time?k.resolved_time.toString().toLowerCase():'')+
                              (k.site?k.site.toString().toLowerCase():'')+
                              (k.short_description?k.short_description.toString().toLowerCase():'')
        if( combined_string.search(fuzzy)!= -1){
          return true
        }
      })
    }

    return temp_list
    }

  },
  methods: {
    downloadanalysisdata(){
      let payload = {
        start_time: parseInt(this.date_payload.start_time.getTime()/1000),
        end_time: parseInt(this.date_payload.end_time.getTime()/1000),
      }
      console.log(payload)
      DownloadAnalysisData(payload).then(response => {
        console.log(response)
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'analysis_data.csv');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
    },
    get_gdh_incidents_summary() {
      const token = this.$store.getters.token;
      GetIncidentSummary({ token }).then(response => {
        console.log(response);
        const data = response.data;
        this.get_gdh_active_incidents = data.active_inc_num;
        this.get_total_resolved_incidents = data.resolved_inc_num;
        this.get_today_resolved_incidents = data.resolved_inc_today;
        this.get_gdh_unassigned_incidents = data.unassigned_inc_num;
      }).catch(error => {
        console.error('Error fetching incident summary:', error);
      });
    },
    // get_gdh_today_resolved_incidents() {
    //   GetTodayResolvedIncidents(this.$store.getters.token).then(response => {
    //     console.log(this.$store.getters.token)
    //     console.log(response);
    //     this.get_today_resolved_incidents = response['data'];
    //   });
    // },
    // get_gdh_total_resolved_incidents() {
    //   GetTotalResolvedIncidents(this.$store.getters.token).then(response => {
    //     console.log(this.$store.getters.token)
    //     console.log(response);
    //     this.get_total_resolved_incidents = response['data'];
    //   });
    // },
    // get_unassigned_incidents() {
    //   GetUnassignedIncidents(this.$store.getters.token).then(response => {
    //     console.log(this.$store.getters.token)
    //     console.log(response);
    //     this.get_gdh_unassigned_incidents = response['data'];
    //   });
    // },
    // get_active_incidents() {
    //   GetActiveIncidents(this.$store.getters.token).then(response => {
    //     console.log(response['data']);
    //     this.get_gdh_active_incidents = response['data'];
    //   });
    // },
    updateCurrentTime() {
      const now = new Date();
      const options = {
        timeZone: 'CET', // Corrected here
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric',
        hour12: true,
      };
      this.current_time = now.toLocaleString('en-GB', options).replace(',', '');
    },
    fetch_chart_data_from_change(){
      console.log(this.date_selection)
      let start_time = this.date_selection[0].addMinutes(-(this.date_selection[0].getTimezoneOffset() + 60))
      let end_time = this.date_selection[1].addMinutes(-(this.date_selection[0].getTimezoneOffset() + 60) + 1440)
      this.date_payload = {
        start_time: start_time,
        end_time: end_time
      }
      this.fetch_chart_data()
    },

    fetch_chart_data(){//start_time and end_time are Date objects
      this.fetching_data = true
      let payload = {
        start_time: parseInt(this.date_payload.start_time.getTime()/1000),
        end_time: parseInt(this.date_payload.end_time.getTime()/1000),
      }
      console.log(payload)
      GetIncidentChartData(payload).then(response => {
        // this.bar_data = [];
        // this.bar_type=  [];
        //initialize the bar data
        if(response.data.top5_models){
          this.bar_type=[];
          this.bar_data=[];
          response.data.top5_models.forEach((item) => {
            this.bar_type.push(item['model']);
            this.bar_data.push(item['count']);  
          });
          this.bar_data_dict={
            data: this.bar_data,
            type: this.bar_type
          }
        }

        if(response.data.date_column){
            this.line_label=[];
            this.line_siab_data=[];
            this.line_wiab_data=[];
            this.line_wtp_data=[];
            this.line_total_data=[];
          response.data.date_column.forEach((item) => {
            this.line_label.push(item['label']);
            this.line_siab_data.push(item['siab']);
            this.line_wiab_data.push(item['wiab']);
            this.line_wtp_data.push(item['wtp']);
            this.line_total_data.push(item['sum']);
          });
          this.line_data={
            label: this.line_label,
            siab: this.line_siab_data,
            wiab: this.line_wiab_data,
            wtp: this.line_wtp_data,
            total: this.line_total_data
          }
        }
        this.fetching_data = false
      });

    },
    change_table_data(data){
      this.top_five_incident_list = data
    },
    sortChange(data) {
      const { prop, order } = data
      if(order==null){
        this.sortChange({prop:'id',order:'ascending'})
        return 
      }
      let flag_num = order=="ascending" ? 1 : -1
      this.filtered_incidents.sort((item1,item2)=>(
        (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
      ))
      // this.set_page()
    },
    set_page(){
      // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.filtered_incidents = this.filtered_incidents.slice(start,end)
    },
  }
}
</script>

<style lang="scss" scoped>
$title: rgb(36, 35, 58); // GDH 标题的背景色
$unassigned: rgb(38, 40, 82); // unassigned incident 
$active: rgb(38, 40, 82); // active incident 
$today: rgb(38, 40, 82); // solved today  
$total: rgb(38, 40, 82); // solved total
$bar: rgb(255, 255, 255); // 柱状图 的大长方形的 背景色 #ivan
$line: rgb(200, 209, 197); // 折线图

.title-background {
  background-color: $title;
}
.unassigned-background {
  background-color: $unassigned;
}
.active-background {
  background-color: $active;
}
.today-background {
  background-color: $today;
}
.total-background {
  background-color: $total;
}
.chart-background {
  background-color: $bar;
}
.line-background {
  background-color: $line;
}
.toolbox_panel {
  padding: 0.3% 0.5px 0.3% 0.5%;
  height: 140%;
  width: 100%;
}
.box-card {
  width: 90%;
  max-width: 100%;
  margin: 0 3% 0 3%;
  border-radius: 6px;
}
.box-card-left-side {
  width: 10%;
  max-width: 100%;
  margin: 0 0 auto 3%;
  border-radius: 6px;
}
.toolbox_panel {
  background-color: rgb(255, 255, 255);
}
.auto_analysis{
  height:8vh; //高度
  font-weight: bold; //粗体
  text-align: left; //居中
  font-size: 2.2vw; //字体大小
  font-family:"FangSong_GB2312";
  margin-top:-15px;//上外边距，抵消父元素的 padding 20px
  //#ivan Auto Analysis 的字体样式的配置
}
.line_button_group{
  height:6vh; //高度
}



.demo-date-picker {
  display: flex;
  padding: 0;
  float:right;
  margin-right:50px
}
.demo-date-picker .block {
  // padding:  0;
  text-align: right;
  border-right: solid 1px var(--el-border-color);
  // flex: 1;
}
.demo-date-picker .block:last-child {
  border-right: none;
}
.demo-date-picker .demonstration {
  font-size: 14px;
  margin-bottom: 20px;
  margin-right: 15px;
}

.el-input__inner {
  border-radius: 4px;
  border: 1px solid #91a5d5;
}
.el-range-editor.is-active {
    border-color: #759cc2;
}
</style>
from models.database import db
from flask import Flask
from business.generic.commonfunc import DBConfig
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from email.header import Header
from business.nowit.incident_handler_tools import IncidentHandlerTools
from business.nowit.email_body import inc_remainder_plain_body
from datetime import date, datetime, timedelta
from base_path import application_path
import os
import pytz
from models.incident_models import IncidentR<PERSON>inderUserModel, IncidentReminderUserSchemaModel


def fetch_incidents(fetch_function):
    incidents_list = []
    for _ in range(10): 
        try:
            incidents = fetch_function()
            if incidents:
                for _incident in incidents:
                    incidents_dict = {  
                        'in_cnumber' : _incident.get('inc_number'),
                        'assign_to_people' : _incident.get('inc_assigned_to'),
                        'create_time' : _incident.get('inc_sys_created_on'),
                        'breach_time' : _incident.get('taskslatable_original_breach_time'),
                        'urgency' : _incident.get('inc_urgency'),
                    }
                    incidents_list.append(incidents_dict)
                break
        except Exception as e:
            print(f"Error fetching incidents: {e}")
    return incidents_list


def init_table_rows():
    inc_tool = IncidentHandlerTools()
    user_std_incidents = fetch_incidents(inc_tool.get_gdh_user_std_breached_incidents)          #user_std incidents number
    '''
    return example:
    user_std_incidents:
    {
    'in_cnumber': 'INC100123456',
    'assign_to_people': 'Isacc Wang',
    'create_time': '2024-10-31 13:06:25',
    'breach_time': '2024-11-05 13:06:25',
    'urgency': '2 - Medium'
    }
    '''
    user_internal_incidents = fetch_incidents(inc_tool.get_gdh_user_internal_breached_incidents)            #user_internal incidents number
    event_incidents =  fetch_incidents(inc_tool.get_gdh_event_breached_incidents)           #event incidents number
    user_incidents_list = user_std_incidents + user_internal_incidents
    total_breached_list  = user_incidents_list + event_incidents
    total_breached_incnumber = len(total_breached_list)         #total breached incidents number
    
    # all active incidens number
    all_incidents_number = len(inc_tool.get_gdh_all_active_incidents())
    all_incidents = inc_tool.get_gdh_all_active_incidents()
    all_case = []
    for _all_incidents in all_incidents:
        all_case_dict = {
        'number': _all_incidents['number'],
        'priority': _all_incidents['priority'],
                        } 
        all_case.append(all_case_dict)
        
    critical_case = [a for a in all_case if a['priority'] == '1 - Critical']
    urgent_case = [a for a in all_case if a['priority'] == '2 - Urgent']
    high_case = [a for a in all_case if a['priority'] == '3 - High']
    critical_case_length = len(critical_case)
    urgent_case_length = len(urgent_case)
    high_case_length = len(high_case)

    all_no_assignee_incidents = inc_tool.get_gdh_all_active_incidents_no_assignee()       #no assignee incidents  
    all_no_assignee_incidents_num = len(all_no_assignee_incidents)

    #ritm task list
    get_gdh_ritm_task = inc_tool.get_gdh_ritm_task()
    get_gdh_ritm_task_list = []
    if get_gdh_ritm_task:
        for _get_gdh_ritm_task in get_gdh_ritm_task:
            get_gdh_ritm_task_dict = { 
            'sci_number' : _get_gdh_ritm_task.get('sci_number'),
            'sci_opened_at' : _get_gdh_ritm_task.get('sci_opened_at'),
            'sci_assigned_to' : _get_gdh_ritm_task.get('sci_assigned_to'),
            'taskslatable_has_breached' : _get_gdh_ritm_task.get('taskslatable_has_breached'),
            'taskslatable_original_breach_time' : _get_gdh_ritm_task.get('taskslatable_original_breach_time'),
            }
            get_gdh_ritm_task_list.append(get_gdh_ritm_task_dict)
    all_gdh_ritm_task_list_num = len(get_gdh_ritm_task_list)  #all ritm task list

    no_assignee_ritm_task_list = [a for a in get_gdh_ritm_task_list if a['sci_assigned_to'] == ''] 
    no_assignee_ritm_task_list_num = len(no_assignee_ritm_task_list)     # no assignee ritm task list 

    all_breached_gdh_ritm_task_list = [a for a in get_gdh_ritm_task_list if a['taskslatable_has_breached'] == 'true']
    all_breached_gdh_ritm_task_list_num = len(all_breached_gdh_ritm_task_list)      #all breached ritm task list

    get_gdh_ctask = inc_tool.get_gdh_ctask()
    get_gdh_ctask_list = []
    if get_gdh_ctask:
        for _get_gdh_ctask in get_gdh_ctask:
            planned_start_date = _get_gdh_ctask.get('planned_start_date')
            if planned_start_date:
                #CET timezone
                cet_tz = pytz.timezone('CET')
                current_cet_time = datetime.now(cet_tz)
                current_timestamp = int(current_cet_time.timestamp())
                #start_time_gap
                planned_start_date_str = _get_gdh_ctask.get('planned_start_date')
                planned_start_date = datetime.strptime(planned_start_date_str, '%Y-%m-%d %H:%M:%S')
                planned_start_date_cet = cet_tz.localize(planned_start_date)
                planned_start_date_timestamp = planned_start_date_cet.timestamp()
                start_time_gap = planned_start_date_timestamp - current_timestamp
                #end_time_gap
                planned_end_date_str = _get_gdh_ctask.get('planned_end_date')
                planned_end_date = datetime.strptime(planned_end_date_str, '%Y-%m-%d %H:%M:%S')
                planned_end_date_cet = cet_tz.localize(planned_end_date)
                planned_end_date_timestamp = planned_end_date_cet.timestamp()
                end_time_gap = planned_end_date_timestamp - current_timestamp

                get_gdh_ctask_dict = { 
                'number' : _get_gdh_ctask.get('number'),
                'state' : _get_gdh_ctask.get('state'),
                'on_hold' : _get_gdh_ctask.get('on_hold'),
                'planned_start_date' : _get_gdh_ctask.get('planned_start_date'),
                'planned_end_date' : _get_gdh_ctask.get('planned_end_date'),
                'assigned_to' : _get_gdh_ctask.get('assigned_to'),
                'change_request' : _get_gdh_ctask.get('change_request'),
                'start_time_gap' : start_time_gap,
                'end_time_gap' : end_time_gap,
                }
                get_gdh_ctask_list.append(get_gdh_ctask_dict)   
        breached_ctask_list = [task for task in get_gdh_ctask_list if task['end_time_gap'] <= 0]
        breached_ctask_list_num = len(breached_ctask_list)

    get_gdh_ctask_no_pm = inc_tool.get_gdh_ctask_no_pm()
    get_gdh_ctask_no_pm_list = []
    if get_gdh_ctask_no_pm:
        for _get_gdh_ctask_no_pm in get_gdh_ctask_no_pm:
            planned_start_date_no_pm = _get_gdh_ctask_no_pm.get('planned_start_date')
            if planned_start_date_no_pm:
                #CET timezone
                cet_tz = pytz.timezone('CET')
                current_cet_time = datetime.now(cet_tz)
                current_timestamp = int(current_cet_time.timestamp())
                #start_time_gap
                planned_start_date_str_no_pm = _get_gdh_ctask_no_pm.get('planned_start_date')
                planned_start_date_no_pm = datetime.strptime(planned_start_date_str_no_pm, '%Y-%m-%d %H:%M:%S')
                planned_start_date_cet_no_pm = cet_tz.localize(planned_start_date_no_pm)
                planned_start_date_timestamp_no_pm = planned_start_date_cet_no_pm.timestamp()
                start_time_gap_no_pm = planned_start_date_timestamp_no_pm - current_timestamp
                #end_time_gap
                planned_end_date_str_no_pm = _get_gdh_ctask_no_pm.get('planned_end_date')
                planned_end_date_no_pm = datetime.strptime(planned_end_date_str_no_pm, '%Y-%m-%d %H:%M:%S')
                planned_end_date_cet_no_pm = cet_tz.localize(planned_end_date_no_pm)
                planned_end_date_timestamp_no_pm = planned_end_date_cet_no_pm.timestamp()
                end_time_gap_no_pm = planned_end_date_timestamp_no_pm - current_timestamp

                get_gdh_ctask_no_pm_dict = { 
                'number' : _get_gdh_ctask_no_pm.get('number'),
                'state' : _get_gdh_ctask_no_pm.get('state'),
                'on_hold' : _get_gdh_ctask_no_pm.get('on_hold'),
                'planned_start_date' : _get_gdh_ctask_no_pm.get('planned_start_date'),
                'planned_end_date' : _get_gdh_ctask_no_pm.get('planned_end_date'),
                'assigned_to' : _get_gdh_ctask_no_pm.get('assigned_to'),
                'change_request' : _get_gdh_ctask_no_pm.get('change_request'),
                'start_time_gap' : start_time_gap_no_pm,
                'end_time_gap' : end_time_gap_no_pm,
                }
                get_gdh_ctask_no_pm_list.append(get_gdh_ctask_no_pm_dict)   

        all_gdh_ctask_list_num = len(get_gdh_ctask_no_pm_list)
        ctask_no_assignee_ctask_list = [a for a in get_gdh_ctask_no_pm_list if a['assigned_to'] == '']
        ctask_no_assignee_ctask_list_num = len(ctask_no_assignee_ctask_list) #      ctask_no_assignee all withhout PM

        ctask_start_in_one_day_list = [task for task in get_gdh_ctask_no_pm_list if task['start_time_gap'] > 0 and task['start_time_gap'] <= 86400]
        ctask_start_in_one_day_list_num = len(ctask_start_in_one_day_list)  #      ctask_no_assignee  in 1 day without PM

        no_assignee_in_one_week_ctask_list = [task for task in get_gdh_ctask_no_pm_list if task['assigned_to'] == '' and task['start_time_gap'] > 0 and task['start_time_gap'] <= 604800]
        no_assignee_in_one_week_ctask_list_num = len(no_assignee_in_one_week_ctask_list)  #      ctask_no_assignee  in 7 day without PM


    email_params = {
        'all_incidents_number': all_incidents_number,
        'total_breached_incnumber': total_breached_incnumber,
        'all_no_assignee_incidents_num': all_no_assignee_incidents_num,
        'critical_case_length': critical_case_length,
        'urgent_case_length': urgent_case_length,
        'high_case_length': high_case_length,
        #ritm
        'all_gdh_ritm_task_list_num': all_gdh_ritm_task_list_num,
        'no_assignee_ritm_task_list_num': no_assignee_ritm_task_list_num,
        'all_breached_gdh_ritm_task_list_num': all_breached_gdh_ritm_task_list_num,
        #ctask
        'all_gdh_ctask_list_num': all_gdh_ctask_list_num,
        'ctask_start_in_one_day_list_num': ctask_start_in_one_day_list_num,
        'breached_ctask_list_num': breached_ctask_list_num,
        'no_assignee_in_one_week_ctask_list_num': no_assignee_in_one_week_ctask_list_num,
        'ctask_no_assignee_ctask_list_num': ctask_no_assignee_ctask_list_num,
    }
    return email_params


def confluence_calendar_api(calendar_url):
    all_confluence = calendar_url()
    all_confluence_invitee = all_confluence['invitees']
    all_tasker = []
    checkall = IncidentReminderUserModel().query.all()
    schema = IncidentReminderUserSchemaModel(many=True)
    result = schema.dump(checkall)
    for _invitee in all_confluence_invitee:
        if 'name' in _invitee: 
            name1 = _invitee['name'].lower()
            matching_records = [n for n in result if name1 == n['name']]
            all_tasker.extend(matching_records)
        else:
            return {"no name found"}

    cn_region = [a['email_address_shortdescription'] for a in all_tasker if a['region'] == 'CN']
    eu_region = [a['email_address_shortdescription'] for a in all_tasker if a['region'] == 'EU']
    us_region = [a['email_address_shortdescription'] for a in all_tasker if a['region'] == 'US']
    cn_region_str = " ".join(cn_region) if cn_region else "N/A" 
    eu_region_str = " ".join(eu_region) if eu_region else "N/A"
    us_region_str = " ".join(us_region) if us_region else "N/A"
    return cn_region_str, eu_region_str, us_region_str


def get_calendar_users():
    inctool = IncidentHandlerTools()
    cet = pytz.timezone('CET')
    current_date_cet = datetime.now(cet)
    current_date_cet_formatted = current_date_cet.strftime('%Y-%m-%d')
    sevenday_date_cet_formatted = (current_date_cet + timedelta(days=7)).strftime('%Y-%m-%d')

    cn_region_tasker_str, eu_region_tasker_str, us_region_tasker_str = confluence_calendar_api(
        lambda: inctool.get_confluence_task_api_url(current_date_cet_formatted))     #calendar_api for who do change tasks
    cn_region_incer_str, eu_region_incer_str, us_region_incer_str = confluence_calendar_api(
        lambda: inctool.get_confluence_inc_api_url(current_date_cet_formatted))         #calendar_api for who do incidents
    cn_region_dutyer_str, eu_region_dutyer_str, us_region_dutyer_str = confluence_calendar_api(
        lambda: inctool.get_confluence_duty_api_url(current_date_cet_formatted))     #calendar_api for who take duty
    cn_region_tasker_7day_str, eu_region_tasker_7day_str, us_region_tasker_7day_str = confluence_calendar_api(
        lambda: inctool.get_confluence_task_api_url(sevenday_date_cet_formatted))         #calendar_api for who do change tasks next week
    calendar_tasker_param = { 
        'cn_region_tasker_str': cn_region_tasker_str,
        'eu_region_tasker_str': eu_region_tasker_str,
        'us_region_tasker_str': us_region_tasker_str,
        'cn_region_incer_str': cn_region_incer_str,
        'eu_region_incer_str': eu_region_incer_str,
        'us_region_incer_str': us_region_incer_str,
        'cn_region_dutyer_str': cn_region_dutyer_str,
        'eu_region_dutyer_str': eu_region_dutyer_str,
        'us_region_dutyer_str': us_region_dutyer_str,
        # who do change tasks next week
        'cn_region_tasker_7day_str': cn_region_tasker_7day_str,
        'eu_region_tasker_7day_str': eu_region_tasker_7day_str,
        'us_region_tasker_7day_str': us_region_tasker_7day_str,
    }
    return calendar_tasker_param


#get day of the week
def cal_weekday(formatted_date):
    date_obj = datetime.strptime(formatted_date, '%Y-%m-%d')
    day = date_obj.isocalendar()[2]  # only take the weekday number (1-7)
    return day
# Email part
def send_email(sender, receiver, subject, body, bccreceiver):
# def send_email(sender, receiver, subject, body):      # no bccreceiver
    message = MIMEMultipart('related') 
    message['From'] = sender
    message['To'] = ",".join(receiver)
    #message['Cc'] = ",".join(cc)
    message['Bcc'] = ",".join(bccreceiver)
    message['Subject'] = Header(subject, 'utf-8')

    message.attach(MIMEText(body, 'html'))
    image_path = os.path.join(application_path, "static", "title.png")  
    with open(image_path, 'rb') as img_file:
        img = MIMEImage(img_file.read())
        img.add_header('Content-ID', '<image1>')  # set Content-ID to used in HTML
        message.attach(img)
    try:
        with smtplib.SMTP('smtp-gw.ikea.com', 25) as server:
            server.send_message(message)
        print("Email Send Successfully")
    except Exception as e:
        print(f"Email Send failed:{e}")


def schedule_time():
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    app.app_context().push()
    db.init_app(app)
    formatted_date = date.today().strftime("%Y-%m-%d")        #get date for title
    day = cal_weekday(formatted_date)
    if day > 5:         # if it's weekend, jump out
        return

    sender = "<EMAIL>"
    receiver = ["<EMAIL>"]
    bccreceiver = ["<EMAIL>"]

    subject = f"Incident-Handler Auto-Reminder {formatted_date}"
    email_params = init_table_rows()
    calendar_tasker_param = get_calendar_users()
    combined_params = {**email_params, **calendar_tasker_param}
    body = inc_remainder_plain_body(**combined_params )
    send_email( sender, receiver, subject, body, bccreceiver)
    #     send_email( sender, receiver, subject, body)       # no bccreceiver
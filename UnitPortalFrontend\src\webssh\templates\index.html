<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <title> GDHWebSSH </title>
    <link href="static/img/favicon.ico" rel="icon" type="image/icon">
    <link href="static/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="static/css/xterm.min.css" rel="stylesheet" type="text/css"/>
    <!-- <link href="static/css/fullscreen.min.css" rel="stylesheet" type="text/css"/> -->
    <!-- <link href="static/css/webssh2.css" rel="stylesheet" type="text/css"/> -->
    <style>
      .row {
        margin-top: 15px;
        margin-bottom: 10px;
      }

      .container {
        margin-top: 20px;
        background-color: #fafafa;
        /* margin-bottom: 40px; */
      }
      
      body, html {
          height: 100%;
          margin: 0;
          /* background-color: #000000; */
      }
      #terminal-container {
        /* position: relative; */
        /* background-color: #000000; */
        display: block;
        top: 0;
        /* bottom: 20px;  */
        height: 100vh;
        left: 2px;
        right: 0;
        overflow: visible;
    }
      #terminal-container .terminal {
          background-color: #000000;
          color: #fafafa;
          padding: 0;
          height: calc(100% - 20px); /* Updated to match the height of the container */
      }
      #terminal-container .terminal:focus .terminal-cursor {
          background-color: #fafafa;
      }
      #bottomdiv {
          position: fixed;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 20px; 
          background-color: rgb(50, 50, 50);
          border-top: 1px solid #fff;
          z-index: 99;
      }
      #status {
        display: inline-block;
        color: rgb(240, 240, 240);
        padding-left: 15%;
        /* padding-right: 15px; */
        margin-right:auto ;
        margin-left: auto;
        margin-top: 20px;
        border-color: white;
        border-style: none solid none solid;
        border-width: 1px;
        /* text-align: left; */
        z-index: 100;
      }
    
      .btn {
        margin-top: 15px;
      }

      .btn-danger {
        margin-left: 5px;
      }
      {% if font.family %}
      @font-face {
        font-family: '{{ font.family }}';
        src: url('{{ font.url }}');
      }

      body {
        font-family: '{{ font.family }}';
      }
      {% end %}
    </style>
  </head>
  <body>
    <div id="waiter" style="display: none"> Connecting ... </div>

    <div class="container form-container" style="display: none">
      <form id="connect" action="" method="post" enctype="multipart/form-data"{% if debug %} novalidate{% end %}>
        <div class="row">
          <div class="col">
            <label for="Hostname">Hostname</label>
            <input class="form-control" type="text" id="hostname" name="hostname" value="" required>
          </div>
          <div class="col">
            <label for="Port">Port</label>
            <input class="form-control" type="number" id="port" name="port" placeholder="22" value="" min=1 max=65535>
          </div>
        </div>
        <div class="row">
          <div class="col">
            <label for="Username">Username</label>
            <input class="form-control" type="text" id="username" name="username" value="" required>
          </div>
          <div class="col">
            <label for="Password">Password</label>
            <input class="form-control" type="password" id="password" name="password" value="">
          </div>
        </div>
        <div class="row" >
          <div class="col">
            <label for="ius">Operator</label>
            <input class="form-control" type="text" id="ius" name="ius" value="" required>
          </div>
          <!-- <div class="col">
            <label for="Passphrase">Passphrase</label>
            <input class="form-control" type="password" id="passphrase" name="passphrase" value="">
          </div> -->
        </div>
        <!-- <div class="row">
          <div class="col">
            <label for="totp">Totp (time-based one-time password)</label>
            <input class="form-control" type="password" id="totp" name="totp" value="">
          </div>
          <div class="col">
          </div>
        </div> -->
        <input type="hidden" id="term" name="term" value="xterm-256color">
        {% module xsrf_form_html() %}
        <button type="submit" class="btn btn-primary">Connect</button>
        <button type="reset" class="btn btn-danger">Reset</button>
      </form>
    </div>

    <div >
      <!-- <div id="status" style="color: red;"></div> -->
      <div id="terminal-container" > </div>
      <div id="bottomdiv" >
        <div class="dropup" id="menu">
          <i class="fas fa-bars fa-fw"></i> Menu
          <div id="dropupContent" class="dropup-content">
            <a id="logBtn"><i class="fas fa-clipboard fa-fw"></i> Start Log</a>
            <a id="downloadLogBtn"><i class="fas fa-download fa-fw"></i> Download Log</a>
          </div>
        </div>
      </div>
    </div>

    <script src="static/js/jquery.min.js"></script>
    <script src="static/js/popper.min.js"></script>
    <script src="static/js/bootstrap.min.js"></script>
    <script src="static/js/xterm.min.js"></script>
    <script src="static/js/xterm-addon-fit.min.js"></script>
    <script src="static/js/main.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsencrypt/3.0.0/jsencrypt.min.js"></script>
  </body>
</html>
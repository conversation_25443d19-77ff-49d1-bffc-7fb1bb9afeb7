from flask import request
from flask_apispec import use_kwargs
from flask_restful import Resource

from business.authentication.authentication import Vault
from business.authentication.tokenvalidation import PrivilegeValidation
from swagger.vault_schema import GetVaultDataSchema
from .route import route


@route('/api/v1/vault/data')
class RestfulVaultData(Resource):
    @use_kwargs(GetVaultDataSchema, location='json', apply=False)
    @PrivilegeValidation(privilege={"role_administration": "view_vault"})
    def post(self):
        body = request.get_json(force=True)
        labels = body.get('labels')

        response = []
        vault = Vault(body.get('tier', "PRODUCTION"), body.get("sa"), body.get('engine'), body.get('namespace'),
                      body.get('url'))
        for label in labels:
            res, secret = vault.get_secret(label)
            if not res:
                item = {label: {"username": "", "secret": ""}}
            else:
                item = {label: secret}
            response.append(item)
        return response

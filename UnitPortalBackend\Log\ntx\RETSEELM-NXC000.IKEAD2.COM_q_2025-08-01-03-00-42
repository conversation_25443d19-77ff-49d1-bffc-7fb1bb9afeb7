2025-08-01 11:00:46,542 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:00:48,839 INFO Getting the cluster list from PC.
2025-08-01 11:00:57,838 INFO Getting cluster list from ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:01:00,697 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/api/nutanix/v3/clusters/list, method: POST, headers: None
2025-08-01 11:01:00,697 INFO params: None
2025-08-01 11:01:00,698 INFO User: <EMAIL>
2025-08-01 11:01:00,698 INFO payload: {'kind': 'cluster'}
2025-08-01 11:01:00,707 INFO files: None
2025-08-01 11:01:00,708 INFO timeout: None
2025-08-01 11:02:10,125 INFO Got the cluster list from PC, searching the target cluster RETSEELM-NXC000 in the list.
2025-08-01 11:02:21,277 INFO RETSEELM-NXC000 exists in ssp-dhd2-ntx.ikead2.com, continue.
2025-08-01 11:02:42,475 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 11:02:42,475 INFO params: None
2025-08-01 11:02:42,475 INFO User: <EMAIL>
2025-08-01 11:02:42,475 INFO payload: None
2025-08-01 11:02:42,475 INFO files: None
2025-08-01 11:02:42,475 INFO timeout: 30
2025-08-01 11:02:44,188 INFO Getting host list from RETSEELM-NXC000.
2025-08-01 11:02:44,188 INFO Got the host list from RETSEELM-NXC000.
2025-08-01 11:02:44,188 INFO Got the host list.
2025-08-01 11:02:44,188 INFO Getting vault from IKEAD2.
2025-08-01 11:02:44,874 INFO Getting Site_Pe_Nutanix.
2025-08-01 11:02:45,349 INFO Got Site_Pe_Nutanix.
2025-08-01 11:02:45,350 INFO Getting Site_Pe_Admin.
2025-08-01 11:02:45,882 INFO Got Site_Pe_Admin.
2025-08-01 11:02:45,882 INFO Getting Site_Oob.
2025-08-01 11:02:46,358 INFO Got Site_Oob.
2025-08-01 11:02:46,358 INFO Getting Site_Ahv_Nutanix.
2025-08-01 11:02:46,824 INFO Got Site_Ahv_Nutanix.
2025-08-01 11:02:46,826 INFO Getting Site_Ahv_Root.
2025-08-01 11:02:47,300 INFO Got Site_Ahv_Root.
2025-08-01 11:02:47,300 INFO Getting Site_Gw_Priv_Key.
2025-08-01 11:02:47,768 INFO Got Site_Gw_Priv_Key.
2025-08-01 11:02:47,769 INFO Getting Site_Gw_Pub_Key.
2025-08-01 11:02:48,303 INFO Got Site_Gw_Pub_Key.
2025-08-01 11:02:48,303 INFO Getting Site_Pe_Svc.
2025-08-01 11:02:48,786 INFO Got Site_Pe_Svc.
2025-08-01 11:02:49,677 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 11:02:49,678 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-01 11:02:49,678 INFO params: None
2025-08-01 11:02:49,678 INFO User: <EMAIL>
2025-08-01 11:02:49,678 INFO payload: None
2025-08-01 11:02:49,678 INFO files: None
2025-08-01 11:02:49,678 INFO timeout: 30
2025-08-01 11:02:51,076 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-08-01 11:02:51,077 WARNING API response is not ok, going to do the 2 retry...
2025-08-01 11:02:52,389 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-08-01 11:02:52,389 WARNING API response is not ok, going to do the 3 retry...
2025-08-01 11:02:53,624 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-08-01 11:02:53,624 WARNING API response is not ok, going to do the 4 retry...
2025-08-01 11:02:54,862 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-08-01 11:02:54,862 WARNING API response is not ok, going to do the 5 retry...
2025-08-01 11:02:56,118 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-08-01 11:02:56,119 ERROR Failed to get the VM list from RETSEELM-NXC000. Error message:502 Bad Gateway: Out of retry times when calling https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms. Response: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'

<template>
  <div ref="rightPanel" :class="{show:show}" class="rightPanel-container">
    <div class="rightPanel-background" />
    <div class="rightPanel">
      <div class="handle-button" :style="{'bottom':buttonbottom+'px','background-color':theme}" @click="show=!show">
        <i :class="show?'el-icon-close':'el-icon-chat-dot-square'" />
        <span class="sticky-button-text">Feedback</span>
      </div>
      <div class="rightPanel-items">
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
import { addClass, removeClass } from '@/utils'

export default {
  name: 'RightPanel',
  props: {
    clickNotClose: {
      default: false,
      type: Boolean
    },
    buttonbottom: {
      default: 250,
      type: Number
    }
  },
  data() {
    return {
      show: false
    }
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme
    }
  },
  watch: {
    show(value) {
      console.log(value)
      console.log(this.clickNotClose)
      if (value && !this.clickNotClose) {
        this.addEventClick()
      }
      if (value) {
        addClass(document.body, 'showRightPanel')
      } else {
        removeClass(document.body, 'showRightPanel')
      }
    }
  },
  mounted() {
    this.insertToBody()
  },
  beforeDestroy() {
    const elx = this.$refs.rightPanel
    elx.remove()
  },
  methods: {
    addEventClick() {
      window.addEventListener('click', this.closeSidebar)
    },
    closeSidebar(evt) {
      const parent = evt.target.closest('.rightPanel')
      if (!parent) {
        this.show = false
        window.removeEventListener('click', this.closeSidebar)
      }
    },
    insertToBody() {
      const elx = this.$refs.rightPanel
      const body = document.querySelector('body')
      body.insertBefore(elx, body.firstChild)
    }
  }
}
</script>

<style>
.showRightPanel {
  /* overflow: auto; */
  position: relative;
  /* width: calc(100% - 15px); */
}
</style>

<style lang="scss" scoped>
.rightPanel-background {
  position: fixed;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);
  background: rgba(0, 0, 0, .2);
  z-index: -1;
}

.rightPanel {
  width: 400px;
  height: 440px;
  
  position: fixed;
  bottom: calc(30px + 1.767rem);
  right: 0;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);
  transition: all ,500ms ;
  // transition: 0.5s ease-in-out;;
  transform: translate(100%);
  background: #ffffff;
  z-index: 40000;
  box-sizing: border-box;
}

.show {
  // transition: all, 3s, cubic-bezier(.7, .3, .1, 1);

  .rightPanel-background {
    z-index: 20000;
    opacity: 0;
    width: 100%;
    // height: 100%;
    overflow: hidden;
  }

  .rightPanel {
    transition: all ,800ms;
    transform: translate(0);
  }
  
}
.rightPanel-items {
    height: 100%;
    // max-height: 46vh;
    // padding: auto;
    margin-bottom: 0;
}
.handle-button {
  width: 28px;
  height: 138px;
  position: absolute;
  top: 0;
  left: -26px;
  text-align: center;
  font-size: 18px;
  border-radius: 6px 0 0 6px !important;
  z-index: 0;
  pointer-events: auto;
  cursor: pointer;
  color: #fff;
  line-height: 48px;
  i {
    font-size: 24px;
    line-height: 48px;
  }
}
.sticky-button-text {
  writing-mode: vertical-rl;
  // text-orientation: upright;
  // white-space: nowrap;\
  position: absolute;
  margin-left: -36px;
  margin-top: 40px;
  color: #fff;
  transform: rotate(180deg);
  writing-mode: vertical-rl;
}

</style>

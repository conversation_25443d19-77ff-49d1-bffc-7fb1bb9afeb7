import importlib
from .route import swagger_docs
from .blueprint import distributedhosting, api
import glob
import os
# glob all files start with restful_ in current direcotry and import them
cwd = os.path.dirname(os.path.abspath(__file__))
restful_files = glob.glob(os.path.join(cwd, 'restful_*.py'))
for file in restful_files:
    # cauculate relative path to cwd
    relative_path = os.path.relpath(file, cwd)
    # remove the .py extension
    relative_path = relative_path.replace('.py', '')
    # insert /
    relative_path = '/' + relative_path
    relative_path = relative_path.replace('/', '.')
    # from relative_path import *
    importlib.import_module(relative_path, package='business.restful')

__all__ = ['distributedhosting', 'api', 'swagger_docs']

import sqlalchemy
import werkzeug.exceptions as flaskex
from sqlalchemy.ext.hybrid import hybrid_method

from models.database import db, ma


class ModelSLIvCenter(db.Model):
    __tablename__        = 'dh_retail_sli_vc'
    id                   = db.Column(db.Integer, primary_key=True)
    fqdn                 = db.Column(db.String(50), unique=True, nullable=False)
    vc_version           = db.Column(db.String(200))
    vc_build             = db.Column(db.String(200))
    # vc_lastupdated          = db.Column(db.String(200))
    clusters_number      = db.Column(db.String(200))
    hosts_number         = db.Column(db.String(200))
    vms_number           = db.Column(db.String(200))
    vc_cn                = db.Column(db.String(200))
    vc_ip                = db.Column(db.String(50))


class ModelSLIvCenterSchema(ma.Schema):
    class Meta:
        fields = ('id', 'fqdn', 'vc_version', 'vc_build', 'clusters_number', 'hosts_number', 'vms_number', 'vc_cn',
                  'vc_ip')


class ModelSLICluster(db.Model):
    __tablename__ = 'dh_retail_sli_cluster'
    id = db.Column(db.Integer, primary_key=True)
    vsan_enabled = db.Column(db.String(50))
    vsan_disk_claim_mode = db.Column(db.String(255))
    parent_id = db.Column(db.String(255))
    parent_folder = db.Column(db.String(255))
    ha_enabled = db.Column(db.String(50))
    ha_admission_control_enabled = db.Column(db.String(50))
    ha_failover_level = db.Column(db.String(50))
    ha_restart_priority = db.Column(db.String(50))
    ha_isolation_response = db.Column(db.String(255))
    vm_swap_file_policy = db.Column(db.String(255))
    drs_enabled = db.Column(db.String(50))
    drs_automation_level = db.Column(db.String(255))
    crypto_mode = db.Column(db.String(255))
    cluster_id = db.Column(db.String(255))
    name = db.Column(db.String(50))
    master_host = db.Column(db.String(50))
    slave_host = db.Column(db.String(50))
    master_ovc = db.Column(db.String(50))
    slave_ovc = db.Column(db.String(50))
    vc_fqdn = db.Column(db.String(50))
    status = db.Column(db.String(50))
    stagepreparation = db.Column(db.String(50))
    movedevop = db.Column(db.String(50))
    stagecutover = db.Column(db.String(50))
    moveip = db.Column(db.String(255))
    planuuid = db.Column(db.String(255))
    syncstatus = db.Column(db.String(255))
    # hosts = db.relationship('ModelSLIHost' , backref = 'cluster' , cascade = "all, delete-orphan")
    
    def check_ifcluster_existence(self, cluster_name):
        return ModelSLICluster.query.filter(ModelSLICluster.status =="Y",ModelSLICluster.name==cluster_name).first()
    
    @hybrid_method    
    def get_syncing_task_move(self):
        cluster = ModelSLICluster.query.filter(ModelSLICluster.stagepreparation != "Done",ModelSLICluster.stagepreparation != "Error", ModelSLICluster.syncstatus=="Syncing").all()
        return cluster

    @hybrid_method
    def get_cluster_by_cluster_name(self, country_code, site_code):
        cluster_name = f'%{country_code}{site_code}'
        try:
            cluster_info = ModelSLICluster.query.filter(ModelSLICluster.name.like(cluster_name)).scalar()
            return cluster_info
        except sqlalchemy.exc.MultipleResultsFound as e:
            db.session.rollback()
            msg = f"Multiple cluster info found with country_code {country_code} and site_code {site_code}! Original error: {e}"
            raise flaskex.InternalServerError(msg)


class ModelSLIClusterSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'vsan_enabled', 'vsan_disk_claim_mode', 'parent_id', 'parent_folder', 'ha_enabled',
            'ha_admission_control_enabled', 'ha_failover_level', 'ha_restart_priority', 'ha_isolation_response',
            'vm_swap_file_policy', 'drs_enabled', 'drs_automation_level', 'crypto_mode', 'cluster_id', 'name',
            'master_host', 'slave_host', 'master_ovc', 'slave_ovc', 'vc_fqdn','status','stagecutover','movedevop',
            'stagepreparation','planuuid','moveip','syncstatus'
        )


class ModelSLIHost(db.Model):
    __tablename__ = 'dh_retail_sli_host'
    id = db.Column(db.Integer, primary_key=True)
    host_id  = db.Column(db.String(50))
    name  = db.Column(db.String(50))
    serialnumber  = db.Column(db.String(50))
    crypto_state  = db.Column(db.String(50))
    api_version  = db.Column(db.String(50))
    network_info  = db.Column(db.String(50))
    storage_info  = db.Column(db.String(50))
    build  = db.Column(db.String(50))
    version  = db.Column(db.String(50))
    time_zone  = db.Column(db.String(50))
    processor_type  = db.Column(db.String(50))
    memory_total_gb  = db.Column(db.String(50))
    model  = db.Column(db.String(50))
    num_cpu  = db.Column(db.String(50))
    cpu_total_mhz  = db.Column(db.String(50))
    license_key  = db.Column(db.String(50))
    parent  = db.Column(db.String(50))
    # cluster_id = db.Column(db.Integer, db.ForeignKey('dh_sli_cluster.id'))
    cluster_id = db.Column(db.Integer)
    status = db.Column(db.String(50))


class ModelSLIHostSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'host_id', 'name', 'crypto_state', 'api_version', 'network_info', 'storage_info', 'build', 'version',
            'time_zone', 'processor_type', 'memory_total_gb', 'model', 'num_cpu', 'cpu_total_mhz', 'license_key',
            'parent', 'cluster_id', 'serialnumber','status'
        )


class ModelSLIVMs(db.Model):
    __tablename__ = 'dh_retail_sli_vms'
    id = db.Column(db.Integer, primary_key=True)
    version = db.Column(db.String(50))
    name = db.Column(db.String(50))
    folder_id = db.Column(db.String(50))
    folder = db.Column(db.String(50))
    resourcepool_id = db.Column(db.String(50))
    persistent_id = db.Column(db.String(50))
    used_space_gb = db.Column(db.String(50))
    provisioned_space_gb = db.Column(db.String(50))
    memory_total_gb = db.Column(db.String(50))
    harestart_priority = db.Column(db.String(50))
    num_cpu = db.Column(db.String(50))
    cores_persocket = db.Column(db.String(50))
    haisolation_response = db.Column(db.String(50))
    guest_id = db.Column(db.String(50))
    host_id = db.Column(db.String(50))
    vmhost_name = db.Column(db.String(50))
    create_date = db.Column(db.String(50))
    # cluster_id = db.Column(db.Integer, db.ForeignKey('dh_sli_cluster.id'))
    power_state = db.Column(db.String(50))
    cluster_id = db.Column(db.Integer)
    guest_name = db.Column(db.String(100))
    vm_id = db.Column(db.String(100))
    status = db.Column(db.String(100))
    vc_fqdn = db.Column(db.String(100))


class ModelSLIVMsSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'version', 'name', 'folder_id', 'folder', 'resourcepool_id', 'persistent_id', 'used_space_gb',
            'provisioned_space_gb', 'memory_total_gb', 'harestart_priority', 'num_cpu', 'cores_persocket',
            'haisolation_response', 'guest_id', 'host_id', 'vmhost_name', 'create_date', 'power_state', 'cluster_id','vc_fqdn','status','vm_id','guest_name'
        )

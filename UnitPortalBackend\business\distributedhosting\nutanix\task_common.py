import logging
import os
import time
from datetime import datetime
import multiprocessing
import uuid

import psutil
import sqlalchemy
from flask import Flask

from business.distributedhosting.nutanix.task_status import TaskStatus
from business.generic.commonfunc import create_file, setup_common_logger, get_user_by_token
from business.loggings.loggings import DBLogging
from models.database import db
import werkzeug.exceptions as flaskex
from business.generic.commonfunc import DBConfig


class TaskSkipped(flaskex.HTTPException):
    code = 667
    description = "The task is skipped."


class TaskCommon:
    def __init__(self, task_model, task_model_schema):
        self.task_model = task_model
        self.task_model_schema = task_model_schema

    def check_task_existence(self, status_list=[TaskStatus.NOT_STARTED, TaskStatus.IN_PROGRESS], **kwargs):
        logging.info("Checking if task already exists in database...")
        result = self.task_model.query.filter_by(**kwargs).filter(self.task_model.status.in_(status_list)).all()
        if result:
            logging.error(f"Task {kwargs} already exists! Please wait for previous task to be done.")
            raise flaskex.Conflict(f"Task {kwargs} already exists! Please wait for previous task to be done.")
        logging.info(f"Task {kwargs} does not exist.")

    def get_exist_tasks(self, **kwargs):
        logging.info("Getting task with specified conditions")
        result = self.task_model.query.filter_by(**kwargs).all()
        return result

    def create_task_in_db(self, **kwargs):
        logging.info("Start to create task in database...")
        kwargs['create_date'] = datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
        if not kwargs.get('creater'):
            kwargs['creater'] = get_user_by_token().username
        kwargs['status'] = TaskStatus.NOT_STARTED
        for _ in range(3):
            try:
                task_to_add = self.task_model(**kwargs)
                db.session.add(task_to_add)
                db.session.commit()
                time.sleep(1)
                logging.info("Task created successfully! "
                             f"Task type: {self.task_model()}. "
                             f"Task detail: {self.task_model_schema().dump(task_to_add)}")
                return task_to_add
            except sqlalchemy.exc.DBAPIError as e:
                logging.warning("SQL error occurred when creating task, trying to rollback and create again...")
                logging.warning(f"Original Error: {e}")
                db.session.rollback()
        raise flaskex.InternalServerError("Failed to create task in db... Please check log.")


    def get_task_by_id_in_db(self, id):
        return self.task_model.query.filter_by(id=id).first()

    def is_task_overtime(self):
        pass

    def spawn_subprocess(self, target_function, task_id, args=[], kwargs={}, init_app=True, check_process_amount=True):
        if init_app:
            self.init_app()
        if args:
            if not isinstance(args, list):
                raise flaskex.InternalServerError("The param 'args' should be list type!")
            p = multiprocessing.Process(target=target_function, args=args)
        elif kwargs:
            if not isinstance(kwargs, dict):
                raise flaskex.InternalServerError("The param 'kwargs' should be dict type!")
            p = multiprocessing.Process(target=target_function, kwargs=kwargs)
        else:
            p = multiprocessing.Process(target=target_function)
        if check_process_amount:
            self.check_process_amount()
        logging.info(f"Spawning a subprocess to do the task, task_id: {task_id}")
        p.start()
        self.store_pid_into_db(p.pid, self.task_model, task_id)
        logging.info(f"Subprocess spawn successfully, pid: {p.pid}")

    @staticmethod
    def store_pid_into_db(pid, task_model, task_id):
        _task = task_model.query.filter_by(id=task_id).first()
        _task.pid = pid
        db.session.commit()

    @staticmethod
    def init_app():
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)
        app.app_context().push()

    def spawn_subprocess_with_multiple_tasks(self, target_function, task_id, args=[], kwargs={}):
        self.init_app()
        if args:
            if not isinstance(args, list):
                raise flaskex.InternalServerError("The param 'args' should be list type!")
            p = multiprocessing.Process(target=target_function, args=args)
        elif kwargs:
            if not isinstance(kwargs, dict):
                raise flaskex.InternalServerError("The param 'kwargs' should be dict type!")
            p = multiprocessing.Process(target=target_function, kwargs=kwargs)
        else:
            p = multiprocessing.Process(target=target_function)
        self.check_process_amount()
        logging.info(f"Spawning a subprocess to do the task, task_id: {task_id}")
        p.start()
        self.store_pid_into_db(p.pid, self.task_model, task_id)
        logging.info(f"Subprocess spawn successfully, pid: {p.pid}")

    @staticmethod
    def check_process_amount(limit=30):
        logging.info("Checking process amount on server...")
        main_pid = os.getpid()
        main_process = psutil.Process(main_pid)
        child_processes = main_process.children(recursive=True)
        count = 0
        for c in child_processes:
            if 'python' in c.name().lower():
                count += 1
            if count >= limit:
                raise flaskex.InternalServerError(f"Processes on the server already reached limit ({limit})! Please wait for previous tasks to be done.")
        logging.info(f"Current processes running on server: {count}, limit is {limit}")

    def setup_loggers(self, task, log_dir, log_type, log_model, local_log_name=None, local_log_path=None):
        lg = DBLogging(logdir=log_dir, taskid=task.id, logtype=log_type, log_model=log_model)
        if not local_log_path and not local_log_name:
            raise flaskex.InternalServerError("Should provide one of 'local_log_name' or 'local_log_path'!")
        if not local_log_path or not os.path.exists(local_log_path):
            local_log_path = create_file(filepath=log_dir, filename=local_log_name)
            logging.warning(f"The previous log path doesn't exist, create a new one: {local_log_path}")

        logger = setup_common_logger(str(uuid.uuid4()), local_log_path)
        task.detaillogpath = local_log_path
        db.session.commit()
        lg.write_log(f"Log file created: {local_log_path}")
        return lg, logger

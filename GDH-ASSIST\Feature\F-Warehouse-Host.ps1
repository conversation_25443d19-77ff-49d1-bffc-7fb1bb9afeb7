function Confirm-WarehouseVcIloName(){
    param(
        [string] [Parameter(ParameterSetName = 'Cluster')] $Cluster
    )
    $Successful  = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Failed      = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Skipped     = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Results     = @{}
    $Vars        = Read-Var
    $VCAddress   = $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Name + `
                   "." + `
                   $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Domain
    $VCUsername  = $Vars.GstAccount.username
    $VCPassword  = $Vars.GstAccount.password
    $VCHosts     = (Rest-VC-List-Host -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    $iLOUsername = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Username
    $iLOPassword = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Password
    #Convert VC host to iLO, and filtering out the set of the configurable
    if ($Cluster) {
        $VCHosts = $VCHosts | Where-Object {$_.name -match $Cluster}
    }
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We have $($VCHosts.Length) hosts"
    $iLOs = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $VCHosts | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $DictSkipped = $using:Skipped 
        $Splits      = $_.name.split(".")
        $iLOAddress  = $Splits[0] + `
                        "ra." + `
                        $Splits[1] + `
                        "." + `
                        $Splits[2]
        $DictiLOs    = $using:iLOs
        
        $iLO = [PSCustomObject]@{
            'Address' = $iLOAddress
            'Session' = $(Rest-iLO-Get-Session -iLOAddress $iLOAddress `
                                               -iLOUsername $using:iLOUsername `
                                               -iLOPassword $using:iLOPassword)
        }
        if($iLO.Session) {
            $Systems = Rest-iLO-Get-Systems -iLOAddress $iLOAddress `
                                            -Session $iLO.Session
            $Managers = Rest-iLO-Get-Managers -iLOAddress $iLOAddress `
                                              -Session $iLO.Session
            if ($Systems -and $Managers) {
                if($Managers.FirmwareVersion -match 'iLO 5') {
                    $Gen = "5"
                    $OSVersion = $($Systems.oem.Hpe.HostOS.OsVersion -split (' '))[0]
                }
                if ($Managers.FirmwareVersion -match 'iLO 4') {
                    $Gen = "4"
                    $OSVersion = 'UNSUPPORTED'
                }
                $iLO | Add-Member -MemberType NoteProperty -Name 'Model' -Value $Systems.Model
                $iLO | Add-Member -MemberType NoteProperty -Name 'ServerName' -Value $Systems.HostName
                $iLO | Add-Member -MemberType NoteProperty -Name 'Gen' -Value $Gen
                $iLO | Add-Member -MemberType NoteProperty -Name 'Firmware' -Value $Managers.FirmwareVersion
                $iLO | Add-Member -MemberType NoteProperty -Name 'OSVersion' -Value $OSVersion
                $DictiLOs.Add($iLO)
            }else {
                $DictSkipped.Add($iLOAddress)
            }
        }else {
            $DictSkipped.Add($iLOAddress)
        }
    } -ThrottleLimit 300
    $iLOs | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $DictSuccessful = $using:Successful
        $DictFailed     = $using:Failed
        $Response       = & $("Rest-iLO-" + $_.Gen + "-Get-NetworkService") -iLOAddress $_.Address `
                                                                       -Session $_.Session
        if ($Response) {
            $Result = [PSCustomObject]@{
                'Address'     = $_.Address
                'Servername'  = $_.ServerName
                'Hostname'    = $Response.HostName
                'FQDN'        = $Response.FQDN
                'Model'       = $_.Model
                'Firmware'    = $_.Firmware
                'OSVersion'   = $_.OSVersion
            }
            $DictSuccessful.Add($Result)
        }else {
            $DictFailed.Add($_.Address)
        }
    } -ThrottleLimit 300
    $Results.Successful = $Successful
    $Results.Failed     = $Failed
    $Results.Skipped    = $Skipped
    return $Results
}
function Confirm-WarehouseVcIloLogin(){
    param(
        [string]
        [Parameter(ParameterSetName = 'Cluster')]
        $Cluster,
        [string]
        $iLOUsername,
        [string]
        $iLOPassword
    )
    $Successful     = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Failed         = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Results        = @{}
    $Vars           = Read-Var
    $VCAddress      = $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Name + `
                      "." + `
                      $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Domain
    $VCUsername     = $Vars.GstAccount.username
    $VCPassword     = $Vars.GstAccount.password
    $VCHosts        = (Rest-VC-List-Host -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    if (!($iLOUsername) -or !($iLOPassword)) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The credential is not assigned, we're now using the credential which stored in the vars"
        $iLOUsername    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Username
        $iLOPassword    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Password
    }
    #Convert VC host to iLO, and filtering out the set of the configurable
    if ($Cluster) {
        $VCHosts = $VCHosts | Where-Object {$_.name -match $Cluster}
    }
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We have $($VCHosts.Length) hosts"
    $VCHosts | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $DictSuccessful = $using:Successful
        $DictFailed     = $using:Failed
        $Splits         = $_.name.split(".")
        $iLOAddress     = $Splits[0] + `
                          "ra." + `
                          $Splits[1] + `
                          "." + `
                          $Splits[2]
        
        $iLO = [PSCustomObject]@{
            'Address' = $iLOAddress
            'Session' = $(Rest-iLO-Get-Session -iLOAddress $iLOAddress `
                                               -iLOUsername $using:iLOUsername `
                                               -iLOPassword $using:iLOPassword)
        }
        if($iLO.Session) {
            $DictSuccessful.Add($iLO)
        }else {
            $DictFailed.Add($iLOAddress)
        }
    } -ThrottleLimit 250
    $Results.Successful = $Successful
    $Results.Failed     = $Failed
    return $Results
}

function Set-WarehouseVcIloFqdn(){
    param(
        [string]
        [Parameter(ParameterSetName = 'Cluster')]
        $Cluster
    )
    $Successful     = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Failed         = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Skipped        = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Results        = @{}
    $Vars           = Read-Var
    $VCAddress      = $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Name + `
                      "." + `
                      $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Domain
    $VCUsername     = $Vars.GstAccount.username
    $VCPassword     = $Vars.GstAccount.password
    $VCHosts        = (Rest-VC-List-Host -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    $iLOUsername    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Username
    $iLOPassword    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Password
    if ($Cluster) {
        $VCHosts = $VCHosts | Where-Object {$_.name -match $Cluster}
    }
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We have $($VCHosts.Length) hosts"
    $iLOs = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $VCHosts | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $Splits      = $_.name.split(".")
        $iLOAddress  = $Splits[0] + `
                       "ra." + `
                       $Splits[1] + `
                       "." + `
                       $Splits[2]
        $DictiLOs    = $using:iLOs
        $DictSkipped = $using:Skipped
        
        $iLO = [PSCustomObject]@{
            'Host'    = $_.name
            'Address' = $iLOAddress
            'Session' = $(Rest-iLO-Get-Session -iLOAddress $iLOAddress `
                                               -iLOUsername $using:iLOUsername `
                                               -iLOPassword $using:iLOPassword)
        }
        if($iLO.Session) {
            $Managers = Rest-iLO-Get-Managers -iLOAddress $iLOAddress `
                                              -Session $iLO.Session
            if ($Managers) {
                if($Managers.FirmwareVersion -match 'iLO 5') {
                    $Body = [PSCustomObject]@{
                        'Oem' = [PSCustomObject]@{
                            'Hpe' = [PSCustomObject]@{
                                'DHCPv4' = [PSCustomObject]@{
                                    'UseDomainName' = $false
                                    'UseNTPServers' = $false
                                }
                                'DHCPv6' = [PSCustomObject]@{
                                    'UseDomainName' = $false
                                    'UseNTPServers' = $false
                                }
                                'HostName' = $Splits[0] + "ra"
                                'DomainName' = "ikea.com"
                            }
                        }
                    }
                }
                if ($Managers.FirmwareVersion -match 'iLO 4') {
                    $Body = [PSCustomObject]@{
                        'Oem' = [PSCustomObject]@{
                            'Hp' = [PSCustomObject]@{
                                'DHCPv4' = [PSCustomObject]@{
                                    'UseDomainName' = $false
                                    'UseNTPServers' = $false
                                }
                                'DHCPv6' = [PSCustomObject]@{
                                    'UseDomainName' = $false
                                    'UseNTPServers' = $false
                                }
                                'HostName' = $Splits[0] + "ra"
                                'DomainName' = "ikea.com"
                            }
                        }
                    }
                }
                $iLO | Add-Member -NotePropertyName 'Body' -NotePropertyValue $Body
                $DictiLOs.Add($iLO)
            }else {
                $DictSkipped.Add($iLOAddress)
            }
        }else {
            $DictSkipped.Add($iLOAddress)
        }
    } -ThrottleLimit 250
    $iLOs | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $iLOAddress     = $_.Address
        $Session        = $_.Session
        $Body           = $_.Body
        $DictSuccessful = $using:Successful
        $DictFailed     = $using:Failed
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're sending payload to $iLOAddress"
        $Response = Invoke-iLO-API -iLOAddress $iLOAddress `
                                   -RequestURI "Managers/1/EthernetInterfaces/1" `
                                   -Method PATCH `
                                   -Headers @{"X-Auth-Token" = $Session} `
                                   -Body $($Body | ConvertTo-Json -Depth 9) `
                                   -Type RestMethod
        if($Response) {
            $DictSuccessful.Add($iLOAddress)
        }else {
            $DictFailed.Add($iLOAddress)
        }
    } -ThrottleLimit 250
    $Results.Successful = $Successful
    $Results.Failed     = $Failed
    $Results.Skipped    = $Skipped
    return $Results
}

function Get-WarehouseVcHostBmc(){
    param(
        [string]
        $Cluster
    )
    $Results    = @{}
    $Successful = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Failed     = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Skipped    = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $VCHosts    = $(Get-WarehouseVcHost).Successful
    if ($Cluster) {
        $VCHosts = $VCHosts | Where-Object {$_.cluster -match $Cluster}
    }
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We have $($VCHosts.Count) hosts"
    if ($VCHosts) {
        $VCHosts | ForEach-Object -Parallel {
            Import-Module GDH-ASSIST
            $DictSuccessful = $using:Successful
            $DictFailed     = $using:Failed
            $DictSkipped    = $using:Skipped
            if (!($_.power_state) -or !('CONNECTED' -eq $_.connection_state)) {
                $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'UNREACHABLE'
                $DictSkipped.Add($_)
                Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "We skip the host $($_.host)"
                return
            }
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Try to login with the default password vmwarevmware1!"
            try {
                $VCHostSession = New-SSHSession -ComputerName $_.host `
                                                -Credential $(New-Object System.Management.Automation.PSCredential ('root', $('vmwarevmware1!' | ConvertTo-SecureString -AsPlainText -Force))) `
                                                -AcceptKey:$true `
                                                -ErrorAction Ignore
            }catch {
                Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "It is unable to login $($_.host) with the default password"
            }
            if ($VCHostSession) {
                $Prompt = Invoke-SSHCommand -SSHSession $VCHostSession -Command 'esxcli hardware ipmi bmc get'
                if ($Prompt) {
                    $BMC = [PSCustomObject]@{}
                    $Prompt.Output | % {
                        $Splits = $_.Split(": ")
                        $BMC | Add-Member -NotePropertyName $($Splits[0].Trim()) -NotePropertyValue $($Splits[1].Trim())
                    }
                    $_ | Add-Member -NotePropertyName 'bmc' -NotePropertyValue $BMC
                    $DictSuccessful.Add($_)
                }else {
                    $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'INVOKE FAILED'
                    $DictFailed.Add($_)
                }
                $VCHostSession.Disconnect()
                return
            }else {
                Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "It is unable to login $($_.host) with the default password, we'll re-try to get password from the Vault"
                $Secret = Get-SecretForWtp -Cluster $($_.cluster) -Display:$true
                if (!($Secret)) {
                    $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'VAULT FAULT'
                    $DictFailed.Add($_)
                    Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The secret of the cluster $($_.cluster) does not exist"
                    return
                }
                try {
                    $VCHostSession = New-SSHSession -ComputerName $_.host `
                                                    -Credential $(New-Object System.Management.Automation.PSCredential ($Secret.Username, $($Secret.Password | ConvertTo-SecureString -AsPlainText -Force))) `
                                                    -AcceptKey:$true `
                                                    -ErrorAction Ignore
                }
                catch {
                    $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'UNKNOWN'
                    $DictFailed.Add($_)
                    Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message $_
                    return
                }
                if ($VCHostSession) {
                    $Prompt = Invoke-SSHCommand -SSHSession $VCHostSession -Command 'esxcli hardware ipmi bmc get'
                    if ($Prompt) {
                        $BMC = [PSCustomObject]@{}
                        $Prompt.Output | % {
                            $Splits = $_.Split(": ")
                            $BMC | Add-Member -NotePropertyName $($Splits[0].Trim()) -NotePropertyValue $($Splits[1].Trim())
                        }
                        $_ | Add-Member -NotePropertyName 'bmc' -NotePropertyValue $BMC
                        $DictSuccessful.Add($_)
                    }else {
                        $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'INVOKE FAILED'
                        $DictFailed.Add($_)
                    }
                    $VCHostSession.Disconnect()
                }else {
                    $_ | Add-Member -NotePropertyName 'cause' -NotePropertyValue 'LOGIN FAILED'
                    $DictFailed.Add($_)
                }
            }
        } -ThrottleLimit 300
    }
    $Results.Successful = $Successful
    $Results.Failed     = $Failed
    $Results.Skipped    = $Skipped
    return $Results
}
function Reset-WarehouseVcHostBmc(){
    param(
        [string]
        $Cluster,
        [string]
        $iLOUsername,
        [string]
        $iLOPassword
    )
    $Successful     = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Failed         = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Results        = @{}
    $Vars           = Read-Var
    $VCAddress      = $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Name + `
                      "." + `
                      $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Domain
    $VCUsername     = $Vars.GstAccount.username
    $VCPassword     = $Vars.GstAccount.password
    $VCHosts        = (Rest-VC-List-Host -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    if (!($iLOUsername) -or !($iLOPassword)) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The credential is not assigned, we're now using the credential which stored in the vars"
        $iLOUsername    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Username
        $iLOPassword    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Password
    }
    #Convert VC host to iLO, and filtering out the set of the configurable
    if ($Cluster) {
        $VCHosts = $VCHosts | Where-Object {$_.name -match $Cluster}
    }
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We have $($VCHosts.Length) hosts"
    $VCHosts | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $DictSuccessful = $using:Successful
        $DictFailed     = $using:Failed
        $Splits         = $_.name.split(".")
        $iLOAddress     = $Splits[0] + `
                          "ra." + `
                          $Splits[1] + `
                          "." + `
                          $Splits[2]
        if (Ssh-iLO-Reset-BMC -iLOAddress $iLOAddress -iLOUsername $using:iLOUsername -iLOPassword $using:iLOPassword) {
            $DictSuccessful.Add($iLOAddress)
        }else {
            $DictFailed.Add($iLOAddress)
        }
    } -ThrottleLimit 250
    $Results.Successful = $Successful
    $Results.Failed     = $Failed
    return $Results
}
function Set-WarehouseVcIloDnsServer(){
    param(
        [string]
        [Parameter(ParameterSetName = 'Cluster')]
        $Cluster
    )
    $Successful     = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Failed         = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Skipped        = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $Results        = @{}
    $Vars           = Read-Var
    $VCAddress      = $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Name + `
                      "." + `
                      $Vars.Infrastructure.Warehouse.ESXI.VCenter.PROD.Domain
    $VCUsername     = $Vars.GstAccount.username
    $VCPassword     = $Vars.GstAccount.password
    $VCHosts        = (Rest-VC-List-Host -VCAddress $VCAddress -VCUsername $VCUsername -VCPassword $VCPassword).value
    $iLOUsername    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Username
    $iLOPassword    = $Vars.Password_Vault.Warehouse.Esxihost.ILO.Password
    if ($Cluster) {
        $VCHosts = $VCHosts | Where-Object {$_.name -match $Cluster}
    }
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We have $($VCHosts.Length) hosts"
    $iLOs = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
    $VCHosts | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $Splits      = $_.name.split(".")
        $iLOAddress  = $Splits[0] + `
                       "ra." + `
                       $Splits[1] + `
                       "." + `
                       $Splits[2]
        $DictiLOs    = $using:iLOs
        $DictSkipped = $using:Skipped
        
        $iLO = [PSCustomObject]@{
            'Host'    = $_.name
            'Address' = $iLOAddress
            'Session' = $(Rest-iLO-Get-Session -iLOAddress $iLOAddress `
                                               -iLOUsername $using:iLOUsername `
                                               -iLOPassword $using:iLOPassword)
        }
        if($iLO.Session) {
            $Managers = Rest-iLO-Get-Managers -iLOAddress $iLOAddress `
                                              -Session $iLO.Session
            if ($Managers) {
				$NetworkRegionalSettings = Get-NetworkRegionalSettings -Object $iLOAddress
                if($Managers.FirmwareVersion -match 'iLO 5') {
                    $Body = [PSCustomObject]@{
                        'Oem' = [PSCustomObject]@{
                            'Hpe' = [PSCustomObject]@{
                                'IPv4' = [PSCustomObject]@{
                                    'DNSServers' = @(
										"$($NetworkRegionalSettings.Primary_DNS)",
										"$($NetworkRegionalSettings.Secondary_DNS)",
										"$($NetworkRegionalSettings.Tertiary_DNS)"
									)
                                }
                            }
                        }
                    }
                }
                if ($Managers.FirmwareVersion -match 'iLO 4') {
                    $Body = [PSCustomObject]@{
                        'Oem' = [PSCustomObject]@{
                            'Hp' = [PSCustomObject]@{
                                'IPv4' = [PSCustomObject]@{
                                    'DNSServers' = @(
										"$($NetworkRegionalSettings.Primary_DNS)",
										"$($NetworkRegionalSettings.Secondary_DNS)",
										"$($NetworkRegionalSettings.Tertiary_DNS)"
									)
                                }
                            }
                        }
                    }
                }
                $iLO | Add-Member -NotePropertyName 'Body' -NotePropertyValue $Body
                $DictiLOs.Add($iLO)
            }else {
                $DictSkipped.Add($iLOAddress)
            }
        }else {
            $DictSkipped.Add($iLOAddress)
        }
    } -ThrottleLimit 250
    $iLOs | ForEach-Object -Parallel {
        Import-Module GDH-ASSIST
        $iLOAddress     = $_.Address
        $Session        = $_.Session
        $Body           = $_.Body
        $DictSuccessful = $using:Successful
        $DictFailed     = $using:Failed
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're sending payload to $iLOAddress"
        $Response = Invoke-iLO-API -iLOAddress $iLOAddress `
                                   -RequestURI "Managers/1/EthernetInterfaces/1" `
                                   -Method PATCH `
                                   -Headers @{"X-Auth-Token" = $Session} `
                                   -Body $($Body | ConvertTo-Json -Depth 9) `
                                   -Type RestMethod
        if($Response) {
            $DictSuccessful.Add($iLOAddress)
        }else {
            $DictFailed.Add($iLOAddress)
        }
    } -ThrottleLimit 250
    $Results.Successful = $Successful
    $Results.Failed     = $Failed
    $Results.Skipped    = $Skipped
    return $Results
}
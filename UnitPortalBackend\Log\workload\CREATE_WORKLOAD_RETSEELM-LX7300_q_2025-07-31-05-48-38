2025-07-31 13:48:38,550 INFO Start to run the task.
2025-07-31 13:48:38,588 INFO ****************************************************************************************************
2025-07-31 13:48:38,588 INFO *                                                                                                  *
2025-07-31 13:48:38,588 INFO *                                        Check VM existence                                        *
2025-07-31 13:48:38,588 INFO *                                                                                                  *
2025-07-31 13:48:38,599 INFO ****************************************************************************************************
2025-07-31 13:48:38,635 INFO Checking if vm already exists in the PE cluster.
2025-07-31 13:48:38,635 INFO Checking if RETSEELM-LX7300 existed in PE.
2025-07-31 13:48:38,635 INFO Getting VM list from RETSEELM-NXC000.
2025-07-31 13:48:38,635 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms?sortCriteria=vm_name&searchString=RETSEELM-LX7300, method: GET, headers: None
2025-07-31 13:48:38,635 INFO params: None
2025-07-31 13:48:38,636 INFO User: <EMAIL>
2025-07-31 13:48:38,636 INFO payload: None
2025-07-31 13:48:38,636 INFO files: None
2025-07-31 13:48:38,636 INFO timeout: 30
2025-07-31 13:48:39,883 WARNING Response content: b'<!doctype html><html lang="en"><head><title>HTTP Status 401 \xe2\x80\x93 Unauthorized</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 401 \xe2\x80\x93 Unauthorized</h1></body></html>'
2025-07-31 13:48:39,883 WARNING API response is not ok, going to do the 2 retry...
2025-07-31 13:48:41,733 INFO Got the VM list from RETSEELM-NXC000.
2025-07-31 13:48:41,734 INFO RETSEELM-LX7300 doesn't exist in RETSEELM-NXC000.
2025-07-31 13:48:41,773 INFO RETSEELM-LX7300 not exists in Cluster RETSEELM-NXC000.IKEAD2.COM, move on...
2025-07-31 13:48:41,812 INFO Checking if vm already exists in the inventory AD/Tower.
2025-07-31 13:48:43,266 INFO FQDN 'RETSEELM-LX7300.IKEAD2.COM' not exists in Tower Inventory, continue...
2025-07-31 13:48:43,304 INFO Checking if vm already exists in IPAM.
2025-07-31 13:48:43,331 INFO Start to check if RETSEELM-LX7300.IKEAD2.COM existed in IPAM...
2025-07-31 13:48:43,331 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-LX7300.IKEAD2.COM', method: GET, headers: None
2025-07-31 13:48:43,331 INFO params: None
2025-07-31 13:48:43,331 INFO User: <EMAIL>
2025-07-31 13:48:43,331 INFO payload: None
2025-07-31 13:48:43,331 INFO files: None
2025-07-31 13:48:43,331 INFO timeout: 30
2025-07-31 13:48:44,402 INFO 'RETSEELM-LX7300' not exists in IPAM, continue...
2025-07-31 13:48:44,437 INFO ****************************************************************************************************
2025-07-31 13:48:44,437 INFO *                                                                                                  *
2025-07-31 13:48:44,437 INFO *                                              Sizing                                              *
2025-07-31 13:48:44,437 INFO *                                                                                                  *
2025-07-31 13:48:44,438 INFO ****************************************************************************************************
2025-07-31 13:48:44,473 INFO Sizing, check if cluster has enough capacity for this VM.
2025-07-31 13:48:44,473 INFO Get a list of existing hosts from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:48:44,474 INFO Calling /hosts through v1 API using GET method
2025-07-31 13:48:44,474 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-07-31 13:48:44,474 INFO params: None
2025-07-31 13:48:44,474 INFO User: <EMAIL>
2025-07-31 13:48:44,474 INFO payload: None
2025-07-31 13:48:44,474 INFO files: None
2025-07-31 13:48:44,474 INFO timeout: None
2025-07-31 13:48:46,288 INFO Get cluster details from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:48:46,289 INFO Calling /cluster through v1 API using GET method
2025-07-31 13:48:46,289 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster, method: GET, headers: None
2025-07-31 13:48:46,289 INFO params: None
2025-07-31 13:48:46,289 INFO User: <EMAIL>
2025-07-31 13:48:46,289 INFO payload: None
2025-07-31 13:48:46,289 INFO files: None
2025-07-31 13:48:46,289 INFO timeout: None
2025-07-31 13:48:48,065 INFO Get a list of existing user VMs from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:48:48,065 INFO Calling /vms through v2 API using GET method
2025-07-31 13:48:48,065 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms, method: GET, headers: None
2025-07-31 13:48:48,065 INFO params: None
2025-07-31 13:48:48,065 INFO User: <EMAIL>
2025-07-31 13:48:48,066 INFO payload: None
2025-07-31 13:48:48,066 INFO files: None
2025-07-31 13:48:48,066 INFO timeout: None
2025-07-31 13:48:49,932 INFO The cluster is a 3 node(s) cluster
2025-07-31 13:48:49,933 INFO Fetching capacity from node RETSEELM-NX7001
2025-07-31 13:48:49,933 INFO Storage of node RETSEELM-NX7001 is 44.68 TiB
2025-07-31 13:48:49,933 INFO Number of cores on node RETSEELM-NX7001 is 20
2025-07-31 13:48:49,933 INFO Memory install on node RETSEELM-NX7001 is 377.08 GiB
2025-07-31 13:48:49,933 INFO Fetching capacity from node RETSEELM-NX7002
2025-07-31 13:48:49,933 INFO Storage of node RETSEELM-NX7002 is 44.68 TiB
2025-07-31 13:48:49,933 INFO Number of cores on node RETSEELM-NX7002 is 20
2025-07-31 13:48:49,933 INFO Memory install on node RETSEELM-NX7002 is 377.08 GiB
2025-07-31 13:48:49,933 INFO Fetching capacity from node RETSEELM-NX7003
2025-07-31 13:48:49,933 INFO Storage of node RETSEELM-NX7003 is 44.68 TiB
2025-07-31 13:48:49,933 INFO Number of cores on node RETSEELM-NX7003 is 20
2025-07-31 13:48:49,933 INFO Memory install on node RETSEELM-NX7003 is 345.58 GiB
2025-07-31 13:48:49,934 INFO Number of nodes in this cluster is 3
2025-07-31 13:48:49,934 INFO Total storage capacity on this cluster is 134.04 TiB
2025-07-31 13:48:49,934 INFO total number of CPU cores on cluster is 60
2025-07-31 13:48:49,934 INFO Total memory capacity on this cluster is 1099.74 GiB
2025-07-31 13:48:49,934 INFO Resilient storage capacity on this cluster is 84.************** TiB
2025-07-31 13:48:49,934 INFO Number of resilient physical CPU cores is 40
2025-07-31 13:48:49,934 INFO Number of resilient physical CPU cores accounting CVMs is 34
2025-07-31 13:48:49,934 INFO Number of resilient virtual CPU cores (assuming 1:4 ratio) is 136
2025-07-31 13:48:49,934 INFO Resilient memory capacity on this cluster is 722.************* GiB
2025-07-31 13:48:49,934 INFO Resilient memory capacity accounting CVMs on this cluster is 658.************* GiB
2025-07-31 13:48:49,934 INFO Utilized storage of cluster is 0.95 TiB
2025-07-31 13:48:49,934 INFO There are 5 VMs on this cluster
2025-07-31 13:48:49,934 INFO Number of virtual cores used by 5 VMs that are powered on is 62
2025-07-31 13:48:49,934 INFO Memory used by 5 VMs that are powered on is 248.0 GiB
2025-07-31 13:48:49,935 INFO Available storage for new VM provisioning is 83.************** TiB
2025-07-31 13:48:49,935 INFO Available vCPU cores for new VM provisioning is 74
2025-07-31 13:48:49,935 INFO Available memory for new VM provisioning is 410.************* GiB
2025-07-31 13:48:49,977 INFO ****************************************************************************************************
2025-07-31 13:48:49,978 INFO *                                                                                                  *
2025-07-31 13:48:49,978 INFO *                                Checking workload network on NTX.                                 *
2025-07-31 13:48:49,978 INFO *                                                                                                  *
2025-07-31 13:48:49,978 INFO ****************************************************************************************************
2025-07-31 13:48:50,045 INFO Checking PE network by VlanId=793
2025-07-31 13:48:50,045 INFO Getting network list from RETSEELM-NXC000
2025-07-31 13:48:50,045 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/networks, method: GET, headers: None
2025-07-31 13:48:50,045 INFO params: None
2025-07-31 13:48:50,045 INFO User: <EMAIL>
2025-07-31 13:48:50,045 INFO payload: None
2025-07-31 13:48:50,046 INFO files: None
2025-07-31 13:48:50,046 INFO timeout: 30
2025-07-31 13:48:51,875 INFO Got the network list from RETSEELM-NXC000.
2025-07-31 13:48:51,875 INFO Vlan 793 is found
2025-07-31 13:48:51,915 INFO The network is found, the UUID is 9531e569-3bec-4d92-8581-6209bea747db
2025-07-31 13:48:51,953 INFO ****************************************************************************************************
2025-07-31 13:48:51,953 INFO *                                                                                                  *
2025-07-31 13:48:51,953 INFO *                                           Check image                                            *
2025-07-31 13:48:51,953 INFO *                                                                                                  *
2025-07-31 13:48:51,954 INFO ****************************************************************************************************
2025-07-31 13:48:56,746 INFO Verifying workload image existence in DB and on cluster.
2025-07-31 13:51:31,250 INFO Checking if RHELx_AUTO image
2025-07-31 13:51:31,250 INFO Validating image 'RHEL9.2-RETSEELM-NXC000' existence in PE 'RETSEELM-NXC000'...
2025-07-31 13:51:31,250 INFO Start to find the image RHEL9.2-RETSEELM-NXC000 from RETSEELM-NXC000
2025-07-31 13:51:31,250 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/images, method: GET, headers: None
2025-07-31 13:51:31,250 INFO params: None
2025-07-31 13:51:31,251 INFO User: <EMAIL>
2025-07-31 13:51:31,251 INFO payload: None
2025-07-31 13:51:31,251 INFO files: None
2025-07-31 13:51:31,251 INFO timeout: 30
2025-07-31 13:51:33,099 INFO Getting image list from RETSEELM-NXC000
2025-07-31 13:51:33,099 INFO Got the image list from RETSEELM-NXC000.
2025-07-31 13:51:33,139 INFO Image doesn't exist on PE, will upload.
2025-07-31 13:51:33,140 INFO Start to upload the image to PE...
2025-07-31 13:51:33,140 INFO Prepare to upload image RHEL9.2 to RETSEELM-NXC000
2025-07-31 13:51:33,140 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/containers, method: GET, headers: None
2025-07-31 13:51:33,140 INFO params: None
2025-07-31 13:51:33,140 INFO User: <EMAIL>
2025-07-31 13:51:33,140 INFO payload: None
2025-07-31 13:51:33,140 INFO files: None
2025-07-31 13:51:33,140 INFO timeout: 30
2025-07-31 13:51:34,863 INFO Getting container list from RETSEELM-NXC000
2025-07-31 13:51:34,863 INFO Got the container list from RETSEELM-NXC000.
2025-07-31 13:51:34,863 INFO The container SelfServiceContainer is found
2025-07-31 13:51:34,863 INFO The container SelfServiceContainer is located, its UUID is 62cf1853-5f93-41e0-a204-99fb9f7b4b45
2025-07-31 13:51:36,535 INFO Get the image RHEL9.2-RETSEELM-NXC000 from D:\Image\rhel-92-401006d-20240601T053114
2025-07-31 13:51:36,536 INFO Upload the image RHEL9.2-RETSEELM-NXC000 to RETSEELM-NXC000
2025-07-31 13:51:36,536 INFO Uploading image RHEL9.2-RETSEELM-NXC000 to RETSEELM-NXC000
2025-07-31 13:51:36,536 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/api/nutanix/v0.8/images, method: POST, headers: None
2025-07-31 13:51:36,536 INFO params: None
2025-07-31 13:51:36,536 INFO User: <EMAIL>
2025-07-31 13:51:36,536 INFO payload: {'name': 'RHEL9.2-RETSEELM-NXC000', 'annotation': 'RHEL9.2-RETSEELM-NXC000', 'imageType': 'DISK_IMAGE', 'imageImportSpec': {'containerUuid': '62cf1853-5f93-41e0-a204-99fb9f7b4b45', 'url': 'D:\\Image\\rhel-92-401006d-20240601T053114'}}
2025-07-31 13:51:36,536 INFO files: None
2025-07-31 13:51:36,537 INFO timeout: 30
2025-07-31 13:51:38,321 INFO It's uploading the image, the task id is 6d36921d-760e-41ec-be36-6d1695b5c467
2025-07-31 13:51:38,322 INFO Get task status attempting 1/120...
2025-07-31 13:51:38,322 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/6d36921d-760e-41ec-be36-6d1695b5c467, method: GET, headers: None
2025-07-31 13:51:38,322 INFO params: None
2025-07-31 13:51:38,322 INFO User: <EMAIL>
2025-07-31 13:51:38,322 INFO payload: None
2025-07-31 13:51:38,322 INFO files: None
2025-07-31 13:51:38,323 INFO timeout: 30
2025-07-31 13:51:40,074 INFO Task status: Failed
2025-07-31 13:51:40,178 ERROR Task failed. Detail: ['Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\base_up_task.py", line 88, in start_task\n    self.task_process()\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\workload\\workload.py", line 187, in task_process\n    self.get_wl_image(_pc, _pe)\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\workload\\workload.py", line 284, in get_wl_image\n    self.upload_wl_image_to_pe(_pe=_pe)  # Upload image to PE\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\workload\\workload.py", line 256, in upload_wl_image_to_pe\n    raise ImageUploadFailed(in_progress=True, image_name=image_pe_name, original_err=msg)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', "TypeError: ImageUploadFailed.__init__() missing 1 required positional argument: 'init'\n"]

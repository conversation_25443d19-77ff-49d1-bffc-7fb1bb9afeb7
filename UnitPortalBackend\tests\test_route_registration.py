# pylint: skip-file
import os
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pytest
from flask import Flask
from sqlalchemy import inspect


# Create a mock that can handle SQLAlchemy model inspection
class MockSQLAlchemy(Mock):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.Model = type("Model", (), {"__mapper__": MagicMock(attrs=[]), "__table__": MagicMock(columns=[])})
        self.Column = Mock()
        self.Integer = Mock()
        self.String = Mock()
        self.Boolean = Mock()
        self.inspect = MagicMock(return_value=MagicMock(columns=[]))

    def __call__(self, *args, **kwargs):
        return self


# Patch DBConfig and create_engine before any imports that might use them
def func_patch_db_config(self):
    return "mssql+pyodbc://test_user:test_pw@test_host/test_db?driver=ODBC+Driver+17+for+SQL+Server"


with patch("business.generic.commonfunc.DBConfig.__call__", func_patch_db_config), patch(
    "sqlalchemy.create_engine", Mock()
), patch("models.database.db", MockSQLAlchemy()), patch("sqlalchemy.inspect", MockSQLAlchemy().inspect):
    from business.restful.blueprint import api


@pytest.fixture
def flask_app():
    """Create a Flask app with the API registered."""
    app = Flask(__name__)
    app.register_blueprint(api.blueprint)
    return app


@pytest.fixture
def test_client(flask_app):
    """Create a test client for the app."""
    return flask_app.test_client()


# An exhaust list of all apis avaliable from backend
API_LIST = [
    "/api/v1/automation/maintenance",
    "/api/v1/automation/cluster_list_move",
    "/api/v1/automation/dsc_pe_availability",
    "/api/v1/compen/add_gain_compen",
    "/api/v1/compen/add_use_compen",
    "/api/v1/compen/delete_gain_compen",
    "/api/v1/compen/delete_use_compen",
    "/api/v1/compen/get_memo_id",
    "/api/v1/compen/list_avaliable_compen",
    "/api/v1/compen/list_gain_compen",
    "/api/v1/compen/list_use_compen",
    "/api/v1/compen/update_gain_compen",
    "/api/v1/compen/update_use_compen",
    "/api/v1/dashboard-diagram",
    "/api/v1/download",
    "/api/v1/group",
    "/api/v1/groups/role",
    "/api/v1/incident_analysis_custom",
    "/api/v1/incident_sheet",
    "/api/v1/incident_sheet_download",
    "/api/v1/incident-handler",
    "/api/v1/incident-handler/active-incidents",
    "/api/v1/incident-handler/resolved-incidents-today",
    "/api/v1/incident-handler/total-resolved-incidents",
    "/api/v1/incident-handler/unassigned-incidents",
    "/api/v1/incident-handler/unit-portal-incident-data",
    "/api/v1/login",
    "/api/v1/ntx/addvmpd",
    "/api/v1/ntx/automation/auto_maintenance_history",
    "/api/v1/ntx/automation/auto_maintenance_jobs",
    "/api/v1/ntx/automation/auto_maintenance_lock",
    "/api/v1/ntx/automation/auto_maintenance_lock/1",
    "/api/v1/ntx/automation/auto_maintenance_log",
    "/api/v1/ntx/automation/auto_maintenance",
    "/api/v1/ntx/automation/auto_maintenance/abort",
    "/api/v1/ntx/automation/data_fetch",
    "/api/v1/ntx/automation/disablevi",
    "/api/v1/ntx/automation/lcm/aos/task/list/warehouse",
    "/api/v1/ntx/automation/lcm/aos/tasks",
    "/api/v1/ntx/automation/lcm/aos/upgrade",
    "/api/v1/ntx/automation/lcm/log/download",
    "/api/v1/ntx/automation/lcm/spp/task/list/retail",
    "/api/v1/ntx/automation/lcm/spp/task/status",
    "/api/v1/ntx/automation/lcm/spp/tasks",
    "/api/v1/ntx/automation/lcm/spp/upgrade",
    "/api/v1/ntx/automation/lcm/seamless_lcm/planned_pe",
    "/api/v1/ntx/automation/lcm/seamless_lcm/plans",
    "/api/v1/ntx/automation/lcm/seamless_lcm/plans/1",
    "/api/v1/ntx/automation/lcm/seamless_lcm/target_version",
    "/api/v1/ntx/automation/metro_add_disk",
    "/api/v1/ntx/automation/metro_add_iscsi_client",
    "/api/v1/ntx/automation/metro_list_vgs",
    "/api/v1/ntx/automation/metro_vg",
    "/api/v1/ntx/automation/new_cluster_task/warehouse/1",
    "/api/v1/ntx/automation/new_cluster/abort/warehouse/1",
    "/api/v1/ntx/automation/new_cluster/warehouse",
    "/api/v1/ntx/automation/new_cluster/log/warehouse/1",
    "/api/v1/ntx/certasks",
    "/api/v1/ntx/cluster/vm/list",
    "/api/v1/ntx/dsctasks",
    "/api/v1/ntx/getvlan",
    "/api/v1/ntx/host",
    "/api/v1/ntx/move_clusterlist",
    "/api/v1/ntx/move_creation_wh",
    "/api/v1/ntx/move_creation",
    "/api/v1/ntx/move_cutover",
    "/api/v1/ntx/move_pre",
    "/api/v1/ntx/move_tasks",
    "/api/v1/ntx/move_vm",
    "/api/v1/ntx/move_whclusterlist",
    "/api/v1/ntx/pe/correspondence",
    "/api/v1/ntx/pe/list",
    "/api/v1/ntx/pe/vm/list",
    "/api/v1/ntx/pm/abort",
    "/api/v1/ntx/pm/create",
    "/api/v1/ntx/pm/delete",
    "/api/v1/ntx/pm/info/1",
    "/api/v1/ntx/pm/update",
    "/api/v1/ntx/prism",
    "/api/v1/ntx/prism/list",
    "/api/v1/ntx/prism/refresh",
    "/api/v1/ntx/pwerotate/tasks",
    "/api/v1/ntx/vm/list",
    "/api/v1/ntx/vmacp",
    "/api/v1/ntx/workload",
    "/api/v1/ntx/workload/cleanup",
    "/api/v1/ntx/workload/info",
    "/api/v1/ntx/workload/log/download",
    "/api/v1/ntx/workload/supported_packages",
    "/api/v1/ntx/workload/task/create",
    "/api/v1/ntx/workload/task/list/create",
    "/api/v1/ntx/workload/task/log",
    "/api/v1/ntx/workload/template",
    "/api/v1/ntx/workload/template/admin",
    "/api/v1/ntx/workload/template/admin/id",
    "/api/v1/ntx/workload/template/list",
    "/api/v1/pm/ntx/log/download",
    "/api/v1/pm/sli/create",
    "/api/v1/pm/sli/log/download",
    "/api/v1/pmtasklist",
    "/api/v1/pm/task/log",
    "/api/v1/pmtasks",
    "/api/v1/rbac_role",
    "/api/v1/rbac_role/1",
    "/api/v1/role",
    "/api/v1/role/list",
    "/api/v1/sa",
    "/api/v1/sa/1",
    "/api/v1/sa/list",
    "/api/v1/simplivity/pm/poweroff",
    "/api/v1/simplivity/vm/list",
    "/api/v1/siteexistence_db",
    "/api/v1/siteexistence",
    "/api/v1/sli/cluster/list",
    "/api/v1/sli/correspondence",
    "/api/v1/sli/host",
    "/api/v1/sli/pm/abort",
    "/api/v1/sli/pm/delete",
    "/api/v1/sli/pm/info/1",
    "/api/v1/sli/pm/update",
    "/api/v1/pm/task/log",
    "/api/v1/upwidgets/taskscounter",
    "/api/v1/sli/vc/list",
    "/api/v1/sli/vms",
    "/api/v1/sli/vms/nt",
    "/api/v1/slipmtasklist",
    "/api/v1/slipmtasks",
    "/api/v1/statistic/tasks",
    "/api/v1/upwidgets/apiutilization",
    "/api/v1/upwidgets/apiutilizationlog",
    "/api/v1/upwidgets/feedback",
    "/api/v1/upwidgets/webpage",
    "/api/v1/upwidgets/taskscounter",
    "/api/v1/user",
    "/api/v1/user/1",
    "/api/v1/user/list",
    "/api/v1/user/role",
    "/api/v1/vault/data",
    "/api/v1/wh/ntx/move_brief_log",
    "/api/v1/wh/ntx/move_cutover",
    "/api/v1/wh/ntx/move_pre",
    "/api/v1/wh/vms/list",
    "/api/v1/ntx/pe",
    "/api/v1/ntx/unlock_account",
    '/api/v1/ntx/pe/cvmlist',
    '/api/v1/ntx/unlock_account/log/download',
    '/api/v1/ntx/unlock_account/logs',
    '/api/v1/ntx/unlock_account/tasks',
    '/api/v1/benchmark/linked_cluster',
    '/api/v1/benchmark/backbone',
    '/api/v1/benchmark/list_with_vlan_config',
    '/api/v1/benchmark/brief_list',
    '/api/v1/benchmark/list',
    '/api/v1/benchmark/1',
    '/api/v1/benchmark/sub_bmk_label',
    '/api/v1/benchmark/all_cluster',
    '/api/v1/benchmark/update/1',
    '/api/v1/benchmark/sub_bmk_backbone',
    '/api/v1/benchmark/sub_bmk_detail',
    '/api/v1/benchmark/sub_bmk/update',
    '/api/v1/benchmark/sub_bmk/create',
    '/api/v1/benchmark/sub_bmk/delete'
]


def url_matches_pattern(url, pattern):
    """Check if a URL matches a URL pattern with parameters."""
    url_parts = url.strip().strip("/").split("/")
    pattern_parts = pattern.strip().strip("/").split("/")

    if len(url_parts) != len(pattern_parts):
        return False

    for url_part, pattern_part in zip(url_parts, pattern_parts):
        if pattern_part.strip().startswith("<") and pattern_part.strip().endswith(">"):
            # If parameter have specified type
            if ":" in pattern_part.strip():
                value_type = pattern_part.strip()[1:-1].split(":")[0]
                # type corresponding test
                type_map = {
                    "int": lambda x: x.isdigit(),
                    "str": lambda: True,
                }
                value_type = type_map[value_type]

                if value_type(url_part):
                    continue
            # No type? any value matches
            else:
                continue
        if url_part != pattern_part:
            return False
    return True


def helper_dump_registered_routes(registered_routes) -> str:
    routes = sorted([f"{route.rule}" for route in registered_routes])
    return "[\n" + "\n".join(routes) + "\n]"


def test_regression_all_endpoints_registered(test_client):
    """Test that all registered endpoints are properly registered and accept correct methods."""
    import business.restful  # pylint: disable=C0415, W0611

    # Get all registered API routes
    registered_routes = []
    print("\nRegistered routes:")
    for rule in test_client.application.url_map.iter_rules():
        print(f"  {rule.endpoint}: {rule.rule} (methods: {rule.methods})")
        if not rule.endpoint == "static":
            registered_routes.append(rule)

    for test_url in API_LIST:
        # Find matching route pattern
        matching_route = None
        for route in registered_routes:
            # print(f"  Comparing with route: {route.rule}")
            if url_matches_pattern(test_url, route.rule):
                matching_route = route
                break
        else:
            print(
                f"No matching route found for {test_url},",
                f"registered routes: {helper_dump_registered_routes(registered_routes)}",
            )

        assert matching_route is not None, f"No matching route found for {test_url}"


def test_regression_no_more_than_listed_endpoints_registered(test_client):
    """Test no more endpoints than listed are registered."""
    import business.restful  # pylint: disable=C0415, W0611

    # Get all registered API routes
    registered_routes = []
    for rule in test_client.application.url_map.iter_rules():
        if not rule.endpoint == "static":
            registered_routes.append(rule)

    for test_url in API_LIST:
        # Find matching route pattern
        for i, route in enumerate(registered_routes):
            if url_matches_pattern(test_url, route.rule):
                registered_routes.pop(i)
                break

    if len(registered_routes) > 0:
        pytest.fail(f"""
Found following endpoints not in API_LIST:
{helper_dump_registered_routes(registered_routes)}
Forgot to add them?
""")

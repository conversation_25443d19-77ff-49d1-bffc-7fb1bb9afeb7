import logging
import sys
import traceback

import sqlalchemy
from flask import jsonify
import werkzeug.exceptions as flaskex
from datetime import datetime
import static.SETTINGS as SETTING
from business.authentication.authentication import ServiceAccount, Vault
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.distributedhosting.nutanix.nutanix import PrismElement
from business.distributedhosting.nutanix.protection_domain import ProtectionDomain
from business.distributedhosting.nutanix.task_status import TaskStatus
from business.distributedhosting.nutanix.workload.workload_exceptions import ThorsDeleteComputerNotFound
from business.generic.thors_hammer_api import ThorsHammerAPI
from business.generic.base_up_exception import VaultGetSecretFailed
from business.generic.ipam import Ipam
from business.generic.linux import Ansible, Tower
from models.database import db
from models.ntx_models import ModelPrismCentral, ModelPrismElement, ModelRetailNutanixVM
from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
from models.workload_models import ModelWorkloadTask, ModelWorkloadTaskSchema, ModelWorkloadTaskLog, \
    ModelWorkloadTaskLogSchema


class DeleteWorkloadTask(BaseUpTask):
    LOG_DIR = SETTING.WORKLOAD_LOG_PATH
    LOG_TYPE = "NTX_WL"
    TASK_TYPE = "DELETE_WORKLOAD"

    def __init__(self, payload):
        super().__init__(ModelWorkloadTask, ModelWorkloadTaskSchema, ModelWorkloadTaskLog, ModelWorkloadTaskLogSchema, payload)
        self.vm_name = payload["name"]
        self.task_duplicated_kwargs = {
            "task_type": self.TASK_TYPE, "name": self.vm_name
        }
        self.task_info = {
            "task_type": self.TASK_TYPE, "name": self.vm_name, "pe": payload["pe"]
        }

    def init_variables(self):
        try:
            pe = ModelPrismElement.query.filter_by(fqdn=self.task.pe).one()
            pc_info = ModelPrismCentral.query.filter_by(fqdn=pe.prism).one()
            res, data = Vault(tier=pc_info.tier).get_secret(f"{pe.name}/Site_Pe_Admin")
            if not res:
                raise VaultGetSecretFailed(f"{pe.name}/Site_Pe_Admin")
            self.ntx_sa = {"username": data["username"], "password": data["secret"]}
            # self.ntx_sa = ServiceAccount(usage=pc_info.service_account).get_service_account()
        except sqlalchemy.exc.NoResultFound:
            try:
                pe = ModelWarehousePrismElement.query.filter_by(fqdn=self.task.pe).one()
                pc_info = ModelWarehousePrismCentral.query.filter_by(fqdn=pe.prism).one()
                self.ntx_sa = ServiceAccount(usage=pc_info.service_account).get_service_account()
            except sqlalchemy.exc.NoResultFound:
                raise Exception(f"Can't find PC '{self.task.pc}' neither in Retail nor in Warehouse!")
        self.domain = pc_info.domain
        self.rest_pe = PrismElement(self.task.pe, self.ntx_sa, logger=self.logger)

    def task_process(self):
        self.ilg.write(f"Start to delete workload {self.vm_name}...")
        self.init_variables()
        fqdn = f"{self.vm_name.lower()}.{self.domain}"
        self.cleanup_ipam(fqdn)
        vm_uuid = self.get_vm_uuid(self.vm_name)
        self.remove_vm_from_ntx_pd(vm_uuid)
        self.cleanup_ntx(vm_uuid, self.vm_name)
        self.cleanup_tower_inventory(fqdn)
        self.cleanup_thors_hammer(self.vm_name)
        self.set_vm_to_unavailable(self.vm_name)
        self.ilg.write(f"Delete workload {self.vm_name} finished.")

    def cleanup_ipam(self, fqdn):
        Ipam(logger=self.logger).cleanup_fqdn(fqdn)

    def get_vm_uuid(self, vm_name):
        res, vms = self.rest_pe.get_vm_list(filter_string=f"sortCriteria=vm_name&searchString={vm_name}")
        if not res:
            self.ilg.write(f"Failed to get vm list for PE '{self.task.pe}'!", severity="warning")
            return None
        vm_uuid = None
        for vm in vms:
            if vm.get("name") == vm_name:
                vm_uuid = vm.get("uuid")
        return vm_uuid

    def remove_vm_from_ntx_pd(self, vm_uuid):
        pd = ProtectionDomain(pe=self.task.pe, sa=self.ntx_sa, logger=self.logger)
        pe_cluster = self.task.pe.split('.')[0]
        pd_name = f"{pe_cluster}-Gold_CCG"
        if not pd.is_pd_existing(pd_name):
            self.ilg.write("PD doesn't exist, skipping...", severity="warning")
            return
        if not pd.is_vm_in_pd(vm_uuid, pd_name):
            self.ilg.write("VM already doesn't exist in PD, skipping...", severity="warning")
            return
        self.ilg.write(f"Removing VM {vm_uuid} from PD {pd_name}...")
        pd.remove_vms_from_pd([vm_uuid], pd_name)
        self.ilg.write("Remove VM from PD succeeded.")

    def cleanup_ntx(self, vm_uuid, vm_name):
        if not vm_uuid:
            self.ilg.write(f"Can't find vm matching name {vm_name} on Nutanix, skipping...", severity="warning")
            return
        self.ilg.write(f"Start to delete VM on Nutanix... vm_uuid: {vm_uuid}, vm_name: {vm_name}")
        task_uuid = self.rest_pe.delete_vm(vm_uuid)
        self.ilg.write(f"Task uuid: {task_uuid}, wait for it done...")
        if not self.rest_pe.is_task_succeeded(task_uuid):
            raise flaskex.InternalServerError("Failed to delete VM!")
        self.ilg.write("Delete VM on Nutanix succeeded.")

    def _cleanup_ansible_inventory(self, fqdn):
        ansible = Ansible(logger=self.logger)
        if not ansible.check_vm_exist(fqdn):
            self.ilg.write(f"Ansible inventory object {fqdn} doesn't exist! Continue...", severity="warning")
            return
        self.ilg.write(f"Deleting ansible inventory object {fqdn}...")
        ansiblepayload = {
            "extra_vars": {
                "ikea_hosts": [{
                    "name": fqdn
                }]
            }
        }
        ansible.launch_ansible_job(template_id=26, ansiblepayload=ansiblepayload, wait_time=60)
        self.ilg.write("Delete ansible inventory object succeed.")

    def cleanup_tower_inventory(self, fqdn):
        tower = Tower(logger=self.logger, db_logger=self.db_logger)
        template_id = 9275
        tower.get_tower_template(template_id, template_type="job")
        if not tower.is_host_existing_in_inventory(1073, fqdn):
            self.ilg.write(f"Tower inventory object {fqdn} doesn't exist! Continue...", severity="warning")
            return
        self.ilg.write(f"Deleting Tower inventory object {fqdn}...")
        payload = {
            "extra_vars": {
                "ikea_hosts": [{
                    "name": fqdn
                }]
            }
        }
        tower.execute_tower_template(template_id=template_id, payload=payload, template_type="job", retry_interval=60)
        self.ilg.write("Delete tower inventory object succeed.")

    def cleanup_thors_hammer(self, vm_name):
        machine_name = vm_name.upper()
        try:
            ThorsHammerAPI(logger=self.logger).delete_computer(machine_name)
            self.ilg.write("Delete computer on ThorsHammer succeed.")
        except ThorsDeleteComputerNotFound as e:
            self.ilg.write(f"Failed to delete computer on ThorsHammer. Detail: {e}.", severity="warning")
            self.ilg.write("Maybe it's not a windows server... Let's ignore and move on.")
            return

    @classmethod
    def get_workload_task(cls, getlog=False):
        try:
            logging.info("Listing workload task list.")
            _task_schema = ModelWorkloadTaskSchema(many=True)
            _task_list = ModelWorkloadTask().query.filter_by(task_type=cls.TASK_TYPE).order_by(
                        ModelWorkloadTask.id.desc()).all()
            if getlog:  # get the task log create a log field in all tasks
                _res = []
                logsschema, _logschema = ModelWorkloadTaskLogSchema(many=True), ModelWorkloadTaskLogSchema()
                _taskschema = ModelWorkloadTaskSchema()
                for task in _task_list:
                    _task = _taskschema.dump(task)
                    # if task.logs:
                    logs = ModelWorkloadTaskLog.query.filter_by(task_id=task.id).order_by(
                        ModelWorkloadTaskLog.id.desc()).all()
                    if logs:
                        _logs = logsschema.dump(logs)
                        # _task['logs'] = _logs  # type: ignore
                        _task['latestlog'] = _logschema.dump(logs[0])  # type: ignore
                    else:
                        # _task['logs'] = []  # type: ignore
                        _task['latestlog'] = {'severity': 'info', 'loginfo': 'No log yet'}  # type: ignore
                    _res.append(_task)
                result = _res
            else:
                result = _task_schema.dump(_task_list)
            return jsonify(result)
        except Exception as e:
            logging.error(str(e))
            return False

    def set_vm_to_unavailable(self, vm_name):
        """
        Set the VM to unavailable in the database.
        """
        self.ilg.write(f"Setting VM status {vm_name} to 'Unavailable' in database.")
        vm = ModelRetailNutanixVM.query.filter_by(name=vm_name).one_or_none()
        if not vm:
            self.ilg.write(f"VM {vm_name} not found in database, skipping status update.", severity="warning")
            return
        vm.status = 'Unavailable'
        db.session.commit()
        self.ilg.write(f"VM {vm_name} status set done.")


class CleanupFailedWorkloadTask(DeleteWorkloadTask):
    TASK_TYPE = "CREATE_WORKLOAD"
    EXISTING_TASK_ALLOWED = True

    def __init__(self, payload):
        self.task_id = int(payload["task_id"])
        task = ModelWorkloadTask.query.filter_by(id=self.task_id).first()
        payload["name"] = task.name
        payload["pe"] = task.pe
        super().__init__(payload)
        self.task_duplicated_kwargs = {
            "task_type": self.TASK_TYPE, "id": self.task_id
        }

    def run(self):
        self.check_task_existence()
        self.task = self.task_model.query.filter_by(id=self.task_id, task_type=self.TASK_TYPE).first()
        task_id = self.task.id
        try:
            self.spawn_subprocess(self.start_task, task_id, init_app=False)
        except Exception as e:
            error_info = str(repr(traceback.format_exception(sys.exception())))
            logging.error(error_info)
            raise e

    def start_task(self):
        logging.info(f"In subprocess task 'Cleanup failed workload', task_id: {self.task.id}")
        self.init_flask_app()
        # Re-set self.task for the sub process
        self.task = ModelWorkloadTask.query.filter_by(id=self.task.id, task_type=self.TASK_TYPE).one()
        vm_name = self.task.name
        self.set_task_status(TaskStatus.IN_CLEANUP)
        detail_log_path = self.task.detail_log_path
        self.setup_loggers(
            log_name=f"cleanup_{vm_name}_{self.task.creater}_{datetime.utcnow().strftime('%Y-%m-%d-%H-%M-%S')}",
            origin_log_path=detail_log_path
        )
        self.init_variables()
        fqdn = f"{vm_name.lower()}.{self.domain}"
        self.ilg.write("Start to cleanup VM...")
        same_failed_tasks = ModelWorkloadTask.query.filter_by(name=vm_name) \
            .filter(ModelWorkloadTask.status.in_([TaskStatus.ERROR])).all()
        self.task.status = TaskStatus.IN_CLEANUP
        for task in same_failed_tasks:
            task.status = TaskStatus.IN_CLEANUP
        db.session.commit()
        try:
            self.cleanup_ipam(fqdn)
            vm_uuid = self.get_vm_uuid(vm_name)
            self.remove_vm_from_ntx_pd(vm_uuid)
            self.cleanup_ntx(vm_uuid, vm_name)
            self.cleanup_tower_inventory(fqdn)
            if self.task.workload_type == "windows":
                self.cleanup_thors_hammer(vm_name)
            self.task.status = TaskStatus.CLEANUP_DONE
            for task in same_failed_tasks:
                task.status = TaskStatus.CLEANUP_DONE
            db.session.commit()
            self.ilg.write("VM clean up successfully.")
        except flaskex.HTTPException as e:
            self.task.status = TaskStatus.ERROR
            for task in same_failed_tasks:
                task.status = TaskStatus.ERROR
            db.session.commit()
            self.logger.error(e)
            self.ilg.write(f"Do cleanup failed. Detail: {e}", severity="error")
        except Exception:
            self.task.status = TaskStatus.ERROR
            for task in same_failed_tasks:
                task.status = TaskStatus.ERROR
            db.session.commit()
            error_info = str(repr(traceback.format_exception(sys.exception())))
            self.logger.error(error_info)
            self.ilg.write(f"Do cleanup failed. Detail: {error_info}", severity="error")

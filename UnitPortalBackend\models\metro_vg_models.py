from models.base_task_models import ModelB<PERSON>Task, ModelBaseTaskLog
from models.database import db, ma


class ModelMetroVGTask(ModelBaseTask):
    __tablename__       = 'dh_metro_vg_task'
    pe                  = db.Column(db.String(100))
    prism               = db.Column(db.String(255))
    vm_names            = db.Column(db.String(255))


class ModelMetroVGTaskSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelMetroVGTask


class ModelMetroVGTaskLog(ModelBaseTaskLog):
    __tablename__       = 'dh_metro_vg_task_log'


class ModelMetroVGTaskLogSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelMetroVGTaskLog

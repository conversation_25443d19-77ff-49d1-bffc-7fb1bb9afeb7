# installed modules
import json
import logging
import re
import os
import uuid
import multiprocessing
from flask_restful import Resource
import werkzeug.exceptions as flaskex
from flask_apispec.views import MethodResource
from flask_apispec import marshal_with, use_kwargs, doc
from flask import abort, Response, send_file, request, jsonify, current_app

# local files
from models.database import db
import static.SETTINGS as SETTING
from models.pm_models import ModelSLIPMTask
from business.loggings.loggings import DBLogging
from business.distributedhosting.pmtask import Task
from business.generic.commonfunc import FileDownloader
from business.authentication.authentication import User
from business.authentication.tokenvalidation import pmtokencheck, PrivilegeValidation
from business.generic.commonfunc import terminate_process_by_id, get_request_token, setup_logger
from business.distributedhosting.simplivity.pm.simplivity_pm import start_simplivity_pm, SimplivityPM
from business.distributedhosting.simplivity.simplivity import SimplivityvCenter, Vcenter, VcCluster, SliDetaillist
from swagger.sli_pm_schema import ListSLIVmRequestSchema, SLIVmSchema, CreateSliPmRequestSchema, \
    CreateSliPmResponseSchema, AbortSliPmRequestSchema, AbortSliPmResponseSchema, GetSliPmTaskInfoResponseSchema
# from business.distributedhosting.simplivity.simplivity import SliHost
from .route import route


@route('/api/v1/simplivity/vm/list')
class RestfulSimplivityVMList(MethodResource, Resource):
    @doc(description="List SimpliVity VMs", tags=['View Resources'])
    @use_kwargs(ListSLIVmRequestSchema, location='json', apply=False)
    @marshal_with(SLIVmSchema(many=True), code=200)
    @PrivilegeValidation(privilege={"role_sli": "view_vm"})
    def post(self):
        try:
            data = request.get_json(force=True)
            if 'cluster' not in list(data.keys()):  # type: ignore
                raise Exception({'code': 400, 'message': 'Missing field [cluster].'})
            simplivity = SimplivityPM(vc=data['vc'], cluster=data['cluster'], usage="simplivity_pm")  # type: ignore
            res, msg = simplivity.if_slicluster_exists()
            if not res:
                raise Exception({'code': 400, 'message': msg})
            vc = SimplivityvCenter(vc=simplivity.vc, username=simplivity.sa['username'], password=simplivity.sa['password'])
            vc.connect()
            if not vc:
                raise Exception({'code': 400, 'message': f"Failed to connect the VC : {data['vc']}, modify the VC."})
            vms = vc.get_vm(clustername=data['cluster'])
            ovcs = [{"name": vm.name, "state": vm.runtime.powerState, "is_controller_vm": "True"} for vm in vms if
                    re.match("OmniStackVC*", vm.name)]
            vms_standard = [{"name": vm.name, "state": vm.runtime.powerState, "is_controller_vm": "false"} for vm in
                            vms]
            vm_list = vms_standard + ovcs
            return Response(json.dumps(vm_list), status=200, mimetype='application/json')              
        except flaskex.BadRequest:
            abort(400, "Please make sure that you have a valid payload.")
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
                    else:
                        abort(500, e)
                else:
                    abort(500, e)
            else:
                abort(500, e)


@route('/api/v1/simplivity/pm/poweroff')
class RestfulSimplivityPMPoweroff(Resource): # unused, just keep there for test
    @pmtokencheck
    def post(self):
        try:
            data = request.get_json(force=True)
            if 'cluster' not in list(data.keys()):  # type: ignore
                raise Exception({'code': 400, 'message': 'Missing field [cluster].'})
            simplivity = SimplivityPM(vc=data['vc'], cluster=data['cluster'], usage="simplivity_pm")  # type: ignore
            vc = SimplivityvCenter(vc=simplivity.vc, username=simplivity.sa['username'],
                                   password=simplivity.sa['password'])
            vc.connect()
            vms = vc.SLIPM_PowerOff(cluster_name=data['cluster'])
            # print(vm_list)
            if vms:
                msg = "PM complete"
                return Response(json.dumps(msg), status=200, mimetype='application/json')
            msg = "PM failed"
            abort(500, msg)
        except flaskex.BadRequest:
            abort(400, "Please make sure that you have a valid payload.")
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
                    else:
                        abort(500, e)
                else:
                    abort(500, e)
            else:
                abort(500, e)


@route('/api/v1/pm/sli/create')
class RestfulCreateSLIPM(MethodResource, Resource):
    # this is the function for creating a nutanix PM
    @doc(description="Create SimpliVity PM", tags=['Power Maintenance'])
    @use_kwargs(CreateSliPmRequestSchema, location='json', apply=False)
    @marshal_with(CreateSliPmResponseSchema, code=200)
    @PrivilegeValidation(privilege={"role_pm": "create_sli_pm"}, pm_type="sli")
    def post(self):
        try:
            # get token
            token = get_request_token()
            if param := request.get_json(force=True):
                task = Task(token=token)
                res = task.create_sli_pm_task(param)
                if param['startnow']:
                    logging.info(f'Got one "startnow" NTX PM task, task_id:{res["id"]}, starting it !')
                    p = multiprocessing.Process(target=start_simplivity_pm, args=[res['id']])
                    p.start()
                    _task = ModelSLIPMTask.query.filter_by(id=res['id']).first()
                    _task.pid = p.pid
                    db.session.commit()
                return jsonify(res)
            raise Exception({'code': 400, 'message': 'Missing fields'})  # raise if pm details not given
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/sli/pm/update')
class RestfulUpdateSLIPM(MethodResource, Resource):  # need to optimize.
    # @use_kwargs(UpdateSliPmRequestSchema, location='json', apply=False)
    # @marshal_with(UpdateSliPmResponseSchema)
    @PrivilegeValidation(privilege={"role_pm": "create_sli_pm"}, pm_type="sli")
    @pmtokencheck
    def post(self):
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res, role = usr.get_role_by_token(token)
            if res:
                username = role.username  # ignore
            else:
                # if cann't get the username, then quit  # noqa
                raise Exception({'code': 401, 'message': 'Username not found by the token.'})
            param = request.get_json(force=True)
            task_id = param['id']
            if not task_id:
                raise Exception({'code': 400, 'message': 'Cannot update the PM task due to lack of task id.'})
            task = Task(id=task_id, token=token)
            print('checking privilege.')
            if not task.sli_if_operation_is_allowed():
                raise Exception({'code': 400,
                                 'message': 'Operation is not allowed. This mainly because user doesn''t have enough privilege.'})  # noqa
            res, msg = task.update_sli_pm_task(**param)
            if res:
                logging.critical(f"PM task {task_id} updated by [{username}].")
                return Response(json.dumps(msg), status=200, mimetype='application/json')
            print(res, msg)
            raise Exception({'code': 500, 'message': msg})
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/sli/pm/abort')
class RestfulAbortSLIPM(MethodResource, Resource):  # need to optimize.
    @doc(description="Abort SimpliVity PM", tags=['Power Maintenance'])
    @use_kwargs(AbortSliPmRequestSchema, location='json', apply=False)
    @marshal_with(AbortSliPmResponseSchema, code=200)
    @PrivilegeValidation(privilege={"role_pm": "abort_sli_pm"}, pm_type="sli")
    def post(self):      # noqa
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res, role = usr.get_role_by_token(token)
            if res:
                username = role.username  # ignore
            else:
                raise Exception({'code': 401,
                                 'message': 'Username not found by the token.'})  # if cann't get the username, then quit  # noqa
            param = request.get_json(force=True)
            pid, logfile, task_id = int(param['pid']), param['detaillogpath'], param['id']
            if not (pid and task_id):
                raise Exception({'code': 400, 'message': 'Cannot abort the PM task due to lack of PID or task id.'})
            if not logfile:
                raise Exception({'code': 400, 'message': 'Cannot abort the PM task due to lack of log file.'})
            logger = setup_logger(str(uuid.uuid4()),
                                  logfile)  # create a new logger to write log for aborting into the same log file as the PM procedure  # noqa
            logger.critical(f"User [{username}] is aborting this PM task.")
            res, msg = terminate_process_by_id(pid)  # kill the PM task process.
            if res:
                logging.critical(f"PM task {task_id} aborted by [{username}].")  # write logs in 2 place for aborting
                logger.critical(f"PM task {task_id} aborted by [{username}].")
                lg = DBLogging(logdir=SETTING.SLI_LOG_PATH, logtype="SLI_PM")
                lg.write_pm_log(loginfo=f'PM was aborted by {username}', logseverity='info', taskid=task_id)
                task = Task(task_id)
                res, msg = task.abort_sli_pm_task()
                if res:
                    return Response(json.dumps(msg), status=200, mimetype='application/json')
                raise Exception({'code': 500,
                                     'message': f'Task was aborted, but failed at update the status in DB. error message:{msg}'})  # noqa
            logger.error(f"Failed to abort the PM task.message :{msg}")
            raise Exception({'code': 500, 'message': msg})
        except ValueError:
            abort(400, 'Cannot abort the PM task, make sure PID is integer and correct.')
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/sli/pm/delete')
class RestfulDeleteSLIPM(MethodResource, Resource):  # need to optimize.
    # @use_kwargs(DeleteSliPmRequestSchema, location='json', apply=False)
    # @marshal_with(DeleteSliPmResponseSchema)
    @PrivilegeValidation(privilege={"role_pm": "delete_sli_pm"}, pm_type="delete")
    def post(self):
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res, role = usr.get_role_by_token(token)
            if res:
                username = role.username  # ignore
            else:
                # if cann't get the username, then quit
                raise Exception({'code': 401, 'message': 'Username not found by the token.'})
            param = request.get_json(force=True)
            task_id = param['id']
            if not task_id:
                raise Exception({'code': 400, 'message': 'Cannot delete the PM task due to lack of task id.'})
            task = Task(task_id)
            res, msg = task.delete_sli_pm_task()
            if res:
                logging.critical(f"PM task deleted by [{username}].")
                return Response(json.dumps(msg), status=200, mimetype='application/json')
            abort(500, f'Failed to delete the PM task, error message: {msg}')
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/pm/sli/log/download')
class RestfulDownloadSLIPMLog(Resource):  # need to optimize.
    def post(self):
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res, role = usr.get_role_by_token(token)
            if res:
                username = role.username  # noqa    # pylint: disable=W0612
            else:
                # if cann't get the username, then quit
                raise Exception({'code': 401, 'message': 'Username not found by the token.'})
            param = request.get_json(force=True)
            filepath = param['filepath']
            if not filepath:
                raise Exception({'code': 400, 'message': 'Cannot download the log file due to lack of file path.'})
            return send_file(filepath)
        except Exception as e:
            if e.args:
                if isinstance(type(e.args[0]), dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/sli/pm/info/<int:pm_id>')
class RestfulGetSLIPMInfo(MethodResource, Resource):
    # this is the function for creating a simplivity PM
    @doc(description="Get Simplivity PM task info", tags=['Power Maintenance'])
    @marshal_with(GetSliPmTaskInfoResponseSchema, code=200)
    @PrivilegeValidation(privilege={"role_pm": "view_sli_pm"}, pm_type="sli")
    def get(self, pm_id):
        task = Task(id=pm_id)
        pmtask = task.get_slipm_task_by_id()
        print(pmtask)
        return Response(json.dumps(pmtask), status=200, mimetype='application/json')


@route('/api/v1/slipmtasklist')
class RestfulSLIPMTaskList(Resource):
    @PrivilegeValidation(privilege={"role_pm": "view_sli_pm"})
    def get(self):
        try:
            _task = Task()
            task_list = _task.get_slipm_task(getlog=True)
            return task_list
        except Exception:
            abort(500, "Internal error")
            
            
@route('/api/v1/slipmtasks')
class RestfulSLIPMTasks(Resource):
    @PrivilegeValidation(privilege={"role_pm": "view_sli_pm"})
    def get(self):
        try:
            _task = Task()
            task_list = _task.get_slipm_task(getlog=False)
            return task_list
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/sli/vc/list')
class RestfulVCList(Resource):
    @PrivilegeValidation(privilege={"role_sli": "view_vc"})
    def get(self):
        try:
            _vc = Vcenter(vc='list')
            res = _vc.get_vc_list_from_db()
            return res
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/sli/cluster/list')
class RestfulClusterList(Resource):
    @PrivilegeValidation(privilege={"role_sli": "view_cluster"})
    def get(self):
        try:
            _cluster = VcCluster(cluster='list')
            res = _cluster.get_cluster_list_from_db()
            return res
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/sli/correspondence')
class RestfulVCCLUSTERCORRESPONDENCE(Resource):
    def get(self):
        try:
            _cluster = VcCluster(cluster='list')
            res = _cluster.get_vc_cluster_correspondence_from_db()
            return res
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/sli/host')
class RestfulSliHost(Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_vm"})
    def get(self):
        download = request.args.get('download', type=bool)
        try:
            res = jsonify(SliDetaillist().get_all_host_info_from_db())
            if download:
                file_path = FileDownloader(data=json.loads(res.data)).download()
                logging.info(f"Downloading file {file_path}...")

                # res = send_file(file_path)
                def gene():
                    with open(file_path) as f:
                        yield from f
                    os.remove(file_path)

                res = current_app.response_class(gene(), mimetype='text/csv')
                res.headers.set('Content-Disposition', 'attachment', filename='data.csv')
                return res
            return res
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/sli/vms')
class RestfulSliVms(Resource):
    # def get(self):
    #     return jsonify(Sli_Detail_list().get_all_slivms_info_from_db())
    @PrivilegeValidation(privilege={"role_ntx": "view_vm"})
    def get(self):
        download = request.args.get('download', type=bool)
        try:
            res = jsonify(SliDetaillist().get_all_slivms_info_from_db())
            if download:
                file_path = FileDownloader(data=json.loads(res.data)).download()
                logging.info(f"Downloading file {file_path}...")

                # res = send_file(file_path)
                def gene():
                    with open(file_path) as f:
                        yield from f
                    os.remove(file_path)

                res = current_app.response_class(gene(), mimetype='text/csv')
                res.headers.set('Content-Disposition', 'attachment', filename='data.csv')
                return res
            return res
        except Exception:
            abort(500, "Internal error")

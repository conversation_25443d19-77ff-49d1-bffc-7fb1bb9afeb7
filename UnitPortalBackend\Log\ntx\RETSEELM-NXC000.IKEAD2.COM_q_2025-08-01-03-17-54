2025-08-01 11:17:56,963 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:17:56,963 INFO Getting the cluster list from PC.
2025-08-01 11:17:56,964 INFO Getting cluster list from ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:17:56,964 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/api/nutanix/v3/clusters/list, method: POST, headers: None
2025-08-01 11:17:56,964 INFO params: None
2025-08-01 11:17:56,964 INFO User: <EMAIL>
2025-08-01 11:17:56,965 INFO payload: {'kind': 'cluster'}
2025-08-01 11:17:56,967 INFO files: None
2025-08-01 11:17:56,967 INFO timeout: None
2025-08-01 11:18:18,529 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002A268C01150>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:18:18,529 WARNING Call api failed, going to do the 2 retry...
2025-08-01 11:18:39,568 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002A268D4B210>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:18:39,568 WARNING Call api failed, going to do the 3 retry...
2025-08-01 11:19:00,626 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000002A268D59210>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:19:00,626 WARNING Call api failed, going to do the 4 retry...
2025-08-01 11:19:17,002 CRITICAL User [q] is aborting this PM task.
2025-08-01 11:19:17,007 CRITICAL PM task 1527 aborted by [q].

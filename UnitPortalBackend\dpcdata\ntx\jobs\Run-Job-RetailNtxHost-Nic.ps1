$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Launch-Job(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $DhHosts          = Select-DhRetailNtxHost -Vars $Vars | Where-Object {$_.status -eq "Running"}
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $NeedToUpdate     = @("Unknown", "")
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhHosts.Count)' hosts need to update" -DumpFile $DumpFile
        $DhHosts | Foreach-Object -ThrottleLimit 50 -Parallel {
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $H          = $_
            $UpdateDict = $using:CollectionUpdate
            $PE         = $using:DhPEs | Where-Object {$_.id -eq $H.pe_id}
            $PC         = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount -and ($SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage "nutanix_datafetch_prod" | Select-Object -First 1)) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for calling '$($PE.name)', we'll use the one of 'nutanix_datafetch_prod'" -DumpFile $using:DumpFile
            }elseif (!$SvcAccount) {
                Write-Console-Logs -Level ERROR -FunctionName (Get-FunctionName) -Message "No service account for calling '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level ERROR -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for calling '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $NicMap = [PSCustomObject]@{
                'name'           = $H.name
                'nic0_uuid'      = "Unknown"
                'nic0_mac'       = "Unknown"
                'nic0_speed'     = "0 Gbkps"
                'nic0_mtu'       = "Unknown"
                'nic0_sw_device' = "Unknown"
                'nic0_sw_port'   = "Unknown"
                'nic0_sw_vendor' = "Unknown"
                'nic0_sw_vlan'   = "Unknown"
                'nic1_uuid'      = "Unknown"
                'nic1_mac'       = "Unknown"
                'nic1_speed'     = "0 Gbkps"
                'nic1_mtu'       = "Unknown"
                'nic1_sw_device' = "Unknown"
                'nic1_sw_port'   = "Unknown"
                'nic1_sw_vendor' = "Unknown"
                'nic1_sw_vlan'   = "Unknown"
            }
            if ($PrismCall = Rest-Prism-v1-Get-HostNic -Fqdn $H.pe_fqdn -Uuid $H.uuid -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We've got NIC profile for '$($H.name)'" -DumpFile $using:DumpFile
                foreach($i in @(0..1)) {
                    $NicMap."$('nic' + $i + '_uuid')"      = $PrismCall[$i].uuid
                    $NicMap."$('nic' + $i + '_mac')"       = $PrismCall[$i].macAddress
                    $NicMap."$('nic' + $i + '_speed')"     = "" + ([int]$PrismCall[$i].linkSpeedInKbps / [Math]::Pow(1000, 2)) + " Gbps"
                    $NicMap."$('nic' + $i + '_mtu')"       = $PrismCall[$i].mtuInBytes
                    $NicMap."$('nic' + $i + '_sw_device')" = $PrismCall[$i].switchDeviceId
                    $NicMap."$('nic' + $i + '_sw_port')"   = $PrismCall[$i].switchPortId
                    $NicMap."$('nic' + $i + '_sw_vendor')" = $PrismCall[$i].switchVendorInfo
                    $NicMap."$('nic' + $i + '_sw_vlan')"   = $PrismCall[$i].switchVlanId
                }
            }else {
                Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to get NIC profile for '$($H.name)'" -DumpFile $using:DumpFile
            }
            $UpdateDict.Add($NicMap)
        } -UseNewRunspace
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_host]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxHost-ByName -Vars $Vars -Collection $CollectionUpdate
    }    
}
Launch-Job -Force $Force
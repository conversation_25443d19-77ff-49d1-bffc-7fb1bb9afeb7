FROM python:3.11.10-slim-bookworm
ARG BUILD_TAG=Unknown
ARG BUILD_DATE=Unknown
ARG GIT_COMMIT_ID=Unknown

ENV BUILD_TAG=${BUILD_TAG}
ENV BUILD_DATE=${BUILD_DATE}
ENV GIT_COMMIT_ID=${GIT_COMMIT_ID}

COPY . DHUP/
RUN apt-get update && apt install -y curl gnupg2 vim
#Install Microsoft ODBC 17
RUN curl https://packages.microsoft.com/keys/microsoft.asc | tee /etc/apt/trusted.gpg.d/microsoft.asc
RUN curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg
RUN curl https://packages.microsoft.com/config/debian/12/prod.list | tee /etc/apt/sources.list.d/mssql-release.list
RUN apt-get update && ACCEPT_EULA=Y apt-get install -y gcc wget msodbcsql17
RUN pip install -r /DHUP/requirements.txt
WORKDIR /DHUP
ENTRYPOINT ["python", "-u", "app.py"]
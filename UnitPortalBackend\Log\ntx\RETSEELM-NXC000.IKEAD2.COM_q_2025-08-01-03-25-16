2025-08-01 11:25:29,831 INFO Getting cluster list from ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:25:29,831 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/api/nutanix/v3/clusters/list, method: POST, headers: None
2025-08-01 11:25:29,831 INFO params: None
2025-08-01 11:25:29,831 INFO User: <EMAIL>
2025-08-01 11:25:29,831 INFO payload: {'kind': 'cluster'}
2025-08-01 11:25:29,831 INFO files: None
2025-08-01 11:25:29,831 INFO timeout: None
2025-08-01 11:25:50,889 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001DE1BF91610>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:25:50,899 WARNING Call api failed, going to do the 2 retry...
2025-08-01 11:26:11,959 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001DE1C06F290>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:26:11,960 WARNING Call api failed, going to do the 3 retry...
2025-08-01 11:26:33,004 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001DE1C075290>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:26:33,004 WARNING Call api failed, going to do the 4 retry...
2025-08-01 11:26:34,690 CRITICAL User [q] is aborting this PM task.
2025-08-01 11:26:34,692 CRITICAL PM task 1531 aborted by [q].

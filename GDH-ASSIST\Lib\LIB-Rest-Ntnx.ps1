function Invoke-Prism-API(){
    param(
        [string]                                               $Fqdn,
        [string] [ValidateSet("clustermgmt", 
                              "dataprotection", 
                              "vmm",
                              "lcm")]                          $Namespace = "clustermgmt",
        [string] [ValidateSet("v0.8", "v1", "v2", "v2pe", "v3", 
                              "v4.0.a1", "v4.0.a2", "v4.0.a3", 
                              "v4.0.a4", "v4.0.b1")]           $Version,
        [string]                                               $RequestURI,
        [string] [ValidateSet("GET", "POST", "PUT", "DELETE")] $Method,
        [object]                                               $Auth,
        [object]                                               $Body,
        [object]                                               $Form,
        [int]                                                  $MaxTry = 5,
        [int]                                                  $TimeoutSec = 30
    )
    if ($MaxTry) {
        $Headers = @{
            'Accept'        = 'application/json'
            'Authorization' = $Auth
            'Content-Type'  = 'application/json'
        }
        $Payload = @{
            'Method'     = $Method
            'Headers'    = $Headers
            'TimeoutSec' = $TimeoutSec
        }
        if ($Body) {
            $Payload['Body'] = $Body | ConvertTo-Json -Depth 100
        }
        if ($Form) {
            $Payload['Form'] = $Form
        }
        switch ($Version) {
            "v0.8" {
                $Payload['Uri'] = "https://$($Fqdn):9440/api/nutanix/v0.8/$($RequestURI)"
            }
            "v1" {
                $Payload['Uri'] = "https://$($Fqdn):9440/api/nutanix/v1/$($RequestURI)"
            }
            "v2" {
                $Payload['Uri'] = "https://$($Fqdn):9440/PrismGateway/services/rest/v2.0/$($RequestURI)"
            }
            "v2pe" {
                $Payload['Uri'] = "https://$($Fqdn):9440/api/nutanix/v2.0/$($RequestURI)"
            }
            "v3" {
                $Payload['Uri'] = "https://$($Fqdn):9440/api/nutanix/v3/$($RequestURI)"
            }
            default {
                $Payload['Uri'] = "https://$($Fqdn):9440/api/$($Namespace)/$($Version)/$($RequestURI)" # V4 API calling
            }
        }
        try {
            return Invoke-RestMethod @Payload -SkipCertificateCheck:$true -SkipHeaderValidation:$true
        }
        catch {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling $Fqdn for $RequestURI through API $Version. Cause: $_ Retry in 5 seconds"
            Start-Sleep 5
            return Invoke-Prism-Api -Fqdn $Fqdn `
                                    -Namespace $Namespace `
                                    -Version $Version `
                                    -RequestURI $RequestURI `
                                    -Method $Method `
                                    -Auth $Auth `
                                    -Body $Body `
                                    -Form $Form `
                                    -MaxTry $($MaxTry - 1) `
                                    -TimeoutSec $($TimeoutSec + 5)
        }
    }else {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling $Fqdn for $RequestURI."
        return $null
    }
}
function Invoke-Genesis-Api(){
    param(
        [string]                                     $Fqdn,
        [string] [ValidateSet("GET", "POST", "PUT")] $Method,
                                                     $Auth,
                                                     $HashPayload,
        [int]                                        $MaxTry     = 5,
        [int]                                        $TimeoutSec = 10
    )
    if ($MaxTry) {
        $Headers = @{
            'Accept'        = 'application/json'
            'Authorization' = $Auth
            'Content-Type'  = 'application/json'
        }
        $Payload = @{
            'Uri'        = "https://$($Fqdn):9440/PrismGateway/services/rest/v1/genesis"
            'Method'     = $Method
            'Headers'    = $Headers
            'TimeoutSec' = $TimeoutSec
        }
        if ($HashPayload) {
            $DoubleJson = $HashPayload | ConvertTo-Json -Depth 10 -Compress
            $Body = @{
                value = $DoubleJson
            }
            $Payload['Body'] = $Body | ConvertTo-Json
        }
        try {
            return Invoke-RestMethod @Payload
        }
        catch {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling $Fqdn for /genesis. Cause: $_ Retry in 5 seconds"
            Start-Sleep 5
            Invoke-Genesis-Api -Fqdn $Fqdn `
                               -Method $Method `
                               -Auth $Auth `
                               -HashPayload $HashPayload `
                               -MaxTry $($MaxTry - 1) `
                               -TimeoutSec $($TimeoutSec + 5)
        }
    }else {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling $Fqdn"
        return $null
    }
}
function Invoke-Lcm-Api(){
    param (
        [string]                                     $Prism,
        [string]                                     $RequestURI,
        [string] [validateSet("v1", "v4")]           $Version,
        [string] [ValidateSet("GET", "POST", "PUT")] $Method,
                                                     $Auth,
                                                     $Body,
        [int]                                        $MaxTry     = 5,
        [int]                                        $TimeoutSec = 10
    )
    if ($MaxTry) {
        $Headers = @{
            'Accept'        = 'application/json'
            'Authorization' = $Auth
            'Content-Type'  = 'application/json'
        }
        $Payload = @{
            'Method'     = $Method
            'Headers'    = $Headers
            'TimeoutSec' = $TimeoutSec
        }
        if ($Body) {
            $Payload['Body'] = $Body | ConvertTo-Json
        }
        switch ($Version) {
            "v1" {
                $Payload['Uri'] = "https://$($Prism):9440/lcm/v1.r0.b1/$($RequestURI)"
            }
            "v4" {
                $Payload['Uri'] = "https://$($Prism):9440/api/lcm/v4.0.a1/$($RequestURI)"
            }
        }
        try {
            return Invoke-RestMethod @Payload
        }
        catch {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling '$Prism' for '$RequestURI' through API '$Version'. Cause: $_ Retry in 5 seconds"
            Start-Sleep 5
            Invoke-Lcm-Api -Prism $Prism `
                           -RequestURI $RequestURI `
                           -Version $Version `
                           -Method $Method `
                           -Auth $Auth `
                           -Body $Body `
                           -MaxTry $($MaxTry - 1) `
                           -TimeoutSec $($TimeoutSec + 5)
        }
    }else {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling '$Prism' for '$RequestURI'"
        return $null
    }
}
function Rest-Genesis-Get-LcmFw(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $HashPayload = @{
        '.oid'    = 'LifeCycleManager'
        '.method' = 'lcm_framework_rpc'
        '.kwargs' = @{
            'method_class' = 'LcmFramework'
            'method'       = 'get_config'
        }
    }
    if ($RestCall = Invoke-Genesis-Api -Fqdn $Fqdn `
                                       -Method POST `
                                       -Auth $Auth `
                                       -HashPayload $HashPayload `
                                       -TimeoutSec 30) {
        $Object = $RestCall.value | ConvertFrom-Json -Depth 10
        return $Object.'.return'
    }
    return $null
}
function Rest-Lcm-v1-Get-LcmEntity(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Body = @{
        'offset' = 0
    } 
    if ($RestCall = Invoke-Lcm-Api -Prism $Fqdn `
                                   -RequestURI "resources/entities/list" `
                                   -Version v1 `
                                   -Method POST `
                                   -Auth $Auth `
                                   -Body $Body `
                                   -TimeoutSec 30) {
        return $RestCall.data.entities
    }
    return $null
}
function Rest-Lcm-v4-Get-LcmEntity(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Page     = 0
    $Entities = @()
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $($Page + 1)"
        $RestCall = Invoke-Lcm-Api -Prism $Fqdn `
                                   -RequestURI "resources/entities?%24page=$($Page)&%24limit=100" `
                                   -Version v4 `
                                   -Method GET `
                                   -Auth $Auth `
                                   -TimeoutSec 30
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.data.Count) objects on page $($Page + 1)"
        $Entities += $RestCall.data
        $Page ++
    } until (
        $RestCall.data.Count -lt 100
    )
    return $Entities
}
function Rest-Prism-v0_8-Add-Image(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $ImageName,
        [string]                                              $Annotation,
        [string] [ValidateSet("ISO_IMAGE", "DISK_IMAGE")]     $ImageType,
        [string]                                              $ContainerUuid,
        [string]                                              $ImageUrl,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Body = @{
        "name"            = $ImageName
        "annotation"      = $Annotation
        "imageType"       = $ImageType
        "imageImportSpec" = @{
            "containerUuid" = $ContainerUuid
            "url"           = $ImageUrl
        }
    }
    return Invoke-Prism-API -Fqdn $Fqdn -RequestURI "images" -Version v0.8 -Method POST -Body $Body -Auth $Auth
}
function Rest-Prism-v1-Get-License(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Uuid) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "license?proxyClusterUuid=$Uuid" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "license" -Method GET -Auth $Auth
}
function Rest-Prism-v1-List-Host(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "hosts" -Method GET -Auth $Auth
}
function Rest-Prism-v1-Get-Host(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Uuid) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "hosts/$Uuid" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "hosts" -Method GET -Auth $Auth
}
function Rest-Prism-v1-List-Cluster(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Uuid) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "clusters/$Uuid" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI 'clusters' -Method GET -Auth $Auth
}
function Rest-Prism-v1-Get-Cluster(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "cluster" -Method GET -Auth $Auth
}
function Rest-Prism-v1-Get-ClusterVersion(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "cluster/version" -Method GET -Auth $Auth
}
function Rest-Prism-v1-Get-ClusterKey(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI 'cluster/public_keys' -Method GET -Auth $Auth
}
function Rest-Prism-v1-List-VM(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI 'vms' -Method GET -Auth $Auth
}
function Rest-Prism-v1-Get-VM(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Uuid) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "vms/$Uuid" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI 'vms' -Method GET -Auth $Auth
}
function Rest-Prism-v1-List-ProtectionDomain(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Fqdn $Fqdn `
                            -RequestURI "protection_domains" `
                            -Version v1 `
                            -Method GET `
                            -Auth $Auth `
                            -TimeoutSec 40
}
function Rest-Prism-v1-List-UnProtectedVm(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Fqdn $Fqdn `
                            -RequestURI "protection_domains/unprotected_vms" `
                            -Version v1 `
                            -Method GET `
                            -Auth $Auth `
                            -TimeoutSec 40
}
function Rest-Prism-v1-List-RsDrSnapshot(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Fqdn $Fqdn `
                            -RequestURI 'remote_sites/dr_snapshots' `
                            -Version v1 `
                            -Method GET `
                            -Auth $Auth `
                            -TimeoutSec 30
}
function Rest-Prism-v1-Add-VmToProtectionDomain(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $PdName,
        [array]                                               $VmIds,
        [bool]                                                $Acg = $false,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Body = @{
        'vmAddRemoveType'         = "LISTED_VMS"
        'protectionDomainName'    = $PdName
        'vmIds'                   = @()
        'volumeGroupUuids'        = @()
        'appConsistentSnapshots'  = $Acg
        'protectReelatedEntities' = $true
    }
    $Body.vmIds += $VmIds
    return Invoke-Prism-API -Fqdn $Fqdn `
                            -RequestURI "protection_domains/$($PdName)/add_entities" `
                            -Version v1 `
                            -Method POST `
                            -Auth $Auth `
                            -Body $Body `
                            -TimeoutSec 60
}
function Rest-Prism-v1-Remove-VmFromProtectionDomain(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $PdName,
        [array]                                               $VmIds,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Body = @{
        'vmAddRemoveType'      = "LISTED_VMS"
        'protectionDomainName' = $PdName
        'vmIds'                = @()
        'volumeGroupUuids'     = @()
    }
    $Body.vmIds += $VmIds
    return Invoke-Prism-API -Fqdn $Fqdn `
                            -RequestURI "protection_domains/$($PdName)/remove_entities" `
                            -Version v1 `
                            -Method POST `
                            -Auth $Auth `
                            -Body $Body `
                            -TimeoutSec 60
}
function Rest-Prism-v1-Get-Alert(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Uuid) {
        return Invoke-Prism-API -Fqdn $Fqdn -RequestURI "alerts/$($Uuid)" -Version v1 -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -RequestURI "alerts" -Version v1 -Method GET -Auth $Auth
}
function Rest-Prism-v1-List-Image(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -RequestURI "images" -Version v1 -Method GET -Auth $Auth
}
function Rest-Prism-v1-List-Container(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI 'containers' -Method GET -Auth $Auth
}
function Rest-Prism-v1-Get-Groups(){
    param(
        [string]                                              $Fqdn,
        [object]                                              $Body,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn `
                            -RequestURI "groups" `
                            -Version v1 `
                            -Method POST `
                            -Auth $Auth `
                            -Body $Body
}
function Rest-Prism-v1-Get-HostNic(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string]                                              $NicId,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($NicId) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "hosts/$($Uuid)/host_nics/$($NicId)" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "hosts/$($Uuid)/host_nics" -Method GET -Auth $Auth
}
function Rest-Prism-v1-Get-DomainFaultToleranceStatus(){
    param(
        [string]                                                        $Fqdn,
        [string] [ValidateSet("NODE", "RACKABLE_UNIT", "RACK", "DISK")] $DomainType,
        [string] [Parameter(ParameterSetName = 'Credential')]           $Username,
        [string] [Parameter(ParameterSetName = 'Credential')]           $PWord,
                    [Parameter(ParameterSetName = 'Session')]           $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($DomainType) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "cluster/domain_fault_tolerance_status/$($DomainType)" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "cluster/domain_fault_tolerance_status" -Method GET -Auth $Auth
}
function Rest-Prism-v1-Get-Keys(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "keys/pem" -Method GET -Auth $Auth
}
function Rest-Prism-v1-Upload-Certificate(){
    param(
        [string]                                              $Fqdn,
        [string] [ValidateSet("RSA_2048", 
                              "RSA_4096", 
                              "EC_DSA_256", 
                              "EC_DSA_384", 
                              "EC_DSA_521")]                  $KeyType = "RSA_2048",
        [string]                                              $KeyFile,
        [string]                                              $CertFile,
        [string]                                              $ChainFile,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Form = @{
        'keyType' = $KeyType
        'key'     = Get-Item -Path $KeyFile
        'cert'    = Get-Item -Path $CertFile
        'caChain' = Get-Item -Path $ChainFile
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "keys/pem/import" -Method POST -Form $Form -Auth $Auth
}
function Rest-Prism-v1-Get-RemoteSite(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $RsName,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($RsName) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "remote_sites/$($RsName)" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "remote_sites" -Method GET -Auth $Auth
}
function Rest-Prism-v1-Add-RemoteSite(){
    param(
        [string]                                              $Fqdn,
        [Object]                                              $RsObj,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "remote_sites" -Body $RsObj -Method POST -Auth $Auth

}
function Rest-Prism-v1-Update-RemoteSite(){
    param(
        [string]                                              $Fqdn,
        [Object]                                              $RsObj,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "remote_sites" -Body $RsObj -Method PUT -Auth $Auth
}
function Rest-Prism-v1-Delete-RemoteSite(){
    param(
        [string]                                              $Fqdn,
        [Object]                                              $RsName,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v1 -RequestURI "remote_sites/$($RsName)" -Method DELETE -Auth $Auth
}
function Rest-Prism-v2-Detach-disk {
    param(
        [string]                                              $Fqdn,
        [string]                                              $VmName,
        [string]                                              $VmUuid,
        [string]                                              $VmDiskUuid,
        [string]                                              $DeviceUuid,
        [int]                                                 $Index,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )

    Begin {
        if (!$Auth) {
            $GstAccount = Read-GstAccount
            $Auth       = Get-Base64-Auth -Username $GstAccount.username -PWord $GstAccount.password
        }
    }

    Process {
        $Body = @{
            vm_disks = @(
                @{
                    disk_address = @{
                        vmdisk_uuid  =  $VmDiskUuid
                        device_uuid  =  $DeviceUuid
                        device_index =  $Index
                        device_bus   = 'scsi'
                    }
                }
            )
        }
 
        Write-ConsoleLog -Level INFO -FunctionName (Get-FunctionName) -Message "We're now removing disk $Index from VM:$VmName "
        $Result = Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "/vms/$VmUuid/disks/detach" -Method POST -Auth $Auth -body $Body
    }

    End {
        return $Result
    }
}
function Rest-Prism-v2-List-Cluster(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "clusters" -Method GET -Auth $Auth
}
function Rest-Prism-v2-Get-Cluster(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Uuid) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "clusters/$Uuid" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "cluster" -Method GET -Auth $Auth
}
function Rest-Prism-v2-Get-VM(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Uuid) {
        $RequestUri = "vms/" + $Uuid + "?include_vm_disk_config=true"
        return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI $RequestUri -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "vms?include_vm_disk_config=true" -Method GET -Auth $Auth
}
function Rest-PrismElement-v2-Get-VM(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Uuid) {
        $RequestUri = "vms/" + $Uuid + "?include_vm_disk_config=true"
        return Invoke-Prism-API -Fqdn $Fqdn -Version v2pe -RequestURI $RequestUri -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "vms?include_vm_disk_config=true" -Method GET -Auth $Auth
}
function Rest-Prism-v2-Get-VM-Nic(){
    param (
        [string]                                              $Fqdn,
        [string]                                              $Vm_Uuid,
        [string]                                              $Nic_Id,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Nic_Id) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "vms/$($Vm_Uuid)/nics/$($Nic_Id)" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "vms/$($Vm_Uuid)/nics" -Method GET -Auth $Auth
}
function Rest-Prism-v2-Set-VM-PowerState(){
    <#
    .SYNOPSIS
    Function that sets power state of the VM on the cluster with particular Fqdn
    
    .DESCRIPTION
    Long description
    
    .PARAMETER Fqdn
    Parameter description
    
    .PARAMETER Uuid
    Parameter description
    
    .PARAMETER State
    Set the power state for VM, allowed values are: on, off, powercycle, reset, pause, suspend, resume, save, acpi_shutdown, acpi_reboot
    
    .PARAMETER Username
    Parameter description
    
    .PARAMETER PWord
    Parameter description
    
    .PARAMETER Auth
    Parameter description
    
    .EXAMPLE
    An example
    
    .NOTES
    General notes
    #>
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string]                                              $State,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Body = @{
        'transition' = $State
    }
    return Invoke-Prism-Api -Fqdn $Fqdn `
                            -RequestURI "vms/$Uuid/set_power_state" `
                            -Version v2 `
                            -Method POST `
                            -Auth $Auth `
                            -Body $Body `
                            -TimeoutSec 30
}
function Rest-Prism-v2-List-Network(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI 'networks' -Method GET -Auth $Auth
}
function Rest-Prism-v2-Create-Network(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Name,
        [string]                                              $VlanId,
        [string]                                              $VSwitchName,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Body = @{
        'name'    = $Name
        'vlan_id' = $VlanId
    }
    if ($VSwitchName) {
        $Body['vswitch_name'] = $VSwitchName
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "networks" -Method POST -Body $Body -Auth $Auth
}
function Rest-Prism-v2-Get-Network(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if (!$Uuid) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "networks" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "networks/$Uuid" -Method GET -Auth $Auth
}
function Rest-Prism-v2-Get-Task(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Fqdn $Fqdn `
                            -RequestURI "tasks/$Uuid" `
                            -Version v2 `
                            -Method GET `
                            -Auth $Auth `
                            -TimeoutSec 40
}
function Rest-Prism-v2-List-Image(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI 'images' -Method GET -Auth $Auth
}
function Rest-Prism-v2-Get-Image(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "images/$Uuid" -Method GET -Auth $Auth
}
function Rest-Prism-v2-Get-Host(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Uuid) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "hosts/$Uuid" -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "hosts" -Method GET -Auth $Auth
}
function Rest-Prism-v2-Get-Alert(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "alerts" -Method GET -Auth $Auth
}
function Rest-Prism-v2-Get-Event(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v2 -RequestURI "events" -Method GET -Auth $Auth
}
function Rest-Prism-v3-List-Host(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        "kind"   = "host"
        "offset" = 0
        "length" = 500
    }
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
        $RestCall = Invoke-Prism-Api -Fqdn $Fqdn `
                                     -RequestURI "hosts/list" `
                                     -Version v3 `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 30
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
        $Entities    += $RestCall.entities
        $Body.offset += 500
        $Page        ++
    } until (
        $RestCall.entities.Count -lt 500
    )
    return $Entities
}
function Rest-Prism-v3-Get-Host(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v3 -RequestURI "hosts/$Uuid" -Method GET -Auth $Auth
}
function Rest-Prism-v3-Get-VM(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($Uuid) {
        return Invoke-Prism-API -Fqdn $Fqdn -Version v3 -RequestURI "vms/$Uuid" -Method GET -Auth $Auth
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        'kind'   = 'vm'
        'offset' = 0
        'length' = 500
    }
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
        $RestCall = Invoke-Prism-Api -Fqdn $Fqdn `
                                     -Version v3 `
                                     -RequestURI 'vms/list' `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 100
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
        $Entities    += $RestCall.entities
        $Body.offset += 500
        $Page        ++
    } until (
        $RestCall.entities.Count -lt 500
    )
    return $Entities
}

function Rest-Prism-v3-List-Network(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        "kind"   = "subnet"
        "offset" = 0
        "length" = 500
    }
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
        $RestCall = Invoke-Prism-Api -Fqdn $Fqdn `
                                     -RequestURI "subnets/list" `
                                     -Version v3 `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 30
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
        $Entities    += $RestCall.entities
        $Body.offset += 500
        $Page        ++
    } until (
        $RestCall.entities.Count -lt 500
    )
    return $Entities
}
function Rest-Prism-v3-Get-Network(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v3 -RequestURI "subnets/$Uuid" -Method GET -Auth $Auth
}
function Rest-Prism-v3-List-App(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Filter,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        'offset' = 0
        'length' = 250
    }
    if ($Filter) {
        $Body['filter'] = $Filter
    }
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
        $RestCall = Invoke-Prism-Api -Fqdn $Fqdn `
                                     -RequestURI "apps/list" `
                                     -Version v3 `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 40
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
        $Entities    += $RestCall.entities
        $Body.offset += $Body.length
        $Page        ++
    } until (
        $RestCall.entities.Count -lt $Body.length
    )
    return $Entities
}
function Rest-Prism-v3-Get-App(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Fqdn $Fqdn `
                            -RequestURI "apps/$Uuid" `
                            -Version v3 `
                            -Method GET `
                            -Auth $Auth `
                            -Body $Body `
                            -TimeoutSec 40
}
function Rest-Prism-v3-Get-Groups(){
    param(
        [string]                                              $Fqdn,
        [object]                                              $Body,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn `
                            -RequestURI "groups" `
                            -Version v3 `
                            -Method POST `
                            -Auth $Auth `
                            -Body $Body
}
function Rest-Prism-v3-List-Category-Keys(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        'offset' = 0
        'length' = 250
    }
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
        $RestCall = Invoke-Prism-Api -Fqdn $Fqdn `
                                     -RequestURI "categories/list" `
                                     -Version v3 `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 40
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
        $Entities    += $RestCall.entities
        $Body.offset += $Body.length
        $Page        ++
    } until (
        $RestCall.entities.Count -lt $Body.length
    )
    return $Entities
}
function Rest-Prism-v3-List-Category-Values(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Name,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        'offset' = 0
        'length' = 250
    }
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
        $RestCall = Invoke-Prism-Api -Fqdn $Fqdn `
                                     -RequestURI "categories/$($Name)/list" `
                                     -Version v3 `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 40
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
        $Entities    += $RestCall.entities
        $Body.offset += $Body.length
        $Page        ++
    } until (
        $RestCall.entities.Count -lt $Body.length
    )
    return $Entities
}
function Rest-Prism-v3-Get-Category-Key(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Name,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v3 -RequestURI "categories/$Name" -Method GET -Auth $Auth
}
function Rest-Prism-v3-Get-Category-Value(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Name,
        [string]                                              $Value,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v3 -RequestURI "categories/$Name/$Value" -Method GET -Auth $Auth
}
function Rest-Prism-v3-Get-Category-Usage(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        'group_member_count'  = 1000
        'group_member_offset' = 1000
    }
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
        $RestCall = Invoke-Prism-Api -Fqdn $Fqdn `
                                     -RequestURI "categories/query" `
                                     -Version v3 `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 40
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
        $Entities    += $RestCall.entities
        $Body.offset += $Body.length
        $Page        ++
    } until (
        $RestCall.entities.Count -lt $Body.length
    )
    return $Entities
}
function Rest-Prism-v3-List-Image(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        "kind"   = "image"
        "offset" = 0
        "length" = 500
    }
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
        $RestCall = Invoke-Prism-Api -Fqdn $Fqdn `
                                     -RequestURI "images/list" `
                                     -Version v3 `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 60
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
        $Entities    += $RestCall.entities
        $Body.offset += $Body.length
        $Page        ++
    } until (
        $RestCall.entities.Count -lt $Body.length
    )
    return $Entities
}
function Rest-Prism-v3-Get-Image(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [switch]                                              $GetContent,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($GetContent) {
        return Invoke-Prism-API -Fqdn $Fqdn -RequestURI "images/$($Uuid)/file" -Version v3 -Method GET -Auth $Auth
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v3 -RequestURI "images/$Uuid" -Method GET -Auth $Auth
}
function Rest-Prism-v3-Delete-Image(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v3 -RequestURI "images/$Uuid" -Method DELETE -Auth $Auth
}
function Rest-Prism-v3-Get-Alert(){
    param (
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v3 -RequestURI "alerts/$Uuid" -Method GET -Auth $Auth
}
function Rest-Prism-v3-List-Alert(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        "kind"   = "alert"
        "offset" = 0
        "length" = 500
    }
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
        $RestCall = Invoke-Prism-Api -Fqdn $Fqdn `
                                     -RequestURI "alerts/list" `
                                     -Version v3 `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 60
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
        $Entities    += $RestCall.entities
        $Body.offset += $Body.length
        $Page        ++
    } until (
        $RestCall.entities.Count -lt $Body.length
    )
    return $Entities
}
function Rest-Prism-v3-Migrate-Image(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $ClusterUuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Body = @{
        "cluster_reference" = @{
            "uuid" = $ClusterUuid
            "kind" = "cluster"
            "name" = "string"
        }
        "image_reference_list" = @()
    }
    return Invoke-Prism-API -Fqdn $Fqdn -RequestURI "images/migrate" -Version v3 -Method POST -Auth $Auth -Body $Body
}
function Rest-Prism-v3-Update-Vm(){
    param (
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [Object]                                              $Vm,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -RequestURI "vms/$Uuid" -Version v3 -Method PUT -Auth $Auth -Body $Vm
}
function Rest-Prism-v3-Get-Cluster(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-API -Fqdn $Fqdn -Version v3 -RequestURI "clusters/$Uuid" -Method GET -Auth $Auth
}
function Rest-Prism-v3-List-Cluster(){
    param(
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        "kind"   = "cluster"
        "offset" = 0
        "length" = 500
    }
    do {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
        $RestCall = Invoke-Prism-Api -Fqdn $Fqdn `
                                     -RequestURI "clusters/list" `
                                     -Version v3 `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 60
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
        $Entities    += $RestCall.entities
        $Body.offset += $Body.length
        $Page        ++
    } until (
        $RestCall.entities.Count -lt $Body.length
    )
    return $Entities
}

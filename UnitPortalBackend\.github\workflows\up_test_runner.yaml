name: up_test_runner
on:
  push:
    branches:
      - dev_curfu1_new_cluster
jobs:
  UpdateCode:
    runs-on: up_test_runner
    steps:
      - name: Checkout
        uses: actions/checkout@v2.3.4
        with:
          repository: DH-Community/UnitPortalBackend
          token: ${{ secrets.GIT_PAT }}
          path: /home/<USER>/code
      - name: Just-a-test
        run: whoami > /tmp/test_test && pwd >> /tmp/test_test
        working-directory:  /home/<USER>/code
import logging
import re
import time

import werkzeug.exceptions as flaskex
from business.authentication.authentication import ServiceAccount
from business.distributedhosting.nutanix.workload.workload_exceptions import TowerJobFailed, TowerJobLaunchFailed, \
    TowerTemplateNotFound
from business.generic.commonfunc import CommonRestCall
from business.loggings.loggings import IntegratedLogger


class Ansible():
    def __init__(self, logger=logging) -> None:
        self.logger = logger if logger else logging
        self.ab = AnsibleAPI(logger=self.logger)

    def check_vm_exist(self, fqdn):
        res = self.ab.check_if_vm_exist(fqdn)
        return res and len(res) >= 1

    def launch_ansible_job(self, template_id, ansiblepayload=None, wait_time=120, retries=100):
        self.ab.check_template_existence(template_id)
        jobid = self.ab.launch_ansible_template(template_id, ansiblepayload)
        error_info = f"Ansible error, please contact linux team. Job link: https://ansible.ikea.com/#/jobs/playbook/{jobid}"
        if not jobid:
            self.logger.error('Failed to get jobid, unable to continue')
            raise flaskex.InternalServerError(f'Failed to get jobid {jobid}, unable to continue. {error_info}')

        results = self.ab.get_ansible_job_state(jobid)
        if re.match('successful', results, re.I):
            self.logger.info('Template completed')
        elif re.match('failed', results, re.I):
            self.logger.error(f'Template failed. Result: {results}')
            raise flaskex.InternalServerError(error_info)
        else:
            for _ in range(retries):
                self.logger.info(f"Wait {wait_time} seconds for the ansible job...")
                time.sleep(wait_time)
                results = self.ab.get_ansible_job_state(jobid)
                if re.match('successful', results, re.I):
                    self.logger.info('Template completed')
                    break
                elif re.match('failed', results, re.I):
                    self.logger.error(f'Template failed. Result: {results}')
                    raise flaskex.InternalServerError(error_info)
            else:
                raise flaskex.InternalServerError(f'Template is running for too long, it is considered as failed. {error_info}')


class AnsibleAPI:
    def __init__(self, sa=None, logger=logging) -> None:
        _sa = sa if sa else ServiceAccount(usage=ServiceAccount.LIN_API).get_service_account()
        self.logger = logger
        self.rest = CommonRestCall(username=_sa['username'], password=_sa['password'])
        self.base_url = "https://ansible.ikea.com/api/v2"

    def check_if_vm_exist(self, fqdn):
        url = f"{self.base_url}/inventories/8/hosts/?name={fqdn}"

        res = self.rest.call_restapi(url, method="GET")
        if not res:
            raise Exception('check ansible failed.')
        return res.json()["results"]

    # def ansiblepayload(self, vm_name, tier, os_version, dc_type, groups):
    #     ansible_payload = {
    #         "ikea_hosts": [{
    #             "name": vm_name,
    #             "hostgroup": f"{tier}/{os_version}/{dc_type}",
    #             "variables": {
    #                 "patch_responsible_email": "<EMAIL>",
    #                 "owners": ["loctechansible", "gdba"],
    #                 "patch_autoreboot": False,
    #                 "patch": False
    #             },
    #             "groups": groups
    #         }]
    #     }
    #     return ansible_payload

    def check_template_existence(self, template_id):
        url = f"{self.base_url}/job_templates/{template_id}"
        try:
            res = self.rest.call_restapi(url, method="GET")
            return res
        except Exception:
            raise flaskex.InternalServerError(f'{template_id} does not exist')

    def launch_ansible_template(self, template_id, ansiblepayload):
        self.logger.info(f"Launch ansible template {template_id}")
        url = f"{self.base_url}/job_templates/{template_id}/launch/"
        res = self.rest.call_restapi(url, method="POST", payload=ansiblepayload)
        if not res:
            raise Exception(f'{template_id} Running Ansible wait procedure.')
        job_id = res.json()["job"]
        self.logger.info(f"Succeed to launch ansible template, job id: {job_id}")
        return job_id

    def get_ansible_job_state(self, jobid):
        self.logger.info(f"Getting ansible job state, job id: {jobid}")
        url = f"{self.base_url}/jobs/?id={jobid}"
        res = self.rest.call_restapi(url, method="GET")
        if not res:
            return False
        status = res.json()["results"][0]["status"]
        self.logger.info(f"Succeed to get ansible job state, status: {status}")
        return status


class Tower:
    def __init__(self, logger, db_logger) -> None:
        self.logger = logger
        self.db_logger = db_logger
        self.ilg = IntegratedLogger(file_lg=logger, db_lg=self.db_logger)
        self.tower = TowerAPI(logger=logger)

    def get_tower_template(self, template_id, template_type="workflow"):
        self.logger.info(f"Checking template {template_id} existence...")
        res = self.tower.check_tower_template(template_id, template_type)
        if not res.ok:
            raise TowerTemplateNotFound(template_id)
        self.logger.info(f"Good, we found the template: {template_id} in tower.")
        
    def execute_tower_template(self, template_id, payload, template_type="workflow", retries=45, retry_interval=120, enable_relaunch=False):    # default to wait for 90 minutes
        self.logger.info(f"Executing tower template:{template_id}, payload:{payload}.")
        res, mes = self.tower.launch_tower_template(template_id=template_id, payload=payload, template_type=template_type)
        if not res:
            raise TowerJobLaunchFailed(mes)
        job_id = mes.json()['id']
        self.ilg.write(f"Job created. Job link: https://tower.ikea.com{mes.json()['url']}")
        self.logger.info("Let's take a nap for 2 minutes and see if this baby finished executing.")
        time.sleep(120)
        job_succeeded = self.wait_workflow_job(job_id, template_type, retry_interval, retries)
        if job_succeeded:
            return job_id
        if not enable_relaunch:
            raise TowerJobFailed(template_id, job_id)
        res = self.tower.relaunch_workflow_job(job_id)
        new_job_id = res.json().get("id")
        self.logger.info(f"New job spawned by relaunch, new job ID is {new_job_id}")
        relaunch_succeeded = self.wait_workflow_job(new_job_id, template_type, retry_interval, retries)
        if relaunch_succeeded:
            self.logger.info("Tower workflow succeeded after relaunch.")
            return new_job_id
        raise TowerJobFailed(template_id, new_job_id)

    def wait_workflow_job(self, job_id, template_type, retry_interval=60, retries=90):
        self.logger.info("Waiting for Ansible workflow Job")
        self.logger.info(f"Tracking Job ID {job_id}, waiting for non-'running' state.")
        for i in range(1, retries + 1):
            self.logger.info(f"Time to check the job status, {i}/{retries}.")
            status = self.tower.get_tower_job_state(job_id, template_type).json()["status"].lower()
            self.logger.info(f"Job status is {status}")
            if any(_ in status for _ in ['running', 'pending', 'waiting']):
                self.logger.info(f"Job is processing, wait {retry_interval} seconds and retry.")
                time.sleep(retry_interval)
                continue
            elif any(_ in status for _ in ['failed', 'error', 'canceled']):
                return False
            elif 'successful' in status:
                self.logger.info("The job has finished with success.")
                return True
            else:
                self.logger.warning(
                    f"Unhandled/unexpected status '{status}' on this workflow job with id '{job_id}'. "
                    f"Please investigate."
                )
                return False
        self.logger.warning(
            f"Job ID {job_id} reached the timeout threshold of '{retries}' times with '{retry_interval}' each retry. "
            f"Please investigate."
        )
        return False

    def is_host_existing_in_inventory(self, inventory_id, host_name):
        res = self.tower.get_host_from_inventory(inventory_id, host_name)
        return res.json()["count"] > 0


class TowerAPI():
    def __init__(self, sa=None, logger=None):
        _sa = sa if sa else ServiceAccount(usage=ServiceAccount.LIN_API).get_service_account()
        self.logger = logger
        self.rest = CommonRestCall(username=_sa['username'], password=_sa['password'])
        self.base_url = "https://tower.ikea.com/api/v2"

    def check_tower_template(self, template_id, template_type="workflow"):
        if template_type == "workflow":
            url = f"{self.base_url}/workflow_job_templates/{template_id}"
        elif template_type == "job":
            url = f"{self.base_url}/job_templates/{template_id}"
        return self.rest.call_restapi(url, method="GET")

    def launch_tower_template(self, template_id, payload, template_type="workflow"):
        if template_type == "workflow":
            url = f"{self.base_url}/workflow_job_templates/{template_id}/launch/"
        elif template_type == "job":
            url = f"{self.base_url}/job_templates/{template_id}/launch/"
        try:
            return True, self.rest.call_restapi(url, method="POST", payload=payload, retry=2)
        except Exception as e:
            return False, str(e)

    def relaunch_workflow_job(self, job_id):
        url = f"{self.base_url}/workflow_jobs/{job_id}/relaunch/"
        response = self.rest.call_restapi(url, method="POST")
        return response

    def get_tower_job_state(self, jobid, template_type="workflow"):
        if template_type == "workflow":
            url = f"{self.base_url}/workflow_jobs/{jobid}"
        elif template_type == "job":
            url = f"{self.base_url}/jobs/{jobid}"
        return self.rest.call_restapi(url, method="GET")

    def get_host_from_inventory(self, inventory_id, name):
        url = f"{self.base_url}/inventories/{inventory_id}/hosts/?name={name}"
        response = self.rest.call_restapi(url)
        return response

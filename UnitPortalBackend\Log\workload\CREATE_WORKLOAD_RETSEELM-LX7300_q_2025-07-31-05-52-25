2025-07-31 13:52:25,685 INFO Start to run the task.
2025-07-31 13:52:25,723 INFO ****************************************************************************************************
2025-07-31 13:52:25,723 INFO *                                                                                                  *
2025-07-31 13:52:25,723 INFO *                                        Check VM existence                                        *
2025-07-31 13:52:25,723 INFO *                                                                                                  *
2025-07-31 13:52:25,733 INFO ****************************************************************************************************
2025-07-31 13:52:25,774 INFO Checking if vm already exists in the PE cluster.
2025-07-31 13:52:25,774 INFO Checking if RETSEELM-LX7300 existed in PE.
2025-07-31 13:52:25,775 INFO Getting VM list from RETSEELM-NXC000.
2025-07-31 13:52:25,775 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms?sortCriteria=vm_name&searchString=RETSEELM-LX7300, method: GET, headers: None
2025-07-31 13:52:25,775 INFO params: None
2025-07-31 13:52:25,775 INFO User: <EMAIL>
2025-07-31 13:52:25,776 INFO payload: None
2025-07-31 13:52:25,776 INFO files: None
2025-07-31 13:52:25,776 INFO timeout: 30
2025-07-31 13:52:27,529 INFO Got the VM list from RETSEELM-NXC000.
2025-07-31 13:52:27,530 INFO RETSEELM-LX7300 doesn't exist in RETSEELM-NXC000.
2025-07-31 13:52:27,569 INFO RETSEELM-LX7300 not exists in Cluster RETSEELM-NXC000.IKEAD2.COM, move on...
2025-07-31 13:52:27,613 INFO Checking if vm already exists in the inventory AD/Tower.
2025-07-31 13:52:29,034 INFO FQDN 'RETSEELM-LX7300.IKEAD2.COM' not exists in Tower Inventory, continue...
2025-07-31 13:52:29,077 INFO Checking if vm already exists in IPAM.
2025-07-31 13:52:29,103 INFO Start to check if RETSEELM-LX7300.IKEAD2.COM existed in IPAM...
2025-07-31 13:52:29,103 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-LX7300.IKEAD2.COM', method: GET, headers: None
2025-07-31 13:52:29,103 INFO params: None
2025-07-31 13:52:29,103 INFO User: <EMAIL>
2025-07-31 13:52:29,103 INFO payload: None
2025-07-31 13:52:29,103 INFO files: None
2025-07-31 13:52:29,103 INFO timeout: 30
2025-07-31 13:52:30,441 INFO 'RETSEELM-LX7300' not exists in IPAM, continue...
2025-07-31 13:52:30,487 INFO ****************************************************************************************************
2025-07-31 13:52:30,487 INFO *                                                                                                  *
2025-07-31 13:52:30,487 INFO *                                              Sizing                                              *
2025-07-31 13:52:30,487 INFO *                                                                                                  *
2025-07-31 13:52:30,487 INFO ****************************************************************************************************
2025-07-31 13:52:30,527 INFO Sizing, check if cluster has enough capacity for this VM.
2025-07-31 13:52:30,528 INFO Get a list of existing hosts from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:52:30,528 INFO Calling /hosts through v1 API using GET method
2025-07-31 13:52:30,528 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-07-31 13:52:30,528 INFO params: None
2025-07-31 13:52:30,528 INFO User: <EMAIL>
2025-07-31 13:52:30,528 INFO payload: None
2025-07-31 13:52:30,528 INFO files: None
2025-07-31 13:52:30,528 INFO timeout: None
2025-07-31 13:52:32,269 INFO Get cluster details from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:52:32,270 INFO Calling /cluster through v1 API using GET method
2025-07-31 13:52:32,270 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster, method: GET, headers: None
2025-07-31 13:52:32,270 INFO params: None
2025-07-31 13:52:32,270 INFO User: <EMAIL>
2025-07-31 13:52:32,270 INFO payload: None
2025-07-31 13:52:32,270 INFO files: None
2025-07-31 13:52:32,270 INFO timeout: None
2025-07-31 13:52:34,157 INFO Get a list of existing user VMs from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:52:34,157 INFO Calling /vms through v2 API using GET method
2025-07-31 13:52:34,157 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms, method: GET, headers: None
2025-07-31 13:52:34,157 INFO params: None
2025-07-31 13:52:34,158 INFO User: <EMAIL>
2025-07-31 13:52:34,158 INFO payload: None
2025-07-31 13:52:34,158 INFO files: None
2025-07-31 13:52:34,158 INFO timeout: None
2025-07-31 13:52:35,987 INFO The cluster is a 3 node(s) cluster
2025-07-31 13:52:35,987 INFO Fetching capacity from node RETSEELM-NX7001
2025-07-31 13:52:35,987 INFO Storage of node RETSEELM-NX7001 is 44.68 TiB
2025-07-31 13:52:35,987 INFO Number of cores on node RETSEELM-NX7001 is 20
2025-07-31 13:52:35,987 INFO Memory install on node RETSEELM-NX7001 is 377.08 GiB
2025-07-31 13:52:35,988 INFO Fetching capacity from node RETSEELM-NX7002
2025-07-31 13:52:35,988 INFO Storage of node RETSEELM-NX7002 is 44.68 TiB
2025-07-31 13:52:35,988 INFO Number of cores on node RETSEELM-NX7002 is 20
2025-07-31 13:52:35,988 INFO Memory install on node RETSEELM-NX7002 is 377.08 GiB
2025-07-31 13:52:35,988 INFO Fetching capacity from node RETSEELM-NX7003
2025-07-31 13:52:35,988 INFO Storage of node RETSEELM-NX7003 is 44.68 TiB
2025-07-31 13:52:35,988 INFO Number of cores on node RETSEELM-NX7003 is 20
2025-07-31 13:52:35,988 INFO Memory install on node RETSEELM-NX7003 is 345.58 GiB
2025-07-31 13:52:35,988 INFO Number of nodes in this cluster is 3
2025-07-31 13:52:35,988 INFO Total storage capacity on this cluster is 134.04 TiB
2025-07-31 13:52:35,988 INFO total number of CPU cores on cluster is 60
2025-07-31 13:52:35,989 INFO Total memory capacity on this cluster is 1099.74 GiB
2025-07-31 13:52:35,989 INFO Resilient storage capacity on this cluster is 84.************** TiB
2025-07-31 13:52:35,989 INFO Number of resilient physical CPU cores is 40
2025-07-31 13:52:35,989 INFO Number of resilient physical CPU cores accounting CVMs is 34
2025-07-31 13:52:35,989 INFO Number of resilient virtual CPU cores (assuming 1:4 ratio) is 136
2025-07-31 13:52:35,989 INFO Resilient memory capacity on this cluster is 722.************* GiB
2025-07-31 13:52:35,989 INFO Resilient memory capacity accounting CVMs on this cluster is 658.************* GiB
2025-07-31 13:52:35,989 INFO Utilized storage of cluster is 0.95 TiB
2025-07-31 13:52:35,989 INFO There are 5 VMs on this cluster
2025-07-31 13:52:35,989 INFO Number of virtual cores used by 5 VMs that are powered on is 62
2025-07-31 13:52:35,989 INFO Memory used by 5 VMs that are powered on is 248.0 GiB
2025-07-31 13:52:35,990 INFO Available storage for new VM provisioning is 83.************** TiB
2025-07-31 13:52:35,990 INFO Available vCPU cores for new VM provisioning is 74
2025-07-31 13:52:35,990 INFO Available memory for new VM provisioning is 410.************* GiB
2025-07-31 13:52:36,031 INFO ****************************************************************************************************
2025-07-31 13:52:36,031 INFO *                                                                                                  *
2025-07-31 13:52:36,032 INFO *                                Checking workload network on NTX.                                 *
2025-07-31 13:52:36,032 INFO *                                                                                                  *
2025-07-31 13:52:36,032 INFO ****************************************************************************************************
2025-07-31 13:52:36,098 INFO Checking PE network by VlanId=793
2025-07-31 13:52:36,098 INFO Getting network list from RETSEELM-NXC000
2025-07-31 13:52:36,099 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/networks, method: GET, headers: None
2025-07-31 13:52:36,099 INFO params: None
2025-07-31 13:52:36,099 INFO User: <EMAIL>
2025-07-31 13:52:36,099 INFO payload: None
2025-07-31 13:52:36,099 INFO files: None
2025-07-31 13:52:36,099 INFO timeout: 30
2025-07-31 13:52:37,824 INFO Got the network list from RETSEELM-NXC000.
2025-07-31 13:52:37,824 INFO Vlan 793 is found
2025-07-31 13:52:37,870 INFO The network is found, the UUID is 9531e569-3bec-4d92-8581-6209bea747db
2025-07-31 13:52:37,909 INFO ****************************************************************************************************
2025-07-31 13:52:37,909 INFO *                                                                                                  *
2025-07-31 13:52:37,909 INFO *                                           Check image                                            *
2025-07-31 13:52:37,909 INFO *                                                                                                  *
2025-07-31 13:52:37,910 INFO ****************************************************************************************************
2025-07-31 13:52:40,206 INFO Verifying workload image existence in DB and on cluster.
2025-07-31 13:52:44,802 INFO Checking if RHELx_AUTO image
2025-07-31 13:52:45,480 INFO Check the latest version from http://itseelm-lx6248.ikea.com:9090/rhel9/ for _AUTO
2025-07-31 13:52:45,806 INFO Validating the latest version of RHEL image
2025-07-31 13:52:46,340 INFO We have the latest _AUTO image name, is rhel-9-1178422-20250628T053035
2025-07-31 13:52:46,340 INFO The current one is mismatch with the latest, we need to update
2025-07-31 13:52:51,680 INFO Validating image 'rhel-9-1178422-20250628T053035-RETSEELM-NXC000' existence in PE 'RETSEELM-NXC000'...
2025-07-31 13:52:52,005 INFO Start to find the image rhel-9-1178422-20250628T053035-RETSEELM-NXC000 from RETSEELM-NXC000
2025-07-31 13:52:52,005 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/images, method: GET, headers: None
2025-07-31 13:52:52,005 INFO params: None
2025-07-31 13:52:52,005 INFO User: <EMAIL>
2025-07-31 13:52:52,006 INFO payload: None
2025-07-31 13:52:52,006 INFO files: None
2025-07-31 13:52:52,006 INFO timeout: 30
2025-07-31 13:52:53,875 INFO Getting image list from RETSEELM-NXC000
2025-07-31 13:52:53,875 INFO Got the image list from RETSEELM-NXC000.
2025-07-31 13:52:53,875 INFO The image rhel-9-1178422-20250628T053035-RETSEELM-NXC000 is found in RETSEELM-NXC000
2025-07-31 13:52:55,281 INFO The image is found in PE
2025-07-31 13:53:02,516 INFO ****************************************************************************************************
2025-07-31 13:53:02,516 INFO *                                                                                                  *
2025-07-31 13:53:02,516 INFO *                                            Update DNS                                            *
2025-07-31 13:53:02,516 INFO *                                                                                                  *
2025-07-31 13:53:02,517 INFO ****************************************************************************************************
2025-07-31 13:53:02,561 INFO Start to assign IP to workload on IPAM...
2025-07-31 13:53:02,585 INFO Start to assign ip for RETSEELM-LX7300.IKEAD2.COM...
2025-07-31 13:53:02,586 INFO Start to find parent subnet by fqdn 'RETSEELM-NXC000.IKEAD2.COM' on IPAM...
2025-07-31 13:53:02,586 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-07-31 13:53:02,586 INFO params: None
2025-07-31 13:53:02,586 INFO User: <EMAIL>
2025-07-31 13:53:02,586 INFO payload: None
2025-07-31 13:53:02,586 INFO files: None
2025-07-31 13:53:02,586 INFO timeout: 30
2025-07-31 13:53:03,728 INFO ipam_object: <Response [200]>
2025-07-31 13:53:03,728 INFO Parent subnet name: IT NOC DH2 (ELM) DH1 (HBG) IKEAD2
2025-07-31 13:53:03,728 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=parent_subnet_name='IT NOC DH2 (ELM) DH1 (HBG) IKEAD2', method: GET, headers: None
2025-07-31 13:53:03,728 INFO params: None
2025-07-31 13:53:03,728 INFO User: <EMAIL>
2025-07-31 13:53:03,728 INFO payload: None
2025-07-31 13:53:03,728 INFO files: None
2025-07-31 13:53:03,728 INFO timeout: 30
2025-07-31 13:53:08,354 INFO Finding free address in subnet id: 91747
2025-07-31 13:53:08,355 INFO Assigning ip...
2025-07-31 13:53:08,355 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rpc/ip_find_free_address, method: GET, headers: None
2025-07-31 13:53:08,355 INFO params: {'subnet_id': '91747', 'max_find': 30}
2025-07-31 13:53:08,355 INFO User: <EMAIL>
2025-07-31 13:53:08,355 INFO payload: None
2025-07-31 13:53:08,355 INFO files: None
2025-07-31 13:53:08,355 INFO timeout: 30
2025-07-31 13:53:09,423 INFO Checking if ************ is already in use...
2025-07-31 13:53:09,684 INFO IP ************ is not able to use.
2025-07-31 13:53:09,684 INFO Checking if ************ is already in use...
2025-07-31 13:53:13,688 INFO IP ************ is able to use.
2025-07-31 13:53:13,692 INFO kwargs: {'url': 'https://IPAM.IKEA.COM/rest/ip_add', 'method': 'POST', 'params': {'hostaddr': '************', 'site_id': 2, 'name': 'retseelm-lx7300.ikead2.com', 'add_flag': 'new_only', 'ip_name_class': 'IKEA/Server_Distribute', 'ip_class_parameters': 'hostname=RETSEELM-LX7300&dns_update=1&__eip_dns_update_inheritance_property=set&ikea_server_type=Linux&ikea_network_ip_statement= This is a VM initially installed on Nutanix cluster :RETSEELM-NXC000.IKEAD2.COM.&ikea_in_pci=0'}, 'verify': False}
2025-07-31 13:53:16,174 INFO IP ************ assigned to RETSEELM-LX7300.IKEAD2.COM successfully. ip_id is 106163652
2025-07-31 13:53:16,341 INFO IP ************ assigned to workload successfully. ip_id: 106163652
2025-07-31 13:53:37,808 INFO template_path: c:\Dev\UnitPortalBackend\static\templates\create_vm_by_pe_payload.jinja2
2025-07-31 13:53:37,832 INFO script_path: c:\Dev\UnitPortalBackend\static\OSCustomization\LX-CloudInit-latest.yml
2025-07-31 13:53:37,858 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_info?ip_id=106163652, method: GET, headers: None
2025-07-31 13:53:37,858 INFO params: None
2025-07-31 13:53:37,858 INFO User: <EMAIL>
2025-07-31 13:53:37,858 INFO payload: None
2025-07-31 13:53:37,858 INFO files: None
2025-07-31 13:53:37,858 INFO timeout: 30
2025-07-31 13:53:38,826 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_info?ip_id=106163652, method: GET, headers: None
2025-07-31 13:53:38,826 INFO params: None
2025-07-31 13:53:38,828 INFO User: <EMAIL>
2025-07-31 13:53:38,828 INFO payload: None
2025-07-31 13:53:38,828 INFO files: None
2025-07-31 13:53:38,828 INFO timeout: 30
2025-07-31 13:53:39,788 INFO cloud init data: #cloud-config

# Set hostname and FQDN
hostname: RETSEELM-LX7300
fqdn: retseelm-lx7300.IKEAD2.COM
manage_etc_hosts: false
ssh_pwauth: true
write_files:
  - path: /etc/sysconfig/network
    content: |
      NETWORKING=yes
      NETWORKING_IPV6=no

runcmd:
  - for i in $(nmcli -g uuid con show); do nmcli con delete $i; done
  - nmcli con add con-name ens3 ifname ens3 type ethernet ipv4.method manual ipv4.address ************/26
  - nmcli con mod ens3 ipv4.gateway ************ autoconnect yes
  - nmcli con mod ens3 ipv4.dns "************ ************" ipv4.dns-search ikead2.com
  - nmcli con up ens3
  - touch /etc/cloud/cloud-init.disabled
2025-07-31 13:53:39,789 INFO Getting network list from RETSEELM-NXC000
2025-07-31 13:53:39,789 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/networks, method: GET, headers: None
2025-07-31 13:53:39,789 INFO params: None
2025-07-31 13:53:39,789 INFO User: <EMAIL>
2025-07-31 13:53:39,789 INFO payload: None
2025-07-31 13:53:39,789 INFO files: None
2025-07-31 13:53:39,789 INFO timeout: 30
2025-07-31 13:53:41,578 INFO Got the network list from RETSEELM-NXC000.
2025-07-31 13:53:41,579 INFO Vlan 793 is found
2025-07-31 13:53:41,579 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/storage_containers, method: GET, headers: None
2025-07-31 13:53:41,579 INFO params: None
2025-07-31 13:53:41,579 INFO User: <EMAIL>
2025-07-31 13:53:41,579 INFO payload: None
2025-07-31 13:53:41,579 INFO files: None
2025-07-31 13:53:41,579 INFO timeout: 30
2025-07-31 13:53:43,275 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms, method: POST, headers: None
2025-07-31 13:53:43,276 INFO params: None
2025-07-31 13:53:43,276 INFO User: <EMAIL>
2025-07-31 13:53:43,276 INFO payload: {'boot': {'secure_boot': False, 'uefi_boot': False, 'disk_address': {'device_bus': 'scsi', 'device_index': 0}}, 'machine_type': 'PC', 'memory_mb': 16384, 'name': 'RETSEELM-LX7300', 'num_cores_per_vcpu': 1, 'num_vcpus': 4, 'power_state': 'UNKNOWN', 'vm_customization_config': {'userdata': '#cloud-config\n\n# Set hostname and FQDN\nhostname: RETSEELM-LX7300\nfqdn: retseelm-lx7300.IKEAD2.COM\nmanage_etc_hosts: false\nssh_pwauth: true\nwrite_files:\n  - path: /etc/sysconfig/network\n    content: |\n      NETWORKING=yes\n      NETWORKING_IPV6=no\n\nruncmd:\n  - for i in $(nmcli -g uuid con show); do nmcli con delete $i; done\n  - nmcli con add con-name ens3 ifname ens3 type ethernet ipv4.method manual ipv4.address ************/26\n  - nmcli con mod ens3 ipv4.gateway ************ autoconnect yes\n  - nmcli con mod ens3 ipv4.dns "************ ************" ipv4.dns-search ikead2.com\n  - nmcli con up ens3\n  - touch /etc/cloud/cloud-init.disabled'}, 'vm_disks': [{'disk_address': {'device_bus': 'scsi', 'device_index': 0}, 'is_cdrom': False, 'vm_disk_clone': {'disk_address': {'vmdisk_uuid': 'ad396a45-5d81-40f9-80ab-029190faad6f'}, 'minimum_size': 137438953472}}], 'vm_nics': [{'network_uuid': '9531e569-3bec-4d92-8581-6209bea747db'}]}
2025-07-31 13:53:43,276 INFO files: None
2025-07-31 13:53:43,276 INFO timeout: 30
2025-07-31 13:53:45,163 INFO Task created, task_uuid: 9d9e18a1-0d13-4e53-a8a9-33d6865d334e
2025-07-31 13:53:45,163 INFO Get task status attempting 1/5...
2025-07-31 13:53:45,163 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/9d9e18a1-0d13-4e53-a8a9-33d6865d334e, method: GET, headers: None
2025-07-31 13:53:45,164 INFO params: None
2025-07-31 13:53:45,164 INFO User: <EMAIL>
2025-07-31 13:53:45,164 INFO payload: None
2025-07-31 13:53:45,164 INFO files: None
2025-07-31 13:53:45,164 INFO timeout: 30
2025-07-31 13:53:46,882 INFO Task status: Succeeded
2025-07-31 13:53:46,883 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/9d9e18a1-0d13-4e53-a8a9-33d6865d334e, method: GET, headers: None
2025-07-31 13:53:46,883 INFO params: None
2025-07-31 13:53:46,883 INFO User: <EMAIL>
2025-07-31 13:53:46,883 INFO payload: None
2025-07-31 13:53:46,883 INFO files: None
2025-07-31 13:53:46,883 INFO timeout: 30
2025-07-31 13:53:48,564 INFO Create VM by PE API succeeded! VM uuid: ae546e3a-5ecb-4287-b900-0e4736d88205
2025-07-31 13:53:48,644 INFO Start to set VM power state to 'ON', VM uuid: ae546e3a-5ecb-4287-b900-0e4736d88205, VM name: None.
2025-07-31 13:53:48,644 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/ae546e3a-5ecb-4287-b900-0e4736d88205/set_power_state, method: POST, headers: None
2025-07-31 13:53:48,644 INFO params: None
2025-07-31 13:53:48,644 INFO User: <EMAIL>
2025-07-31 13:53:48,644 INFO payload: {'transition': 'ON'}
2025-07-31 13:53:48,644 INFO files: None
2025-07-31 13:53:48,645 INFO timeout: 30
2025-07-31 13:53:50,411 INFO Get task status attempting 1/5...
2025-07-31 13:53:50,412 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/4de15899-3921-4255-aab7-9e9a58365a22, method: GET, headers: None
2025-07-31 13:53:50,412 INFO params: None
2025-07-31 13:53:50,412 INFO User: <EMAIL>
2025-07-31 13:53:50,412 INFO payload: None
2025-07-31 13:53:50,412 INFO files: None
2025-07-31 13:53:50,412 INFO timeout: 30
2025-07-31 13:53:52,139 INFO Task status: Running
2025-07-31 13:53:52,139 INFO Task is not ended, sleep 60s to retry... Task percentage: 1
2025-07-31 13:54:52,140 INFO Get task status attempting 2/5...
2025-07-31 13:54:52,140 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/4de15899-3921-4255-aab7-9e9a58365a22, method: GET, headers: None
2025-07-31 13:54:52,140 INFO params: None
2025-07-31 13:54:52,140 INFO User: <EMAIL>
2025-07-31 13:54:52,140 INFO payload: None
2025-07-31 13:54:52,140 INFO files: None
2025-07-31 13:54:52,140 INFO timeout: 30
2025-07-31 13:54:53,978 INFO Task status: Succeeded
2025-07-31 13:54:53,978 INFO Successfully set VM power state to 'ON'.
2025-07-31 14:01:20,276 INFO Sleep 5 minutes to ensure that DNS is correctly configured...
2025-07-31 14:11:48,117 INFO Woke up from sleep.
2025-07-31 14:11:51,953 INFO Checking template 9423 existence...
2025-07-31 14:11:53,584 INFO Good, we found the template: 9423 in tower.
2025-07-31 14:11:53,585 INFO Executing tower template:9423, payload:{'extra_var': {'ikea_hosts': [{'name': 'retseelm-lx7300'}]}}.
2025-07-31 14:11:55,341 INFO Job created. Job link: https://tower.ikea.com/api/v2/workflow_jobs/7933309/
2025-07-31 14:11:55,341 INFO Let's take a nap for 2 minutes and see if this baby finished executing.
2025-07-31 14:13:55,339 INFO Waiting for Ansible workflow Job
2025-07-31 14:13:55,339 INFO Tracking Job ID 7933309, waiting for non-'running' state.
2025-07-31 14:13:55,340 INFO Time to check the job status, 1/90.
2025-07-31 14:13:56,989 INFO Job status is failed
2025-07-31 14:13:57,032 INFO Tower job failed. Template id: 9423, job id: 7933309
2025-07-31 14:14:08,152 ERROR Task failed. Detail: Tower job failed. Please check the job link for details. Template id: 9423. Job link: https://tower.ikea.com/#/jobs/workflow/7933309/output

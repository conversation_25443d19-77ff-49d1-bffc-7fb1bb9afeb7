from marshmallow import Schema, fields


class CheckSiteExistenceRequestSchema(Schema):
    country_code = fields.Str(required=True)
    site_code = fields.Str(required=True)


class RetailNtxSchema(Schema):
    existence = fields.Bool()
    # pe = fields.List(fields.Str())
    pe = fields.Str()
    pc = fields.Str()


class RetailSliSchema(Schema):
    existence = fields.Bool()
    cluster = fields.Str()
    vc = fields.Str()


class CheckSiteExistenceResponseSchema(Schema):
    retail_nutanix = fields.Nested(RetailNtxSchema)
    retail_simplivity = fields.Nested(RetailSliSchema)

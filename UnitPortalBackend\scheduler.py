from datetime import datetime
from flask_apscheduler import APScheduler
from os import environ
from socket import gethostname
import logging
from business.nowit.incident_devourer import IncidentDevourer
from business.nowit.incident_analysis_process import IncidentAnalysis
from business.schedulejob.scanmovetask import scan_move_datasynctask, scan_whmove_datasynctask
from business.schedulejob.scanspptask import scan_spp_task, scan_aos_task
from business.schedulejob.scanautomaintenancetask import scan_automaintenancetask_stuck_tasks
from business.nowit.incident_reminder_email_daily_schedule import schedule_time
from business.schedulejob.scancollectortask import scan_collector_task
from pytz import timezone


def add_scheduler():
    scheduler = APScheduler()
    scheduler.add_job(id='Scheduled SPP Upgrade Task', func=scan_aos_task, trigger="interval", minutes=10,
                        next_run_time=datetime.now())
    if gethostname() == "NTXSEELM-NT3000" or environ.get("DPC_ENVIRONMENT", None) == "production":
        #both windows or container need this when it's production
        logging.info("Add scheduled jobs for production env.")
        scheduler.add_job(id='Scheduled SPP Upgrade Task', func=scan_spp_task, trigger="interval", minutes=30,
                        next_run_time=datetime.now())
        scheduler.add_job(id='Scheduled AOS Upgrade Task', func=scan_aos_task, trigger="interval", minutes=30,
                          next_run_time=datetime.now())
        scheduler.add_job(id='Scheduled Move Data Sync Task', func=scan_move_datasynctask, trigger="interval", minutes=30,
                          next_run_time=datetime.now())
        scheduler.add_job(id='Scheduled WH Move Data Sync Task', func=scan_whmove_datasynctask, trigger="interval", minutes=6,
                          next_run_time=datetime.now())
        # Below job must run `python app.py` by admin!
        scheduler.add_job(
            id='Incident handler scheduler task',
            func=IncidentDevourer().trigger_incident_devourer,
            trigger="interval",
            minutes=30,
            next_run_time=datetime.now()
        )
        scheduler.add_job(id='reminder', func=schedule_time, trigger="cron",     
                        day='*', hour='8', minute='0', timezone=timezone('Asia/Shanghai'))
        scheduler.add_job(id='Incident Analysis', func=IncidentAnalysis().scheduled_batch, trigger='cron',
                          day='*', hour='9', minute='11', timezone=timezone('Asia/Shanghai'))
        
        scheduler.add_job(id='Scheduled auto maintenance stuck tasks', func=scan_automaintenancetask_stuck_tasks,
                          trigger="cron", day_of_week='sat', hour=12, minute=0, timezone=timezone('Asia/Shanghai'))
        
        # Starts every collector with 24 hour interval at 00:00
        scheduler.add_job(id='Scheduled Data Fetch Task', func=scan_collector_task, trigger="cron",
                  day='*', hour=0, minute=0, timezone=timezone('Asia/Shanghai'))
        
    if gethostname() == "NTXSEELM-NT3000":
        # container won't need this
        logging.info("Add scheduled jobs for windows production env.")
        from dpcdata.ntx.jobs.scripts.TaskSchedulerHandler import TaskSchedulerHandler
        handler = TaskSchedulerHandler()
        scheduler.add_job(id='Register Windows Scheduler Task', func=handler.register_tasks_from_config, trigger="cron",
                  day='*',
                  hour='*/12', minute='0')
    
    
    scheduler.start()

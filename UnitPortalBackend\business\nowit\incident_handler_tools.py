"""
╔═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
║Module: incident handler tools                                                                                                         ║
║                                                                                                                                       ║
║Author:         Incident Devourer                                                                                                      ║
║Created:        2024-08-01                                                                                                             ║
║Last Updated:   2024-08-16                                                                                                             ║
║                                                                                                                                       ║
║Description:                                                                                                                           ║
║This module provides the tools for handling various incident scenarios                                                                 ║
║                                                                                                                                       ║
║Usage:                                                                                                                                 ║
║Various methods provided>>                                                                                                             ║
║   - Get current time                                                                                                                  ║
║        Returns the current date and time in "%D:%H:%M:%S" format.                                                                     ║
║    - Calculate new time                                                                                                               ║
║        Calculates a new time by subtracting a given time difference from a date string.                                               ║
║    - Convert CET to microseconds                                                                                                      ║
║        Converts a CET time string to microseconds since the Unix epoch.                                                               ║
║    - Convert microseconds to CET                                                                                                      ║
║        Converts microseconds since the Unix epoch to CET time                                                                         ║
║    - Calculate new time duration                                                                                                      ║
║        Calculates new time durations for incident creation time.                                                                      ║
║    - Ping ip address                                                                                                                  ║
║    - Create incident handler log                                                                                                      ║
║        Write in incident handler process related logs                                                                                 ║
║    - Create incident handler data                                                                                                     ║
║    - Commit incident handler date to DB                                                                                               ║
║    - Commit incident handler log to DB                                                                                                ║
║    - Assign incident and write log to DB                                                                                              ║
║    - Incident assignment payload                                                                                                      ║
║    - Resolved incidents payload                                                                                                       ║
║    - Unassign incident                                                                                                                ║
║    - Get incident handler(NowIT) token                                                                                                ║
║    - Get GDH active incidents                                                                                                         ║
║    - Get GDH active incidents without assignee                                                                                        ║
║    - Assign GDH incident                                                                                                              ║
║    - Unassign GDH incident                                                                                                            ║
║    - Create worknote for resolved incident                                                                                            ║
║    - <PERSON>reate worknote for WTP host connection                                                                                          ║
║    - Incident terminator                                                                                                              ║
║        Resolve incidents                                                                                                              ║
║    - Get Nutanix cluster alerts                                                                                                       ║
║    - Get number of Nutanix cluster alerts                                                                                             ║
║    - Handle 'null' prefix incidents                                                                                                   ║
║    - Handle resolved Nutanix alert                                                                                                    ║
║    - Handle unresolved Nutanix alert                                                                                                  ║
║    - Common process for handling Nutanix cases                                                                                        ║
║    - Get number of historical Nutanix alerts                                                                                          ║
║    - handle_unresolved_host_critical_alert                                                                                            ║
║    - get_nx_host_info                                                                                                                 ║
║    - iLO_power_status                                                                                                                 ║
║                                                                                                                                       ║                                                                                                         ║
║Update Logs:                                                                                                                           ║
║-[2024/8/16]-[IVFAN]-[Added 'Get number of historical Nutanix alerts']                                                                   ║
║-[2024/8/27]-[IVFAN]-[Added 'Handle nutanix host unreachable critical issue']                                                          ║
║             -[EJ] - [Added 'get_nx_host_info']                                                                                        ║
║             -[EJ] - [Added 'iLO_power_status']                                                                                        ║
╚═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
"""
import os
import uuid

import requests
import base64
import datetime
import socket
import re
import logging
import subprocess
import paramiko
import time
from sqlalchemy import desc
from requests.auth import HTTPBasicAuth
from datetime import timedelta, timezone, datetime
from models.database import db
from business.nowit.incident_handler_config import IncHandlerCfg
from models.incident_models import IncidentHandlerData, IncidentHandlerLog, IncidentHandlerUnresolvedCases, IncidentHandlerErrorLog, IncidentHandlerUnitPortalData
from business.authentication.authentication import ServiceAccount, Vault
from base_path import application_path
from ..generic.commonfunc import setup_common_logger
import pytz


class IncidentHandlerTools:
    def __init__(self) -> None:
        self.logger = logging.getLogger("Incident_Handler")
        self.token_request_payload = {}
        self.sa           = ServiceAccount()
        self.nx_account   = self.sa.get_service_account(usage='nutanix_pm')
        self.nowit_account = self.sa.get_service_account(usage='nowit_api')
        self.wtp_ilo_account = self.sa.get_service_account(usage='wtp_ilo')
        self.conluence_api_account = self.sa.get_service_account(usage='confluence_sa')
        self.nowit_username = self.nowit_account['username']
        self.nowit_password = self.nowit_account['password']
        self.token_request_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/login'
        self.gdh_team_active_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/incident?sysparm_query=active=true%5Eassignment_group=321458b5878c099071b9a8a90cbb3521%5EstateNOT%20IN6,7,8'
        self.gdh_team_active_no_assignee_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/incident?sysparm_query=active=true%5Eassignment_group=321458b5878c099071b9a8a90cbb3521%5EstateNOT%20IN6,7,8%5Eassigned_toISEMPTY%5Esys_created_by=l-splunkno-u-itsemal'
        self.gdh_team_all_active_no_assignee_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/incident?sysparm_query=active=true%5Eassignment_group=321458b5878c099071b9a8a90cbb3521%5EstateNOT%20IN6,7,8%5Eassigned_toISEMPTY'
        self.incident_basic_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/incident/'
        self.get_gdh_event_breached_incidents_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/incident_sla?sysparm_query=inc_assignment_group%3D321458b5878c099071b9a8a90cbb3521^inc_active=true^taskslatable_has_breached=true^taskslatable_active=true^taskslatable_slaLIKESLA%20Event^inc_stateIN1%2C2%2C3'
        self.get_gdh_user_std_breached_incidents_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/incident_sla?sysparm_query=inc_assignment_group%3D321458b5878c099071b9a8a90cbb3521^inc_active=true^taskslatable_has_breached=true^taskslatable_active=true^taskslatable_slaLIKESLA%20std^inc_stateIN1%2C2%2C3'
        self.get_gdh_user_internal_breached_incidents_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/incident_sla?sysparm_query=inc_assignment_group%3D321458b5878c099071b9a8a90cbb3521^inc_active=true^taskslatable_has_breached=true^taskslatable_active=true^taskslatable_slaLIKESLA%20Internal^inc_stateIN1%2C2%2C3'
        self.get_gdh_ritm_task_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/sc_req_item_sla?sysparm_query=sci_assignment_group=321458b5878c099071b9a8a90cbb3521^sci_active=true^taskslatable_slaLIKEGeneric%20SLA%20REQ%20RSV^taskslatable_stage%3Din_progress'
        self.get_gdh_ctask_url = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/change_task?sysparm_query=assignment_group%3D321458b5878c099071b9a8a90cbb3521^active=true'
        self.get_gdh_ctask_url_no_pm = 'https://prod-nowit-wrapper-ops-intel-aiops.gke.ingka.com/api/v1/change_task?sysparm_query=assignment_group%3D321458b5878c099071b9a8a90cbb3521^active=true^short_descriptionNOT%20LIKEPOWER%20DOWN^short_descriptionNOT%20LIKEPOWER%20ON'

        self.headers = {
            "x-access-token": self.get_incident_hanlder_token()
        }
        self.auth = f"{self.nx_account['username']}:{self.nx_account['password']}"
        self.nowit_auth = f"{self.nowit_account['username']}:{self.nowit_account['password']}"
        self.pe_cluster_sa_credential_bb64 = base64.b64encode(self.auth.encode()).decode('utf-8')
        self.nx_request_headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "Authorization": f"Basic {self.pe_cluster_sa_credential_bb64}"
        }

        incident_handler = os.path.join(application_path, "Log", "incidentHandler")
        self.logger = setup_common_logger(str(uuid.uuid4()), incident_handler + "\\IncidentHandlerLog")

    def get_current_cet_date(self):
        cet_timezone = pytz.timezone('CET')
        cet_time = datetime.now(cet_timezone)
        current_date = cet_time.strftime('%Y-%m-%d')
        return current_date

    def current_date(self):
        return datetime.now(pytz.timezone('CET')).strftime('%Y-%m-%d')

    def get_current_time(cls):
        current_time = datetime.now(pytz.timezone('CET')).strftime("%D:%H:%M:%S")
        return  current_time

    def calculate_new_time(self, date_string, time_diff, unit):
        time = datetime.strptime(date_string, '%Y-%m-%d %H:%M:%S')
        if unit == 'minutes':
            new_time = time-timedelta(minutes=time_diff)
        elif unit == 'day':
            new_time = time-timedelta(days=time_diff)
        elif unit == 'week':
            new_time = time-timedelta(weeks=time_diff)
        else:
            return None
        return new_time.strftime('%Y-%m-%d %H:%M:%S')
    
    def cet_to_microseconds(self, date_string):
        cet_timezone = pytz.timezone('CET')
        cet_time = cet_timezone.localize(datetime.strptime(date_string, '%Y-%m-%d %H:%M:%S'))
        utc_time = cet_time.astimezone(pytz.utc)
        microseconds = int((utc_time - datetime(1970, 1, 1, tzinfo=pytz.utc)).total_seconds() * 1e6)
        return microseconds

    def microseconds_to_cet(self, microseconds):
        seconds = microseconds / 1e6
        dt = datetime.fromtimestamp(seconds, tz=timezone.utc)
        cet_dz = dt.astimezone(timezone(timedelta(hours=2)))
        return cet_dz
    
    def calculate_new_time_duration(self, incident_creation_time):
        new_time_minus_1_day = self.calculate_new_time(incident_creation_time, 1, 'day')
        new_time_minus_1_week = self.calculate_new_time(incident_creation_time, 1, 'week')
        start_time_minus_1_day = self.cet_to_microseconds(new_time_minus_1_day)
        start_time_minus_1_week = self.cet_to_microseconds(new_time_minus_1_week)
        end_time = self.cet_to_microseconds(incident_creation_time)
        return end_time, start_time_minus_1_day, start_time_minus_1_week

    def pattern_match(self, text, patterns):
        matches = []
        for pattern in patterns:
            found = re.findall(pattern, text)
            matches.extend(found)
        return matches


    def resolve_ip(self, hostname):
        try:
            ip_address = socket.gethostbyname(hostname)
            return ip_address
        except socket.gaierror as e:
            return f"Error resolving IP address:{e}"
    
    def ping_ip(self, ip_address):
        command = ['ping', '-c', '1', ip_address]
        try:
            subprocess.check_output(command)
            return True
        except subprocess.CalledProcessError:
            return False
    
    def create_incident_handler_log(self, task_type, log_details, creation_time, severity='INFO'):
        incident_handler_log = {
            'task_type': task_type,
            'severity': severity,
            'log_details': log_details,
            'creation_time': creation_time 
        }
        return incident_handler_log
    
    def create_incident_handler_up_data(self, active_inc_num, resolved_inc_num, resolved_inc_today, unassigned_inc_num, handler_status, creation_time):
        incident_handler_up_data = {
            "active_inc_num": active_inc_num,
            "resolved_inc_num": resolved_inc_num,
            "resolved_inc_today": resolved_inc_today,
            "unassigned_inc_num": unassigned_inc_num,
            "handler_status": handler_status,
            "date": creation_time
        }
        return incident_handler_up_data
    
    def create_incident_handler_unresolved_cases(self, inc_num, orig_short_desc):
        incident_handler_unresolved_cases = {
            "number": inc_num,
            "description": orig_short_desc,
            "handle_date": self.current_date(),
        }
        return incident_handler_unresolved_cases
    
    def create_incident_handler_error_log(self, inc_num, orig_short_desc, error_message, current_time):
        incident_handler_error_log = {
            "number": inc_num,
            "description": orig_short_desc,
            "error": error_message,
            "handle_date": current_time,
        }
        return incident_handler_error_log   
    
    def create_incident_handler_data(self, number, description, work_note, task_type, creation_date):
        incident_handle_data = {
            "number": number,
            "description": description,
            "work_note": work_note,
            "task_type": task_type,
            "creation_date": creation_date
        }
        return incident_handle_data
    
    def commit_incident_handler_data(self, number, description, work_note, task_type):
        _log = self.create_incident_handler_data(number, description, work_note, task_type, self.current_date())
        db.session.add(IncidentHandlerData(**_log))
        db.session.commit()
    
    def commit_inc_handler_unresolved_cases(self, inc_num, orig_short_desc):
        _log = self.create_incident_handler_unresolved_cases(inc_num, orig_short_desc)
        db.session.add(IncidentHandlerUnresolvedCases(**_log))
        db.session.commit()
    
    def commit_inc_handler_error_log(self, inc_num, orig_short_desc, error_message, current_time = None):
        if current_time is None:
            current_time = self.get_current_time()
        _log = self.create_incident_handler_error_log(inc_num, orig_short_desc, error_message, current_time)
        db.session.add(IncidentHandlerErrorLog(**_log))
        db.session.commit()
    
    def commit_inc_handler_up_data(self, active_inc_num, resolved_inc_num, resolved_inc_today, unassigned_inc_num, handler_status, creation_time = None):
        if creation_time is None:
            creation_time = self.get_current_time()
        _log = self.create_incident_handler_up_data(active_inc_num, resolved_inc_num, resolved_inc_today, unassigned_inc_num, handler_status, creation_time)
        db.session.add(IncidentHandlerUnitPortalData(**_log))
        db.session.commit()
   
    def commit_incident_handler_log(self, task_type, log_details):
        _log = self.create_incident_handler_log(task_type, log_details, self.get_current_time())
        db.session.add(IncidentHandlerLog(**_log))
        db.session.commit()
    
    def assign_incident_and_write_log(self, incident_number, assignee, log_message):
        self.assign_gdh_incident(incident_number, assignee)
        self.commit_incident_handler_log(IncHandlerCfg.INC_ASSIGN, log_message)
      
    def incident_assignment_payload(self, assignee):
        incident_assignment_payload = {
            "data": {
                "assigned_to": assignee
            }
        }
        return incident_assignment_payload
    
    def resolved_incidents_payload(self, work_notes, orig_short_desc, svc_type):
        resolved_incidents_payload = {
            "data": {
                "state": IncHandlerCfg.INC_STATE_RESOLVED,
                "assigned_to": IncHandlerCfg.INC_ASSIGNEE,
                "short_description": orig_short_desc,
                "comments": work_notes,
                "close_code": "Solved (Permanently)",
                "u_cause_service": svc_type,
                "close_notes": work_notes,
                "resolved_by": IncHandlerCfg.INC_ASSIGNEE
            }
        }
        return resolved_incidents_payload
    
    def unassign_incident(self, new_description):
        unassign_incident = {
            "data": {
              "assigned_to": "",
              "short_description": new_description
            }
        }
        return unassign_incident
    
    def add_handling_prefix_payload(self, new_description):
        add_handling_prefix_payload = {
            "data": {
            "short_description": new_description
            }
        }
        return add_handling_prefix_payload
    
    def add_no_model_prefix(self, new_description):
        add_no_model_prefix = {
            "data": {
            "short_description": new_description
            }
        }
        return add_no_model_prefix
    
    def payload_convert_incident_to_critical(self, host_ip, new_short_despt):
        convert_incident_to_critical = {
            "data": {
                "assigned_to": "",
                "short_description": new_short_despt,
                "impact": "1",
                "urgency": "1",
                "comments": f"""Incident handler update>>
                The {host_ip} is still not reachable,adjusting the case priority to critical!
                """
            }
        }
        return convert_incident_to_critical
    
    def payload_adjust_inc_to_high(self, new_short_despt, comments):
        convert_inc_to_high = {
            "data": {
                "assigned_to": "",
                "impact": "2",
                "urgency": "1",
                "short_description": new_short_despt,
                "comments": f"""Incident handler update>>
                {comments}"""
            }
        }
        return convert_inc_to_high
        
    def get_incident_hanlder_token(self):
        max_tries = 5
        proxies = {
            "http": None,
            "https": None,
        }
        for _ in range(max_tries):
            try:
                response = requests.post(
                    self.token_request_url,
                    data=self.token_request_payload,
                    auth=HTTPBasicAuth(self.nowit_username, self.nowit_password),
                    verify=False,
                    proxies=proxies,
                    timeout=10
                    )
                if response.status_code == 200:           
                    token = response.text
                    return token
                self.commit_inc_handler_error_log("Get NowIT Token", "Failed", f"NowIT token request failed:{response.status_code}")
            except ValueError as e:
                self.commit_inc_handler_error_log("Get NowIT Token", "Failed", f"Error is {e}")
            # except requests.RequestException as e:
            #     self.commit_inc_handler_error_log("Get NowIT token","Failed","Error is {e}")
            time.sleep(5)
        return None
    
    def get_gdh_active_incidents(self):
        get_gdh_active_incidents = requests.request('GET', headers=self.headers, url=self.gdh_team_active_url, verify=False)
        number_active_incidents = len(get_gdh_active_incidents.json()['result'])
        return number_active_incidents
    
    def get_gdh_all_active_incidents(self):
        get_gdh_all_active_incidents = requests.request('GET', headers=self.headers, url=self.gdh_team_active_url, verify=False)
        return get_gdh_all_active_incidents.json()['result']
    
    def get_gdh_active_incidents_no_assignee(self):
        max_retries = 5
        for attempt in range(max_retries):
            response = requests.request('GET', headers=self.headers, url=self.gdh_team_active_no_assignee_url, verify=False)
            if response.status_code == 200:
                try:
                    result = response.json().get('result', [])
                    return result if isinstance(result, list) else []
                except ValueError as e:
                    self.commit_inc_handler_error_log("Get no_assignee_incidents", "Failed", f"ERROR is {e}")
            else:
                self.commit_inc_handler_error_log("Get no assignee incidents", "Failed", f"Request failed with status code: {response.status_code}, retry {attempt + 1}")
            time.sleep(5)

        self.commit_inc_handler_error_log("Get no_assigneed_incidents", "Failed", "Max retried(5) reached. Returning empty list!")
        return []
    
    def get_gdh_all_active_incidents_no_assignee(self):
        get_gdh_all_active_incidents_no_assignee = requests.request('GET', headers=self.headers, url=self.gdh_team_all_active_no_assignee_url, verify=False)
        return get_gdh_all_active_incidents_no_assignee.json()['result']
    
    def get_gdh_event_breached_incidents(self):
        get_gdh_event_breached_incidents = requests.request('GET', headers=self.headers, url=self.get_gdh_event_breached_incidents_url, verify=False)
        return get_gdh_event_breached_incidents.json()['result']
    
    def get_gdh_user_std_breached_incidents(self):
        get_gdh_user_std_breached_incidents = requests.request('GET', headers=self.headers, url=self.get_gdh_user_std_breached_incidents_url, verify=False)
        return get_gdh_user_std_breached_incidents.json()['result']
    
    def get_gdh_user_internal_breached_incidents(self):
        get_gdh_user_internal_breached_incidents = requests.request('GET', headers=self.headers, url=self.get_gdh_user_internal_breached_incidents_url, verify=False)
        return get_gdh_user_internal_breached_incidents.json()['result']
    
    def get_gdh_ritm_task(self):
        get_gdh_ritm_task = requests.request('GET', headers=self.headers, url=self.get_gdh_ritm_task_url, verify=False)
        return get_gdh_ritm_task.json()['result']
    
    def get_gdh_ctask(self):
        get_gdh_ctask = requests.request('GET', headers=self.headers, url=self.get_gdh_ctask_url, verify=False)
        return get_gdh_ctask.json()['result']    
    
    def get_gdh_ctask_no_pm(self):
        get_gdh_ctask_no_pm = requests.request('GET', headers=self.headers, url=self.get_gdh_ctask_url_no_pm, verify=False)
        return get_gdh_ctask_no_pm.json()['result']   

    def get_confluence_task_api_url(self, current_date_cet_formatted):
        confluence_request_url = (
            'https://confluence.build.ingka.ikea.com/rest/calendar-services/1.0/calendar/events.json?'
            'subCalendarId=fa02a486-260e-4274-a2af-2f4740516a60&'
            'userTimeZoneId=UTC&'
            f'start={current_date_cet_formatted}T00%3A00%3A00Z&'
            f'end={current_date_cet_formatted}T00%3A00%3A00Z&'
            '_=*************'
        )
        '''
        e.g.
            'https://confluence.build.ingka.ikea.com/rest/calendar-services/1.0/calendar/events.json?'
            'subCalendarId=fa02a486-260e-4274-a2af-2f4740516a60&'
            'userTimeZoneId=UTC&'
            'start=2025-02-17T00%3A00%3A00Z&'
            'end=2025-02-17T00%3A00%3A00Z&'
            '_=*************'
        '''
        get_confluence_calendar_task_name = requests.request('GET',
                url=confluence_request_url,
                auth=HTTPBasicAuth(self.conluence_api_account['username'], self.conluence_api_account['password']),
                verify=False)
        return get_confluence_calendar_task_name.json()['events'][0]
    
    def get_confluence_inc_api_url(self, current_date_cet_formatted):
        confluence_request_url = (
            'https://confluence.build.ingka.ikea.com/rest/calendar-services/1.0/calendar/events.json?'
            'subCalendarId=0370a302-b418-4dbc-8da0-ea2a3516c5b9&'
            'userTimeZoneId=UTC&'
            f'start={current_date_cet_formatted}T00%3A00%3A00Z&'
            f'end={current_date_cet_formatted}T00%3A00%3A00Z&'
            '_=*************'
        )
        get_confluence_calendar_inc_name = requests.request('GET',
                url=confluence_request_url,
                auth=HTTPBasicAuth(self.conluence_api_account['username'], self.conluence_api_account['password']),
                verify=False)
        return get_confluence_calendar_inc_name.json()['events'][0]
    
    def get_confluence_duty_api_url(self, current_date_cet_formatted):
        confluence_request_url = (
            'https://confluence.build.ingka.ikea.com/rest/calendar-services/1.0/calendar/events.json?'
            'subCalendarId=3b10b657-3856-4c70-9efd-24ee7f147293&'
            'userTimeZoneId=UTC&'
            f'start={current_date_cet_formatted}T00%3A00%3A00Z&'
            f'end={current_date_cet_formatted}T00%3A00%3A00Z&'
            '_=*************'
        )
        get_confluence_calendar_duty_name = requests.request('GET',
                url=confluence_request_url,
                auth=HTTPBasicAuth(self.conluence_api_account['username'], self.conluence_api_account['password']),
                verify=False)
        return get_confluence_calendar_duty_name.json()['events'][0]

    def get_unassigned_incidents(self):
        _response = requests.request('GET', headers=self.headers, url=self.gdh_team_active_no_assignee_url, verify=False)
        if _response.status_code == 200:
            try:
                return len(_response.json()['result'])
            except ValueError as e:
                print(f"Error{e}")
                return None
        else:
            return None
        
    def assign_gdh_incident(self, number, assignee):
        _payload = self.incident_assignment_payload(assignee=assignee)
        _url = self.incident_basic_url+number
        for _ in range(3):
            try:
                response = requests.put(headers=self.headers, url=_url, json=_payload, verify=False)
                if response.status_code == 200:
                    print(f"{number} is unassigned successfully!")
                    return response.json()
            except requests.RequestException as e:
                print(f"request fialed {e}")
        print(f"{number} unassign failed with 3 times try!")
        return None
        
    def unassign_gdh_incident(self, number, new_dscription):
        _payload = self.unassign_incident(new_dscription)
        _url = self.incident_basic_url+number
        requests.put(headers=self.headers, url=_url, json=_payload, verify=False)
    
    def add_prefix_handling(self, inc_num, orig_short_desc):
        handling_prefix_desc = "[Handling] "+orig_short_desc
        payload = self.add_handling_prefix_payload(handling_prefix_desc)
        url = self.incident_basic_url+inc_num
        requests.put(headers=self.headers, url=url, json=payload, verify=False)
        self.commit_incident_handler_log("INC_HANDLING", f"{inc_num}--Incident-Handler is handling the case!")
    
    def add_prefix_no_model(self, number, new_description):
        no_model_prefix_desc = "[No Model] "+new_description
        payload = self.add_no_model_prefix(no_model_prefix_desc) 
        url = self.incident_basic_url+number
        requests.put(headers=self.headers, url=url, json=payload, verify=False)
    
    def unassign_gdh_host_critical_incident(self, number, host_ip, new_short_despt):
        # Convert the incident details to a payload for critical incidents
        _payload = self.payload_convert_incident_to_critical(host_ip, new_short_despt)
        # Construct the URL for the API request
        _url = self.incident_basic_url + number

        try:
            # Send a PUT request to the specified URL with the payload
            response = requests.put(headers=self.headers, url=_url, json=_payload, verify=False)
            # Raise an error for bad HTTP responses (4XX or 5XX)
            response.raise_for_status()  
            # Return the JSON content of the response
            return response.json()   
        except Exception as e:
            # Handle any other exceptions
            print(f"An error occurred: {e}")
            self.logger.error(f"An error occurred: {e}")
    
    #Update-20241120-Removed host_ip for now
    def unassign_inc_and_promote_to_high(self, number, new_short_despt, comments):
        _payload = self.payload_adjust_inc_to_high(new_short_despt, comments)
        _url = self.incident_basic_url+number
        requests.put(headers=self.headers, url=_url, json=_payload, verify=False)
        
    def worknote(self, auto_resolve, resolved_by_username, resolved_time):
        worknote = f"""GDH_update(handler)>>
        
        The Nutanix alert has been resolved;hence,closing the case.
        Auto_resolved is {auto_resolve}
        Resolved_by_username:{resolved_by_username}
        Resolved_time_CET:{resolved_time}
        """
        return worknote
    
    def pc_resolved_alert_worknote(self, pe_uuid, pc_add):
        worknote = f"""GDH update(handler)>>
        
        No active alerts found based on the pe:{pe_uuid} on pc:{pc_add}.
        Incident-Handler treats it as resolved.
        Closing the case.
        """
        return worknote
    
    def wtp_host_connection_worknote(self, host_ip, host_name):
        wtp_host_connection_worknote = f"""GDH update>>
        The {host_name} - {host_ip} is reachable now.
        It's a temporary connection issue, closing the case.
        """
        return wtp_host_connection_worknote
    
    def incident_terminator(self, number, work_notes, orig_short_desc, svc_type):
        _payload = self.resolved_incidents_payload(work_notes=work_notes, orig_short_desc=orig_short_desc, svc_type=svc_type)
        _url = self.incident_basic_url+number
        _resolved_gdh_incident = requests.put(headers=self.headers, url=_url, json=_payload, verify=False)
        return(_resolved_gdh_incident.status_code)
    
    def transfer_inc_payload(self, state, impact, urgency, work_notes, assign_gp, new_short_despt):
        transfer_inc_payload = {
            "data": {
                "short_description": new_short_despt,
                "state": state,
                "impact": impact,
                "urgency": urgency,
                "work_notes": work_notes,
                "assignment_group": assign_gp,
            }
        }
        return transfer_inc_payload

    def incident_transfer(self, number, state, impact, urgency, work_notes, assign_gp, new_short_despt):
        _payload = self.transfer_inc_payload(state, impact, urgency, work_notes, assign_gp, new_short_despt)
        _url = self.incident_basic_url + number
        _transfer_gdh_inc = requests.put(headers=self.headers, url=_url, json=_payload, verify=False)
        return (_transfer_gdh_inc.status_code)

    def add_workload_payload(self, worknotes):
        add_notes_payload = {
            "data": {
                "work_notes": worknotes,
            }
        }
        return add_notes_payload
    
    def inc_add_worknotes(self, number, worknotes):
        _payload = self.add_workload_payload(worknotes)
        _url = self.incident_basic_url + number
        _transfer_gdh_inc = requests.put(headers=self.headers, url=_url, json=_payload, verify=False)
        return (_transfer_gdh_inc.status_code)

    def get_nutanix_host(self, fqdn):
        _url = f"https://{fqdn}.ikea.com:9440/PrismGateway/services/rest/v2.0/hosts"
        _get_info = requests.get(headers=self.nx_request_headers, url=_url, verify=False)
        return _get_info.json()

    def get_nutanix_cluster_alerts(self, pe, alert_type_uuid):
        _url = f"https://{pe}.ikea.com:9440/api/nutanix/v2.0/alerts/?alert_type_uuid={alert_type_uuid}"
        try:
            response = requests.get(headers=self.nx_request_headers, url=_url, verify=False)
            response.raise_for_status()  

            return response.json() 

        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err}")
            return {"error": "HTTP error", "message": str(http_err)}
        except requests.exceptions.RequestException as req_err:
            print(f"Request error occurred: {req_err}")
            return {"error": "Request error", "message": str(req_err)}
        except ValueError as json_err:
            print(f"JSON decoding error: {json_err}")
            return {"error": "JSON error", "message": str(json_err)}

    def get_unresolved_nutanix_cluster_alerts(self, pe, alert_type_uuid):
        _url = f"https://{pe}.ikea.com:9440/api/nutanix/v2.0/alerts/?resolved=false&alert_type_uuid={alert_type_uuid}"
        try:
            response = requests.get(headers=self.nx_request_headers, url=_url, verify=False)
            response.raise_for_status()  # 检查 HTTP 错误
            return response.json()  # 如果成功，返回 JSON 响应
        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err}")
            return {"error": "HTTP error", "message": str(http_err)}
        except requests.exceptions.RequestException as req_err:
            print(f"Request error occurred: {req_err}")
            return {"error": "Request error", "message": str(req_err)}
        except ValueError as json_err:
            print(f"JSON decoding error: {json_err}")
            return {"error": "JSON error", "message": str(json_err)}

    def post_resolve_alerts(self, pe, alert_id):
        _url = f"https://{pe}.ikea.com:9440/api/nutanix/v2.0/alerts/{alert_id}/resolve"
        payload = """{}"""
        _post_resolve_alert = requests.post(headers=self.nx_request_headers, url=_url, json=payload, verify=False)
        return _post_resolve_alert.status_code

    def post_resolve_pc_alerts(self, pc_add, alert_id_list):
        _url = f"https://{pc_add}:9440/api/nutanix/v3/alerts/action/RESOLVE"
        payload = {
            "alert_uuid_list": [f"{alert_id_list}"]
        }
        try:
            _post_resolve_pc_alert = requests.post(headers=self.nx_request_headers, url=_url, json=payload, verify=False)
            json_data = _post_resolve_pc_alert.json()
            return json_data['task_uuid']
        except requests.exceptions.HTTPError as http_err:
            return {"error": "HTTP error", "message": str(http_err)}
        except requests.exceptions.RequestException as req_err:
            return {"error": "Request error", "message": str(req_err)}
        except ValueError as json_err:
            return {"error": "JSON error", "message": str(json_err)}

    def get_nutanix_cluster_alerts_number(self, pe, start_time, end_time, alert_type_uuid):
        
        # 构建 URL 
        _url = f"https://{pe}.ikea.com:9440/api/nutanix/v2.0/alerts/?start_time_in_usecs={start_time}&end_time_in_usecs={end_time}&alert_type_uuid={alert_type_uuid}"

            # 发送请求
        _get_alerts = requests.get(headers=self.nx_request_headers, url=_url, verify=False)

        # 检查响应状态码
        if _get_alerts.status_code == 200:
            try:
                # 尝试解析 JSON
                return len(_get_alerts.json().get('entities', []))
            except ValueError as e:
                print(f"JSON decode error: {e}")
                print(f"Response Text: {_get_alerts.text}")
                return 0  # 或者根据需要处理错误
        else:
            print(f"Error { _get_alerts.status_code }: { _get_alerts.text }")
            return 0  # 或者根据需要处理错误
    
    def handle_null_pe(self, incident_number, description, svc_type):
        lack_info_case_worknotes = f"The case {incident_number} misses the key information - Nutanix cluster, close the bad case."
        self.incident_terminator(incident_number, lack_info_case_worknotes, description, svc_type)
        self.commit_incident_handler_data(incident_number, description, lack_info_case_worknotes, task_type='INC_RESOLVED')
        self.commit_incident_handler_log(IncHandlerCfg.INC_RESOLVED, f"{incident_number} is resolved successfully!")
        self.logger.info(f"{incident_number} is resolved successfully!")
        
    def handle_resolved_alert(self, incident_number, latest_alert, orig_short_desc, svc_type):
        self.commit_incident_handler_log(IncHandlerCfg.NX_ALERT_CHK, f"{incident_number}-{orig_short_desc} was resolved!")
        self.logger.info(f"{incident_number}-{orig_short_desc} was resolved!")
        auto_resolve = latest_alert['auto_resolved']
        resolved_by_username = latest_alert['resolved_by_username']
        resolved_time_usecs = latest_alert['resolved_time_stamp_in_usecs']
        resolved_time_cet = self.microseconds_to_cet(resolved_time_usecs)
        worknotes = self.worknote(auto_resolve, resolved_by_username, resolved_time_cet)
        self.incident_terminator(incident_number, worknotes, orig_short_desc, svc_type)
        self.commit_incident_handler_data(incident_number, orig_short_desc, worknotes, task_type='INC_RESOLVED')
        self.commit_incident_handler_log(IncHandlerCfg.INC_RESOLVED, f"{incident_number} is resolved successfully!")
        self.logger.info(f"{incident_number} is resolved successfully!")
    
    #Here the description means incident short description
    def handle_unresolved_alert(self, incident_number, alerts_number_description, description):
        original_description = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, description).group(1)
        new_short_description = IncHandlerCfg.NX_INC_SHORT_DESC_PREFIX + alerts_number_description + original_description
        self.unassign_gdh_incident(number=incident_number, new_dscription=new_short_description)
        self.commit_incident_handler_log(IncHandlerCfg.INC_UNASSIGN, f"{incident_number} is unassigned with {new_short_description}")
        self.logger.info(f"{incident_number} is unassigned with {new_short_description}")
    
    def handle_unresolved_pc_alert(self, inc_num, orig_desc):
        # orig_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, desc).group(1)
        new_short_desc = "[Human] " + orig_desc
        self.unassign_gdh_incident(number=inc_num, new_dscription=new_short_desc)
        self.commit_incident_handler_log(IncHandlerCfg.INC_UNASSIGN, f"{inc_num} is unassigned with {new_short_desc}")
    
    def nutanix_case_handle_common_process(self, incident_number, pe, orig_short_desc, alert_type_uuid, svc_type):
        if pe == 'null':
            self.handle_null_pe(incident_number, orig_short_desc, svc_type)
            return
        try:
            run_check_nx_alerts = self.get_nutanix_cluster_alerts(pe, alert_type_uuid)
        except Exception as e:
            self.logger.error(f"Error getting Nutanix cluster alerts: {e}")
            return None
        entities = run_check_nx_alerts.get('entities', [])
        if not entities:
            self.logger.error(f"No alerts found for incident {incident_number} with PE {pe}.")
            return None  
        latest_alert = entities[0]
        return latest_alert

    def get_active_alert_info_pc(self, pc_add, alert_id):
        pc_alert_url = f"https://{pc_add}:9440/api/nutanix/v3/alerts/list"
        _payload = {
            "kind": "alert",
            "offset": 0,
            "filter": f"type_id=={alert_id};resolved==false"
        }
        for i in range(3):
            try:
                res = requests.post(headers=self.nx_request_headers, url=pc_alert_url, json=_payload, verify=False)
                alerts_info_dict = {}
                for entity in res.json().get('entities', []):
                    try:
                        cluster_uuid = entity['status']['resources']['source_entity']['cluster_uuid']
                        alert_uuid = entity['metadata']['uuid']
                        alerts_info_dict[cluster_uuid] = alert_uuid
                    except KeyError:
                        print(f"KeyError: {entity}")
                return alerts_info_dict
            except requests.exceptions.HTTPError as http_err:
                print(f"HTTP error occurredon attempt{i+1}: {http_err}")
            except Exception as e:
                print(f"Error occurred on attempt{i+1}: {e}")
        return None

    def get_nutanix_history_alerts_number(self, pe, start_time_minus_1_day, start_time_minus_1_week, end_time, alert_type_uuid):
        run_check_nx_alerts_number_1_day = self.get_nutanix_cluster_alerts_number(pe=pe, start_time = start_time_minus_1_day, end_time = end_time, alert_type_uuid=alert_type_uuid)
        run_check_nx_alerts_number_1_week = self.get_nutanix_cluster_alerts_number(pe=pe, start_time = start_time_minus_1_week, end_time = end_time, alert_type_uuid=alert_type_uuid)
        alerts_number_description = f"-{run_check_nx_alerts_number_1_day}/D/{run_check_nx_alerts_number_1_week}/W]-  "
        return alerts_number_description
    
    def get_last_repl_snap_id(self, pe):
        _url = f"https://{pe}.ikea.com:9440/PrismGateway/services/rest/v2.0/remote_sites"
        try:
            _get_info = requests.get(headers=self.nx_request_headers, url=_url, verify=False, timeout=15)
        except requests.exceptions.ConnectTimeout:
            print(f"Connection to {pe} timed out.")
            return None, 0 

        if _get_info.status_code == 200:
            try:
                for entity in _get_info.json()['entities']:
                    for replication_link in entity['replication_links']:
                        if pe.upper() in replication_link['id']:
                            last_repl_snap_id = replication_link['last_successful_replication_snapshot_id']      
                return last_repl_snap_id
            except ValueError as e:
                print(f"JSON decode error: {e}")
                print(f"Response Text: {_get_info.text}")
                return 0  
        else:
            print(f"Error { _get_info.status_code }: { _get_info.text }")
            return 0  


    def get_nutanix_cluster_detail(self, pe, cluster_uuid):
        _url = f"https://{pe}.ikea.com:9440/api/nutanix/v3/clusters/{cluster_uuid}"
        _get_info = requests.get(headers=self.nx_request_headers, url=_url, verify=False)
        return _get_info.json()
    
    def get_pd(self, pe, pd_name):
        _url = f"https://{pe}.ikea.com:9440/PrismGateway/services/rest/v2.0/protection_domains/{pd_name}"
        try:
            _get_info = requests.get(headers=self.nx_request_headers, url= _url, verify=False)
        except Exception as e:
            return False, e
        return True, _get_info.json()

    def get_unprotected_vms_in_pd(self, pe):
        _url = f"https://{pe}.ikea.com:9440/PrismGateway/services/rest/v2.0/protection_domains/unprotected_vms"
        try:
            _get_info = requests.get(headers=self.nx_request_headers, url= _url, verify=False)
        except Exception as e:
            return False, e
        return True, _get_info.json()

    def remove_vms_from_pd(self, pe, vm_uuid_list, pd_name):
        _url = f"https://{pe}.ikea.com:9440/PrismGateway/services/rest/v1/protection_domains/{pd_name}/remove_entities"
        payload = {"vmIds": vm_uuid_list}
        _post_remove_vms = requests.post(headers=self.nx_request_headers, url=_url, json=payload, verify=False)
        return _post_remove_vms.status_code

    def is_vm_in_pd(self, pe, vm_uuid, pd_name):
        flag, pd_info = self.get_pd(pe, pd_name)
        if flag:
            for vm in pd_info["vms"]:
                if vm.get("vm_id") == vm_uuid:
                    return True
        return False

    def add_vm_to_pd(self, pe, vm_uuid, pd_name):
        # check if vm already exists in pd
        if self.is_vm_in_pd(pe, vm_uuid, pd_name):
            return
        _url = f"https://{pe}.ikea.com:9440/PrismGateway/services/rest/v2.0/protection_domains/{pd_name}/protect_vms"
        payload = {"uuids": [vm_uuid]}
        _post_add_vms = requests.post(headers=self.nx_request_headers, url=_url, json=payload, verify=False)
        return _post_add_vms.status_code


    def reset_protection_domain(self, pe_name, pd_name, vm_name):
        """move the duplicated/not existed VM from protect domain to unprotect,
           then move VM to protect if VM exists in unprotect"""
        try:
            flag, all_vmdata = self.get_pd(pe_name, pd_name)
            if flag:
                # 2.get pd vm name lists in protected
                vmid_lists = [vm["vm_id"] for vm in all_vmdata["vms"] if vm["vm_name"] in vm_name]
            else:
                return False
            # 3.move protected vms to unprotected
            if vmid_lists:
                self.remove_vms_from_pd(pe=pe_name, vm_uuid_list=vmid_lists, pd_name=pd_name)
            # 4.get unprotected vm lists
            flag2, unprotected_vmdata = self.get_unprotected_vms_in_pd(pe_name)
            if flag2:
                unprotected_vmlist = [uuid['uuid'] for uuid in unprotected_vmdata["entities"]]
                if unprotected_vmlist:
                    # 5.add unprotected vms to protected""
                    for vmuuid in unprotected_vmlist:
                        self.add_vm_to_pd(pe_name, vmuuid, pd_name)
                return True
        except Exception as e:
            self.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"reset production domain failed: +{e}")
            return False
        
    def get_nx_host_info(self, pe, sn):
        """using NX Api(v2.0/hosts) to get the ilo ip by SN"""
        ilo_ip = None
        # map json data，found ilo ip according to SN
        try:
            host_data = self.get_nutanix_host(pe)
            ilo_info = host_data['entities']
            for item in ilo_info:
                if item['serial'] == sn:
                    ilo_ip = item['ipmi_address']
            return ilo_ip
        except Exception as e:
            print(f"Error getting ilo ip by SN: {e}")
            self.logger.error(f"Error getting ilo ip by SN: {e}")
            return None

    def get_nx_disk_info(self, pe, disk_sn):
        """using NX API 2.0 to get disk mounted status(True/False) by disk sn """
        try:
            host_data = self.get_nutanix_host(pe)
            new_data = host_data['entities']
            lenth = len(new_data)
            i = 0
            while i < lenth:
                disk_info = new_data[i].get('disk_hardware_configs')
                for id, item in disk_info.items():
                    print(id)
                    if item != None:
                        if item['serial_number'] == disk_sn:
                            return item['mounted']
                i += 1
            return False
        except Exception:
            return None

    def get_nx_ahv_version(self, pe, clusteruuid):
        """using NX API3.0 get cluster AHV version"""
        new_data = self.get_nutanix_cluster_detail(pe, clusteruuid)
        try:
            version_data = new_data.get('status', {}).get('resources', {}).get('nodes', {}).get('hypervisor_server_list', [])
            seen_versions = set()
            for server in version_data:
                if server['type'] == 'AHV' and server['version']:   # not null
                    seen_versions.add(server['version'])
            return seen_versions     #return type{'ahv sversion'}
        except Exception as e:
            self.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"sth wrong with get AOS version: +{e}")
            seen_versions = set()
            return seen_versions

    def handle_common_failed(self, inc_num, orig_short_desc, prefix=None, worknotes=None):
        new_short_description = prefix + orig_short_desc
        self.unassign_gdh_incident(number=inc_num, new_dscription=new_short_description)
        self.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"{inc_num} is unassigned with +{new_short_description}+{worknotes}")
        self.logger.error(f"{inc_num} is unassigned with +{new_short_description}+{worknotes}")

    def handle_common_resolve(self, inc_num, orig_short_desc, svc_type, worknotes=None):
        auto_resolve = 'True'
        resolved_by_username = '<EMAIL>'
        resolved_time_cet = self.get_current_time()
        if worknotes is None:
            worknotes = self.worknote(auto_resolve, resolved_by_username, resolved_time_cet)
        self.incident_terminator(inc_num, worknotes, orig_short_desc, svc_type)
        self.commit_incident_handler_data(inc_num, orig_short_desc, worknotes, task_type='INC_RESOLVED')
        self.commit_incident_handler_log(IncHandlerCfg.INC_RESOLVED, f"{inc_num} is resolved successfully!")
        self.logger.info(f"{inc_num} is resolved successfully!")

    def get_handled_inc(self):
        inc_list = IncidentHandlerUnresolvedCases.query.filter_by(handle_date=self.get_current_cet_date()).all()
        inc_ids = [inc.number for inc in inc_list]
        return inc_ids

    def get_vault_psd(self, secret, prod=None):
        if prod:
            namespace = "digital-warehouse/dhprod"
            _sa = ServiceAccount(usage='vault_warehouse')
            wiab_account = _sa.get_service_account()
            auth = Vault(sa=wiab_account, engine="NutanixClusters", namespace=namespace)
        else:
            auth = Vault()
        try:
            _, vault_data = auth.get_secret(secret)
            return vault_data['secret']
        except Exception as e:
            logging.error(f"error for getting vault psd: {e}")

    def common_ilo_power_state(self, host, username, password):
        ilo_url = f"https://{host}/redfish/v1/Chassis/1/Power"
        for i in range(5):
            print(f"try {i+1} times")
            _get_data = requests.get(ilo_url, auth=HTTPBasicAuth(username, password), verify=False)
            if _get_data.status_code in (200, 201, 202):
                return _get_data.json()
            time.sleep(3)
        return None

    def get_wtp_ilo_power_state(self, host):
        return self.common_ilo_power_state(host, self.wtp_ilo_account['username'], self.wtp_ilo_account['password'])

    def get_ntx_ilo_power_states(self, pe, ilo_ip):
        ilo_user = 'administrator'
        ilo_psd = self.get_vault_psd(f'{pe}/Site_Oob')
        return self.common_ilo_power_state(ilo_ip, ilo_user, ilo_psd)

    def run_remote_command(self, host, username, password, command, max_retries=2):
        """
        Remotely execute command, and returning result. Retries up to max_retries times if it fails.

        Args:
            host (str): The hostname or IP address of the remote server.
            username (str): The username used to authenticate with the remote server.
            password (str): The password used to authenticate with the remote server.
            command (str): The command to execute on the remote server.
            max_retries (int, optional): The maximum number of retry attempts. Default is 2.

        Returns:
            str: The output of the command, or an error message if it fails.
        """
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        retries = 0
        while retries <= max_retries:
            try:
                ssh.connect(hostname=host, username=username, password=password)
                stdin, stdout, stderr = ssh.exec_command(command)
                print(stdin)
                output = stdout.read().decode('utf-8')
                error = stderr.read().decode('utf-8')

                if error:
                    retries += 1
                    if retries > max_retries:
                        return f"Error after {max_retries + 1} attempts: {error}"
                else:
                    return output
            except Exception as e:
                retries += 1
                if retries > max_retries:
                    return f"Exception occurred after {max_retries + 1} attempts: {str(e)}"
            finally:
                ssh.close()

        return "Failed to execute command after multiple attempts."
            
    def host_transfer_cvm(self, hostname):
        """
            transfer RETxxxxx-NXC000(1) to RETxxxxx-nx7001cvm(nx7101cvm)
                RETSEHBG-NXP001(PC CLUSTER) to RETSEHBG-NXC000
        """
        try:
            endfix = re.search(IncHandlerCfg.CLUSTER_SUFFIX_PATTERN, hostname).group(1)
            match (endfix):
                case 'NXC000':
                    hostname = hostname.replace(endfix, 'nx7001cvm')
                case 'NXC001':
                    hostname = hostname.replace(endfix, 'nx7101cvm')
                case 'NXC002':
                    hostname = hostname.replace(endfix, 'nx7201cvm')
                case 'NXC003':
                    hostname = hostname.replace(endfix, 'nx7301cvm')
                case 'NXP001':
                    hostname = hostname.replace(endfix, 'NXC000')
                case 'NXP002':
                    hostname = hostname.replace(endfix, 'NXC001')
                case _:
                    errormsg = 'wrong hostname:'+hostname
                    return False, errormsg
            return True, hostname
        except Exception as error:
            return False, error

    def handle_pe_pc(self, host, ip):
        """
            host contains PE or PC, if PE(RETxxx-NXCOOO) we need transfer to cvm
            then ssh to this cluster, ping ip 9440 port
        """
        host = host.upper()
        flag, name = self.host_transfer_cvm(host)
        if 'NXC' in host:
            psd = self.get_vault_psd(f'{host}/Site_Pe_Nutanix')
        elif 'NXP' in host:  #PC
            psd = self.get_vault_psd(f'{name}/Site_Pc_Nutanix')
            name = host  # using 'retsehbg-nxp001'
        if flag:
            user = 'nutanix'
            cmd = f'nc -v {ip} 9440 -z'
            output = self.run_remote_command(host=name, username=user, password=psd, command=cmd)
            if 'Connected' in output:
                worknotes = 'good'
                return True, worknotes
            worknotes = 'failed connection'
            return False, worknotes
        return False, name

    def ecro_time_cal(self, time_str):
        time_obj = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
        current_time = datetime.now()
        time_difference = current_time - time_obj
        if time_difference > timedelta(hours=1):
            return False
        return True

    def ecro_handle(self, short_desc):
        parts = short_desc.split("RET")
        if len(parts) > 1:
            # get "RET" after parts
            after_ret = parts[1]
            # get site Countey code
            result = after_ret[:2]
        else:
            return False
        result = '-' + result
        group = [info for info in IncHandlerCfg.LIT_GROUP if result in info]  # get LIT group(list)
        if group:
            return group[0]
        return False

    def handle_cvm_disconnected(self, pe_name, cvmip1, cvmip2):
        """cvmip1 is the ip need check, ssh to cvmip2 to ping cvmip1"""
        user = IncHandlerCfg.NX_SSH_CVM_NAME
        psd = self.get_vault_psd(f'{pe_name}/Site_Pe_Nutanix')
        cmd = f'ping {cvmip1} -c 4'
        output = self.run_remote_command(host=cvmip2, username=user, password=psd, command=cmd)
        if '100%' not in output:  # not 100% packet loss,  means cvm is connected,close inc
            return True
        return False

    def common_model_handler(self, inc_num, pe_name, alert_uuid, orig_short_desc, inc_brief, svc_type):
        """
        this is a common func, for find nutanix alert.
        If alert resolved, close the incident
        """
        # self.assign_incident_and_write_log(inc_num, assignee, log_message=f"{inc_num} is assigned to {assignee}")
        find_alert = self.get_unresolved_nutanix_cluster_alerts(pe_name, alert_uuid)
        if 'error' not in find_alert:
            latest_alert = find_alert.get('entities')
            if latest_alert:
                alert_id = latest_alert[0]['id']
                return alert_id
            worknotes = """GDH update>>
            The NX alert already resolved,hence closing the inc"""
            self.incident_terminator(inc_num, worknotes, orig_short_desc, svc_type)
            self.commit_incident_handler_data(inc_num, orig_short_desc, worknotes, task_type='incident_resolved')
            self.commit_incident_handler_log(IncHandlerCfg.INC_RESOLVED,
                                                f"{inc_num} {inc_brief} resolved successfully!")
            self.logger.info(f"{inc_num} {inc_brief} resolved successfully!")
            return 'close'
        return find_alert
        
    def close_nx_inc_alert(self, inc_num, orig_short_desc, pe_name, alert_id, svc_type):
        """
        this is a common func, for closing existing nutanix alert
        """
        status_code = self.post_resolve_alerts(pe_name, alert_id)  # close NX alert
        if status_code in (200, 201, 202):  # close inc
            self.handle_common_resolve(inc_num, orig_short_desc, svc_type)
        else:  # NX resolve alert API failed
            prefix = IncHandlerCfg.NX_INC_SHORT_DES_PREFIX_HUMAN
            worknotes = 'resolve NTX alert failed'
            self.handle_common_failed(inc_num, orig_short_desc, prefix, worknotes)

    def del_cvm_logs(self, pe_name, cvm_ip):
        """
        sudo rm -rf /home/<USER>/data/installer/*
        sudo rm -rf /home/<USER>/tmp/*tar.gz
        sudo rm -rf /home/<USER>/software_downloads/nos/*
        sudo rm -rf /home/<USER>/software_downloads/hypervisor/*
        """
        user = IncHandlerCfg.NX_SSH_CVM_NAME
        psd = self.get_vault_psd(f'{pe_name}/Site_Pe_Nutanix')
        cmd1 = 'df -h'
        output1 = self.run_remote_command(host=cvm_ip, username=user, password=psd, command=cmd1)
        home_usage_str = re.search(IncHandlerCfg.NX_DISK_USAGE_PAT, output1).group(1)
        home_usage = int(home_usage_str)
        if home_usage >= 75:
            cmd2 = r'cd /home/<USER>/data/logs;find -type f \( -name "*nutanix.log*" \) -size +10M -delete'
            self.run_remote_command(host=cvm_ip, username=user, password=psd, command=cmd2)
            time.sleep(2)
            output2 = self.run_remote_command(host=cvm_ip, username=user, password=psd, command=cmd1)
            home_usage_str2 = re.search(IncHandlerCfg.NX_DISK_USAGE_PAT, output2).group(1)
            home_usage2 = int(home_usage_str2)
            return home_usage2
        return home_usage

    def get_hypervisor_ip(self, text, target_state):
        lines = text.splitlines()
        pattern = re.compile(f'{IncHandlerCfg.IP_PATTERN2}+{target_state}')
        ip_arr = []
        for line in lines:
            match = pattern.search(line)
            if match:
                ip_arr.append(match.group(1))
        return ip_arr

    def check_maintenance_mode(self, pe_name, cvm_ip):
        user = IncHandlerCfg.NX_SSH_CVM_NAME
        psd = self.get_vault_psd(f'{pe_name}/Site_Pe_Nutanix')
        cmd = '/usr/local/nutanix/bin/acli host.list'
        output = self.run_remote_command(host=cvm_ip, username=user, password=psd, command=cmd)
        target = "EnteredMaintenanceMode"
        ip_list = self.get_hypervisor_ip(output, target)
        hostlist = ''
        if ip_list:
            #one or more host in maintenance mode
            for ip in ip_list:
                try:
                    hostname = socket.gethostbyaddr(ip)
                    hostlist = hostlist + hostname[0] + '/'
                except socket.gaierror as e:
                    return False, e
                return False, hostlist
        else:
            return True, hostlist

    def get_resolved_inc_num(self):
        """
        get all resolved incidents from database
        """
        resolved_inc_num = len(IncidentHandlerData.query.filter_by().all()) - 147  # minus 147 is for the duplicate incidents
        return resolved_inc_num
    
    def get_resolved_inc_num_today(self):
        try:
            resolved_inc_today = IncidentHandlerData.query.filter_by(creation_date=self.current_date()).count()
            return resolved_inc_today
        except Exception as e:
            logging.error(f"Error occurred while retrieving resolved incidents for today: {e}")
            return 0
     
            
    def generate_unit_portal_incident_handler_data(self):
        """
        fetch last row of IncidentHandlerUnitPortalData table from database
        """
        fet_last_row = (IncidentHandlerUnitPortalData.query
        .order_by(desc(IncidentHandlerUnitPortalData.id))
        .first()
        )
        if fet_last_row is not None:
            active_inc_num = fet_last_row.active_inc_num
            resolved_inc_num = fet_last_row.resolved_inc_num
            resolved_inc_today = fet_last_row.resolved_inc_today
            unassigned_inc_num = fet_last_row.unassigned_inc_num
            unit_portal_needs_data = {
                "active_inc_num": active_inc_num,
                "resolved_inc_num": resolved_inc_num,
                "resolved_inc_today": resolved_inc_today,
                "unassigned_inc_num": unassigned_inc_num
            }
            return unit_portal_needs_data
        return None
    
    def common_handle_pc_alert_inc(self, 
                                   inc_short_desc, 
                                   inc_num, 
                                   inc_desc,
                                   alert_type,
                                   alert_type_uuid,
                                   svc_type,
                                   ncc_chk = None
                                   ):
        matched_message = f"Found target --{inc_num} is matched with {alert_type}!"
        if 'Unnamed' in inc_short_desc or 'null' in inc_short_desc:
            self.handle_null_pe(inc_num, inc_short_desc, svc_type)
            return
        self.commit_incident_handler_log(IncHandlerCfg.INC_CHK, matched_message)
        orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT,  inc_short_desc).group()
        pc_add = re.search(IncHandlerCfg.PC_ADDRESS_PATTERN, inc_desc).group(1)
        pe_uuid = re.search(IncHandlerCfg.PE_UUID_PATTERN, inc_desc).group(1)
        self.add_prefix_handling(inc_num, orig_short_desc)
        active_alert_info_dict = self.get_active_alert_info_pc(pc_add, alert_type_uuid)
        if active_alert_info_dict is None:
            raise ValueError("latest_alert is None")
        if pe_uuid not in active_alert_info_dict:
            worknotes = self.pc_resolved_alert_worknote(pe_uuid, pc_add)
            self.incident_terminator(inc_num, worknotes, orig_short_desc, svc_type)
            self.commit_incident_handler_data(inc_num, orig_short_desc, worknotes, task_type='INC_RESOLVED')
        else:
            if ncc_chk is not None:
                exec_ncc_check = self.exec_ncc_check(pc_add, pe_uuid, ncc_chk)
                print('wait for 30 seconds')
                time.sleep(30)
                print('timeis up')
                if exec_ncc_check is not None:
                    get_ncc_check_status = self.get_ncc_check_status(
                        pc_add,
                        exec_ncc_check,
                        pe_uuid
                        )
                    if get_ncc_check_status == 'kPass':
                        logging.info(f"NCC check{ncc_chk} executed successfully, start resolving NX alert!")
                        self.post_resolve_pc_alerts(pc_add, active_alert_info_dict[pe_uuid])
                        worknotes = self.pc_resolved_alert_worknote(pe_uuid, pc_add)
                        self.incident_terminator(inc_num, worknotes, orig_short_desc, svc_type)
                        self.commit_incident_handler_data(inc_num, orig_short_desc, worknotes, task_type='INC_RESOLVED')
            else:
                self.commit_inc_handler_unresolved_cases(inc_num, orig_short_desc)
                self.handle_unresolved_pc_alert(inc_num, orig_short_desc)
            
    def exec_ncc_check(self, pc_add, pe_uuid, ncc_chk):
        _url = f"https://{pc_add}:9440/PrismGateway/services/rest/v1/ncc/checks?proxyClusterUuid={pe_uuid}"
        _payload = {
            "sendEmail": False,
            "nccChecks": [ncc_chk]
        }
        try:
            _res = requests.post(headers=self.nx_request_headers, url=_url, json=_payload, verify=False)

            if _res.status_code != 200:
                print(f"Error executing NCC check: {_res.status_code} - {_res.text}")
                return None

            response_json = _res.json()
            if 'taskUuid' in response_json:
                print(f"NCC check executed successfully, task UUID: {response_json['taskUuid']}")
                return response_json['taskUuid']
            print("Response JSON does not contain 'taskUuid'")
            return None
        except requests.exceptions.RequestException as e:
            print(f"Request error executing NCC check: {e}")
            return None
        except Exception as e:
            print(f"Error executing NCC check: {e}")
            return None
        
    def get_ncc_check_status(self, pc_add, task_uuid, pe_uuid):
        _url = f"https://{pc_add}:9440/PrismGateway/services/rest/v1/ncc/{task_uuid}?proxyClusterUuid={pe_uuid}"
        try:
            _res = requests.get(headers=self.nx_request_headers, url=_url, verify=False)

            if _res.status_code != 200:
                print(f"Error getting NCC check status: {_res.status_code} - {_res.text}")
                return None
            return _res.json()['runStatus']
        except requests.exceptions.RequestException as e:
            print(f"Request error executing NCC check: {e}")
            return None
        except Exception as e:
            print(f"Error executing NCC check: {e}")
            return None
        
        
from models.database import db, ma


class IncidentHandlerLog(db.Model):
    __tablename__                     = 'dh_incident_handler_log'
    id                                = db.Column(db.Integer, primary_key=True)
    task_type                         = db.Column(db.String(255))
    severity                          = db.Column(db.String(255))
    log_details                       = db.Column(db.String(255))
    creation_time                     = db.Column(db.String(255))


class IncidentHandlerData(db.Model)   : 
    __tablename__                     = 'dh_incident_handler_data'
    id                                = db.Column(db.Integer, primary_key=True)
    number                            = db.Column(db.String(255))
    description                       = db.Column(db.String(255))
    work_note                         = db.Column(db.String(255))
    task_type                         = db.Column(db.String(255))
    creation_date                     = db.Column(db.String(255))


class IncidentHandlerUnresolvedCases(db.Model)   : 
    __tablename__                     = 'dh_incident_handler_unresolved_cases'
    id                                = db.Column(db.Integer, primary_key=True)
    number                            = db.Column(db.String(255))
    description                       = db.Column(db.String(255))
    handle_date                       = db.Column(db.String(255))
    
    
class IncidentHandlerErrorLog(db.Model)   : 
    __tablename__                     = 'dh_incident_handler_error_log'
    id                                = db.Column(db.Integer, primary_key=True)
    number                            = db.Column(db.String(255))
    description                       = db.Column(db.String(255))
    error                             = db.Column(db.String(255))
    handle_date                       = db.Column(db.String(255))


class IncidentHandlerUnitPortalData(db.Model)   : 
    __tablename__                     = 'dh_incident_handler_unit_portal_data'
    id                                = db.Column(db.Integer, primary_key=True)
    active_inc_num                    = db.Column(db.String(255))
    resolved_inc_num                  = db.Column(db.String(255))    
    resolved_inc_today                = db.Column(db.String(255))
    unassigned_inc_num                = db.Column(db.String(255))
    handler_status                    = db.Column(db.String(255))
    date                              = db.Column(db.String(255))
    
    
class IncidentHandlerAnalysis(db.Model):
    __tablename__                     = 'dh_incident_analysis'
    id                                = db.Column(db.Integer, primary_key=True)
    inc_num                           = db.Column(db.String(255))
    type                              = db.Column(db.String(255))
    site                              = db.Column(db.String(255))
    model                             = db.Column(db.String(255))
    assigned                          = db.Column(db.String(255))
    creation_time                     = db.Column(db.String(255))
    resolved_time                     = db.Column(db.String(255))
    duration_time                     = db.Column(db.String(255))
    timestamp                         = db.Column(db.String(255))
    priority                          = db.Column(db.String(255))
    human_check                       = db.Column(db.String(50))
    auto_resolved                     = db.Column(db.String(50))
    short_description                 = db.Column(db.String(50))
    note                              = db.Column(db.String(255))



class IncidentReminderUserModel(db.Model):
    __tablename__                         = 'incident_reminder_user'
    id                                    = db.Column(db.Integer , primary_key=True)
    name                                  = db.Column(db.String(255))
    email_address                         = db.Column(db.String(255))
    email_address_shortdescription         = db.Column(db.String(255))
    region                                = db.Column(db.String(255))


class IncidentReminderUserSchemaModel(ma.Schema):
    class Meta:
        fields = ('id', 'name', 'email_address', 'email_address_shortdescription', 'region')
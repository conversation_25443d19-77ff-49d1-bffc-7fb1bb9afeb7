# installed modules
import sys
from datetime import datetime

from flask import jsonify

# local file
from models.auth_models import ModelUser
from models.database import db
from models.pm_models import ModelNTXPMTask, ModelNTXPMTaskSchema, ModelNTXPMLogSchema, ModelNTXPMLog, ModelSLIPMTask, \
    ModelSLIPMTaskSchema, ModelSLIPMLogSchema, ModelSLIPMLog


class Task:
    def __init__(self, id=None, token=None) -> None:
        self.id = id
        self.token = token

    def if_operation_is_allowed(self):
        # user with pmuser role is not allowed to change something admin created.
        user = ModelUser.query.filter_by(token=self.token).first()
        if user.role == 'admin':  # if admin is updating the task , that's fine, I will just do it
            return True
        task = ModelNTXPMTask.query.filter_by(id=self.id).first()
        creater = ModelUser.query.filter_by(username=task.creater).first()
        if creater.role == 'admin':  # if other role is updating the pm that created by admin, no , not allowed....
            return False
        return False

    def get_pm_task(self, getlog=False, page=None, limit=None):
        taskschema = ModelNTXPMTaskSchema(many=True)
        query = ModelNTXPMTask.query
        total = query.count()
        query = query.order_by(ModelNTXPMTask.id.desc())
        if page is not None and limit is not None:
            query = query.offset((page - 1) * limit).limit(limit)
        tasks = query.all()
        if getlog:
            _res = []
            logsschema, _logschema = ModelNTXPMLogSchema(many=True), ModelNTXPMLogSchema()
            _taskschema = ModelNTXPMTaskSchema()
            for task in tasks:
                _task = _taskschema.dump(task)
                if task.logs:
                    logs = ModelNTXPMLog.query.filter_by(task_id=task.id).order_by(ModelNTXPMLog.id.desc()).all()
                    if logs:
                        _logs = logsschema.dump(logs)
                        _task['latestlog'] = _logschema.dump(logs[0])  # type: ignore
                    else:
                        _task['latestlog'] = {'severity': 'info', 'loginfo': 'No log yet'}  # type: ignore
                else:
                    _task['latestlog'] = {'severity': 'info', 'loginfo': 'No log yet'}  # type: ignore
                _res.append(_task)
            result = _res
        else:
            result = taskschema.dump(tasks)
        if page is not None and limit is not None:
            return jsonify({"data": result, "total": total})
        return jsonify(result)

    def create_ntx_pm_task(self, data):
        user = ModelUser.query.filter_by(token=self.token).first()
        payload = {
            "prism": data['prism'],
            "cluster": data['cluster'],
            "startdate": ("startnow" if data['startnow'] else data['scheduledate']),
            "status": "Not Started",
            "creater": user.username,
            "createdate": datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S"),
            "description": data['description'],
            "pmtype": data['pmtype']
        }
        task = ModelNTXPMTask(**payload)
        db.session.add(task)
        db.session.commit()
        _taskschema = ModelNTXPMTaskSchema()
        _task = _taskschema.dump(task)
        return _task

    def update_ntx_pm_task(self, paramdict=None, **param):  # pylint: disable=W0613
        try:
            pmtask_fileds = ['startdate', 'createrinfo', 'cluster', 'description', 'prism',
                             'pmtype']  # fields that are allowed to be changed by user.
            kwags = {}
            for _k in param.keys():
                if _k in pmtask_fileds:
                    kwags[_k] = param[_k]
            if kwags:
                ModelNTXPMTask.query.filter_by(id=self.id).update(kwags)
                db.session.commit()
                return True, f'PM task with id:({self.id}) was updated.'
            return True, f'PM task with id:({self.id}) was not updated, because there is no allowed fields.'
        except Exception as e:
            return False, str(e)

    def delete_ntx_pm_task(self):
        try:
            task = ModelNTXPMTask.query.filter_by(id=self.id).first()
            db.session.delete(task)
            db.session.commit()
            return True, f'PM task with id:({self.id}) was deleted.'
        except Exception as e:
            return False, str(e)

    def abort_ntx_pm_task(self):
        try:
            task = ModelNTXPMTask.query.filter_by(id=self.id).first()
            task.status = "Aborted"
            db.session.commit()
            return True, f'PM task with id:({self.id}) was Aborted.'
        except Exception as e:
            return False, str(e)

    def get_pm_task_by_id(self):
        taskschema = ModelNTXPMTaskSchema()
        print('id', self.id)
        _task = ModelNTXPMTask.query.filter_by(id=self.id).first()
        task = taskschema.dump(_task)
        logschema = ModelNTXPMLogSchema(many=True)
        _logs = ModelNTXPMLog.query.filter_by(task_id=self.id).order_by(ModelNTXPMLog.id.desc()).all()
        logs = logschema.dump(_logs)
        task['logs'] = logs
        return task

    def get_pm_tasklog_by_id(self, task_id):
        logs = ModelNTXPMLog.query.filter_by(task_id=task_id).order_by(ModelNTXPMLog.id.desc()).all()
        _schema = ModelNTXPMLogSchema(many=True)
        return _schema.dump(logs)
    #####  Simplivity PM task operation  ######

    def sli_if_operation_is_allowed(self):
        # user with pmuser role is not allowed to change something admin created.
        user = ModelUser.query.filter_by(token=self.token).first()
        if user.role == 'admin':  # if admin is updating the task , that's fine, I will just do it
            return True
        task = ModelSLIPMTask.query.filter_by(id=self.id).first()
        creater = ModelUser.query.filter_by(username=task.creater).first()
        if creater.role == 'admin':  # if other role is updating the pm that created by admin, no , not allowed....
            return False
        return False

    def delete_sli_pm_task(self):
        try:
            task = ModelSLIPMTask.query.filter_by(id=self.id).first()
            db.session.delete(task)
            db.session.commit()
            return True, f'PM task with id:({self.id}) was deleted.'
        except Exception as e:
            return False, str(e)

    def abort_sli_pm_task(self):
        try:
            task = ModelSLIPMTask.query.filter_by(id=self.id).first()
            task.status = "Aborted"
            db.session.commit()
            return True, f'PM task with id:({self.id}) was Aborted.'
        except Exception as e:
            return False, str(e)

    def create_sli_pm_task(self, data):
        user = ModelUser.query.filter_by(token=self.token).first()
        payload = {
            "vcenter": data['vcenter'].lower(),
            "cluster": data['cluster'].upper(),
            "startdate": ("startnow" if data['startnow'] else data['scheduledate']),
            "status": "Not Started",
            "creater": user.username,
            "createdate": datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S"),
            "description": data['description'],
            "pmtype": data['pmtype']
        }
        task = ModelSLIPMTask(**payload)
        db.session.add(task)
        db.session.commit()
        _taskschema = ModelSLIPMTaskSchema()
        _task = _taskschema.dump(task)
        return _task

    def update_sli_pm_task(self, paramdict=None, **param):  # pylint: disable=W0613
        try:
            pmtask_fileds = ['startdate', 'createrinfo', 'cluster', 'description', 'vcenter',
                             'pmtype']  # fields that are allowed to be changed by user.
            kwags = {}
            for _k in param.keys():
                if _k in pmtask_fileds:
                    kwags[_k] = param[_k]
            if kwags:
                ModelSLIPMTask.query.filter_by(id=self.id).update(kwags)
                db.session.commit()
                return True, f'PM task with id:({self.id}) was updated.'
            return True, f'PM task with id:({self.id}) was not updated, because there is no allowed fields.'
        except Exception as e:
            return False, str(e)

    def get_slipm_task_by_id(self):
        taskschema = ModelSLIPMTaskSchema()
        print('id', self.id)
        print(f'id:{self.id}', file=sys.stdout)
        _task = ModelSLIPMTask.query.filter_by(id=self.id).first()
        task = taskschema.dump(_task)
        logschema = ModelSLIPMLogSchema(many=True)
        _logs = ModelSLIPMLog.query.filter_by(task_id=self.id).order_by(ModelSLIPMLog.id.desc()).all()
        logs = logschema.dump(_logs)
        task['logs'] = logs
        return task

    def get_slipm_task(self, getlog=False):
        taskschema = ModelSLIPMTaskSchema(many=True)
        tasks = ModelSLIPMTask.query.all()
        if getlog:  # get the task log create a log field in all tasks
            _res = []
            logsschema, _logschema = ModelSLIPMLogSchema(many=True), ModelSLIPMLogSchema()
            _taskschema = ModelSLIPMTaskSchema()
            for task in tasks:
                _task = _taskschema.dump(task)
                if task.logs:
                    logs = ModelSLIPMLog.query.filter_by(task_id=task.id).order_by(ModelSLIPMLog.id.desc()).all()
                    if logs:
                        _logs = logsschema.dump(logs)
                        _task['logs'] = _logs  # type: ignore
                        _task['latestlog'] = _logschema.dump(logs[0])  # type: ignore
                    else:
                        _task['logs'] = []  # type: ignore
                        _task['latestlog'] = {'severity': 'info', 'loginfo': 'No log yet'}  # type: ignore
                else:
                    _task['logs'] = []  # type: ignore
                    _task['latestlog'] = {'severity': 'info', 'loginfo': 'No log yet'}  # type: ignore
                _res.append(_task)
            result = _res
        else:
            result = taskschema.dump(tasks)
        return jsonify(result)

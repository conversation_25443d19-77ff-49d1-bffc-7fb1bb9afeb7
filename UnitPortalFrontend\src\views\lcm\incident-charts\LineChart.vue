<template>
    <div :class="className" :style="{height:height,width:width}" />
  </template>
  
  <script>
  import echarts from 'echarts'
  require('echarts/theme/macarons') // echarts theme
  import resize from '@/views/dashboard/admin/components/mixins/resize'
  export default {
    mixins: [resize],
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '40vh'
      },

      line_data:{

      }
    },
    watch: {
      line_data: {
        handler() {
          this.initChart() 
        },
        deep: true
      }
    },
    data() {
      return {
        chart: null,
      }
    },
    mounted() {
      this.initChart()
    },
    beforeDestroy() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
    methods: {    

      initChart() {
        console.log('initing.')
        console.log(this.line_data)
        new Promise((resolve, reject) => {
          resolve()
        }).then(() => {
        this.chart = echarts.init(this.$el, 'macarons')
        let option = {
          title: {
            text: "GDH Incidents Trend",
            left: 'center',
            top: '10',
            padding: [-10, 0],
            textStyle: {
              color: '#000',
              fontSize: 16
            }
          },
          legend: {
            padding:10,
            data: [
            { name: 'Wiab', icon: 'circle'},
            { name: 'Siab', icon: 'circle'},
            { name: 'Total', icon: 'circle'}
          ],
            top: 'bottom'
          },
          xAxis: {
            type: 'category',
            data: this.line_data.label
          },
          yAxis: {},
          series: [
            {
              name: 'Wiab',
              data: this.line_data.wiab,
              type: 'line',
              smooth: false, 
              lineStyle: {
                color: 'green',//#ivan 折线颜色
                width: 4,
                type: 'solid'
              },
              itemStyle: { color: 'green'}, //#ivan 标注点的颜色
              symbol: 'circle',
              symbolSize: 8,
              label: {
                show: true,
                position: 'bottom',//#ivan 数字标注点的位置，top, left, right,bottom,inside,isideLeft...
                color: 'green' //#ivan 数字标注点的颜色
              }
            },
            {
              name: 'Siab',
              data: this.line_data.siab,
              type: 'line',
              smooth: false, 
              lineStyle: {
                color: 'blue',
                width: 4,
                type: 'solid'
              },
              itemStyle: { color: 'blue' },
              symbol: 'circle',
              symbolSize: 8,
              label: {
                show: true,
                position: 'bottom',
                color: 'blue'
              }
            },
            {
              name: 'Total',
              data: this.line_data.total,
              type: 'line',
              smooth: false, 
              lineStyle: {
                color: 'red',
                width: 4,
                type: 'solid'
              },
              itemStyle: { color: 'red' },
              symbol: 'circle',
              symbolSize: 8,
              label: {
                show: true,
                position: 'bottom',
                color: 'red'
              }
            }
          ]
          };
        this.chart.setOption(option)
        })
       
      }
    }
  }
  </script>
  
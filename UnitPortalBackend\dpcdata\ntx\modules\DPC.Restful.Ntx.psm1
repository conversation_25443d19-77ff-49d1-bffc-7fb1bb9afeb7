function Invoke-Prism-Api(){
    <#
    .SYNOPSIS
    Common function that calls Prism API
    
    .DESCRIPTION
    Long description
    
    .PARAMETER Prism
    The Prism address, either IP or FQDN of Prism Central/Element
    
    .PARAMETER RequestURI
    The request URI relative to resource
    
    .PARAMETER Version
    API version, 0.8,1,2,3 are available
    
    .PARAMETER Method
    GET/POST/PUT
    
    .PARAMETER Auth
    The Base64 format authentication comes from username/password
    
    .PARAMETER Body
    The body object for API call
    
    .PARAMETER MaxTry
    The maximum try times when exceptions occurs
    
    .PARAMETER TimeoutSec
    The timeout for waiting response
    
    .EXAMPLE
    An example
    
    .NOTES
    The function returns raw data of response
    #>
    param (
        [string]                                         $Prism,
        [string]                                         $RequestURI,
        [string] [validateSet("v0.8", "v1", "v2", "v3")] $Version,
        [string] [ValidateSet("GET", "POST", "PUT")]     $Method,
                                                         $Auth,
                                                         $Body,
        [int]                                            $MaxTry     = 5,
        [int]                                            $TimeoutSec = 15
    )
    if ($MaxTry) {
        $Headers = @{
            'Accept'        = 'application/json'
            'Authorization' = $Auth
            'Content-Type'  = 'application/json'
        }
        $Payload = @{
            'Method'     = $Method
            'Headers'    = $Headers
            'TimeoutSec' = $TimeoutSec
        }
        if ($Body) {
            $Payload['Body'] = $Body | ConvertTo-Json
        }
        switch ($Version) {
            "v0.8" {
                $Payload['Uri'] = "https://$($Prism):9440/api/nutanix/v0.8/$($RequestURI)"
            }
            "v1" {
                $Payload['Uri'] = "https://$($Prism):9440/api/nutanix/v1/$($RequestURI)"
            }
            "v2" {
                $Payload['Uri'] = "https://$($Prism):9440/PrismGateway/services/rest/v2.0/$($RequestURI)"
            }
            "v3" {
                $Payload['Uri'] = "https://$($Prism):9440/api/nutanix/v3/$($RequestURI)"
            }
        }
        try {
            return Invoke-RestMethod @Payload -SkipCertificateCheck:$true
        }
        catch {
            Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling '$Prism' for '$RequestURI' through API '$Version'. Cause: $_ '$($MaxTry - 1)' times to try, re-try in 5 seconds." -DumpFile $Global:DumpFile
            Start-Sleep 5
            return Invoke-Prism-Api -Prism $Prism `
                                    -RequestURI $RequestURI `
                                    -Version $Version `
                                    -Method $Method `
                                    -Auth $Auth `
                                    -Body $Body `
                                    -MaxTry $($MaxTry - 1) `
                                    -TimeoutSec $($TimeoutSec + 5)
        }
    }else {
        Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling '$Prism' for '$RequestURI'" -DumpFile $Global:DumpFile
        return $null
    }
}
function Invoke-Genesis-Api(){
    param (
        [string]                                     $Prism,
        [string] [ValidateSet("GET", "POST", "PUT")] $Method,
                                                     $Auth,
                                                     $HashPayload,
        [int]                                        $MaxTry     = 5,
        [int]                                        $TimeoutSec = 10
    )
    if ($MaxTry) {
        $Headers = @{
            'Accept'        = 'application/json'
            'Authorization' = $Auth
            'Content-Type'  = 'application/json'
        }
        $Payload = @{
            'Uri'        = "https://$($Prism):9440/PrismGateway/services/rest/v1/genesis"
            'Method'     = $Method
            'Headers'    = $Headers
            'TimeoutSec' = $TimeoutSec
        }
        if ($HashPayload) {
            $DoubleJson = $HashPayload | ConvertTo-Json -Depth 10 -Compress
            $Body = @{
                value = $DoubleJson
            }
            $Payload['Body'] = $Body | ConvertTo-Json
        }
        try {
            return Invoke-RestMethod @Payload -SkipCertificateCheck:$true
        }
        catch {
            Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling '$Prism' for '/genesis'. Cause: $_ '$($MaxTry - 1)' times to try, re-try in 5 seconds" -DumpFile $Global:DumpFile
            Start-Sleep 5
            Invoke-Genesis-Api -Prism $Prism `
                               -Method $Method `
                               -Auth $Auth `
                               -HashPayload $HashPayload `
                               -MaxTry $($MaxTry - 1) `
                               -TimeoutSec $($TimeoutSec + 5)
        }
    }else {
        Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling '$Prism' for '/genesis'" -DumpFile $Global:DumpFile
        return $null
    }
}
function Invoke-Lcm-Api(){
    param (
        [string]                                     $Prism,
        [string]                                     $RequestURI,
        [string] [validateSet("v1", "v4")]           $Version,
        [string] [ValidateSet("GET", "POST", "PUT")] $Method,
                                                     $Auth,
                                                     $Body,
        [int]                                        $MaxTry     = 5,
        [int]                                        $TimeoutSec = 10
    )
    if ($MaxTry) {
        $Headers = @{
            'Accept'        = 'application/json'
            'Authorization' = $Auth
            'Content-Type'  = 'application/json'
        }
        $Payload = @{
            'Method'     = $Method
            'Headers'    = $Headers
            'TimeoutSec' = $TimeoutSec
        }
        if ($Body) {
            $Payload['Body'] = $Body | ConvertTo-Json
        }
        switch ($Version) {
            "v1" {
                $Payload['Uri'] = "https://$($Prism):9440/lcm/v1.r0.b1/$($RequestURI)"
            }
            "v4" {
                $Payload['Uri'] = "https://$($Prism):9440/api/lcm/v4.0.a1/$($RequestURI)"
            }
        }
        try {
            return Invoke-RestMethod @Payload -SkipCertificateCheck:$true
        }
        catch {
            Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurs when calling '$Prism' for '$RequestURI' through API '$Version'. Cause: $_ '$($MaxTry - 1)' times to try, re-try in 5 seconds" -DumpFile $Global:DumpFile
            Start-Sleep 5
            Invoke-Lcm-Api -Prism $Prism `
                           -RequestURI $RequestURI `
                           -Version $Version `
                           -Method $Method `
                           -Auth $Auth `
                           -Body $Body `
                           -MaxTry $($MaxTry - 1) `
                           -TimeoutSec $($TimeoutSec + 5)
        }
    }else {
        Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Out of the max try times when calling '$Prism' for '$RequestURI'" -DumpFile $Global:DumpFile
        return $null
    }
}
function Rest-Prism-v1-Get-Cluster(){
    <#
    .SYNOPSIS
    Get cluster details by calling v1 API

    .DESCRIPTION
    Either the Prism Central or Element can response the request

    .PARAMETER Fqdn
    The endpoint of the Prism

    .PARAMETER Username
    The authenticated user for calling API

    .PARAMETER PWord
    The authenticated user form calling API

    .PARAMETER Auth
    The Base64 format authentication string which is the alternative of username/password

    .EXAMPLE
    Rest-Prism-v1-Get-Cluster -Fqdn ssp-china-ntx.ikea.com -Username <EMAIL> -PWord abcd.1234
    Rest-Prism-v1-Get-Cluster -Fqdn retjp651-nxc000.ikea.com -Auth $Auth

    .NOTES
    Sample of function returns in JSON:
        id                                    : 5e644590-e520-4648-9ac0-4c973742d297::999
        uuid                                  : 5e644590-e520-4648-9ac0-4c973742d297
        clusterIncarnationId                  : 6801637825857996360
        clusterUuid                           : 5e644590-e520-4648-9ac0-4c973742d297
        name                                  : retcnchn-nxp001
        clusterExternalIPAddress              : ************
        clusterExternalAddress                : {@{ipv4=************}}
        clusterFullyQualifiedDomainName       :
        isNSEnabled                           : False
        clusterExternalDataServicesIPAddress  :
        clusterExternalDataServicesAddress    :
        segmentedIscsiDataServicesIPAddress   :
        segmentedIscsiDataServicesAddress     :
        clusterMasqueradingIPAddress          :
        clusterMasqueradingAddress            :
        clusterMasqueradingPort               :
        timezone                              : America/Los_Angeles
        supportVerbosityType                  : BASIC_COREDUMP
        operationMode                         : Normal
        encrypted                             : True
        clusterUsageWarningAlertThresholdPct  : 75
        clusterUsageCriticalAlertThresholdPct : 90
        storageType                           : all_flash
        clusterFunctions                      : {Multicluster}
        isLTS                                 : False
        isRegisteredToPC                      :
        numNodes                              : 3
        blockSerials                          : {null}
        version                               : 6.0
        fullVersion                           : el7.3-release-fraser-6.0-stable-9b8a9cdf0f78f4f01261357c2c3305ed2c466d46
        targetVersion                         : 6.0
        externalSubnet                        : ************/***************
        externalAddress                       : {@{ipv4=************/***************}}
        internalSubnet                        : ************/***************
        internalAddress                       : {@{ipv4=************/***************}}
        nccVersion                            : ncc-4.6.0
        enableLockDown                        : False
        enablePasswordRemoteLoginToCluster    : True
        fingerprintContentCachePercentage     : 100
        ssdPinningPercentageLimit             : 25
        enableShadowClones                    : True
        enableRf1Container                    : False
        enableRebuildReservation              : False
        globalNfsWhiteList                    : {}
        globalNfsWhiteListAddress             : {}
        nameServers                           : {***********, ************}
        nameServersList                       : {@{ipv4=***********}, @{ipv4=************}}
        ntpServers                            : {ntp1-cn.ikea.com, ntp1-ap.ikea.com, ntp1-eu.ikea.com, ntp2-eu.ikea.com…}
        ntpServersList                        : {@{hostname=ntp1-cn.ikea.com}, @{hostname=ntp1-ap.ikea.com},
                                                @{hostname=ntp1-eu.ikea.com}, @{hostname=ntp2-eu.ikea.com}…}
        serviceCenters                        : {}
        httpProxies                           : {}
        rackableUnits                         : {@{id=6; rackableUnitUuid=dc44444a-a816-4280-8f68-5f996b76a36d; model=Null;
                                                modelName=null; location=; serial=null; positions=System.Object[];
                                                nodes=System.Object[]; nodeUuids=System.Object[]}}
        publicKeys                            : {@{name=Nutanix_Support; key=ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDH8rrDuRjJJE
                                                ZsiIH91qQzevHObqRmhVCGIRwTS4Gl90z9aI2cE9rUDb4j6DZDbQjk8r8BC9KpiSbUF7ky+ZDxYWmh8
                                                egYvB8KNZn6OWTHc/4KdJOy8BoTWEZZ48XiiL+L5AivCwJ9kEkQ/DH0JN8CxfucYSBFM4exJeXz7kbd
                                                PdNxqixnZr4HQjyElJZN7gO7Un9ejU3/GdRTUgRYs53v+jNi+sRXnqu7lNX9Pbn+LmMis5o5Yzh9tkK
                                                PwsjmwERyoYRQJnRay5w8MxhG53FUBi0ygr9X8pyr2D9VYVt4k9SfH1GqoyhkJTPyrUD6UDN8Kd9nLb
                                                VC+azVszuBw9OR nutanix@ntnx-sgh036t1zj-a-cvm}, @{name=Gateway; key=ssh-rsa AAAA
                                                B3NzaC1yc2EAAAABJQAAAQEAu+fMkr2mG3H+fmztwUGt3+xlAamoOOdA8g8+igvM8WhzA+p6d4gDMmq
                                                +LEyBSrum3I9wyTIrfwTbtUtz1LyKNd+5LgdMfGjTo9rw/Q92xQSvuuRHLX19MV8ozVQhfwUHq2Sh4a
                                                26loVqJl57jvl9Tsv1L4/KkR3vX96hImcF3G1CuyeKpAK2HUsCpP60qbsZ8i2LDH+qDPW6BlsAvqtl8
                                                TDu0Tiy1DXLVclga6HOsP6DWm5hvNAL4u9uH/5siWLyYRienO/tsK8s7ymf0p0lRZDzHHUdR0UD0WJC
                                                0LR6FPG2YbS+BcGG18NRXixsooeL0nx3DUydDDPJqKSkJTtTeQ== rsa-key-20210619}}
        smtpServer                            :
        hypervisorTypes                       : {kNull}
        clusterRedundancyState                : @{currentRedundancyFactor=2; desiredRedundancyFactor=2; redundancyStatus=}
        multicluster                          : True
        cloudcluster                          : False
        hasSelfEncryptingDrive                : False
        isUpgradeInProgress                   : False
        securityComplianceConfig              : @{schedule=DAILY; enableAide=False; enableCore=False;
                                                enableHighStrengthPassword=False; enableBanner=False; enableSNMPv3Only=False}
        hypervisorSecurityComplianceConfig    : @{schedule=DAILY; enableAide=False; enableCore=False;
                                                enableHighStrengthPassword=False; enableBanner=False}
        hypervisorLldpConfig                  : @{enableLldpTx=True}
        clusterArch                           : X86_64
        isSspEnabled                          : True
        iscsiConfig                           :
        domain                                :
        nosClusterAndHostsDomainJoined        : False
        allHypervNodesInFailoverCluster       : False
        credential                            :
        enforceRackableUnitAwarePlacement     : False
        disableDegradedNodeMonitoring         : False
        commonCriteriaMode                    : False
        enableOnDiskDedup                     :
        managementServers                     :
        faultToleranceDomainType              : NODE
        thresholdForStorageThinProvision      :
        recycleBinDTO                         : @{recycleBinTTLSecs=86400}
    #>
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI 'cluster' `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Prism-v3-Get-Cluster(){
    param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    } 
    if ($RestCall = Invoke-Prism-Api -Prism $Fqdn `
                                     -Version v3 `
                                     -RequestURI "clusters/$Uuid" `
                                     -Method GET `
                                     -Auth $Auth) {
        return $RestCall
    }
    return $null
}
function Rest-Prism-v1-List-Cluster(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI 'clusters' `
                            -Method GET `
                            -Auth $Auth `
                            -TimeoutSec 60
}
#function Rest-Prism-v3-List-Cluster(){
#  param(
#      [string]                                              $Fqdn,
#      [string] [Parameter(ParameterSetName = 'Credential')] $Username,
#      [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
#      [string] [Parameter(ParameterSetName = 'Session')]    $Auth
#  )
#  if (!$Auth) {
#      $Auth = Get-Base64Auth -Username $Username -PWord $PWord
#  }
#  $Entities = @()
#  $Page     = 1
#  $Body     = @{
#      'kind'   = 'cluster'
#      'offset' = 0
#      'length' = 500
#  }
#  do {
#      Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
#      $RestCall = Invoke-Prism-Api -Prism $Fqdn `
#                                   -Version v3 `
#                                   -RequestURI 'clusters/list' `
#                                   -Method POST `
#                                   -Auth $Auth `
#                                   -Body $Body `
#                                   -TimeoutSec 60
#      Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page $Page"
#      $Entities    += $RestCall.entities
#      $Body.offset += $Body.length
#      $Page        ++
#  } until (
#      $RestCall.entities.Count -lt $Body.length
#  )
#  return $Entities
#}
function Rest-Prism-v1-List-Host(){
    <#
    .SYNOPSIS
    Get a list of hosts from Prism Central/Element by calling v1 API
    
    .DESCRIPTION
    Long description
    
    .PARAMETER Fqdn
    The endpoint of the Prism Central/Element, either FQDN of IP are acceptable
    
    .PARAMETER Username
    The authenticated user for calling API
    
    .PARAMETER PWord
    The authenticated user for calling API
    
    .PARAMETER Auth
    The Base64 formated authentication string which is the alternative of username/password for calling
    
    .EXAMPLE
    Rest-Prism-v1-List-Host -Fqdn ssp-china-ntx.ikea.com -Username <EMAIL>  -PWord abcd.1234
    Rest-Prism-v1-List-Host -Fqdn retse999-nxc000.ikea.com -Auth $Auth
    
    .NOTES
    Sample of function returns in JSON:
    {
    	"metadata": {
    	"grandTotalEntities": 127,
    	"totalEntities": 127,
    	"filterCriteria": "",
    	"sortCriteria": "",
    	"page": 1,
    	"count": 127,
    	"startIndex": 1,
    	"endIndex": 127
    	},
    	"entities": [
    		{
    			"serviceVMId": "0005c080-c8bc-6c59-0fc4-d4f5ef1e47b0::10",
    			"uuid": "3c8d1f64-09fe-4b39-9882-b893401e04fb",
    			"diskHardwareConfigs": null,
    			"name": "RETCNSOS-NX7004",
    			"serviceVMExternalIP": "*************",
    			"serviceVMExternalAddress": [
    				{
    					"ipv4": "*************"
    				}
    			],
    			"serviceVMNatIP": null,
    			"serviceVMNatPort": null,
    			"oplogDiskPct": 0.1,
    			"oplogDiskSize": 71629103104,
    			"hypervisorKey": "*************",
    			"hypervisorAddress": "*************",
    			"hypervisorAddressValue": [
    				{
    					"ipv4": "*************"
    				}
    			],
    			"hypervisorUsername": "root",
    			"hypervisorPassword": null,
    			"backplaneIp": null,
    			"backplaneAddress": null,
    			"controllerVmBackplaneIp": "*************",
    			"controllerVmBackplaneAddress": [
    				{
    					"ipv4": "*************"
    				}
    			],
    			"rdmaBackplaneIps": null,
    			"rdmaBackplaneAddressList": null,
    			"managementServerName": "*************",
    			"ipmiAddress": "*************",
    			"ipmiAddressValue": [
    				{
    					"ipv4": "*************"
    				}
    			],
    			"ipmiUsername": "ADMIN",
    			"ipmiPassword": null,
    			"monitored": true,
    			"position": {
    				"ordinal": 1,
    				"name": "",
    				"physicalPosition": null
    			},
    			"serial": "SGH109YPLB",
    			"blockSerial": "SGH109YPLB",
    			"blockModel": "UseLayout",
    			"blockModelName": "HPE DX380-12 G10",
    			"blockLocation": null,
    			"hostMaintenanceModeReason": "life_cycle_management",
    			"hypervisorState": "kAcropolisNormal",
    			"acropolisConnectionState": "kConnected",
    			"metadataStoreStatus": "kNormalMode",
    			"metadataStoreStatusMessage": "Metadata store enabled on the node",
    			"state": "NORMAL",
    			"dynamicRingChangingNode": null,
    			"removalStatus": [
    				"NA"
    			],
    			"vzoneName": "",
    			"cpuModel": "Intel(R) Xeon(R) Gold 6226R CPU @ 2.90GHz",
    			"numCpuCores": 32,
    			"numCpuThreads": 64,
    			"numCpuSockets": 2,
    			"cpuFrequencyInHz": 2900000000,
    			"cpuCapacityInHz": 92800000000,
    			"memoryCapacityInBytes": ************,
    			"hypervisorFullName": "Nutanix 20201105.2298",
    			"hypervisorType": "kKvm",
    			"numVMs": 8,
    			"bootTimeInUsecs": 1668620554003374,
    			"isDegraded": false,
    			"isSecureBooted": false,
    			"isHardwareVirtualized": false,
    			"failoverClusterFqdn": null,
    			"failoverClusterNodeState": null,
    			"rebootPending": false,
    			"defaultVmLocation": null,
    			"defaultVmContainerId": "0005c080-c8bc-6c59-0fc4-d4f5ef1e47b0::",
    			"defaultVmContainerUuid": null,
    			"defaultVhdLocation": null,
    			"defaultVhdContainerId": "0005c080-c8bc-6c59-0fc4-d4f5ef1e47b0::",
    			"defaultVhdContainerUuid": null,
    			"biosVersion": null,
    			"biosModel": null,
    			"bmcVersion": null,
    			"bmcModel": null,
    			"hbaFirmwaresList": null,
    			"clusterUuid": "0005c080-c8bc-6c59-0fc4-d4f5ef1e47b0",
    			"stats": {
    				"hypervisor_avg_io_latency_usecs": "0",
    				"num_read_iops": "463",
    				"hypervisor_write_io_bandwidth_kBps": "0",
    				"timespan_usecs": "30000000",
    				"controller_num_read_iops": "356",
    				"read_io_ppm": "978611",
    				"controller_num_iops": "407",
    				"total_read_io_time_usecs": "-1",
    				"controller_total_read_io_time_usecs": "6545116",
    				"hypervisor_num_io": "0",
    				"controller_total_transformed_usage_bytes": "-1",
    				"hypervisor_cpu_usage_ppm": "220808",
    				"controller_num_write_io": "1533",
    				"avg_read_io_latency_usecs": "-1",
    				"content_cache_logical_ssd_usage_bytes": "0",
    				"controller_total_io_time_usecs": "8779387",
    				"controller_total_read_io_size_kbytes": "92260",
    				"controller_num_seq_io": "-1",
    				"controller_read_io_ppm": "874488",
    				"content_cache_num_lookups": "80602",
    				"controller_total_io_size_kbytes": "141655",
    				"content_cache_hit_ppm": "358564",
    				"controller_num_io": "12214",
    				"hypervisor_avg_read_io_latency_usecs": "0",
    				"content_cache_num_dedup_ref_count_pph": "100",
    				"num_write_iops": "10",
    				"controller_num_random_io": "-1",
    				"num_iops": "473",
    				"hypervisor_num_read_io": "0",
    				"hypervisor_total_read_io_time_usecs": "0",
    				"controller_avg_io_latency_usecs": "718",
    				"num_io": "14213",
    				"controller_num_read_io": "10681",
    				"hypervisor_num_write_io": "0",
    				"controller_seq_io_ppm": "-1",
    				"controller_read_io_bandwidth_kBps": "3075",
    				"controller_io_bandwidth_kBps": "4721",
    				"hypervisor_num_received_bytes": "226314431048523",
    				"hypervisor_timespan_usecs": "30589984",
    				"hypervisor_num_write_iops": "0",
    				"total_read_io_size_kbytes": "182896",
    				"hypervisor_total_io_size_kbytes": "0",
    				"avg_io_latency_usecs": "288",
    				"hypervisor_num_read_iops": "0",
    				"content_cache_saved_ssd_usage_bytes": "0",
    				"controller_write_io_bandwidth_kBps": "1646",
    				"controller_write_io_ppm": "125511",
    				"hypervisor_avg_write_io_latency_usecs": "0",
    				"hypervisor_num_transmitted_bytes": "375005662363582",
    				"hypervisor_total_read_io_size_kbytes": "-1",
    				"read_io_bandwidth_kBps": "6096",
    				"hypervisor_memory_usage_ppm": "532486",
    				"hypervisor_num_iops": "0",
    				"hypervisor_io_bandwidth_kBps": "0",
    				"controller_num_write_iops": "51",
    				"total_io_time_usecs": "4094428",
    				"content_cache_physical_ssd_usage_bytes": "-1",
    				"controller_random_io_ppm": "-1",
    				"controller_avg_read_io_size_kbytes": "8",
    				"total_transformed_usage_bytes": "-1",
    				"avg_write_io_latency_usecs": "-1",
    				"num_read_io": "13909",
    				"write_io_bandwidth_kBps": "1392",
    				"hypervisor_read_io_bandwidth_kBps": "0",
    				"random_io_ppm": "-1",
    				"total_untransformed_usage_bytes": "-1",
    				"hypervisor_total_io_time_usecs": "0",
    				"num_random_io": "-1",
    				"controller_avg_write_io_size_kbytes": "32",
    				"controller_avg_read_io_latency_usecs": "612",
    				"num_write_io": "304",
    				"total_io_size_kbytes": "224684",
    				"io_bandwidth_kBps": "7489",
    				"content_cache_physical_memory_usage_bytes": "5187316244",
    				"controller_timespan_usecs": "30000000",
    				"num_seq_io": "-1",
    				"content_cache_saved_memory_usage_bytes": "0",
    				"seq_io_ppm": "-1",
    				"write_io_ppm": "21388",
    				"controller_avg_write_io_latency_usecs": "1457",
    				"content_cache_logical_memory_usage_bytes": "5187316244"
    			},
    			"usageStats": {
    				"storage_tier.das-sata.usage_bytes": "42257812586496",
    				"storage.capacity_bytes": "100734946161622",
    				"storage.logical_usage_bytes": "94483246514176",
    				"storage_tier.das-sata.capacity_bytes": "87683612910552",
    				"storage.free_bytes": "48656360842198",
    				"storage_tier.ssd.usage_bytes": "9820772732928",
    				"storage_tier.ssd.capacity_bytes": "13051333251070",
    				"storage_tier.das-sata.free_bytes": "45425800324056",
    				"storage.usage_bytes": "52078585319424",
    				"storage_tier.ssd.free_bytes": "3230560518142"
    			},
    			"hasCsr": false,
    			"hostNicIds": [],
    			"hostGpus": null,
    			"gpuDriverVersion": null,
    			"hostType": "HYPER_CONVERGED",
    			"keyManagementDeviceToCertificateStatus": {},
    			"hostInMaintenanceMode": false
    		},
    		{
    			...
    		},
    		...
    	]
    }
    #>
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI 'hosts' `
                            -Method GET `
                            -Auth $Auth
}
<#
.SYNOPSIS
Get a list of hosts from Prism Central/Element by calling v3 API

.DESCRIPTION
Long description

.PARAMETER Fqdn
The endpoint of the Prism Central/Element, either FQDN or IP are acceptable 

.PARAMETER Username
The authenticated user for calling API

.PARAMETER PWord
The authenticated user for calling API

.PARAMETER Auth
The Base64 authentication string, the alternative of username/password for calling API, 

.EXAMPLE
Rest-Prism-v3-List-Host -Fqdn ssp-china-ntx.ikea.com -Username <EMAIL> -PWord abcd.1234
Rest-Prism-v3-List-Host -Fqdn retde494-nxc000.ikea.com -Auth $Auth

.NOTES
Sample of function returns in JSON:
[
	{
		"status": {
			"state": "COMPLETE",
			"name": "RETCN340-NX7003",
			"resources": {
			    "serial_number": "SGH241VVVF",
			    "ipmi": {
			    	"ip": "*************"
			    },
			    "host_type": "HYPER_CONVERGED",
			    "cpu_model": "Intel(R) Xeon(R) Gold 6326 CPU @ 2.90GHz",
			    "host_nics_id_list": [],
			    "num_cpu_sockets": 1,
			    "gpu_list": [],
			    "num_cpu_cores": 16,
			    "rackable_unit_reference": {
			    	"kind": "rackable_unit",
			    	"uuid": "71f1869c-54b7-40dd-b579-56c0979285d9"
			    },
			    "controller_vm": {
			    	"ip": "**************",
			    	"oplog_usage": {
			    	    "oplog_disk_pct": 0.9658434710850992,
			    	    "oplog_disk_size": ************
			    	}
			    },
			    "cpu_capacity_hz": 2900000000,
			    "hypervisor": {
			    	"num_vms": 2,
			    	"ip": "**************",
			    	"hypervisor_full_name": "Nutanix 20201105.2298"
			    },
			    "memory_capacity_mib": 515232,
			    "block": {
			    	"block_serial_number": "SGH241VVVF",
			    	"block_model": "HPE DX360-4 G10 Plus"
			    },
			    "host_disks_reference_list": [
			    	{
			    	    "kind": "disk",
			    	    "uuid": "ef5d1ff2-9614-46ef-941f-e08cc6636cc3"
			    	},
			    	{
			    	    "kind": "disk",
			    	    "uuid": "a5737a38-e607-4572-b80e-6a8c255db68e"
			    	},
			    	{
			    	    "kind": "disk",
			    	    "uuid": "749fd57d-8e84-484b-8182-e302c5bfe57f"
			    	},
			    	{
			    	    "kind": "disk",
			    	    "uuid": "1f123d32-858c-4cde-8ca1-a2893075f877"
			    	}
			    ]
			},
			"cluster_reference": {
			    "kind": "cluster",
			    "uuid": "0005eff0-**************-84160c39e466"
			}
		},
		"spec": {
			"name": "RETCN340-NX7003",
			"resources": {
			    "controller_vm": {
			    	"ip": "**************",
			    	"oplog_usage": {
			    	    "oplog_disk_pct": 1.0060869490469784,
			    	    "oplog_disk_size": ************
			    	}
			    }
			}
		},
		"metadata": {
			"last_update_time": "2022-12-19T08:29:57Z",
			"kind": "host",
			"uuid": "edb19863-997f-4327-8f29-7afab0cc7c1f",
			"spec_version": 0,
			"creation_time": "2022-12-19T08:29:57Z",
			"categories_mapping": {},
			"categories": {}
		}
	}
	{
		...
	}
]
#>
function Rest-Prism-v3-List-Host(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    #$Page     = 1
    $Body     = @{
        'kind'   = 'host'
        'offset' = 0
        'length' = 500
    }
    #do {
    #    Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page $Page"
    #    $RestCall = Invoke-Prism-Api -Prism $Fqdn `
    #                                 -Version v3 `
    #                                 -RequestURI 'hosts/list' `
    #                                 -Method POST `
    #                                 -Auth $Auth `
    #                                 -Body $Body `
    #                                 -TimeoutSec 60
    #    Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We've found $($RestCall.entities.Count) objects on page $Page"
    #    $Entities    += $RestCall.entities
    #    $Body.offset += 500
    #    $Page        ++
    #} until (
    #    $RestCall.entities.Count -lt 500
    #)
    $RestCall = Invoke-Prism-Api -Prism $Fqdn `
                                     -Version v3 `
                                     -RequestURI 'hosts/list' `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 60
    $Entities += $RestCall.entities
    return $Entities
}
function Rest-Prism-v3-Get-Host(){
    param (
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v3 `
                            -RequestURI "hosts/$Uuid" `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Prism-v1-List-Vm(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI 'vms' `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Prism-v2-List-Vm(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v2 `
                            -RequestURI 'vms?include_vm_disk_config=true&include_vm_nic_config=true' `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Prism-v3-List-Vm(){
    <#
    .SYNOPSIS
    Get a list of VMs from Prism Central/Element by calling v3 API
    
    .DESCRIPTION
    Long description
    
    .PARAMETER Fqdn
    The endpoint of Prism, either the FQDN or IP are acceptable
    
    .PARAMETER Username
    The authenticated user for API calling
    
    .PARAMETER PWord
    The authenticated user for API calling
    
    .PARAMETER Auth
    The Base64 format atuhentication string, it's the alternative of username/password
    
    .EXAMPLE
    Rest-Prism-v3-List-Vm -Fqdn ssp-china-ntx.ikea.com -Username <EMAIL> -PWord abcd.1234
    Rest-Prism-v3-List-Vm -Fqdn retde494-nxc000.ikea.com -Auth $Auth
    
    .NOTES
    Sample of function returns in JSON:
    [
        {
          "status": {
            "description": "Terminal Server",
            "state": "COMPLETE",
            "execution_context": {
              "task_uuids": [
                "67e1310e-a162-436a-8591-60a57d77481c"
              ]
            },
            "cluster_reference": {
              "kind": "cluster",
              "uuid": "0005aee0-b9a4-c4d3-28a7-48df37dea620",
              "name": "RETSI528-NXC000"
            },
            "resources": {
              "num_threads_per_core": 1,
              "boot_config": {
                "boot_device": {
                  "disk_address": {
                    "device_index": 0,
                    "adapter_type": "SCSI"
                  }
                },
                "boot_type": "UEFI"
              },
              "num_vcpus_per_socket": 1,
              "num_sockets": 4,
              "is_agent_vm": false,
              "storage_config": {
                "flash_mode": "DISABLED"
              },
              "protection_type": "PD_PROTECTED",
              "gpu_list": [],
              "memory_size_mib": 32768,
              "machine_type": "Q35",
              "hardware_clock_timezone": "UTC",
              "power_state_mechanism": {
                "guest_transition_config": {
                  "should_fail_on_script_failure": false,
                  "enable_script_exec": false
                },
                "mechanism": "HARD"
              },
              "vga_console_enabled": true,
              "disk_list": [
                {
                  "uuid": "1d760b3e-bbbe-40d7-b7bc-46d9db9faea7",
                  "is_migration_in_progress": false,
                  "disk_size_bytes": ************,
                  "storage_config": {
                    "flash_mode": "DISABLED",
                    "storage_container_reference": {
                      "kind": "storage_container",
                      "uuid": "25a39d28-55c2-4a44-8b64-613a0c1f4fe9",
                      "name": "SelfServiceContainer"
                    }
                  },
                  "device_properties": {
                    "disk_address": {
                      "device_index": 0,
                      "adapter_type": "SCSI"
                    },
                    "device_type": "DISK"
                  },
                  "data_source_reference": {
                    "kind": "image",
                    "uuid": "26ebf9b4-042d-4940-8efb-604d6fd851d5"
                  },
                  "disk_size_mib": 307200
                }
              ],
              "vnuma_config": {
                "num_vnuma_nodes": 0
              },
              "nic_list": [
                {
                  "nic_type": "NORMAL_NIC",
                  "uuid": "c80f6d5e-fede-4dac-a5f0-7a5714baaa51",
                  "ip_endpoint_list": [
                    {
                      "ip": "**********",
                      "type": "LEARNED"
                    }
                  ],
                  "secondary_ip_address_list": [],
                  "vlan_mode": "ACCESS",
                  "mac_address": "50:6b:8d:86:bf:61",
                  "subnet_reference": {
                    "kind": "subnet",
                    "name": "RETSI528-104-Servers",
                    "uuid": "1dcf3420-be66-491e-adea-265beda63e6e"
                  },
                  "is_connected": true,
                  "trunked_vlan_list": []
                }
              ],
              "host_reference": {
                "kind": "host",
                "uuid": "1991068f-**************-7bec577cedea",
                "name": "***********"
              },
              "serial_port_list": [],
              "hardware_virtualization_enabled": false,
              "hypervisor_type": "AHV",
              "power_state": "ON"
            },
            "name": "RETSI528-NT1000"
          },
          "spec": {
            "cluster_reference": {
              "kind": "cluster",
              "name": "RETSI528-NXC000",
              "uuid": "0005aee0-b9a4-c4d3-28a7-48df37dea620"
            },
            "description": "Terminal Server",
            "resources": {
              "num_threads_per_core": 1,
              "vnuma_config": {
                "num_vnuma_nodes": 0
              },
              "serial_port_list": [],
              "hardware_virtualization_enabled": false,
              "num_vcpus_per_socket": 1,
              "nic_list": [
                {
                  "nic_type": "NORMAL_NIC",
                  "uuid": "c80f6d5e-fede-4dac-a5f0-7a5714baaa51",
                  "ip_endpoint_list": [],
                  "vlan_mode": "ACCESS",
                  "mac_address": "50:6b:8d:86:bf:61",
                  "subnet_reference": {
                    "kind": "subnet",
                    "name": "RETSI528-104-Servers",
                    "uuid": "1dcf3420-be66-491e-adea-265beda63e6e"
                  },
                  "is_connected": true,
                  "trunked_vlan_list": []
                }
              ],
              "num_sockets": 4,
              "gpu_list": [],
              "storage_config": {
                "flash_mode": "DISABLED"
              },
              "is_agent_vm": false,
              "memory_size_mib": 32768,
              "boot_config": {
                "boot_device": {
                  "disk_address": {
                    "device_index": 0,
                    "adapter_type": "SCSI"
                  }
                },
                "boot_type": "UEFI"
              },
              "hardware_clock_timezone": "UTC",
              "power_state_mechanism": {
                "guest_transition_config": {
                  "should_fail_on_script_failure": false,
                  "enable_script_exec": false
                },
                "mechanism": "HARD"
              },
              "power_state": "ON",
              "machine_type": "Q35",
              "vga_console_enabled": true,
              "disk_list": [
                {
                  "uuid": "1d760b3e-bbbe-40d7-b7bc-46d9db9faea7",
                  "disk_size_bytes": ************,
                  "storage_config": {
                    "flash_mode": "DISABLED",
                    "storage_container_reference": {
                      "kind": "storage_container",
                      "uuid": "25a39d28-55c2-4a44-8b64-613a0c1f4fe9",
                      "name": "SelfServiceContainer"
                    }
                  },
                  "device_properties": {
                    "disk_address": {
                      "device_index": 0,
                      "adapter_type": "SCSI"
                    },
                    "device_type": "DISK"
                  },
                  "data_source_reference": {
                    "kind": "image",
                    "uuid": "26ebf9b4-042d-4940-8efb-604d6fd851d5"
                  },
                  "disk_size_mib": 307200
                }
              ]
            },
            "name": "RETSI528-NT1000"
          },
          "metadata": {
            "last_update_time": "2022-02-17T10:22:37Z",
            "kind": "vm",
            "uuid": "82daa71c-ac50-4edf-950e-dbf330a09105",
            "project_reference": {
              "kind": "project",
              "name": "RETSI528-NXC000",
              "uuid": "a3783cfe-70fb-46e0-8833-5e1db1bb7b62"
            },
            "creation_time": "2020-09-11T11:58:30Z",
            "spec_version": 32,
            "categories_mapping": {
              "App_Type": [
                "1"
              ],
              "BU_Type": [
                "RET"
              ],
              "Region": [
                "EMEA"
              ],
              "CityName": [
                "Ljubljana"
              ],
              "BU_Code": [
                "528"
              ],
              "CalmApplication": [
                "Workload RETSI528-NT1000"
              ],
              "CalmPackage": [
                "AHV_ICC_Package"
              ],
              "Snap_Consistency": [
                "CCG"
              ],
              "BU_Country": [
                "SI"
              ],
              "App_Role": [
                "Terminal Server"
              ],
              "NW_NTXLAN_Name": [
                "RETSI528-26212-104"
              ],
              "Remote_Recovery_Site": [
                "RETSEHBG-NXC000"
              ],
              "CalmService": [
                "NEW_ICC_VM"
              ],
              "OSType": [
                "Windows"
              ],
              "Timezone": [
                "UTC+02"
              ],
              "CalmDeployment": [
                "24aab91d_deployment"
              ],
              "NW_VLAN_ID": [
                "104"
              ],
              "Snap_Repl_Policy": [
                "GOLD"
              ],
              "WindowsDomain": [
                "ikea.com"
              ]
            },
            "entity_version": "34",
            "owner_reference": {
              "kind": "user",
              "uuid": "532fbcd8-82f5-5a7b-99fa-a86c60137c82",
              "name": "1-click-robo"
            },
            "categories": {
              "App_Type": "1",
              "BU_Type": "RET",
              "Region": "EMEA",
              "CityName": "Ljubljana",
              "BU_Code": "528",
              "CalmApplication": "Workload RETSI528-NT1000",
              "CalmPackage": "AHV_ICC_Package",
              "Snap_Consistency": "CCG",
              "BU_Country": "SI",
              "App_Role": "Terminal Server",
              "NW_NTXLAN_Name": "RETSI528-26212-104",
              "Remote_Recovery_Site": "RETSEHBG-NXC000",
              "CalmService": "NEW_ICC_VM",
              "OSType": "Windows",
              "Timezone": "UTC+02",
              "CalmDeployment": "24aab91d_deployment",
              "NW_VLAN_ID": "104",
              "Snap_Repl_Policy": "GOLD",
              "WindowsDomain": "ikea.com"
            }
          }
        }
        {
            ...
        }
    ]
    #>
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        'kind'           = 'vm'
        'offset'         = 0
        'length'         = 500
        'sort_attribute' = 'name'
        'sort_order'     = 'ASCENDING'
    }
    do {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page '$Page' from '$Fqdn'" -DumpFile $Global:DumpFile
        $RestCall = Invoke-Prism-Api -Prism $Fqdn `
                                     -Version v3 `
                                     -RequestURI 'vms/list' `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 100
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page '$Page' from '$Fqdn'" -DumpFile $Global:DumpFile
        $Entities    += $RestCall.entities
        $Body.offset += 500
        $Page        ++
    } until (
        $RestCall.entities.Count -lt 500
    )
    return $Entities
}
function Rest-Prism-v1-List-VirtualDisk(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI 'virtual_disks' `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Prism-v1-List-VirtualNic(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI 'virtual_nics' `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Lcm-v4-Get-LcmEntity(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Page     = 1
    $Entities = @()
    do {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page '$Page' from '$Fqdn'" -DumpFile $Global:DumpFile
        $RestCall = Invoke-Lcm-Api -Prism $Fqdn `
                                   -RequestURI "resources/entities?%24page=$($Page - 1)&%24limit=100" `
                                   -Version v4 `
                                   -Method GET `
                                   -Auth $Auth `
                                   -TimeoutSec 30
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.data.Count) objects on page '$Page' from '$Fqdn'" -DumpFile $Global:DumpFile
        $Entities += $RestCall.data
        $Page ++
    } until (
        $RestCall.data.Count -lt 100
    )
    return $Entities
}
function Rest-Lcm-v1-Get-LcmEntity(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Body = @{
        'offset' = 0
    } 
    if ($RestCall = Invoke-Lcm-Api -Prism $Fqdn `
                                   -RequestURI "resources/entities/list" `
                                   -Version v1 `
                                   -Method POST `
                                   -Auth $Auth `
                                   -Body $Body `
                                   -TimeoutSec 30) {
        return $RestCall.data.entities
    }
    return $null
}

function Rest-Prism-v3-List-Group(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Body = @{
        'entity_type' = 'cluster'
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v3 `
                            -RequestURI 'groups' `
                            -Method POST `
                            -Auth $Auth `
                            -Body $Body
}
function Rest-Prism-v3-List-PcVm(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    if ($RestCall = Invoke-Prism-Api -Prism $Fqdn `
                                     -Version v3 `
                                     -RequestURI 'prism_central' `
                                     -Method GET `
                                     -Auth $Auth) {
        return $RestCall.resources.pc_vm_list
    }
    return $null
}
function Rest-Genesis-Get-LcmFw(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $HashPayload = @{
        '.oid'    = 'LifeCycleManager'
        '.method' = 'lcm_framework_rpc'
        '.kwargs' = @{
            'method_class' = 'LcmFramework'
            'method'       = 'get_config'
        }
    }
    if ($RestCall = Invoke-Genesis-Api -Prism $Fqdn `
                                       -Method POST `
                                       -Auth $Auth `
                                       -HashPayload $HashPayload `
                                       -TimeoutSec 30) {
        $Object = $RestCall.value | ConvertFrom-Json -Depth 10
        return $Object.'.return'
    }
    return $null
}
function Rest-Prism-v1-List-RemoteSite(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI "remote_sites" `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Prism-v1-Get-RemoteSite(){
  param (
        [string]                                              $Fqdn,
        [string]                                              $RsName,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI "remote_sites/$($RsName)" `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Prism-v1-List-RsDrSnapshot(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI 'remote_sites/dr_snapshots' `
                            -Method GET `
                            -Auth $Auth `
                            -TimeoutSec 30
}
function Rest-Prism-v1-List-ProtectionDomain(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI 'protection_domains' `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Prism-v1-List-DrSnapShot(){
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v1 `
                            -RequestURI 'protection_domains/dr_snapshots' `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Prism-v08-List-Network(){
    <#
    .SYNOPSIS
    List networks from Prism Element by calling v0.8 API
    
    .DESCRIPTION
    
    
    .PARAMETER Fqdn
    The Fqdn of Prism Element
    
    .PARAMETER Username
    The authenticated user to call API
    
    .PARAMETER PWord
    The authenticated user to call API
    
    .PARAMETER Auth
    The alternative of username/pword to call API
    
    .EXAMPLE
    Rest-Prism-v08-List-Network -Fqdn retfr435-nxc000.ikea.com -Username <EMAIL> -PWord abcd.1234
    Rest-Prism-v08-List-Network -Fqdn retfr435-nxc000.ikea.com -Auth $Auth
    
    .NOTES
    Sample of function returns in JSON:
    {
        "metadata": {
          "grandTotalEntities": 5,
          "totalEntities": 5
        },
        "entities": [
          {
            "logicalTimestamp": 3,
            "vlanId": 115,
            "ipConfig": {
              "prefixLength": 0,
              "ipamEnabled": false,
              "freeIps": -1,
              "assignedIps": -1,
              "numMacs": 2,
              "dhcpOptions": {},
              "pool": []
            },
            "uuid": "dc4123c0-4add-4706-b9cd-be7ebd577145",
            "virtualSwitchUuid": "548b16a2-980a-4d62-818a-fc22002e66f2",
            "name": "RETFR435-115-PaloAlto_Sync",
            "vswitchName": "br0"
          },
          {
            ...
          }
        ]
    }
    #>
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    return Invoke-Prism-Api -Prism $Fqdn `
                            -Version v0.8 `
                            -RequestURI 'networks' `
                            -Method GET `
                            -Auth $Auth
}
function Rest-Prism-v3-List-Subnet(){
    <#
    .SYNOPSIS
    List subnets from Prism Central/Element by calling v3 API
    
    .DESCRIPTION

    
    .PARAMETER Fqdn
    The FQDN of Prism Central or Element
    
    .PARAMETER Username
    The authenticated user to call API
    
    .PARAMETER PWord
    The authenticated user to call API
    
    .PARAMETER Auth
    The alternative of username/pword to call API
    
    .EXAMPLE
    Rest-Prism-v3-List-Subnet -Fqdn ssp-china-ntx.ikea.com -Username <EMAIL> -PWord abcd.1234
    Rest-Prism-v3-List-Subnet -Fqdn retfr413-nxc000.ikea.com -Auth $Auth
    
    .NOTES
    Sample of function returns in JSON:
    [
	    {
	    	"status": {
	    		"state": "COMPLETE",
	    		"name": "RETFR435-157-NutanixMgmnt-FW",
	    		"resources": {
	    			"vswitch_name": "br0",
	    			"subnet_type": "VLAN",
	    			"virtual_switch_uuid": "548b16a2-980a-4d62-818a-fc22002e66f2",
	    			"vlan_id": 0
	    		},
	    		"cluster_reference": {
	    			"kind": "cluster",
	    			"name": "RETFR435-NXC000",
	    			"uuid": "0005f3b9-8e47-ecc6-4305-84160c4b80c6"
	    		}
	    	},
	    	"spec": {
	    		"name": "RETFR435-157-NutanixMgmnt-FW",
	    		"resources": {
	    			"vswitch_name": "br0",
	    			"subnet_type": "VLAN",
	    			"virtual_switch_uuid": "548b16a2-980a-4d62-818a-fc22002e66f2",
	    			"vlan_id": 0
	    		},
	    		"cluster_reference": {
	    			"kind": "cluster",
	    			"name": "RETFR435-NXC000",
	    			"uuid": "0005f3b9-8e47-ecc6-4305-84160c4b80c6"
	    		}
	    	},
	    	"metadata": {
	    		"last_update_time": "2023-05-05T01:42:17Z",
	    		"kind": "subnet",
	    		"uuid": "85f747c0-3399-4005-99c3-21bc97946733",
	    		"spec_version": 0,
	    		"creation_time": "2023-05-05T01:42:17Z",
	    		"categories_mapping": {},
	    		"categories": {}
	    	}
        },
        {
        	...
        }
    ]
    #>
    param (
        [string]                                              $Fqdn,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
    )
    if (!$Auth) {
        $Auth = Get-Base64Auth -Username $Username -PWord $PWord
    }
    $Entities = @()
    $Page     = 1
    $Body     = @{
        "kind"           = "subnet"
        "offset"         = 0
        "length"         = 500 
        "sort_attribute" = "name"
        "sort_order"     = "ASCENDING"
    }
    do {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page '$Page' from '$Fqdn'" -DumpFile $Global:DumpFile
        $RestCall = Invoke-Prism-Api -Prism $Fqdn `
                                     -RequestURI "subnets/list" `
                                     -Version v3 `
                                     -Method POST `
                                     -Auth $Auth `
                                     -Body $Body `
                                     -TimeoutSec 30
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We found '$($RestCall.entities.Count)' objects on page '$Page' from '$Fqdn'" -DumpFile $Global:DumpFile
        $Entities    += $RestCall.entities
        $Body.offset += 500
        $Page        ++
    } until (
        $RestCall.entities.Count -lt 500
    )
    return $Entities
}
function Rest-Prism-v3-Get-Groups(){
  param (
        [string]                                              $Fqdn,
        [object]                                              $Body,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
        [string] [Parameter(ParameterSetName = 'Session')]    $Auth
  )
  if (!$Auth) {
    $Auth = Get-Base64Auth -Username $Username -PWord $PWord
  }
  return Invoke-Prism-Api -Prism $Fqdn `
                          -RequestURI "groups" `
                          -Version v3 `
                          -Method POST `
                          -Auth $Auth `
                          -Body $Body `
                          -TimeoutSec 180
}
function Rest-Prism-v3-List-App(){
  param (
      [string]                                              $Fqdn,
      [string]                                              $Filter,
      [string] [Parameter(ParameterSetName = 'Credential')] $Username,
      [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                  [Parameter(ParameterSetName = 'Session')] $Auth
  )
  if (!$Auth) {
      $Auth = Get-Base64Auth -Username $Username -PWord $PWord
  }
  $Entities = @()
  $Page     = 1
  $Body     = @{
      'offset' = 0
      'length' = 250
  }
  if ($Filter) {
      $Body['filter'] = $Filter
  }
  do {
      Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now loading page '$Page' from '$Fqdn'" -DumpFile $Global:DumpFile
      $RestCall = Invoke-Prism-Api -Prism $Fqdn `
                                   -RequestURI "apps/list" `
                                   -Version v3 `
                                   -Method POST `
                                   -Auth $Auth `
                                   -Body $Body `
                                   -TimeoutSec 40
      Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We found $($RestCall.entities.Count) objects on page '$Page' from '$Fqdn'" -DumpFile $Global:DumpFile
      $Entities    += $RestCall.entities
      $Body.offset += $Body.length
      $Page        ++
  } until (
      $RestCall.entities.Count -lt $Body.length
  )
  return $Entities
}
function Rest-Prism-v3-Get-App(){
  param (
      [string]                                              $Fqdn,
      [string]                                              $Uuid,
      [string] [Parameter(ParameterSetName = 'Credential')] $Username,
      [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                  [Parameter(ParameterSetName = 'Session')] $Auth
  )
  if (!$Auth) {
      $Auth = Get-Base64Auth -Username $Username -PWord $PWord
  }
  return Invoke-Prism-Api -Prism $Fqdn `
                          -RequestURI "apps/$Uuid" `
                          -Version v3 `
                          -Method GET `
                          -Auth $Auth `
                          -Body $Body `
                          -TimeoutSec 40
}
function Rest-Prism-v1-Get-License() {
  param(
        [string]                                              $Fqdn,
        [string]                                              $Uuid,
        [string] [Parameter(ParameterSetName = 'Credential')] $Username,
        [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                    [Parameter(ParameterSetName = 'Session')] $Auth
  )
  if (!$Auth) {
    $Auth = Get-Base64Auth -Username $Username -PWord $PWord
  }
  if ($Uuid) {
      return Invoke-Prism-API -Prism $Fqdn -Version v1 -RequestURI "license?proxyClusterUuid=$Uuid" -Method GET -Auth $Auth
  }
  return Invoke-Prism-API -Prism $Fqdn -Version v1 -RequestURI "license" -Method GET -Auth $Auth  
}
function Rest-Prism-v1-Get-HostNic(){
  param(
      [string]                                              $Fqdn,
      [string]                                              $Uuid,
      [string]                                              $NicId,
      [string] [Parameter(ParameterSetName = 'Credential')] $Username,
      [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                  [Parameter(ParameterSetName = 'Session')] $Auth
  )
  if (!$Auth) {
      $Auth = Get-Base64Auth -Username $Username -PWord $PWord
  }
  if ($NicId) {
      return Invoke-Prism-API -Prism $Fqdn -Version v1 -RequestURI "hosts/$($Uuid)/host_nics/$($NicId)" -Method GET -Auth $Auth
  }
  return Invoke-Prism-API -Prism $Fqdn -Version v1 -RequestURI "hosts/$($Uuid)/host_nics" -Method GET -Auth $Auth
}
function Rest-Prism-v1-Get-Keys(){
  param(
      [string]                                              $Fqdn,
      [string] [Parameter(ParameterSetName = 'Credential')] $Username,
      [string] [Parameter(ParameterSetName = 'Credential')] $PWord,
                  [Parameter(ParameterSetName = 'Session')] $Auth
  )
  if (!$Auth) {
      $Auth = Get-Base64Auth -Username $Username -PWord $PWord
  }
  return Invoke-Prism-API -Prism $Fqdn -Version v1 -RequestURI "keys/pem" -Method GET -Auth $Auth
}
Export-ModuleMember *
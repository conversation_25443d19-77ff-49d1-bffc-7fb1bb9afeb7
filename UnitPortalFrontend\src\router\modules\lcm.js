/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'
import AppMain from '@/layout/components/AppMain'
const lcmRouter = {
  path: '/lcm',
  component: Layout,
  redirect: '/lcm/lcm',
  // name: 'Table',
  meta: {
    title: 'Automation',
    icon: 'automation',
    roles: ['superadmin','pmuser'],
    privilege:"role_lcm"
  },
  children: [
    {
      path: 'spp',
      name: 'SPP LCM',
      component: AppMain, // 一级路由
      meta: { title: 'SPP LCM', roles: ['admin','pmuser','superadmin'], privilege: "view_spp" },
      children: [
        {
          path: 'retail',
          component: () => import('@/views/lcm/spp/retail'), // 子级路由
          name: 'SPPRetail',
          meta: { title: 'Retail', roles: ['pmuser'], privilege: "view_spp", customized_title: "SPP - Retail"},
        },
        {
          path: 'warehouse',
          component: () => import('@/views/lcm/spp/warehouse'),
          name: 'SPPWarehouse',
          meta: { title: 'Warehouse', roles: ['pmuser'], privilege: "view_spp", customized_title: "SPP - Warehouse"}
        }
      ]
    },
    // {
    //   path: 'aos',
    //   name: 'AOS LCM',
    //   component: AppMain, // 一级路由
    //   meta: { title: 'AOS LCM', roles: ['pmuser'], privilege: "view_aos" },
    //   children: [
    //     {
    //       path: 'retail',
    //       component: () => import('@/views/lcm/aos/retail'), // 子级路由
    //       name: 'Retail',
    //       hidden: true,
    //       meta: { title: 'Retail', roles: ['pmuser'], privilege: "view_atm" },
    //     },
    //     {
    //       path: 'warehouse',
    //       component: () => import('@/views/lcm/aos/warehouse'),
    //       name: 'Warehouse',
    //       meta: { title: 'Warehouse', roles: ['pmuser'], privilege: "view_atm" }
    //     }
    //   ]
    // },
    {
      path: 'underdevelopment',
      component: () => import('@/views/lcm/underdev'),
      name: 'Underdevelopment',
      hidden: true,
      meta: { title: 'Underdevelopment', roles: [ 'admin','pmuser','superadmin'], privilege: "view_aos" }
    },
    {
      path: 'slcm_detail',
      component: () => import('@/views/lcm/seamless_lcmstatus'),
      name: 'Seamless LCM Status',
      hidden: true,
      meta: { title: 'Seamless LCM Status', roles: [ 'admin','pmuser','superadmin'], privilege: "view_aos" }
    },
    {
      path: 'lcm_plans',
      component: () => import('@/views/lcm/seamless_lcmplan'),
      name: 'Seamless LCM Plan',
      hidden: true,
      meta: { title: 'Seamless LCM Plan', roles: [ 'admin','pmuser','superadmin'], privilege: "view_aos" }
    },
    {
      path: 'siabaos',
      component: () => import('@/views/lcm/aos'),
      name: 'Siab AOS',
      hidden: true,
      meta: { title: 'Siab AOS LCM' , roles: ['admin','pmuser','superadmin'],privilege:"view_aos"}
    },
    {
      path: 'wiabaos',
      component: () => import('@/views/lcm/aos/warehouse'),
      name: 'Wiab AOS',
      hidden: true,
      meta: { title: 'Wiab AOS LCM' , roles: ['admin','pmuser','superadmin'],privilege:"view_aos"}
    },
    {
      path: 'seamless_lcm',
      component: () => import('@/views/lcm/seamless_lcm'),
      name: 'Seamless LCM(Beta)',
      meta: { title: 'Seamless LCM(Beta)', roles: [ 'admin','pmuser','superadmin'], privilege: "view_slcm" }
    },
    // {
    //   path: 'ntx_move',
    //   component: () => import('@/views/lcm/ntx_move'),
    //   name: 'NTX Move',
    //   meta: { title: 'NTX Move', roles: [ 'pmuser'], privilege: "view_move" }
    // },
    {
      path: 'ntx_move_wh',
      component: () => import('@/views/lcm/ntx_move_wh'),
      name: 'NTX Move WH',
      meta: { title: 'NTX Move WH', roles: ['pmuser'], privilege: "view_move" }
    },
    {
      path: 'maintenance',
      component: () => import('@/views/lcm/maintenance'),
      name: 'Auto maintenance',
      meta: { title: 'Auto maintenance', roles: [ 'pmuser'], privilege: "view_atm" }
    },
    // {
    //   path: 'new_cluster',
    //   component: () => import('@/views/lcm/new_cluster'),
    //   name: 'New Cluster',
    //   meta: { title: 'New Cluster', roles: [ 'pmuser'], privilege: "view_atm" }
    // },
    {
      path: 'new_cluster',
      name: 'Create Cluster',
      component: AppMain, // 一级路由
      meta: { title: 'Create Cluster', roles: ['pmuser'], privilege: "view_atm" },
      children: [
        {
          path: 'new_siab_cluster',
          component: () => import('@/views/lcm/new_cluster/new_siab_cluster'), // 子级路由
          name: 'New Siab',
          meta: { title: 'New Siab', roles: ['pmuser'], privilege: "view_atm" },
        },
        {
          path: 'new_wiab_cluster',
          component: () => import('@/views/lcm/new_cluster/new_wiab_cluster'),
          name: 'New Wiab',
          meta: { title: 'New Wiab', roles: ['pmuser'], privilege: "view_atm" }
        }
      ]
    },
    {
      path: 'toolbox',
      component: () => import('@/views/lcm/toolbox'),
      name: 'Toolbox',
      meta: { title: 'Toolbox', roles: [ 'pmuser'], privilege: "view_atm" }
    },
    {
      path: 'incident',
      component: () => import('@/views/lcm/incident'),
      name: 'Incident handler',
      meta: { title: 'Incident handler', roles: [ 'pmuser'], privilege: "view_atm" }
    }
  ]
}
export default lcmRouter

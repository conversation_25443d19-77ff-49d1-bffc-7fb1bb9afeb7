import sys
import traceback

from flask import Blueprint, jsonify
from flask_restful import Api
from marshmallow import ValidationError
from werkzeug.exceptions import HTTPException
from business.generic.base_up_exception import BaseUpException


class ExtendedApi(Api):
    def handle_error(self, err):
        """It helps preventing writing unnecessary
        try/except block though out the application
        """
        exc = sys.exception()  # Python 3.11+
        tb_str = ''.join(traceback.format_exception(type(exc), exc, exc.__traceback__))
        print(tb_str)
        # Handle HTTPExceptions
        if isinstance(err.exc, ValidationError):
            response = {
                'name': 'ValidationError',
                'description': err.exc.args[0]['_schema'][0],
            }
            return response, err.code
        if isinstance(err, HTTPException):
            return super().handle_error(err.data if hasattr(err, 'data') else err)
        # If msg attribute is not set,
        # consider it as Python core exception and
        # hide sensitive error info from end user
        # if not getattr(err, 'message', None):
        #     return jsonify({
        #         'message': 'Server has encountered some error'
        #     }), 500
        if isinstance(err, BaseUpException):
            response = {
                'name': type(err).__name__,
                'description': err.msg
            }
            if getattr(err, 'http_code'):
                return response, err.http_code
            return response
        # Handle application specific custom exceptions
        return jsonify(**err.kwargs), err.http_status_code


distributedhosting = Blueprint('distributedhosting', __name__)
api = ExtendedApi(distributedhosting)

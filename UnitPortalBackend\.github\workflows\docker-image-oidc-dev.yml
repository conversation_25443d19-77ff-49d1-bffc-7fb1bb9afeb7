name: Docker Build DHUP Backend OIDC dev

on: workflow_dispatch

env:
  ARTIFACTORY_URL: artifactory.build.ingka.ikea.com
  ARTIFACTORY_REPO: distributedhostingcodecommunity-dhup-docker-dev-local
  IMAGE_NAME: dhup-backend

permissions:
  id-token: write
  contents: read

jobs:
  Build_and_push_image_to_Artifactory:
    runs-on: mgke-prod
    outputs:
      GIT_BRANCH: ${{ steps.meta.outputs.tags }}
      SHA_SHORT: ${{ steps.runtime-variables.outputs.SHA_SHORT }}
      UNIX_DATE: ${{ steps.runtime-variables.outputs.UNIX_DATE }}
      FULL_IMAGE_URI: ${{ steps.compiled-variable.outputs.FULL_IMAGE_URI }}
    steps:
      - name: Checkout local repository
        uses: actions/checkout@v4
        with:
          clean: 'true'
          fetch-tags: 'true'

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.ARTIFACTORY_URL }}/${{ env.ARTIFACTORY_REPO }}/${{ env.IMAGE_NAME }} #Becomes something like "artifactory.build.ingka.ikea.com/distributedhostingcodecommunity-dhup-docker-dev-local/dhup-backend-test:latest"

      - name: Setup JFrog CLI
        uses: jfrog/setup-jfrog-cli@v4
        id: login
        env:
          JF_URL: https://${{ env.ARTIFACTORY_URL }}
        with:
          oidc-provider-name: "ghes-prod"
          oidc-audience: "jfrog-github"

      - name: Login to Artifactory
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ARTIFACTORY_URL }}
          username: ${{ steps.login.outputs.oidc-user }}
          password: ${{ steps.login.outputs.oidc-token }}
          
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set runtime variables
        id: runtime-variables
        run: |
          echo "BUILD_DATE=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_OUTPUT
          echo "SHA_SHORT=$(git rev-parse --short "$GITHUB_SHA")" >> $GITHUB_OUTPUT
          echo "UNIX_DATE=$(date +'%s')" >> $GITHUB_OUTPUT

      - name: Set compiled variable
        id: compiled-variable
        run: |
          echo "FULL_IMAGE_URI=${{ steps.meta.outputs.tags }}-${{ steps.runtime-variables.outputs.SHA_SHORT }}-${{ steps.runtime-variables.outputs.UNIX_DATE }}" >> $GITHUB_OUTPUT

      - name: Push to artifactory
        uses: docker/build-push-action@v6
        with:
          push: true
          context: .
          tags: ${{ steps.meta.outputs.tags }}-${{ steps.runtime-variables.outputs.SHA_SHORT }}-${{ steps.runtime-variables.outputs.UNIX_DATE }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BUILD_TAG=${{ github.ref_name }}
            BUILD_DATE=${{ steps.runtime-variables.outputs.BUILD_DATE }}
            GIT_COMMIT_ID=${{ steps.runtime-variables.outputs.SHA_SHORT }}
    
  Update-GitOps-Image-Tag:
    needs: Build_and_push_image_to_Artifactory
    runs-on: mgke-prod
    steps:

      - name: Checkout GitOps repository
        uses: actions/checkout@v4
        with:
          repository: 'DH-Community/argocd-up'
          ssh-key: ${{ secrets.DHUP_GITOPS_DEPLOY_KEY_PRIV }}
          path: argocd-up
          ref: 'main'
          github-server-url: 'https://git.build.ingka.ikea.com'
          clean: 'true'
          fetch-tags: 'true'
          ssh-known-hosts: 'git.build.ingka.ikea.com ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBLL3vBMBRwdzq9693APfXdiXQKxqWMFgKdbRGafxvLvpLYyXH7CeC+V5GbMTIVUnlvhtIBOJCyNhm/lN5Mcy+l8='

      - name: Update GitOps repo with new image
        run: |           
          cd argocd-up
          FULL_IMAGE_URI="${{ needs.Build_and_push_image_to_Artifactory.outputs.FULL_IMAGE_URI }}"

          #Use "," as sed delimiter to avoid the slashes in the link to be interpreted as delimiters. Here we'll replace the target image URI
          sed -i -E "s,image: .*,image: $FULL_IMAGE_URI," UnitPortal/up-backend-deployment.yaml

          #Prepare commit
          git config --global user.name "Github Actions"
          git config --global user.email "<EMAIL>"

          git commit -a -m "Bump target image to "${{ needs.Build_and_push_image_to_Artifactory.outputs.SHA_SHORT }}" of Unit Portal Backend."
          git push

<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <span class="right-menu-item" >{{ welcome_word }}</span>
        <search id="header-search" class="right-menu-item" />

        <error-log class="errLog-container right-menu-item hover-effect" />

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="Global Size" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>

      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <!-- <img :src="avatar+'?imageView2/1/w/80/h/80'" class="user-avatar"> -->
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <!-- <router-link to="/profile/index">
            <el-dropdown-item>Profile</el-dropdown-item>
          </router-link> -->
          <router-link to="/">
            <el-dropdown-item>Dashboard</el-dropdown-item>
          </router-link>
          <a target="_blank" href="https://dhapi.ikea.com/swagger-ui/">
            <el-dropdown-item>REST API Explorer</el-dropdown-item>
          </a>
          <el-dropdown-item @click.native="showapiuti()">About</el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display:block;">Log Out</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  <!-- </div> -->
  <!-- <div class="app-container"> -->
    <el-dialog :visible.sync="dialogapiutilVisible" :title="'UnitPortal Utilization'" :close-on-click-modal="false" @close="closeapitildialog" >
            <div style="float: right;">
              <el-date-picker
                v-model="value9"
                type="daterange"
                start-placeholder="Start Date"
                end-placeholder="End Date"
                size="mini"
                >
              </el-date-picker>
              <el-button class="filter-item"  type="primary" size="mini" @click="handle_tasks_filters" style="margin-left: 6px;">
              Search
            </el-button>
          </div>
      <div style="font-size:22px;margin-left:2%; padding: 0 6px">
        Traffic
      </div>
      <div style="padding: 4px 0;">
        <el-card  class="box-card">
          <el-row :gutter="8" class="panel-group">
            <el-col  :lg="6" class="card-panel-col">
              <div class="card-panel" >
                <div class="card-panel-description">
                  <div class="card-panel-text">
                    Dashboard : {{tratotal['dashboard']}}
                  </div>
                </div>
              </div>
            </el-col>
            <el-col  :lg="6" class="card-panel-col">
              <div class="card-panel">
                
                <div class="card-panel-description">
                  <div class="card-panel-textpm" >
                    PM :  {{tratotal['pm']}}
                  </div>
                </div>
              </div>
            </el-col>
            <el-col  :lg="6" class="card-panel-col">
              <div class="card-panel">
                
                <div class="card-panel-description">
                  <div class="card-panel-text">
                    Workload : {{tratotal['workload']}}
                  </div>
                </div>
              </div>
            </el-col>
            <el-col  :lg="6" class="card-panel-col">
              <div class="card-panel" >
                
                <div class="card-panel-description">
                  <div class="card-panel-text">
                    Maintenance :{{ tratotal['maintenance'] }}
                  </div>
                </div>
              </div>
            </el-col>
            
          </el-row>
        </el-card>
      </div>
      <div style="font-size:22px;margin-left:2%; padding: 20px 6px 4px 4px">
        API Utilization  <span class="link-type" style="font-size:14px; float: right; margin-right: 6px;"  @click="dialogapiVisible= true"> More</span>
      </div>
      <div style="padding: 4px 0;">
        <el-card  class="box-card">
          <el-row :gutter="8" class="panel-group">
              <el-col  :lg="12" class="card-panel-col">
                <div class="card-panel" >
                <div class="card-panel-description">
                  <div class="apicard-panel-text" style="margin-left: 60%;">
                    {{apilist[0].router}} :{{ apilist[0].times }}
                  </div>
                </div>
              </div>
              </el-col>
              <el-col  :lg="12" class="card-panel-col">
                <div class="card-panel" >
                  <div class="card-panel-description">
                    <div class="apicard-panel-text" style = "margin-left: 60%;">
                      {{apilist[1].router}} :{{ apilist[1].times }}
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="8" class="panel-group">
              <el-col  :lg="12" class="card-panel-col">
                <div class="card-panel" >
                  
                  <div class="card-panel-description">
                    <div class="apicard-panel-text" style="margin-left: 60%;">
                      {{apilist[2].router}} :{{ apilist[2].times }}
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col  :lg="12" class="card-panel-col">
                <div class="card-panel" >
                  
                  <div class="card-panel-description">
                    <div class="apicard-panel-text" style="margin-left: 60%;">
                      {{apilist[3].router}} :{{ apilist[3].times }}
                    </div>
                  </div>
                </div>
              </el-col>
              
            </el-row>
          </el-card>
      </div>
      <div style="font-size:22px;margin-left:2%; padding: 20px 6px 4px">
        Task Execution History
      </div>
      <div style="padding: 4px 0;">
          <el-card  class="box-card">
            
            <el-table :key="tableKey" 
                      v-loading="listLoading" 
                      :data="currentapitasklist" 
                      border 
                      fit 
                      highlight-current-row 
                      style="width: 100%;" 
                      class='apitask-table'
                      ref='apitasktable'>
              <el-table-column label="TaskName" class-name="status-col" min-width="13%" align="center"  prop="taskname" overflow:hidden >
                <template slot-scope="{row}">
                  <span >{{ row.taskname}}</span>
                </template>
              </el-table-column>
              <el-table-column label="ExecutionTime" class-name="status-col" min-width="13%" align="center"  prop="times" overflow:hidden >
                <template slot-scope="{row}">
                  <span >{{ row.times}}</span>
                </template>
              </el-table-column>
            </el-table>
            <!-- <pagination v-show="total>0" :total="total" :page.sync="listQu/ery.page" :limit.sync="listQuery.limit" @pagination="set_page" /> -->
          </el-card>
        </div>
        <!-- </template> -->
    </el-dialog>
    <el-dialog :visible.sync="dialogapiVisible" :title="'API Utilization'" :close-on-click-modal="false" >
      <el-table :data="apiutilist" border fit highlight-current-row style="width: 100%" max-height="500" @sort-change="apisortChange" >
        <el-table-column prop="taskrouter" label="API Path"  min-width="40%" sortable="custom">
          <template slot-scope="{row}">
            <span>{{ row.taskrouter }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="times" label="Times" min-width="15%"  sortable="custom">
          <template slot-scope="{row}">
            <span>{{ row.times }}</span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogapiVisible = false">OK</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import ErrorLog from '@/components/ErrorLog'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import splitPane from 'vue-splitpane'
import Pagination from '@/components/Pagination'
import { WebPageList, APIUtilizationLogList } from '@/api/upwidgets'
import { GetSPPTasks, GetAOSLCMTasks, Get_Tasklist_Move, Get_DSC_Tasks, Get_PWRotate_Tasks, Get_CER_Tasks} from '@/api/automation'
import { GetNTXPMTasks } from '@/api/nutanixpm'
import { GetSLIPMTasks } from '@/api/simplivity'
import { GetCleanupWorkloadTasks, GetWorkloadTasks } from '@/api/nutanix'

// import { forEach } from 'mock/user'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    ErrorLog,
    Screenfull,
    SizeSelect,
    Search,
    splitPane,
    Pagination
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device'
    ])
  },
  created() {
    this.get_webpage_list(),
    // this.get_apiutilization_list(),
    this.get_apiutilizationlog_list()
    // this.handle_webpagenum()
    // this.handle_tasks()
    // this.handle_tasks_filters()
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    showapiuti() {
      this.currentapitasklist = [];
      this.dialogapiutilVisible = true;
      this.handle_tasks();
    },
    get_aostasklist() {
      return GetAOSLCMTasks(this.$store.getters.token).then(response => {
        this.tasks.aostaskslist = response.data;
        this.tasks.atmtotal += this.tasks.aostaskslist.length;
        this.exctasks.atm.data.push(this.tasks.aostaskslist);
      });
    },
    get_spptasklist() {
      return GetSPPTasks(this.$store.getters.token).then(response => {
        this.tasks.spptaskslist = response.data;
        this.tasks.atmtotal += this.tasks.spptaskslist.length;
        this.exctasks.atm.data.push(this.tasks.spptaskslist);
      });
    },
    get_movetasklist() {
      return Get_Tasklist_Move(this.$store.getters.token).then(response => {
        this.tasks.movetaskslist = response.data;
        this.tasks.atmtotal += this.tasks.movetaskslist.length;
        this.exctasks.atm.data.push(this.tasks.movetaskslist);
      });
    },
    get_dsctasklist() {
      return Get_DSC_Tasks(this.$store.getters.token).then(response => {
        this.tasks.dsctaskslist = response.data;
        this.tasks.atmtotal += this.tasks.dsctaskslist.length;
        this.exctasks.atm.data.push(this.tasks.dsctaskslist);
      });
    },
    get_pwrotatetasklist() {
      return Get_PWRotate_Tasks(this.$store.getters.token).then(response => {
        this.tasks.pwrotaskslist = response.data;
        this.tasks.atmtotal += this.tasks.pwrotaskslist.length;
        this.exctasks.atm.data.push(this.tasks.pwrotaskslist);
      });
    },
    get_certasklist() {
      return Get_CER_Tasks(this.$store.getters.token).then(response => {
        this.tasks.certaskslist = response.data;
        this.tasks.atmtotal += this.tasks.certaskslist.length;
        this.exctasks.atm.data.push(this.tasks.certaskslist);
      });
    },
    get_ntxpmtasklist() {
      return GetNTXPMTasks(this.$store.getters.token).then(response => {
        this.tasks.ntxpmtaskslist = response.data;
        this.tasks.pmtotal += this.tasks.ntxpmtaskslist.length;
        this.exctasks.pm.data.push(this.tasks.ntxpmtaskslist);
      });
    },
    get_slipmtasklist() {
      return GetSLIPMTasks(this.$store.getters.token).then(response => {
        this.tasks.slipmtaskslist = response.data;
        this.tasks.pmtotal += this.tasks.slipmtaskslist.length;
        this.exctasks.pm.data.push(this.tasks.slipmtaskslist);
      });
    },
    get_cwltasklist() {
      return GetCleanupWorkloadTasks(this.$store.getters.token).then(response => {
        this.tasks.cwltaskslist = response.data;
        this.tasks.atmtotal += this.tasks.cwltaskslist.length;
        this.exctasks.atm.data.push(this.tasks.cwltaskslist);
      });
    },
    get_wltasklist() {
      return GetWorkloadTasks(this.$store.getters.token).then(response => {
        this.tasks.wltaskslist = response.data;
        this.tasks.wltotal += this.tasks.wltaskslist.length;
        this.exctasks.atm.data.push(this.tasks.wltaskslist);
      });
    },
    handle_tasks() {
      const tasks = [
        this.get_aostasklist(),
        this.get_spptasklist(),
        this.get_movetasklist(),
        this.get_dsctasklist(),
        this.get_pwrotatetasklist(),
        this.get_certasklist(),
        this.get_ntxpmtasklist(),
        this.get_slipmtasklist(),
        this.get_cwltasklist(),
        this.get_wltasklist(),
      ];

      Promise.all(tasks)
        .then(() => {
          console.log("Data loaded successfully");
        })
        .catch(error => {
          console.error("Loaded with error:", error);
        });
    },
    handle_tasksdata() {
      this.exctasks.pm.data.append(this.tasks.slipmtaskslist)
    },
    handle_tasks_filters() {
      if(this.value9){
        this.currentapitasklist=[]
        let utctime_start =new Date( this.value9[0].getTime() + 60*1000*this.value9[0].getTimezoneOffset())
        let utctime_end =new Date( this.value9[1].getTime() + 60*1000*this.value9[1].getTimezoneOffset())
        let arr = []
        //Task Execution history fileter
        let pmtotal=0
        let wltotal=0
        let atmtotal = 0
        // let pm = [this.tasks.slipmtaskslist]
        for (let p of this.exctasks.pm.data) {
          let newlist = p.filter(function (e) {
            let taskdate=new Date(e.createdate)
            return  taskdate >= utctime_start && taskdate <= utctime_end
          })
          pmtotal = pmtotal+newlist.length
      
        }
        for (let a of this.exctasks.atm.data) {
          if(!a){continue}
          let newlist = a.filter(function (e) {
            let taskdate=new Date(e.create_date)
            return  taskdate >= utctime_start && taskdate <= utctime_end
          })
          atmtotal = atmtotal+newlist.length
        }
        for (let w of this.exctasks.wl.data) {
          let newlist = w.filter(function (e) {
            let taskdate=new Date(e.create_date)
            return  taskdate >= utctime_start && taskdate <= utctime_end
          })
          wltotal = wltotal+newlist.length
        }
        this.currentapitasklist=[],
        this.currentapitasklist.push(
              {
                taskname: "PM",
                times:pmtotal
              },
              {
                taskname: "Workload",
                times:wltotal
              },
              {
                taskname: "Automation",
                times:atmtotal
              },
            )
        //API Utilization filter
        this.apiutilist=[]
        this.newapilist.forEach(item=>{
          let r = this.totalapilist.filter(function (e) {
            // if (e.api_path.toString().indexOf(item) != -1) {
            //   return true
            // }
            // console.log(e)
            let apidate=new Date(e.date)
            return  apidate >= utctime_start && apidate <= utctime_end && e.api_path.toString().indexOf(item) != -1
          })
          this.apiutilist.push(
            {
              // taskname: "NA",
              taskrouter:item,
              times:r.length
            }
          )
        })
        this.handle_apilist()
      //wabpages filter
        let nwlist = this.temp_webpagelist.filter(function (e) {
          let webdate=new Date(e.date)
          return  webdate >= utctime_start && webdate <= utctime_end
        })
        this.handle_webpagenum(nwlist)
      } else {
        this.currentapitasklist= [],
        this.currentapitasklist.push(
          {
            taskname: "PM",
            times:this.tasks.pmtotal
          },
          {
            taskname: "Workload",
            times:this.tasks.wltotal
          },
          {
            taskname: "Automation",
            times:this.tasks.atmtotal
          },
        )
        this.get_webpage_list(),
        this.get_apiutilizationlog_list()
        this.handle_apilist()
        // this.handle_webpagenum()
        // this.handle_tasks()
        // this.currentapitasklist=this.tempapitasklist
        }
    },
    closeapitildialog() {
      this.currentapitasklist = []
      this.value9=''
    },
    handle_apilist(){
      this.apilist.forEach(item=> {
        item.times=0
          this.apiutilist.forEach(i=>{
            if (i.taskrouter.toString().search(item.router) != -1) {
              item.times=item.times+i.times
            }
          })
        })
      this.filtered_list=this.apiutilist
    },
    handle_tasks_bydate(datetype, arr) {
      let d = datetype
      let _this =this
      let utctime_start =new Date( this.value9[0].getTime() + 60*1000*this.value9[0].getTimezoneOffset())
      let utctime_end =new Date( this.value9[1].getTime() + 60*1000*this.value9[1].getTimezoneOffset())
      let newlist = arr.filter(function(e, _this) {
        let a = _this.tratotal
        let taskdate=new Date(e.date)
        return  taskdate >= utctime_start && taskdate <= utctime_end
      })
      return newlist
    },
    get_apiutilizationlog_list(){
      this.apiutilist=[]
      APIUtilizationLogList(this.$store.getters.token).then(response => {
        this.totalapilist = response.data
        this.newapilist = this.remove_duplicate(response.data)
        this.newapilist.forEach(item=>{
          let r = response.data.filter(function (e) {
            if (e.api_path.toString().indexOf(item) != -1) {
              return true
            }
          })
          this.apiutilist.push(
            {
              // taskname: "NA",
              taskrouter:item,
              times:r.length
            }
          )
          this.apilist.forEach(item=> {
            if (r[0].api_path.toString().search(item.router) != -1) {
              item.times=item.times+r.length
            }
          
          })
        })
        this.filtered_list=this.apiutilist
        this.listLoading=false
      })
    },
    handleapifilter() {
      this.currentapitasklist=[]
      let utctime_start =new Date( this.value9[0].getTime() + 60*1000*this.value9[0].getTimezoneOffset())
      let utctime_end =new Date( this.value9[1].getTime() + 60*1000*this.value9[1].getTimezoneOffset())
      
      this.apitasklist.forEach(item => {
        let result = this.tempapitasklist.filter(function(e){
          let apidate=new Date(e.date)
          return  apidate >= utctime_start && apidate <= utctime_end && e.api_path.toString().search(item.router) != -1
        })
        this.currentapitasklist.push(
            {
              taskname: item.name,
              times:result.length
            }
          )
      })
    },
    customized_search(str, search){
      if(!str){
        return false
      }
      if(["number","string"].includes(typeof(str))){
        // check if str is either a number or a string
        return str.toString().toLowerCase().search(search) != -1
      }
      return false
    },
    get_webpage_list(){
      WebPageList(this.$store.getters.token).then(response => {
        if (response.status == 200) {
          // let new_webpagelist = response.data.map((e) => {
          //   e.times = parseInt(e.times)
          //   return e
          // })
          this.temp_webpagelist = response.data
          this.handle_webpagenum(response.data)
        }
      });
    },
    handle_webpagenum(arr) {
      for (let t in this.traffic) {
        this.tratotal[t] = 0
        this.traffic[t].forEach(item => {
          let r = arr.filter(function (e) {
            return e.page_name == item
          })
          if(r.length){
            this.tratotal[t] = this.tratotal[t]+r.length
          }
          })
        }
        // this.tratotal[t]=r.length
      // }
    },
    remove_duplicate(arr) {
      //去除重复值
      const newArr = []
      arr.forEach(item => {
        if (!newArr.includes(item.api_path)) {
          newArr.push(item.api_path)
        }
      })
      return newArr
    },
    set_page(){
      // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start,end)
    },
    handleFilter() {
      this.listQuery.page = 1
      },
    apisortChange(data) {
      const { prop, order } = data
      if (prop === 'id') {
        this.sortByID(order)
      }
      let flag_num = order=="ascending" ? 1 : -1
      this.filtered_list.sort((item1,item2)=>(
        (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
      ))
      this.set_page()
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+id'
      } else {
        this.listQuery.sort = '-id'
      }
      this.handleFilter()
    },
  },
  data() {
    return {
      welcome_word: "Hi: " + this.$store.getters.name,
      listLoading: true,
      dialogapiVisible: false,
      value9:'',
      tableKey: 0,
      filtered_list: null,
      current_list: null,
      page_list: null,
      currenttextshow: false,
      dialogapiutilVisible: false,
      refreshKey: 0,
      listQuery: {
        page: 1,
        limit: 18,
        status: '',
        sort: '+id'
      },
      filter:{
        startdate:'',
        enddate:'',
      },
      lcm : '',
      aos : '',
      ntxpm : '',
      slipm : '',
      toolbox : '',
      workload : '',
      template : '',
      dashboard : '',
      ntxmovewh : '',
      automaintenance : '',
      pmnumber: '',
      maintenancenum :'',
      workloadnum :'',
      starnum: 500,
      total: 0,
      temp_webpagelist:'',
      newapilist:[],
      totalapilist:'',
      apiutilist: [],
      tempapiutilist: [],
      currentapitasklist: [],
      tempapitasklist: [],
      startdate:'',
      enddate:'',
      
      apilist: [
        {
          name: 'login',
          times:0,
          router:'/api/v1/login'
        },
        {
          name: 'workload',
          times:0,
          router:'/api/v1/ntx/workload'
        },
        {
          name: 'pm',
          times:0,
          router:'/api/v1/pm'
        },
        {
          name: 'automation',
          times:0,
          router:'/api/v1/ntx/automation'
        },
      ],
      traffic:{
        dashboard: ['Dashboard'],
        pm: ['NutanixPM', 'SimpliVityPM'],
        workload: ['Workload', 'Template'],
        maintenance: ['Auto maintenance', 'Toolbox', 'NTX Move WH', 'AOS', 'LCM',],
        
      },
      tratotal: {},
      tasks: {
        aostaskslist: '',
        spptaskslist: '',
        movetaskslist:'',
        dsctaskslist: '',
        pwrotaskslist:'',
        certaskslist: '',
        nxpmtaskslist: '',
        slipmtaskslist: '',
        cwltaskslist: '',
        wltaskslist: '', 
        pmtotal:0,
        atmtotal: 0,
        wltotal:0,
      },
      exctasks:{
        pm:{
          name: 'PM',
          data: [],
          pmtotal:0
        },
        atm:{
          name: 'Automation',
          data: [],
          atmtotal:0
        },
        wl:{
          name: 'Workload',
          data: [],
          wltotal:0
        },
      },
    }
  },
  mounted() {
    this.handle_tasks();
  },
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
  .el-card {
    border: none; /* 移除默认边框 */
    // border-top: 2px solid #1890ff; /* 上边框 */
    border: 1px solid #a2a4a5fd; /* 下边框 */
    position: relative; /* 为伪元素定位做准备 */
    border-radius: 8px;
    margin-left: 8px;
  }
  .panel-group {
    margin-top: 4px;

    .card-panel-col {
      margin-bottom: 0;
      padding:2px
    }

    .card-panel {
      
      border-radius: 8px;
      height: 50px;
      cursor: pointer;
      font-size: 12px;
      position: relative;
      overflow: hidden;
      color: #666;
      background: #fff;
      box-shadow: 2px 0px 4px 2px rgba(0, 0, 0, .1);
      // border:2px rgba(0, 0, 0, .05);
      border: 1px solid #a2a4a5fd;

      &:hover {
        .card-panel-icon-wrapper {
          color: #fff;
        }

        .icon-people {
          background: #40c9c6;
        }

        .icon-message {
          background: #36a3f7;
        }

        .icon-money {
          background: #f4516c;
        }

        .icon-shopping {
          background: #34bfa3
        }
      }
      
      .card-panel-description {
        float: right;
        font-weight: bold;
        margin-top: 18px;
        white-space: nowrap;
        // margin-right:58px;
        // margin-left: 20%;
        
        // text-align: center;
        // // float: inline-start;
        width:36px;
        position:absolute;
        .card-panel-text {
          line-height: 16px;
          color: rgba(0, 0, 0, 0.45);
          font-size: 16px;
          margin-bottom: 2px;
          position:absolute;
          text-align: center;
          // margin:center;
          padding: 0 60%         
        }
        .apicard-panel-text {
          line-height: 16px;
          color: rgba(0, 0, 0, 0.45);
          font-size: 16px;
          margin-bottom: 2px;
          // margin-left: 0;
          position:absolute;
          text-align: justify;
          white-space: nowrap;
          margin-left: 25%;
        }
        .card-panel-textpm {
          line-height: 16px;
          color: rgba(0, 0, 0, 0.45);
          font-size: 16px;
          margin-bottom: 2px;
          position:absolute;
          text-align: center;
          padding: 0 10% 0 70%  
        }
      }
    }
  }
</style>

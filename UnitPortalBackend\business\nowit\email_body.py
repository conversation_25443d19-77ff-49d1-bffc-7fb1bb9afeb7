def inc_remainder_plain_body(
                    all_incidents_number,
                    all_no_assignee_incidents_num,
                    total_breached_incnumber,
                    critical_case_length,
                    urgent_case_length,
                    high_case_length,
                    all_gdh_ritm_task_list_num,
                    no_assignee_ritm_task_list_num,
                    all_breached_gdh_ritm_task_list_num,
                    all_gdh_ctask_list_num,
                    ctask_no_assignee_ctask_list_num,
                    ctask_start_in_one_day_list_num,
                    breached_ctask_list_num,
                    no_assignee_in_one_week_ctask_list_num,
                    cn_region_tasker_str,
                    eu_region_tasker_str,
                    us_region_tasker_str,
                    cn_region_incer_str,
                    eu_region_incer_str,
                    us_region_incer_str,
                    cn_region_dutyer_str,
                    eu_region_dutyer_str,
                    us_region_dutyer_str,
                    cn_region_tasker_7day_str,
                    eu_region_tasker_7day_str,
                    us_region_tasker_7day_str

                    
):
    
    body = f"""
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GDH Incident Summary Report</title>
    <style>
        body {{
            width: 100%; 
            font-family: Verdana, sans-serif; 
            background-color: #ffffff; 
            margin: 0; 
            padding: 20px;  
            font-size: 16px; 
        }}
        .container {{
            max-width: 80%;
            margin: 0 10%;
            background-color: #ffffff;
        }}
        img {{
            width: 100%; 
            height: auto; 
            object-fit: cover;
            display: block;
            margin: 0 auto;
            margin-bottom: 30px;
        }}
        h1 {{
            color: #000000; 
            font-size: 25px;
        }}
        p {{ 
            color: #000000;
        }}
        .italic {{
            font-style: italic;  
        }}

        table {{
            width: 80%;
            border-collapse: collapse;
            margin: 20px 0;
            border: none;
        }}
        th, td {{
            width: auto;
            padding: 10px 0px 10px 20px;
            text-align: left;
            border-left: none;
            border-right: none;
            border-top: 1px solid black;
            border-bottom: 1px solid black;
            vertical-align: top;
            line-height: 1;

        }}
        th {{
            background-color: transparent;
            color: black;
        }}
    </style>
 </head>
 <body>
     <div>  
        <img src="cid:image1"/>  
     </div>
    <div class="container">
        <h1>Hej!</h1>
        <p>Here is a summary of today's incidents, please handle it in a timely manner!</p>
        <p class="italic"><span style="color: red;">*</span> This mail was generated automatically by Incident-Handler Auto-Reminder. Please don't reply to the email!</p>
        <p class="italic"><strong>Duty Person:</strong> CN:&nbsp;&nbsp;{cn_region_dutyer_str}&nbsp;&nbsp;EU:&nbsp;&nbsp;{eu_region_dutyer_str}&nbsp;&nbsp;US:&nbsp;&nbsp;{us_region_dutyer_str}</p>
          <div class="content">
            <table>
                <thead>
                    <tr>
                        <th style="width: 10%;">Item</th>
                        <th style="width: 30%;">Person_In_Charge</th> 
                        <th style="width: 13%;">Breached</th>
                        <th style="width: 13%;">No_Assignee</th>
                        <th style="width: 13%;">Total</th>
                        <th style="width: 21%;">Comment</th>
                    </tr>
                </thead>
                <tbody>
                    <tr> 
                        <td><a href="https://ingkaprod.service-now.com/now/nav/ui/classic/params/target/%24pa_dashboard.do%3Fsysparm_dashboard%3D5b25319e87d41554efdc65760cbb35a0%26sysparm_tab%3D6048fd1687d81554efdc65760cbb35dc%26sysparm_cancelable%3Dtrue%26sysparm_editable%3Dfalse%26sysparm_active_panel%3Dfalse" style="text-decoration: none; color: #0000EE;">INC</td>
                        <td>CN:&nbsp;&nbsp;{cn_region_incer_str}<br>EU:&nbsp;&nbsp;{eu_region_incer_str}<br>US:&nbsp;&nbsp;{us_region_incer_str}</td> 
                        <td>{total_breached_incnumber}</td>
                        <td>{all_no_assignee_incidents_num}</td>
                        <td>{all_incidents_number}</td>
                        <td>Critical:&nbsp;&nbsp;{critical_case_length} <br>Urgent:&nbsp;&nbsp;{urgent_case_length} <br>High:&nbsp;&nbsp;{high_case_length}</td>
                    </tr>
                    <tr>
                        <td><a href="https://ingkaprod.service-now.com/now/nav/ui/classic/params/target/%24pa_dashboard.do%3Fsysparm_dashboard%3D5b25319e87d41554efdc65760cbb35a0%26sysparm_tab%3D96caae128718d554efdc65760cbb3547%26sysparm_cancelable%3Dtrue%26sysparm_editable%3Dundefined%26sysparm_active_panel%3Dfalse" style="text-decoration: none; color: #0000EE;">CTASK</td>
                        <td>CN:&nbsp;&nbsp;{cn_region_tasker_str}<br>EU:&nbsp;&nbsp;{eu_region_tasker_str}<br>US:&nbsp;&nbsp;{us_region_tasker_str}</td>
                        <td>{breached_ctask_list_num}</td>
                        <td>{ctask_no_assignee_ctask_list_num}</td>
                        <td>{all_gdh_ctask_list_num}</td>
                        <td>Start in One Day:&nbsp;&nbsp;{ctask_start_in_one_day_list_num} <br>Next 7 Days no assignee:&nbsp;&nbsp;{no_assignee_in_one_week_ctask_list_num}</td>
                    </tr>
                    <tr>
                        <td><a href="https://ingkaprod.service-now.com/now/nav/ui/classic/params/target/%24pa_dashboard.do%3Fsysparm_dashboard%3D5b25319e87d41554efdc65760cbb35a0%26sysparm_tab%3D64a5399687181554efdc65760cbb35b6%26sysparm_cancelable%3Dtrue%26sysparm_editable%3Dfalse%26sysparm_active_panel%3Dfalse" style="text-decoration: none; color: #0000EE;">RITM</td>
                        <td>CN:&nbsp;&nbsp;{cn_region_tasker_str}<br>EU:&nbsp;&nbsp;{eu_region_tasker_str}<br>US:&nbsp;&nbsp;{us_region_tasker_str}</td>
                        <td>{all_breached_gdh_ritm_task_list_num}</td>
                        <td>{no_assignee_ritm_task_list_num}</td>
                        <td>{all_gdh_ritm_task_list_num}</td>
                        <td> </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <p class="italic"><strong>Next week CHG/TASK/PBR Handling: </strong> CN:&nbsp;&nbsp;{cn_region_tasker_7day_str}&nbsp;&nbsp;EU:&nbsp;&nbsp;{eu_region_tasker_7day_str}&nbsp;&nbsp;US:&nbsp;&nbsp;{us_region_tasker_7day_str}</p>
        <p><a href="https://confluence.build.ingka.ikea.com/pages/viewpage.action?pageId=1056462385" style="text-decoration: none; color: #0000EE;">S&C Daily Operation Roles & Responsibilities</p>
    </div>
  </body>
</html>
 """
    return body
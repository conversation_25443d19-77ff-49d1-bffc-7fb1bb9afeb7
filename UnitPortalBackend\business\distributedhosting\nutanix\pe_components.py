import json
from typing import List

import werkzeug.exceptions as flaskex

from business.distributedhosting.nutanix.automation.dsc_exception import RemoteSiteInUse
from business.distributedhosting.nutanix.nutanix import PrismElement


class RestCluster(PrismElement):
    ENDPOINT = "/cluster"

    def update_cluster(self, payload):
        self.logger.info("Start to update cluster...")
        res, data = self.rest_pe.call_pe_patch(f"{RestCluster.ENDPOINT}", payload=payload)
        if not res:
            raise Exception(f"Failed to update cluster! Reason: {data}")
        self.logger.info("Update cluster: Done")
        return data

    def add_ntp_servers(self, names: List):
        res, data = self.rest_pe.call_pe_post(f"{RestCluster.ENDPOINT}/ntp_servers/add_list", payload=names)
        if not res:
            raise Exception(f"Failed to add ntp servers! Reason: {data}")
        return data

    def get_ntp_servers(self):
        res, data = self.rest_pe.call_pe_get(f"{RestCluster.ENDPOINT}/ntp_servers", api_version=2, retries=10)
        if not res:
            raise Exception(f"Failed to get ntp servers! Reason: {data}")
        return data

    def remove_ntp_server(self, name):
        res, data = self.rest_pe.call_pe_delete(f"{RestCluster.ENDPOINT}/ntp_servers/{name}")
        if not res:
            raise Exception(f"Failed to remove ntp server '{name}'! Reason: {data}")
        return data

    def remove_ntp_servers(self, names: List):
        res, data = self.rest_pe.call_pe_post(f"{RestCluster.ENDPOINT}/ntp_servers/remove_list", payload=names)
        if not res:
            raise Exception(f"Failed to remove ntp servers! Reason: {data}")
        return data

    def add_dns_servers(self, names: List):
        res, data = self.rest_pe.call_pe_post(f"{RestCluster.ENDPOINT}/name_servers/add_list", payload=names)
        if not res:
            raise Exception(f"Failed to add dns servers! Reason: {data}")
        return data

    def get_dns_servers(self):
        res, data = self.rest_pe.call_pe_get(f"{RestCluster.ENDPOINT}/name_servers", api_version=2, retries=10)
        if not res:
            raise Exception(f"Failed to get dns servers! Reason: {data}")
        return data

    def remove_dns_servers(self, names):
        res, data = self.rest_pe.call_pe_post(f"{RestCluster.ENDPOINT}/name_servers/remove_list", payload=names)
        if not res:
            raise Exception(f"Failed to remove dns servers! Reason: {data}")
        return data

    def add_public_key(self, key, name):
        payload = {
            "name": name,
            "key": key
        }
        res, data = self.rest_pe.call_pe_v2_post(f"{RestCluster.ENDPOINT}/public_keys", payload=payload)
        if not res:
            raise Exception(f"Failed to add public key! Reason: {data}")
        return data

    def get_public_keys(self):
        res, data = self.rest_pe.call_pe_get(f"{RestCluster.ENDPOINT}/public_keys", api_version=2)
        if not res:
            raise Exception(f"Failed to get public keys! Reason: {data}")
        return data


class RestDirectoryServices(PrismElement):
    ENDPOINT = "/directory_services"

    def list_directory_services(self):
        self.logger.info("Getting the list of directory services...")
        payload = {
            "kind": "directory_service",
            "length": 100
        }
        res, data = self.rest_pe.call_pc_post(f"{RestDirectoryServices.ENDPOINT}/list", payload=payload)
        if not res:
            raise Exception(f"Failed to get directory services! Reason: {data}")
        return data


class RestEulas(PrismElement):
    ENDPOINT = "/eulas"

    def list_eulas(self):
        res, data = self.rest_pe.call_pe_get(RestEulas.ENDPOINT)
        if not res:
            raise Exception(f"Failed to get EULAS list! Reason: {data}")
        return data

    def accept_eulas(self, username, company_name, job_title):
        payload = {
            "username": username,
            "companyName": company_name,
            "jobTitle": job_title
        }
        res, data = self.rest_pe.call_pe_post(f"{RestEulas.ENDPOINT}/accept", payload=payload)
        if not res:
            raise Exception(f"Failed to set EULAS! Reason: {data}")
        return data


class RestGenesis(PrismElement):
    ENDPOINT = "/genesis"

    def call_genesis(self, value):
        payload = {
            "value": json.dumps(value)
        }
        res, data = self.rest_pe.call_pe_post(self.ENDPOINT, payload=payload)
        if not res:
            raise Exception(f"Failed to call genesis! Reason: {data}")
        return data


class RestProgressMonitor(PrismElement):
    ENDPOINT = "/progress_monitors"

    def list_progress(self, params):
        url = self.ENDPOINT
        if params:
            url += f"?{params}"
        res, data = self.rest_pe.call_pe_get(self.ENDPOINT)
        if not res:
            raise Exception(f"Failed to set pulse! Reason: {data}")
        return data


class RestPulse(PrismElement):
    ENDPOINT = "/pulse"

    def set(self, enable):
        payload = {
            "enable": enable,
            "enableDefaultNutanixEmail": "false",
            "isPulsePromptNeeded": "false"
        }
        res, data = self.rest_pe.call_pe_put(self.ENDPOINT, payload=payload)
        if not res:
            raise Exception(f"Failed to set pulse! Reason: {data}")
        return data


class RestSMTP(PrismElement):
    def set_smtp_config(self, smtp_sender, smtp_server, security="NONE", smtp_user=None, smtp_pass=None, smtp_port=25):
        self.logger.info("Configuring SMTP Settings")
        payload = {
            "address": smtp_server,
            "port": smtp_port,
            "username": smtp_user,
            "password": smtp_pass,
            "secureMode": security,
            "fromEmailAddress": smtp_sender,
            "emailStatus": {
                "status": "UNKNOWN",
                "message": None
            }
        }
        res, data = self.rest_pe.call_pe_put('/cluster/smtp', payload=payload)
        if not res:
            raise Exception(f"Failed to set SMTP config! Reason: {data}")
        return data

    def set_alert_config(self, smtp_receiver, smtp_sender, smtp_server, security="NONE", smtp_user=None, smtp_pass=None,
                         smtp_port=25):
        self.logger.info("Configuring SMTP Alert Settings")
        payload = {
            "emailContactList": [smtp_receiver],
            "enable": False,
            "enableDefaultNutanixEmail": True,
            "skipEmptyAlertEmailDigest": True,
            "defaultNutanixEmail": "<EMAIL>",
            "smtpserver": {
                "address": smtp_server,
                "port": smtp_port,
                "username": smtp_user,
                "password": smtp_pass,
                "secureMode": security,
                "fromEmailAddress": smtp_sender,
                "emailStatus": {
                    "status": "UNKNOWN",
                    "message": None
                }
            },
            "tunnelDetails": {
                "httpProxy": None,
                "serviceCenter": None,
                "connectionStatus": {
                    "lastCheckedTimeStampUsecs": 0,
                    "status": "UNKNOWN",
                    "message": None
                },
                "transportStatus": {
                    "status": "UNKNOWN",
                    "message": None,
                }
            },
            "emailConfigRules": None,
            "emailTemplate": {
                "subjectPrefix": None,
                "bodySuffix": None
            }
        }
        res, data = self.rest_pe.call_pe_put("/alerts/configuration", payload=payload)
        if not res:
            raise Exception(f"Failed to set alert config! Reason: {data}")
        return data


class RestHA(PrismElement):
    ENDPOINT = "/ha"

    def get_config(self):
        res, data = self.rest_pe.call_pe_get(RestHA.ENDPOINT, api_version=2)
        if not res:
            raise Exception(f"Failed to get HA config! Reason: {data}")
        return data

    def enable_ha(self):
        payload = {
            "enable_failover": True,
            "num_host_failures_to_tolerate": 1
        }
        res, data = self.rest_pe.call_pe_put(RestHA.ENDPOINT, payload=payload, api_version=2)
        if not res:
            raise Exception(f"Failed to set HA config! Reason: {data}")
        return data


class RestAuthConfig(PrismElement):
    ENDPOINT = "/authconfig"

    def get_auth_config(self):
        res, data = self.rest_pe.call_pe_get(RestAuthConfig.ENDPOINT, api_version=2)
        if not res:
            raise Exception(f"Failed to get auth config! Reason: {data}")
        return data

    def add_auth_config(self, domain_name, ldap_user, ldap_pass, ldap_fqdn, recursive, ldap_port=3268):
        self.update_auth_config(domain_name, ldap_user, ldap_pass, ldap_fqdn, recursive, ldap_port, is_add=True)

    def remove_auth_config(self, auth_config_name):
        res, data = self.rest_pe.call_pe_delete(
            f"{RestAuthConfig.ENDPOINT}/directories/{auth_config_name}", api_version=2)
        if not res:
            raise flaskex.BadGateway(f"Failed to delete auth config! Reason: {data}")
        return data

    def update_auth_config(self, domain_name, ldap_user, ldap_pass, ldap_fqdn, recursive, ldap_port=3268, is_add=False):
        net_bios = domain_name.split(".")[0]
        ldap_prefix = "ldaps" if ldap_port == "636" or ldap_port == "3269" else "ldap"
        payload = {
            "name": net_bios,
            "domain": domain_name,
            "directoryUrl": f"{ldap_prefix}://{ldap_fqdn}:{ldap_port}",
            "groupSearchType": recursive,
            "directoryType": "ACTIVE_DIRECTORY",
            "connectionType": "LDAP",
            "serviceAccountUsername": ldap_user,
            "serviceAccountPassword": ldap_pass
        }
        if is_add:
            res, data = self.rest_pe.call_pe_post(f"{RestAuthConfig.ENDPOINT}/directories", payload=payload)
        else:
            res, data = self.rest_pe.call_pe_put(f"{RestAuthConfig.ENDPOINT}/directories", payload=payload)
        if not res:
            raise flaskex.BadGateway(f"Failed to update auth config! Reason: {data}")
        return data

    def get_role_mappings(self, auth_config_name):
        res, data = self.rest_pe.call_pe_get(
            f"{RestAuthConfig.ENDPOINT}/directories/{auth_config_name}/role_mappings", api_version=1)
        if not res:
            raise flaskex.BadGateway(f"Failed to get role mappings! Reason: {data}")
        return data

    def delete_role_mapping(self, auth_config_name, role, entity_type):
        res, data = self.rest_pe.call_pe_delete(
            f"{RestAuthConfig.ENDPOINT}/directories/{auth_config_name}/role_mappings?&entityType={entity_type}&role={role}",
            api_version=1
        )
        if not res:
            raise flaskex.BadGateway(f"Failed to delete role mappings! Reason: {data}")
        return data

    def add_role_mapping(self, auth_config_name, entity_values, role, entity_type="GROUP"):
        payload = {
            "directoryName": auth_config_name,
            "role": role,
            "entityType": entity_type,
            "entityValues": entity_values   # list
        }
        res, data = self.rest_pe.call_pe_post(
            f"{RestAuthConfig.ENDPOINT}/directories/{auth_config_name}/role_mappings?&entityType={entity_type}&role={role}",
            payload=payload
        )
        if not res:
            raise flaskex.BadGateway(f"Failed to add role mappings! Reason: {data}")
        return data

    def update_role_mapping(self, auth_config_name, entity_values, role, entity_type="GROUP"):
        # Note: can only update the role mappings with existing role + entity_type
        payload = {
            "directoryName": auth_config_name,
            "role": role,
            "entityType": entity_type,
            "entityValues": entity_values
        }
        res, data = self.rest_pe.call_pe_put(
            f"{RestAuthConfig.ENDPOINT}/directories/{auth_config_name}/role_mappings",
            payload=payload
        )
        if not res:
            raise flaskex.BadGateway(f"Failed to add role mappings! Reason: {data}")
        return data


class StorageContainer(PrismElement):
    ENDPOINT = "/storage_containers"

    def list_storage_containers(self):
        res, data = self.rest_pe.call_pe_get(StorageContainer.ENDPOINT, api_version=2)
        if not res:
            raise flaskex.InternalServerError(f"Failed to get the list of the storage containers. Details: {data}")
        return data

    def list_vdisks(self, sc_uuid):
        res, data = self.rest_pe.call_pe_get(f"{StorageContainer.ENDPOINT}/{sc_uuid}/vdisks", api_version=2)
        if not res:
            raise flaskex.InternalServerError(f"Failed to get vdisks for storage container {sc_uuid}. Details: {data}")
        return data

    def update_container(self, obj):
        res, data = self.rest_pe.call_pe_put(StorageContainer.ENDPOINT, payload=obj, api_version=2)
        if not res:
            raise flaskex.InternalServerError(f"Failed to set storage container. Details: {data}")
        return data


class RestRemoteSite(PrismElement):
    ENDPOINT = "/remote_sites"

    def get_remote_sites(self):
        self.logger.info(f"Getting remote sites for {self.pe}")
        res, data = self.rest_pe.call_pe_get(RestRemoteSite.ENDPOINT, api_version=2)
        if res:
            return data
        raise flaskex.BadGateway(f"Failed to get remote_sites, error: {data}")

    def get_remote_site_detail(self, site_name):
        self.logger.info(f"Getting Remote Site detail for site '{site_name}'")
        res, data = self.rest_pe.call_pe_get(f"/{RestRemoteSite.ENDPOINT}/{site_name}", 1)
        if not res:
            raise flaskex.BadGateway(f"Failed to GET /remote_sites/{site_name}! Response: {data}")
        return data

    def create_remote_site(
        self, remote_site_name, target_ip,
        target_port=2020, compression_enabled=True, ssh_enabled=False, proxy_enabled=False
    ):
        self.logger.info(f"Creating Remote Site : '{remote_site_name}' for {self.pe}")
        payload = {
            "name": remote_site_name,
            "remoteIpPorts": {
                target_ip: target_port,
            },
            "proxyEnabled": proxy_enabled,
            "compressionEnabled": compression_enabled,
            "sshEnabled": ssh_enabled,
            "capabilities": ["BACKUP"],
        }
        res, data = self.rest_pe.call_pe_post(RestRemoteSite.ENDPOINT, payload=payload)    # v1
        if not res:
            raise flaskex.BadGateway(f"Failed to POST /remote_sites! Response: {data}")
        return data

    def update_remote_site(
            self, bw_policy_start_time, bw_policy_end_time, bw_cap_day, bw_cap_night,
            nw_map_obj, remote_site_obj, enable_compression, cvm_list, exists, target_port=2020):
        # TODO
        self.logger.info("Updating remote Site...")
        remote_site_obj.pop('stats', None)
        if len(remote_site_obj["replicationLinks"]) >= 1:
            for _ in remote_site_obj["replicationLinks"]:
                _.pop("stats", None)
        # compose the bandwidth policy object
        remote_site_obj.pop("bandwidthPolicy", None)
        bw_policy_obj = {
            "policyName": f"{remote_site_obj['name']}_Bw_Policy",
            "bandwidthConfigurations": None,
            "defaultBandwidthLimit": bw_cap_night
        }
        if bw_policy_end_time <= bw_policy_start_time:
            bw_policy_obj["bandwidthConfigurations"] = [
                {
                    "startTime": bw_policy_start_time,
                    "endTime": 86340000000,
                    "daysSelected": 127,
                    "bandwidthLimit": bw_cap_day
                },
            ]
            if bw_policy_end_time != 0:         # Avoid startTime and endTime both 0
                self.logger.info("This schedule is cross midnight. Lets add 2 schedules..")
                bw_policy_obj["bandwidthConfigurations"].append({
                    "startTime": 0,
                    "endTime": bw_policy_end_time,
                    "daysSelected": 127,
                    "bandwidthLimit": bw_cap_day
                })
        else:
            self.logger.info("One schedule will do for this timezone...")
            bw_policy_obj["bandwidthConfigurations"] = [{
                "startTime": bw_policy_start_time,
                "endTime": bw_policy_end_time,
                "daysSelected": 127,
                "bandwidthLimit": bw_cap_day
            }]
        for ip in cvm_list:
            remote_site_obj["remoteIpPorts"][ip] = target_port 
        remote_site_obj["bandwidthPolicy"] = bw_policy_obj
        remote_site_obj["bandwidthPolicyEnabled"] = True
        remote_site_obj["compressionEnabled"] = enable_compression
        remote_site_obj["vstoreNameMap"] = {
            "SelfServiceContainer": "SelfServiceContainer"
        }
        l2_network_mappings = []
        if not exists:
            for m in nw_map_obj:
                nw_map_hash = {
                    "srcHypervisorType": "kKvm",
                    "srcNetworkName": m["Source"],
                    "destHypervisorType": "kKvm",
                    "destNetworkName": m["Target"]
                }
                l2_network_mappings.append(nw_map_hash)
            remote_site_obj["networkMapping"] = {
                "uuid": None,
                "l2NetworkMappings": l2_network_mappings
            }
        res, data = self.rest_pe.call_pe_put(RestRemoteSite.ENDPOINT, payload=remote_site_obj, api_version=1, retry=1)
        if not res:
            if "Remote site is in use" in data:
                raise RemoteSiteInUse(f"Update remote site failed due to remote site in use! Response: {data}")
            raise flaskex.BadGateway(f"Failed to update /remote_sites! Response: {data}")
        return data

    def delete_remote_site(self, rs_name):
        self.logger.info(f"Removing remote Site '{rs_name}'...")
        res, data = self.rest_pe.call_pe_delete(f"/remote_sites/{rs_name}", 1)
        if not res:
            raise flaskex.BadGateway(f"Failed to DELETE /remote_sites/{rs_name}! Response: {data}")
        return data
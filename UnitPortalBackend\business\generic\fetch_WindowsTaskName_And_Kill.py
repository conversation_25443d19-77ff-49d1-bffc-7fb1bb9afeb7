# Python Script
# Created by : <PERSON><PERSON>
# 31-Oct-2023
# <PERSON>ript to find  PID data and kill the process

import psutil


class clsfetch_WindowsTask_Name_And_Kill:

    def __init__(self):

        # self.pattern = r"^\d+(,\d+)*$"
        self.Constant_service_name = 'UnitPortalBackend'

    # Compare the input string and validate isit integer or not and then
    # assign it to list variable as return value.
    def get_service_pid(self, service_name):
        try:
            for service in psutil.win_service_iter():
                if service.name() == service_name:
                    return service.pid()
            return None
        except Exception as e:
            print(f"Error: {e}")
            return None

    def kill_service_process(self, service_name):
        pid = self.get_service_pid(service_name)
        print(pid)
        if pid is not None:
            try:
                process = psutil.Process(pid)
                process.terminate()
                print(
                    f"Terminated the process (PID: {pid}) associated with the {service_name} service.")
                # self.kill_process_by_name(service_name)
            # except psutil.NoSuchProcess:
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                print(f"No such process found with PID {pid}.")
        else:
            print(
                f"The {service_name} service does not exist or is not running.")

    def kill_service_process_pid(self, pid):
        # pid = self.get_service_pid(service_name)
        # print(pid)
        if pid is not None:
            try:
                process = psutil.Process(pid)
                process.terminate()
                print(
                    f"Terminated the process (PID: {pid}) associated with the service.")
                # self.kill_process_by_name(service_name)
            # except psutil.NoSuchProcess:
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                print(f"No such process found with PID {pid}.")
        else:
            print(
                f"The {pid} service does not exist or is not running.")


# # Replace 'MyServiceName' with the name of the service you want to find the PID for.
# fun_kill_Service = fetch_WindowsTask_Name_And_Kill()
# # service_name = 'UnitPortalBackend'
# # fun_kill_Service.kill_service_process(fun_kill_Service.service_name)
# fun_kill_Service.kill_service_process('UnitPortalBackend')

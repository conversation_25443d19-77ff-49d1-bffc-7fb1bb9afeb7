<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import {GetDashboardInfo} from '@/api/nutanix'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    }
  },
  data() {
    return {
      chart: null,
      chart_data: [
              { value: 2500, name: 'Windows' },
              { value: 1200, name: 'Linux' },
              { value: 500, name: 'Network Appliance' },
              { value: 410, name :'others'}
        ]
    }
  },
  mounted() {
    this.get_dashboard_info()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    get_dashboard_info(){
      GetDashboardInfo(this.$store.getters.token).then(response => {
        if (response.status == 200){
          let _data = response.data.pie_os.ntx
          this.chart_data = Object.keys(_data).map((item)=>{return {'name':item,'value':_data[item]}})
          this.initChart()
        }
      })
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        // legend: {
        //   left: 'center',
        //   bottom: '10',
        //   data: ['Industries', 'Technology', 'Forex', 'Gold', 'Forecasts']
        // },
        series: [
          {
            name: 'VM type',
            type: 'pie',
            roseType: 'radius',
            radius: [15, 135],
            center: ['50%', '48%'],
            data: this.chart_data,
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      })
    }
  }
}
</script>

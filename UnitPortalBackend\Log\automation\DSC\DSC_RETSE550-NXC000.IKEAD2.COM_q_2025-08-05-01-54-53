2025-08-05 09:54:53,977 INFO Start to run the task.
2025-08-05 09:54:54,006 INFO ****************************************************************************************************
2025-08-05 09:54:54,006 INFO *                                                                                                  *
2025-08-05 09:54:54,006 INFO *                                  Configuring MaintenanceMode...                                  *
2025-08-05 09:54:54,007 INFO *                                                                                                  *
2025-08-05 09:54:54,016 INFO ****************************************************************************************************
2025-08-05 09:55:01,858 INFO Checking Maintenance Mode via v2.0 API (/hosts)
2025-08-05 09:55:02,275 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/hosts, method: GET, headers: None
2025-08-05 09:55:02,275 INFO params: None
2025-08-05 09:55:02,275 INFO User: 1-click-nutanix
2025-08-05 09:55:02,275 INFO payload: None
2025-08-05 09:55:02,275 INFO files: None
2025-08-05 09:55:02,275 INFO timeout: 30
2025-08-05 09:55:16,126 INFO API Check: All good, no hosts or CVMs are in maintenance mode.
2025-08-05 09:55:18,423 INFO Checking CVM status
2025-08-05 09:55:18,887 INFO Trying to SSH to the RETSE550-NXC000.IKEAD2.COM.
2025-08-05 09:55:18,887 INFO First try with username/password.
2025-08-05 09:55:18,887 INFO SSH connecting to RETSE550-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:55:21,424 INFO SSH connected to RETSE550-NXC000.IKEAD2.COM.
2025-08-05 09:55:27,534 INFO Sending 'cluster status |grep -v UP' to the server.
2025-08-05 09:55:43,537 INFO CVM IP:************* Status:Up
2025-08-05 09:55:43,537 INFO CVM IP:************* Status:Up
2025-08-05 09:55:43,538 INFO CVM IP:************* Status:Up
2025-08-05 09:55:43,538 INFO Great, all CVM status are Up
2025-08-05 09:55:47,667 INFO MaintenanceMode: Done
2025-08-05 09:55:47,677 INFO ****************************************************************************************************
2025-08-05 09:55:47,678 INFO *                                                                                                  *
2025-08-05 09:55:47,678 INFO *                                    Configuring IpamHealth...                                     *
2025-08-05 09:55:47,678 INFO *                                                                                                  *
2025-08-05 09:55:47,678 INFO ****************************************************************************************************
2025-08-05 09:55:49,530 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:55:49,530 INFO params: None
2025-08-05 09:55:49,530 INFO User: <EMAIL>
2025-08-05 09:55:49,530 INFO payload: None
2025-08-05 09:55:49,530 INFO files: None
2025-08-05 09:55:49,530 INFO timeout: 30
2025-08-05 09:55:50,958 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=parent_subnet_id='81005', method: GET, headers: None
2025-08-05 09:55:50,958 INFO params: None
2025-08-05 09:55:50,958 INFO User: <EMAIL>
2025-08-05 09:55:50,958 INFO payload: None
2025-08-05 09:55:50,958 INFO files: None
2025-08-05 09:55:50,958 INFO timeout: 30
2025-08-05 09:55:52,018 WARNING Can't find vlan_id '789' under subnet_id 81005, but we will do another try...
2025-08-05 09:55:52,018 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name LIKE '%RETSE550-%oob%', method: GET, headers: None
2025-08-05 09:55:52,018 INFO params: None
2025-08-05 09:55:52,018 INFO User: <EMAIL>
2025-08-05 09:55:52,018 INFO payload: None
2025-08-05 09:55:52,018 INFO files: None
2025-08-05 09:55:52,018 INFO timeout: 30
2025-08-05 09:55:53,298 INFO Succeeded to find the network by name pattern %RETSE550-%oob%
2025-08-05 09:55:53,298 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91752', method: GET, headers: None
2025-08-05 09:55:53,298 INFO params: None
2025-08-05 09:55:53,298 INFO User: <EMAIL>
2025-08-05 09:55:53,298 INFO payload: None
2025-08-05 09:55:53,298 INFO files: None
2025-08-05 09:55:53,298 INFO timeout: 30
2025-08-05 09:55:54,501 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:55:54,501 INFO params: None
2025-08-05 09:55:54,501 INFO User: <EMAIL>
2025-08-05 09:55:54,501 INFO payload: None
2025-08-05 09:55:54,501 INFO files: None
2025-08-05 09:55:54,501 INFO timeout: 30
2025-08-05 09:55:55,503 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91750', method: GET, headers: None
2025-08-05 09:55:55,503 INFO params: None
2025-08-05 09:55:55,503 INFO User: <EMAIL>
2025-08-05 09:55:55,503 INFO payload: None
2025-08-05 09:55:55,503 INFO files: None
2025-08-05 09:55:55,503 INFO timeout: 30
2025-08-05 09:55:56,824 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:55:56,824 INFO params: None
2025-08-05 09:55:56,824 INFO User: <EMAIL>
2025-08-05 09:55:56,824 INFO payload: None
2025-08-05 09:55:56,824 INFO files: None
2025-08-05 09:55:56,824 INFO timeout: 30
2025-08-05 09:55:57,854 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91750', method: GET, headers: None
2025-08-05 09:55:57,854 INFO params: None
2025-08-05 09:55:57,854 INFO User: <EMAIL>
2025-08-05 09:55:57,854 INFO payload: None
2025-08-05 09:55:57,854 INFO files: None
2025-08-05 09:55:57,854 INFO timeout: 30
2025-08-05 09:55:59,013 INFO Checking IPAM Health
2025-08-05 09:55:59,013 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:55:59,013 INFO params: None
2025-08-05 09:55:59,013 INFO User: 1-click-nutanix
2025-08-05 09:55:59,013 INFO payload: None
2025-08-05 09:55:59,013 INFO files: None
2025-08-05 09:55:59,013 INFO timeout: 30
2025-08-05 09:56:00,125 INFO Getting host list from RETSE550-NXC000.
2025-08-05 09:56:00,125 INFO Got the host list from RETSE550-NXC000.
2025-08-05 09:56:00,149 INFO Node records on IPAM and NTX match.
2025-08-05 09:56:00,159 INFO Checking IPAM integration...
2025-08-05 09:56:00,159 INFO Let's check '9' IP records, DNS update status
2025-08-05 09:56:00,159 INFO Pulling Ipam record with IP *************
2025-08-05 09:56:00,159 INFO This record is already enabled.
2025-08-05 09:56:00,159 INFO Pulling Ipam record with IP *************
2025-08-05 09:56:00,159 INFO This record is already enabled.
2025-08-05 09:56:00,159 INFO Pulling Ipam record with IP *************
2025-08-05 09:56:00,159 INFO This record is already enabled.
2025-08-05 09:56:00,159 INFO Pulling Ipam record with IP *************
2025-08-05 09:56:00,159 INFO This record is already enabled.
2025-08-05 09:56:00,159 INFO Pulling Ipam record with IP *************
2025-08-05 09:56:00,159 INFO This record is already enabled.
2025-08-05 09:56:00,159 INFO Pulling Ipam record with IP *************
2025-08-05 09:56:00,159 INFO This record is already enabled.
2025-08-05 09:56:00,159 INFO Pulling Ipam record with IP *************
2025-08-05 09:56:00,159 INFO This record is already enabled.
2025-08-05 09:56:00,159 INFO Pulling Ipam record with IP *************
2025-08-05 09:56:00,159 INFO This record is already enabled.
2025-08-05 09:56:00,159 INFO Pulling Ipam record with IP *************
2025-08-05 09:56:00,159 INFO This record is already enabled.
2025-08-05 09:56:00,180 INFO Records are all enabled.
2025-08-05 09:56:00,189 INFO IpamHealth: Done
2025-08-05 09:56:00,209 INFO ****************************************************************************************************
2025-08-05 09:56:00,209 INFO *                                                                                                  *
2025-08-05 09:56:00,209 INFO *                                      Configuring CvmRam...                                       *
2025-08-05 09:56:00,209 INFO *                                                                                                  *
2025-08-05 09:56:00,209 INFO ****************************************************************************************************
2025-08-05 09:56:01,907 INFO Desired State 'cvm_ram' has been disabled inside this site profile.
2025-08-05 09:56:01,908 INFO Checking CVM RAM Desired state...
2025-08-05 09:56:01,908 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/genesis, method: POST, headers: None
2025-08-05 09:56:01,908 INFO params: None
2025-08-05 09:56:01,908 INFO User: 1-click-nutanix
2025-08-05 09:56:01,908 INFO payload: {'value': '{".oid": "ClusterManager", ".method": "get_cluster_cvm_params_map", ".kwargs": {}}'}
2025-08-05 09:56:01,908 INFO files: None
2025-08-05 09:56:01,908 INFO timeout: 30
2025-08-05 09:56:03,120 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:56:03,120 INFO params: None
2025-08-05 09:56:03,120 INFO User: <EMAIL>
2025-08-05 09:56:03,120 INFO payload: None
2025-08-05 09:56:03,120 INFO files: None
2025-08-05 09:56:03,120 INFO timeout: 30
2025-08-05 09:56:04,122 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91750', method: GET, headers: None
2025-08-05 09:56:04,122 INFO params: None
2025-08-05 09:56:04,122 INFO User: <EMAIL>
2025-08-05 09:56:04,122 INFO payload: None
2025-08-05 09:56:04,122 INFO files: None
2025-08-05 09:56:04,122 INFO timeout: 30
2025-08-05 09:56:05,332 INFO CVM RAM Desired state already met.
2025-08-05 09:56:05,414 INFO CvmRam: Done
2025-08-05 09:56:05,441 INFO ****************************************************************************************************
2025-08-05 09:56:05,441 INFO *                                                                                                  *
2025-08-05 09:56:05,441 INFO *                                      Configuring VmRbac...                                       *
2025-08-05 09:56:05,441 INFO *                                                                                                  *
2025-08-05 09:56:05,441 INFO ****************************************************************************************************
2025-08-05 09:56:07,295 INFO This is not a Central Site, This step is desired to only run on central sites, skip...
2025-08-05 09:56:07,308 INFO VmRbac: Done
2025-08-05 09:56:07,319 INFO ****************************************************************************************************
2025-08-05 09:56:07,319 INFO *                                                                                                  *
2025-08-05 09:56:07,319 INFO *                                   Configuring StorageConfig...                                   *
2025-08-05 09:56:07,319 INFO *                                                                                                  *
2025-08-05 09:56:07,319 INFO ****************************************************************************************************
2025-08-05 09:56:09,235 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/storage_containers, method: GET, headers: None
2025-08-05 09:56:09,235 INFO params: None
2025-08-05 09:56:09,235 INFO User: 1-click-nutanix
2025-08-05 09:56:09,235 INFO payload: None
2025-08-05 09:56:09,235 INFO files: None
2025-08-05 09:56:09,235 INFO timeout: 30
2025-08-05 09:56:10,381 INFO Checking vdisks...
2025-08-05 09:56:10,394 INFO State of the default container is already as desired.
2025-08-05 09:56:10,394 INFO Configuring storage containers...
2025-08-05 09:56:10,421 INFO StorageConfig: Done
2025-08-05 09:56:10,427 INFO ****************************************************************************************************
2025-08-05 09:56:10,427 INFO *                                                                                                  *
2025-08-05 09:56:10,427 INFO *                                       Configuring Smtp...                                        *
2025-08-05 09:56:10,427 INFO *                                                                                                  *
2025-08-05 09:56:10,427 INFO ****************************************************************************************************
2025-08-05 09:56:12,259 INFO Desired State 'smtp_config' has been disabled inside this site profile.
2025-08-05 09:56:12,260 INFO Configuring SMTP Settings
2025-08-05 09:56:12,260 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/smtp, method: PUT, headers: None
2025-08-05 09:56:12,260 INFO params: None
2025-08-05 09:56:12,260 INFO User: 1-click-nutanix
2025-08-05 09:56:12,260 INFO payload: {'address': 'smtp-gw.ikea.com', 'port': 25, 'username': None, 'password': None, 'secureMode': 'NONE', 'fromEmailAddress': '<EMAIL>', 'emailStatus': {'status': 'UNKNOWN', 'message': None}}
2025-08-05 09:56:12,260 INFO files: None
2025-08-05 09:56:12,260 INFO timeout: 30
2025-08-05 09:56:13,842 INFO Configuring SMTP Alert Settings
2025-08-05 09:56:13,843 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/alerts/configuration, method: PUT, headers: None
2025-08-05 09:56:13,843 INFO params: None
2025-08-05 09:56:13,843 INFO User: 1-click-nutanix
2025-08-05 09:56:13,843 INFO payload: {'emailContactList': ['<EMAIL>'], 'enable': False, 'enableDefaultNutanixEmail': True, 'skipEmptyAlertEmailDigest': True, 'defaultNutanixEmail': '<EMAIL>', 'smtpserver': {'address': 'smtp-gw.ikea.com', 'port': 25, 'username': None, 'password': None, 'secureMode': 'NONE', 'fromEmailAddress': '<EMAIL>', 'emailStatus': {'status': 'UNKNOWN', 'message': None}}, 'tunnelDetails': {'httpProxy': None, 'serviceCenter': None, 'connectionStatus': {'lastCheckedTimeStampUsecs': 0, 'status': 'UNKNOWN', 'message': None}, 'transportStatus': {'status': 'UNKNOWN', 'message': None}}, 'emailConfigRules': None, 'emailTemplate': {'subjectPrefix': None, 'bodySuffix': None}}
2025-08-05 09:56:13,843 INFO files: None
2025-08-05 09:56:13,843 INFO timeout: 30
2025-08-05 09:56:15,392 INFO Smtp: Done
2025-08-05 09:56:15,405 INFO ****************************************************************************************************
2025-08-05 09:56:15,405 INFO *                                                                                                  *
2025-08-05 09:56:15,405 INFO *                                      Configuring DnsNtp...                                       *
2025-08-05 09:56:15,405 INFO *                                                                                                  *
2025-08-05 09:56:15,406 INFO ****************************************************************************************************
2025-08-05 09:56:17,242 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:56:17,242 INFO params: None
2025-08-05 09:56:17,242 INFO User: <EMAIL>
2025-08-05 09:56:17,242 INFO payload: None
2025-08-05 09:56:17,242 INFO files: None
2025-08-05 09:56:17,242 INFO timeout: 30
2025-08-05 09:56:18,239 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=subnet_id%3D%2791750%27, method: GET, headers: None
2025-08-05 09:56:18,241 INFO params: None
2025-08-05 09:56:18,241 INFO User: <EMAIL>
2025-08-05 09:56:18,242 INFO payload: None
2025-08-05 09:56:18,242 INFO files: None
2025-08-05 09:56:18,242 INFO timeout: 30
2025-08-05 09:56:19,124 INFO AHV / CVM Subnet: '91750' finding the rest.
2025-08-05 09:56:19,124 INFO Desired State 'dns_ntp_config' has been disabled inside this site profile.
2025-08-05 09:56:19,144 INFO Configuring NTP...
2025-08-05 09:56:19,144 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/cluster/ntp_servers, method: GET, headers: None
2025-08-05 09:56:19,144 INFO params: None
2025-08-05 09:56:19,144 INFO User: 1-click-nutanix
2025-08-05 09:56:19,144 INFO payload: None
2025-08-05 09:56:19,144 INFO files: None
2025-08-05 09:56:19,144 INFO timeout: 30
2025-08-05 09:56:20,460 INFO Target NTP servers: ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com', 'ntp1-na.ikea.com', 'ntp1-ap.ikea.com', 'ntp1-cn.ikea.com']
2025-08-05 09:56:20,460 INFO Current NTP servers: ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com', 'ntp1-na.ikea.com', 'ntp1-ap.ikea.com', 'ntp1-cn.ikea.com']
2025-08-05 09:56:20,478 INFO NTP is already as desired.
2025-08-05 09:56:20,493 INFO Configuring DNS...
2025-08-05 09:56:20,494 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/cluster/name_servers, method: GET, headers: None
2025-08-05 09:56:20,494 INFO params: None
2025-08-05 09:56:20,494 INFO User: 1-click-nutanix
2025-08-05 09:56:20,494 INFO payload: None
2025-08-05 09:56:20,494 INFO files: None
2025-08-05 09:56:20,494 INFO timeout: 30
2025-08-05 09:56:21,779 INFO Target DNS servers: ['10.59.253.2', '10.59.67.9']
2025-08-05 09:56:21,779 INFO Current DNS servers: ['10.59.253.2', '10.59.67.9']
2025-08-05 09:56:21,800 INFO DNS is already as desired.
2025-08-05 09:56:21,817 INFO DnsNtp: Done
2025-08-05 09:56:21,841 INFO ****************************************************************************************************
2025-08-05 09:56:21,841 INFO *                                                                                                  *
2025-08-05 09:56:21,841 INFO *                                        Configuring Ha...                                         *
2025-08-05 09:56:21,841 INFO *                                                                                                  *
2025-08-05 09:56:21,841 INFO ****************************************************************************************************
2025-08-05 09:56:23,740 INFO Desired State 'ha_reservation' has been disabled inside this site profile.
2025-08-05 09:56:23,740 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:56:23,740 INFO params: None
2025-08-05 09:56:23,740 INFO User: 1-click-nutanix
2025-08-05 09:56:23,740 INFO payload: None
2025-08-05 09:56:23,741 INFO files: None
2025-08-05 09:56:23,741 INFO timeout: 30
2025-08-05 09:56:24,862 INFO Getting host list from RETSE550-NXC000.
2025-08-05 09:56:24,862 INFO Got the host list from RETSE550-NXC000.
2025-08-05 09:56:24,862 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/ha, method: GET, headers: None
2025-08-05 09:56:24,862 INFO params: None
2025-08-05 09:56:24,862 INFO User: 1-click-nutanix
2025-08-05 09:56:24,862 INFO payload: None
2025-08-05 09:56:24,862 INFO files: None
2025-08-05 09:56:24,862 INFO timeout: 30
2025-08-05 09:56:25,972 INFO HA Reservation is already in desired state.
2025-08-05 09:56:25,992 INFO Ha: Done
2025-08-05 09:56:26,005 INFO ****************************************************************************************************
2025-08-05 09:56:26,005 INFO *                                                                                                  *
2025-08-05 09:56:26,005 INFO *                                    Configuring AuthConfig...                                     *
2025-08-05 09:56:26,007 INFO *                                                                                                  *
2025-08-05 09:56:26,007 INFO ****************************************************************************************************
2025-08-05 09:56:28,473 INFO Configuring LDAP...
2025-08-05 09:56:28,473 INFO Desired auth config name: ikead2
2025-08-05 09:56:28,473 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 09:56:28,473 INFO params: None
2025-08-05 09:56:28,474 INFO User: 1-click-nutanix
2025-08-05 09:56:28,474 INFO payload: None
2025-08-05 09:56:28,474 INFO files: None
2025-08-05 09:56:28,474 INFO timeout: 30
2025-08-05 09:56:29,596 INFO PE is already Joined towards 'ikead2.com'.
2025-08-05 09:56:29,608 INFO Username is already set towards '<EMAIL>'
2025-08-05 09:56:29,623 INFO Configuring the Role Mappings for 'ikead2'
2025-08-05 09:56:29,623 INFO Getting Groups that need to be bound.
2025-08-05 09:56:29,623 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 09:56:29,623 INFO params: None
2025-08-05 09:56:29,623 INFO User: 1-click-nutanix
2025-08-05 09:56:29,623 INFO payload: None
2025-08-05 09:56:29,623 INFO files: None
2025-08-05 09:56:29,623 INFO timeout: 30
2025-08-05 09:56:30,720 INFO We are setting up RoleMappings using group: '['NXAdmin']'
2025-08-05 09:56:30,720 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/authconfig/directories/ikead2/role_mappings, method: GET, headers: None
2025-08-05 09:56:30,720 INFO params: None
2025-08-05 09:56:30,720 INFO User: 1-click-nutanix
2025-08-05 09:56:30,720 INFO payload: None
2025-08-05 09:56:30,720 INFO files: None
2025-08-05 09:56:30,720 INFO timeout: 30
2025-08-05 09:56:31,892 INFO Role ROLE_USER_ADMIN with entity type GROUP is already in desired state.
2025-08-05 09:56:31,892 INFO Role ROLE_CLUSTER_ADMIN with entity type GROUP is already in desired state.
2025-08-05 09:56:31,892 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 09:56:31,892 INFO params: None
2025-08-05 09:56:31,892 INFO User: 1-click-nutanix
2025-08-05 09:56:31,892 INFO payload: None
2025-08-05 09:56:31,892 INFO files: None
2025-08-05 09:56:31,892 INFO timeout: 30
2025-08-05 09:56:33,148 INFO AuthConfig: Done
2025-08-05 09:56:33,174 INFO ****************************************************************************************************
2025-08-05 09:56:33,174 INFO *                                                                                                  *
2025-08-05 09:56:33,174 INFO *                                    Configuring AhvHostname...                                    *
2025-08-05 09:56:33,174 INFO *                                                                                                  *
2025-08-05 09:56:33,174 INFO ****************************************************************************************************
2025-08-05 09:56:35,441 INFO Configure AHV Hostnames
2025-08-05 09:56:35,441 INFO Desired State 'ahv_host_names' has been disabled inside this site profile.
2025-08-05 09:56:35,441 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:56:35,441 INFO params: None
2025-08-05 09:56:35,441 INFO User: 1-click-nutanix
2025-08-05 09:56:35,441 INFO payload: None
2025-08-05 09:56:35,441 INFO files: None
2025-08-05 09:56:35,441 INFO timeout: 30
2025-08-05 09:56:36,580 INFO Getting host list from RETSE550-NXC000.
2025-08-05 09:56:36,580 INFO Got the host list from RETSE550-NXC000.
2025-08-05 09:56:36,580 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:56:36,580 INFO params: None
2025-08-05 09:56:36,580 INFO User: <EMAIL>
2025-08-05 09:56:36,580 INFO payload: None
2025-08-05 09:56:36,580 INFO files: None
2025-08-05 09:56:36,580 INFO timeout: 30
2025-08-05 09:56:37,583 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91750', method: GET, headers: None
2025-08-05 09:56:37,583 INFO params: None
2025-08-05 09:56:37,583 INFO User: <EMAIL>
2025-08-05 09:56:37,583 INFO payload: None
2025-08-05 09:56:37,583 INFO files: None
2025-08-05 09:56:37,583 INFO timeout: 30
2025-08-05 09:56:38,657 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:56:38,657 INFO params: None
2025-08-05 09:56:38,657 INFO User: <EMAIL>
2025-08-05 09:56:38,657 INFO payload: None
2025-08-05 09:56:38,657 INFO files: None
2025-08-05 09:56:38,658 INFO timeout: 30
2025-08-05 09:56:39,644 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91750', method: GET, headers: None
2025-08-05 09:56:39,644 INFO params: None
2025-08-05 09:56:39,644 INFO User: <EMAIL>
2025-08-05 09:56:39,644 INFO payload: None
2025-08-05 09:56:39,644 INFO files: None
2025-08-05 09:56:39,644 INFO timeout: 30
2025-08-05 09:56:40,729 INFO Current node is good, skipping...
2025-08-05 09:56:40,729 INFO Current node is good, skipping...
2025-08-05 09:56:40,729 INFO Current node is good, skipping...
2025-08-05 09:56:40,744 INFO AhvHostname: Done
2025-08-05 09:56:40,758 INFO ****************************************************************************************************
2025-08-05 09:56:40,758 INFO *                                                                                                  *
2025-08-05 09:56:40,758 INFO *                                    Configuring CvmHostname...                                    *
2025-08-05 09:56:40,758 INFO *                                                                                                  *
2025-08-05 09:56:40,758 INFO ****************************************************************************************************
2025-08-05 09:56:43,117 INFO Configure CVM Hostnames
2025-08-05 09:56:43,117 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:56:43,117 INFO params: None
2025-08-05 09:56:43,117 INFO User: 1-click-nutanix
2025-08-05 09:56:43,117 INFO payload: None
2025-08-05 09:56:43,117 INFO files: None
2025-08-05 09:56:43,117 INFO timeout: 30
2025-08-05 09:56:44,224 INFO Getting host list from RETSE550-NXC000.
2025-08-05 09:56:44,224 INFO Got the host list from RETSE550-NXC000.
2025-08-05 09:56:44,224 INFO Desired State 'cvm_host_names' has been disabled inside this site profile.
2025-08-05 09:56:44,224 INFO Getting VM list from RETSE550-NXC000.
2025-08-05 09:56:44,224 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-05 09:56:44,224 INFO params: None
2025-08-05 09:56:44,224 INFO User: 1-click-nutanix
2025-08-05 09:56:44,224 INFO payload: None
2025-08-05 09:56:44,224 INFO files: None
2025-08-05 09:56:44,224 INFO timeout: 30
2025-08-05 09:56:45,442 INFO Got the VM list from RETSE550-NXC000.
2025-08-05 09:56:45,442 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:56:45,442 INFO params: None
2025-08-05 09:56:45,442 INFO User: <EMAIL>
2025-08-05 09:56:45,442 INFO payload: None
2025-08-05 09:56:45,442 INFO files: None
2025-08-05 09:56:45,442 INFO timeout: 30
2025-08-05 09:56:46,406 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91750', method: GET, headers: None
2025-08-05 09:56:46,406 INFO params: None
2025-08-05 09:56:46,406 INFO User: <EMAIL>
2025-08-05 09:56:46,406 INFO payload: None
2025-08-05 09:56:46,406 INFO files: None
2025-08-05 09:56:46,406 INFO timeout: 30
2025-08-05 09:56:47,439 INFO Working with 'RETSE550-NX7001CVM'
2025-08-05 09:56:47,439 INFO The name to set to NTX cvm: 'NTNX-RETSE550-NX7001-CVM'
2025-08-05 09:56:47,439 INFO Current node name on NTX is equal to IPAM, skipping...
2025-08-05 09:56:47,439 INFO Working with 'RETSE550-NX7002CVM'
2025-08-05 09:56:47,439 INFO The name to set to NTX cvm: 'NTNX-RETSE550-NX7002-CVM'
2025-08-05 09:56:47,439 INFO Current node name on NTX is equal to IPAM, skipping...
2025-08-05 09:56:47,439 INFO Working with 'RETSE550-NX7003CVM'
2025-08-05 09:56:47,439 INFO The name to set to NTX cvm: 'NTNX-RETSE550-NX7003-CVM'
2025-08-05 09:56:47,439 INFO Current node name on NTX is equal to IPAM, skipping...
2025-08-05 09:56:47,456 INFO CvmHostname: Done
2025-08-05 09:56:47,459 INFO ****************************************************************************************************
2025-08-05 09:56:47,459 INFO *                                                                                                  *
2025-08-05 09:56:47,459 INFO *                                        Configuring Oob...                                        *
2025-08-05 09:56:47,459 INFO *                                                                                                  *
2025-08-05 09:56:47,459 INFO ****************************************************************************************************
2025-08-05 09:56:52,985 INFO oneview_scope is DPC_EU_D2, myscope is /rest/scopes/dc4d1e10-ba24-44e9-97cc-3f45c31f7ff1
2025-08-05 09:57:03,412 INFO Desired State 'oob_health' has been disabled inside this site profile.
2025-08-05 09:57:03,412 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:57:03,412 INFO params: None
2025-08-05 09:57:03,412 INFO User: 1-click-nutanix
2025-08-05 09:57:03,412 INFO payload: None
2025-08-05 09:57:03,412 INFO files: None
2025-08-05 09:57:03,412 INFO timeout: 30
2025-08-05 09:57:04,796 INFO Getting host list from RETSE550-NXC000.
2025-08-05 09:57:04,796 INFO Got the host list from RETSE550-NXC000.
2025-08-05 09:57:04,798 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NX7001oob.ikead2.com', method: GET, headers: None
2025-08-05 09:57:04,798 INFO params: None
2025-08-05 09:57:04,798 INFO User: <EMAIL>
2025-08-05 09:57:04,798 INFO payload: None
2025-08-05 09:57:04,798 INFO files: None
2025-08-05 09:57:04,798 INFO timeout: 30
2025-08-05 09:57:05,997 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=subnet_id%3D%2791752%27, method: GET, headers: None
2025-08-05 09:57:05,997 INFO params: None
2025-08-05 09:57:05,997 INFO User: <EMAIL>
2025-08-05 09:57:05,997 INFO payload: None
2025-08-05 09:57:05,997 INFO files: None
2025-08-05 09:57:05,997 INFO timeout: 30
2025-08-05 09:57:06,984 INFO ****************************************************************************************************
2025-08-05 09:57:06,984 INFO *                                                                                                  *
2025-08-05 09:57:06,984 INFO *                                 Checking OOB for RETSE550-NX7001                                 *
2025-08-05 09:57:06,984 INFO *                                                                                                  *
2025-08-05 09:57:06,984 INFO ****************************************************************************************************
2025-08-05 09:57:06,984 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NX7001oob.ikead2.com', method: GET, headers: None
2025-08-05 09:57:06,984 INFO params: None
2025-08-05 09:57:06,984 INFO User: <EMAIL>
2025-08-05 09:57:06,984 INFO payload: None
2025-08-05 09:57:06,985 INFO files: None
2025-08-05 09:57:06,985 INFO timeout: 30
2025-08-05 09:57:08,007 INFO SSH connecting to *************, this is the '1' try.
2025-08-05 09:57:12,422 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:57:12,422 INFO SSH connecting to *************, this is the '2' try.
2025-08-05 09:57:18,670 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:57:18,670 INFO SSH connecting to *************, this is the '3' try.
2025-08-05 09:57:23,091 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:57:23,091 INFO SSH connecting to *************, this is the '4' try.
2025-08-05 09:57:29,394 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:57:29,394 INFO SSH connecting to *************, this is the '5' try.
2025-08-05 09:57:35,673 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:57:35,673 ERROR We've retried for 5 times, still not connected, aborting.
2025-08-05 09:57:35,673 ERROR Failed to connect to *************
2025-08-05 09:57:35,673 INFO OOB for RETSE550-NX7001 is reachable.
2025-08-05 09:57:35,673 INFO Checking iLO status...
2025-08-05 09:57:36,980 INFO ILO status is good.
2025-08-05 09:57:36,981 INFO Checking ILO name and DNS name for RETSE550-NX7001
2025-08-05 09:57:36,981 INFO Setting server name and DNS name for RETSE550-NX7001OOB
2025-08-05 09:57:36,981 INFO Retrieving RedFish Network Stack '*************'
2025-08-05 09:57:36,981 INFO Calling restapi, URL: https://*************/redfish/v1/SessionService/Sessions, method: POST, headers: {'Content-Type': 'application/json;charset=UTF-8'}
2025-08-05 09:57:36,981 INFO params: None
2025-08-05 09:57:36,981 INFO User: administrator
2025-08-05 09:57:36,981 INFO payload: {'UserName': 'administrator', 'Password': '*****'}
2025-08-05 09:57:36,982 INFO files: None
2025-08-05 09:57:36,982 INFO timeout: 5
2025-08-05 09:57:39,325 INFO Calling restapi, URL: https://*************/redfish/v1/Systems/1/, method: GET, headers: {'X-Auth-Token': 'c69fc9f4ea5f072c3f2e57f7a8042961'}
2025-08-05 09:57:39,339 INFO params: None
2025-08-05 09:57:39,339 INFO User: administrator
2025-08-05 09:57:39,339 INFO payload: None
2025-08-05 09:57:39,339 INFO files: None
2025-08-05 09:57:39,339 INFO timeout: None
2025-08-05 09:57:40,813 INFO Get ILO HostName successfull.
2025-08-05 09:57:40,815 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/EthernetInterfaces/1/, method: GET, headers: None
2025-08-05 09:57:40,815 INFO params: None
2025-08-05 09:57:40,815 INFO User: administrator
2025-08-05 09:57:40,815 INFO payload: None
2025-08-05 09:57:40,815 INFO files: None
2025-08-05 09:57:40,815 INFO timeout: None
2025-08-05 09:57:42,162 INFO Got the response with OK
2025-08-05 09:57:42,163 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:57:42,163 INFO params: None
2025-08-05 09:57:42,163 INFO User: administrator
2025-08-05 09:57:42,164 INFO payload: None
2025-08-05 09:57:42,164 INFO files: None
2025-08-05 09:57:42,164 INFO timeout: None
2025-08-05 09:57:43,375 INFO Got the response with OK
2025-08-05 09:57:43,375 INFO Group Already exists.
2025-08-05 09:57:43,884 INFO LDAP Binding already in desired state.
2025-08-05 09:57:43,884 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:57:43,884 INFO params: None
2025-08-05 09:57:43,884 INFO User: administrator
2025-08-05 09:57:43,884 INFO payload: None
2025-08-05 09:57:43,884 INFO files: None
2025-08-05 09:57:43,884 INFO timeout: None
2025-08-05 09:57:45,123 INFO Got the response with OK
2025-08-05 09:57:45,124 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Roles/dirgroup3a7c6167d73b780ee5fe83de, method: GET, headers: None
2025-08-05 09:57:45,124 INFO params: None
2025-08-05 09:57:45,124 INFO User: administrator
2025-08-05 09:57:45,124 INFO payload: None
2025-08-05 09:57:45,125 INFO files: None
2025-08-05 09:57:45,125 INFO timeout: None
2025-08-05 09:57:46,324 INFO Got the response with OK
2025-08-05 09:57:46,325 INFO Privileges are already correct.
2025-08-05 09:57:46,326 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/EthernetInterfaces/1, method: GET, headers: None
2025-08-05 09:57:46,326 INFO params: None
2025-08-05 09:57:46,326 INFO User: administrator
2025-08-05 09:57:46,326 INFO payload: None
2025-08-05 09:57:46,326 INFO files: None
2025-08-05 09:57:46,326 INFO timeout: None
2025-08-05 09:57:47,561 INFO Got the response with OK
2025-08-05 09:57:47,561 INFO DNS is already correct.
2025-08-05 09:57:47,561 INFO Checking NTP configuration...
2025-08-05 09:57:47,561 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/DateTime, method: GET, headers: None
2025-08-05 09:57:47,561 INFO params: None
2025-08-05 09:57:47,561 INFO User: administrator
2025-08-05 09:57:47,561 INFO payload: None
2025-08-05 09:57:47,561 INFO files: None
2025-08-05 09:57:47,561 INFO timeout: None
2025-08-05 09:57:48,727 INFO Got the response with OK
2025-08-05 09:57:48,728 INFO Current DateTime configuration: {'@odata.context': '/redfish/v1/$metadata#HpeiLODateTime.HpeiLODateTime', '@odata.etag': 'W/"4E5FADBF"', '@odata.id': '/redfish/v1/Managers/1/DateTime', '@odata.type': '#HpeiLODateTime.v2_0_0.HpeiLODateTime', 'Id': 'DateTime', 'ConfigurationSettings': 'Current', 'DateTime': '2025-08-05T01:57:46Z', 'Links': {'EthernetNICs': {'@odata.id': '/redfish/v1/Managers/1/EthernetInterfaces'}}, 'NTPServers': ['*************', ''], 'Name': 'iLO Date and Time Settings', 'PropagateTimeToHost': False, 'StaticNTPServers': ['*************', ''], 'TimeZone': {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, 'TimeZoneList': [{'Index': 0, 'Name': 'International Date Line West', 'UtcOffset': '-12:00', 'Value': 'GMT+12:00'}, {'Index': 1, 'Name': 'Midway Island, Samoa', 'UtcOffset': '-11:00', 'Value': 'SST+11:00'}, {'Index': 2, 'Name': 'Hawaii', 'UtcOffset': '-10:00', 'Value': 'HST+10:00'}, {'Index': 3, 'Name': 'Marquesas', 'UtcOffset': '-09:30', 'Value': 'MART+9:30'}, {'Index': 4, 'Name': 'Alaska', 'UtcOffset': '-09:00', 'Value': 'AKST+9:00AKDT+08:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 5, 'Name': 'Pacific Time(US & Canada), Tijuana, Portland', 'UtcOffset': '-08:00', 'Value': 'PST+8:00PDT+07:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 6, 'Name': 'Arizona, Chihuahua, La Paz, Mazatlan, Mountain Time (US & Canad', 'UtcOffset': '-07:00', 'Value': 'MST+7:00MDT+06:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 7, 'Name': 'Central America, Central Time(US & Canada)', 'UtcOffset': '-06:00', 'Value': 'CST+6:00CDT+05:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 8, 'Name': 'Bogota, Lima, Quito, Eastern Time(US & Canada)', 'UtcOffset': '-05:00', 'Value': 'EST+5:00EDT+04:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 9, 'Name': 'Caracas, Georgetown', 'UtcOffset': '-04:00', 'Value': 'VET+4:00'}, {'Index': 10, 'Name': 'Atlantic Time(Canada), Santiago', 'UtcOffset': '-04:00', 'Value': 'AST+4:00ADT+03:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 11, 'Name': 'Newfoundland', 'UtcOffset': '-03:30', 'Value': 'NST+3:30NDT+02:30:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 12, 'Name': 'Brasilia, Buenos Aires, Greenland', 'UtcOffset': '-03:00', 'Value': 'ART+3:00'}, {'Index': 13, 'Name': 'Mid-Atlantic', 'UtcOffset': '-02:00', 'Value': 'GST+2:00'}, {'Index': 14, 'Name': 'Azores, Cape Verde Is.', 'UtcOffset': '-01:00', 'Value': 'CVT+1:00'}, {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, {'Index': 16, 'Name': 'Dublin, London', 'UtcOffset': '+00:00', 'Value': 'WET-0WEST-1,M3.5.0/01:00:00,M10.5.0/02:00:00'}, {'Index': 17, 'Name': 'Amsterdam, Berlin, Bern, Rome, Paris, West Central Africa', 'UtcOffset': '+01:00', 'Value': 'CET-1:00CEST-02:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 18, 'Name': 'Athens, Bucharest, Cairo, Jerusalem', 'UtcOffset': '+02:00', 'Value': 'EET-2:00EEST-03:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 19, 'Name': 'Baghdad, Kuwait, Riyadh, Moscow, Istanbul, Nairobi', 'UtcOffset': '+03:00', 'Value': 'AST-3:00'}, {'Index': 20, 'Name': 'Tehran', 'UtcOffset': '+03:30', 'Value': 'IRST-3:30IRDT-04:30:00,80/00:00:00,264/00:00:00'}, {'Index': 21, 'Name': 'Abu Dhabi, Muscat, Baku, Tbilisi, Yerevan', 'UtcOffset': '+04:00', 'Value': 'GST-4:00'}, {'Index': 22, 'Name': 'Kabul', 'UtcOffset': '+04:30', 'Value': 'AFT-4:30'}, {'Index': 23, 'Name': 'Ekaterinburg, Islamabad, Karachi, Tashkent', 'UtcOffset': '+05:00', 'Value': 'YEKT-5:00'}, {'Index': 24, 'Name': 'Chennai, Kolkata, Mumbai, New Delhi', 'UtcOffset': '+05:30', 'Value': 'IST-5:30'}, {'Index': 25, 'Name': 'Kathmandu', 'UtcOffset': '+05:45', 'Value': 'NPT-5:45'}, {'Index': 26, 'Name': 'Almaty, Dhaka, Sri Jayawardenepura', 'UtcOffset': '+06:00', 'Value': 'ALMT-6:00'}, {'Index': 27, 'Name': 'Rangoon', 'UtcOffset': '+06:30', 'Value': 'MMT-6:30'}, {'Index': 28, 'Name': 'Bangkok, Hanio, Jakarta, Novosibirsk, Astana, Krasnoyarsk', 'UtcOffset': '+07:00', 'Value': 'ICT-7:00'}, {'Index': 29, 'Name': 'Beijing, Chongqing, Hong Kong, Urumqi, Taipei, Perth', 'UtcOffset': '+08:00', 'Value': 'CST-8:00'}, {'Index': 30, 'Name': 'Eucla', 'UtcOffset': '+08:45', 'Value': 'ACWST-08:45'}, {'Index': 31, 'Name': 'Osaka, Sapporo, Tokyo, Seoul, Yakutsk', 'UtcOffset': '+09:00', 'Value': 'JST-9:00'}, {'Index': 32, 'Name': 'Adelaide, Darwin', 'UtcOffset': '+09:30', 'Value': 'ACST-9:30ACDT-10:30:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 33, 'Name': 'Canberra, Melbourne, Sydney, Guam, Hobart, Vladivostok', 'UtcOffset': '+10:00', 'Value': 'AEST-10:00AEDT-11:00:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 34, 'Name': 'Lord Howe', 'UtcOffset': '+10:30', 'Value': 'LHST-10:30LHDT11:00'}, {'Index': 35, 'Name': 'Chatham', 'UtcOffset': '+10:45', 'Value': 'CHAST-10:45CHADT-11:45'}, {'Index': 36, 'Name': 'Magadan, Solomon Is., New Caledonia', 'UtcOffset': '+11:00', 'Value': 'MAGT-11:00'}, {'Index': 37, 'Name': 'Auckland, Wellington, Fiji, Kamchatka, Marshall Is.', 'UtcOffset': '+12:00', 'Value': 'NZST-12:00NZDT-13:00:00,M9.5.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 38, 'Name': "Nuku'alofa", 'UtcOffset': '+13:00', 'Value': 'TKT-13:00'}, {'Index': 39, 'Name': 'Line Islands', 'UtcOffset': '+14:00', 'Value': 'LINT-14:00'}, {'Index': 40, 'Name': 'Unspecified Time Zone', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}]}
2025-08-05 09:57:48,728 INFO NTP servers do not match desired configuration...
2025-08-05 09:57:48,728 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/DateTime, method: PATCH, headers: None
2025-08-05 09:57:48,728 INFO params: None
2025-08-05 09:57:48,728 INFO User: administrator
2025-08-05 09:57:48,729 INFO payload: {'StaticNTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com']}
2025-08-05 09:57:48,729 INFO files: None
2025-08-05 09:57:48,729 INFO timeout: None
2025-08-05 09:57:49,971 INFO ILO object updated successfully
2025-08-05 09:57:49,971 INFO Successfully updated NTP configuration
2025-08-05 09:57:49,971 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/Actions/Manager.Reset, method: POST, headers: {'Content-Type': 'application/json;charset=UTF-8'}
2025-08-05 09:57:49,971 INFO params: None
2025-08-05 09:57:49,971 INFO User: administrator
2025-08-05 09:57:49,971 INFO payload: {'ResetType': 'GracefulRestart'}
2025-08-05 09:57:49,971 INFO files: None
2025-08-05 09:57:49,971 INFO timeout: None
2025-08-05 09:57:51,130 INFO iLO reset successful
2025-08-05 09:57:51,131 INFO Start ILO reset, Waiting for OOB to come back up.
2025-08-05 09:58:51,133 INFO Oneview part
2025-08-05 09:58:51,133 INFO Check if the OOB is in oneview
2025-08-05 09:58:52,189 INFO OOB is in oneview and state is normal
2025-08-05 09:58:52,212 INFO OOB configuration is done for RETSE550-NX7001.
2025-08-05 09:58:52,226 INFO ****************************************************************************************************
2025-08-05 09:58:52,226 INFO *                                                                                                  *
2025-08-05 09:58:52,226 INFO *                                 Checking OOB for RETSE550-NX7002                                 *
2025-08-05 09:58:52,226 INFO *                                                                                                  *
2025-08-05 09:58:52,226 INFO ****************************************************************************************************
2025-08-05 09:58:52,226 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NX7002oob.ikead2.com', method: GET, headers: None
2025-08-05 09:58:52,226 INFO params: None
2025-08-05 09:58:52,226 INFO User: <EMAIL>
2025-08-05 09:58:52,226 INFO payload: None
2025-08-05 09:58:52,226 INFO files: None
2025-08-05 09:58:52,226 INFO timeout: 30
2025-08-05 09:58:53,213 INFO SSH connecting to *************, this is the '1' try.
2025-08-05 09:58:59,676 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:58:59,676 INFO SSH connecting to *************, this is the '2' try.
2025-08-05 09:59:04,208 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:59:04,208 INFO SSH connecting to *************, this is the '3' try.
2025-08-05 09:59:10,687 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:59:10,687 INFO SSH connecting to *************, this is the '4' try.
2025-08-05 09:59:15,172 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:59:15,173 INFO SSH connecting to *************, this is the '5' try.
2025-08-05 09:59:19,676 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:59:19,676 ERROR We've retried for 5 times, still not connected, aborting.
2025-08-05 09:59:19,676 ERROR Failed to connect to *************
2025-08-05 09:59:19,676 INFO OOB for RETSE550-NX7002 is reachable.
2025-08-05 09:59:19,676 INFO Checking iLO status...
2025-08-05 09:59:21,136 INFO ILO status is good.
2025-08-05 09:59:21,136 INFO Checking ILO name and DNS name for RETSE550-NX7002
2025-08-05 09:59:21,136 INFO Setting server name and DNS name for RETSE550-NX7002OOB
2025-08-05 09:59:21,136 INFO Retrieving RedFish Network Stack '*************'
2025-08-05 09:59:21,136 INFO Calling restapi, URL: https://*************/redfish/v1/SessionService/Sessions, method: POST, headers: {'Content-Type': 'application/json;charset=UTF-8'}
2025-08-05 09:59:21,136 INFO params: None
2025-08-05 09:59:21,136 INFO User: administrator
2025-08-05 09:59:21,136 INFO payload: {'UserName': 'administrator', 'Password': '*****'}
2025-08-05 09:59:21,136 INFO files: None
2025-08-05 09:59:21,137 INFO timeout: 5
2025-08-05 09:59:23,486 INFO Calling restapi, URL: https://*************/redfish/v1/Systems/1/, method: GET, headers: {'X-Auth-Token': '590eaf3ef733e22ec58d33f074846c60'}
2025-08-05 09:59:23,486 INFO params: None
2025-08-05 09:59:23,486 INFO User: administrator
2025-08-05 09:59:23,486 INFO payload: None
2025-08-05 09:59:23,486 INFO files: None
2025-08-05 09:59:23,486 INFO timeout: None
2025-08-05 09:59:24,741 INFO Get ILO HostName successfull.
2025-08-05 09:59:24,741 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/EthernetInterfaces/1/, method: GET, headers: None
2025-08-05 09:59:24,741 INFO params: None
2025-08-05 09:59:24,741 INFO User: administrator
2025-08-05 09:59:24,741 INFO payload: None
2025-08-05 09:59:24,741 INFO files: None
2025-08-05 09:59:24,741 INFO timeout: None
2025-08-05 09:59:25,870 INFO Got the response with OK
2025-08-05 09:59:25,872 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:59:25,872 INFO params: None
2025-08-05 09:59:25,872 INFO User: administrator
2025-08-05 09:59:25,872 INFO payload: None
2025-08-05 09:59:25,872 INFO files: None
2025-08-05 09:59:25,873 INFO timeout: None
2025-08-05 09:59:27,057 INFO Got the response with OK
2025-08-05 09:59:27,057 INFO Group Already exists.
2025-08-05 09:59:27,640 INFO LDAP Binding already in desired state.
2025-08-05 09:59:27,640 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:59:27,640 INFO params: None
2025-08-05 09:59:27,640 INFO User: administrator
2025-08-05 09:59:27,640 INFO payload: None
2025-08-05 09:59:27,640 INFO files: None
2025-08-05 09:59:27,640 INFO timeout: None
2025-08-05 09:59:28,865 INFO Got the response with OK
2025-08-05 09:59:28,866 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Roles/dirgroup3a7c6167d73b780ee5fe83de, method: GET, headers: None
2025-08-05 09:59:28,866 INFO params: None
2025-08-05 09:59:28,866 INFO User: administrator
2025-08-05 09:59:28,867 INFO payload: None
2025-08-05 09:59:28,867 INFO files: None
2025-08-05 09:59:28,867 INFO timeout: None
2025-08-05 09:59:30,058 INFO Got the response with OK
2025-08-05 09:59:30,059 INFO Privileges are already correct.
2025-08-05 09:59:30,059 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/EthernetInterfaces/1, method: GET, headers: None
2025-08-05 09:59:30,059 INFO params: None
2025-08-05 09:59:30,059 INFO User: administrator
2025-08-05 09:59:30,059 INFO payload: None
2025-08-05 09:59:30,059 INFO files: None
2025-08-05 09:59:30,059 INFO timeout: None
2025-08-05 09:59:31,227 INFO Got the response with OK
2025-08-05 09:59:31,227 INFO DNS is already correct.
2025-08-05 09:59:31,227 INFO Checking NTP configuration...
2025-08-05 09:59:31,227 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/DateTime, method: GET, headers: None
2025-08-05 09:59:31,227 INFO params: None
2025-08-05 09:59:31,227 INFO User: administrator
2025-08-05 09:59:31,227 INFO payload: None
2025-08-05 09:59:31,227 INFO files: None
2025-08-05 09:59:31,227 INFO timeout: None
2025-08-05 09:59:32,529 INFO Got the response with OK
2025-08-05 09:59:32,534 INFO Current DateTime configuration: {'@odata.context': '/redfish/v1/$metadata#HpeiLODateTime.HpeiLODateTime', '@odata.etag': 'W/"1BF57873"', '@odata.id': '/redfish/v1/Managers/1/DateTime', '@odata.type': '#HpeiLODateTime.v2_0_0.HpeiLODateTime', 'Id': 'DateTime', 'ConfigurationSettings': 'Current', 'DateTime': '2025-08-05T01:59:32Z', 'Links': {'EthernetNICs': {'@odata.id': '/redfish/v1/Managers/1/EthernetInterfaces'}}, 'NTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'Name': 'iLO Date and Time Settings', 'PropagateTimeToHost': False, 'StaticNTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'TimeZone': {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, 'TimeZoneList': [{'Index': 0, 'Name': 'International Date Line West', 'UtcOffset': '-12:00', 'Value': 'GMT+12:00'}, {'Index': 1, 'Name': 'Midway Island, Samoa', 'UtcOffset': '-11:00', 'Value': 'SST+11:00'}, {'Index': 2, 'Name': 'Hawaii', 'UtcOffset': '-10:00', 'Value': 'HST+10:00'}, {'Index': 3, 'Name': 'Marquesas', 'UtcOffset': '-09:30', 'Value': 'MART+9:30'}, {'Index': 4, 'Name': 'Alaska', 'UtcOffset': '-09:00', 'Value': 'AKST+9:00AKDT+08:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 5, 'Name': 'Pacific Time(US & Canada), Tijuana, Portland', 'UtcOffset': '-08:00', 'Value': 'PST+8:00PDT+07:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 6, 'Name': 'Arizona, Chihuahua, La Paz, Mazatlan, Mountain Time (US & Canad', 'UtcOffset': '-07:00', 'Value': 'MST+7:00MDT+06:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 7, 'Name': 'Central America, Central Time(US & Canada)', 'UtcOffset': '-06:00', 'Value': 'CST+6:00CDT+05:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 8, 'Name': 'Bogota, Lima, Quito, Eastern Time(US & Canada)', 'UtcOffset': '-05:00', 'Value': 'EST+5:00EDT+04:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 9, 'Name': 'Caracas, Georgetown', 'UtcOffset': '-04:00', 'Value': 'VET+4:00'}, {'Index': 10, 'Name': 'Atlantic Time(Canada), Santiago', 'UtcOffset': '-04:00', 'Value': 'AST+4:00ADT+03:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 11, 'Name': 'Newfoundland', 'UtcOffset': '-03:30', 'Value': 'NST+3:30NDT+02:30:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 12, 'Name': 'Brasilia, Buenos Aires, Greenland', 'UtcOffset': '-03:00', 'Value': 'ART+3:00'}, {'Index': 13, 'Name': 'Mid-Atlantic', 'UtcOffset': '-02:00', 'Value': 'GST+2:00'}, {'Index': 14, 'Name': 'Azores, Cape Verde Is.', 'UtcOffset': '-01:00', 'Value': 'CVT+1:00'}, {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, {'Index': 16, 'Name': 'Dublin, London', 'UtcOffset': '+00:00', 'Value': 'WET-0WEST-1,M3.5.0/01:00:00,M10.5.0/02:00:00'}, {'Index': 17, 'Name': 'Amsterdam, Berlin, Bern, Rome, Paris, West Central Africa', 'UtcOffset': '+01:00', 'Value': 'CET-1:00CEST-02:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 18, 'Name': 'Athens, Bucharest, Cairo, Jerusalem', 'UtcOffset': '+02:00', 'Value': 'EET-2:00EEST-03:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 19, 'Name': 'Baghdad, Kuwait, Riyadh, Moscow, Istanbul, Nairobi', 'UtcOffset': '+03:00', 'Value': 'AST-3:00'}, {'Index': 20, 'Name': 'Tehran', 'UtcOffset': '+03:30', 'Value': 'IRST-3:30IRDT-04:30:00,80/00:00:00,264/00:00:00'}, {'Index': 21, 'Name': 'Abu Dhabi, Muscat, Baku, Tbilisi, Yerevan', 'UtcOffset': '+04:00', 'Value': 'GST-4:00'}, {'Index': 22, 'Name': 'Kabul', 'UtcOffset': '+04:30', 'Value': 'AFT-4:30'}, {'Index': 23, 'Name': 'Ekaterinburg, Islamabad, Karachi, Tashkent', 'UtcOffset': '+05:00', 'Value': 'YEKT-5:00'}, {'Index': 24, 'Name': 'Chennai, Kolkata, Mumbai, New Delhi', 'UtcOffset': '+05:30', 'Value': 'IST-5:30'}, {'Index': 25, 'Name': 'Kathmandu', 'UtcOffset': '+05:45', 'Value': 'NPT-5:45'}, {'Index': 26, 'Name': 'Almaty, Dhaka, Sri Jayawardenepura', 'UtcOffset': '+06:00', 'Value': 'ALMT-6:00'}, {'Index': 27, 'Name': 'Rangoon', 'UtcOffset': '+06:30', 'Value': 'MMT-6:30'}, {'Index': 28, 'Name': 'Bangkok, Hanio, Jakarta, Novosibirsk, Astana, Krasnoyarsk', 'UtcOffset': '+07:00', 'Value': 'ICT-7:00'}, {'Index': 29, 'Name': 'Beijing, Chongqing, Hong Kong, Urumqi, Taipei, Perth', 'UtcOffset': '+08:00', 'Value': 'CST-8:00'}, {'Index': 30, 'Name': 'Eucla', 'UtcOffset': '+08:45', 'Value': 'ACWST-08:45'}, {'Index': 31, 'Name': 'Osaka, Sapporo, Tokyo, Seoul, Yakutsk', 'UtcOffset': '+09:00', 'Value': 'JST-9:00'}, {'Index': 32, 'Name': 'Adelaide, Darwin', 'UtcOffset': '+09:30', 'Value': 'ACST-9:30ACDT-10:30:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 33, 'Name': 'Canberra, Melbourne, Sydney, Guam, Hobart, Vladivostok', 'UtcOffset': '+10:00', 'Value': 'AEST-10:00AEDT-11:00:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 34, 'Name': 'Lord Howe', 'UtcOffset': '+10:30', 'Value': 'LHST-10:30LHDT11:00'}, {'Index': 35, 'Name': 'Chatham', 'UtcOffset': '+10:45', 'Value': 'CHAST-10:45CHADT-11:45'}, {'Index': 36, 'Name': 'Magadan, Solomon Is., New Caledonia', 'UtcOffset': '+11:00', 'Value': 'MAGT-11:00'}, {'Index': 37, 'Name': 'Auckland, Wellington, Fiji, Kamchatka, Marshall Is.', 'UtcOffset': '+12:00', 'Value': 'NZST-12:00NZDT-13:00:00,M9.5.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 38, 'Name': "Nuku'alofa", 'UtcOffset': '+13:00', 'Value': 'TKT-13:00'}, {'Index': 39, 'Name': 'Line Islands', 'UtcOffset': '+14:00', 'Value': 'LINT-14:00'}, {'Index': 40, 'Name': 'Unspecified Time Zone', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}]}
2025-08-05 09:59:32,534 INFO NTP configuration is already in desired state
2025-08-05 09:59:32,534 INFO No configuration changes made, skipping ILO reset.
2025-08-05 09:59:32,534 INFO Oneview part
2025-08-05 09:59:32,534 INFO Check if the OOB is in oneview
2025-08-05 09:59:33,603 INFO OOB is in oneview and state is normal
2025-08-05 09:59:33,617 INFO OOB configuration is done for RETSE550-NX7002.
2025-08-05 09:59:33,627 INFO ****************************************************************************************************
2025-08-05 09:59:33,627 INFO *                                                                                                  *
2025-08-05 09:59:33,627 INFO *                                 Checking OOB for RETSE550-NX7003                                 *
2025-08-05 09:59:33,627 INFO *                                                                                                  *
2025-08-05 09:59:33,627 INFO ****************************************************************************************************
2025-08-05 09:59:33,627 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSE550-NX7003oob.ikead2.com', method: GET, headers: None
2025-08-05 09:59:33,627 INFO params: None
2025-08-05 09:59:33,627 INFO User: <EMAIL>
2025-08-05 09:59:33,627 INFO payload: None
2025-08-05 09:59:33,627 INFO files: None
2025-08-05 09:59:33,627 INFO timeout: 30
2025-08-05 09:59:34,604 INFO SSH connecting to *************, this is the '1' try.
2025-08-05 09:59:37,220 INFO SSH connected to *************.
2025-08-05 09:59:38,382 INFO Sending 'sudo ipmitool lan print | grep "IP Address " | grep -v "Source"
' to the server.
2025-08-05 09:59:40,885 INFO OOB for RETSE550-NX7003 is reachable.
2025-08-05 09:59:40,886 INFO Checking iLO status...
2025-08-05 09:59:42,157 INFO ILO status is good.
2025-08-05 09:59:42,172 INFO Checking ILO name and DNS name for RETSE550-NX7003
2025-08-05 09:59:42,172 INFO Setting server name and DNS name for RETSE550-NX7003OOB
2025-08-05 09:59:42,172 INFO Retrieving RedFish Network Stack '*************'
2025-08-05 09:59:42,172 INFO Calling restapi, URL: https://*************/redfish/v1/SessionService/Sessions, method: POST, headers: {'Content-Type': 'application/json;charset=UTF-8'}
2025-08-05 09:59:42,172 INFO params: None
2025-08-05 09:59:42,172 INFO User: administrator
2025-08-05 09:59:42,173 INFO payload: {'UserName': 'administrator', 'Password': '*****'}
2025-08-05 09:59:42,173 INFO files: None
2025-08-05 09:59:42,173 INFO timeout: 5
2025-08-05 09:59:45,302 INFO Calling restapi, URL: https://*************/redfish/v1/Systems/1/, method: GET, headers: {'X-Auth-Token': '4e15ffbe886abb29d021163516cdb2cc'}
2025-08-05 09:59:45,302 INFO params: None
2025-08-05 09:59:45,302 INFO User: administrator
2025-08-05 09:59:45,302 INFO payload: None
2025-08-05 09:59:45,302 INFO files: None
2025-08-05 09:59:45,302 INFO timeout: None
2025-08-05 09:59:46,666 INFO Get ILO HostName successfull.
2025-08-05 09:59:46,668 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/EthernetInterfaces/1/, method: GET, headers: None
2025-08-05 09:59:46,668 INFO params: None
2025-08-05 09:59:46,668 INFO User: administrator
2025-08-05 09:59:46,668 INFO payload: None
2025-08-05 09:59:46,668 INFO files: None
2025-08-05 09:59:46,668 INFO timeout: None
2025-08-05 09:59:47,803 INFO Got the response with OK
2025-08-05 09:59:47,804 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:59:47,804 INFO params: None
2025-08-05 09:59:47,804 INFO User: administrator
2025-08-05 09:59:47,805 INFO payload: None
2025-08-05 09:59:47,805 INFO files: None
2025-08-05 09:59:47,805 INFO timeout: None
2025-08-05 09:59:49,128 INFO Got the response with OK
2025-08-05 09:59:49,128 INFO Group Already exists.
2025-08-05 09:59:49,596 INFO LDAP Binding already in desired state.
2025-08-05 09:59:49,596 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:59:49,596 INFO params: None
2025-08-05 09:59:49,597 INFO User: administrator
2025-08-05 09:59:49,597 INFO payload: None
2025-08-05 09:59:49,597 INFO files: None
2025-08-05 09:59:49,597 INFO timeout: None
2025-08-05 09:59:50,851 INFO Got the response with OK
2025-08-05 09:59:50,853 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Roles/dirgroup3a7c6167d73b780ee5fe83de, method: GET, headers: None
2025-08-05 09:59:50,853 INFO params: None
2025-08-05 09:59:50,853 INFO User: administrator
2025-08-05 09:59:50,853 INFO payload: None
2025-08-05 09:59:50,853 INFO files: None
2025-08-05 09:59:50,854 INFO timeout: None
2025-08-05 09:59:52,064 INFO Got the response with OK
2025-08-05 09:59:52,069 INFO Privileges are already correct.
2025-08-05 09:59:52,069 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/EthernetInterfaces/1, method: GET, headers: None
2025-08-05 09:59:52,069 INFO params: None
2025-08-05 09:59:52,069 INFO User: administrator
2025-08-05 09:59:52,069 INFO payload: None
2025-08-05 09:59:52,069 INFO files: None
2025-08-05 09:59:52,069 INFO timeout: None
2025-08-05 09:59:53,257 INFO Got the response with OK
2025-08-05 09:59:53,257 INFO DNS is already correct.
2025-08-05 09:59:53,257 INFO Checking NTP configuration...
2025-08-05 09:59:53,257 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/DateTime, method: GET, headers: None
2025-08-05 09:59:53,257 INFO params: None
2025-08-05 09:59:53,257 INFO User: administrator
2025-08-05 09:59:53,257 INFO payload: None
2025-08-05 09:59:53,257 INFO files: None
2025-08-05 09:59:53,257 INFO timeout: None
2025-08-05 09:59:54,566 INFO Got the response with OK
2025-08-05 09:59:54,567 INFO Current DateTime configuration: {'@odata.context': '/redfish/v1/$metadata#HpeiLODateTime.HpeiLODateTime', '@odata.etag': 'W/"1BF57873"', '@odata.id': '/redfish/v1/Managers/1/DateTime', '@odata.type': '#HpeiLODateTime.v2_0_0.HpeiLODateTime', 'Id': 'DateTime', 'ConfigurationSettings': 'Current', 'DateTime': '2025-08-05T01:59:54Z', 'Links': {'EthernetNICs': {'@odata.id': '/redfish/v1/Managers/1/EthernetInterfaces'}}, 'NTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'Name': 'iLO Date and Time Settings', 'PropagateTimeToHost': False, 'StaticNTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'TimeZone': {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, 'TimeZoneList': [{'Index': 0, 'Name': 'International Date Line West', 'UtcOffset': '-12:00', 'Value': 'GMT+12:00'}, {'Index': 1, 'Name': 'Midway Island, Samoa', 'UtcOffset': '-11:00', 'Value': 'SST+11:00'}, {'Index': 2, 'Name': 'Hawaii', 'UtcOffset': '-10:00', 'Value': 'HST+10:00'}, {'Index': 3, 'Name': 'Marquesas', 'UtcOffset': '-09:30', 'Value': 'MART+9:30'}, {'Index': 4, 'Name': 'Alaska', 'UtcOffset': '-09:00', 'Value': 'AKST+9:00AKDT+08:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 5, 'Name': 'Pacific Time(US & Canada), Tijuana, Portland', 'UtcOffset': '-08:00', 'Value': 'PST+8:00PDT+07:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 6, 'Name': 'Arizona, Chihuahua, La Paz, Mazatlan, Mountain Time (US & Canad', 'UtcOffset': '-07:00', 'Value': 'MST+7:00MDT+06:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 7, 'Name': 'Central America, Central Time(US & Canada)', 'UtcOffset': '-06:00', 'Value': 'CST+6:00CDT+05:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 8, 'Name': 'Bogota, Lima, Quito, Eastern Time(US & Canada)', 'UtcOffset': '-05:00', 'Value': 'EST+5:00EDT+04:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 9, 'Name': 'Caracas, Georgetown', 'UtcOffset': '-04:00', 'Value': 'VET+4:00'}, {'Index': 10, 'Name': 'Atlantic Time(Canada), Santiago', 'UtcOffset': '-04:00', 'Value': 'AST+4:00ADT+03:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 11, 'Name': 'Newfoundland', 'UtcOffset': '-03:30', 'Value': 'NST+3:30NDT+02:30:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 12, 'Name': 'Brasilia, Buenos Aires, Greenland', 'UtcOffset': '-03:00', 'Value': 'ART+3:00'}, {'Index': 13, 'Name': 'Mid-Atlantic', 'UtcOffset': '-02:00', 'Value': 'GST+2:00'}, {'Index': 14, 'Name': 'Azores, Cape Verde Is.', 'UtcOffset': '-01:00', 'Value': 'CVT+1:00'}, {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, {'Index': 16, 'Name': 'Dublin, London', 'UtcOffset': '+00:00', 'Value': 'WET-0WEST-1,M3.5.0/01:00:00,M10.5.0/02:00:00'}, {'Index': 17, 'Name': 'Amsterdam, Berlin, Bern, Rome, Paris, West Central Africa', 'UtcOffset': '+01:00', 'Value': 'CET-1:00CEST-02:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 18, 'Name': 'Athens, Bucharest, Cairo, Jerusalem', 'UtcOffset': '+02:00', 'Value': 'EET-2:00EEST-03:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 19, 'Name': 'Baghdad, Kuwait, Riyadh, Moscow, Istanbul, Nairobi', 'UtcOffset': '+03:00', 'Value': 'AST-3:00'}, {'Index': 20, 'Name': 'Tehran', 'UtcOffset': '+03:30', 'Value': 'IRST-3:30IRDT-04:30:00,80/00:00:00,264/00:00:00'}, {'Index': 21, 'Name': 'Abu Dhabi, Muscat, Baku, Tbilisi, Yerevan', 'UtcOffset': '+04:00', 'Value': 'GST-4:00'}, {'Index': 22, 'Name': 'Kabul', 'UtcOffset': '+04:30', 'Value': 'AFT-4:30'}, {'Index': 23, 'Name': 'Ekaterinburg, Islamabad, Karachi, Tashkent', 'UtcOffset': '+05:00', 'Value': 'YEKT-5:00'}, {'Index': 24, 'Name': 'Chennai, Kolkata, Mumbai, New Delhi', 'UtcOffset': '+05:30', 'Value': 'IST-5:30'}, {'Index': 25, 'Name': 'Kathmandu', 'UtcOffset': '+05:45', 'Value': 'NPT-5:45'}, {'Index': 26, 'Name': 'Almaty, Dhaka, Sri Jayawardenepura', 'UtcOffset': '+06:00', 'Value': 'ALMT-6:00'}, {'Index': 27, 'Name': 'Rangoon', 'UtcOffset': '+06:30', 'Value': 'MMT-6:30'}, {'Index': 28, 'Name': 'Bangkok, Hanio, Jakarta, Novosibirsk, Astana, Krasnoyarsk', 'UtcOffset': '+07:00', 'Value': 'ICT-7:00'}, {'Index': 29, 'Name': 'Beijing, Chongqing, Hong Kong, Urumqi, Taipei, Perth', 'UtcOffset': '+08:00', 'Value': 'CST-8:00'}, {'Index': 30, 'Name': 'Eucla', 'UtcOffset': '+08:45', 'Value': 'ACWST-08:45'}, {'Index': 31, 'Name': 'Osaka, Sapporo, Tokyo, Seoul, Yakutsk', 'UtcOffset': '+09:00', 'Value': 'JST-9:00'}, {'Index': 32, 'Name': 'Adelaide, Darwin', 'UtcOffset': '+09:30', 'Value': 'ACST-9:30ACDT-10:30:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 33, 'Name': 'Canberra, Melbourne, Sydney, Guam, Hobart, Vladivostok', 'UtcOffset': '+10:00', 'Value': 'AEST-10:00AEDT-11:00:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 34, 'Name': 'Lord Howe', 'UtcOffset': '+10:30', 'Value': 'LHST-10:30LHDT11:00'}, {'Index': 35, 'Name': 'Chatham', 'UtcOffset': '+10:45', 'Value': 'CHAST-10:45CHADT-11:45'}, {'Index': 36, 'Name': 'Magadan, Solomon Is., New Caledonia', 'UtcOffset': '+11:00', 'Value': 'MAGT-11:00'}, {'Index': 37, 'Name': 'Auckland, Wellington, Fiji, Kamchatka, Marshall Is.', 'UtcOffset': '+12:00', 'Value': 'NZST-12:00NZDT-13:00:00,M9.5.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 38, 'Name': "Nuku'alofa", 'UtcOffset': '+13:00', 'Value': 'TKT-13:00'}, {'Index': 39, 'Name': 'Line Islands', 'UtcOffset': '+14:00', 'Value': 'LINT-14:00'}, {'Index': 40, 'Name': 'Unspecified Time Zone', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}]}
2025-08-05 09:59:54,567 INFO NTP configuration is already in desired state
2025-08-05 09:59:54,567 INFO No configuration changes made, skipping ILO reset.
2025-08-05 09:59:54,567 INFO Oneview part
2025-08-05 09:59:54,568 INFO Check if the OOB is in oneview
2025-08-05 09:59:55,692 INFO OOB is in oneview and state is normal
2025-08-05 09:59:55,712 INFO OOB configuration is done for RETSE550-NX7003.
2025-08-05 09:59:55,741 INFO Oob: Done
2025-08-05 09:59:55,751 INFO ****************************************************************************************************
2025-08-05 09:59:55,751 INFO *                                                                                                  *
2025-08-05 09:59:55,751 INFO *                                       Configuring Pulse...                                       *
2025-08-05 09:59:55,751 INFO *                                                                                                  *
2025-08-05 09:59:55,751 INFO ****************************************************************************************************
2025-08-05 09:59:57,591 INFO Desired State 'pulse' has been disabled inside this site profile.
2025-08-05 09:59:57,607 INFO Configure User License Agreement Acceptance...
2025-08-05 09:59:57,608 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/eulas, method: GET, headers: None
2025-08-05 09:59:57,608 INFO params: None
2025-08-05 09:59:57,608 INFO User: 1-click-nutanix
2025-08-05 09:59:57,608 INFO payload: None
2025-08-05 09:59:57,608 INFO files: None
2025-08-05 09:59:57,608 INFO timeout: 30
2025-08-05 09:59:58,988 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/eulas/accept, method: POST, headers: None
2025-08-05 09:59:58,988 INFO params: None
2025-08-05 09:59:58,988 INFO User: 1-click-nutanix
2025-08-05 09:59:58,988 INFO payload: {'username': 'Emil Nilsson', 'companyName': 'IKEA', 'jobTitle': 'Digital Technology Engineer'}
2025-08-05 09:59:58,989 INFO files: None
2025-08-05 09:59:58,989 INFO timeout: 30
2025-08-05 10:00:00,146 INFO Configuring Pulse...
2025-08-05 10:00:00,147 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/pulse, method: PUT, headers: None
2025-08-05 10:00:00,148 INFO params: None
2025-08-05 10:00:00,148 INFO User: 1-click-nutanix
2025-08-05 10:00:00,148 INFO payload: {'enable': True, 'enableDefaultNutanixEmail': 'false', 'isPulsePromptNeeded': 'false'}
2025-08-05 10:00:00,148 INFO files: None
2025-08-05 10:00:00,148 INFO timeout: 30
2025-08-05 10:00:01,672 INFO Configuring Remote Diagnostics...
2025-08-05 10:00:01,672 INFO Disabling remote diagnostics for RETSE550-NXC000.IKEAD2.COM...
2025-08-05 10:00:02,233 INFO Trying to SSH to the RETSE550-NXC000.IKEAD2.COM.
2025-08-05 10:00:02,235 INFO First try with username/password.
2025-08-05 10:00:02,235 INFO SSH connecting to RETSE550-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 10:00:04,792 INFO SSH connected to RETSE550-NXC000.IKEAD2.COM.
2025-08-05 10:00:08,917 INFO Sending '/home/<USER>/ncc/bin/nusights/set_remote_diagnostics_status --enable=false' to the server.
2025-08-05 10:00:08,917 INFO Checking result
2025-08-05 10:00:08,917 INFO Sending '/usr/local/nutanix/cluster/bin/zkcat /appliance/logical/nusights/collectors/kCommand/override_config' to the server.
2025-08-05 10:00:10,918 INFO Receiving the output .
2025-08-05 10:00:10,918 INFO Received the output: #SSH OUTPUT START#.
2025-08-05 10:00:10,918 INFO 

Nutanix Controller VM (CVM) is a virtual storage appliance.



Alteration of the CVM (unless advised by Nutanix Technical Support or

Support Portal Documentation) is unsupported and may result in loss

of User VMs or other data residing on the cluster.



Unsupported alterations may include (but are not limited to):



- Configuration changes / removal of files.

- Installation of third-party software/scripts not approved by Nutanix.

- Installation or upgrade of software packages from non-Nutanix

  sources (using yum, rpm, or similar).



** SSH to CVM via 'nutanix' user will be restricted in coming releases.  **

** Please consider using the 'admin' user for basic workflows.           **

Last login: Tue Aug  5 01:59:23 UTC 2025 from ************* on ssh

Last login: Tue Aug  5 02:00:04 2025 from **************


nutanix@NTNX-CZ202301X4-A-CVM:*************:~$ /home/<USER>/ncc/bin/nusights/set
t_remote_diagnostics_status --enable=false

RCC status has been changed successfully, rcc enabled = false

nutanix@NTNX-CZ202301X4-A-CVM:*************:~$ /usr/local/nutanix/cluster/bin/zkc
cat /appliance/logical/nusights/collectors/kCommand/override_config

{"rcc_enabled":false,"rcc_status_details":""}nutanix@NTNX-CZ202301X4-A-CVM:*************:~$ 
2025-08-05 10:00:10,918 INFO #SSH OUTPUT END#
2025-08-05 10:00:10,935 INFO Remote diagnostics has been disabled.
2025-08-05 10:00:10,947 INFO Pulse: Done
2025-08-05 10:00:10,962 INFO ****************************************************************************************************
2025-08-05 10:00:10,962 INFO *                                                                                                  *
2025-08-05 10:00:10,962 INFO *                                Configuring SyncBackupSettings...                                 *
2025-08-05 10:00:10,962 INFO *                                                                                                  *
2025-08-05 10:00:10,962 INFO ****************************************************************************************************
2025-08-05 10:00:13,706 ERROR ['Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 91, in task_process\n    c(self.pe, self.logger, self.db_logger, self.facility_type).configure()\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 1537, in __init__\n    self.rs_target_site_sa = self._init_target_site_sa()\n                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 1544, in _init_target_site_sa\n    raise VaultGetSecretFailed(pe_svc_label)\n', 'business.generic.base_up_exception.VaultGetSecretFailed: Failed to get secret data of None/Site_Pe_Svc!\n']
2025-08-05 10:00:13,711 WARNING Failed on current step, but will continue the other steps...
2025-08-05 10:00:13,760 ERROR Task failed. Detail: The task is done, but with some error occurred.

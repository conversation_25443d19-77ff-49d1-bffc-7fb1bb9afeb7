import axios from 'axios'
import { endpoint } from './endpoint'
import request from '@/utils/request'

export function GetVCList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/sli/vc/list`,config)
  return res
}

export function GetClusterList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/sli/cluster/list`,config)
  return res
}

export function GetSliHostsList(token,download = false) {
  if(download){
    var config = {
      headers: {'Authorization': 'Bearer ' + token},
      responseType:'blob'
    };
    let res =  request.get(`${endpoint}/sli/host?download=true`,config)
    return res
  }
  else{ 
    var config = {
        headers: { 'Authorization': 'Bearer ' + token }
      };
      let res = request.get(`${endpoint}/sli/host`, config)
      return res
  }
}

export function GetSLIVMList(token, download = false){
  if(download){
    var config = {
      headers: {'Authorization': 'Bearer ' + token},
      responseType:'blob'
    };
    let res =  request.get(`${endpoint}/sli/vms?download=true`,config)
    return res
  }
  else{
    var config = {
      headers: {'Authorization': 'Bearer ' +token}
    };

    let res =  request.get(`${endpoint}/sli/vms`,config)
    return res
  }

}

export function GetPMTaskList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/slipmtasklist`,config)
  return res
}
export function GetSLIPMTasks(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/slipmtasks`,config)
  return res
}

export function DeleteSLIPM(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/sli/pm/delete`,param.data,config)
  return res
}
export function GetVCClusterCorrespondence(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/sli/correspondence`,config)
  return res
}

export function AbortSLIPM(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/sli/pm/abort`,param.data,config)
  return res
}

export function CreateSLIPM(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/pm/sli/create`,param.data,config)
  return res
}

export function DownloadPMLog(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token},
    responseType:'blob'
  }
  let res = request.post(`${endpoint}/download`,param.data,config)
  return res
}
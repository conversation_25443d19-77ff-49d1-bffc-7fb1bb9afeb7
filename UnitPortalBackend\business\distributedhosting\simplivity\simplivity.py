import base64
import json
import logging
import platform
import re
import ssl
import subprocess
import time
import pytz
import requests
from datetime import datetime
from flask import jsonify
from pyVim.connect import SmartConnect
from pyVim.task import WaitForTask
from pyVmomi import vim

from business.authentication.authentication import ServiceAccount
from models.sli_models import ModelSLIHostSchema, ModelSLIHost, ModelSLIVMs, ModelSLIVMsSchema, ModelSLIClusterSchema, \
    ModelSLICluster, ModelSLIvCenterSchema, ModelSLIvCenter


class SimplivityvCenter():
    def __init__(self, vc, username, password, logger=logging, taskid=None, l_g=None) -> None:
        self.vc = vc
        self.username = username
        self.password = password
        self.logger = logger
        self.taskid = taskid
        self.l_g = l_g

    def connect(self):
        try:
            s = ssl.SSLContext(ssl.PROTOCOL_TLSv1_2)
            # s = ssl.SSLContext(ssl.TLSv1_2_METHOD)
            s.verify_mode = ssl.CERT_NONE
            self.session = SmartConnect(host=self.vc, user=self.username, pwd=self.password, sslContext=s)
            self.logger.info(f"Successfully Connected to the VC : {self.vc}")
            # self.l_g.write_pm_log(loginfo=f"Successfully Connected to the VC : {self.vc}",taskid = self.taskid ,logseverity='info',logtype='SLI_PM')    # noqa
            return True
        except Exception:
            self.logger.info("Error : Got an error when connecting to vCenter.")
            # self.l_g.write_pm_log(loginfo=f' Got an error when connecting to vCenter : {self.vc}' ,taskid = self.taskid ,logseverity='info' )    # noqa
            return False

    def name_filter(self, obj_list, name):
        if name:
            if isinstance(name, list):
                result_list = []
                for res in obj_list:
                    if res.name in name:
                        result_list.append(res)
                return result_list
            if isinstance(name, str):
                for res in obj_list:
                    if res.name == name:
                        return [res]
            else:
                return None
        else:
            return obj_list

    def get_obj(self, vimtype, name=None):
        """Get the vsphere object associated with a given text name"""
        container = self.session.content.viewManager.CreateContainerView(self.session.content.rootFolder, vimtype, True)
        return self.name_filter(container.view, name)

    def get_cluster(self, clustername=None):
        vimtype = [vim.ClusterComputeResource]
        return self.get_obj(vimtype, clustername)

    def get_vm(self, clustername=None, vmname=None, include_template=True):
        vimtype = [vim.VirtualMachine]
        vms = []
        if clustername:  # get vms in specific clusters
            clusters = self.get_cluster(clustername)
            nodes = [cluster.host for cluster in clusters]
            # for nod in nodes[0]:
            #     vms   = [node.vm for node in nod]
            for node in nodes[0]:
                vms += node.vm
        else:  # get all vms in vcenter
            vms = self.get_obj(vimtype, vmname)
        if vmname:
            vms = self.name_filter(vms, vmname)
        if include_template:
            return vms
        return [vm for vm in vms if not vm.config.template]

    def contains(self, other):
        return self.__contains__(other)

    def get_standardvm(self, clustername=None, include_template=True):
        vms = []
        if clustername:  # get vms in specific clusters
            # vms=self.get_vm(clustername=clustername)
            clusters = self.get_cluster(clustername)
            nodes = [cluster.host for cluster in clusters]
            for nod in nodes[0]:
                vms = [node.vm for node in nod]
            vmlist = vms[0] + vms[1]
        if include_template:
            return vms
        return [vm for vm in vmlist if not vm.config.template]
        
    def get_nt_vms(self, clustername):
        _pat = re.compile(r'-NT|lx40')
        vms = self.get_vm(clustername=clustername)
        vm = [vm.name for vm in vms if re.search(_pat, vm.name)]
        return vm
    
    def get_dashoststate(self, clustername=None):
        hosts_info = {}
        if clustername:
            clusters = self.get_cluster(clustername)
            datastores = [cluster.datastore for cluster in clusters]
            nodes = [cluster.host for cluster in clusters]
            for nod in nodes[0]:
                if nod.summary.runtime.dasHostState.state == 'master':
                    sn = nod.hardware.systemInfo.otherIdentifyingInfo[1].identifierValue
                    datastore_sn = "datastore-" + sn
                    datastore_ovc = self.name_filter(datastores[0], datastore_sn)
                    ovc_master = datastore_ovc[0].vm
                    master_ovcip = ovc_master[0].summary.guest.ipAddress
                    hosts_info['master_host'] = nod.name
                    hosts_info['master_ovcip'] = master_ovcip
                    hosts_info['master_ovc'] = ovc_master[0].name
                    # return nod.name,ovc_master.name
                else:
                    # nod.summary.runtime.dasHostState.state == 'connectedToMaster':
                    sn = nod.hardware.systemInfo.otherIdentifyingInfo[1].identifierValue
                    datastore_sn = "datastore-" + sn
                    datastore_ovc = self.name_filter(datastores[0], datastore_sn)
                    ovc_slave = datastore_ovc[0].vm
                    slave_ovcip = ovc_slave[0].summary.guest.ipAddress
                    hosts_info['slave_host'] = nod.name
                    hosts_info['slave_ovcip'] = slave_ovcip
                    hosts_info['slave_ovc'] = ovc_slave[0].name
                    # return nod.name,ovc_slave.name
            return hosts_info

    def control_maintenancemode(self, mode_status, hostname=None, cluster_name=None):      # noqa
        hosts = self.get_cluster(clustername=cluster_name)[0].host
        if hostname:
            host = self.name_filter(hosts, hostname)[0]
            if mode_status == "on":
                if not host.summary.runtime.inMaintenanceMode:
                    #     return True
                    # else:
                    task = host.EnterMaintenanceMode(100)
                    WaitForTask(task, si=None)
                return True
            if mode_status == "off":
                if host.summary.runtime.inMaintenanceMode:
                    task = host.ExitMaintenanceMode(100)
                    WaitForTask(task, si=None)
                #     return True
                # else:
                return True
        else:
            for host in hosts:
                if mode_status == "on":
                    if not host.summary.runtime.inMaintenanceMode:
                        #     return False
                        # else:
                        task = host.EnterMaintenanceMode(100)
                        WaitForTask(task, si=None)
                        self.logger.info(f"{host.name} Enabled Maintenance Mode successfuly.")
                        self.l_g.write_pm_log(loginfo=f"{host.name} Enabled Maintenance Mode successfuly.",
                                              taskid=self.taskid, logseverity='info')
                    # return True
                if mode_status == "off":
                    if host.summary.runtime.inMaintenanceMode:
                        task = host.ExitMaintenanceMode(100)
                        WaitForTask(task, si=None)        # noqa
                        self.logger.info(f"{host.name} Exited Maintenance Mode successfuly.")
                        self.l_g.write_pm_log(loginfo=f"{host.name} Exited Maintenance Mode successfuly.",
                                              taskid=self.taskid, logseverity='info')
                    # return True
                    # else:
                    #     return False
            return True

    def controlha(self, cluster_name=None, ha_status=None):
        cluster = self.get_cluster(clustername=cluster_name)[0]
        ha_enabled = cluster.configuration.dasConfig.enabled
        if ha_status == "Disabled":
            if ha_enabled:
                cluster_spec = vim.cluster.ConfigSpecEx()
                das_info = vim.cluster.DasConfigInfo()
                das_info.enabled = False
                cluster_spec.dasConfig = das_info
                cluster.ReconfigureComputeResource_Task(cluster_spec, True)
                task = cluster.ReconfigureComputeResource_Task(cluster_spec, True)
                WaitForTask(task, si=None)    # noqa
                # return res
                # print(res)
                self.logger.info("Successfully Disabled the HA.")
                self.l_g.write_pm_log(loginfo="Successfully Disabled the HA. ", taskid=self.taskid, logseverity='info')
            else:
                self.l_g.write_pm_log(loginfo="HA has been Disabled", taskid=self.taskid, logseverity='info')
                self.logger.info("HA has been Disabled")
        if ha_status == "Enable":
            if not ha_enabled:
                cluster_spec = vim.cluster.ConfigSpecEx()
                das_info = vim.cluster.DasConfigInfo()
                das_info.enabled = True
                cluster_spec.dasConfig = das_info
                cluster.ReconfigureComputeResource_Task(cluster_spec, True)
                task = cluster.ReconfigureComputeResource_Task(cluster_spec, True)
                WaitForTask(task, si=None)    # noqa
                # return res
                # print(res)
                self.logger.info("Successfully Enabled the HA")
                self.l_g.write_pm_log(loginfo="Successfully Enabled the HA", taskid=self.taskid, logseverity='info')
            else:
                self.l_g.write_pm_log(loginfo="HA has been Enabled", taskid=self.taskid, logseverity='info')
                self.logger.info("HA has been Enabled")
        # return True

    def get_ovctoken(self, ovcaddress):
        simplivity = "simplivity:"
        b64_val = base64.b64encode(simplivity.encode("utf-8"))
        b64_str = str(b64_val, "utf-8")
        url = "https://" + ovcaddress + "/api/oauth/token"
        body = {
            "grant_type": "password",
            "username": self.username,
            "password": self.password
        }
        headers = {
            "Authorization": "Basic %s" % b64_str,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        return requests.post(url, headers=headers, params=body, verify=False)

    def get_vmstatus(self, ovcadd, clustername):
        vms = self.get_vm(clustername=clustername, include_template=False)
        vmlist = [vm.name for vm in vms]
        vmname = ','.join(vmlist)
        reqes = self.get_ovctoken(ovcaddress=ovcadd)
        if reqes:
            access_token = reqes.json()['access_token']
            headers_hastatus = {
                'Authorization': 'Bearer %s' % access_token
            }
            body_hastatus = {
                'show_optional_fields': '1',
                'name': vmname
            }
            # body_HAstatus={
            #     'name': 'vmname'
            # }
            url_hastatus = "https://" + ovcadd + "/api/virtual_machines"
            reqes_infos = requests.get(url_hastatus, headers=headers_hastatus, params=body_hastatus, verify=False)
            return [reqes_info for reqes_info in reqes_infos.json()['virtual_machines']]
        return False
            # print("ovc not reachable")

    def ovc_shutdown(self, hostname, ovcadd, ovc_name, clustername):
        i = 0
        try:
            vm_ovc = self.get_vm(vmname=ovc_name, clustername=clustername)[0]
            if vm_ovc.runtime.powerState == "poweredOn":
                reqes = self.get_ovctoken(ovcaddress=ovcadd)
                # print(reqes)
                if reqes:
                    access_token = reqes.json()['access_token']
                    headers_hostid = {
                        'Authorization': f'Bearer {access_token}'
                    }
                    body_hostid = {
                        'name': hostname
                    }
                    url_hostid = "https://" + ovcadd + "/api/hosts"

                    reqes_hostid = requests.get(url_hostid, headers=headers_hostid, params=body_hostid, verify=False)
                    hostid = reqes_hostid.json()['hosts'][0]['id']
                    url_ovc = "https://" + ovcadd + "/api/hosts/" + hostid + "/shutdown_virtual_controller"
                    body_ovc = {
                        'ha_wait': 'true'
                    }
                    headers_ovc = {
                        'Authorization': 'Bearer %s' % access_token,
                        'Content-Typ': 'application/vnd.simplivity.v1+json'
                    }
                    reqes_ovc = requests.post(url_ovc, headers=headers_ovc, json=body_ovc, verify=False)    # noqa  # pylint: disable=W0612
                    while i <= 15:
                        vm_ovc = self.get_vm(vmname=ovc_name, clustername=clustername)[0]
                        if vm_ovc.runtime.powerState == "poweredOn":
                            self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} powering off in progress...",
                                                  taskid=self.taskid, logseverity='info')
                            self.logger.info(f"{vm_ovc.name} powering off in progress...")
                            time.sleep(10)
                            i += 1
                            if i == 10:
                                self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} powered off by force!",
                                                      taskid=self.taskid, logseverity='info')
                                self.logger.info(f"{vm_ovc.name} powered off by force!")
                                vm_ovc.PowerOff()
                        else:
                            self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} powered off successfully", taskid=self.taskid,
                                                  logseverity='info')
                            self.logger.info(f"{vm_ovc.name} powered off successfully")
                            break
            return True
        except BaseException:
            # if reqes_ovc:
            #     return True
            # def ilo_connection(self,ILOUrl):
            #     try:
            #         urllib.request.urlopen(ILOUrl, timeout=10)
            #         return True
            #     except:
            return False

    def ovc_poweron(self, cluster_name, ovc_name):      # noqa
        vm_ovc = self.get_vm(vmname=ovc_name, clustername=cluster_name)[0]
        i = 0
        try:
            if vm_ovc.runtime.powerState == "poweredOn":
                if len(vm_ovc.guest.net[1].ipAddress) == 2:
                    self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} has been powered on with 4 IPs", taskid=self.taskid,
                                          logseverity='info')
                    self.logger.info(f"{vm_ovc.name} has been powered on with 4 IPs")
                    return True
                if len(vm_ovc.guest.net[1].ipAddress) == 1:
                    self.logger.info(f"{vm_ovc.name} has been powered on with 3 IPs")
                    self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} has been powered on with 3 IPs", taskid=self.taskid,
                                          logseverity='info')
                    return True
                self.l_g.write_pm_log(
                    loginfo=f"{vm_ovc.name} has been powered on with 2 IPs, sleep 1 mins for Storage sync.",
                    taskid=self.taskid, logseverity='info')
                self.logger.info(f"{vm_ovc.name} has been powered on with 2 IPs, sleep 1 mins for Storage sync.")
                self.logger.info
                time.sleep(80)
                if len(vm_ovc.guest.net[1].ipAddress) == 1 or len(vm_ovc.guest.net[1].ipAddress) == 2:
                    self.logger.info(f"{vm_ovc.name} has been powered on")
                    return True
            self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} is still powered off, powering on...", taskid=self.taskid,
                                  logseverity='info')
            self.logger.info(f"{vm_ovc.name} is still powered off, powering on...")
            vm_ovc.PowerOn()
            time.sleep(30)
            # print(vm_ovc.guest.net[1].ipAddress)
            self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} powering on is still in progress, sleep 30 secs...",
                                  taskid=self.taskid, logseverity='info')
            self.logger.info(f"{vm_ovc.name} powering on is still in progress, sleep 40 secs...")
            time.sleep(40)
            self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} powering on is still in progress, sleep 30 secs...",
                                  taskid=self.taskid, logseverity='info')
            self.logger.info(f"{vm_ovc.name} powering on is still in progress, sleep 30 secs...")
            time.sleep(40)
            self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} powering on is still in progress, sleep 30 secs...",
                                  taskid=self.taskid, logseverity='info')
            self.logger.info(f"{vm_ovc.name} powering on is still in progress, sleep 30 secs...")
            time.sleep(30)
            # print(vm_ovc.guest.net[1].ipAddress)
            while i <= 24:
                if vm_ovc.runtime.powerState == "poweredOn":
                    timenow = datetime.now()
                    ovc_boottime = ((vm_ovc.runtime.bootTime).astimezone(pytz.utc)).replace(tzinfo=None)
                    if (timenow - ovc_boottime).seconds > 150:

                        # if len(vm_ovc.guest.net[1].ipAddress):
                        if len(vm_ovc.guest.net[1].ipAddress) != 1 and len(vm_ovc.guest.net[1].ipAddress) != 2:
                            self.l_g.write_pm_log(
                                loginfo=f"{vm_ovc.name} has been powered on with 2 IPs, sleep 10 secs for the Sync... ",
                                taskid=self.taskid, logseverity='info')
                            self.logger.info(
                                f"{vm_ovc.name} has been powered on with 2 IPs, sleep 10 secs for the Sync...")
                            time.sleep(20)
                            i += 1
                        else:
                            self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} : Successfully powered on. ",
                                                  taskid=self.taskid, logseverity='info')
                            self.logger.info(f"{vm_ovc.name} : Successfully powered on")
                            break
                        # else:
                        #     print(len(vm_ovc.guest.net[1].ipAddress))
                        #     self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} : OS powering on in progress... ",taskid = self.taskid ,logseverity='info')    # noqa
                        #     self.logger.info(f"{vm_ovc.name} OS powering on in progress...")
                        #     print(vm_ovc.name+" OS powering on in progress...")
                        #     time.sleep(10)
                        #     continue
                    else:
                        self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} : OS powering on in progress... ",
                                              taskid=self.taskid, logseverity='info')
                        self.logger.info(f"{vm_ovc.name} OS powering on in progress...")
                        time.sleep(10)
                        i += 1
                        continue
                else:
                    self.l_g.write_pm_log(loginfo=f"{vm_ovc.name} : OS still is off, check again in 10 secs... ",
                                          taskid=self.taskid, logseverity='info')
                    self.logger.info(f"{vm_ovc.name} OS still is off, check again in 10 secs...")
                    time.sleep(10)
                    i += 1
                    continue
            return True
        except BaseException as e:
            self.l_g.write_pm_log(loginfo=f"{e}", taskid=self.taskid, logseverity='info')
            self.logger.info(f"{e} ")
            return False

    def ilo_connection(self, vmname):
        param = '-n' if platform.system().lower() == 'windows' else '-c'
        command = ['ping', param, '1', vmname]
        return subprocess.call(command) == 0

    # get_ilotoken
    def get_ilotoken(self, iloaddress, ilo_user, ilo_password):
        url_ilotoken = "https://" + iloaddress + "/redfish/v1/SessionService/Sessions"
        body_ilotoken = {
            "UserName": ilo_user,
            "Password": ilo_password
        }
        reqes = requests.post(url_ilotoken, headers={"Content-Type": "application/json;charset=UTF-8"},
                              data=json.dumps(body_ilotoken), verify=False)
        return reqes.headers["X-Auth-Token"]

    def get_ilostate(self, iloaddress, ilo_user, ilo_password):
        ilotoken = self.get_ilotoken(iloaddress=iloaddress, ilo_user=ilo_user, ilo_password=ilo_password)
        # get_ilostate
        header_ilostate = {'X-Auth-Token': ilotoken}
        url_ilostate = "https://" + iloaddress + "/redfish/v1/Systems/1"
        reqes_ilostate = requests.get(url_ilostate, headers=header_ilostate, verify=False)
        return reqes_ilostate.json()

    # Change-ILOHostState
    def change_ilohoststate(self, iloaddress, resettye, ilo_user, ilo_password):
        ilotoken = self.get_ilotoken(iloaddress=iloaddress, ilo_user=ilo_user, ilo_password=ilo_password)
        url_resthoststate = "https://" + iloaddress + "/redfish/v1/Systems/1/Actions/ComputerSystem.Reset"
        header_resthoststate = {'X-Auth-Token': ilotoken}
        body_resthoststate = {'ResetType': resettye}
        reques_resthoststate = requests.post(url_resthoststate, headers=header_resthoststate, json=body_resthoststate,
                                             verify=False)
        if reques_resthoststate == 201:
            return True
        return False

    def verify_cluster(self, country_code, site_code):
        if country_code == "NL" and site_code == 151:
            cluster_name = "IISNL151"
        else:
            cluster_name = "RET" + country_code + site_code
        if self.get_cluster(cluster_name):
            return True
        return False

    def vmoperator(self, cluster_name, vm_status):      # noqa
        i = 0
        responds = {}
        vms = self.get_vm(clustername=cluster_name, include_template=False)
        # dashosts=models.GDH_SLIHOSTS.objects.filter(cluster=cluster_name).values()[0]
        # masterovc_name  =   dashosts['masterovc']
        # slaveovc_name   =   dashosts['slaveovc']
        cluster_info = ModelSLICluster.query.filter_by(name=cluster_name).first()
        masterovc_name = cluster_info.master_ovc
        slaveovc_name = cluster_info.slave_ovc
        masterovc = self.get_vm(vmname=masterovc_name, clustername=cluster_name)[0]
        slaveovc = self.get_vm(vmname=slaveovc_name, clustername=cluster_name)[0]
        vms.remove(masterovc)
        vms.remove(slaveovc)
        if vm_status == "off":
            # skippedvms=[responds.update({vm.name : "VM has been off, Skipped"}) for vm in vms if vm.runtime.powerState == "poweredOff"]    # noqa
            skippedvms = ";".join([vm.name for vm in vms if vm.runtime.powerState == "poweredOff"])
            self.l_g.write_pm_log(loginfo=f"Skipped VMs list : {skippedvms}  ", taskid=self.taskid, logseverity='info')
            self.logger.info(f"Skipped VMs list : {skippedvms} ")
            vms_on = ";".join([vm.name for vm in vms if vm.runtime.powerState == "poweredOn"])
            self.logger.info(f"Online VMs list : {vms_on} ")
            self.l_g.write_pm_log(loginfo=f"Online VMs list : {vms_on} ", taskid=self.taskid, logseverity='info')
            responds.update({vms_on: "Shutting down in progress ..."})
            for vm in [vm for vm in vms if vm.runtime.powerState == "poweredOn"]:
                try:
                    self.l_g.write_pm_log(loginfo=f"{vm} is shutting down...", taskid=self.taskid, logseverity='info')
                    self.logger.info(f"{vm} is shutting down...")
                    vm.ShutdownGuest()
                except BaseException:
                    self.l_g.write_pm_log(loginfo=f"{vm} is shutting down...", taskid=self.taskid, logseverity='info')
                    self.logger.info(f"{vm} is shutting down by force...")
                    vm.PowerOff()
            try:
                # states=[vm.ShutdownGuest() for vm in vms if vm.runtime.powerState == "poweredOn"]
                time.sleep(10)
                while i <= 5:
                    vmson_status = [vm.name for vm in vms if vm.runtime.powerState == "poweredOn"]
                    if vmson_status:
                        self.l_g.write_pm_log(
                            loginfo=f'VMs on:{";".join(vmson_status)}; Powering off is still in progress',
                            taskid=self.taskid, logseverity='info')
                        self.logger.info(f'VMs :{";".join(vmson_status)}; Powering off is still in progress')
                        time.sleep(10)
                        i += 1
                    else:
                        break
                vmson_status = [vm.name for vm in vms if vm.runtime.powerState == "poweredOn"]
                if vmson_status:
                    self.logger.info(f'VMs :{";".join(vmson_status)}powering off is still in progress')
                    self.l_g.write_pm_log(loginfo=f'VMs :{";".join(vmson_status)} powering off is still in progress',
                                          taskid=self.taskid, logseverity='info')
                    self.l_g.write_pm_log(loginfo=f'Powering off the VMs by force : {";".join(vmson_status)}',
                                          taskid=self.taskid, logseverity='info')
                    self.logger.info(f'Powering off the VMs by force : {";".join(vmson_status)}')
                    [vm.PowerOff() for vm in vms if vm.runtime.powerState == "poweredOn"]
                    time.sleep(10)
                return True
            except BaseException as e:
                self.l_g.write_pm_log(loginfo=f"{e}", taskid=self.taskid, logseverity='info')
                self.logger.info(f"{e} ")
                return False
        if vm_status == "on":
            vms_off = ";".join([vm.name for vm in vms if vm.runtime.powerState == "poweredOff"])
            [vm.PowerOn() for vm in vms if vm.runtime.powerState == "poweredOff"]
            self.l_g.write_pm_log(loginfo=f'VMs :{vms_off} :powering on in progress', taskid=self.taskid,
                                  logseverity='info')
            self.logger.info(f'VMs :{vms_off} :powering on in progress')
            time.sleep(10)
            while i <= 5:
                vmson_status = [vm.name for vm in vms if vm.runtime.powerState == "poweredOff"]
                if vmson_status:
                    self.l_g.write_pm_log(loginfo=f'VMs :{";".join(vmson_status)}powering on is still in progress',
                                          taskid=self.taskid, logseverity='info')
                    self.logger.info(f'VMs :{";".join(vmson_status)}powering on is still in progress')
                    time.sleep(10)
                    i += 1
                else:
                    self.l_g.write_pm_log(loginfo='Successfully powered on all VMs !', taskid=self.taskid,
                                          logseverity='info')
                    self.logger.info('Successfully powered on all VMs !')
                    break
            if i == 6:
                vmson_status = [vm.name for vm in vms if vm.runtime.powerState == "poweredOff"]
                self.l_g.write_pm_log(loginfo=f'VMs :{";".join(vmson_status)} still off after multiple try.', taskid=self.taskid, logseverity='info')           
                self.logger.info(f'Error during the VMs up, please contact administrator. VMs :{";".join(vmson_status)} still off after multiple try.')
                return False
            return True

    def hosts_status_verify(self, cluster_name):
        try:
            i = 0
            hosts = self.get_cluster(clustername=cluster_name)[0].host
            ilo_host0 = "ilo" + hosts[0].name
            ilo_host1 = "ilo" + hosts[1].name
            while i < 30:
                host0_status = hosts[0].runtime.connectionState
                host1_status = hosts[1].runtime.connectionState
                ilo_host0_connection = self.ilo_connection(vmname=ilo_host0)
                ilo_host1_connection = self.ilo_connection(vmname=ilo_host1)
                if ilo_host1_connection and ilo_host0_connection:
                    self.logger.info("ILOs on Both Hosts are reachable, Starting the PM_Startup...")
                    self.l_g.write_pm_log(loginfo='ILOs on Both Hosts are reachable, Starting the PM_Startup...',
                                          taskid=self.taskid, logseverity='info')
                    break
                elif host0_status == 'connected' and host1_status == 'connected':
                    self.logger.info(
                        "ILOs unreachable,Both Hosts are reachable, Skip the ILOs and Continue the PM_Startup steps...")    # noqa
                    self.l_g.write_pm_log(
                        loginfo='ILOs unreachable,Both Hosts are reachable, Skip the ILOs and Continue the PM_Startup steps...',    # noqa
                        taskid=self.taskid, logseverity='error')
                    break
                else:
                    self.logger.info(
                        "ILOs unreachable, Hosts unreachable, please try to contact LIT to power on with power button. Will check again 20 Secs...")    # noqa
                    self.l_g.write_pm_log(
                        loginfo='ILOs unreachable, Hosts unreachable, please try to contact LIT to power on with power button. Will check again 20 Secs...',    # noqa
                        taskid=self.taskid, logseverity='error')
                    time.sleep(20)
                    i += 1
            if i == 30:
                self.logger.info(
                    "8 mins passed, ILOs unreachable,Both Hosts unreachable, Please contact the LIT to power on the host manually or check the network...")    # noqa
                self.l_g.write_pm_log(
                    loginfo='8 mins passed, ILOs unreachable,Both Hosts unreachable, Please contact the LIT to power on the host manually or check the network...',    # noqa
                    taskid=self.taskid, logseverity='error')
                return False
            return True
        except BaseException as e:
            self.l_g.write_pm_log(loginfo=f"{e}", taskid=self.taskid, logseverity='info')
            self.logger.info(f"{e} ")
            return False

    def hosts_off(self, cluster_name):
        try:
            i = 0
            hostson_list = []
            hosts = self.get_cluster(clustername=cluster_name)[0].host
            host0_status = hosts[0].runtime.connectionState
            host1_status = hosts[1].runtime.connectionState
            [hostson_list.append(host) for host in hosts if host.runtime.connectionState == "connected"]
            if hostson_list:
                # mhost=vc.name_filter(hosts,masterhost)[0]
                # shost=vc.name_filter(hosts,slavehost)[0]
                # mhost.Shutdown(force=1)#1 for true
                [host.Shutdown(force=1) for host in hostson_list]
                time.sleep(20)
                while i <= 20:
                    host0_status = hosts[0].runtime.connectionState
                    host1_status = hosts[1].runtime.connectionState
                    self.l_g.write_pm_log(
                        loginfo=f'{hosts[0].name} is {host0_status}; {hosts[1].name} is {host1_status}',
                        taskid=self.taskid, logseverity='info')
                    self.logger.info(f'{hosts[0].name} is {host0_status}; {hosts[1].name} is {host1_status}')
                    if host0_status == 'notResponding' and host1_status == 'notResponding':
                        self.logger.info("Both hosts are powered off successfully")
                        self.l_g.write_pm_log(loginfo='Both hosts are powered off successfully', taskid=self.taskid,
                                              logseverity='info')
                        break
                    self.logger.info("Power off task is still in progress, check again in 20 sec...")
                    self.l_g.write_pm_log(loginfo='Power off task is still in progress, check again in 20 sec...',
                                          taskid=self.taskid, logseverity='info')
                    time.sleep(20)
                    i += 1
                if i == 21:
                    self.logger.info("The hosts are still on in 8 mins, please check it manually!")
                    self.l_g.write_pm_log(loginfo='The hosts are still on in 8 mins, please check it manually!',
                                          taskid=self.taskid, logseverity='error')
                self.logger.info("The hosts are Powered Off successfully in {str(round(i * 20 / 60, 2))} mins!")
                self.l_g.write_pm_log(
                    loginfo=f'The hosts are Powered Off successfully in {str(round(i * 20 / 60, 2))} mins!',
                    taskid=self.taskid, logseverity='info')
                # return True
            else:
                self.logger.info("The hosts are Powered Off successfully in {str(round(i * 20 / 60, 2))} mins!")
                self.l_g.write_pm_log(
                    loginfo=f'The hosts are Powered Off successfully in {str(round(i * 20 / 60, 2))} mins!',
                    taskid=self.taskid, logseverity='info')
            return True
        except BaseException as e:
            self.l_g.write_pm_log(loginfo=f"{e}", taskid=self.taskid, logseverity='info')
            self.logger.info(f"{e} ")
            return False

    def hosts_on(self, cluster_name, ilo_user, ilo_password):
        try:
            i = 0
            hosts = self.get_cluster(clustername=cluster_name)[0].host
            host0_status = hosts[0].runtime.connectionState
            host1_status = hosts[1].runtime.connectionState
            if host0_status == 'connected' and host1_status == 'connected':
                self.logger.info("Both Hosts are reachable, Continue the PM_Startup steps...")
                self.l_g.write_pm_log(loginfo='Both Hosts are reachable, Continue the PM_Startup steps...',
                                      taskid=self.taskid, logseverity='error')
                return True
            ilo_addresses = ["ilo" + host.name for host in hosts]
            [self.change_ilohoststate(iloaddress=Ilo_Addreess, resettye="On", ilo_user=ilo_user,
                                      ilo_password=ilo_password) for Ilo_Addreess in ilo_addresses]
            time.sleep(10)
            while i <= 20:
                host0_status = hosts[0].runtime.connectionState
                host1_status = hosts[1].runtime.connectionState
                self.l_g.write_pm_log(loginfo=f'{hosts[0].name} is {host0_status}; {hosts[1].name} is {host1_status}',
                                      taskid=self.taskid, logseverity='info')
                self.logger.info(f'{hosts[0].name} is {host0_status}; {hosts[1].name} is {host1_status}')
                if host0_status == 'connected' and host1_status == 'connected':
                    self.logger.info("Both hosts are Powered On successfully")
                    # self.l_g.write_pm_log(loginfo=f'Both hosts are Powered On successfully',taskid = self.taskid ,logseverity='info')    # noqa
                    # print("Both hosts are Powered On successfully")
                    break
                self.logger.info("Powering On task is still in progress, will check again in 20 sec...")
                self.l_g.write_pm_log(loginfo='Powering On task is still in progress, will check again in 20 sec...',
                                      taskid=self.taskid, logseverity='info')
                time.sleep(20)
                i += 1
            if i == 21:
                self.logger.info("The hosts are still off in 8 mins, please check it manually!")
                self.l_g.write_pm_log(loginfo='The hosts are still off in 8 mins, please check it manually!',
                                      taskid=self.taskid, logseverity='error')
            self.logger.info(f"The hosts are Powered On successfully in {str(round(i * 20 / 60, 2))} mins!")
            self.l_g.write_pm_log(
                loginfo=f'The hosts are Powered On successfully in {str(round(i * 20 / 60, 2))} mins!',
                taskid=self.taskid, logseverity='info')
            return True
        except BaseException as e:
            self.l_g.write_pm_log(loginfo=f"{e}", taskid=self.taskid, logseverity='info')
            self.logger.info(f"{e} ")
            return False


class Vcenter():
    def __init__(self, vc, sa=None, logger=logging, domain='ikea.com'):
        if not sa:
            _sa = ServiceAccount(usage="simplivity_pm")
            self.sa = _sa.get_service_account()
        else:
            self.sa = sa  # eg: {"username":'abc', "password":'secret'}
        self.vc = vc
        self.logger = logger
        _pat = re.compile(r'\.(ikea|ikeadt)\.com', re.IGNORECASE)  # match ikea.com / ikeadt.com ,ingore case
        try:
            self.domain = re.search(_pat, self.vc).group()
        except Exception:
            self.domain = domain

    def get_vc_list_from_db(self):
        vcschema = ModelSLIvCenterSchema(many=True)
        vc_list = ModelSLIvCenter.query.all()
        print(vc_list)
        result = vcschema.dump(vc_list)
        return jsonify(result)


class VcCluster():
    def __init__(self, cluster, sa=None, logger=logging) -> None:
        # pe should always be like "RETCN888-NXC000.ikea.com"
        rep = re.compile(re.escape('.ikea.com'), re.IGNORECASE)
        if re.search(rep, cluster):  # pe is like an fqdn  :"RETCN888-NXC000.ikea.com"
            self.cluster_fqdn = cluster
            self.cluster = rep.sub("", self.pe_fqdn)
        else:  # pe is not like an fqdn: "RETCN888-NXC000"
            self.cluster = cluster
            self.cluster_fqdn = f"{self.cluster}.ikea.com"
        self.logger = logger
        if not sa:
            _sa = ServiceAccount(usage="nutanix_pm")
            self.sa = _sa.get_service_account()
        else:
            self.sa = sa

    def get_cluster_list_from_db(self):
        # clusterschema = ModelSLIClusterSchema(many=True)
        cluster_list = ModelSLICluster.query.filter_by(status="Y")
        _res = []
        _clusterschema = ModelSLIClusterSchema()
        for cluster in cluster_list:
            _cluster = _clusterschema.dump(cluster)
            vm_counts = len(ModelSLIVMs.query.filter_by(cluster_id=cluster.id).order_by(ModelSLIVMs.id.desc()).all())
            host_counts = len(
                ModelSLIHost.query.filter_by(cluster_id=cluster.id).order_by(ModelSLIHost.id.desc()).all())
            if vm_counts:
                _cluster['vm_counts'] = vm_counts  # type: ignore
                _cluster['host_counts'] = host_counts  # type: ignore
            else:
                _cluster['vm_counts'] = 'None'  # type: ignore
                _cluster['host_counts'] = {'severity': 'info', 'loginfo': 'No VMs matched'}  # type: ignore
            _res.append(_cluster)
        result = _res
        return jsonify(result)

    def get_vc_cluster_correspondence_from_db(self):
        cluster_list = ModelSLICluster.query.order_by('vc_fqdn').all()
        from itertools import groupby
        from operator import attrgetter
        cluster_list = groupby(cluster_list, attrgetter('vc_fqdn'))
        correspondence = [{"vc": key.upper(), "cluster": [ v.name.upper() for v in value if v.status == 'Y']} for key, value in cluster_list]
        return jsonify(correspondence)


class SliDetaillist:
    def get_all_host_info_from_db(self):
        return ModelSLIHostSchema(many=True).dump(ModelSLIHost.query.filter_by(status="Y"))

    def get_all_slivms_info_from_db(self):
        return ModelSLIVMsSchema(many=True).dump(ModelSLIVMs.query.filter_by(status="Y"))
    ########asdfadfdfasdg test#############

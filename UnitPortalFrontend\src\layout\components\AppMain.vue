<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
import {WebPageTracer} from '@/api/upwidgets'
export default {
  name: 'AppMain',
  data() {
    return {
      webtracerres : '',
      pagepath : ''
    }
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      console.log(this.$route)
      console.log('key', this.$route.path)
      this.pagepath = this.$route.path
      return this.$route.path
    },
  },
  watch: {
    pagepath: function (val) {
      let payload = {
        token: this.$store.getters.token,
        data:{
          pagename: this.$route.name
        }
      }
      WebPageTracer(payload).then(response => { 
        this.webtracerres = response.data
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header+.app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header+.app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>

#cloud-config

# Set hostname and FQDN
hostname: @@{VM_Name}@@
fqdn: @@{VM_Fqdn}@@
manage_etc_hosts: false
ssh_pwauth: true
write_files:
  - path: /etc/sysconfig/network
    content: |
      NETWORKING=yes
      NETWORKING_IPV6=no

runcmd:
  - for i in $(nmcli -g uuid con show); do nmcli con delete $i; done
  - nmcli con add con-name ens3 ifname ens3 type ethernet ipv4.method manual ipv4.address @@{VM_IP}@@/@@{VM_SubnetSize}@@
  - nmcli con mod ens3 ipv4.gateway @@{VM_GW}@@ autoconnect yes
  - nmcli con mod ens3 ipv4.dns "@@{VM_DNS1}@@ @@{VM_DNS2}@@" ipv4.dns-search @@{VM_DnsDomain}@@
  - nmcli con up ens3
  - touch /etc/cloud/cloud-init.disabled
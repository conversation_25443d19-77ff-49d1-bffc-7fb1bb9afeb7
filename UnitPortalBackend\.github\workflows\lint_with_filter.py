import os
import re
import subprocess
import sys
from argparse import Arg<PERSON>nt<PERSON><PERSON><PERSON>


def get_changed_files(base_sha, target_sha):
    """Get all changed Python files in the PR."""
    if not target_sha:
        cmd = ["git", "diff", "--name-only", base_sha, "--", "*.py"]
    else:
        cmd = ["git", "diff", "--name-only", base_sha, target_sha, "--", "*.py"]
    diff_output = subprocess.check_output(cmd).decode("utf-8")
    return [f.strip() for f in diff_output.splitlines() if f.strip()]


def get_changed_lines(base_sha, target_sha, file_path):
    """Get line numbers that were changed in the PR for a specific file."""
    if not target_sha:
        cmd = ["git", "diff", "-U0", base_sha, "--", file_path]
    else:
        cmd = ["git", "diff", "-U0", base_sha, target_sha, "--", file_path]
    diff_output = subprocess.check_output(cmd).decode("utf-8")

    changed_lines = set()
    for line in diff_output.splitlines():
        match = re.match(r"^@@ -\d+(?:,\d+)? \+(\d+)(?:,(\d+))? @@", line)
        if match:
            start_line = int(match.group(1))
            line_count = int(match.group(2)) if match.group(2) else 1
            changed_lines.update(range(start_line, start_line + line_count))

    return changed_lines


def check_pylint(base_sha, target_sha, files):
    """Run pylint on changed lines."""
    issues_in_changed_lines = []
    all_pylint_output = []
    exit_code = 0

    for file_path in files:
        if not os.path.exists(file_path):
            continue

        changed_lines = get_changed_lines(base_sha, target_sha, file_path)
        if not changed_lines:
            continue

        cmd = ["pylint", "--rcfile=.pylintrc", file_path]
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)

        file_output = result.stdout.splitlines()
        all_pylint_output.extend(file_output)

        if result.returncode != 0:
            for line in file_output:
                if "W0611:" in line:
                    issues_in_changed_lines.append(line)
                    continue

                match = re.match(r".*:(\d+):\d+:", line)
                if match:
                    line_num = int(match.group(1))
                    if line_num in changed_lines:
                        issues_in_changed_lines.append(line)

    if all_pylint_output:
        print("Full pylint output:")
        for line in all_pylint_output:
            print(line)

    if issues_in_changed_lines:
        print("\nPylint issues in changed lines:")
        for line in issues_in_changed_lines:
            print("!> " + line)
        exit_code = 1
    else:
        print("\nNo pylint issues found in changed lines.")

    return exit_code


def check_flake8(base_sha, target_sha, files):
    """Run flake8 on changed lines."""
    issues_in_changed_lines = []
    all_flake8_output = []
    exit_code = 0

    for file_path in files:
        if not os.path.exists(file_path):
            continue

        changed_lines = get_changed_lines(base_sha, target_sha, file_path)
        if not changed_lines:
            continue

        # Run flake8 on the entire file
        cmd = ["flake8", "--config=.flake8", file_path]
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)

        # Get all flake8 output for this file
        file_output = result.stdout.splitlines()
        all_flake8_output.extend(file_output)

        # Filter output to only include issues in changed lines
        if result.returncode != 0:
            for line in file_output:
                match = re.match(r".*:(\d+):\d+:", line)
                if match:
                    line_num = int(match.group(1))
                    if line_num in changed_lines:
                        issues_in_changed_lines.append(line)

    if all_flake8_output:
        print("Full flake8 output:")
        for line in all_flake8_output:
            print(line)

    if issues_in_changed_lines:
        print("\nFlake8 issues in changed lines:")
        for line in issues_in_changed_lines:
            print("!> " + line)
        exit_code = 1
    else:
        print("\nNo flake8 issues found in changed lines.")

    return exit_code


def main():
    argparse = ArgumentParser()
    # function: pylint or flake8
    argparse.add_argument("-f", "--function", choices=["pylint", "flake8"])
    argparse.add_argument("-b", "--base-sha", type=str)
    argparse.add_argument("-t", "--target-sha", default=None, type=str)
    argparse.add_argument("files", type=str, nargs="*")
    args = argparse.parse_args()

    func = args.function
    base_sha = args.base_sha
    target_sha = args.target_sha
    files = args.files if args.files else get_changed_files(base_sha, target_sha)

    if not files:
        print("No Python files changed in this PR")
        sys.exit(0)

    if func == "pylint":
        exit_code = check_pylint(base_sha, target_sha, files)
    elif func == "flake8":
        exit_code = check_flake8(base_sha, target_sha, files)
    else:
        print(f"Unknown function: {func}. Must be either 'pylint' or 'flake8'")
        sys.exit(1)

    sys.exit(exit_code)


if __name__ == "__main__":
    main()

import os
import threading
from collections import OrderedDict, deque
from datetime import datetime, timedelta, time
import apscheduler
import sqlalchemy
from apscheduler.schedulers.background import BackgroundScheduler
from dateutil import tz
from pytz import timezone
from sqlalchemy import and_

from base_path import application_path
from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.automation.automation_exception import \
    SeamlessLcmClusterHasExistingPlanIn24Hrs, SeamlessLcmClusterConflict, GenerateLcmPlanFailed
from business.distributedhosting.nutanix.automation.lcm import LCM, AosLCM
from business.distributedhosting.nutanix.automation.seamless_lcm_specs import SeamlessLcmSpec
from business.distributedhosting.nutanix.base_up_task_property import BaseUpTaskProperty
from business.distributedhosting.nutanix.task_status import SeamlessLcmPlannedPEStatus, TaskStatus, \
    SeamlessLcmPlanStatus
from business.distributedhosting.scheduler.seamless_lcm_scheduler import seamless_lcm_scheduler
from business.generic.commonfunc import get_request_token, get_user_by_token, setup_common_logger
from models.atm_models import ModelNutanixSeamlessLcmPlans, ModelNutanixSeamlessLcmPlannedPEs, \
    ModelNutanixSeamlessLcmPlannedPEsSchema, ModelNutanixSeamlessLcmPlansSchema
from models.database import db
from models.ntx_models import ModelPrismElement
from models.ntx_models_wh import ModelWarehousePrismElement
from collector.collectors.modules.api_calls import with_app_context


class SeamlessLcm:
    LOG_DIR = os.path.join(application_path, "Log", "automation", "SEAMLESS_LCM")
    LOG_PATH = os.path.join(LOG_DIR, "slcm.log")

    def __init__(self):
        self.logger = setup_common_logger("SEAMLESS_LCM", self.LOG_PATH)
        self.user = get_user_by_token().username
        self.facility_type = None
        self.pe_model = None

    def create_plan(self, payload):
        self.logger.info(f"User {self.user} is creating Seamless LCM plan...")
        self.logger.info(f"Payload: {payload}")
        self.facility_type = payload.get(BaseUpTaskProperty.FACILITY_TYPE)
        self.pe_model = ModelPrismElement if self.facility_type == FacilityType.RETAIL else ModelWarehousePrismElement
        plans = self.generate_lcm_plan(payload, self.pe_model)
        response = self.register_lcm_task(plans, payload)
        if payload.get(SeamlessLcmSpec.DAILY_REPORT):       # TODO
            self.prepare_daily_report()
        self.logger.info("Plan created successfully.")
        return response

    def _save_plan_into_db(self, payload, plan_id=None):
        plan_params = {
            BaseUpTaskProperty.CREATE_DATE: datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S"),
            BaseUpTaskProperty.CREATOR: self.user,
            BaseUpTaskProperty.FACILITY_TYPE: payload.get(BaseUpTaskProperty.FACILITY_TYPE),
            SeamlessLcmSpec.DAILY_REPORT: payload[SeamlessLcmSpec.DAILY_REPORT],
            SeamlessLcmSpec.EXECUTION_SEQUENCE: payload[SeamlessLcmSpec.EXECUTION_SEQUENCE],
            SeamlessLcmSpec.INTERVAL: payload[SeamlessLcmSpec.INTERVAL],
            SeamlessLcmSpec.LCM_TYPE: payload[SeamlessLcmSpec.LCM_TYPE],
            SeamlessLcmSpec.MAX_COUNT: payload[SeamlessLcmSpec.MAX_COUNT],
            SeamlessLcmSpec.DESIRED_PLAN_DATE: payload[SeamlessLcmSpec.DESIRED_PLAN_DATE],
            BaseUpTaskProperty.STATUS: SeamlessLcmPlanStatus.PLANNED,
        }
        if plan_id:
            ModelNutanixSeamlessLcmPlans.query.filter_by(id=plan_id).update(plan_params)
            db.session.commit()
        else:
            new_plan = ModelNutanixSeamlessLcmPlans(**plan_params)
            db.session.add(new_plan)
            db.session.commit()
            plan_id = new_plan.id
        return plan_id

    def generate_lcm_plan(self, payload, pe_model):
        plans = {}
        for pc, _ in payload.get(SeamlessLcmSpec.CLUSTER_LIST).items():
            if payload.get(SeamlessLcmSpec.EXECUTION_SEQUENCE) == "COUNTRY":
                _ = OrderedDict(_)
            for country_code, pe_fqdn_list in _.items():
                for pe_fqdn in pe_fqdn_list:
                    try:
                        pe_timezone = pe_model.query.filter_by(fqdn=pe_fqdn).one().timezone
                    except sqlalchemy.exc.NoResultFound:
                        self.logger.error(f"Can't find PE '{pe_fqdn}' in database, skip this one.")
                        continue
                    if not plans.get(pc):
                        plans[pc] = [{
                            BaseUpTaskProperty.PE_FQDN: pe_fqdn,
                            'country': country_code,
                            'timezone': pe_timezone,
                        }]
                    else:
                        plans[pc].append({
                            BaseUpTaskProperty.PE_FQDN: pe_fqdn,
                            'country': country_code,
                            'timezone': pe_timezone,
                        })
        """
        plans = {
            <pc1>: [
                {
                    "pe_fqdn": "",
                    "country": "",
                    "timezone": "",
                },
                ...
            ],
            <pc2>: []...
        }
        """
        return plans

    def register_lcm_task(self, plans_list, payload, plan_id=None):
        max_count = payload.get(SeamlessLcmSpec.MAX_COUNT)

        if not seamless_lcm_scheduler.running:
            seamless_lcm_scheduler.start()
        # Check all planned jobs before register schedules, instead of try-excepting ConflictingIdError,
        # in order to print all conflict jobs in response at once
        original_start_date = self._get_original_start_date(payload.get(SeamlessLcmSpec.DESIRED_PLAN_DATE))

        planned_pes = []
        for pc, plans in plans_list.items():
            # Different PCs start from same time, so reset start time to 00:00 for each PC
            # comment out when test
            start_time = original_start_date
            # start_time = datetime.now() + timedelta(minutes=1)
            # comment out when test
            planned_count = 0
            self.check_existing_jobs(payload.get(SeamlessLcmSpec.LCM_TYPE), plans)
            plan_id = self._save_plan_into_db(payload, plan_id)
            plans = deque(plans)
            att = 0
            while plans:
                att += 1
                plan = plans.popleft()
                self.logger.info(f"Generating plan for {plan}... Attempt: {att}")
                # comment out when test
                # Check if start_time(UTC) is in the time range(23:00-4:00) of PE's timezone
                if not self._is_time_in_time_range(
                    start_time, plan['timezone'], time(23, 0, 0), time(4, 0, 0)
                ):
                    if plan.get('refind'):
                        start_time += timedelta(minutes=payload.get(SeamlessLcmSpec.INTERVAL))
                    plan['refind'] = True
                    plans.append(plan)
                    continue
                is_time_slot_occupied = self.is_time_slot_occupied(start_time, payload.get(SeamlessLcmSpec.INTERVAL), pc)
                if is_time_slot_occupied:
                    self.logger.warning(f"Time slot {start_time} is occupied, skipping...")
                    if plan.get('refind'):
                        start_time += timedelta(minutes=payload.get(SeamlessLcmSpec.INTERVAL))
                    plan['refind'] = True
                    plans.append(plan)
                    continue
                # comment out when test
                start_time = start_time.replace(microsecond=0)

                params = {
                    "country": plan['country'],
                    BaseUpTaskProperty.PC: pc,
                    BaseUpTaskProperty.PE_FQDN: plan[BaseUpTaskProperty.PE_FQDN],
                    SeamlessLcmSpec.PLANNED_DATE: start_time,
                    BaseUpTaskProperty.FACILITY_TYPE: payload.get(BaseUpTaskProperty.FACILITY_TYPE),
                    BaseUpTaskProperty.STATUS: SeamlessLcmPlannedPEStatus.PLANNED,
                    SeamlessLcmSpec.LCM_TYPE: payload.get(SeamlessLcmSpec.LCM_TYPE),
                    SeamlessLcmSpec.PLAN_ID: plan_id,
                }
                planned_pes.append(params)

                start_time += timedelta(minutes=payload.get(SeamlessLcmSpec.INTERVAL))
                planned_count += 1
                if planned_count >= max_count:
                    start_time = (start_time + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
                    planned_count = 0  # TODO: 0 or 1?
            if plans:
                # Still left in the queue, means not all jobs are scheduled
                raise GenerateLcmPlanFailed(plans)

        thread_pool = []
        for pe in planned_pes:
            t = threading.Thread(
                target=self._register_lcm_job,
                kwargs={
                    SeamlessLcmSpec.LCM_TYPE: payload.get(SeamlessLcmSpec.LCM_TYPE),
                    BaseUpTaskProperty.PE_FQDN: pe[BaseUpTaskProperty.PE_FQDN],
                    BaseUpTaskProperty.PC: pe[BaseUpTaskProperty.PC],
                    BaseUpTaskProperty.FACILITY_TYPE: pe[BaseUpTaskProperty.FACILITY_TYPE],
                    'start_time': pe[SeamlessLcmSpec.PLANNED_DATE],
                    'creator': self.user,
                    'slcm_plan_id': pe[SeamlessLcmSpec.PLAN_ID]
                }
            )
            t.start()
            thread_pool.append(t)
        for thread in thread_pool:
            thread.join()

        db.session.execute(ModelNutanixSeamlessLcmPlannedPEs.__table__.insert(), planned_pes)
        db.session.commit()

    @with_app_context
    def _register_lcm_job(self, lcm_type, pe_fqdn, pc, facility_type, start_time: datetime, creator, slcm_plan_id):
        self.logger.info("Registering LCM job...")
        self.logger.info(f"PE: {pe_fqdn}, LCM type: {lcm_type}, Run Date: {start_time}(UTC)")
        match lcm_type:
            case "AOS":
                job = seamless_lcm_scheduler.add_job(
                    id=f"{pe_fqdn}_aos",
                    func=AosLCM(
                        pc=pc,
                        pe=pe_fqdn,
                        facility_type=facility_type,
                        slcm_plan_id=slcm_plan_id
                    ).run,
                    kwargs={
                        'param': {
                            BaseUpTaskProperty.PC: pc,
                            BaseUpTaskProperty.PE: pe_fqdn,   # Note: in AosLCM, pe is pe_fqdn
                            BaseUpTaskProperty.CREATOR: creator,
                            BaseUpTaskProperty.FACILITY_TYPE: facility_type,
                            'slcm_plan_id': slcm_plan_id
                        }
                    },
                    trigger='date',
                    run_date=start_time,
                    jobstore=SeamlessLcmSpec.JOB_STORE
                )
            case "SPP":
                # TODO: implement SPP
                lcm = LCM(
                    pc=pc,
                    pe=pe_fqdn,    # Note: in class LCM, pe is pe_fqdn
                    token=get_request_token(),
                    facility_type=self.facility_type,
                    slcm_plan_id=slcm_plan_id
                )
                job = seamless_lcm_scheduler.add_job(
                    id=f"{pe_fqdn}_spp",
                    func=lcm.create_task,
                    trigger='date',
                    run_date=start_time,
                    jobstore=SeamlessLcmSpec.JOB_STORE
                )
        self.logger.info("LCM job registered successfully.")
        return job

    def prepare_daily_report(self):
        return  # TODO
        scheduler = BackgroundScheduler()
        # scheduler.add_job(id='Scheduled Task', func=ScanPMTask, trigger="interval", seconds=60,
        #   next_run_time=datetime.now())
        scheduler.add_job(
            id='Seamless LCM daily report',
            func=self.send_daily_report,
            trigger="cron",
            day='*', hour=0, minutes=0, second=0
        )

    def send_daily_report(self):
        # TODO
        pass

    def check_existing_jobs(self, lcm_type, plans):
        existing_clusters = []
        for plan in plans:
            existing_job = seamless_lcm_scheduler.get_job(
                f"{plan['pe_fqdn']}_{lcm_type.lower()}", jobstore=SeamlessLcmSpec.JOB_STORE
            )
            if existing_job:
                existing_clusters.append({"cluster": plan['pe_fqdn'], "lcm_type": lcm_type})
        if existing_clusters:
            raise SeamlessLcmClusterConflict(existing_clusters)

    def is_time_slot_occupied(self, planned_datetime, interval, pc):
        # check if the time slot (+- interval) is occupied by any existing job (in same PC)
        results = db.session.query(ModelNutanixSeamlessLcmPlannedPEs).filter(
            and_(
                planned_datetime - timedelta(minutes=interval) < ModelNutanixSeamlessLcmPlannedPEs.planned_date,
                planned_datetime + timedelta(minutes=interval) > ModelNutanixSeamlessLcmPlannedPEs.planned_date,
                ModelNutanixSeamlessLcmPlannedPEs.status == SeamlessLcmPlannedPEStatus.PLANNED,
                ModelNutanixSeamlessLcmPlannedPEs.pc == pc
            )
        ).all()
        return len(results) > 0

    def check_existing_aos_spp_jobs(self, plans, start_time):
        conflict_plans = []
        # TODO: not in use
        job_id_to_check = None
        for plan in plans:
            existing_job = seamless_lcm_scheduler.get_job(job_id_to_check, jobstore=SeamlessLcmSpec.JOB_STORE)
            next_run_time = existing_job.next_run_time.astimezone(timezone('UTC'))
            if abs(next_run_time - start_time) <= timedelta(days=1):
                conflict_plans.append({
                    'cluster': plan['pe'],
                    'existing_plan': next_run_time.strftime('%Y-%m-%d %H:%M:%S %Z%z'),
                    'new_plan': start_time.strftime('%Y-%m-%d %H:%M:%S %Z%z')
                })
        if conflict_plans:
            raise SeamlessLcmClusterHasExistingPlanIn24Hrs(conflict_plans)

    def cancel_plan(self, plan_id):
        """
        pe status "planned" -> canceled; other status: keep it
        plan status -> "canceled"
        """
        self.logger.info(f"User {self.user} is canceling seamless LCM plan {plan_id}...")
        updated_rows = self._update_planned_pes_status(plan_id, SeamlessLcmPlanStatus.CANCELED)
        job_ids = [f"{row.pe_fqdn}_{row.lcm_type.lower()}" for row in updated_rows]
        self.remove_jobs_from_scheduler(job_ids)
        ModelNutanixSeamlessLcmPlans.query.filter_by(id=plan_id).update(
            {BaseUpTaskProperty.STATUS: SeamlessLcmPlanStatus.CANCELED}
        )
        db.session.commit()
        self.logger.info(f"Seamless LCM plan '{plan_id}' canceled successfully.")
        return '', 204

    def _update_planned_pes_status(self, plan_id, target_status):
        # Note: Update pe status in "planned" to target_status
        rows = ModelNutanixSeamlessLcmPlannedPEs.query.filter_by(
            plan_id=plan_id,
            status=SeamlessLcmPlannedPEStatus.PLANNED
        ).all()     # TODO: The query is only to return value, maybe it can improve
        ModelNutanixSeamlessLcmPlannedPEs.query.filter_by(
            plan_id=plan_id,
            status=SeamlessLcmPlannedPEStatus.PLANNED
        ).update(
            {BaseUpTaskProperty.STATUS: target_status}
        )
        db.session.commit()
        return rows

    @staticmethod
    def list_plans():
        result = []
        plans = ModelNutanixSeamlessLcmPlans.query.order_by(ModelNutanixSeamlessLcmPlans.id).all()
        _plans = ModelNutanixSeamlessLcmPlansSchema(many=True).dump(plans)
        for plan in _plans:
            pes = ModelNutanixSeamlessLcmPlannedPEs.query.filter_by(plan_id=plan['id']).order_by(
                ModelNutanixSeamlessLcmPlannedPEs.id).all()
            _pes = ModelNutanixSeamlessLcmPlannedPEsSchema(many=True).dump(pes)
            plan["pes"] = _pes
            result.append(plan)
            plan[BaseUpTaskProperty.STATUS] = SeamlessLcm.calculate_plan_status(plan)
        return result

    def _is_time_in_time_range(self, dt: datetime, offset_str: str, start_time: time, end_time: time):
        """
        offset_str e.g. "UTC+08:00"
        """
        target_tz = tz.gettz(offset_str)            # TODO: offset string will be replaced by IANA string
        dt_in_target_tz = dt.astimezone(target_tz)
        if start_time <= end_time:      # e.g. 04:00 - 23:00    # pylint: disable=no-else-return
            return start_time <= dt_in_target_tz.time() <= end_time
        else:                           # e.g. 23:00 - 04:00    # pylint: disable=no-else-return
            return start_time <= dt_in_target_tz.time() or dt_in_target_tz.time() <= end_time

    @staticmethod
    def calculate_plan_status(plan):
        pe_statuses = [pe["status"] for pe in plan["pes"]]
        current_plan_status = plan[BaseUpTaskProperty.STATUS]
        desired_plan_status = None
        if current_plan_status == SeamlessLcmPlanStatus.CANCELED:
            desired_plan_status = SeamlessLcmPlanStatus.CANCELED
        elif all(s == SeamlessLcmPlannedPEStatus.PLANNED for s in pe_statuses):
            desired_plan_status = SeamlessLcmPlanStatus.PLANNED
        elif any(s in SeamlessLcmPlannedPEStatus.IN_PROGRESS_STATUSES for s in pe_statuses):
            desired_plan_status = TaskStatus.IN_PROGRESS
        elif any(s in (TaskStatus.ERROR, TaskStatus.DONE, SeamlessLcmPlannedPEStatus.CANCELED) for s in pe_statuses) and \
                any(s == SeamlessLcmPlanStatus.PLANNED for s in pe_statuses):
            desired_plan_status = TaskStatus.IN_PROGRESS
        elif any(s == TaskStatus.ERROR for s in pe_statuses):
            desired_plan_status = SeamlessLcmPlanStatus.COMPLETED       # red completed
        elif all(s in (TaskStatus.DONE, SeamlessLcmPlannedPEStatus.CANCELED) for s in pe_statuses):
            desired_plan_status = SeamlessLcmPlanStatus.COMPLETED       # green completed
        if current_plan_status != desired_plan_status:
            ModelNutanixSeamlessLcmPlans.query.filter_by(id=plan[BaseUpTaskProperty.ID]).update(
                {BaseUpTaskProperty.STATUS: desired_plan_status}
            )
            db.session.commit()
        return desired_plan_status

    def remove_jobs_from_scheduler(self, job_ids):
        def _remove_job_thread(job_id):
            try:
                seamless_lcm_scheduler.remove_job(job_id, jobstore=SeamlessLcmSpec.JOB_STORE)
                self.logger.info(f"Seamless LCM job {job_id} removed from scheduler.")
            except apscheduler.jobstores.base.JobLookupError:
                self.logger.warning(f"Job {job_id} not found in scheduler, maybe already removed.")

        t_pool = []
        for job_id in job_ids:
            t = threading.Thread(target=_remove_job_thread, kwargs={'job_id': job_id})
            t.start()
            t_pool.append(t)
        for t in t_pool:
            t.join()

    def remove_planned_pes_by_id(self, id_list):
        """
        Remove planned PE from database and scheduler
        :param id: ModelNutanixSeamlessLcmPlannedPEs.id
        """
        ModelNutanixSeamlessLcmPlannedPEs.query.filter(ModelNutanixSeamlessLcmPlannedPEs.id.in_(id_list)).delete()
        db.session.commit()

    def _get_original_start_date(self, dt_str):
        original_start_date = datetime.fromisoformat(
            dt_str                                            # desired_plan_date str: '2025-06-12T05:27:15.174Z'
        ).replace(minute=0, second=0, microsecond=0)  # datetime: 2025-06-12T00:00:00.000Z
        today = datetime.now(tz=tz.tzutc())
        if original_start_date < today:
            self.logger.warning(f"User input date ({dt_str}) is in the past, using today as the original start date.")
            original_start_date = today
        return original_start_date


class SeamlessLcmPlan(SeamlessLcm):
    def update_plan(self, plan_id, payload):
        self.logger.info(f"User {self.user} is updating seamless LCM plan {plan_id}...")
        self.logger.info(f"Payload: {payload}")
        executed_rows = ModelNutanixSeamlessLcmPlannedPEs.query.filter(
            ModelNutanixSeamlessLcmPlannedPEs.plan_id == plan_id,
            ModelNutanixSeamlessLcmPlannedPEs.status != SeamlessLcmPlannedPEStatus.PLANNED
        ).all()
        executed_pes = [row.pe_fqdn for row in executed_rows]
        # Cancel PEs planned but not executed yet
        pes_to_remove = ModelNutanixSeamlessLcmPlannedPEs.query.filter(
            ModelNutanixSeamlessLcmPlannedPEs.plan_id == plan_id,
            ModelNutanixSeamlessLcmPlannedPEs.status == SeamlessLcmPlannedPEStatus.PLANNED
        ).all()
        job_ids = [f"{row.pe_fqdn}_{row.lcm_type.lower()}" for row in pes_to_remove]
        self.remove_jobs_from_scheduler(job_ids)
        self.remove_planned_pes_by_id([row.id for row in pes_to_remove])
        new_cluster_list = {}
        for pc, _ in payload.get(SeamlessLcmSpec.CLUSTER_LIST).items():
            new_cluster_list[pc] = {}
            for country_code, pe_fqdn_list in _.items():
                new_cluster_list[pc][country_code] = [pe for pe in pe_fqdn_list if pe not in executed_pes]
        payload[SeamlessLcmSpec.CLUSTER_LIST] = new_cluster_list
        pe_model = ModelPrismElement if payload.get(
            BaseUpTaskProperty.FACILITY_TYPE) == FacilityType.RETAIL else ModelWarehousePrismElement
        plans = self.generate_lcm_plan(payload, pe_model)
        response = self.register_lcm_task(plans, payload, plan_id)
        self.logger.info(f"Seamless LCM plan {plan_id} updated successfully.")
        return response


class SeamlessLcmPlannedPe(SeamlessLcm):

    def list_planned_pes(self):
        self.logger.info("Listing all planned PEs...")
        planned_pes = ModelNutanixSeamlessLcmPlannedPEs.query.all()
        schema = ModelNutanixSeamlessLcmPlannedPEsSchema(many=True)
        response = schema.dump(planned_pes)
        return response

    def update_planned_pe(self, payload):
        self.logger.info(f"User {self.user} is updating planned PE '{payload.get(BaseUpTaskProperty.PE_FQDN)}'")
        self.logger.info(f"Payload: {payload}")
        planned_pe = ModelNutanixSeamlessLcmPlannedPEs.query.filter_by(
            plan_id=payload.get(SeamlessLcmSpec.PLAN_ID),
            pe_fqdn=payload.get(BaseUpTaskProperty.PE_FQDN),
            facility_type=payload.get(BaseUpTaskProperty.FACILITY_TYPE),
            lcm_type=payload.get(SeamlessLcmSpec.LCM_TYPE)
        ).one()
        planned_pe.status = "Planned"
        planned_pe.planned_date = payload[SeamlessLcmSpec.PLANNED_DATE]
        db.session.commit()
        # convert planned_date str to datetime
        pe_model = ModelPrismElement if payload.get(BaseUpTaskProperty.FACILITY_TYPE) == FacilityType.RETAIL else ModelWarehousePrismElement
        target_timezone = tz.tzstr(pe_model.query.filter_by(fqdn=payload.get(BaseUpTaskProperty.PE_FQDN)).one().timezone)
        localized_run_date = datetime.fromisoformat(payload.get(SeamlessLcmSpec.PLANNED_DATE)).astimezone(target_timezone)
        specific_time = datetime.combine(localized_run_date.date(), time(hour=23))  # use 23:00:00 as the updated time
        job_id = f"{planned_pe.pe_fqdn}_{planned_pe.lcm_type.lower()}"
        try:
            seamless_lcm_scheduler.reschedule_job(
                job_id=job_id,
                jobstore=SeamlessLcmSpec.JOB_STORE,
                trigger='date',
                run_date=specific_time,          # TODO: timezone of user input date?
            )
        except apscheduler.jobstores.base.JobLookupError:
            self.logger.warning(f"Job {job_id} not found in scheduler, maybe already removed. Let's register it again.")
            self._register_lcm_job(
                lcm_type=planned_pe.lcm_type,
                pe_fqdn=planned_pe.pe_fqdn,
                pc=planned_pe.pc,
                facility_type=planned_pe.facility_type,
                start_time=specific_time,
                creator=self.user,
                slcm_plan_id=planned_pe.plan_id
            )
        self.logger.info("Scheduled job and planned PE updated.")

    def cancel_planned_pe(self, payload):
        self.logger.info(f"User {self.user} is canceling planned PE '{payload.get(BaseUpTaskProperty.PE_FQDN)}'")
        self.logger.info(f"Payload: {payload}")
        planned_pe = ModelNutanixSeamlessLcmPlannedPEs.query.filter_by(
            plan_id=payload.get(SeamlessLcmSpec.PLAN_ID),
            pe_fqdn=payload.get(BaseUpTaskProperty.PE_FQDN),
            facility_type=payload.get(BaseUpTaskProperty.FACILITY_TYPE),
            lcm_type=payload.get(SeamlessLcmSpec.LCM_TYPE)
        ).one()
        try:
            seamless_lcm_scheduler.remove_job(
                f"{planned_pe.pe_fqdn}_{planned_pe.lcm_type.lower()}", jobstore=SeamlessLcmSpec.JOB_STORE)
        except apscheduler.jobstores.base.JobLookupError:
            self.logger.warning(f"Job {planned_pe.pe_fqdn} not found in scheduler, maybe already removed.")
        planned_pe.status = SeamlessLcmPlannedPEStatus.CANCELED
        db.session.commit()
        self.logger.info("Scheduled job and planned PE canceled.")

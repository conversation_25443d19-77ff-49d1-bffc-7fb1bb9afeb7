from marshmallow import Schema, fields, validate

from models.pm_models import ModelNTXPMLogSchema
from swagger.common_schema import LogSchema

class CreateNtxPmRequestSchema(Schema):
    prism = fields.Str(required=True)
    cluster = fields.Str(required=True)
    startnow = fields.Bool()
    scheduledate = fields.Str()
    pmtype = fields.Str(required=True, validate=validate.OneOf(["poweron", "poweroff"]))
    description = fields.Str(required=True, default='webpage entrance')


class CreateNtxPmResponseSchema(Schema):
    cluster = fields.Str()
    createdate = fields.Str()
    creater = fields.Str()
    createrinfo = fields.Str()
    description = fields.Str()
    detaillogpath = fields.Str()
    id = fields.Int()
    pid = fields.Int()
    pmtype = fields.Str()
    prism = fields.Str()
    startdate = fields.Str()
    status = fields.Str()


class UpdateNtxPmRequestSchema(Schema):
    prism = fields.Str(required=True)
    cluster = fields.Str(required=True)
    startdate = fields.Str()
    id = fields.Int(required=True)
    pmtype = fields.Str(required=True)
    description = fields.Str(required=True)


class AbortNtxPmRequestSchema(Schema):
    cluster = fields.Str()
    createdate = fields.Str()
    creater = fields.Str()
    createrinfo = fields.Str()
    description = fields.Str()
    detaillogpath = fields.Str(required=True)
    id = fields.Int(required=True)
    latestlog = fields.Nested(LogSchema)
    logs = fields.List(fields.Nested(LogSchema))
    pid = fields.Int(required=True)
    pmtype = fields.Str()
    prism = fields.Str()
    startdate = fields.Str()
    status = fields.Str()


class AbortNtxPmResponseSchema(Schema):
    pass


class DeleteNtxPmRequestSchema(Schema):
    id = fields.Int(required=True)


class DeleteNtxPmResponseSchema(Schema):
    # message = fields.Str()
    pass


class GetPmTaskInfoResponseSchema(Schema):
    id = fields.Int()
    prism = fields.Str()
    cluster = fields.Str()
    startdate = fields.DateTime()
    pmtype = fields.Str()
    status = fields.Str()
    creater = fields.Str()
    createdate = fields.DateTime()
    createrinfo = fields.Str()
    description = fields.Str()
    pid = fields.Int()
    detaillogpath = fields.Str()
    logs = fields.List(fields.Nested(ModelNTXPMLogSchema))

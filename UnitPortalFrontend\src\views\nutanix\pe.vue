<template>
  <div class="app-container">
    <div class="filter-container">
      <el-popover placement="right-start" width="200" trigger="click" ref="pop_operations" @show="toggleOperationarrow"
        @hide="toggleOperationarrow">
        <div class="trends_btn_wrap">
          <el-checkbox-group v-model="checkedOptions">
            <el-checkbox style="margin-top: 5px;" v-for="item in oper_headerOptions" :key="item.label" :label="item"
              :value="item.value" :checked="item.checked" class="checkbox-line" @change="handleChecked($event, item)">{{
              item.label }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-popover>
      <el-popover placement="right-start" width="200" trigger="click" ref="pop_column" @show="togglecolumnarrow"
        @hide="togglecolumnarrow">
        <div class="trends_btn_wrap">
          <el-checkbox-group v-model="checkedOptions">
            <el-checkbox style="margin-top: 5px;" v-for="item in col_headerOptions" :key="item.label"
              :label="item.label" :value="item.value" :checked="item.checked" class="checkbox-line"
              @change="handleChecked($event, item)">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-popover>
      <el-row :gutter="5">
        <el-col :span="5" style='float:left;'>
          <el-button-group>
            <el-button v-popover:pop_operations size="small">Operations<i
                :class="handleoperarrow ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i></el-button>
            <el-button v-popover:pop_column size="small">Column <i
                :class="handlecolarrow ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i></el-button>
          </el-button-group>
        </el-col>
        <el-col :span="4" :offset="7">
          <el-select size="large" v-model="filter.selected_pc" multiple collapse-tags placeholder="Filter the PC"
            style="width:100%;">
            <el-option v-for="item in filter.pc_list" :key="item" :label="item" :value="item"
              style="font-size: large;" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE "
            @keyup.enter.native="filter_pe_list" size="large" />
        </el-col>
        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item" type="success" size="large"
            @click="download_pe_list">
            Download
          </el-button>
        </el-col>
        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item" type="primary" size="large"
            @click="filter_pe_list">
            Search
          </el-button>
        </el-col>
      </el-row>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="current_list" border fit highlight-current-row
      style="width: 100%;" @sort-change="sortChange">
      <el-table-column label="ID" prop="id" sortable="custom" align="center" min-width="3%">
        <template slot-scope="{row}">
          <span>{{ current_list.indexOf(row) + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column v-for="item in col_headerOptions" :label="item.label" class-name="status-col" min-width="6%"
        align="center" sortable="custom" :prop="item.value" v-if="item.checked">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ item.transform ? item.transform(row[item.value]) : row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit"
      @pagination="set_page" />

  </div>
</template>

<script>
import { GetPEList } from '@/api/nutanix'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

const prismoptions = [
  { key: 'EU', display_name: 'SSP-EU-NTX.IKEA.COM' },
  { key: 'CN', display_name: 'SSP-CHINA-NTX.IKEA.COM' },
  { key: 'APAC', display_name: 'SSP-APAC-NTX.IKEA.COM' },
  { key: 'NA', display_name: 'SSP-NA-NTX.IKEA.COM' },
  { key: 'RU', display_name: 'SSP-RUSSIA-NTX.IKEA.COM' },
  { key: 'DT', display_name: 'SSP-DT-NTX.IKEADT.COM' },
  { key: 'PPE', display_name: 'SSP-PPE-NTX.IKEA.COM' },
]
const peoptions = [
  { pc: 'SSP-EU-NTX.IKEA.COM', pe: ['RETDE068-NXC000.ikea.com', 'RETSEHBG-NXC000.ikea.com', 'RETDE124-NXC000.ikea.com', 'RETFR134-NXC000.ikea.com'] },
  { pc: 'SSP-CHINA-NTX.IKEA.COM', pe: ['RETCN856-NXC000.ikea.com', 'RETCNCHN-NXC000.ikea.com', 'RETCN644-NXC000.ikea.com', 'RETCNSOS-NXC000.ikea.com'] },
  { pc: 'SSP-APAC-NTX.IKEA.COM', pe: ['RETKR373-NXC000.ikea.com', 'RETJP509-NXC000.ikea.com', 'RETKR522-NXC000.ikea.com', 'RETKRSO-NXC000.ikea.com'] },
  { pc: 'SSP-NA-NTX.IKEA.COM', pe: ['RETUS100-NXC000.ikea.com', 'RETCA040-NXC000.ikea.com', 'RETUS209-NXC000.ikea.com', 'RETUS374-NXC000.ikea.com'] },
  { pc: 'SSP-RUSSIA-NTX.IKEA.COM', pe: ['RETRU401', 'RETRU403', 'RETRU551', 'RETRU513'] },
  { pc: 'SSP-DT-NTX.IKEADT.COM', pe: ['RETSEELM-NXC000.ikea.com'] },
  { pc: 'SSP-PPE-NTX.IKEA.COM', pe: ['RETSE999-NXC000.ikea.com', 'RETCN888-NXC000.ikea.com'] },
]

const peTypeKeyValue = peoptions.reduce((acc, cur) => {
  acc[cur.pc] = cur.pe
  return acc
}, {})


export default {
  name: 'PETable',
  components: { Pagination },
  directives: { waves },
  filters: {
  },
  data() {
    const validateTime = (rule, value, callback) => {
      if (this.temp.datatimepickerdisabled) {
        callback()
      }
      let currentdate = new Date()
      let utctime = new Date(currentdate.getTime() + 60 * 1000 * currentdate.getTimezoneOffset())
      if (value < utctime) {
        callback(new Error('Schedule date must be later then now.'))
      } else {
        let currnettime = utctime.getTime()
        let scheduletime = value.getTime()
        let timediff = scheduletime - currnettime
        if (timediff / 1000 / 60 < 5) {
          callback(new Error('Schedule date is too close from now.'))
        } else {
          callback()
        }
      }
      callback()
    }
    return {
      check: 1,
      tableKey: 0,
      totalItems: 0,
      all_pe_list: null,
      filtered_list: null,
      current_list: null,
      page_list: null,
      filter: {
        pc_list: [],
        selected_pc: [],
        fuzzy_string: "",
      },
      listLoading: true,
      loading: false,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: '',
        prism: '',
        status: '',
        sort: '+id'
      },
      statusToShowEditButton: ['Not Started'],
      statusToShowAbortButton: ['In Progress'],
      statusToShowDeleteButton: ['Not Started', 'Done', 'Error', 'Aborted'],
      statusOptions: ['Not Started', 'In Progress', 'Done', 'Error', 'Aborted'],
      prismoptions,
      peTypeKeyValue,
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      // statusOptions: ['published', 'draft', 'deleted'],
      ShowCreationDate: false,
      temp: {
        id: '',
        timestamp: new Date(),
        cluster: '',
        prism: '',
        status: '',
        startnow: 1,
        datatimepickerdisabled: false,
        description: '',
        pmtype: 1
      },
      selectedrow: '',
      dialogFormVisible: false,
      dialogStatus: '',
      dialogPvVisible: false,
      logdata: [],
      rules: {
        prism: [{ required: true, message: 'prism is required', trigger: 'change' }],
        cluster: [{ required: true, message: 'cluster is required', trigger: 'change' }],
        timestamp: [{ type: 'date', required: true, trigger: 'change', validator: validateTime }]
      },
      menuVisible: false,
      handleoperarrow: false,
      handlecolarrow: false,
      oper_headerOptions: [
        {
          label: 'Show Siab',
          value: 'showsiab',
          checked: true
        }, {
          label: 'Show Wiab',
          value: 'showwiab',
          checked: true
        }
      ],
      checkedOptions: [],
      col_headerOptions: [
        {
          label: 'PC',
          value: 'prism',
          transform: (x) => { return x.toUpperCase() },
          checked: true
        }, {
          label: 'PE',
          value: 'fqdn',
          transform: (x) => { return x.toUpperCase() },
          checked: true
        }, {
          label: 'Node Num',
          value: 'node_number',
          checked: true
        }, {
          label: 'VM Num',
          value: 'vm_number',
          checked: true
        }, {
          label: 'Remote Site',
          value: 'remote_site_runtime',
          transform: (x) => { return x ? x.toUpperCase() : "" },
          checked: true
        }, {
          label: 'Remote Backup Number',
          short_label: 'RBN',
          value: 'remote_backup_number',
          checked: true
        }, {
          label: 'AHV Version',
          short_label: 'AHV',
          value: 'ahv_version',
          checked: true
        }, {
          label: 'AOS Version',
          value: 'aos_version',
          checked: true
        }, {
          label: 'FD Version',
          value: 'foundation_version',
          checked: true
        }, {
          label: 'LCM Version',
          value: 'lcm_version',
          checked: true
        }, {
          label: 'Cert_Expiry_Date',
          value: 'cert_expiry_date',
          checked: false
        }, {
          label: 'NCC Version',
          value: 'ncc_version',
          checked: false
        }, {
          label: 'Backup Bandwidth',
          short_label: 'BBW',
          value: 'backup_bandwidth',
          checked: false
        }, {
          label: 'Dark Site Bandwidth',
          short_label: 'DSBW',
          value: 'darksite_bandwidth',
          checked: false
        }, {
          label: 'Bandwidth Limit (Mbps)',
          short_label: 'BWL-Mbps',
          value: 'default_bandwidth_limit_mbps',
          checked: false
        }, {
          label: 'Last Update',
          value: 'last_update',
          checked: false
        }, {
          label: 'BU Type',
          value: 'bu_type',
          checked: false
        }, {
          label: 'BU Code',
          value: 'bu_code',
          checked: false
        }, {
          label: 'License Category',
          value: 'license_category',
          checked: false
        }, {
          label: 'License Cores Capacity',
          value: 'license_cores_capacity',
          checked: false
        }, {
          label: 'License Flash Capacity',
          value: 'license_flash_capacity',
          checked: false
        }, {
          label: 'License HDD Capacity',
          value: 'license_hdd_capacity',
          checked: false
        }, {
          label: 'CPU',
          value: 'total_cpu',
          checked: false
        }, {
          label: 'RAM',
          value: 'total_memory',
          checked: false
        }, {
          label: 'Capacity',
          value: 'total_storage',
          checked: false
        }, {
          label: 'UUID',
          value: 'uuid',
          checked: false
        },

      ],
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.totalItems / this.pageSize);
    },
    paginatedItems() {
      const start = (this.page - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.current_list = this.items.slice(start, end);
      console.log(this.current_list)
      return this.current_list;
    },
    total() {
      if (this.filtered_list) {
        return this.filtered_list.length
      }
      else {
        return 0
      }
    }
  },
  mounted() {
    this.loadData();
  },
  // created() {
  //   this.get_pe_list()
  // },
  methods: {
    togglecolumnarrow() {
      this.handlecolarrow = !this.handlecolarrow;
    },
    toggleOperationarrow() {
      this.handleoperarrow = !this.handleoperarrow;
    },

    async loadData() {
      if (this.loading) return;
      this.loading = true;
      try {
        const response = await GetPEList(this.$store.getters.token);
        const data = response.data || [];
        console.log(data)
        this.items = data;
        this.totalItems = response.data.length;
        this.all_pe_list = response.data.map((e) => {
          e.remote_backup_number = parseInt(e.remote_backup_number)
          e.total_cpu = parseInt(e.total_cpu)
          return e
        })
        this.filtered_list = this.all_pe_list
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start, end
        if (page * limit >= this.total) {
          start = (page - 1) * limit
          end = this.total
        }
        else {
          start = (page - 1) * limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start, end)
        this.listLoading = false
        let all_prism_list = this.all_pe_list.map((obj, index) => { return obj['prism'] })
        this.filter.pc_list = this.remove_duplicate(all_prism_list)
      } catch (error) {
        console.error('Failed to load data', error);
      } finally {
        this.loading = false;
      }
    },
    nextPage() {
      if (this.page < this.totalPages) {
        this.page += 1;
        this.loadData();
      }
    },
    prevPage() {
      if (this.page > 1) {
        this.page -= 1;
        this.loadData();
      }
    },

    remove_duplicate(arr) {
      //去除重复值
      const newArr = []
      arr.forEach(item => {
        if (!newArr.includes(item)) {
          newArr.push(item)
        }
      })
      return newArr
    },
    set_page() {
      // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start, end
      if (page * limit >= this.total) {
        start = (page - 1) * limit
        end = this.total
      }
      else {
        start = (page - 1) * limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start, end)
    },
    filter_pe_list() {
      this.listQuery.page = 1
      let temp_list = this.filtered_list
      let originlist = this.filtered_list
      if (this.filter.selected_pc.length) {
        temp_list = this.all_pe_list.filter((item) => {
          return this.filter.selected_pc.includes(item['prism'].toLowerCase())
        })
        this.filtered_list = temp_list
      }
      else {
        this.filtered_list = this.all_pe_list
      }

      if (!this.oper_headerOptions.find(item => item.value === 'showwiab').checked) {
        this.filtered_list = this.filtered_list.filter((item) => {
          return item['name'].search('DS') == -1 && item['name'].search('MOD') == -1
        })
      }

      if (!this.oper_headerOptions.find(item => item.value === 'showsiab').checked) {
        this.filtered_list = this.filtered_list.filter((item) => {
          return item['name'].search('DS') != -1 || item['name'].search('MOD') != -1
        })
      }

      if (this.filter.fuzzy_string.trim().length) {
        let temp_list = this.filtered_list
        let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
        for (let fuzzy of fuzzy_list) {
          fuzzy = fuzzy.toString().toLowerCase()
          temp_list = temp_list.filter((k) => {
            if (k.id.toString().toLowerCase().search(fuzzy) != -1
              || k.fqdn.toLowerCase().search(fuzzy) != -1
              || k.prism.toLowerCase().search(fuzzy) != -1
              || k.node_number.toString().toLowerCase().search(fuzzy) != -1
              || k.vm_number.toString().toLowerCase().search(fuzzy) != -1
              || k.ahv_version.toLowerCase().search(fuzzy) != -1
              || k.aos_version.toLowerCase().search(fuzzy) != -1
            ) {
              return true
            }
          })
        }
        this.filtered_list = temp_list
      }
      this.set_page()
    },
    handleFilter() {
      this.listQuery.page = 1
    },
    sortChange(data) {
      const { prop, order } = data
      if (order == null) {
        this.sortChange({ prop: 'id', order: 'ascending' })
        return
      }
      let flag_num = order == "ascending" ? 1 : -1
      this.filtered_list.sort((item1, item2) => (
        (item1[prop] > item2[prop]) ? flag_num * 1 : ((item1[prop] < item2[prop]) ? flag_num * -1 : 0)
      ))
      this.set_page()
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    handleChecked(val, item) {
      item.checked = val
      this.filter_pe_list()
    },
    async download_pe_list() {
      let data = this.filtered_list.length != 0 ? this.filtered_list : await GetPEList(this.$store.getters.token, true).data
      console.log(data)
      const href = URL.createObjectURL(response.data);
      // create "a" HTML element with href to file & click
      const link = document.createElement('a');
      link.href = href;
      link.setAttribute('download', "pe_list"); //or any other extension
      document.body.appendChild(link);
      link.click();
      // clean up "a" element & remove ObjectURL
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    },
  }
}
</script>
<style lang="scss" scoped>
.bigger_font {
  font-size: 16px;
}
</style>

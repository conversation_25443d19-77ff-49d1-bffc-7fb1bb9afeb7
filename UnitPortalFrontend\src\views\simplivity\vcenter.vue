<template>
    <div class="app-container">
      <div class="filter-container">

      </div>
  
      <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="current_list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
        @sort-change="sortChange"
        @row-click="handle_row_click"
        class='prism-table'
        ref='pctable'
      >
  
        <el-table-column prop="fqdn" label="vCenter(Click to open)"  min-width="10%" align="center" >
          <template  slot-scope="{row}">
            <span ><a :href="'https://' + row.fqdn " target="_blank" style="text-decoration:underline;">{{ row.fqdn }}</a></span>
          </template>
        </el-table-column>

        <el-table-column label="VC build" min-width="10%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.vc_build }}</span>
          </template>
        </el-table-column>
        <el-table-column label="VC Version" class-name="status-col" min-width="6%" align="center" >
          <template slot-scope="{row}">
            <span>
              {{ row.vc_version }}</span>
          </template>
        </el-table-column>

        <el-table-column label="Cluster" align="center" min-width="5%" >
          <template slot-scope="{row}">
            <span>{{ row.clusters_number }}</span>
          </template>
        </el-table-column>

        <el-table-column label="Host" align="center" min-width="5%">
          <template slot-scope="{row}">
            <span>{{ row.hosts_number }}</span>
          </template>
        </el-table-column>

        <el-table-column label="IP" align="center" min-width="5%">
          <template slot-scope="{row}">
            <span>{{ row.vc_ip }}</span>
          </template>
        </el-table-column>
      </el-table>
  
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="get_vc_list" />

    </div>
  </template>
  
  <script>
  import {GetVCList} from '@/api/simplivity'
  import waves from '@/directive/waves' // waves directive
  import { parseTime } from '@/utils'
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination

  export default {
    name: 'PrismTable',
    components: { Pagination },
    directives: { waves },
    data() {
      return {
        tableKey: 0,
        list: null,
        vclist: null,
        current_list: null,
        total: 0,
        listLoading: true,
        listQuery: {
          page: 1,
          limit: 30,
          cluster: '',
          prism: '',
          status: '',
          sort: '+id'
        },
        selectedrow:''
      }
    },
    created() {
      this.get_vc_list()
    },
    methods: {
      get_vc_list(){
        this.listLoading = true
        GetVCList(this.$store.getters.token).then(response => {
            this.vclist = response.data
            this.vclist.sort((a, b) => {
                const vc_a = a.fqdn.toUpperCase(); // ignore upper and lowercase
                const vc_b = b.fqdn.toUpperCase(); // ignore upper and lowercase
                if (vc_a < vc_b) {
                  return -1;
                }
                if (vc_a > vc_b) {
                  return 1;
                }
              
                // names must be equal
                return 0;
              });
            this.total = response.data.length

            let page = this.listQuery.page
            let limit = this.listQuery.limit
            let start , end
            if(page*limit>=this.total){
              start = (page-1)*limit
              end = this.total
            }
            else{
              start = (page-1)*limit
              end = page * limit
            }
            this.current_list = this.vclist.slice(start,end)

            this.listLoading = false
        })
      },
      handleFilter() {
        this.listQuery.page = 1
      },
      sortChange(data) {
        const { prop, order } = data
        if (prop === 'id') {
          this.sortByID(order)
        }
      },
      sortByID(order) {
        if (order === 'ascending') {
          this.listQuery.sort = '+id'
        } else {
          this.listQuery.sort = '-id'
        }
        this.handleFilter()
      },
      handle_row_click(row,column,event){
        this.selectedrow = row
      },
      formatJson(filterVal) {
        return this.list.map(v => filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        }))
      },
      getSortClass: function(key) {
        const sort = this.listQuery.sort
        return sort === `+${key}` ? 'ascending' : 'descending'
      },
      set_page(){
        // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total 
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.vclist.slice(start,end)
      },
      test(a,b){
        console.log(a,b)
      }
    },
    beforeDestroy(){
      clearInterval( this.intervaljob )
    }
  }
  </script>
  <style scoped>
   .prism-table span{
    
       font-size: 17px
  }
  </style>
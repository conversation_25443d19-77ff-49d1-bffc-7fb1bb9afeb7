2025-07-31 13:45:22,671 INFO Start to run the task.
2025-07-31 13:45:22,712 INFO ****************************************************************************************************
2025-07-31 13:45:22,713 INFO *                                                                                                  *
2025-07-31 13:45:22,713 INFO *                                        Check VM existence                                        *
2025-07-31 13:45:22,713 INFO *                                                                                                  *
2025-07-31 13:45:22,723 INFO ****************************************************************************************************
2025-07-31 13:45:22,762 INFO Checking if vm already exists in the PE cluster.
2025-07-31 13:45:22,763 INFO Checking if RETSEELM-LX7300 existed in PE.
2025-07-31 13:45:22,763 INFO Getting VM list from RETSEELM-NXC000.
2025-07-31 13:45:22,763 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms?sortCriteria=vm_name&searchString=RETSEELM-LX7300, method: GET, headers: None
2025-07-31 13:45:22,763 INFO params: None
2025-07-31 13:45:22,763 INFO User: <EMAIL>
2025-07-31 13:45:22,763 INFO payload: None
2025-07-31 13:45:22,763 INFO files: None
2025-07-31 13:45:22,763 INFO timeout: 30
2025-07-31 13:45:24,622 INFO Got the VM list from RETSEELM-NXC000.
2025-07-31 13:45:24,622 INFO RETSEELM-LX7300 doesn't exist in RETSEELM-NXC000.
2025-07-31 13:45:24,660 INFO RETSEELM-LX7300 not exists in Cluster RETSEELM-NXC000.IKEAD2.COM, move on...
2025-07-31 13:45:24,706 INFO Checking if vm already exists in the inventory AD/Tower.
2025-07-31 13:45:26,662 INFO FQDN 'RETSEELM-LX7300.IKEAD2.COM' not exists in Tower Inventory, continue...
2025-07-31 13:45:26,699 INFO Checking if vm already exists in IPAM.
2025-07-31 13:45:26,725 INFO Start to check if RETSEELM-LX7300.IKEAD2.COM existed in IPAM...
2025-07-31 13:45:26,725 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-LX7300.IKEAD2.COM', method: GET, headers: None
2025-07-31 13:45:26,725 INFO params: None
2025-07-31 13:45:26,725 INFO User: <EMAIL>
2025-07-31 13:45:26,726 INFO payload: None
2025-07-31 13:45:26,726 INFO files: None
2025-07-31 13:45:26,726 INFO timeout: 30
2025-07-31 13:45:27,855 INFO 'RETSEELM-LX7300' not exists in IPAM, continue...
2025-07-31 13:45:27,893 INFO ****************************************************************************************************
2025-07-31 13:45:27,894 INFO *                                                                                                  *
2025-07-31 13:45:27,894 INFO *                                              Sizing                                              *
2025-07-31 13:45:27,894 INFO *                                                                                                  *
2025-07-31 13:45:27,894 INFO ****************************************************************************************************
2025-07-31 13:45:27,934 INFO Sizing, check if cluster has enough capacity for this VM.
2025-07-31 13:45:27,934 INFO Get a list of existing hosts from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:45:27,934 INFO Calling /hosts through v1 API using GET method
2025-07-31 13:45:27,934 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-07-31 13:45:27,934 INFO params: None
2025-07-31 13:45:27,934 INFO User: <EMAIL>
2025-07-31 13:45:27,934 INFO payload: None
2025-07-31 13:45:27,934 INFO files: None
2025-07-31 13:45:27,935 INFO timeout: None
2025-07-31 13:45:29,739 INFO Get cluster details from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:45:29,739 INFO Calling /cluster through v1 API using GET method
2025-07-31 13:45:29,739 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster, method: GET, headers: None
2025-07-31 13:45:29,739 INFO params: None
2025-07-31 13:45:29,739 INFO User: <EMAIL>
2025-07-31 13:45:29,739 INFO payload: None
2025-07-31 13:45:29,739 INFO files: None
2025-07-31 13:45:29,739 INFO timeout: None
2025-07-31 13:45:31,682 INFO Get a list of existing user VMs from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:45:31,682 INFO Calling /vms through v2 API using GET method
2025-07-31 13:45:31,682 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms, method: GET, headers: None
2025-07-31 13:45:31,682 INFO params: None
2025-07-31 13:45:31,682 INFO User: <EMAIL>
2025-07-31 13:45:31,682 INFO payload: None
2025-07-31 13:45:31,682 INFO files: None
2025-07-31 13:45:31,682 INFO timeout: None
2025-07-31 13:45:33,395 INFO The cluster is a 3 node(s) cluster
2025-07-31 13:45:33,395 INFO Fetching capacity from node RETSEELM-NX7001
2025-07-31 13:45:33,395 INFO Storage of node RETSEELM-NX7001 is 44.68 TiB
2025-07-31 13:45:33,395 INFO Number of cores on node RETSEELM-NX7001 is 20
2025-07-31 13:45:33,395 INFO Memory install on node RETSEELM-NX7001 is 377.08 GiB
2025-07-31 13:45:33,395 INFO Fetching capacity from node RETSEELM-NX7002
2025-07-31 13:45:33,395 INFO Storage of node RETSEELM-NX7002 is 44.68 TiB
2025-07-31 13:45:33,395 INFO Number of cores on node RETSEELM-NX7002 is 20
2025-07-31 13:45:33,395 INFO Memory install on node RETSEELM-NX7002 is 377.08 GiB
2025-07-31 13:45:33,396 INFO Fetching capacity from node RETSEELM-NX7003
2025-07-31 13:45:33,396 INFO Storage of node RETSEELM-NX7003 is 44.68 TiB
2025-07-31 13:45:33,396 INFO Number of cores on node RETSEELM-NX7003 is 20
2025-07-31 13:45:33,396 INFO Memory install on node RETSEELM-NX7003 is 345.58 GiB
2025-07-31 13:45:33,396 INFO Number of nodes in this cluster is 3
2025-07-31 13:45:33,396 INFO Total storage capacity on this cluster is 134.04 TiB
2025-07-31 13:45:33,396 INFO total number of CPU cores on cluster is 60
2025-07-31 13:45:33,396 INFO Total memory capacity on this cluster is 1099.74 GiB
2025-07-31 13:45:33,396 INFO Resilient storage capacity on this cluster is 84.************** TiB
2025-07-31 13:45:33,396 INFO Number of resilient physical CPU cores is 40
2025-07-31 13:45:33,396 INFO Number of resilient physical CPU cores accounting CVMs is 34
2025-07-31 13:45:33,396 INFO Number of resilient virtual CPU cores (assuming 1:4 ratio) is 136
2025-07-31 13:45:33,396 INFO Resilient memory capacity on this cluster is 722.************* GiB
2025-07-31 13:45:33,396 INFO Resilient memory capacity accounting CVMs on this cluster is 658.************* GiB
2025-07-31 13:45:33,396 INFO Utilized storage of cluster is 0.95 TiB
2025-07-31 13:45:33,396 INFO There are 5 VMs on this cluster
2025-07-31 13:45:33,396 INFO Number of virtual cores used by 5 VMs that are powered on is 62
2025-07-31 13:45:33,397 INFO Memory used by 5 VMs that are powered on is 248.0 GiB
2025-07-31 13:45:33,397 INFO Available storage for new VM provisioning is 83.************** TiB
2025-07-31 13:45:33,397 INFO Available vCPU cores for new VM provisioning is 74
2025-07-31 13:45:33,397 INFO Available memory for new VM provisioning is 410.************* GiB
2025-07-31 13:45:33,442 INFO ****************************************************************************************************
2025-07-31 13:45:33,443 INFO *                                                                                                  *
2025-07-31 13:45:33,443 INFO *                                Checking workload network on NTX.                                 *
2025-07-31 13:45:33,443 INFO *                                                                                                  *
2025-07-31 13:45:33,443 INFO ****************************************************************************************************
2025-07-31 13:45:33,515 INFO Checking PE network by VlanId=793
2025-07-31 13:45:33,515 INFO Getting network list from RETSEELM-NXC000
2025-07-31 13:45:33,515 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/networks, method: GET, headers: None
2025-07-31 13:45:33,515 INFO params: None
2025-07-31 13:45:33,515 INFO User: <EMAIL>
2025-07-31 13:45:33,515 INFO payload: None
2025-07-31 13:45:33,515 INFO files: None
2025-07-31 13:45:33,515 INFO timeout: 30
2025-07-31 13:45:34,834 INFO Got the network list from RETSEELM-NXC000.
2025-07-31 13:45:34,834 INFO Vlan 793 is found
2025-07-31 13:45:34,875 INFO The network is found, the UUID is 9531e569-3bec-4d92-8581-6209bea747db
2025-07-31 13:45:34,913 INFO ****************************************************************************************************
2025-07-31 13:45:34,913 INFO *                                                                                                  *
2025-07-31 13:45:34,913 INFO *                                           Check image                                            *
2025-07-31 13:45:34,913 INFO *                                                                                                  *
2025-07-31 13:45:34,914 INFO ****************************************************************************************************
2025-07-31 13:45:34,954 INFO Verifying workload image existence in DB and on cluster.
2025-07-31 13:45:34,983 INFO Checking if RHELx_AUTO image
2025-07-31 13:45:34,983 INFO Validating image 'RHEL9.2-RETSEELM-NXC000' existence in PE 'RETSEELM-NXC000'...
2025-07-31 13:45:34,983 INFO Start to find the image RHEL9.2-RETSEELM-NXC000 from RETSEELM-NXC000
2025-07-31 13:45:34,984 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/images, method: GET, headers: None
2025-07-31 13:45:34,984 INFO params: None
2025-07-31 13:45:34,984 INFO User: <EMAIL>
2025-07-31 13:45:34,984 INFO payload: None
2025-07-31 13:45:34,984 INFO files: None
2025-07-31 13:45:34,984 INFO timeout: 30
2025-07-31 13:45:36,901 INFO Getting image list from RETSEELM-NXC000
2025-07-31 13:45:36,902 INFO Got the image list from RETSEELM-NXC000.
2025-07-31 13:45:36,945 INFO Image doesn't exist on PE, will upload.
2025-07-31 13:45:36,945 INFO Start to upload the image to PE...
2025-07-31 13:45:36,955 INFO Prepare to upload image RHEL9.2 to RETSEELM-NXC000
2025-07-31 13:45:36,956 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/containers, method: GET, headers: None
2025-07-31 13:45:36,956 INFO params: None
2025-07-31 13:45:36,956 INFO User: <EMAIL>
2025-07-31 13:45:36,956 INFO payload: None
2025-07-31 13:45:36,956 INFO files: None
2025-07-31 13:45:36,956 INFO timeout: 30
2025-07-31 13:45:38,844 INFO Getting container list from RETSEELM-NXC000
2025-07-31 13:45:38,844 INFO Got the container list from RETSEELM-NXC000.
2025-07-31 13:45:38,844 INFO The container SelfServiceContainer is found
2025-07-31 13:45:38,844 INFO The container SelfServiceContainer is located, its UUID is 62cf1853-5f93-41e0-a204-99fb9f7b4b45
2025-07-31 13:47:34,378 INFO Get the image RHEL9.2-RETSEELM-NXC000 from D:\Image\rhel-92-401006d-20240601T053114
2025-07-31 13:47:34,379 INFO Upload the image RHEL9.2-RETSEELM-NXC000 to RETSEELM-NXC000
2025-07-31 13:47:34,379 INFO Uploading image RHEL9.2-RETSEELM-NXC000 to RETSEELM-NXC000
2025-07-31 13:47:34,379 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/api/nutanix/v0.8/images, method: POST, headers: None
2025-07-31 13:47:34,379 INFO params: None
2025-07-31 13:47:34,379 INFO User: <EMAIL>
2025-07-31 13:47:34,379 INFO payload: {'name': 'RHEL9.2-RETSEELM-NXC000', 'annotation': 'RHEL9.2-RETSEELM-NXC000', 'imageType': 'DISK_IMAGE', 'imageImportSpec': {'containerUuid': '62cf1853-5f93-41e0-a204-99fb9f7b4b45', 'url': 'D:\\Image\\rhel-92-401006d-20240601T053114'}}
2025-07-31 13:47:34,379 INFO files: None
2025-07-31 13:47:34,379 INFO timeout: 30
2025-07-31 13:47:36,202 INFO It's uploading the image, the task id is fed9258d-f89f-483e-9585-87682a3db489
2025-07-31 13:47:36,202 INFO Get task status attempting 1/120...
2025-07-31 13:47:36,202 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/fed9258d-f89f-483e-9585-87682a3db489, method: GET, headers: None
2025-07-31 13:47:36,202 INFO params: None
2025-07-31 13:47:36,202 INFO User: <EMAIL>
2025-07-31 13:47:36,203 INFO payload: None
2025-07-31 13:47:36,203 INFO files: None
2025-07-31 13:47:36,203 INFO timeout: 30
2025-07-31 13:47:37,984 INFO Task status: Failed
2025-07-31 13:47:38,107 ERROR Task failed. Detail: ['Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\base_up_task.py", line 88, in start_task\n    self.task_process()\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\workload\\workload.py", line 187, in task_process\n    self.get_wl_image(_pc, _pe)\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\workload\\workload.py", line 284, in get_wl_image\n    self.upload_wl_image_to_pe(_pe=_pe)  # Upload image to PE\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\workload\\workload.py", line 256, in upload_wl_image_to_pe\n    raise ImageUploadFailed(in_progress=True, image_name=image_pe_name, original_err=msg)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', "TypeError: ImageUploadFailed.__init__() missing 1 required positional argument: 'init'\n"]

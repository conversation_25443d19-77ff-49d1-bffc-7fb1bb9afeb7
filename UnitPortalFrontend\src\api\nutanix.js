import axios from 'axios'
import { endpoint } from './endpoint'
import request from '@/utils/request'


export function GetPrismList(token) {
  let res =  request.get(`${endpoint}/ntx/prism/list`)
  return res
}

export function AddPrism(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/prism`,param.data,config)
  return res
}

export function GetPEList(token , download = false) {
  if(download){
    var config = {
      headers: {'Authorization': 'Bearer ' + token},
      responseType:'blob'
    };
    let res =  request.get(`${endpoint}/ntx/pe/list?download=true`,config)
    return res
  }
  else{
    var config = {
      headers: {'Authorization': 'Bearer ' + token}
    };
    let res =  request.get(`${endpoint}/ntx/pe/list`,config)
    return res
  }

}

export function GetNtxHostList(token , download = false) {
  if(download){
    var config = {
      headers: {'Authorization': 'Bearer ' + token},
      responseType:'blob'
    };
    let res =  request.get(`${endpoint}/ntx/host?download=true`,config)
    return res
  }
  else{
    var config = {
      headers: {'Authorization': 'Bearer ' + token}
    };
    let res =  request.get(`${endpoint}/ntx/host`,config)
    return res
  }

}

export function GetPCPECorrespondence(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/pe/correspondence`,config)
  return res
}

export function GetPCPECorrespondence_Lcm(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/pe/correspondence?country=true`,config)
  return res
}

export function GetTemplateList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/workload/template/list`,config)
  return res
}

export function GetWorkloadTaskList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/workload/task/list/create`,config)
  return res
}
export function GetCleanupWorkloadTaskList() {

  let res =  request.get(`${endpoint}/ntx/workload/task/list/delete`)
  return res
}
export function GetWorkloadTasks(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/workload/task/create`,config)
  return res
}
export function GetCleanupWorkloadTasks(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/workload/task/delete`,config)
  return res
}


export function GetWorkloadInfo(token){
  var config = {
    headers: {Authorization: 'Bearer ' + token}
  };
  console.log(config)
  let res =  request.get(`${endpoint}/ntx/workload/info`,config)
  return res
}

export function CreateWorkload(param){
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/workload`,param.data, config)
  return res
}

export function CleanupWorkload(payload){
  let res =  request.delete(`${endpoint}/ntx/workload`, {data:payload})
  return res

}

export function GetWorkloadTaskLogs(param){
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/workload/task/log`,param.data, config)
  return res
}

export function CreateWorkloadTemplate(param){
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/ntx/workload/template`,param.data, config)
  return res
}

export function DeleteWorkloadTemplate(param){  // delete only accept 2 param, url and {data:{},headers:{}}
  let payload = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data   : param.data
  }
  let res =  request.delete(`${endpoint}/ntx/workload/template`,payload)
  return res
}

export function Download(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token},
    responseType:'blob'
  }
  let res = request.post(`${endpoint}/download`,param.data,config)
  return res
}

export function GetVMList(token, download = false){
  if(download){
    var config = {
      headers: {'Authorization': 'Bearer ' + token},
      responseType:'blob'
    };
    let res =  request.get(`${endpoint}/ntx/vm/list?download=true`,config)
    return res
  }
  else{
    var config = {
      headers: {'Authorization': 'Bearer ' +token}
    };

    let res =  request.get(`${endpoint}/ntx/vm/list`,config)
    return res
  }
}

export function GetClusterVMList(payload){

    let res = request.post(`${endpoint}/ntx/cluster/vm/list`, payload)
    return res

}

export function GetDashboardInfo(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/dashboard-diagram`, config)
  return res
}

export function GetAppPackageList(token){
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/ntx/workload/supported_packages`, config)
  return res
}

export function CleanUpVM(param){
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data:param.data
  };
  let res =  request.delete(`${endpoint}/ntx/workload/cleanup`, config)
  return res
}

export function GetBenchmarkList() {
  let res =  request.get(`${endpoint}/benchmark/list_with_vlan_config`)
  console.log(res)
  return res
}

export function GetActiveIncidents(param){
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data:param.data
  };
  let res =  request.get(`${endpoint}/incident-handler/active-incidents`, config)
  return res
}

export function GetUnassignedIncidents(param){
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data:param.data
  };
  let res =  request.get(`${endpoint}/incident-handler/unassigned-incidents`, config)
  return res
}

export function GetTotalResolvedIncidents(param){
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data:param.data
  };
  let res =  request.get(`${endpoint}/incident-handler/total-resolved-incidents`, config)
  return res
}

export function GetTodayResolvedIncidents(param){
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data:param.data
  };
  let res =  request.get(`${endpoint}/incident-handler/resolved-incidents-today`, config)
  return res
}

export function GetIncidentSummary(param){
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data:param.data
  };
  let res =  request.get(`${endpoint}/incident-handler/unit-portal-incident-data`, config)
  return res
}

export function DownloadAnalysisData(param){
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/incident_sheet_download`,param, config)
  return res
}

export function UnlockAccount(data) {
  return request({
    url: `${endpoint}/ntx/unlock_account`,
    method: 'post',
    data
  })
}

export function GetUnlockAccountTaskList() {
  return request({
    url: `${endpoint}/ntx/unlock_account/tasks`,
    method: 'get'
  })
}

export function GetUnlockAccountTaskLogs(param) {
  return request({
    url: `${endpoint}/ntx/unlock_account/logs`,
    method: 'post',
    data: param.data
  })
}

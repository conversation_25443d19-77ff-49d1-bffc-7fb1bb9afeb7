<template>
    <div :class="className" :style="{height:height,width:width}" />
  </template>
  
  <script>
  import echarts from 'echarts'
  require('echarts/theme/macarons') // echarts theme
  import resize from '@/views/dashboard/admin/components/mixins/resize'
  import {
    GetIncidentList
  } from '@/api/incident'
  export default {
    mixins: [resize],
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '40vh'
      },
      bar_data_dict:{},
      date_payload:{},
      top_five_incident_list:{}
    },
    watch: {
      bar_data_dict: {
        handler() {

          this.init_chart()
        },
        deep: true
      }
    },
    data() {
      return {
        chart: null,
        table_loading:false,
        last_clicked_index: null 
      }
    },
    mounted() {
      this.init_chart()
      // this.$nextTick(() => {
      //   this.initChart()
      // })
    },
    beforeDestroy() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
    methods: {    

      init_chart() {
        new Promise((resolve, reject) => {
          resolve()
        }).then(() => {
          this.chart = echarts.init(this.$el, 'macarons')
          let option = {
            title: {
              text: "Top Incident Models",
              left: 'center',
              top: '10',
              padding: [-10, 20],
              textStyle: {
                color: '#000',
                fontSize: 16
              }
            },
            xAxis: {
              type: 'value',
              show: false
            },
            yAxis: {
              type: 'category',
              data: this.bar_data_dict['type'],
              axisLabel: {  
                fontSize: 16,//柱状图左边字体大小 ，#ivan
                overflow: 'truncate',
                color: '#010000'//#ivan 字体颜色
              }
            },
            grid: {
              left:'37%',
              // borderColor:'#ccc',
              top:'10%',
              bottom:'10%'  
            },
            series: [
              {
                type: 'bar',
                data: this.bar_data_dict['data'].map(value => ({
                value: value,
                itemStyle: {
                  color: '#d2d2df' //设置柱状图里 bar的颜色 #ivan
                }
              })),
                barCategoryGap: '40%',
                // itemStyle: {
                //   color: '#d2d2df' // 
                // },
                label: {
                  show: true,
                  position: 'right',
                }
              }
            ]
          };
          this.chart.setOption(option)
          // add listener 
          this.chart.on('click', this.handle_bar_click)
          this.last_clicked_index = null 
          // 自动触发点击第一条 bar
          if (this.bar_data_dict['data'] && this.bar_data_dict['data'].length > 0) {
            const firstBarParams = {
              dataIndex: this.bar_data_dict['data'].length-1,
              name: this.bar_data_dict['type'].slice(-1)[0],
              value: this.bar_data_dict['data'].slice(-1)[0],
            };
            console.log(firstBarParams)
            this.handle_bar_click(firstBarParams);
          }
        })
      },
      handle_bar_click(params){
            // 如果点击的是同一个 bar，则直接返回
        if (this.last_clicked_index === params.dataIndex) {
          // return if the same bar was clicked
          return
        }
        if(this.table_loading){
          // return if the table is loading
          return
        }
        this.table_loading = true
        this.last_clicked_index = params.dataIndex
        console.log('柱状图被点击了:', params)
        let option = this.chart.getOption()
        console.log(option)
        // 重置所有柱子的颜色
        option.series[0].data.forEach((item) => {
          item.itemStyle.color = '#d2d2df' // 恢复默认颜色
        })

        // 设置被点击柱子的颜色为高亮色
        option.series[0].data[params.dataIndex].itemStyle.color = '#ff5722' // #ivan，点击bar，将对应的 bar 改成高亮颜色

        // 更新图表
        this.chart.setOption(option)

        // 发送请求，获取数据
        let payload = {
          'model_name': params.name,
          'start_time': parseInt(this.date_payload.start_time.getTime()/1000),
          'end_time': parseInt(this.date_payload.end_time.getTime()/1000)
        }
        GetIncidentList(payload).then(response => {
          this.$emit("change_table_data", response['data'])
        });

        this.table_loading = false  
      }
    }
  }
  </script>
  
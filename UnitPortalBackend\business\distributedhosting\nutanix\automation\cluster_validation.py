import json
import logging
import re
import sys
import time
import traceback
import uuid
from json import JSONDecodeError

import werkzeug.exceptions as flaskex
from datetime import datetime

from flask import Flask
from sqlalchemy import and_

from business.authentication.authentication import Vault
from business.distributedhosting.nutanix.automation.automation import Automation
from business.distributedhosting.nutanix.automation.desired_state_config import DscIpamBase
from business.distributedhosting.nutanix.nutanix import PrismElement, PrismCentral, NutanixCLI
from business.distributedhosting.nutanix.pe_components import RestAuthConfig, StorageContainer, RestSMTP, RestHA, \
    RestCluster, RestEulas, RestPulse, RestGenesis, RestProgressMonitor
from business.distributedhosting.nutanix.task_common import TaskCommon, TaskStatus
from business.generic.commonfunc import create_file, setup_common_logger, SSHConnect
from business.generic.ipam import Ipam
from business.loggings.loggings import DBLogging, IntegratedLogger
import static.SETTINGS as SETTING
from models.ntx_models import ModelPrismElement, ModelPrismCentral
from scheduler import auto_maintenance_scheduler
from models.database import db


class ClusterValidation():
    #static params

    def __init__(self, pe) -> None:
        self.pe = pe

    def start_validation(self):
        validations=[
            ValidationDNS
        ]
        for v in validations:
            v(self.pe).validate()
    # def setup_loggers(self):
    #     lg = DBLogging(logdir=SETTING.DSC_LOG_PATH, taskid=self.task.id, logtype="DSC", log_model=ModelRetailNutanixAutomationDscTaskLog)
    #     self.lg = lg
    #     local_log_path = create_file(filepath=SETTING.DSC_LOG_PATH, filename=f"{self.pe}_{datetime.utcnow().strftime('%Y-%m-%d-%H-%M-%S')}")
    #     self.logger = setup_common_logger(str(uuid.uuid4()), local_log_path)
    #     self.task.detaillogpath = local_log_path
    #     db.session.commit()
    #     self.lg.write_log(f"Log file created: {local_log_path}")



class ValidationBase():
    #base class for validation 
    def __init__(self, pe) -> None:
        self.pe = pe

#check if the site level dns servers are pinging
class ValidationDNS(ValidationBase):
    def __init__(self, pe) -> None:
        super().__init__(pe)

    def validate(self):
        dsc_ipam = DscIpamBase(pe = self.pe, logger = logging, db_logger = None)
        subnet = dsc_ipam.get_ahv_cvm_subnet()
        print(subnet)
<template>
    <div class="app-container" >
      <el-card class="box-card-top">
        <el-table border :data="translateData" :show-header="false">
          <el-table-column v-for="(item, index) in originData.length+1"   :key="index" 
          :class-name="index ===0 ? 'cloumn-header':''" align="center">
              <template slot-scope="{row}">
                  {{row[index]}}
              </template>
          </el-table-column>
      </el-table>
      </el-card>
      <el-card  class="box-card-left">
          <div class="filter-container" style="line-height: 40px;">
            <el-row :gutter="5">
              <el-col :span="3">
                <el-button class="filter-item" type="primary" @click="handle_gaincompen_add_dialog">
                  Gain Compens
                </el-button>
              </el-col>
              <el-col :span="3" :offset="15">
                <el-button class="filter-item" type="warning" @click="handle_gaincompen_edit_dialog" style="float:right">
                  Edit
                </el-button>
              </el-col>
              <el-col :span="3">
                <el-button class="filter-item" type="danger" @click="handle_gaincompen_delete" style="float:right">
                  Delete
                </el-button>
              </el-col>
            </el-row> 
          </div>
        <el-table
          :key="tableKey"
          v-loading="listLoading"
          :data="gaincompenlist"
          border
          fit
          highlight-current-row
          style="width: 100%;"
          @sort-change="sortChange"
          @row-click="handle_row_click"
          class='compen-table'
          ref='pctable'
        >
          <el-table-column label="User" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="username" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.username }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Tasks/INCs" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="reason" show-overflow-tooltip >
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.reason }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Start Date" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="start_date" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.start_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="End Date" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="end_date" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.end_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Hours" class-name="status-col" min-width="3%" align="center" sortable="custom" prop="total_hours" >
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.total_hours }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Gain" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="gain_hours" >
            <template slot-scope="{row}">
              <span class="bigger_font">{{ row.gain_hours }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" />
      </el-card>
      <el-card  class="box-card-right">
          <div class="filter-container" style="line-height: 40px;">
            <el-row :gutter="5">
              <el-col :span="3">
                <el-button class="filter-item" type="primary" @click="handle_usecompen_add_dialog">
                    Use Compens
                </el-button>
              </el-col>
              <el-col :span="3" :offset="14" >
                <el-button class="filter-item" type="warning" @click="handle_usecompen_edit_dialog" style="float:right">
                  Edit
                </el-button>
              </el-col>
              <el-col :span="4" >
                <el-button class="filter-item" type="danger" @click="handle_usecompen_delete" style="float:right">
                  Delete
                </el-button>
              </el-col>
            </el-row> 
          </div>
          <el-table
            :key="tableKey"
            v-loading="listLoading"
            :data="usecompen_list"
            border
            fit
            highlight-current-row
            style="width: 100%;"
            @sort-change="ucsortChange"
            @row-click="handle_row_click"
            class='compen-table'
            ref='pctable'
          >
            <el-table-column label="User" class-name="status-col" min-width="5%" align="center" sortable="custom" prop="username" show-overflow-tooltip>
              <template slot-scope="{row}">
                <span class="bigger_font">{{ row.username }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Start Time" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="start_date" show-overflow-tooltip >
              <template slot-scope="{row}">
                <span class="bigger_font">{{ row.start_date }}</span>
              </template>
            </el-table-column>
            <el-table-column label="End Time" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="end_date" show-overflow-tooltip >
              <template slot-scope="{row}">
                <span class="bigger_font">{{ row.end_date }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Detail" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="reason" show-overflow-tooltip>
              <template slot-scope="{row}">
                <span class="bigger_font">{{ row.reason }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Hours" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="use_hours" >
              <template slot-scope="{row}">
                <span class="bigger_font">{{ row.use_hours }}</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="usecompen_total>0" :total="usecompen_total" :page.sync="uc_listQuery.page" :limit.sync="uc_listQuery.limit" @pagination="set_usecompen_page" />
        <!-- </div> -->
      </el-card>
      <el-dialog 
        :title="'Gain Compen'" 
        :visible.sync="gain_compen_dialog_form_visible" 
        style="width:80%;margin-left:10%"
        @close="handledialogClose"
        :before-close="handledialogbeforeClose">
        <!-- https://element.eleme.cn/#/zh-CN/component/transfer -->
        <el-form ref="gaincompenform" :rules="rules" :model="gain_compen" label-position="right" label-width="18%" style="width: 100%; margin-left:15px;">
          <el-form-item label="Task/INC"   prop="reason" > 
            <el-input v-model="gain_compen.reason" style="width:50%;" placeholder="Please enter your tasks or incidents here."/>
          </el-form-item>
          <el-form-item label="Start Time" prop="starttime" >
            <el-date-picker  :picker-options="pickerOptions" format="yyyy-MM-dd HH:mm" v-model="gain_compen.starttime" type="datetime"  placeholder="Please pick a start date"  style="width:50%"/>
          </el-form-item>
          <el-form-item label="End Time" prop="endtime" >
            <el-date-picker :picker-options="pickerOptions" format="yyyy-MM-dd HH:mm" v-model="gain_compen.endtime" type="datetime" placeholder="Please pick a end date"  style="width:50%"/>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="gain_compen_dialog_form_visible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="gaincompen()">
            Confirm
          </el-button>
        </div>
      </el-dialog>
      <el-dialog 
        :title="'Use Compen'" 
        :visible.sync="new_usecompen_dialog_form_visible" 
        style="width:80%;margin-left:10%"
        @close="handledialogClose"
        :before-close="handledialogbeforeClose">
        <el-form ref="usecompenForm" :rules="rules" :model="usecompen" label-position="right" label-width="95px" style="width: 1000px; margin-left:15px;">
          <el-form-item label="Task/INC"   prop="reason" > 
            <el-input v-model="usecompen.reason" style="width:30%;" placeholder="It is OK, if you leave it empty."/>
          </el-form-item>
          <el-form-item label="Start Time" prop="starttime" >
            <el-date-picker :picker-options="pickerOptions" format="yyyy-MM-dd HH:mm" v-model="usecompen.starttime" type="datetime" placeholder="Please pick a start date"  style="width:30%"/>
          </el-form-item>
          <el-form-item label="End Time" prop="endtime" >
            <el-date-picker :picker-options="pickerOptions" format="yyyy-MM-dd HH:mm" v-model="usecompen.endtime" type="datetime" placeholder="Please pick a end date"  style="width:30%"/>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="new_usecompen_dialog_form_visible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="use_compen()">
            Confirm
          </el-button>
        </div>
      </el-dialog>
      <el-dialog 
        :title="'Edit Gain Compens'" 
        :visible.sync="edit_gaincompen_dialog_form_visible" 
        style="width:80%;margin-left:10%"
        :before-close="handledialogbeforeClose">
        <el-form ref="editgaincompen" label-position="left" label-width="18%" style="width: 100%; margin-left:15px;">
          <el-form-item label="Task/INC"   prop="reason" > 
            <el-input v-model="gain_compen.reason" style="width:50%;" placeholder="Please enter your tasks or incidents here."/>
          </el-form-item>
          <el-form-item label="Start Time" prop="starttime" >
            <el-date-picker :picker-options="pickerOptions" format="yyyy-MM-dd HH:mm"  v-model="gain_compen.starttime" type="datetime" placeholder="Please pick a start date"  style="width:50%"/>
          </el-form-item>
          <el-form-item label="End Time" prop="endtime" >
            <el-date-picker :picker-options="pickerOptions" format="yyyy-MM-dd HH:mm" v-model="gain_compen.endtime" type="datetime" placeholder="Please pick a end date"  style="width:50%"/>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="edit_gaincompen_dialog_form_visible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="handle_gaincompen_edit()">
            Confirm
          </el-button>
        </div>
      </el-dialog>
      <el-dialog 
        :title="'Edit Use Compens'" 
        :visible.sync="edit_usecompen_dialog_form_visible" 
        style="width:1500px;margin-left:12%"
        :before-close="handledialogbeforeClose">
        <el-form ref="editusecompenform" label-position="left" label-width="95px" style="width: 1000px; margin-left:15px;">
          <el-form-item label="Task/INC"   prop="reason" > 
            <el-input v-model="usecompen.reason" style="width:50%;" placeholder="Please enter your tasks or incidents here."/>
          </el-form-item>
          <el-form-item label="Start Time" prop="starttime" >
            <el-date-picker :picker-options="pickerOptions" format="yyyy-MM-dd HH:mm" v-model="usecompen.starttime" type="datetime" placeholder="Please pick a start date"  style="width:50%"/>
          </el-form-item>
          <el-form-item label="End Time" prop="endtime" >
            <el-date-picker :picker-options="pickerOptions" format="yyyy-MM-dd HH:mm" v-model="usecompen.endtime" type="datetime" placeholder="Please pick a end date"  style="width:50%"/>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="edit_usecompen_dialog_form_visible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="handle_usecompen_edit()">
            Confirm
          </el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
    import {GetMemoID, GetAvaliableCompen, ListGainCompen, AddGainCompen, UpdateGainCompen, DeleteGainCompen, ListUseCompen, AddUseCompen, UpdateUseCompen, DeleteUseCompen} from '@/api/compensation'
    import waves from '@/directive/waves' // waves directive
    import { parseTime } from '@/utils'
    import Pagination from '@/components/Pagination' // secondary package based on el-pagination
    
    export default {
      name: 'CompensationTable',
      components: { Pagination },
      directives: { waves },
      data() {
        // const gaincompn_validator =(rule, value, callback)=>{//check if there is at least 1 record been selected
        //   if(this.gain_compen.reason.length==0||this.gain_compen.starttime.length==0||this.gain_compen.endtime.length==0){
        //     callback(new Error("Missing fields."))
        //   }
        //   else{
        //     callback()
        //   }
        // }
        // const usecompen_validator =(rule, value, callback)=>{//check if there is at least 1 record been selected
        //   if(this.gain_compen.reason.length==0||this.gain_compen.starttime.length==0||this.gain_compen.endtime.length==0){
        //     callback(new Error("Missing fields."))
        //   }
        //   else{
        //     callback()
        //   }
        // }
        const name_validator = (rule, value, callback)=>{//check if there is at least 1 record been selected
          if(value==null){
            callback(new Error("Memo id only."))
          }
          callback()
        }
        return {
          pickerOptions: {
            selectableRange :'00:00:00 - 23:59:59',
              // start: '08:30',
              step: '00:30:00',
              // end: '18:30'
          },
          compen_temp: {
            endtime:new Date(),
            starttime:new Date()
          },
          compen_calculate_rules: {
            0:{'after_6':1.5,'before_6':2},
            1:{'after_6':1,'before_6':2},
            2:{'after_6':1,'before_6':1.5},
            3:{'after_6':1,'before_6':1.5},
            4:{'after_6':1,'before_6':1.5},
            5:{'after_6':1,'before_6':1.5},
            6:{'after_6':1.5, 'before_6':1.5}
          },
          compen_info: {
            'one_time'          : 0,
            'one_and_half_time' : 0,
            'two_time'          : 0,
            'total_time'        : 0
          },
          gain_compen: {
            reason: '',
            starttime: '',
            endtime:''      
          },
          edit_gain_compen: {
            newreason: '',
            newstarttime: '',
            newendtime:''
          },
          usecompen: {
            reason: '',
            starttime: '',
            endtime:''      
          },
          // validatememoid:false,
          tableKey: 0,
          filtered_list: null,
          usecompen_filtered_list: null,
          gaincompenlist: null,
          usecompen_list: null,
          validatememoid: null,
          // total: 0,
          listLoading: true,
          listQuery:{
            page: 1,
            limit: 20,
            sort: '+id'
          },
          uc_listQuery:{
            page: 1,
            limit: 20,
            sort: '+id'
          },
          selectedrow:'',
          edit_gaincompen_dialog_form_visible: false,
          gain_compen_dialog_form_visible: false,
          edit_usecompen_dialog_form_visible: false,
          new_usecompen_dialog_form_visible: false,
          rules: {
            // role: { required: true, message: 'Role is required', validator:role_validator},
            // reason: { required: true, trigger: 'change', validator:gaincompn_validator},
            reason: { required: true, trigger: 'blur', message: 'please input the reason.'},
            starttime: { required: true, trigger: 'blur', message: 'please selct a start time.'},
            endtime: { required: true, trigger: 'blur', message: 'please selct a end time.'},
            // password:{required: true, message: 'Password is required when creating local user',  validator:password_validator}
          },
          intervaljob:'',
          originData:'',
          wrapperLoading: false,
          monthValue:"",
          bandwidthChart: {},
          originTitle: ['User', 'Compen',],
          translateData: []
        }
      },
      computed: {
        total() {
          if(this.filtered_list){
            return this.filtered_list.length
          }
          else{
              return 0
          }
        },
        usecompen_total() {
          if(this.usecompen_filtered_list){
            return this.usecompen_filtered_list.length
          }
          else{
              return 0
          }
        }
      },
      created() {
        this.get_gaincompen_list()
        this.get_usecompen_list()
        this.get_avaliable_compen()
        // this.validate_memoid()
      },
    methods: {
        init_avaliable_compen() {
          let matrixData = this.originData.map((row) => {
          let arr = []
          for (let key in row) {
              arr.push(row[key])
          }
          return arr
          })
          this.translateData = matrixData[0].map((col, i) => {
              return [this.originTitle[i], ...matrixData.map((row) => {
                  return row[i]
              })]
          })
        },
        get_avaliable_compen() {
            GetAvaliableCompen(this.$store.getters.token).then(response => { 
              this.originData = response.data;
              this.init_avaliable_compen()
            })
        },
        get_gaincompen_list() {
          //get the user list
          this.listLoading = true
          ListGainCompen(this.$store.getters.token).then(response => {
            this.filtered_list = response.data
            let page = this.listQuery.page
            let limit = this.listQuery.limit
            let start , end
            if(page*limit>=this.total){
              start = (page-1)*limit
              end = this.total
            }
            else{
              start = (page-1)*limit
              end = page * limit
            }
            this.gaincompenlist = this.filtered_list.slice(start,end)
            this.listLoading = false
          })
          
        },
        get_usecompen_list() {
          this.listLoading = true
          ListUseCompen(this.$store.getters.token).then(response => {
            this.usecompen_filtered_list = response.data
            let page = this.uc_listQuery.page
            let limit = this.uc_listQuery.limit
            let start , end
            if(page*limit>=this.usecompen_total){
              start = (page-1)*limit
              end = this.usecompen_total
            }
            else{
              start = (page-1)*limit
              end = page * limit
            }
            this.usecompen_list = this.usecompen_filtered_list.slice(start,end)
            this.listLoading = false
          })
          
        },
        handledialogClose(){
          //set some vaule to be default once the dialog closed
          this.usecompen.starttime    = '',
          this.usecompen.reason       = '',
          this.usecompen.endtime      = '',
          this.gain_compen.starttime  = '',
          this.gain_compen.reason     = '',
          this.gain_compen.endtime    = ''
        },
        handledialogbeforeClose(done) {
          this.$confirm('Are you sure to close ?')
            .then(_ => {
              done();
            })
            .catch(_ => {});
        },
        remove_duplicate(arr) {
          //remove the duplicated ones
          const newArr = []
          arr.forEach(item => {
            if (!newArr.includes(item)) {
              newArr.push(item)
            }
          })
          return newArr
        },
        set_page(){
          // set the page with the page number
          let page = this.listQuery.page
          let limit = this.listQuery.limit
          let start , end
          if(page*limit>=this.total){
            start = (page-1)*limit
            end = this.total 
          }
          else{
            start = (page-1)*limit
            end = page * limit
          }
          this.gaincompenlist = this.filtered_list.slice(start,end)
        },
        set_usecompen_page(){
          // set the page with the page number
          let page = this.uc_listQuery.page
          let limit = this.uc_listQuery.limit
          let start , end
          if(page*limit>=this.usecompen_total){
            start = (page-1)*limit
            end = this.usecompen_total 
          }
          else{
            start = (page-1)*limit
            end = page * limit
          }
          this.usecompen_list = this.usecompen_filtered_list.slice(start,end)
        },
        sortChange(data) {
          // sorted table column
        const { prop, order } = data
        if(order==null){
          this.sortChange({prop:'id',order:'ascending'})
          return 
        }
        let flag_num = order=="ascending" ? 1 : -1
        this.filtered_list.sort((item1,item2)=>(
          (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
        ))
        this.set_page()
      },
      ucsortChange(data) {
          // sorted table column
        const { prop, order } = data
        if(order==null){
          this.ucsortChange({prop:'id',order:'ascending'})
          return 
        }
        let flag_num = order=="ascending" ? 1 : -1
        this.usecompen_filtered_list.sort((item1,item2)=>(
          (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
        ))
        this.set_usecompen_page()
      },
      handleFilter() {
        this.listQuery.page = 1
      },
      sortByID(order) {
        if (order === 'ascending') {
          this.listQuery.sort = '+id'
        } else {
          this.listQuery.sort = '-id'
        }
        this.handleFilter()
      },
      handleucFilter() {
        this.uc_listQuery.page = 1
      },
      ucsortByID(order) {
        if (order === 'ascending') {
          this.uc_listQuery.sort = '+id'
        } else {
          this.uc_listQuery.sort = '-id'
        }
        this.handleucFilter()
      },
      handle_gaincompen_add_dialog() {
        this.gain_compen_dialog_form_visible = true
        this.$nextTick(() => {
          this.$refs['gaincompenform'].clearValidate()
        })
      },
      handle_usecompen_add_dialog() {
        this.new_usecompen_dialog_form_visible = true
        this.$nextTick(() => {
          this.$refs['usecompenForm'].clearValidate()
        })
      },
      async handle_gaincompen_edit_dialog() {
        if(!this.selectedrow){
          this.$message({
              type: 'warning',
              message: 'Select a Gain compen record.',
              duration: 4000
            });  
          return 
        }
        if (!this.validatememoid) {
          this.$alert("You don't have access to edit compen of other people! ", "Alert", {
              confirmButtonText: "Confirm",
          });
          return false
        } else {
            this.edit_gaincompen_dialog_form_visible = true
            // this.$nextTick(() => {
            //   this.$refs['editgaincompen'].clearValidate()
            // })
            this.gain_compen.starttime = this.selectedrow.start_date
            this.gain_compen.reason = this.selectedrow.reason
            this.gain_compen.endtime = this.selectedrow.end_date
        }
      },
      handle_usecompen_edit_dialog() {
        if(!this.selectedrow){
          this.$message({
              type: 'warning',
              message: 'Select a Use Compen record.',
              duration: 4000
            });  
          return 
        }
        if (!this.validatememoid) {
          this.$alert("You don't have access to edit compen of other people! ", "Alert", {
              confirmButtonText: "Confirm",
          });
          return false
        } else {
            this.edit_usecompen_dialog_form_visible = true
            this.usecompen.starttime = this.selectedrow.start_date
            this.usecompen.reason = this.selectedrow.reason
            this.usecompen.endtime = this.selectedrow.end_date
          }
      },
      handle_gaincompen_delete(){
        //delete the selected compen record
        // this.validate_memoid()
        if (!this.validatememoid) {
          this.$alert("You don't have access to delete compen of other people! ", "Alert", {
              confirmButtonText: "Confirm",
          });
          return false
        } else {
          let payload = {
            data: { id: this.selectedrow.id },
            token: this.$store.getters.token
          }
          this.$confirm(`Deleting the compen record for: ${this.selectedrow.username}`, 'Delete Compen', {
          confirmButtonText: 'YES',
          cancelButtonText: 'NO',
          type: 'danger'
          }).then(() => {
              DeleteGainCompen(payload).then(() => {
              this.$notify({
                  title: 'Success',
                  message: `Successfully deleted compen record for: ${this.selectedrow.username} .`,
                  type: 'success',
                  duration: 2000
                })
                this.get_gaincompen_list()
                this.get_avaliable_compen()
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: `Failed to delete the compen record for: ${this.selectedrow.username} .`
              });
          });
        }
      },
      handle_usecompen_delete(){
        //delete the selected user
        if (!this.validatememoid) {
          this.$alert("You don't have access to delete compen of other people! ", "Alert", {
              confirmButtonText: "Confirm",
          });
          return false
        }else{
          let payload = {
            data:{id:this.selectedrow.id},
            token: this.$store.getters.token
          }
          this.$confirm(`Deleting the compen record for: ${this.selectedrow.username}`, 'Delete Compen', {
              confirmButtonText: 'YES',
              cancelButtonText: 'NO',
              type: 'danger'
            }).then(() => {
              DeleteUseCompen(payload).then(()=>{
                this.$notify({
                      title: 'Success',
                      message: `Successfully deleted compen record for: ${this.selectedrow.username} .` ,
                      type: 'success',
                      duration: 2000
                    })
                  this.get_usecompen_list()
                  this.get_avaliable_compen()
              })
            }).catch(() => {
              this.$message({
                type: 'info',
                message: `Failed to delete the compen record: ${this.selectedrow.username} .`
              });          
            });
          }
      },
      handle_gaincompen_edit(){
        //edit the selected user
        if (!this.get_compen()) {
          return
        }
        let payload = {
          token: this.$store.getters.token,
          data: {
            id: this.selectedrow.id,
            reason: this.gain_compen.reason,
            start_date: this.gain_compen.starttime,
            end_date: this.gain_compen.endtime,
            times_1_hours: this.compen_info.one_time,
            times_1p5_hours: this.compen_info.one_and_half_time,
            times_2_hours: this.compen_info.two_time,
            gain_hours: this.compen_info.total_time,
            total_hours: this.compen_info.one_time + this.compen_info.one_and_half_time + this.compen_info.two_time,
            detail: ' '
          }
        }
        this.edit_gaincompen_dialog_form_visible = false
        UpdateGainCompen(payload).then(() => {
          this.$notify({
            title: 'Success',
            message: `Successfully updated the record, total hours: ${this.selectedrow.username} .`,
            type: 'success',
            duration: 2000
          })
          
          this.get_gaincompen_list()
          this.get_avaliable_compen()
        }).catch(() => {
          this.$message({
            title: 'Error',
            type: 'error',
            message: `Failed to update the record. .`
            });
        });
      },
      handle_usecompen_edit(){
        //edit the selected user
        let _hours = this.calculate_usedcompen()
        if (!_hours) {
          return
        } else {
          let payload = {
            data: {
                id: this.selectedrow.id,
              reason: this.usecompen.reason,
              start_date: this.usecompen.starttime,
              end_date: this.usecompen.endtime,
                total_hours: _hours,
                detail: ' '
            },
            token: this.$store.getters.token,
          }
          this.edit_usecompen_dialog_form_visible = false
          UpdateUseCompen(payload).then(() => {
            this.$notify({
              title: 'Success',
              message: `Successfully Edited the User: ${this.selectedrow.username} .`,
              type: 'success',
              duration: 5000
            })
            
            this.get_usecompen_list()
            this.get_avaliable_compen()
          }).catch(() => {
            this.$message({
              title: 'Error',
              type: 'error',
              message: `Failed to Edited the User: ${this.selectedrow.username} .`
              });
          });
        }
      },
      handle_row_click(row,column,event){
        this.selectedrow = row
        this.validate_memoid()
      },
        formatJson(filterVal) {
          return this.list.map(v => filterVal.map(j => {
            if (j === 'timestamp') {
              return parseTime(v[j])
            } else {
              return v[j]
            }
          }))
        },
        getSortClass: function(key) {
          const sort = this.listQuery.sort
          return sort === `+${key}` ? 'ascending' : 'descending'
        },
        gaincompen(){
          //create new user
          // let newsa = this.new_user.ifnewaccount == '1'? false : true
          this.$refs['gaincompenform'].validate((valid)=>{
            if(!valid){
              return
            }
            if (!this.get_compen()) {
              return
            }
            let payload = {
                token: this.$store.getters.token,
                data :{
                  reason: this.gain_compen.reason,
                  start_date: this.gain_compen.starttime,
                  end_date: this.gain_compen.endtime,
                  times_1_hours:this.compen_info.one_time,
                  times_1p5_hours:this.compen_info.one_and_half_time,
                  times_2_hours:this.compen_info.two_time,
                  gain_hours:this.compen_info.total_time,
                  total_hours:this.compen_info.one_time+this.compen_info.one_and_half_time+this.compen_info.two_time,
                  detail:' '
                }
              }
            this.gain_compen_dialog_form_visible = false
            AddGainCompen(payload)
              .then(() => {
                this.$notify({
                  title: 'Success',
                  message: `Gain Compen :${this.compen_info.total_time}.`,
                  type: 'success',
                  duration: 5000
                })
                this.get_gaincompen_list()
                this.get_avaliable_compen()
              })
              .catch((error) => {
                let error_message
                if(error.response.data.message){
                  error_message = error.response.data.message
                }
                else if (error.response.data.errors){
                  error_message = error.response.data.errors.json._schema
                }
                this.gain_compen_dialog_form_visible  = false
                this.$notify({
                  title: 'Error',
                  message: `Failed to add gain compen. Reason: ${error_message}`,
                  type: 'error',
                  duration: 5000
                })
              })
          })
        },
        use_compen(){
          let _hours = this.calculate_usedcompen()
          if(!_hours){
              return 
          }else{
            let payload = {
              data:{        
                reason: this.usecompen.reason,
                start_date: this.usecompen.starttime,
                end_date: this.usecompen.endtime,
                total_hours:_hours,
                detail:' '
              },
              token: this.$store.getters.token
            }
            this.$refs['usecompenForm'].validate((valid) => {
              if (valid) {
                this.new_usecompen_dialog_form_visible = false
                AddUseCompen(payload)
                .then(() => {
                  this.$notify({
                    title: 'Success',
                    message: `Use compen record added successfully.`,
                    type: 'success',
                    duration: 2000
                  })
                  this.get_usecompen_list()
                  this.get_avaliable_compen()
                })
                .catch((error) => {
                  this.new_usecompen_dialog_form_visible  = false
                  this.$notify({
                    title: 'Error',
                    message: `Failed to add Use compen record: ${error}.`,
                    type: 'error',
                    duration: 2000
                  })
                })
              }
            })
            return 0
          }
        },
        get_compen(){
          let h1=0, h2=0, h15=0, res
          let startdate = this.gain_compen.starttime
          let enddate   = this.gain_compen.endtime
          if((startdate=='')||(enddate=='')){
              this.$alert("Missing Date , please modify! ", "Alert", {
                  confirmButtonText: "Confirm",
              });
              return false
          }
          let sd = new Date(startdate)
          let ed = new Date(enddate)
          let _sdd = sd.getDate()
          let _sdm = sd.getMonth()
          let _sdy = sd.getFullYear()
          let _sd = new Date(_sdy, _sdm, _sdd)
          let _edd = ed.getDate()
          let _edm = ed.getMonth()
          let _edy = ed.getFullYear()
          let _ed = new Date(_edy, _edm, _edd)
          let diff  = (new Date( ed -  sd))/ 1000 /24/60/60  // 时间上的差距，天
          let _diff = (new Date(_ed - _sd))/ 1000 /24/60/60  // 时间上的差距， 天
          if(sd >= ed){
              this.$alert("Start date should not be later than end date, please modify! ", "Alert", {
              confirmButtonText: "Confirm",
              });
              return false
          }
          else{
            if (_diff == 0) {//start end 是同一天
              res = this.calculate_compen(sd,ed)
              h1 += res[1]
              h2 += res[2]
              h15 += res[1.5]
            }
            else {
              let res_head = this.calculate_compen(sd, this.add_days(sd, 1))
              h1 += res_head[1]
              h2 += res_head[2]
              h15 += res_head[1.5]
              for(var i = 1 ;i<_diff;i++){
                  let res_body = this.calculate_compen(this.add_days(sd, (1 + i - 1)), this.add_days(sd, (1 + i)))
                  h1 += res_body[1]
                  h2 += res_body[2]
                  h15 += res_body[1.5]
              }
              let res_tail = this.calculate_compen(_ed,ed)
              h1 += res_tail[1]
              h2 += res_tail[2]
              h15 += res_tail[1.5]
            }
            this.compen_info.one_time = (Math.round(h1*100))/100
            this.compen_info.one_and_half_time = (Math.round(h15*100))/100
            this.compen_info.two_time = (Math.round(h2*100))/100
            this.compen_info.total_time = (Math.round((this.compen_info.one_time + this.compen_info.one_and_half_time * 1.5 + this.compen_info.two_time * 2) * 100)) / 100
            return true
          }
        },
        calculate_compen(sd,ed){
          let result ={
              1   :    0,
              1.5 :    0,
              2   :    0
          }
          let time,_rule
          let _morning_hour = 6
          let y = sd.getFullYear()
          let m = sd.getMonth()
          let d = sd.getDate()
          let counting_date = new Date(y,m,d,_morning_hour) //早上6点 yyyy-MM-dd 06:00 AM
          let day = sd.getDay()//获取星期几， 0 是周日， 1是周一....
          if(sd >= counting_date){// 开始时间大于 早上6点
              _rule = this.compen_calculate_rules[day]['after_6']
              time = new Date(ed-sd)/1000 /60/60 // 获取相差的小时数
              result[_rule] += time
          }
          else if(ed <= counting_date){// 结束时间小于 早上6点
              _rule = this.compen_calculate_rules[day]['before_6']
              time = new Date(ed-sd)/1000 /60/60 // 获取相差的小时数
              result[_rule] += time
          }
          else{// 开始时间小于 早上6点 且 结束时间大于早上6 点
              time = new Date(counting_date-sd)/1000 /60/60 // 获取6点前的小时数
              _rule = this.compen_calculate_rules[day]['before_6']
              result[_rule] += time

              time = new Date(ed-counting_date)/1000 /60/60 // 获取6点前的小时数
              _rule = this.compen_calculate_rules[day]['after_6']
              result[_rule] += time
          }
          return result
        },
        calculate_usedcompen(){
          let startdate = this.usecompen.starttime
          let enddate = this.usecompen.endtime
          if((startdate=='')||(enddate=='')){
              this.$alert("Missing Date , please modify! ", "Alert", {
                  confirmButtonText: "Confirm",
              });
              return false
          }
          let sd = new Date(startdate)
          let ed = new Date(enddate)
          let _sdd = sd.getDate()
          let _sdm = sd.getMonth()
          let _sdy = sd.getFullYear()
          let _sd = new Date(_sdy, _sdm, _sdd)
          let _edd = ed.getDate()
          let _edm = ed.getMonth()
          let _edy = ed.getFullYear()
          let _ed = new Date(_edy, _edm, _edd)
            if (_sd.toJSON() != _ed.toJSON()) {
              this.$alert("Start/End time should be the same day! ", "Alert", {
                  confirmButtonText: "Confirm",
              });
              return false
          }
            if (sd >= ed) {
              this.$alert("Start date should not be later than end date, please modify!", "Alert", {
                  confirmButtonText: "Confirm",
              });
              return false
          }   
          else{
              let _diff = new Date(ed-sd)/ 1000/60/60
              _diff = (Math.round(_diff * 100).toFixed(2)) / 100
              return _diff
          }
        },
        get_start_date(){
            let y = this.getFullYear()
            let m = this.getMonth()
            let d = this.getDate()
            return new Date(y,m,d)
        },
        add_days(dt,days){
            let y = dt.getFullYear()
            let m = dt.getMonth()
            let d = dt.getDate()
            return new Date(y,m,d+days)
        },
        validate_memoid() {
          GetMemoID(this.$store.getters.token).then(response => {
            this.validatememoid = this.selectedrow.username == response.data ? true : false
          })
        }
      },
      // beforeDestroy(){
      //   clearInterval( this.intervaljob )
      // }
    }
  </script>
  <style scoped>
  .compen-table span{
    font-size: 17px
  }
  
  .el-row {
    margin-bottom: 20px;
  }

  .el-row:last-child {
    margin-bottom: 0;
  }

  .el-col {
    border-radius: 4px;
  }
  
  .box-card-left {
    width: 55%;
    max-width: 80%;
    float: left;
    margin-left: 30px auto;
    border-radius: 6px;
  }

  .box-card-right {
    width: 43%;
    float: left;
    max-width: 80%;
    margin-left: 15px;
    border-radius: 6px;
  }
  .box-card-top {
    width: 99%;
    float: left;
    max-width: 160%;
    margin-left: 30px auto;
    margin-bottom: 8px;
    border-radius: 6px;
  }

  </style>
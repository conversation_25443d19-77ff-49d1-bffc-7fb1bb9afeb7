from flask import Flask
from flask_socketio import So<PERSON><PERSON>, join_room, send
from flask_cors import CORS
import paramiko
import traceback
import sys
import time
from threading import Thread
from business.generic.commonfunc import DBConfig
from models.database import db
from business.authentication.authentication import Vault
from business.authentication.tokenvalidation import PrivilegeValidation


socketio = SocketIO()
app = Flask(__name__)
CORS(app)
app.config["SECRET_KEY"] = 'secret'
app.config['CORS_SUPPORTS_CREDENTIALS'] = True
app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
app.config['SQLALCHEMY_TRACK_MODIFACATIONS'] = False

app.app_context().push()
db.init_app(app)  # db is in models.py
SSH_CONNECTIONS = {}

# tran = paramiko.Transport("10.235.62.200",22)
# tran.start_client()
# tran.auth_password('nutanix', 'wNmW|):(A0Tw2slT')
# chan = tran.open_session()
# chan.get_pty(height=492,width=1312)
# chan.invoke_shell()


@PrivilegeValidation(privilege={"role_lcm": "view_atm"})
@socketio.on('connect', namespace='/ws')
def hello_world():
    print(111)
    return 1


@PrivilegeValidation(privilege={"role_lcm": "view_atm"})
@socketio.on('join', namespace='/ws')
def on_join(data):
    room = data['room']
    print(data)
    join_room(room)
    socketio.emit('message', {'data': f'welcome to room {room}'}, room=room)


@PrivilegeValidation(privilege={"role_lcm": "view_atm"})
@socketio.on("message", namespace = '/ws')
def send(message):
    print(message)
    chan_id  = message['chan']
    room = message['room']
    Thread(target=lambda: read_output(chan_id, room)).start()
    SSH_CONNECTIONS[message['chan']].send(message['data'])
    if(SSH_CONNECTIONS[message['chan']].recv_ready()):
        d = SSH_CONNECTIONS[message['chan']].recv(99999)
    else:
        d = b""
    # chan.send(message)
    # time.sleep(1)
    # ret = chan.recv(9999)
    send({'chan': message['chan'], 'data': d.decode("UTF-8")}, namespace='/ws', to=room, include_self=True,)
    # socketio.emit("response",
    #                 {'chan':message['chan'],'data':d.decode("UTF-8")},
    #                 namespace='/ws',
    #                 # to=message['room'],
    #                 include_self=True,
    #                 # room=message['room']
    #                 )


@PrivilegeValidation(privilege={"role_lcm": "view_atm"})
@socketio.on("disconnect", namespace='/ws')
def disconnect():
    print("connection lost.")


@PrivilegeValidation(privilege={"role_lcm": "view_atm"})
@socketio.on("init_ssh", namespace = '/ws')
def init_ssh(data):
    print(data)
    try:
        pe    = data['pe']
        _type = data['type']
        chan_id  = data['chan']
        room = data['room']
        _vault = Vault(tier="PRODUCTION")
        _, secret = _vault.get_secret(f"{pe.upper()}/Site_Pe_Nutanix")
        print(secret)
        tran  = paramiko.Transport(pe, 22)
        tran.start_client()
        tran.auth_password(secret['username'], secret['secret'])
        chan = tran.open_session()
        chan.get_pty(height=492, width=1312)
        chan.invoke_shell()
        SSH_CONNECTIONS[chan_id] = chan
        SSH_CONNECTIONS[chan_id].send('\r\n')
        Thread(target=lambda: read_output(chan_id, room)).start()
    except Exception as _:
        print(str(repr(traceback.format_exception(sys.exception()))))
        pass
    

def read_output(channel, room):
    i = 0
    while i < 1000:
        print(i)
        try:
            if SSH_CONNECTIONS[channel].recv_ready():
                res = SSH_CONNECTIONS[channel].recv(99999)
                if res:
                    print(f'thread:{res}')
                    socketio.emit("response",
                            {'chan': channel, 'data': res.decode("UTF-8")},
                            namespace='/ws',
                            room=room)
                    i = 0
            time.sleep(0.01)
            i += 1
        except:
            print("/")


socketio.init_app(app, cors_allowed_origins='*')

if __name__ == '__main__':
    socketio.run(app, host="0.0.0.0", port=5001, log_output=True, ssl_context=('flask.cer', 'flask.key'))
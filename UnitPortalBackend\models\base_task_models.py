from models.database import db


class ModelBaseTask(db.Model):
    __abstract__        = True
    id                  = db.Column(db.Integer, primary_key=True)
    create_date         = db.Column(db.String(100))
    status              = db.Column(db.String(100))
    pid                 = db.Column(db.Integer)
    creater             = db.Column(db.String(100))
    detail_log_path     = db.Column(db.String(255))


class ModelBaseTaskLog(db.Model):
    __abstract__        = True
    id                  = db.Column(db.Integer, primary_key=True)
    task_id             = db.Column(db.Integer)
    log_date            = db.Column(db.String(255))
    severity            = db.Column(db.String(255))
    log_info            = db.Column(db.String(8000))


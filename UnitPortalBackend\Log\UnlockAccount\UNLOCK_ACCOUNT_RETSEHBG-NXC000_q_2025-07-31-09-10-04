2025-07-31 17:10:04,273 INFO Start to run the task.
2025-07-31 17:10:04,310 INFO Starting to unlock account for PE: RETSEHBG-NXC000
2025-07-31 17:10:04,350 INFO Getting PE information
2025-07-31 17:10:04,470 INFO Found PE in retail: retsehbg-nxc000.ikea.com, PC: ssp-eu-ntx.ikea.com
2025-07-31 17:10:04,507 INFO Executing account unlock operation
2025-07-31 17:10:04,544 INFO Connecting to Nutanix cluster...
2025-07-31 17:10:06,214 INFO Unlocking accounts...
2025-07-31 17:10:06,214 INFO Trying to SSH to the pe retsehbg-nxc000.
2025-07-31 17:10:06,225 INFO SSH connecting to retsehbg-nxc000.ikea.com, this is the '1' try.
2025-07-31 17:10:08,721 INFO SSH connected to retsehbg-nxc000.ikea.com.
2025-07-31 17:10:10,350 INFO SSH connecting to ssp-eu-ntx.ikea.com, this is the '1' try.
2025-07-31 17:10:12,861 INFO SSH connected to ssp-eu-ntx.ikea.com.
2025-07-31 17:10:13,980 INFO Unlocking PC account: 'admin'
2025-07-31 17:10:13,981 INFO Sending 'allssh sudo faillock --user admin --reset' to the server.
2025-07-31 17:10:18,982 INFO Successfully unlocked PC account: 'admin'
2025-07-31 17:10:18,982 INFO Unlocking PC account: 'nutanix'
2025-07-31 17:10:18,982 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.
2025-07-31 17:10:23,984 INFO Successfully unlocked PC account: 'nutanix'
2025-07-31 17:10:23,984 INFO Unlocking PC account: '1-click-nutanix'
2025-07-31 17:10:23,984 INFO Sending 'allssh sudo faillock --user 1-click-nutanix --reset' to the server.
2025-07-31 17:10:28,986 INFO Successfully unlocked PC account: '1-click-nutanix'
2025-07-31 17:10:30,038 INFO SSH connecting to retsehbg-nxc000.ikea.com, this is the '1' try.
2025-07-31 17:10:32,570 INFO SSH connected to retsehbg-nxc000.ikea.com.
2025-07-31 17:10:33,665 INFO Unlocking PE account: 'admin'
2025-07-31 17:10:33,666 INFO Sending 'allssh sudo faillock --user admin --reset' to the server.
2025-07-31 17:10:38,667 INFO Successfully unlocked PE account: 'admin'
2025-07-31 17:10:38,667 INFO Unlocking PE account: 'nutanix'
2025-07-31 17:10:38,667 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.
2025-07-31 17:10:43,669 INFO Successfully unlocked PE account: 'nutanix'
2025-07-31 17:10:43,670 INFO Unlocking PE account: '1-click-nutanix'
2025-07-31 17:10:43,670 INFO Sending 'allssh sudo faillock --user 1-click-nutanix --reset' to the server.
2025-07-31 17:10:48,671 INFO Successfully unlocked PE account: '1-click-nutanix'
2025-07-31 17:10:48,714 INFO Account unlocked successfully
2025-07-31 17:10:48,856 INFO Task is in 'Done' status.

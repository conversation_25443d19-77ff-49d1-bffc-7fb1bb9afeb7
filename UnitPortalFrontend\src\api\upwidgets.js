import { endpoint } from './endpoint'
import request from '@/utils/request'

export function GetCommentList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/upwidgets/feedback`,config)
  return res
}
export function CreatCommentRecord(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/upwidgets/feedback`,param.data,config)
  return res
}
export function UpdateCommentRecord(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.put(`${endpoint}/upwidgets/feedback`,param.data,config)
  return res
}
export function WebPageTracer(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/upwidgets/webpage`,param.data,config)
  return res
}
export function WebPageList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/upwidgets/webpage`,config)
  return res
}
export function APIUtilizationList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/upwidgets/apiutilization`,config)
  return res
}
export function APIUtilizationLogList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/upwidgets/apiutilizationlog`,config)
  return res
}

import re
from flask_restful import Resource
from sqlalchemy.sql import func
from sqlalchemy import Integer
from sqlalchemy import try_cast, cast
from business.authentication.tokenvalidation import PrivilegeValidation
from models.database import db
from models.ntx_models import ModelPrismCentral, ModelPrismElement, ModelRetailNutanixHost, ModelRetailNutanixVM
from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement, ModelWarehouseNutanixHost, ModelWarehouseNutanixVM
from models.sli_models import ModelSLIvCenter, ModelSLIHost, ModelSLIVMs, ModelSLICluster
from .route import route


@route('/api/v1/dashboard-diagram')
class RestfulDashboardDiagram(Resource):
    @PrivilegeValidation(privilege={"role_dashboard": "view"})
    def get(self):
        result = {
            "ntx": self._get_ntx(),
            "ntx_wiab": self._get_ntx_wiab(),
            "pie_model": self._get_pie_model(),
            "pie_os": self._get_pie_os(),
        }
        return result

    def _get_ntx(self):
        return {
            "pc": db.session.query(func.count(ModelPrismCentral.id)).scalar(),
            "pe": db.session.query(func.count(ModelPrismElement.id)).scalar(),
            "host": db.session.query(func.count(ModelRetailNutanixHost.id)).scalar(),
            "vm": db.session.query(func.count(ModelRetailNutanixVM.id)).scalar(),
            "cpu": db.session.query(func.sum(cast(ModelPrismElement.total_cpu, Integer))).filter_by(status='Running').scalar(),     # noqa
            "memory": self._convert_GiB_to_TB(sum([int(m) for (m,) in db.session.query(ModelPrismElement.total_memory).all() if m])),     # noqa
        }
    
    def _get_ntx_wiab(self):
        return {
            "pc": db.session.query(func.count(ModelWarehousePrismCentral.id)).scalar(),
            "pe": db.session.query(func.count(ModelWarehousePrismElement.id)).scalar(),
            "host": db.session.query(func.count(ModelWarehouseNutanixHost.id)).scalar(),
            "vm": db.session.query(func.count(ModelWarehouseNutanixVM.id)).scalar(),
            "cpu": db.session.query(func.sum(cast(ModelWarehousePrismElement.total_cpu, Integer))).filter_by(status='Running').scalar(),     # noqa
            "memory": self._convert_GiB_to_TB(sum([int(m) for (m,) in db.session.query(ModelWarehousePrismElement.total_memory).all() if m])),     # noqa
        }

    def _convert_GiB_to_TB(self, num):      # pylint: disable=C0103
        return int(num * (1024 ** 3) / (1000 ** 4))

    def _get_sli(self):
        return {
            "vCenter": db.session.query(func.count(ModelSLIvCenter.id)).scalar(),
            "cluster": db.session.query(func.count(ModelSLICluster.id)).scalar(),
            "host": db.session.query(func.count(ModelSLIHost.id)).scalar(),
            "vm": db.session.query(func.count(ModelSLIVMs.id)).scalar(),
            "cpu": db.session.query(func.sum(try_cast(ModelSLIHost.num_cpu, Integer))).scalar(),
            "memory": self._convert_GiB_to_TB(db.session.query(func.sum(try_cast(ModelSLIHost.memory_total_gb, Integer))).scalar())     # noqa
        }

    def _get_pie_os(self):
        # ntx
        ntx_os_count = db.session.query(
            ModelRetailNutanixVM.os, func.count(ModelRetailNutanixVM.os)
        ).distinct(ModelRetailNutanixVM.os).group_by(ModelRetailNutanixVM.os).all()
        # sli
        sli_os_count = db.session.query(ModelSLIVMs.name).all()
        network_appliance_count, linux_count, windows_count = 0, 0, 0
        for (name,) in sli_os_count:
            if re.match(r'[A-Z]{3}w{1}\-\w+\-[a-zA-Z]+\-[a-zA-Z0-9]+', name):
                network_appliance_count += 1
            elif re.match(r'\w+\-lx\w+', name):
                linux_count += 1
            elif re.match(r'\w+\-(NT|nt)\w+', name):
                windows_count += 1
        return {
            "ntx": {
                os: count for (os, count) in ntx_os_count if "AOS" not in os.upper()
            },
            "sli": {
                "Network Appliance": network_appliance_count,
                "Linux": linux_count,
                "Windows": windows_count
            }
        }

    def _get_pie_model(self):
        ntx_model_count = db.session.query(
            ModelRetailNutanixHost.model, func.count(ModelRetailNutanixHost.model)
        ).distinct(ModelRetailNutanixHost.model).group_by(ModelRetailNutanixHost.model).all()
        sli_model_count = db.session.query(
            ModelSLIHost.model, func.count(ModelSLIHost.model)
        ).distinct(ModelSLIHost.model).group_by(ModelSLIHost.model).all()
        return {
            "ntx": dict(ntx_model_count),
            "sli": dict(sli_model_count),
        }

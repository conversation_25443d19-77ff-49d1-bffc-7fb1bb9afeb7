let Service = require('node-windows').Service;

let svc = new Service({
    name:'Unit Portal Frontend',
    description: 'Unit portal Frontend',
    script : 'D:\\UP-Curry\\UnitPortalFrontend\\node_modules\\serve\\bin\\serve.js',
    scriptOptions: 'D:\\UP-Curry\\UnitPortalFrontend\\dist -l 9527 --ssl-cert vue1.cer --ssl-key vue1.key',
    wait: '1',
    grow: '0.25',
    maxRestarts: '40'
});

svc.on('install',()=>{
    svc.start();
    console.log('install complete.')
});

svc.on('uninstall',()=>{
    console.log('uninstall...');
    console.log('The service exists:', svc.exists)
});


svc.on('alreadyinstalled',()=>{
    console.log('This service is already installed.')
});

if(svc.exists) return svc.uninstall();
svc.install();
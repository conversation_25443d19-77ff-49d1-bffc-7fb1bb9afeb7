import ntnx_volumes_py_client
import uuid
import datetime

from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
from business.authentication.authentication import Vault
from business.generic.base_up_exception import VaultGetSecretFailed
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.distributedhosting.nutanix.base_up_task_property import BaseUpTaskProperty
from models.metro_vg_models import ModelMetroVGTask, ModelMetroVGTaskSchema, ModelMetroVGTaskLog, ModelMetroVGTaskLogSchema

from business.generic.commonfunc import setup_common_logger, create_file
from static.SETTINGS import METRO_VG_LOG_PATH


class WiabMetroAddIscsiClient(BaseUpTask):
    LOG_DIR = METRO_VG_LOG_PATH
    TASK_TYPE = "ADD_ISCSI_CLIENTS_TO_VG"

    def __init__(self, payload):
        super().__init__(
            ModelMetroVGTask, ModelMetroVGTaskSchema, ModelMetroVGTaskLog, ModelMetroVGTaskLogSchema, payload
        )
        self.payload = payload
        self.primary_ip = payload.get("primary_ip")
        self.secondary_ip = payload.get("secondary_ip")
        self.vg_ext_id = payload.get("vg_ext_id")
        self.pe = payload.get("pe")
        self.pc_svc_account = self.init_svc_account()
        self.task_info = {
            BaseUpTaskProperty.PE:          payload.get("pe"),
        }
        self.task_duplicated_kwargs = {
            "pe": payload.get("pe")
        }
        self.task_identifier = payload.get("pe")
        self.log_path = create_file(filepath=METRO_VG_LOG_PATH, filename=f'ADD_ISCSI_CLIENTS_TO_VG_{datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d_%H:%M:%S")}')
        self.logger = setup_common_logger(str(uuid.uuid4()), self.log_path)
        self.volumes_client = None
        self.primary_site = "PRIMARY"
        self.secondary_site = "SECONDARY"

    def init_svc_account(self):
        def get_central_pe_name():
            prism = ModelWarehousePrismElement.query.filter_by(name=self.payload.get("pe")).first().prism
            central_pe_fqdn = ModelWarehousePrismCentral.query.filter_by(fqdn=prism).first().central_pe_fqdn
            central_pe_name = ModelWarehousePrismElement.query.filter_by(fqdn=central_pe_fqdn).first().name
            return prism, central_pe_name
        prism, central_pe_name = get_central_pe_name()
        vault = Vault(tier=ModelWarehousePrismCentral.query.filter_by(fqdn=prism).first().tier)
        pc_svc_label = f"{central_pe_name.upper()}/Site_Pc_Svc"
        res, data = vault.get_secret(pc_svc_label)
        if not res:
            raise VaultGetSecretFailed(pc_svc_label)
        pc_svc_account = {"username": data["username"], "password": data["secret"]}
        return pc_svc_account

    def init_api_client_config(self, config):
        config.host = ModelWarehousePrismElement.query.filter_by(name=self.payload.get("pe")).first().prism
        config.port = 9440  # Port to which to connect to
        config.username = self.pc_svc_account['username']  # UserName to connect to the cluster
        config.password = self.pc_svc_account['password']  # Password to connect to the cluster
        config.verify_ssl = True  # TODO: set to True for prod
        return config

    def init_ntx_clients(self):
        self.logger.info("Initializing API client(s)...")
        self.volumes_client = ntnx_volumes_py_client.ApiClient(self.init_api_client_config(ntnx_volumes_py_client.Configuration()))

    def add_iscsi_client(self, ip, vg_ext_id, attachment_site):
        self.logger.info(f"Attaching client {ip} to volume group {vg_ext_id}")
        volume_groups_api = ntnx_volumes_py_client.VolumeGroupsApi(api_client=self.volumes_client)
        iscsi_client = ntnx_volumes_py_client.IscsiClient()

        iscsi_client = {
            "iscsiInitiatorNetworkId": {
                "ipv4": {
                    "value": ip,
                    "prefixLength": 32}
            },
            "enabledAuthentications": "NONE",
            "numVirtualTargets": 3,
            "attachmentSite": attachment_site,
        }

        try:
            volume_groups_api.attach_iscsi_client(extId=vg_ext_id, body=iscsi_client)
        except ntnx_volumes_py_client.rest.ApiException as e:
            self.logger.error("Exception occurred: %s", e, exc_info=True)

    def task_process(self):
        self.init_ntx_clients()
        self.add_iscsi_client(self.primary_ip, self.vg_ext_id, self.primary_site)
        self.add_iscsi_client(self.secondary_ip, self.vg_ext_id, self.secondary_site)
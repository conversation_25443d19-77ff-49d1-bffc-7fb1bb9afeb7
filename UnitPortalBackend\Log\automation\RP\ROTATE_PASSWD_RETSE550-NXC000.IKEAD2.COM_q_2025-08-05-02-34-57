2025-08-05 10:34:57,758 INFO Start to run the task.
2025-08-05 10:35:01,019 INFO Checking Maintenance Mode via v2.0 API (/hosts)
2025-08-05 10:35:01,030 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/hosts, method: GET, headers: None
2025-08-05 10:35:01,031 INFO params: None
2025-08-05 10:35:01,031 INFO User: 1-click-nutanix
2025-08-05 10:35:01,031 INFO payload: None
2025-08-05 10:35:01,031 INFO files: None
2025-08-05 10:35:01,031 INFO timeout: 30
2025-08-05 10:35:02,563 INFO API Check: All good, no hosts or CVMs are in maintenance mode.
2025-08-05 10:35:02,566 INFO Checking CVM status
2025-08-05 10:35:03,047 INFO Trying to SSH to the RETSE550-NXC000.IKEAD2.COM.
2025-08-05 10:35:03,047 INFO First try with username/password.
2025-08-05 10:35:03,047 INFO SSH connecting to RETSE550-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 10:35:05,572 INFO SSH connected to RETSE550-NXC000.IKEAD2.COM.
2025-08-05 10:35:11,702 INFO Sending 'cluster status |grep -v UP' to the server.
2025-08-05 10:35:27,705 INFO CVM IP:************* Status:Up
2025-08-05 10:35:27,705 INFO CVM IP:************* Status:Up
2025-08-05 10:35:27,705 INFO CVM IP:************* Status:Up
2025-08-05 10:35:27,705 INFO Great, all CVM status are Up
2025-08-05 10:35:27,728 INFO Calling restapi, URL: https://retse550-nxc000.ikead2.com:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 10:35:27,728 INFO params: None
2025-08-05 10:35:27,728 INFO User: <EMAIL>
2025-08-05 10:35:27,728 INFO payload: None
2025-08-05 10:35:27,728 INFO files: None
2025-08-05 10:35:27,728 INFO timeout: 30
2025-08-05 10:35:28,969 INFO Getting host list from retse550-nxc000.
2025-08-05 10:35:28,969 INFO Got the host list from retse550-nxc000.
2025-08-05 10:35:28,969 INFO Got the host list.
2025-08-05 10:35:28,969 INFO Getting vault from IKEAD2.
2025-08-05 10:35:30,184 INFO Getting Site_Pe_Nutanix.
2025-08-05 10:35:30,867 INFO Got Site_Pe_Nutanix.
2025-08-05 10:35:30,867 INFO Getting Site_Pe_Admin.
2025-08-05 10:35:31,404 INFO Got Site_Pe_Admin.
2025-08-05 10:35:31,404 INFO Getting Site_Oob.
2025-08-05 10:35:32,097 INFO Got Site_Oob.
2025-08-05 10:35:32,097 INFO Getting Site_Ahv_Nutanix.
2025-08-05 10:35:32,982 INFO Got Site_Ahv_Nutanix.
2025-08-05 10:35:32,982 INFO Getting Site_Ahv_Root.
2025-08-05 10:35:33,570 INFO Got Site_Ahv_Root.
2025-08-05 10:35:33,571 INFO Getting Site_Gw_Priv_Key.
2025-08-05 10:35:34,046 INFO Got Site_Gw_Priv_Key.
2025-08-05 10:35:34,046 INFO Getting Site_Gw_Pub_Key.
2025-08-05 10:35:34,552 INFO Got Site_Gw_Pub_Key.
2025-08-05 10:35:34,552 INFO Getting Site_Pe_Svc.
2025-08-05 10:35:35,048 INFO Got Site_Pe_Svc.
2025-08-05 10:35:35,064 INFO Checking if cluster 'RETSE550-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-05 10:35:35,180 INFO Connecting to CVM ************* for AHV password updates
2025-08-05 10:35:35,180 INFO SSH connecting to *************, this is the '1' try.
2025-08-05 10:35:37,171 INFO SSH connected to ************* with SSHKEY.
2025-08-05 10:35:38,283 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 10:35:43,297 INFO Start reset AHV user nutanix password from CVM *************
2025-08-05 10:35:43,297 INFO unlocking nutanix account
2025-08-05 10:35:43,297 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 10:35:54,314 INFO Sending '*' to the server.
2025-08-05 10:36:09,826 INFO AHV User nutanix Password Update Success
2025-08-05 10:36:09,835 INFO Start reset AHV user root password from CVM *************
2025-08-05 10:36:09,847 INFO unlocking root account
2025-08-05 10:36:09,848 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 10:36:20,349 INFO Sending '*' to the server.
2025-08-05 10:36:35,866 INFO AHV User root Password Update Success
2025-08-05 10:36:35,880 INFO Connecting to CVM ************* for AHV password updates
2025-08-05 10:36:35,881 INFO SSH connecting to *************, this is the '1' try.
2025-08-05 10:36:37,940 INFO SSH connected to ************* with SSHKEY.
2025-08-05 10:36:39,029 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 10:36:44,043 INFO Start reset AHV user nutanix password from CVM *************
2025-08-05 10:36:44,052 INFO unlocking nutanix account
2025-08-05 10:36:44,052 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 10:36:55,054 INFO Sending '*' to the server.
2025-08-05 10:37:10,568 INFO AHV User nutanix Password Update Success
2025-08-05 10:37:10,588 INFO Start reset AHV user root password from CVM *************
2025-08-05 10:37:10,598 INFO unlocking root account
2025-08-05 10:37:10,598 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 10:37:21,100 INFO Sending '*' to the server.
2025-08-05 10:37:36,616 INFO AHV User root Password Update Success
2025-08-05 10:37:36,637 INFO Connecting to CVM ************* for AHV password updates
2025-08-05 10:37:36,638 INFO SSH connecting to *************, this is the '1' try.
2025-08-05 10:37:38,635 INFO SSH connected to ************* with SSHKEY.
2025-08-05 10:37:39,754 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 10:37:44,768 INFO Start reset AHV user nutanix password from CVM *************
2025-08-05 10:37:44,778 INFO unlocking nutanix account
2025-08-05 10:37:44,778 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 10:37:55,780 INFO Sending '*' to the server.
2025-08-05 10:38:11,294 INFO AHV User nutanix Password Update Success
2025-08-05 10:38:11,316 INFO Saving token to Vault... Username: nutanix, label: RETSE550-NXC000/Site_Ahv_Nutanix
2025-08-05 10:38:11,951 INFO Saving token completed.
2025-08-05 10:38:11,990 INFO Start reset AHV user root password from CVM *************
2025-08-05 10:38:12,001 INFO unlocking root account
2025-08-05 10:38:12,001 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 10:38:22,502 INFO Sending '*' to the server.
2025-08-05 10:38:38,018 INFO AHV User root Password Update Success
2025-08-05 10:38:38,028 INFO Saving token to Vault... Username: root, label: RETSE550-NXC000/Site_Ahv_Root
2025-08-05 10:38:38,698 INFO Saving token completed.
2025-08-05 10:38:38,711 INFO Starting to reset iLO password for user 'administrator' across all hosts.
2025-08-05 10:38:38,722 INFO Updating host RETSE550-NX7001 (*************)
2025-08-05 10:38:38,734 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-05 10:38:38,735 INFO Finding account URI for user 'administrator'.
2025-08-05 10:38:38,735 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 10:38:38,735 INFO params: None
2025-08-05 10:38:38,735 INFO User: administrator
2025-08-05 10:38:38,735 INFO payload: None
2025-08-05 10:38:38,736 INFO files: None
2025-08-05 10:38:38,736 INFO timeout: None
2025-08-05 10:38:39,871 INFO Got the response with OK
2025-08-05 10:38:39,872 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 10:38:39,872 INFO params: None
2025-08-05 10:38:39,872 INFO User: administrator
2025-08-05 10:38:39,872 INFO payload: None
2025-08-05 10:38:39,872 INFO files: None
2025-08-05 10:38:39,872 INFO timeout: None
2025-08-05 10:38:41,084 INFO Got the response with OK
2025-08-05 10:38:41,086 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 10:38:41,087 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 10:38:41,087 INFO params: None
2025-08-05 10:38:41,087 INFO User: administrator
2025-08-05 10:38:41,087 INFO payload: {'Password': '*****'}
2025-08-05 10:38:41,087 INFO files: None
2025-08-05 10:38:41,088 INFO timeout: None
2025-08-05 10:38:42,379 INFO ILO object updated successfully
2025-08-05 10:38:42,398 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-05 10:38:42,409 INFO Updating host RETSE550-NX7002 (*************)
2025-08-05 10:38:42,420 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-05 10:38:42,421 INFO Finding account URI for user 'administrator'.
2025-08-05 10:38:42,421 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 10:38:42,421 INFO params: None
2025-08-05 10:38:42,421 INFO User: administrator
2025-08-05 10:38:42,421 INFO payload: None
2025-08-05 10:38:42,421 INFO files: None
2025-08-05 10:38:42,421 INFO timeout: None
2025-08-05 10:38:43,684 INFO Got the response with OK
2025-08-05 10:38:43,685 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 10:38:43,685 INFO params: None
2025-08-05 10:38:43,686 INFO User: administrator
2025-08-05 10:38:43,686 INFO payload: None
2025-08-05 10:38:43,686 INFO files: None
2025-08-05 10:38:43,686 INFO timeout: None
2025-08-05 10:38:44,866 INFO Got the response with OK
2025-08-05 10:38:44,867 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 10:38:44,867 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 10:38:44,867 INFO params: None
2025-08-05 10:38:44,867 INFO User: administrator
2025-08-05 10:38:44,867 INFO payload: {'Password': '*****'}
2025-08-05 10:38:44,867 INFO files: None
2025-08-05 10:38:44,867 INFO timeout: None
2025-08-05 10:38:46,201 INFO ILO object updated successfully
2025-08-05 10:38:46,213 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-05 10:38:46,231 INFO Updating host RETSE550-NX7003 (*************)
2025-08-05 10:38:46,241 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-05 10:38:46,241 INFO Finding account URI for user 'administrator'.
2025-08-05 10:38:46,241 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 10:38:46,241 INFO params: None
2025-08-05 10:38:46,242 INFO User: administrator
2025-08-05 10:38:46,242 INFO payload: None
2025-08-05 10:38:46,242 INFO files: None
2025-08-05 10:38:46,242 INFO timeout: None
2025-08-05 10:38:47,406 INFO Got the response with OK
2025-08-05 10:38:47,407 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 10:38:47,407 INFO params: None
2025-08-05 10:38:47,407 INFO User: administrator
2025-08-05 10:38:47,407 INFO payload: None
2025-08-05 10:38:47,407 INFO files: None
2025-08-05 10:38:47,407 INFO timeout: None
2025-08-05 10:38:48,628 INFO Got the response with OK
2025-08-05 10:38:48,630 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 10:38:48,630 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 10:38:48,630 INFO params: None
2025-08-05 10:38:48,630 INFO User: administrator
2025-08-05 10:38:48,630 INFO payload: {'Password': '*****'}
2025-08-05 10:38:48,630 INFO files: None
2025-08-05 10:38:48,630 INFO timeout: None
2025-08-05 10:38:49,967 INFO ILO object updated successfully
2025-08-05 10:38:50,003 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-05 10:38:50,037 INFO Successfully updated password for 'administrator' on all hosts. Saving to Vault.
2025-08-05 10:38:50,081 INFO Saving token to Vault... Username: administrator, label: RETSE550-NXC000/Site_Oob
2025-08-05 10:38:50,866 INFO Saving token completed.
2025-08-05 10:38:50,959 INFO Start reset CVM Nutanix password
2025-08-05 10:38:51,034 INFO Attempting to reset password for user 'nutanix' on cluster associated with CVM RETSE550-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-05 10:38:51,034 INFO SSH connecting to RETSE550-NXC000.ikead2.com, this is the '1' try.
2025-08-05 10:38:53,736 INFO SSH connected to RETSE550-NXC000.ikead2.com.
2025-08-05 10:38:53,796 INFO Unlocking user 'nutanix' on all CVMs.
2025-08-05 10:38:53,796 INFO SSH Executing 'allssh sudo faillock --user nutanix --reset'.
2025-08-05 10:38:54,636 INFO Waiting for 10 seconds for the execution.
2025-08-05 10:39:04,637 INFO stdout: b''
2025-08-05 10:39:04,650 INFO Changing password for 'nutanix' on single node RETSE550-NXC000.ikead2.com.
2025-08-05 10:39:04,651 INFO SSH Executing '*****************************************************'.
2025-08-05 10:39:05,181 INFO Waiting for 5 seconds for the execution.
2025-08-05 10:39:10,183 INFO stdout: b'Changing password for user nutanix.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 10:39:10,201 INFO Password change command sent. Verifying new password for 'nutanix' with a new SSH connection.
2025-08-05 10:39:15,202 INFO SSH connecting to RETSE550-NXC000.ikead2.com, this is the '1' try.
2025-08-05 10:39:17,785 INFO SSH connected to RETSE550-NXC000.ikead2.com.
2025-08-05 10:39:17,797 INFO Successfully reset and verified new password for user 'nutanix'.
2025-08-05 10:39:17,812 INFO Saving token to Vault... Username: nutanix, label: RETSE550-NXC000/Site_Pe_Nutanix
2025-08-05 10:39:18,489 INFO Saving token completed.
2025-08-05 10:39:19,001 INFO taking a 30S extra powernap
2025-08-05 10:39:49,042 INFO Start reset admin password
2025-08-05 10:39:49,056 INFO Attempting to reset password for user 'admin' on cluster associated with CVM RETSE550-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-05 10:39:49,056 INFO SSH connecting to RETSE550-NXC000.ikead2.com, this is the '1' try.
2025-08-05 10:39:51,563 INFO SSH connected to RETSE550-NXC000.ikead2.com.
2025-08-05 10:39:51,577 INFO Unlocking user 'admin' on all CVMs.
2025-08-05 10:39:51,577 INFO SSH Executing 'allssh sudo faillock --user admin --reset'.
2025-08-05 10:39:52,448 INFO Waiting for 10 seconds for the execution.
2025-08-05 10:40:02,449 INFO stdout: b''
2025-08-05 10:40:02,475 INFO Changing password for 'admin' on single node RETSE550-NXC000.ikead2.com.
2025-08-05 10:40:02,475 INFO SSH Executing '***************************************************'.
2025-08-05 10:40:02,998 INFO Waiting for 5 seconds for the execution.
2025-08-05 10:40:07,999 INFO stdout: b'Changing password for user admin.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 10:40:08,011 INFO Password change command sent. Verifying new password for 'admin' with a new SSH connection.
2025-08-05 10:40:13,012 INFO SSH connecting to RETSE550-NXC000.ikead2.com, this is the '1' try.
2025-08-05 10:40:15,558 INFO SSH connected to RETSE550-NXC000.ikead2.com.
2025-08-05 10:40:15,575 INFO Successfully reset and verified new password for user 'admin'.
2025-08-05 10:40:15,586 INFO Saving token to Vault... Username: admin, label: RETSE550-NXC000/Site_Pe_Admin
2025-08-05 10:40:16,209 INFO Saving token completed.
2025-08-05 10:43:06,477 INFO Start reset 1-click-nutanix password for PE RETSE550-NXC000
2025-08-05 10:43:07,036 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-08-05 10:43:07,036 INFO params: None
2025-08-05 10:43:07,036 INFO User: admin
2025-08-05 10:43:07,036 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-08-05 10:43:07,036 INFO files: None
2025-08-05 10:43:07,036 INFO timeout: None
2025-08-05 10:43:08,564 INFO Saving token to Vault... Username: 1-click-nutanix, label: RETSE550-NXC000/Site_Pe_Svc
2025-08-05 10:43:09,202 INFO Saving token completed.
2025-08-05 10:43:09,218 INFO ****************************************************************************************************
2025-08-05 10:43:09,218 INFO *                                                                                                  *
2025-08-05 10:43:09,218 INFO *                                          Renew SSH key                                           *
2025-08-05 10:43:09,219 INFO *                                                                                                  *
2025-08-05 10:43:09,219 INFO ****************************************************************************************************
2025-08-05 10:43:10,311 INFO Check if ssh_key exist
2025-08-05 10:43:10,311 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: GET, headers: None
2025-08-05 10:43:10,311 INFO params: None
2025-08-05 10:43:10,311 INFO User: admin
2025-08-05 10:43:10,311 INFO payload: None
2025-08-05 10:43:10,311 INFO files: None
2025-08-05 10:43:10,311 INFO timeout: None
2025-08-05 10:43:11,693 INFO SSH key exist, we need replace it.
2025-08-05 10:43:11,693 INFO Deleting public key...
2025-08-05 10:43:11,694 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys/Gateway, method: DELETE, headers: None
2025-08-05 10:43:11,694 INFO params: None
2025-08-05 10:43:11,694 INFO User: admin
2025-08-05 10:43:11,694 INFO payload: None
2025-08-05 10:43:11,694 INFO files: None
2025-08-05 10:43:11,694 INFO timeout: None
2025-08-05 10:43:13,480 INFO Delete public key finished.
2025-08-05 10:43:13,501 INFO Generating ssh_key
2025-08-05 10:43:13,502 INFO Generating SSH key: ssh-keygen -t rsa -b 2048 -f c:\Dev\UnitPortalBackend\tmp\sshkey\RETSE550-NXC000_2025-08-05-02-43-10\prvkey -q -N "" -m PEM
2025-08-05 10:43:13,768 INFO Key pair generated: c:\Dev\UnitPortalBackend\tmp\sshkey\RETSE550-NXC000_2025-08-05-02-43-10\prvkey
2025-08-05 10:43:13,772 INFO Installing public key...
2025-08-05 10:43:13,773 INFO Calling restapi, URL: https://RETSE550-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: POST, headers: None
2025-08-05 10:43:13,773 INFO params: None
2025-08-05 10:43:13,773 INFO User: admin
2025-08-05 10:43:13,773 INFO payload: {'name': 'Gateway', 'key': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDP2VL1yd0UPvvx5FwFzHJyTWOslV1srEwgDL8Ivz5KwTyVa00zyL3EthMcXIYc8M0r7Ry3zLcBE09rfVnyva2XjAMxONoRQBCAsXqN8aGvypbj7KCcNISSN1wxe9TX4+Y4ILJ2cbp/Q7yskYrhNHNDYMvWeJAPg81s+N9DsfJQ2odLog0iZGTGMldsMI3i1OxS1tgqMf4Vk02gIhPxjtSC/THWhZ3ReoOyZrlLgnVtVAFGFO0v0Pl5MAO3se3NHDUp4PwTXpwmgSgaA78rr8xr7mhCAcRhAnT+emeepFBWIlk83n1hZD7nIfA4CcEQvm0M4bYZ5V1QFPvhc39CWsxN ikea\\hunhe@ITCNSHG-NB0436'}
2025-08-05 10:43:13,773 INFO files: None
2025-08-05 10:43:13,773 INFO timeout: None
2025-08-05 10:43:15,439 INFO Install public key finished.
2025-08-05 10:43:15,450 INFO Saving token to Vault... Username: nutanix, label: RETSE550-NXC000/Site_Gw_Priv_Key
2025-08-05 10:43:16,183 INFO Saving token completed.
2025-08-05 10:43:16,211 INFO Saving token to Vault... Username: nutanix, label: RETSE550-NXC000/Site_Gw_Pub_Key
2025-08-05 10:43:16,863 INFO Saving token completed.
2025-08-05 10:43:16,913 INFO Task is in 'Done' status.

# pylint: disable=invalid-name
class WorkloadSpec:
    PC = 'pc'
    PE = 'pe'
    VM_NAME = 'name'
    WORKLOAD_TYPE = 'workload_type'
    CPU = 'cpu'
    CPU_CORE = 'cpu_core'
    MEMORY = 'memory'
    DISK = 'disk'
    BOOT_MODE = 'boot_mode'
    SUBNET_IP = 'subnet_ip'                 # prior to vlan_id
    VLAN_ID = 'vlan_id'
    IMAGE_ID = 'image_id'
    IMAGE_NAME = 'image_name'
    IMAGE_PATH = 'image_path'
    IMAGE_ALIAS = 'image_alias'
    IMAGE_VM_DISK_ID = 'image_vm_disk_id'
    IP = 'ip'
    IP_ID = 'ip_id'                         # insert into vm_spec after ip assigned on IPAM
    PACKAGES = 'app_package'
    LINUX_PAYLOAD = 'linux_payload'
    VM_UUID = 'vm_uuid'
    USE_TEMPLATE = 'use_template'
    TEMPLATE_ID = 'template_id'
    UPDATE_DNS = 'update_dns'
    NETWORK_UUID = 'network_uuid'           # network uuid on NTX
    SKIP_SIZING = 'skip_sizing'
    FACILITY_TYPE = 'facility_type'
    USER_SPECIFIED_IP = 'user_specified_ip'

class TemplateSpec:
    ID = 'id'
    TEMPLATE_NAME = 'name'
    NAMING_CONVENTION = 'naming_convention'

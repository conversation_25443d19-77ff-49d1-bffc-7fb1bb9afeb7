<template>
    <div class="app-container">
      <div class="filter-container">
        <el-row :gutter="5" >
          <el-col :span="4"  :offset="14" >
            <el-select size="large"
              v-model="filter.selected_pc" multiple collapse-tags placeholder="Filter the PC" style="width:100%;" >
              <el-option v-for="item in filter.pc_list" :key="item" :label="item" :value="item" style="font-size: large;"/>
            </el-select>
          </el-col>
          <el-col :span="4" >
            <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large" @keyup.enter.native="filter_task_list" />
          </el-col>
          <el-col :span="2" style='float:right;'>
            <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="filter_task_list">
              Search
            </el-button>
          </el-col>
        </el-row>
  
      </div>
      <el-table 
      :header-cell-style="{background:'#ffffff',color:'#606266',height:'10px', padding: '3px 0'}" 
      v-loading="listLoading" 
      :data="showing_list" 
      fit 
      highlight-current-row 
      style="width: 100%;" 
      @sort-change="sortChange">
        <!-- <el-table-column label="ID"  sortable="custom" align="center" min-width="60px">
          <template slot-scope="{row}">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column> -->
<!--   
        <el-table-column label="PC" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="prism">
          <template slot-scope="{row}">
            <span class="bigger_font">{{ row.prism.toUpperCase() }}</span>
          </template>
        </el-table-column> -->
  
        <el-table-column label="PE"  align="center" sortable="custom"  min-width="160px" prop="pe">
          <template slot-scope="{row}">
            <span class="pe_font">{{ row.pe}}</span>
          </template>
        </el-table-column>
  
        <el-table-column label="Password Rotation"  align="center" >
            <el-table-column label="Status" align="center" min-width="110px" sortable="custom" prop="rotate_password_status">
                <template slot-scope="{row}">
                  <el-tag :type="row.rotate_password_status | task_status_filter" class="bigger_font pointer" @click="show_brief_log(row, 'rotate_password')">
                    {{ row.rotate_password_status == null ? 'No task yet' : row.rotate_password_status}}
                  </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="Last Run" align="center" min-width="160px" sortable="custom" prop="rotate_password_lastrun">
                <template slot-scope="{row}">
                    {{ row.rotate_password_lastrun}}
                </template>
            </el-table-column>
        </el-table-column>

        <el-table-column label="SSL Renewal" align="center" >
            <el-table-column label="Status" align="center" min-width="110px" sortable="custom" prop="renew_certificate_status">
                <template slot-scope="{row}">
                  <el-tag :type="row.renew_certificate_status | task_status_filter" class="bigger_font pointer"  @click="show_brief_log(row, 'renew_certificate')">
                    {{ row.renew_certificate_status == null ? 'No task yet' : row.renew_certificate_status}}
                  </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="Last Run" align="center" min-width="160px" sortable="custom" prop="renew_certificate_lastrun">
                <template slot-scope="{row}">
                    {{ row.renew_certificate_lastrun}}
                </template>
            </el-table-column>
        </el-table-column>

        <el-table-column label="Desired state config"  align="center" >
            <el-table-column label="Status" align="center" min-width="110px" sortable="custom" prop="desired_state_config_status">
                <template slot-scope="{row}">
                  <el-tag :type="row.desired_state_config_status | task_status_filter" class="bigger_font pointer" @click="show_brief_log(row, 'desired_state_config')">
                    {{ row.desired_state_config_status == null ? 'No task yet' : row.desired_state_config_status}}
                  </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="Last Run" align="center" min-width="160px"  sortable="custom" prop="desired_state_config_lastrun">
                <template slot-scope="{row}">
                    {{ row.desired_state_config_lastrun}}
                </template>
            </el-table-column>
        </el-table-column>




        <!-- <el-table-column label="Cluster validate" align="center" >
            <el-table-column label="Status" align="center" min-width="110px" sortable="custom" >
                <template slot-scope="{row}">
                  <el-tag :type="row.cluster_validation_status | task_status_filter" class="bigger_font pointer" @click="show_brief_log(row, 'Cluster validation')">
                    {{ row.cluster_validation_status == null ? 'No task yet' : row.cluster_validation_status}}
                  </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="Last Run" align="center" min-width="160px" sortable="custom" >
                <template slot-scope="{row}">
                    {{ row.cluster_validation_lastrun}}
                </template>
            </el-table-column>
        </el-table-column> -->

        <el-table-column label="Operations"  align="center"  min-width="190px">
          <template slot-scope="{row}">
            <el-dropdown split-button type="primary" v-if="!row.locked" class="table_button"  @click="execute_auto_maintenance(row, 'all')">
            Execute All
            <template #dropdown>
              <el-dropdown-menu slot="dropdown" placement="right" >
                <el-dropdown-item 
                :disabled ="row.rotate_password_status=='In Progress'"
                @click.native="execute_auto_maintenance(row, 'rotate_password')">Password rotation </el-dropdown-item>
                <el-dropdown-item 
                :disabled ="row.rotate_password_status=='In Progress'||row.renew_certificate_status=='In Progress'"
                @click.native="execute_auto_maintenance(row, 'renew_certificate')">Certificate renewal</el-dropdown-item>
                <el-dropdown-item 
                :disabled ="row.rotate_password_status=='In Progress'||row.desired_state_config_status=='In Progress'"
                @click.native="execute_auto_maintenance(row,'desired_state_config')">Desired state config</el-dropdown-item>
                <el-dropdown-item disabled @click.native="execute_auto_maintenance(row, 'validation')">Cluster validation</el-dropdown-item>
                <el-dropdown-item  divided  @click.native="show_auto_maintenance_history(row)">View history</el-dropdown-item>
                <!-- <el-dropdown-item  divided  @click.native="open_ssh(row, 'cvm')">SSH to CVM</el-dropdown-item> -->
                <!--SSH function not fully ready yet. -->
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button type="warning" class="table_button" v-if="row.locked"  @click="show_auto_maintenance_history(row)">
              View history
          </el-button>

          </template>
        </el-table-column>

        <el-table-column label="Next Execution(CET)"  align="center" min-width="120px" sortable="custom" prop="locked">
          <template slot-scope="{row}">
            {{ row.next_run_time}}
          </template>
        </el-table-column>
<!-- 
        <el-table-column label="Freeze"  align="center" min-width="120px" sortable="custom" prop="locked">
          <template slot-scope="{row}">
            <svg-icon :icon-class="row.locked?'lock':'unlock'" class-name="card-panel-icon" class="pointer" style="font-size: 25px" @click="row.locked?get_unlock_info(row):set_lock(row)"/>
          </template>
        </el-table-column> -->
  
      </el-table>
  
      <pagination v-show="pagination.total>0" :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.limit" @pagination="set_page" />

      <!-- Modal for brief log --->
      <el-dialog id="modal_brief_log" :visible.sync="modal.brief_log.visible" :title="modal.brief_log.title" style="height: 1200px;">
        <el-table :data="modal.brief_log.logs" border fit highlight-current-row  max-height="500" >
          <el-table-column label="log date"  min-width="25%" >
            <template slot-scope="{row}">
              <span :class="row.severity=='error'?'log_table_error':''">{{ row.log_date||row.logdate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="log info" min-width="55%"  >
            <template slot-scope="{row}">
              <span :class="row.severity=='error'?'log_table_error':''">{{ row.log_info||row.loginfo }}</span>
            </template>
          </el-table-column>
          <el-table-column label="log severity"  min-width="10%"  >
            <template slot-scope="{row}">
              <span :class="row.severity=='error'?'log_table_error':''">{{ row.severity }}</span>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button type="danger" v-show="modal.brief_log.abort_button_visible" @click="abort_atm()">Abort</el-button>
          <el-button type="warning" @click="download_log_file">Download Detail Log</el-button>
          <el-button type="primary" style="width:100px" @click="modal.brief_log.visible = false">OK</el-button>
          <el-button type="danger" style="width:100px" @click="abort_atm_execution" v-show="modal.brief_log.abort_button_showing">Abort</el-button>  
        </span>
      </el-dialog>

      <!-- Modal for cluster auto maintenance history --->
      <el-dialog :visible.sync="modal.cluster_auto_maintenance_history.visible" :title="modal.cluster_auto_maintenance_history.title" >
        
        <el-table :data="modal.cluster_auto_maintenance_history.data" border fit highlight-current-row  max-height="500" >
          <el-table-column label="Task"  align="center" sortable="custom"  min-width="160px">
            <template slot-scope="{row}">
              <span class="pe_font_bold">{{ row.type|type_filter}}</span>
            </template>
          </el-table-column>
          <el-table-column label="Start date"  align="center" sortable="custom"  min-width="160px">
            <template slot-scope="{row}">
              <span class="pe_font">{{ row.create_date}}</span>
            </template>
          </el-table-column>
          <el-table-column label="Status" align="center" min-width="110px" sortable="custom" >
                <template slot-scope="{row}">
                  <el-tag :type="row.status | task_status_filter" class="bigger_font pointer">
                    {{ row.status }}
                  </el-tag>
                </template>
          </el-table-column>

          <el-table-column label="Creater"  align="center" sortable="custom"  min-width="160px">
            <template slot-scope="{row}">
              <span class="pe_font">{{ row.creater}}</span>
            </template>
          </el-table-column>

          <el-table-column label="Download"  align="center" sortable="custom"  min-width="160px">
            <template slot-scope="{row}">
              <el-button type="warning" @click="download_history_log_file(row)">
                Download Detail Log
              </el-button>
            </template>
          </el-table-column>

        </el-table>

        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="modal.cluster_auto_maintenance_history.visible = false">OK</el-button>
        </span>
      </el-dialog>

      <!-- Modal for executing auto maintenance --->
      <el-dialog class="modal_atm" :visible.sync="modal.execute_auto_maintenance.visible" :title="modal.execute_auto_maintenance.title" >
        <div style="border-top: 0.1px solid rgb(185, 182, 182);width:100%;height:0;margin-bottom:2%"></div>
        <div style="margin-left:1%"> 
          <span class="unlock_span">  
            You are going to execute:
          </span>
          <div>
            <el-checkbox-group v-model="atm_execution.check_group" >
              <el-checkbox 
                style="width:60%;margin-left: 3%;margin-top:2%" 
                v-for     = "_exe in (atm_execution.executions)" 
                :key      = "_exe.name" 
                :label    = "_exe.name" 
                :disabled = "selected_row.rotate_password_status=='In Progress'||selected_row[_exe.name+'_status']=='In Progress'"
              >
              {{_exe.name}}
              </el-checkbox>
              </el-checkbox-group>
          </div>

        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="launch_auto_maintenance">Yes, execute</el-button>
          <el-button type="primary" @click="modal.execute_auto_maintenance.visible = false">No, not now</el-button>
        </span>
      </el-dialog>

      <!-- Modal for lockers --->
      <el-dialog id="modal_lock" :visible.sync="modal.auto_maintenance_locker.visible" :title="modal.auto_maintenance_locker.title" >
        <div style="border-top: 0.1px solid rgb(185, 182, 182);width:100%;height:0;margin-bottom:6%"></div>
        <el-form ref="lock_form" label-position="left" style="width: 100%; ">
          <el-form-item  prop="lock_date" > 
            <div class="form-label" style="line-height: 19px;">
             <div style="margin-top:0px"><span>Lock date</span></div> 
              <span style="font-size: 12;color: red;">(local timezone)</span>
            </div>
            <el-date-picker
              v-model="new_lock.dates"
              type="datetimerange"
              range-separator="To"
              start-placeholder="Start date"
              end-placeholder="End date"
              unlink-panels
              size="22"
              :picker-options="new_lock.picker_options"
            />
          </el-form-item>
          <el-form-item  prop="lock_description" >  
            <div class="form-label"><span>Description</span></div>
            <el-input style="width:60%" v-model="new_lock.description"></el-input>
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button type="success" style="width:10%" @click="create_lock">OK</el-button>
          <el-button type="primary" @click="modal.auto_maintenance_locker.visible = false" style="width:10%">Cancel</el-button>
        </span>
      </el-dialog>

      <!-- Modal for unlockers --->
      <el-dialog 
          id="modal_unlock" 
          :visible.sync="modal.auto_maintenance_unlocker.visible" 
          :title="modal.auto_maintenance_unlocker.title" 
      >
        <div style="border-top: 0.1px solid rgb(185, 182, 182);width:100%;height:0;margin-bottom:2%"></div>
        <div style="margin-left:2%">
          <span style="font-size: 19px;margin-left: 1%">The cluster was locked for executing auto maintaince </span>
          <br><br>
          <span class="unlock_span">by</span><span class="unlock_span_underline">{{ selected_lock.creater }}</span>
          <br><br>
          <span class="unlock_span">from</span><span class="unlock_span_underline">{{ selected_lock.start_time }}</span><span class="unlock_span">to</span><span class="unlock_span_underline">{{ selected_lock.end_time }}</span>
          <br><br>
          <span class="unlock_span">because</span><span class="unlock_span_underline">{{ selected_lock.description }}</span>

        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="danger" @click="set_unlock">Yes, unlock</el-button>
          <el-button type="primary" @click="modal.auto_maintenance_unlocker.visible = false">Let me think</el-button>
        </span>
      </el-dialog>


      <!-- Modal for ssh --->
        <el-dialog
          id="modal_term"
          v-dialogDrag
          :title="title"
          :fullscreen="isfullscreen"
          :visible.sync="modal.ssh.visible"
          :append-to-body="true"
          :show-close="false"
          :modal="ismodal"
          class="ZDialog"
          @close="handleClose"
          :close-on-press-escape="true"
          :class="isminimize ? 'isminimize' : ''"
          
        >
          <div
            v-show="!isminimize"
            slot="title"
            class="medium"
            @dblclick="resetFull"
            
          >
            <div class="lefts">
              <span>{{ title }}</span>
            </div>
            <div class="icons">
              <i
                title="Minus"
                style="font-size: 24px"
              ><svg style="height: 16;width: 18;" @click="minimize" >
                <use xlink:href="#icon-minus"></use>
              </svg></i>
              <i
                title="Full Screen"
                style="font-size: 24px"
              ><svg-icon :icon-class="isfullscreen?'exit-fullscreen':'fullscreen'" @click="Fullscreen"  id="fullscreenclick"/></i>
              <i
                title="Close"
                style="font-size: 24px"
              ><svg style="height: 16;width: 18;" @click="handleClose" >
                <use xlink:href="#icon-Close"></use>
              </svg></i>
            </div>
          </div>
          <div v-show="isminimize" slot="title" class="horn" @dblclick="resetFull">
            <div class="lefts">
              <span>{{ title }}</span>
            </div>
            <div class="icons">
              <i
                title="Full Screen"
                style="font-size: 24px"
              ><svg-icon :icon-class="isfullscreen?'exit-fullscreen':'fullscreen'" @click="resetFull" /></i>
            </div>
            <i
              title="Close"
              style="font-size: 24px"
            ><svg style="height: 16;width: 18;" @click="handleClose" >
                <use xlink:href="#icon-Close"></use>
              </svg></i>
          </div>
          <div v-show="!isminimize" class="dialogBody" ref="sshdialog">
            <slot name="body" solt="body">
              <el-form label-position="left" style="width: 100%;">
              <el-tabs
                
                v-model="modal.ssh.active_tab"
                type="card"
                closable
                @tab-remove="remove_tab"
              >
                <el-tab-pane
                  v-for="session in modal.ssh.sessions"
                  :key="session.name"
                  :label="session.title"
                  :name="session.name"
                >
                <div  :ref="session.ref">
                </div>

                </el-tab-pane>
              </el-tabs>
            </el-form>
            </slot>
          </div>
          <div v-show="!isminimize" class="dialogFooter">
            <slot name="footer" solt="footer">
              <el-button type="success" @click="downloadhistory">Download</el-button>
              <el-button type="primary" @click="modal.ssh.visible = false" style="width:10%">Cancel</el-button>
            </slot>
          </div>
        </el-dialog>

    </div>
  </template>
  
  <script>
  import {GetMaintenanceTaskList, GetMaintenanceLock, SetMaintenanceLock, RemoveMaintenanceLock, 
    LaunchAutoMaintenance, GetMaintenanceLog, DownloadATMDetailLog, GetMaintenanceHistory, AbortAutoMaintenance } from  '@/api/automation'
  import waves from '@/directive/waves'  // waves directive
  import Pagination from '@/components/Pagination'  // secondary package based on el-pagination
  import io from 'socket.io-client'
  import  {Terminal} from 'xterm'
  import { FitAddon } from 'xterm-addon-fit'
  import { fetch_domain, fetch_env} from '@/utils/commonfunc'
  import '@/styles/xterm.css'

  Date.prototype.addDays   = function(d){return new Date(this.valueOf()+864E5*d);};
  Date.prototype.addMonths = function(m){
    let d = new Date()
    return d.setMonth(d.getMonth()+ m )
  };
  Date.prototype.getStandardFormat = function(){
    let nice_format = (int)=>{
      return (int < 10 ? "0" + int : int).toString()
    }
    return (this.getFullYear() + "-" + nice_format(this.getMonth()) + "-" + nice_format(this.getDay()) + " " + nice_format(this.getHours()) + ":" + nice_format(this.getMinutes()) + ":" + nice_format(this.getSeconds()) )
  }
  export default {
    name      : 'ATMTable',
    components: { Pagination},
    directives: { waves },
    filters   : {    
      task_status_filter(status){
        status      = status.toLowerCase()
        let task_status = [
          "done",
          "in progress",
          "aborted",
          "error"
        ]
        if(task_status.includes(status)){
          switch(status){
            case "done": 
              return 'success'
            case "in progress": 
              return 'primary'
            case "aborted": 
              return 'warning'
            case "error": 
              return 'danger'
          }
        }
        else{
          return 'primary'
        }
      },
      type_filter(type){
        let type_dict = {
          "renew_certificate": "Certficate renewal",
          "desired_state_config": "DSC",
          "rotate_password":"Password Rotation"
        }
        if(Object.keys(type_dict).find(e=>e==type)!=undefined){
          return type_dict[type]
        }
        else{
          return type
        }
      }
    },
    data() {
      return {
        title:'SSH Session',
        isfullscreen: false, // 全屏
        isminimize: false,
        visible: false,
        ismodal: true,
        term: "", 
        // term: new Terminal({
        //   fontSize: 18,
        //   rows: this.rows, 
        //   cols: this.cols,
        //   // convertEol: true,
        //   rendererType: 'canvas', 
        //   cursorBlink: true, 
        //   // disableStdin: true,
        //   windowsMode: true, 
        //   cursorStyle: 'block',
        //   theme: {
        //     foreground: "#ECECEC",
        //     background: '#060101', 
        //     cursor: 'help', 
        //     lineHeight: 20,
        //   }
        //   }),//terminal 黑窗口容器
        rows: 40,
        cols: 100,
        ws_id:"123",
        ws_chan: "200",
        ssh_name:"",
        pagination:{
          total:0,
          page:1,
          limit:20
        },
        modal:{
            brief_log:{
              type:"",
              visible: false,
              title  : "Log(brief)",
              logs   : [],
              abort_button_visible:false
            },
            cluster_auto_maintenance_history:{
              visible: false,
              title  : "Auto maintenance history:",
              data   : []
            },
            execute_auto_maintenance:{
              visible: false,
              title  : "Execute auto maintenance:",
              data   : []
            },
            auto_maintenance_locker:{
              visible: false,
              title  : "Lock auto maintenance:",
              data   : []
            },
            auto_maintenance_unlocker:{
              visible: false,
              title  : "Unlock auto maintenance:",
              data   : []
            },
            ssh:{
              visible: false,
              active_tab:{},
              sessions:[],
              socket: "",
              temp_data: "",
              session_output: {},
            }
        },
        all_maintenance_task_list    : [],
        filtered_list                : [],
        showing_list                 : [],
        listLoading                  : false,
        filter                       : {
            pc_list     : [],
            selected_pc : [],
            fuzzy_string: "",
        },
        selected_row: {},
        new_lock    : {
          pe            : "",
          size          : "large",
          dates         : [],
          picker_options: {
            shortcuts:[
            {
              text: '1 week',
              onClick(picker) {
                const start = new Date();
                picker.$emit('pick', [start, start.addDays(7)]);
              }
            },
            {
              text: '2 weeks',
              onClick(picker) {
                const start = new Date();
                picker.$emit('pick', [start, start.addDays(14)]);
              }
            },
            {
              text: '1 month',
              onClick(picker) {
                const start = new Date();
                picker.$emit('pick', [start, start.addMonths(1)]);
              }
            },
          ]
          },
          description: ""
        },
        selected_lock:{
          creater:"",
          start_time:"",
          end_time:"",
          description:""
        },
        atm_execution:{
          check_group:[],
          executions:[
            {
              name:"rotate_password",
              key:"rotate_password"
            },
            {
              name:"renew_certificate",
              key:"renew_certificate"
            },
            {
              name:"desired_state_config",
              key:"desired_state_config"
            }
          ],
          all_execution_list:["rotate_password","renew_certificate","desired_state_config"]
        }
      }
    },
    computed: {

    },
    created() {
        this.get_task_list()

    },
    mounted() {
      // this.initXterm()
    },
    methods: {
      initXterm() {
          let _this = this
          let term = new Terminal({
            fontSize: 18,
            rendererType: "canvas", //渲染类型
            // rows: parseInt(window.innerHeight/40), //行数
            rows:_this.rows,
            cols: _this.cols, // 不指定行数，自动回车后光标从下一行开始
            convertEol: true, //启用时，光标将设置为下一行的开头
            // scrollback: 50, //终端中的回滚量
            disableStdin: false, //是否应禁用输入
            cursorStyle: "underline", //光标样式
            cursorBlink: true, //光标闪烁
            theme: {
              foreground: "#ECECEC", //字体
              background: "#000000", //背景色
              cursor: "help", //设置光标
              lineHeight: 20
            }
          })
          const fitAddon = new FitAddon()
          term.loadAddon(fitAddon)
          fitAddon.fit()
          let size = { cols: 40, rows: 90 }
          fullscreenclick
          let b=this
          document.getElementById('fullscreenclick').addEventListener("click",function(_this=b){
            // 不传size
          if (b.isfullscreen) {
            b.rows = parseInt(document.getElementById('modal_term').clientHeight/25);
            b.cols = parseInt(document.documentElement.clientWidth / 20);
          }
            try {
              fitAddon.fit();
              term.resize(b.cols, b.rows)
            } catch (e) {
            }
          });
          _this.term = term
        },
      minimize() {
        this.isminimize = !this.isminimize;
        this.ismodal = !this.ismodal;
        if (this.isfullscreen) { this.isfullscreen = !this.isfullscreen };
      },
      closeDialog() {
        this.$emit('update:show', false)
        this.$emit('update:closeOnClickModal', false)
      },
      Fullscreen() {
        this.isfullscreen = !this.isfullscreen;
        this.ismodal = !this.ismodal;
      },
      resetFull() {
        if (this.isfullscreen === true && this.isminimize === false) {
          this.minimize();
        }
        if (this.isfullscreen === false && this.isminimize === false ) {
          this.Fullscreen();
        }
        if (this.isfullscreen === false && this.isminimize === true) {
          this.minimize();
        }
        if (this.isfullscreen === true && this.isminimize === true) {
          this.Fullscreen();
        }
      },
      handleClose() {
        this.modal.ssh.visible = false;
        this.modal.ssh.sessions = []
      },
      handleshow() {
          if (this.modal.ssh.visible = true) { 
          return true
        }
      },
      writeToFile(d1, d2){
        var fso = new ActiveXObject("Scripting.FileSystemObject");
        var fh = fso.OpenTextFile("data.txt", 8, false, 0);
        fh.WriteLine(d1 + ',' + d2);
        fh.Close();
      },
      createtextfile( text ) {
        var textFile = null;
        var data = new Blob( [text], {type:'text/plain'} );
        if (textFile !== null) window.URL.revokeObjectURL(textFile);
        textFile = window.URL.createObjectURL(data);
        return textFile;
      },
      downloadhistory() {
        const link = document.createElement('a');
        link.href = this.createtextfile( this.modal.ssh.session_output[this.modal.ssh.active_tab] );
        link.setAttribute('download', this.modal.ssh.active_tab+'.txt'); //or any other extension
        document.body.appendChild(link);
        link.click();
      },
      resizeScreen() {
        try {
          const fitAddon = new FitAddon();
          this.term.loadAddon(fitAddon);
          fitAddon.fit();
          this.term.onResize(() => {
            _this.onSend({ Op: "resize", Cols: 100, Rows: 40 });
          });
        } catch (e) {
          console.log("e", e.message);
        }
      },
      open_ssh(row, server_type) {
        this.modal.ssh.visible = true
        let ssh_name, ssh_title
        let history = []
        let inputtext = []
        let i = 0
        let j = 0
        let h = 0
        let currentIndex = 0;
        let sessionid = '';
        
        if (this.isminimize) {
          this.isminimize = !this.isminimize;
          this.ismodal = !this.ismodal;
        }
        for(var n = 0; n < 3; n++){
            var r = Math.floor(Math.random() * 10);
            sessionid += r;
        }
        // console.log(sessionid);
        if(this.modal.ssh.sessions.find((s)=>s.name==(this.$store.getters.name+'@'+row.pe+server_type+'-'+sessionid))){
          for(let i = 2 ;i < 10; i++){
            if(this.modal.ssh.sessions.find((s)=>s.name==(this.$store.getters.name+'@'+row.pe+server_type+'-'+sessionid+i.toString() ))){
              continue
            }else{
              ssh_name = this.$store.getters.name+'@'+row.pe+server_type+'-'+sessionid+i.toString() 
              ssh_title = this.$store.getters.name+'@'+row.pe+server_type+'-'+i.toString()
              break
            }
          } 
        }else{
          ssh_name = this.$store.getters.name+'@'+row.pe + server_type+'-'+sessionid
          ssh_title = this.$store.getters.name+'@'+row.pe + server_type
        }
        this.modal.ssh.session_output[ssh_name] = ssh_name + '\n'
        this.ssh_name = ssh_name
        // generate the tab name
        let _tab = {
          "name":ssh_name,
          "title":ssh_title,
          "ref":ssh_name,
          "pe":row.pe,
          "type":server_type
        }
        // let d =""
        this.modal.ssh.active_tab = _tab['name']
        this.modal.ssh.sessions.push(_tab)
        this.socket = io('https://10.59.157.153:8080/ws')
        this.socket.on("connect",function(){
        })
        let a = this
        this.socket.on("response",function(recv,_this=a){
          // console.log(recv)
          // console.log(_this)
          _this.modal.ssh.sessions.forEach(s=>{
            if(s['name']==recv['chan']){
              s['terminal'].write(recv['data'])
              _this.modal.ssh.session_output[ssh_name] += recv['data'].replace(/\n+$/, "")
            }
          })
          // term.write(data)
        })
        this.socket.emit('join', { 'room': this.$store.getters.token + ssh_name })
       
      
        
        setTimeout( //render ssh console.
          () => {
            // this.initXterm()
            // _tab['terminal']=a.term
            // _tab['terminal'].open(this.$refs[_tab['ref']][0])
            // console.log(this.rows,this.cols)
            _tab['terminal'] = new Terminal({
              fontSize: 18,
              rows: a.rows, 
              cols: a.cols,
              // convertEol: true,
              rendererType: 'canvas', 
              cursorBlink: true, 
              // disableStdin: true,
              windowsMode: true, 
              cursorStyle: 'block',
              theme: {
                foreground: "#ECECEC",
                background: '#060101', 
                cursor: 'help', 
                lineHeight: 20,
              }
              });
            var fitAddon = new FitAddon()
            _tab['terminal'].loadAddon(fitAddon)
            fitAddon.fit()
            fullscreenclick
            let b=this
            document.getElementById('fullscreenclick').addEventListener("click",function(_this=b){
              // 不传size
            if (b.isfullscreen) {
              b.rows = parseInt(document.getElementById('modal_term').clientHeight/25);
              b.cols = parseInt(document.documentElement.clientWidth / 20);
            }
              try {
                fitAddon.fit();
                _tab['terminal'].resize(b.cols, b.rows)
              } catch (e) {
              }
            });
          // _this.term = term
          _tab['terminal'].open(this.$refs[_tab['ref']][0])
          _tab['terminal'].onData((data) => {
            _tab['terminal'].write(data)
            if (data == '\r') {
              if (this.modal.ssh.temp_data.trim()) {
                history.push(this.modal.ssh.temp_data)
              }
                this.modal.ssh.session_output[ssh_name] += data+'\n'
                data = this.modal.ssh.temp_data + '\r'
              // history.push(this.modal.ssh.temp_data)
                currentIndex = history.length
                this.socket.emit("message", { 'chan': _tab['name'], 'data': data, 'room': this.$store.getters.token + ssh_name })
                this.modal.ssh.temp_data = ""
                inputtext=[]
                i=0
              } else if (data == '\x7F') {
                if (inputtext.length){
                  _tab['terminal'].write('\b \b');
                  this.modal.ssh.temp_data = (this.modal.ssh.temp_data).slice(0, -1);
                  i = 0
                  inputtext.length--
                }else{_tab['terminal'].write("\x1b[?K");}
              }
              else if (data == '\x1B[A') {
                inputtext = []
                if (i == 0) {
                  j = _tab['terminal']._core.buffer.x
                  h = _tab['terminal']._core.buffer.y
                  this.modal.ssh.temp_data = '\n';
                }
                if (history.indexOf(history[currentIndex - 1]) === -1) {
                  _tab['terminal']._core.buffer.x = j
                  _tab['terminal'].write('\n')
                  _tab['terminal'].write("\x1b[?K");
                  _tab['terminal'].write(history[0]);
                  _tab['terminal']._core.buffer.x = j
                  inputtext = Array.from(history[0])
                } else {
                  this.modal.ssh.temp_data = history[currentIndex - 1];
                  // _tab['terminal']._core.buffer.x = j
                  _tab['terminal'].write('\n')
                  _tab['terminal'].write("\x1b[?K");
                  _tab['terminal'].write(history[currentIndex - 1]);
                  _tab['terminal']._core.buffer.x = j
                  inputtext=Array.from(history[currentIndex - 1])
                  i++
                  currentIndex--;
                }
              }
              else if (data == '\x1B[B') {
                inputtext=[]
                _tab['terminal']._core.buffer.x = j
                _tab['terminal']._core.buffer.y = h+1
                if (i == 0) {
                  _tab['terminal'].write("\x1b[?K");
                  
                }
                if (currentIndex == (history.length -1)) {
                  _tab['terminal'].write("\x1b[?K");
                  // currentIndex++
                  _tab['terminal']._core.buffer.x = j
                  _tab['terminal']._core.buffer.y = h+1
                } else {
                  this.modal.ssh.temp_data = history[currentIndex+1]
                _tab['terminal'].write("\x1b[?K");
                _tab['terminal'].write(history[currentIndex+1]);
                _tab['terminal']._core.buffer.x = j
                _tab['terminal']._core.buffer.y = h+1
                inputtext=Array.from(history[currentIndex+1])
                i--
                currentIndex++;
                }
              }
              else { this.modal.ssh.temp_data += data; inputtext.push(data); i=0 }
          });
          this.socket.emit("init_ssh", { 'chan': _tab['name'], 'pe': _tab['pe'], type: _tab['type'], 'room': this.$store.getters.token + ssh_name })
        }, "100");
      },
      remove_tab(target_name){
        if (this.modal.ssh.active_tab.name === target_name) {
          this.modal.ssh.sessions.forEach((tab, index) => {
            if (tab.name === target_name) {
              const nextTab = this.modal.ssh.sessions[index + 1] || this.modal.ssh.sessions[index - 1]
              if (nextTab) {
                this.modal.ssh.active_tab.name = nextTab.name
              }else{this.modal.ssh.visible = false}
            }
          })
        }
        this.modal.ssh.sessions = this.modal.ssh.sessions.filter((tab) => tab.name !== target_name)
      },
      ws(){
        if(this.socket&&this.socket.connected){
          return
        }
        this.socket = io('https://10.59.157.153:8080/ws')
        this.socket.on("connect",function(){
          // console.log('connected to socket server.')
        })
        // console.log('Creating a private room:' + this.$store.getters.token+this.ssh_name)
        this.socket.emit('join',{'room':this.$store.getters.token+this.ssh_name})

        this.socket.emit("message",200)
        this.socket.on("response",function(recv){
          term.write(recv.data)
        })
        var window_width = 1000;
        var window_height = 1000;
        var term = new Terminal({
          fontSize: 18,
          convertEol: true, // 启用时，光标将设置为下一行的开头
          rendererType: 'canvas', // 渲染类型
          cursorBlink: true, // 光标闪烁
          cursorStyle: 'bar', // 光标样式 underline
          theme: {
            background: '#060101', // 背景色
            cursor: 'help' // 设置光标
          }
        }
        );
        const fitPlugin = new FitAddon();
        term.open(document.getElementById('terminal'));
        term.loadAddon(fitPlugin);
        term.write('11111')
        // term.on("data",function(data){
        //          console.log(data)
        //          //socket.emit("message",{"data":data});
        //       });
        term.onData((data) => {
          this.socket.emit("message",{'chan':'200','data':data})
          // console.log(data.charCodeAt(0));
          // if (data.charCodeAt(0) == 13)
          //     term.write('\n');
          // term.write(data);
          // console.log(term)
        });
      },
      ws_send(){
        this.socket.emit("message",{
          "chan":this.ws_chan,
          "data":"hostname"
        })
      },
        get_task_list() {
            this.listLoading = true
            GetMaintenanceTaskList(this.$store.getters.token).then(response => {
                this.all_maintenance_task_list  = response.data
                this.pagination.total = response.data.length
                this.listLoading                = false
                  //get prism list , for filtering
                let all_prism_list              = this.all_maintenance_task_list.map((obj,index)=>{return obj['prism']})
                this.filter.pc_list             = this.remove_duplicate(all_prism_list)
                this.filter_task_list()
            })
            
        },
        remove_duplicate(arr) {
            //去除重复值
            const newArr = []
            arr.forEach(item => {
                if (!newArr.includes(item)) {
                    newArr.push(item)
                }
            })
            return newArr
        },
        filter_task_list(){
              //根据过滤条件筛选表格显示内容
              //screen the table as per filters
            this.pagination.page = 1
            let temp_list
              //filter selected pc first.
            if (this.filter.selected_pc.length){
                //No filter, so select all
                temp_list = this.all_maintenance_task_list.filter((item)=>{
                  return this.filter.selected_pc.includes(item['prism'].toLowerCase())
                })
                this.filtered_list = temp_list
            }
            else{
                this.filtered_list = this.all_maintenance_task_list
            }

            if(this.filter.fuzzy_string.trim().length){
                let temp_list  = this.filtered_list
                let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
                  //去除空格 并以空格分割成数组
                  //remove space, and split into array by space 
                for(let fuzzy of fuzzy_list){
                  fuzzy     = fuzzy.toString().toLowerCase()
                  temp_list = temp_list.filter((k)=>{
                    let combined_string = (k.prism?k.prism.toString().toLowerCase():'')+
                                          (k.pe?k.pe.toString().toLowerCase():'')+
                                          (k.desired_state_config_status?k.desired_state_config_status.toString().toLowerCase():'')+
                                          (k.desired_state_config_lastrun?k.desired_state_config_lastrun.toString().toLowerCase():'')+
                                          (k.rotate_password_status?k.rotate_password_status.toString().toLowerCase():'')+
                                          (k.rotate_password_lastrun?k.rotate_password_lastrun.toString().toLowerCase():'')+
                                          (k.renew_certificate_status?k.renew_certificate_status.toString().toLowerCase():'')+
                                          (k.renew_certificate_lastrun?k.renew_certificate_lastrun.toString().toLowerCase():'')
                    if( combined_string.search(fuzzy)!= -1){
                      return true
                    }
                  })
                }
                this.filtered_list = temp_list
            }
            this.pagination.total = this.filtered_list.length
            this.set_page()
        },
        show_brief_log(row, type){
          if(row[type+'_status']=='N/A'){
            this.$notify({
                title   : 'Warning',
                message : "No log yet.",
                type    : 'warning',
                duration: 5000
            })
            return
          }
          this.modal.brief_log.abort_button_visible = (type=="desired_state_config" &&row[type+'_status']=='In Progress')
          this.modal.brief_log.type = type
          this.selected_row = row
          let payload = {
            token: this.$store.getters.token,
            data:{
              atm_type: type,
              id: row[type+"_id"]
            }
          }
          GetMaintenanceLog(payload).then(
            response=>{
              this.modal.brief_log.logs = response.data
              this.modal.brief_log.visible = true
              this.modal.brief_log.title   = type + " Log(brief)"
            }
          ).catch(
              (error)=>{
                this.$notify({
                title   : 'Error',
                message : "Failed to get the brief log, error: " + error.response.request.response||error.response.data.message,
                type    : 'error',
                duration: 5000
              })
            }
          )
        },
        set_lock(row){
          this.selected_row                          = row
          this.modal.auto_maintenance_locker.visible = true                                     // pop the window out
          this.modal.auto_maintenance_locker.title   = "Lock auto maintenance on "+ row.pe +":"
        },
        get_unlock_info(row){
          this.selected_row = row
          let payload = {
              token: this.$store.getters.token,
              pe_id : this.selected_row.pe_id
          }
          GetMaintenanceLock(payload).then(response=>{
              let lock_info = response.data
              this.selected_lock.creater = lock_info.creater
              this.selected_lock.description = lock_info.description
              this.selected_lock.start_time = (new Date(lock_info.start_time * 1000)).getStandardFormat()
              this.selected_lock.end_time = (new Date(lock_info.end_time * 1000)).getStandardFormat()
              // this.selected_lock.
              this.modal.auto_maintenance_unlocker.visible = true                                        // pop the window out
              this.modal.auto_maintenance_unlocker.title   = "Unlock auto maintenance for "+ row.pe +":"
          }).catch(
            (error)=>{
                this.$notify({
                title   : 'Error',
                message : "Failed to get the lock infomation, " + error,
                type    : 'error',
                duration: 5000
              })
            }
          )
        },
        set_unlock(){
          let payload = {
            token: this.$store.getters.token,
            id: this.selected_row.id
          }
          RemoveMaintenanceLock(payload).then(
            response=>{
              this.get_task_list()
              this.modal.auto_maintenance_unlocker.visible = false
            }
          )
        },
        show_auto_maintenance_history(row){
          let payload = {
            token: this.$store.getters.token,
            data:{
              pe: row.pe
            }
          }
          GetMaintenanceHistory(payload).then(
            response => { 
            this.modal.cluster_auto_maintenance_history.data = response.data.tasks
            this.modal.cluster_auto_maintenance_history.visible = true                                       // pop the window out
            // this.modal.cluster_auto_maintenance_history.title   = "Auto maintenance history ("+ row.pe +"):"
            
          }).catch(            
            (error) => {
                this.$notify({
                title   : 'Error',
                message : "Failed to get the history infomation, " + error,
                type    : 'error',
                duration: 5000
              })
            }
          )
        },
        execute_auto_maintenance(row, item){
          this.selected_row = row
          if(this.selected_row.rotate_password_status=='In Progress')// cant do anything 
          {
            // shouldn't do anything...
            this.atm_execution.check_group=[]
          }
          else{

            this.atm_execution.check_group=[]
            if(item=="all"){
              this.atm_execution.all_execution_list.forEach(e=>{
                if(this.selected_row[e+'_status']!='In Progress'){
                  this.atm_execution.check_group.push(e)
                }
              })
            }else{
                if(this.selected_row[item+'_status']!='In Progress'){
                  this.atm_execution.check_group.push(item)
                }
            }

          }
          this.modal.execute_auto_maintenance.visible = true                                     // pop the window out
          this.modal.execute_auto_maintenance.title   = "Execute maintenance on ("+ row.pe +"):"
        },
        abort_atm(){
          let payload = {
            token: this.$store.getters.token,
            data : {
              type: "desired_state_config",// only dsc is allowed to be aborted.
              task_id: this.selected_row[this.modal.brief_log.type + "_id"]
            }
          }
          AbortAutoMaintenance(payload).then(response=>{
            this.$notify({
                title   : 'Aborted',
                message : "Task has been aborted.",
                type    : 'success',
                duration: 5000
              })
          })
          .catch(error=>{
            console.log(error)
            this.$notify({
                title   : 'Error',
                message : "Failed to abort the task, error: " + error.response.request.response||error.response.data.message,
                type    : 'error',
                duration: 5000
              })
          }).finally(()=>{
            this.get_task_list()
            this.filter_task_list()
          }
          )
        },
        create_lock(){
          try{

            if( this.new_lock.dates.length < 2){
              throw new TypeError("Please make sure start date and end date were entered!")
            }

            if( this.new_lock.dates.find(e => !(e instanceof Date))){
              throw new TypeError("Some date are not Date type, shouldn't've happened, contact Curry.")
            }

            // check if lock duration is more then 30 days
            let start_date = this.new_lock.dates[0]
            let end_date   = this.new_lock.dates[1]
            let date_diff  = Math.floor(( end_date.getTime() - start_date.getTime() )/ (24 * 3600 * 1000))

            if(date_diff >30){
              throw new TypeError("Cannot lock for more than 30 days!")
            }

            let payload = {
              token: this.$store.getters.token,
              data:{
                pe_id      : this.selected_row.pe_id,
                description: this.new_lock.description,
                start_time : this.new_lock.dates[0].getTime()/1000, //milliseconds to seconds
                end_time   : this.new_lock.dates[1].getTime()/1000
              }
            }
            SetMaintenanceLock(payload).then(response=>{
              this.get_task_list()
              this.modal.auto_maintenance_locker.visible = false   
            })

          }
          catch ({name, message}){
            this.$notify({
              title   : 'Error',
              message : message,
              type    : 'error',
              duration: 5000
            })
          }
        },
        set_page(){
          // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
          let page = this.pagination.page
          let limit = this.pagination.limit
          let start , end
          if(page*limit>=this.total){
            start = (page-1)*limit
            end = this.total 
          }
          else{
            start = (page-1)*limit
            end = page * limit
          }
          this.showing_list = this.filtered_list.slice(start,end)
        },
        launch_auto_maintenance(){
          let pe = this.selected_row.pe + fetch_domain(this.selected_row.prism)
          // pe needs to go with pe fqdn
          // if(pe.match(/ikea.com/i)==null){
          //   pe = (pe + ".ikea.com").toUpperCase()
          // }
          let steps = {}

          this.atm_execution.check_group.forEach((v,i)=>steps[v]=true)

          let payload = {
            token : this.$store.getters.token,
            data :{
              pe: pe.toUpperCase(),
              steps:steps,
              facility_type: this.selected_row.facility_type
            }
          }
          LaunchAutoMaintenance(payload).then(response=>{
            this.$notify({
                title   : 'Running',
                message : "Maintenance has been launched!",
                type    : 'success',
                duration: 5000
              })
            this.modal.execute_auto_maintenance.visible = false
          }).catch(error=>{
            this.$notify({
                title   : 'Oooooops...',
                message : "Error orcurred, "+ error,
                type    : 'error',
                duration: 5000
              })
            this.modal.execute_auto_maintenance.visible = false
          })
        },
        download_log_file(){
          let log_path = this.selected_row[this.modal.brief_log.type + "_logpath"]
          if (!log_path || log_path=="N/A"){
            this.$notify({
                  title: 'Ooooops',
                  message: 'No log yet!',
                  type: 'error',
                  duration: 2000
                })
            return
          }
          let payload = {
            data:{ 
                    filepath : log_path
                  },
            token: this.$store.getters.token
          }

          DownloadATMDetailLog(payload)
          .then((response)=>{
            const href = URL.createObjectURL(response.data);
            // create "a" HTML element with href to file & click
            const link = document.createElement('a');
            link.href = href;
            link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
            document.body.appendChild(link);
            link.click();
            // clean up "a" element & remove ObjectURL
            document.body.removeChild(link);
            URL.revokeObjectURL(href);
          })
        },
        abort_atm_execution(){
          // console.log(this.selected_row)
          // console.log(this.modal.brief_log)
          

        },
        download_history_log_file(row){
          console;
          let log_path = row.log_path
          if (!log_path || log_path=="N/A"){
            this.$notify({
                  title: 'Ooooops',
                  message: 'No log yet!',
                  type: 'error',
                  duration: 2000
                })
            return
          }
          let payload = {
            data:{ 
                    filepath : log_path
                  },
            token: this.$store.getters.token
          }

          DownloadATMDetailLog(payload)
          .then((response)=>{
            const href = URL.createObjectURL(response.data);
            // create "a" HTML element with href to file & click
            const link = document.createElement('a');
            link.href = href;
            link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
            document.body.appendChild(link);
            link.click();
            // clean up "a" element & remove ObjectURL
            document.body.removeChild(link);
            URL.revokeObjectURL(href);
          })
        },    
        sortChange(data) {
          const { prop, order } = data
          if(order==null){
            this.sortChange({prop:'id',order:'ascending'})
            return 
          }
          let flag_num = order=="ascending" ? 1 : -1
          this.filtered_list.sort((item1,item2)=>{
            let prop1 = item1[prop]?item1[prop]:''
            let prop2 = item2[prop]?item2[prop]:''        
            return (prop1 > prop2) ? flag_num*1 : ((prop1 < prop2) ? flag_num*-1 : 0)
          })
          this.set_page()
        },
    },
    // watch: {
    //   visible(val) {
    //     if (val) {
    //       const el = this.$refs[this.zDialogRef].$el.querySelector(".el-dialog");
    //       el.style.left = 0;
    //       el.style.top = 0;
    //     }
    //   },
    // show: {
    //     immediate: true,
    //     handler(show) {
    //       this.visible = this.show
    //     }
    //   }
    // },
    directives: {
      dialogDrag: {
        bind(el, binding, vnode, oldVnode) {
          const dialogHeaderEl = el.querySelector(".el-dialog__header");
          const dragDom = el.querySelector(".el-dialog");
          dialogHeaderEl.style.cursor = "move";
          // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
          const sty =
            dragDom.currentStyle || window.getComputedStyle(dragDom, null);
          // const fixedX =
          // const fixedY =
          dialogHeaderEl.onmousedown = e => {
            // 鼠标按下，计算当前元素距离可视区的距离
            const disX = e.clientX - dialogHeaderEl.offsetLeft;
            const disY = e.clientY - dialogHeaderEl.offsetTop;

            // 获取到的值带px 正则匹配替换
            let styL, styT;

            // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
            if (sty.left.includes("%")) {
              styL =
                +document.body.clientWidth * (+sty.left.replace(/\%/g, "") / 100);
              styT =
                +document.body.clientHeight * (+sty.top.replace(/\%/g, "") / 100);
            } else {
              styL = +sty.left.replace("px", "");
              styT = +sty.top.replace("px", "");
            }

            document.onmousemove = function(e) {
              // 通过事件委托，计算移动的距离
              const l = e.clientX - disX;
              const t = e.clientY - disY;
              // 移动当前元素
              dragDom.style.left = `${l + styL}px`;
              dragDom.style.top = `${t + styT}px`;
            };

            document.onmouseup = function(e) {
              const dragDom = el.querySelector(".el-dialog");
              const offsetLeft = dragDom.offsetLeft;
              const offsetTop = dragDom.offsetTop;
              const left = Number(dragDom.style.left.replace("px", ""));
              const top = Number(dragDom.style.top.replace("px", ""));
              const windowWidth = window.innerWidth;
              const windowHeight = window.innerHeight - 50;
              const offsetRight = offsetLeft + dragDom.offsetWidth - windowWidth;
              const offsetBottom =
                offsetTop + dragDom.offsetHeight - windowHeight;
              if (offsetLeft < 0) {
                dragDom.style.left = left - offsetLeft + "px";
              }
              if (offsetTop < 0) {
                dragDom.style.top = top - offsetTop + "px";
              }
              if (offsetRight > 0) {
                dragDom.style.left = left - offsetRight + "px";
              }
              if (offsetBottom > 0) {
                dragDom.style.top = top - offsetBottom + "px";
              }
              document.onmousemove = null;
              document.onmouseup = null;
            };
          };
        }
      }
    },
    
    
}
  
</script>
<style lang = "scss" scoped>
    .bigger_font {
        font-size: 15px;
    }
    .unlock_span {
        margin-left: 2%;
        font-size: 17px;
    }
    .unlock_span_underline {
        margin-left: 1%;
        font-size: 17px;
        border-bottom: 1px solid grey;
    }
    .pe_font {
        font-size: 18px;
    }
    .pe_font_bold {
        font-size: 19px;
        font-weight: bold;
    }
    .el-table th {
        background: #ffffff;
    }
    .table_button{
      width: 140px
    }
    .example-showcase .el-dropdown + .el-dropdown {
      margin-left: 15px;
    }
    el-tag .pointer{
      cursor: pointer;
    }
    .log_table_error{
      color: red
    }
    .form-label{
      color      : black;
      width      : 11%;
      float      : left;
      margin-left: 5%;
    }
    .form-label_half_left{              
      color      : gray;
      width      : 20%;
      float      : left;
      margin-left: 20%;
    }
    .form-label_half_right{
      color      : gray;
      width      : 20%;
      float      : left;
      margin-left: 5%
    }
</style>  

<style lang = "scss" >

    #modal_brief_log{

      .el-dialog__header{
        padding: 20px 15px;
      }
      .el-dialog__body {
        padding: 10px 10px;
        width  : 100%
      }
    }

    #modal_lock{
      .el-dialog__header{
        padding: 20px 20px;
      }
      .el-dialog__body {
        padding: 0px 10px;
        width  : 900
      }
    }
    #modal_unlock{
      .el-dialog{
        width:30%
      }
      .el-dialog__header{
        padding: 20px 20px;
      }
      .el-dialog__body {
        padding: 0px 10px;
        width  : 600
      }
    }
    .modal_atm{
      .el-dialog{
        width:30%
      }
      .el-dialog__header{
        padding: 20px 20px;
      }
      .el-dialog__body {
        padding: 0px 10px;
        width  : 900
      }
    }
    .el-dialog {
      margin-top: 10vh !important;
    }
    .no_select {
      -webkit-touch-callout: none; /* iOS Safari */
      -webkit-user-select: none; /* Chrome/Safari/Opera */
      -khtml-user-select: none; /* Konqueror */
      -moz-user-select: none; /* Firefox */
      -ms-user-select: none; /* Internet Explorer/Edge */
      user-select: none; /* Non-prefixed version, currently */
    }
    .isminimize {
      left: 20px;
      bottom: 20px;
      top: auto;
      right: auto;
      overflow: hidden;

      .el-dialog {
        margin: 0 !important;
        width: 240px !important;
        height: 100px;
        top: 0 !important;
        left: 0 !important;
      }
      .el-dialog__header {
        cursor: auto !important;

        .el-dialog__headerbtn {
          display: none;
        }
      }
      .dialogFooter {
        position: absolute;
        bottom: 0;
      }
    }
    .ZDialog {
      .is-fullscreen {
        width: 100% !important;
        left: 0 !important;
        top: 0 !important;
        margin-top: 0 !important;
        overflow: hidden;
        position: relative;
        .el-dialog__header {
          cursor: auto !important;
        }
        .el-dialog__body {
          height: 100%;
          padding: 0px !important;
          .dialogBody {
            height: 100% !important;
            width: 100% !important;
            max-height: none !important;
            padding-bottom: 300px !important;
          }
        }
        .dialogFooter {
          position: absolute;
          bottom: 0;
          width: 100%;
          background: #fff;
        }
      }
      .el-dialog {
        .el-dialog__header {
          width: 100%;
          padding: 10px 10px 10px !important;
          display: flex;
          border-bottom: 1px solid #ccc;
          @extend .no_select;
          cursor: auto;
          .medium {
            width: 100%;
            height: 100%;
            display: flex;
            div {
              flex: 1;
            }
            .lefts {
              margin-top: 3px;
              span {
                text-align: left;
                font-size: 16px;
                color: #606266;
              }
            }
            .icons {
              display: flex;
              justify-content: flex-end;
              i {
                color: #5f6368;
                font-size: 18px !important;
                display: block;
                padding: 8px;
              }
              i:hover {
                background: #dcdfe6;
                cursor: pointer;
              }
              .el-icon-close:hover {
                background: #f00;
                color: #fff;
              }
            }
          }
          .horn {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-between;
            div {
              i {
                color: #5f6368;
                font-size: 50px !important;
              }
            }
            .lefts {
              flex: 4;
              margin-top: 3px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              span {
                font-size: 16px;
                color: #606266;
              }
            }
            .icons {
              display: flex;
              justify-content: flex-end;
              i {
                color: #5f6368;
                font-size: 18px !important;
                display: block;
                padding: 3px;
              }
              i:hover {
                background: #dcdfe6;
                cursor: pointer;
              }
              .el-icon-close:hover {
                background: #f00;
                color: #fff;
              }
            }
            // .centers {
            //   flex: 1;
            // }
            // .rights {
            //   flex: 1;
            // }
            i:hover {
              cursor: pointer;
              color: #000;
            }
          }
          .el-dialog__headerbtn {
            top: 0;
            font-size: 24px;
          }
        }
        .el-dialog__body {
          padding: 0px !important;
          .dialogBody {
            max-height: calc(80vh - 50px);
            // box-shadow: inset 0px -2px 10px 1px #b0b3b2;
            overflow: auto;
            padding: 20px 25px 20px;
            &::-webkit-scrollbar {
              width: 4px;
              height: 8px;
            }
            &::-webkit-scrollbar-thumb {
              background: transparent;
              border-radius: 4px;
            }
            &:hover::-webkit-scrollbar-thumb {
              background: hsla(0, 0%, 53%, 0.4);
            }
            &:hover::-webkit-scrollbar-track {
              background: hsla(0, 0%, 53%, 0.1);
            }
          }
          .dialogFooter {
            padding: 10px 18px;
            border-top: 1px solid #ccc;
            text-align: right;
            .el-button {
              padding: 10px 18px;
            }
          }
        }
      }
      .ZDialog {
        // display: flex;
        // justify-content: center;
        .el-select {
          width: 100%;
        }
        .el-date-editor {
          width: 100%;
        }
      }
    }
    .terminal-container{
    /* this is important */
      overflow: hidden;
    }
    .xterm .xterm-viewport {
      width: initial !important;
    }
    .xterm-viewport {
      width: initial !important;
      }
      
</style>
#installed modules
import paramiko
from datetime import datetime
import subprocess
import os
import time
import re
import random
import string
import werkzeug.exceptions as flaskex
#local files
import static.SETTINGS as SETTING
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.generic.commonfunc import SSHConnect, Redfish, \
    CommonRestCall
from business.distributedhosting.nutanix.nutanix import PrismElement, NutanixOperation
from business.authentication.authentication import Vault
from models.atm_models import ModelNtxAutomationRotatePasswordTask, \
    ModelNtxAutomationRotatePasswordTaskSchema, ModelNtxAutomationRotatePasswordTaskLog, \
    ModelNtxAutomationRotatePasswordTaskLogSchema
from models.ntx_models import ModelPrismCentral, ModelPrismElement
from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
from io import StringIO
from business.distributedhosting.nutanix.automation.desired_state_config import DscMaintenanceMode


class PasswordRotate(BaseUpTask):
    LOG_DIR = SETTING.ROTATE_PASSWD_LOG_PATH
    LOG_TYPE = "ROTATE_PASSWD"
    TASK_TYPE = "ROTATE_PASSWD"

    def __init__(self, pe, facility_type, pc_fqdn=None) -> None:
        # pe is PE fqdn
        self.facility_type = facility_type.lower()
        if self.facility_type == "retail":
            model_pe = ModelPrismElement
            model_pc = ModelPrismCentral
        elif self.facility_type == "warehouse":
            model_pe = ModelWarehousePrismElement
            model_pc = ModelWarehousePrismCentral
        self.pe_fqdn = pe
        self.pe = self.pe_fqdn.split(".")[0]
        if pc_fqdn:
            pc_info = model_pc.query.filter_by(fqdn=pc_fqdn).first()
            self.central_pe = pc_info.central_pe_fqdn.split(".")[0]
            self.pc = pc_fqdn
        else:
            pe_info = model_pe.query.filter_by(fqdn=self.pe_fqdn).first()
            pc_info = model_pc.query.filter_by(fqdn=pe_info.prism).first()
            self.central_pe = model_pe.query.filter_by(fqdn=pc_info.central_pe_fqdn).first().name
            self.pc = model_pe.get_prism_by_pe_name(self.pe_fqdn)
        self.is_central_pe = str(self.central_pe).lower().strip() == str(self.pe).lower().strip()
        self.domain = pc_info.domain
        self.pcip = pc_info.ip

        self.vault = Vault(pc_info.tier)
        super().__init__(
            ModelNtxAutomationRotatePasswordTask, ModelNtxAutomationRotatePasswordTaskSchema,
            ModelNtxAutomationRotatePasswordTaskLog, ModelNtxAutomationRotatePasswordTaskLogSchema
        )
        self.task_identifier = self.pe_fqdn
        self.task_duplicated_kwargs = {"pe": self.pe_fqdn}
        self.task_info = {"pe": self.pe_fqdn}

    def init_secret_folder(self):
        vault_passwd_path = os.path.join(SETTING.VAULT_PASSWD_PATH, self.pe)
        if not os.path.exists(vault_passwd_path):
            os.mkdir(vault_passwd_path)
        return vault_passwd_path

    def generate_newpassword(self):
        length = SETTING.NEW_PWD_LENGTH
        characters = string.ascii_letters + string.digits + string.punctuation.replace("'", "").replace("\\", "").replace('"', "")
        while True:
            password = ''.join(random.choice(characters) for i in range(length))
            if (password[0] != '-' and all(any(func(x) for x in password)
                for func in [str.isdigit, str.islower, str.isupper,
                                lambda x: x in string.punctuation.replace("'", "").replace("\\", "").replace('"', "")]) and
                not re.search(r'(.)\1{2,}', password)):
                break
        return password

    # Get host_info
    def get_hosts(self, pc, pe):
        op = NutanixOperation(pc, pe, logger=self.logger, facility_type=self.facility_type)
        res, hosts = op.get_pe_info()
        if not res:
            res, data = self.vault.get_secret(f"{self.pe.upper()}/Site_Pe_Admin")
            sa = {"username": data['username'],  "password": data['secret']}
            res, hosts = op.get_pe_info(sa=sa)
            if not res:
                raise flaskex.BadGateway(f'Can not get Hosts, reason: {hosts}')
        return hosts


    def write_passwd_to_folder(self, hosts):
        self.ilg.write("Writing password to local folder...")
        ahv_inventory_content = "[ahv_hosts]\n"
        cvm_inventory_content = "[cvm_hosts]\n"
        oob_inventory_content = "[oob_hosts]\n"

        for host in hosts:
            # AHV
            ahv_inventory_content += "{} ansible_host={} ansible_user={} ansible_ssh_pass='{}'\n".format(
                host['name'], host['ip'], host['ahv_nutanix']['username'], host['ahv_nutanix']['password'])

            # CVM
            cvm_inventory_content += "{} ansible_host={} ansible_user={} ansible_ssh_pass='{}'\n".format(
                host['name'], host['cvm_ip'], host['cvm_nutanix']['username'], host['cvm_nutanix']['password'])

            # OOB
            oob_inventory_content += "{} ansible_host={} ansible_user={} ansible_ssh_pass='{}'\n".format(
                host['name'], host['oob_ip'], host['oob_admin']['username'], host['oob_admin']['password'])

        def write_inventory_file(filename, content):
            secret_folder = self.init_secret_folder()
            filepath = os.path.join(secret_folder, filename)
            with open(filepath, 'w') as file:
                file.write(content.strip())

        write_inventory_file('ahv_inventory.ini', ahv_inventory_content)
        write_inventory_file('cvm_inventory.ini', cvm_inventory_content)
        write_inventory_file('oob_inventory.ini', oob_inventory_content)

        self.ilg.write('Inventory files have been created.')

    def set_vault_token(self, username, password, label):
        self.ilg.write(f"Saving token to Vault... Username: {username}, label: {label}")
        description = f'Nutanix {username} account'
        self.vault.set_vault_password(username, password, label, description)
        self.ilg.write("Saving token completed.")

    def set_pc_vault_local_token(self, pe, username, password, group):
        label = pe.upper() + '/' + SETTING.PC_VAULT_LOCAL_LABEL[group]
        description = f'Nutanix {username} account'
        self.vault.set_vault_password(username, password, label, description)

    def reset_passwd_pcvm(self, secret_name, target_user, public_key):
        new_passwd = self.generate_newpassword()
        _res, data = self.vault.get_secret(secret_name)
        _res, targetdata = self.vault.get_secret(f'{self.pe.upper()}/{target_user}')
        self.ilg.write('taking a 30S extra powernap')
        time.sleep(30)
        self.ilg.write(f'Start reset PCVM {target_user} password')
        self.reset_passwd_cvm(self.pcip, data['username'], data['secret'], targetdata['username'], new_passwd, public_key)
        self.vault.set_vault_password(
            username=targetdata['username'],
            password=new_passwd,
            label=f"{self.pe.upper()}/{SETTING.PC_VAULT_LABEL[target_user]}",
            description=f'Nutanix {targetdata["username"]} account'
        )

    def task_process(self):
        dsc_maintenance_mode = DscMaintenanceMode(
            pe=self.pe_fqdn, logger=self.logger, db_logger=self.db_logger, facility_type=self.facility_type)
        dsc_maintenance_mode.configure()
        # {'name': 'RETSE999-NX7003', 'ip': '**********', 'cvm_ip': '*********', 'oob_ip': '***********', 'state': 'NORMAL', 'cvm_nutanix': {'username': 'nutanix', 'password': '1Nvv(Qe<5[CZ3Gci'}, 'cvm_admin': {'username': 'admin', 'password': '^g"!X&o%`|4;o{[$'}, 'oob_admin': {'username': 'administrator', 'password': 'SXA;C#uV2i0$Tdg)'}, 'ahv_nutanix': {'username': 'nutanix', 'password': '8&O+{"oJC/gr8TKc'}, 'ahv_root': {'username': 'root', 'password': '+]/5[Z^Y7Em]eTU-'}, 'Site_Gw_Priv_Key': {'username': 'nutanix', 'password': '-----BEGIN RSA PRIVATE KEY-----MIIEowIBAAKCAQEApCVc6uEOUETwfvxcuqN/4VInKJHktxeVhxYKKTIqWZd03ISBG1HXGTCXC0LnyAwPj9mtMH7vXxKq4ylTRJQdeG+8gtVQVWO1R++sgjWKUl737xR0iiUDfHlTXivue41hrXTcxnyOWDN3EvC2ZFwK7e8U71CqxkSJ8KkYSlgVCMZzRTDkGI4u2YUGLc/pbYHTRJBLRgLHgiUoaCXk7/S5+jhrhaCMeCGAwwX1StsuNZRjVOUQ+oawDkgBpMtgV28unWYb/040opAVdzGPEWfpEZ/0O3NYOJ1Y4RY9g8kuCIGpljhqNHZrUyNksNwelZ/5PUDbaM49Xa19wjDTm++1MwIDAQABAoIBAA11FroW+3JvRHxUulF+6BQUBSvZUjQElfi+QNUBzOENfsI5htAzlHv3NNrygEgxXPFBOma4cW2M/T+kerQrpILkPkNHs89cXldoee8u7ok0SgVq2uccg72nwML+vQ2aGMD8zMDlI1Jt3bxKydlOhoLdUA0Swv2ATwLRCjHGKOtQIDRFyB8g7uIaWGLQLOXOTrSby0CHFAV3LEOorBgclqcOPIu3r0XOoEPj8Pjss9kodBdzhMsVp9vfA2moDoB2r9l8NUenYH1a67cYYJYfow/qpm+lh3MFmKHe7a9+mE5Qu2LCoGtFucASuhVpco1uphevErzhSe0QhQwt5TAAFxECgYEAzf/5K9BIQOaIGDNamfLa4eRDinUwTEVxFkXNWL9wyhIv9ETifvexbtMq5p7SqtCYWP8C6Q1p/8YKQ4+TZGWxHHtvtinLMRqwLu7Yq76eW1kuX8gg9Hp4MO6ogUjHTtnbNOlK4BOstK5vHYZig+WGsT3WW0Y+nGxD2o1FhQ64SskCgYEAy/y/0xxvwwTL7TVegIHibYtxZjnddhz2T6cW84sOpRJxZogOkwdsNRMmuaGE4RKLD4iMntIrv1AoDNtawgeyt1wKLpJZB/l9aeQPh2Qeo807GqdypCImsnpuIzhfmavopliwl53Z0Qx0I7YA95pEeIhjqsezFhY4szrgqhV4QhsCgYBu7pVj/rmqGXrYbW8w0BMG6kFk4k6sBAkuEVDPpeg7KSghxqah8QjfwV+s6YJQ8hvYcRtEHTEjx6XnU/3vZsPFrsMtubvuKY0O+Rg9BF2Km6/ZbJAyl3D6CMs9M9AAqljdWXQ0zuOGORklMSUEGio6cC5q4Jz+515y5vi0+oRL4QKBgAqp4dVFbUpCm+6AlboeEBot6fWwHLWp+e5cC8Ubhd53Qlpbznc9MhjsMWAO44VolMBxIPjEf+j2ZcGWTf5KrLsNIrMgj2rtTfMVKwKm5zsk5WwVSNbvuhomcE+DCOxdokHf7QtgAxz8i626O/61n9K1PpCU8/W5n6ku8MOzb5/1AoGBAKj6KTPWEgxcRInn/Y6G3XxIwps+80zFe0sWNR+mGSaLUETrkh68mISV4p9x3zP/ejjSVDiiF1x2k6M4gjFppKBBYaTLY/6XrKxjrd6QiZOgHh6QVO2LrZmC6Z6QT2p1IpzyfZRRhoyyr+Xo5XMpr+B4gKdpb47Kc/fbKAQ15bo2-----END RSA PRIVATE KEY-----'}, 'Site_Gw_Pub_Key': {'username': 'nutanix', 'password': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCkJVzq4Q5QRPB+/Fy6o3/hUicokeS3F5WHFgopMipZl3TchIEbUdcZMJcLQufIDA+P2a0wfu9fEqrjKVNElB14b7yC1VBVY7VH76yCNYpSXvfvFHSKJQN8eVNeK+57jWGtdNzGfI5YM3cS8LZkXArt7xTvUKrGRInwqRhKWBUIxnNFMOQYji7ZhQYtz+ltgdNEkEtGAseCJShoJeTv9Ln6OGuFoIx4IYDDBfVK2y41lGNU5RD6hrAOSAGky2BXby6dZhv/TjSikBV3MY8RZ+kRn/Q7c1g4nVjhFj2DyS4IgamWOGo0dmtTI2Sw3B6Vn/k9QNtozj1drX3CMNOb77Uz ikea\\zoexu6@ITCNSHG-NB0381'}}
        self.rotate_process()

    def reset_passwd_cvm(self, ssh_ip, ssh_user, ssh_pass, target_user, new_pass, public_key=None):
        self.ilg.write(f"Attempting to reset password for user '{target_user}' on cluster associated with CVM {ssh_ip}, authenticating as '{ssh_user}'.")
        # Step 1: Establish initial connection as the admin user (ssh_user) and unlock the target account
        ssh_initial = SSHConnect(ssh_ip, ssh_user, ssh_pass, public_key, logger=self.logger)
        res, _ = ssh_initial.connect()
        if not res:
            raise flaskex.InternalServerError(f"Initial SSH connection to {ssh_ip} as user '{ssh_user}' failed.")
        try:
            # Unlock the target_user account on all nodes
            self.ilg.write(f"Unlocking user '{target_user}' on all CVMs.")
            unlock_command = f"allssh sudo faillock --user {target_user} --reset"
            ssh_initial.exec_command(unlock_command, wait=10)
            # Step 2: Change the password for the target_user on the single connected node
            self.ilg.write(f"Changing password for '{target_user}' on single node {ssh_ip}.")
            passwd_command = f"echo '{new_pass}' | sudo passwd --stdin {target_user}"
            output = ssh_initial.exec_command(passwd_command, wait=5)
            # Perform a basic check for the success keyword in the output.
            output_str = output.decode(errors='ignore').lower()
            if "successfully" not in output_str:
                self.ilg.write(f"Password command may not have succeeded. Output: {output_str}", severity="warning")
        except Exception as e:
            ssh_initial.ssh.close()
            raise flaskex.InternalServerError(f"Failed during unlock or passwd command execution for user '{target_user}': {str(e)}")
        ssh_initial.ssh.close()
        # Step 3: Verify the new password by establishing a new SSH connection AS THE TARGET USER
        self.ilg.write(f"Password change command sent. Verifying new password for '{target_user}' with a new SSH connection.")
        time.sleep(5)
        ssh_verify = SSHConnect(ssh_ip, target_user, new_pass, public_key, logger=self.logger)
        res_verify, _ = ssh_verify.connect(timeout=15)
        if not res_verify:
            raise flaskex.InternalServerError(f"Verification failed. Unable to connect to {ssh_ip} as '{target_user}' with the new password.")
        ssh_verify.ssh.close()
        self.ilg.write(f"Successfully reset and verified new password for user '{target_user}'.")

    def reset_passwd_ahv(self, ssh, target_user, new_pass):
        self.ilg.write(f'unlocking {target_user} account')
        command = f"sudo faillock --user {target_user} --reset"
        ssh.send_command(command)
        time.sleep(10)
        ssh.receive_output()
        command = f"echo '{new_pass}' | sudo passwd --stdin {target_user}"
        ssh.send_command(command)
        time.sleep(15)
        command_output = ssh.receive_output()
        # check if password reset successful
        pattern = r"successfully"
        if not re.search(pattern, command_output):
            raise flaskex.InternalServerError(f'Password update failed for user {target_user}')
        self.ilg.write(f'AHV User {target_user} Password Update Success')

    def reset_passwd_ilo(self, ilo_ip, ilo_user, current_pass, new_pass):
        """
        Resets a specific iLO user's password using the Redfish API.
        It authenticates as the user to change their own password.
        """
        self.ilg.write(f"Connecting to Redfish API on {ilo_ip} to reset password for user '{ilo_user}'.")
        try:
            redfish = Redfish(ip=ilo_ip, username=ilo_user, password=current_pass, logger=self.logger)
            self.logger.info(f"Finding account URI for user '{ilo_user}'.")
            get_accounts_url = "AccountService/Accounts/"
            res, accounts_data = redfish.call_redfish_get(request_url=get_accounts_url)
            if not res:
                raise flaskex.InternalServerError(f"Failed to get user accounts from {ilo_ip}: {accounts_data}")
            target_account_uri = None
            for member in accounts_data.get('Members', []):
                account_uri = member.get('@odata.id')
                if not account_uri:
                    continue
                relative_uri = account_uri.split("/redfish/v1/")[-1].rstrip('/')
                res, account_details = redfish.call_redfish_get(request_url=relative_uri)
                if res and account_details.get('UserName', '').lower() == ilo_user.lower():
                    target_account_uri = relative_uri
                    break
            if not target_account_uri:
                raise flaskex.NotFound(f"Could not find user '{ilo_user}' on iLO {ilo_ip}.")
            self.logger.info(f"Sending PATCH request to {target_account_uri} to update the password.")
            payload = {"Password": new_pass}
            res, data = redfish.update_ilo_object(request_url=target_account_uri, payload=payload)
            if not res:
                error_message = "Unknown error."
                if isinstance(data, dict):
                    error_message = data.get('error', {}).get('message', str(data))
                else:
                    error_message = str(data)
                self.ilg.write(f"Failed to update iLO password for user '{ilo_user}' on {ilo_ip}. Response: {error_message}", severity="error")
                raise flaskex.InternalServerError(f"iLO password update failed on {ilo_ip}: {error_message}")
            self.ilg.write(f"Successfully updated iLO password for user '{ilo_user}' on {ilo_ip}.")
        except Exception as e:
            error_message = f"An unexpected error occurred while trying to reset iLO password on {ilo_ip}: {str(e)}"
            self.ilg.write(error_message, severity="error")
            raise flaskex.InternalServerError(error_message) from e


    def reset_svc(self, new_passwd):
        _res, data = self.vault.get_secret(f"{self.pe.upper()}/Site_Pe_Admin")
        _sa = {"username": data['username'],  "password": data['secret']}
        rest = CommonRestCall(username=_sa['username'], password=_sa['password'], logger=self.logger)
        url = f"https://{self.pe_fqdn}:9440/PrismGateway/services/rest/v1/users/reset_password"
        payload = {
            "username": "1-click-nutanix",
            "password": new_passwd,
        }
        rest.call_restapi(url, method="POST", payload=payload)
        if self.is_central_pe:
            _res, data = self.vault.get_secret(f"{self.pe.upper()}/Site_Pc_Admin")
            _sa = {"username": data['username'],  "password": data['secret']}
            rest = CommonRestCall(username=_sa['username'], password=_sa['password'], logger=self.logger)
            url = f"https://{self.pc}:9440/PrismGateway/services/rest/v1/users/reset_password"
            rest.call_restapi(url, method="POST", payload=payload)

    def rotate_process(self):
        hosts = self.get_hosts(self.pc, self.pe)
        self.ilg.write(f"Checking if cluster '{self.pe}' exists in {self.pc}.")
        cvmips = [host["cvm_ip"] for host in hosts]
        ahvusers = {key: value for key, value in hosts[0].items() if 'ahv_' in key and isinstance(value, dict)}
        oobusers = {key: value for key, value in hosts[0].items() if 'oob_' in key and isinstance(value, dict)}
        cvmusers = {key: value for key, value in hosts[0].items() if 'cvm_' in key and isinstance(value, dict)}

        private_key_file = StringIO(hosts[0]['Site_Gw_Priv_Key']['password'])
        public_key = None
        try:
            public_key = paramiko.RSAKey(file_obj=private_key_file)
        except Exception:
            self.ilg.write("Not a valid RSA private key file")

        login_user = hosts[0]['cvm_nutanix']['username']
        login_passwd = hosts[0]['cvm_nutanix']['password']

        # --- AHV Password Rotation (Optimized Logic) ---
        self.ilg.write("--- Starting AHV Password Rotation ---")

        # 1. Pre-generate new passwords for all AHV users and update Vault first
        ahv_new_passwords = {}
        for key, value in ahvusers.items():
            target_user = value['username']
            self.ilg.write(f"Generating and pre-storing new password for AHV user '{target_user}' in Vault.")
            new_passwd = self.generate_newpassword()
            ahv_new_passwords[target_user] = new_passwd
            self.set_vault_token(target_user, new_passwd, f"{self.pe.upper()}/{SETTING.PE_VAULT_LABEL[key]}")

        # 2. Connect to each CVM once and apply all pre-generated passwords
        for cvm_ip in cvmips:
            self.ilg.write(f"Connecting to CVM {cvm_ip} to apply all AHV password updates.")
            try:
                ssh = SSHConnect(cvm_ip, login_user, login_passwd, public_key, logger=self.logger)
                res, _ = ssh.connect_sshkey()
                if not res:
                    res_sshkey, _ = ssh.connect()
                    if not res_sshkey:
                        self.ilg.write(f"CRITICAL: Failed to connect to CVM {cvm_ip}. Skipping this host. Manual intervention may be required.", severity="error")
                        continue

                ssh.invoke_shell()
                # Connect to the local AHV host from the CVM
                ssh.send_command("ssh root@***********")
                time.sleep(5)

                # Apply the new passwords for all users in this single session
                for target_user, new_passwd in ahv_new_passwords.items():
                    self.ilg.write(f"Applying new password for '{target_user}' on AHV host via CVM {cvm_ip}.")
                    self.reset_passwd_ahv(ssh, target_user, new_passwd)

                ssh.ssh.close()
                self.ilg.write(f"Successfully applied all AHV password updates via CVM {cvm_ip}.")
            except Exception as e:
                self.ilg.write(f"An error occurred while processing CVM {cvm_ip}: {e}. Manual check may be needed as Vault is already updated.", severity="error")

        self.ilg.write("--- Finished AHV Password Rotation ---")


        # --- iLO Password Rotation (Safe Logic) ---
        self.ilg.write("--- Starting iLO Password Rotation ---")
        for key, value in oobusers.items():
            target_user = value['username']
            current_pass = value['password']

            self.ilg.write(f"Starting password rotation for iLO user '{target_user}' across all hosts.")

            # 1. Generate ONE new password for the current user
            new_passwd = self.generate_newpassword()

            # 2. Immediately update the vault with the new password to prevent password loss
            self.ilg.write(f"Updating Vault for iLO user '{target_user}' before applying to hosts.")
            self.set_vault_token(target_user, new_passwd, f"{self.pe.upper()}/{SETTING.PE_VAULT_LABEL[key]}")

            # 3. Iterate through each host to apply the new password
            for host in hosts:
                ilo_ip = host['oob_ip']
                try:
                    self.ilg.write(f"Applying new password for '{target_user}' on iLO {ilo_ip} for host {host['name']}.")
                    self.reset_passwd_ilo(ilo_ip, target_user, current_pass, new_passwd)
                except Exception as e:
                    self.ilg.write(f"CRITICAL: Failed to update iLO password for {target_user} on host {host['name']}. The correct password is in Vault. Manual intervention required. Error: {e}", severity="error")
                    # Continue to the next host
        self.ilg.write("--- Finished iLO Password Rotation ---")


        ### start change CVM  user password
        for key, value in cvmusers.items():
            target_user = value['username']
            new_passwd = self.generate_newpassword()

            # For CVM users, we update Vault first as the recovery path is clearer.
            self.ilg.write(f"Updating Vault for CVM user '{target_user}' before applying.")
            self.set_vault_token(target_user, new_passwd, f"{self.pe.upper()}/{SETTING.PE_VAULT_LABEL[key]}")

            try:
                if target_user == "nutanix":
                    self.ilg.write('Start reset CVM Nutanix password')
                    self.reset_passwd_cvm(f"{self.pe}.{self.domain}", login_user, login_passwd, target_user, new_passwd, public_key)
                else:
                    _res, data = self.vault.get_secret(f"{self.pe.upper()}/Site_Pe_Nutanix")
                    self.ilg.write('taking a 30S extra powernap')
                    time.sleep(30)
                    self.ilg.write(f'Start reset {target_user} password')
                    self.reset_passwd_cvm(f"{self.pe}.{self.domain}", login_user, data['secret'], target_user, new_passwd, public_key)
            except Exception as e:
                 self.ilg.write(f"Failed to apply new password for CVM user '{target_user}': {e}. Manual check may be needed as Vault is already updated.", severity="error")


        #######Reset PCVM password################################
        if self.is_central_pe:
            self.ilg.write("This is a central PE, start to reset PCVM password...")
            for key, value in SETTING.PC_VAULT_LABEL.items():
                self.logger.info(f"Resetting password for {value}...")
                self.reset_passwd_pcvm(f"{self.pe.upper()}/Site_Pc_Nutanix", value, public_key)

        ########Start change service account passwd#########################
        self.logger.info(f'Start reset 1-click-nutanix password for PE {self.pe}')
        self.db_logger.write_resetpwd_log(loginfo=f'Start reset 1-click-nutanix password for PE {self.pe}')
        new_passwd = self.generate_newpassword()
        self.reset_svc(new_passwd)
        if self.is_central_pe:
            self.logger.info(f'This is centrol PE, Start reset 1-click-nutanix password for Pc {self.pc}')
            self.db_logger.write_resetpwd_log(loginfo=f'This is centrol PE,Start reset 1-click-nutanix password for Pc {self.pc}')
            self.set_pc_vault_local_token(self.pe, "1-click-nutanix", new_passwd, "Site_Pc_Svc")
        self.set_vault_token("1-click-nutanix", new_passwd, f"{self.pe.upper()}/Site_Pe_Svc")

        # ##### Start renew ssh_key for PE
        self.renew_ssh_key()

    def renew_ssh_key(self):
        self.ilg.write("Renew SSH key", severity="title")
        _res, data = self.vault.get_secret(f"{self.pe.upper()}/Site_Pe_Admin")
        sa = {"username": data['username'],  "password": data['secret']}
        sshkey = Sshkeys(pe=self.pe_fqdn, pc=self.pc, sa=sa, central_pe=self.central_pe, vault=self.vault, logger=self.logger)
        self.ilg.write('Check if ssh_key exist')
        res = sshkey.get_pe_keys()
        if res:
            self.ilg.write('SSH key exist, we need replace it.')
            sshkey.delete_pe_public_key()
        if self.is_central_pe:
            res = sshkey.get_pc_keys()
            if res:
                self.ilg.write('SSH key exist, we need replace it.')
                sshkey.delete_pc_public_key()
        self.ilg.write('Generating ssh_key')
        public_key, private_key = sshkey.generate_ssh_key(private_key=os.path.join(sshkey.sshpath, "prvkey"))
        sshkey.install_pe_public_key(public_key)
        if self.is_central_pe:
            sshkey.install_pc_public_key(public_key)
        self.set_vault_token("nutanix", private_key, f"{self.pe.upper()}/{SETTING.PE_VAULT_LABEL['Site_Gw_Priv_Key']}")
        self.set_vault_token("nutanix", public_key, f"{self.pe.upper()}/{SETTING.PE_VAULT_LABEL['Site_Gw_Pub_Key']}")


class Sshkeys():
    def __init__(self, pc=None, pe=None, sa=None, central_pe=None, vault=None, logger=None) -> None:
        self.pe = PrismElement(pe=pe, logger=logger).pe
        self.pe_fqdn = PrismElement(pe=pe, logger=logger).pe_fqdn
        self.pc = pc
        self.logger = logger
        self.rest = CommonRestCall(username=sa['username'], password=sa['password'], logger=self.logger)
        _res, data  = vault.get_secret(f"{central_pe.upper()}/Site_Pc_Admin")
        pc_sa = {"username": data['username'],  "password": data['secret']}
        self.rest_pc = CommonRestCall(username=pc_sa['username'], password=pc_sa['password'], logger=self.logger)
        self.sshpath = self.init_ssh_folder()

    def init_ssh_folder(self):
        ssh_key_path = os.path.join(SETTING.SSH_KEY_PATH, f'{self.pe}_{datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}')
        if not os.path.exists(ssh_key_path):
            os.mkdir(ssh_key_path)
        return ssh_key_path

    def generate_ssh_key(self, private_key, key_type="rsa", bits=2048):
        if os.path.exists(private_key):
            self.logger.info(f"Removing existing key: {private_key}")
            os.remove(private_key)

        command = f'ssh-keygen -t {key_type} -b {bits} -f {private_key} -q -N "" -m PEM'

        try:
            self.logger.info(f"Generating SSH key: {command}")
            subprocess.run(command, shell=True, check=True)
        except subprocess.CalledProcessError as e:
            raise Exception(f"Error generating key: {e}")

        public_key = f"{private_key}.pub"

        self.logger.info(f"Key pair generated: {private_key}")

        with open(f"{public_key}", "r") as key:
            public_key_str = key.read().strip()
        with open(f"{private_key}", "r") as key:
            private_key_str = key.read().strip()
        return public_key_str, private_key_str

    def get_pe_keys(self):
        url = f"https://{self.pe_fqdn}:9440/PrismGateway/services/rest/v1/cluster/public_keys"
        res = self.rest.call_restapi(url, method="GET")
        return res.json()

    def delete_pe_public_key(self, keyname="Gateway"):
        self.logger.info("Deleting public key...")
        url = f"https://{self.pe_fqdn}:9440/PrismGateway/services/rest/v1/cluster/public_keys/{keyname}"
        self.rest.call_restapi(url, method="DELETE")
        self.logger.info("Delete public key finished.")

    def install_pe_public_key(self, public_key):
        self.logger.info("Installing public key...")
        url = f"https://{self.pe_fqdn}:9440/PrismGateway/services/rest/v1/cluster/public_keys"
        payload = {
            "name": "Gateway",
            "key": public_key,
        }
        self.rest.call_restapi(url, method="POST", payload=payload)
        self.logger.info("Install public key finished.")

    def get_pc_keys(self):
        url = f"https://{self.pc}:9440/PrismGateway/services/rest/v1/cluster/public_keys"
        res = self.rest_pc.call_restapi(url, method="GET")
        return res.json()

    def delete_pc_public_key(self, keyname="Gateway"):
        self.logger.info("Deleting public key...")
        url = f"https://{self.pc}:9440/PrismGateway/services/rest/v1/cluster/public_keys/{keyname}"
        self.rest_pc.call_restapi(url, method="DELETE")
        self.logger.info("Delete public key finished.")

    def install_pc_public_key(self, public_key):
        self.logger.info("Installing public key...")
        url = f"https://{self.pc}:9440/PrismGateway/services/rest/v1/cluster/public_keys"
        payload = {
            "name": "Gateway",
            "key": public_key,
        }
        self.rest_pc.call_restapi(url, method="POST", payload=payload)
        self.logger.info("Install public key finished.")

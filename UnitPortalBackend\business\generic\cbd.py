# installed module
# local file
from business.authentication.authentication import ServiceAccount
from business.generic.commonfunc import CommonRestCall


class Cbd():
    def __init__(self, tier=None, client_id=None, client_secret=None, logger=None, retry=5) -> None:
        fqdn = {
            "stage": "api.ingka.ppe.ikeadt.com",
            "production": "api.ingka.ikea.com"
        }
        if not tier:
            raise Exception("The tier is not given, it's mandatory!")
        self.tier = tier
        self.api_endpoint = f"https://{fqdn[self.tier]}/ecbd/api"
        if not(client_id and client_secret):
            sa = ServiceAccount().get_service_account(usage=f"cbd_{tier}")
            client_id, client_secret = sa['username'], sa['password']
        self.logger = logger
        self.rest = CommonRestCall(logger=self.logger)
        self.retry = retry
        self.token = f"Bearer {self.get_access_token(client_id=client_id, client_secret=client_secret)}"

    def call_cbd_get(self, request_url):
        url = f"{self.api_endpoint}{request_url}"
        headers = {
            "Authorization": self.token
        }
        return self.rest.call_restapi(url=url, method="GET", headers=headers, retry=self.retry)
    
    def get_access_token(self, client_id, client_secret):
        token_endpoint = {
            "stage": "https://login.microsoftonline.com/275ecf73-5c59-43a6-9302-1fc535a308bb/oauth2/v2.0/token",
            "production": "https://login.microsoftonline.com/720b637a-655a-40cf-816a-f22f40755c2c/oauth2/v2.0/token"
        }
        token_scope = {
            "stage": "https://api.test.ingkadt.com/.default",
            "production": "https://api.prod.ingka.com/.default"
        }
        url = token_endpoint[self.tier]
        payload = {
            "client_id": client_id,
            "client_secret": client_secret,
            "scope": token_scope[self.tier],
            "grant_type": "client_credentials"
        }
        resp = self.rest.call_restapi(url=url, method="POST", payload=payload, is_json=False)
        return (resp.json()).get('access_token')
    
    def get_business_unit(self, bu_type=None, bu_code=None, country=None, page_no=0, page_size=20):
        content = []
        while True:
            request_url = f"/business-unit/v1?pageNo={page_no}&pageSize={page_size}"
            if bu_type:
                request_url += f"&type={bu_type}"
            if bu_code:
                request_url += f"&code={bu_code}"
            if country:
                request_url += f"&country={country}"
            self.logger.info(f"Loading page {page_no + 1}")
            try:
                self.logger.info(f"Getting business unit data through {request_url}")
                data = self.call_cbd_get(request_url=request_url)
                content += data.json().get('content')
                page_no += 1
                if not data.json().get('hasNextPage'):
                    self.logger.info("It's the last page")
                    break
            except Exception as e:
                self.logger.warning(f"It's failed to request {request_url}, reason: {e}")
                break
        return content

    def get_bu_address_calendar(self, bu_type=None, bu_code=None, country=None, page_no=0, page_size=20):
        content = []
        while True:
            request_url = f"/bu-address-calendar/v1?pageNo={page_no}&pageSize={page_size}"
            if bu_type:
                request_url += f"&type={bu_type}"
            if bu_code:
                request_url += f"&code={bu_code}"
            if country:
                request_url += f"&country={country}"
            self.logger.info(f"Loading page {page_no + 1}")
            data = self.call_cbd_get(request_url=request_url)
            content += data.json().get('content')
            page_no += 1
            if not data.json().get('hasNextPage'):
                self.logger.info("It's the last page")
                break
        return content

    def get_customer_meeting_point(self, code=None, format=None, lig=None, country=None, page_no=0, page_size=20):
        content = []
        while True:
            request_url = f"/cmp/v1?pageNo={page_no}&pageSize={page_size}"
            if code:
                request_url += f"&code={code}"
            if format:
                request_url += f"&format={format}"
            if lig:
                request_url += f"&lig={lig}"
            if country:
                request_url += f"&country={country}"
            self.logger.info(f"Loading page {page_no + 1}")
            data = self.call_cbd_get(request_url=request_url)
            content += data.json().get('content')
            page_no += 1
            if not data.json().get('hasNextPage'):
                self.logger.info("It's the last page")
                break
        return content

    def get_ga_country(self, country=None, continent=None, page_no=0, page_size=20):
        content = []
        while True:
            request_url = f"/ga-country/v1?pageNo={page_no}&pageSize={page_size}"
            if country:
                request_url += f"&country={country}"
            if continent:
                request_url += f"&continent={continent}"
            self.logger.info(f"Loading page {page_no + 1}")
            data = self.call_cbd_get(request_url=request_url)
            content += data.json().get('content')
            page_no += 1
            if not data.json().get('hasNextPage'):
                self.logger.info("It's the last page")
                break
        return content
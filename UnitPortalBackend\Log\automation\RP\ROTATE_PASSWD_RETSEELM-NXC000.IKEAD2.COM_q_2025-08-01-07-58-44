2025-08-01 15:58:44,603 INFO Start to run the task.
2025-08-01 15:58:48,026 INFO Checking Maintenance Mode
2025-08-01 15:58:48,027 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-08-01 15:58:48,027 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-01 15:58:50,535 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 15:58:50,535 INFO SSH Executing '/home/<USER>/prism/cli/ncli host list --json=pretty'.
2025-08-01 15:58:51,382 INFO Waiting for 5 seconds for the execution.
2025-08-01 15:58:56,396 INFO stdout: b'{\n  "data" : [ {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::4",\n    "uuid" : "8a276a5a-9cd0-4702-8ed9-5699d07c192e",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH967RD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::60",\n        "diskUuid" : "ba87f2bb-2824-44e3-8240-75c32c2c4e80",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH967RD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH99N2D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::51",\n        "diskUuid" : "35ea81b9-7cfb-4924-9b45-f07a4cd6fc3f",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N2D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH7B71D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::53",\n        "diskUuid" : "65a27c83-885e-4672-be27-05971598814b",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7B71D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9726D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::61",\n        "diskUuid" : "f4390867-7871-494b-8fd2-65dfa19de512",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9726D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH9B3ED",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::58",\n        "diskUuid" : "9979a224-9d9e-42bc-a282-b1af4e2f9acd",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B3ED",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9421D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::52",\n        "diskUuid" : "01e76618-04df-4259-a761-7e0bab50887b",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9421D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH8DKKD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::56",\n        "diskUuid" : "e31d9325-c534-4c9e-a67e-65081ebff024",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8DKKD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98J3D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::57",\n        "diskUuid" : "c61322d0-febf-45eb-8293-8bb50ef57e95",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98J3D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307893",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::55",\n        "diskUuid" : "d55987bc-a972-43e0-8c49-380c1fed8ff0",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307893",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307888",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::54",\n        "diskUuid" : "514be4f9-c6dc-4f16-805f-8372704d5f1d",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307888",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7001",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********29",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********29"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8S",\n    "blockSerial" : "CZ20240J8S",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753432724206906,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "20000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "0",\n      "controller_num_iops" : "62",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "243227",\n      "controller_num_write_io" : "1243",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "1477949",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "2486",\n      "controller_total_io_size_kbytes" : "11608",\n      "content_cache_hit_ppm" : "565969",\n      "controller_num_io" : "1243",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "96",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1189",\n      "num_io" : "3",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "580",\n      "hypervisor_num_received_bytes" : "2568836751463",\n      "hypervisor_timespan_usecs" : "29976185",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "0",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "65",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "580",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "1926336145205",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "0",\n      "hypervisor_memory_usage_ppm" : "412539",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "62",\n      "total_io_time_usecs" : "197",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "0",\n      "write_io_bandwidth_kBps" : "1",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "3",\n      "total_io_size_kbytes" : "24",\n      "io_bandwidth_kBps" : "1",\n      "content_cache_physical_memory_usage_bytes" : "2444366568",\n      "controller_timespan_usecs" : "20000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-78537208",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "1000000",\n      "controller_avg_write_io_latency_usecs" : "1189",\n      "content_cache_logical_memory_usage_bytes" : "2365829360"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "6679638016",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "864151224320",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97634365621864",\n      "storage_tier.ssd.usage_bytes" : "************",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91529282270824",\n      "storage.usage_bytes" : "************",\n      "storage_tier.ssd.free_bytes" : "6105083351040"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::5",\n    "uuid" : "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH9453D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::63",\n        "diskUuid" : "55b9bef0-e64e-4b83-b56d-07424ac5a4fa",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9453D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH9B0JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::69",\n        "diskUuid" : "87299dda-79ad-4648-a18d-867837bdb061",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B0JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH947JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::66",\n        "diskUuid" : "21accc04-9438-4b1e-a735-121e6651e2c1",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH947JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9B2GD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::68",\n        "diskUuid" : "8fcec93c-f3a5-4a88-a18f-ad5a75a3d36e",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B2GD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH95WYD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::64",\n        "diskUuid" : "190548f4-f43c-40fd-8e0d-c805d972bf31",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH95WYD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9984D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::67",\n        "diskUuid" : "f73b0d65-6966-4e81-92fb-7815e0986aea",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9984D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH99N1D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::70",\n        "diskUuid" : "ebe3c979-d24f-46fb-9f2d-06b675e7f957",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N1D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98WLD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::65",\n        "diskUuid" : "3b10236d-18ef-48d1-b777-8f9f6b64ced8",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98WLD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N200095",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::72",\n        "diskUuid" : "4ce56fa0-31fa-45aa-9740-39f6561ab0e2",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N200095",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307878",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::71",\n        "diskUuid" : "064c52a5-ac8a-4f7f-ae98-967f225ddb32",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307878",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7002",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8R",\n    "blockSerial" : "CZ20240J8R",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753430337664248,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "20000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "277777",\n      "controller_num_iops" : "68",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "1130",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "245324",\n      "controller_num_write_io" : "2041",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "3380739",\n      "controller_total_read_io_size_kbytes" : "12",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "1467",\n      "content_cache_num_lookups" : "2306",\n      "controller_total_io_size_kbytes" : "19744",\n      "content_cache_hit_ppm" : "540329",\n      "controller_num_io" : "2044",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "95",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1653",\n      "num_io" : "18",\n      "controller_num_read_io" : "3",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "658",\n      "hypervisor_num_received_bytes" : "2901140220722",\n      "hypervisor_timespan_usecs" : "29991533",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "25",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "798",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "657",\n      "controller_write_io_ppm" : "998532",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "1953581581902",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "1",\n      "hypervisor_memory_usage_ppm" : "412461",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "68",\n      "total_io_time_usecs" : "14365",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "4",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "5",\n      "write_io_bandwidth_kBps" : "7",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "376",\n      "num_write_io" : "13",\n      "total_io_size_kbytes" : "174",\n      "io_bandwidth_kBps" : "8",\n      "content_cache_physical_memory_usage_bytes" : "1929555492",\n      "controller_timespan_usecs" : "30000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-77593084",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "722222",\n      "controller_avg_write_io_latency_usecs" : "1655",\n      "content_cache_logical_memory_usage_bytes" : "1851962408"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "6783471616",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "915138314240",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97580172174952",\n      "storage_tier.ssd.usage_bytes" : "************",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91529178437224",\n      "storage.usage_bytes" : "************",\n      "storage_tier.ssd.free_bytes" : "6050993737728"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::6",\n    "uuid" : "34ff0abd-9dee-4e7d-9481-4716d64569b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH96JDD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::41",\n        "diskUuid" : "2c172a78-3626-4861-bfff-98d6d79fef49",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH96JDD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH995TD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::43",\n        "diskUuid" : "94de93d2-5e8f-44d7-85d7-c414177670a7",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH995TD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH991DD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::46",\n        "diskUuid" : "4d3bffc9-fa0d-4ff3-bd95-436cf68ab516",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH991DD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9825D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::45",\n        "diskUuid" : "ea324991-7bb3-49a9-84f6-7c30e1c938a5",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9825D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH8XYHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::48",\n        "diskUuid" : "5f14642e-c45b-4efc-b5ee-95c532192c22",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8XYHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH99MHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::44",\n        "diskUuid" : "5758e233-4edc-4045-8446-0625e9ba05c0",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99MHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH9ADHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::47",\n        "diskUuid" : "dbeb0a0e-ff11-4b98-a2b7-a45bad3407fd",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9ADHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH7XGHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::42",\n        "diskUuid" : "08dba0c0-cd98-4d75-a131-a60323ba43b5",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7XGHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307864",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::50",\n        "diskUuid" : "39e3bde6-2802-4ad2-825c-a0d61ae9fb35",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307864",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307881",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::49",\n        "diskUuid" : "a032c48d-3866-4fd6-88af-1f6a25fe5ac3",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307881",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7003",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********31",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********31"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8Q",\n    "blockSerial" : "CZ20240J8Q",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 4,\n    "bootTimeInUsecs" : 1753435095317913,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "444444",\n      "controller_num_iops" : "55",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "461",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "199160",\n      "controller_num_write_io" : "1108",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "1061166",\n      "controller_total_read_io_size_kbytes" : "4",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "901",\n      "content_cache_num_lookups" : "328",\n      "controller_total_io_size_kbytes" : "8180",\n      "content_cache_hit_ppm" : "948170",\n      "controller_num_io" : "1109",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "96",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "956",\n      "num_io" : "9",\n      "controller_num_read_io" : "1",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "409",\n      "hypervisor_num_received_bytes" : "1886354594792",\n      "hypervisor_timespan_usecs" : "30130011",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "30",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "317",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "408",\n      "controller_write_io_ppm" : "999098",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "3187459403842",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "3",\n      "hypervisor_memory_usage_ppm" : "509738",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "55",\n      "total_io_time_usecs" : "2860",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "4",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "4",\n      "write_io_bandwidth_kBps" : "2",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "7",\n      "controller_avg_read_io_latency_usecs" : "461",\n      "num_write_io" : "5",\n      "total_io_size_kbytes" : "58",\n      "io_bandwidth_kBps" : "5",\n      "content_cache_physical_memory_usage_bytes" : "2545128296",\n      "controller_timespan_usecs" : "20000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-79246200",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "555555",\n      "controller_avg_write_io_latency_usecs" : "957",\n      "content_cache_logical_memory_usage_bytes" : "2465882096"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "5840179200",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "1058565357568",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97426518455912",\n      "storage_tier.ssd.usage_bytes" : "822321405952",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91530121729640",\n      "storage.usage_bytes" : "828161585152",\n      "storage_tier.ssd.free_bytes" : "5896396726272"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  } ],\n  "status" : 0\n}\n'
2025-08-01 15:58:56,412 INFO All good, no hosts are set as NCLI maintenance inside this cluster.
2025-08-01 15:58:56,412 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-08-01 15:58:56,413 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-01 15:58:58,979 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 15:58:58,980 INFO SSH Executing '/usr/local/nutanix/bin/acli -o json host.list'.
2025-08-01 15:58:59,806 INFO Waiting for 5 seconds for the execution.
2025-08-01 15:59:04,807 INFO stdout: b'{"data": [{"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "8a276a5a-9cd0-4702-8ed9-5699d07c192e", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "34ff0abd-9dee-4e7d-9481-4716d64569b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}], "error": null, "status": 0}\n'
2025-08-01 15:59:04,807 INFO This seems a very old AOS version...
2025-08-01 15:59:04,807 INFO This seems a very old AOS version...
2025-08-01 15:59:04,807 INFO This seems a very old AOS version...
2025-08-01 15:59:04,820 INFO All good, no hosts are set as ACLI maintenance inside this cluster.
2025-08-01 15:59:04,841 INFO Checking CVM status
2025-08-01 15:59:05,380 INFO Trying to SSH to the RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 15:59:05,380 INFO First try with username/password.
2025-08-01 15:59:05,380 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-01 15:59:07,875 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 15:59:13,963 INFO Sending 'cluster status |grep -v UP' to the server.
2025-08-01 15:59:29,965 INFO CVM IP:*********** Status:Up
2025-08-01 15:59:29,965 INFO CVM IP:*********** Status:Up
2025-08-01 15:59:29,966 INFO CVM IP:*********** Status:Up
2025-08-01 15:59:29,966 INFO Great, all CVM status are Up
2025-08-01 15:59:30,011 INFO Calling restapi, URL: https://retseelm-nxc000.ikead2.com:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 15:59:30,012 INFO params: None
2025-08-01 15:59:30,012 INFO User: <EMAIL>
2025-08-01 15:59:30,012 INFO payload: None
2025-08-01 15:59:30,012 INFO files: None
2025-08-01 15:59:30,012 INFO timeout: 30
2025-08-01 15:59:31,798 INFO Getting host list from retseelm-nxc000.
2025-08-01 15:59:31,799 INFO Got the host list from retseelm-nxc000.
2025-08-01 15:59:31,799 INFO Got the host list.
2025-08-01 15:59:31,799 INFO Getting vault from IKEAD2.
2025-08-01 15:59:33,068 INFO Getting Site_Pe_Nutanix.
2025-08-01 15:59:33,723 INFO Got Site_Pe_Nutanix.
2025-08-01 15:59:33,723 INFO Getting Site_Pe_Admin.
2025-08-01 15:59:34,318 INFO Got Site_Pe_Admin.
2025-08-01 15:59:34,318 INFO Getting Site_Oob.
2025-08-01 15:59:34,807 INFO Got Site_Oob.
2025-08-01 15:59:34,808 INFO Getting Site_Ahv_Nutanix.
2025-08-01 15:59:35,274 INFO Got Site_Ahv_Nutanix.
2025-08-01 15:59:35,275 INFO Getting Site_Ahv_Root.
2025-08-01 15:59:35,753 INFO Got Site_Ahv_Root.
2025-08-01 15:59:35,754 INFO Getting Site_Gw_Priv_Key.
2025-08-01 15:59:36,298 INFO Got Site_Gw_Priv_Key.
2025-08-01 15:59:36,298 INFO Getting Site_Gw_Pub_Key.
2025-08-01 15:59:36,779 INFO Got Site_Gw_Pub_Key.
2025-08-01 15:59:36,779 INFO Getting Site_Pe_Svc.
2025-08-01 15:59:37,326 INFO Got Site_Pe_Svc.
2025-08-01 15:59:37,338 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-01 15:59:43,576 INFO Starting to reset iLO password for user 'administrator' across all hosts.
2025-08-01 15:59:45,531 INFO Updating host RETSEELM-NX7001 (***********29)
2025-08-01 15:59:46,717 INFO Connecting to Redfish API on ***********29 to reset password for user 'administrator'.
2025-08-01 15:59:48,483 INFO Finding account URI for user 'administrator'.
2025-08-01 15:59:49,626 INFO Calling restapi, URL: https://***********29/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-01 15:59:49,627 INFO params: None
2025-08-01 15:59:49,627 INFO User: administrator
2025-08-01 15:59:49,628 INFO payload: None
2025-08-01 15:59:49,628 INFO files: None
2025-08-01 15:59:49,628 INFO timeout: None
2025-08-01 15:59:50,811 INFO Got the response with OK
2025-08-01 15:59:54,440 INFO Calling restapi, URL: https://***********29/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-01 15:59:54,441 INFO params: None
2025-08-01 15:59:54,441 INFO User: administrator
2025-08-01 15:59:54,441 INFO payload: None
2025-08-01 15:59:54,441 INFO files: None
2025-08-01 15:59:54,441 INFO timeout: None
2025-08-01 15:59:55,613 INFO Got the response with OK
2025-08-01 15:59:55,628 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-01 15:59:56,961 INFO Calling restapi, URL: https://***********29/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-01 15:59:56,961 INFO params: None
2025-08-01 15:59:56,961 INFO User: administrator
2025-08-01 15:59:56,962 INFO payload: {'Password': '*****'}
2025-08-01 15:59:56,962 INFO files: None
2025-08-01 15:59:56,962 INFO timeout: None
2025-08-01 15:59:58,397 INFO ILO object updated successfully
2025-08-01 16:00:00,596 ERROR Failed to update iLO password for user 'administrator' on ***********29.
2025-08-01 16:00:22,627 INFO Successfully updated iLO password for user 'administrator' on ***********29.
2025-08-01 16:00:22,639 INFO Updating host RETSEELM-NX7002 (*************)
2025-08-01 16:00:22,695 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-01 16:00:22,717 INFO Finding account URI for user 'administrator'.
2025-08-01 16:00:22,717 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-01 16:00:22,717 INFO params: None
2025-08-01 16:00:22,717 INFO User: administrator
2025-08-01 16:00:22,717 INFO payload: None
2025-08-01 16:00:22,717 INFO files: None
2025-08-01 16:00:22,717 INFO timeout: None
2025-08-01 16:00:23,944 INFO Got the response with OK
2025-08-01 16:00:23,954 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-01 16:00:23,954 INFO params: None
2025-08-01 16:00:23,954 INFO User: administrator
2025-08-01 16:00:23,954 INFO payload: None
2025-08-01 16:00:23,954 INFO files: None
2025-08-01 16:00:23,954 INFO timeout: None
2025-08-01 16:00:25,338 INFO Got the response with OK
2025-08-01 16:00:25,338 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-01 16:00:31,661 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-01 16:00:31,661 INFO params: None
2025-08-01 16:00:31,661 INFO User: administrator
2025-08-01 16:00:31,662 INFO payload: {'Password': '*****'}
2025-08-01 16:00:31,662 INFO files: None
2025-08-01 16:00:31,662 INFO timeout: None
2025-08-01 16:00:32,897 INFO ILO object updated successfully
2025-08-01 16:00:32,914 ERROR Failed to update iLO password for user 'administrator' on *************.
2025-08-01 16:00:32,917 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-01 16:00:32,932 INFO Updating host RETSEELM-NX7003 (***********31)
2025-08-01 16:00:32,947 INFO Connecting to Redfish API on ***********31 to reset password for user 'administrator'.
2025-08-01 16:00:32,960 INFO Finding account URI for user 'administrator'.
2025-08-01 16:00:32,960 INFO Calling restapi, URL: https://***********31/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-01 16:00:32,960 INFO params: None
2025-08-01 16:00:32,960 INFO User: administrator
2025-08-01 16:00:32,961 INFO payload: None
2025-08-01 16:00:32,961 INFO files: None
2025-08-01 16:00:32,961 INFO timeout: None
2025-08-01 16:00:34,109 INFO Got the response with OK
2025-08-01 16:00:34,110 INFO Calling restapi, URL: https://***********31/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-01 16:00:34,111 INFO params: None
2025-08-01 16:00:34,111 INFO User: administrator
2025-08-01 16:00:34,111 INFO payload: None
2025-08-01 16:00:34,111 INFO files: None
2025-08-01 16:00:34,111 INFO timeout: None
2025-08-01 16:00:35,261 INFO Got the response with OK
2025-08-01 16:00:35,277 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-01 16:00:37,525 INFO Calling restapi, URL: https://***********31/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-01 16:00:37,526 INFO params: None
2025-08-01 16:00:37,526 INFO User: administrator
2025-08-01 16:00:37,526 INFO payload: {'Password': '*****'}
2025-08-01 16:00:37,527 INFO files: None
2025-08-01 16:00:37,527 INFO timeout: None
2025-08-01 16:00:38,981 INFO ILO object updated successfully
2025-08-01 16:00:38,992 ERROR Failed to update iLO password for user 'administrator' on ***********31.
2025-08-01 16:00:39,004 INFO Successfully updated iLO password for user 'administrator' on ***********31.
2025-08-01 16:00:39,016 INFO Successfully updated password for 'administrator' on all hosts. Saving to Vault.
2025-08-01 16:00:39,025 INFO Saving token to Vault... Username: administrator, label: RETSEELM-NXC000/Site_Oob
2025-08-01 16:00:39,714 INFO Saving token completed.
2025-08-01 16:50:59,267 INFO Start reset CVM Nutanix password
2025-08-01 16:50:59,269 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-01 16:51:01,794 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-01 16:51:02,908 INFO unlocking nutanix account
2025-08-01 16:51:02,909 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.

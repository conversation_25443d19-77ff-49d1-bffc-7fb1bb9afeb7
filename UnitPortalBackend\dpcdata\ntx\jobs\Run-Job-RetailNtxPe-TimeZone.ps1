$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Get-NtxControlPanel(){
    Param (
        [string] $Fqdn,
        [string] $Uuid,
                 $Auth
    )
    if ($Uuid) {
        if ($PrismCall = Rest-Prism-v3-Get-App -Fqdn $Fqdn -Uuid $Uuid -Auth $Auth) {
            $ControlPanel = [PSCustomObject]@{
                'Name'              = $PrismCall.status.name
                'State'             = $PrismCall.status.state
                'Uuid'              = $PrismCall.status.uuid
                'Cluster'           = $PrismCall.metadata.project_reference.name
                'SiteNameCode'      = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AB_SiteNameCode"}).value
                'AhvSubnet'         = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AD_SiteSubnetId"}).value
                'SiteType'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AG_SiteProfile"}).value
                'RemoteSite'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "ZZZ_REPLTarget"}).value
                'BackupBandwidth'   = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AH_SiteBackupBandwidth"}).value
                'DarksiteBandwidth' = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AH_SiteDarkSiteBandwidth"}).value
                'Timezone'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AJ_SiteTimeZone"}).value
                'Latitude'          = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AK_SiteLatitude"}).value
                'Longtitude'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AK_SiteLongitude"}).value
                'AosVersion'        = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "AN_SiteAosVersion"}).value
                'ConfigVersion'     = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "Z_ReleaseCustomer"}).value
                'CoreFwVersion'     = ($PrismCall.spec.resources.variable_list | Where-Object {$_.name -eq "Z_ReleaseFramework"}).value
            }
            return $ControlPanel
        }
    }else {
        $ControlPanels = @()
        if ($PrismCall = Rest-Prism-v3-List-App -Fqdn $Fqdn -Filter "name==.*CPL.*;_state!=deleted" -Auth $Auth) {
            $PrismCall | ForEach-Object {
                $ControlPanel = [PSCustomObject]@{
                    'Name'  = $_.status.name
                    'Uuid'  = $_.status.uuid
                    'Prism' = $_.metadata.project_reference.name
                }
                $ControlPanels += $ControlPanel
            }
        }
        return $ControlPanels
    }
}
function Launch-Job(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        #Grab the function definition of Get-NtxControlPanel to a variable, it will be used in the parallized loop
        $GetNtxControlPanel = ${function:Get-NtxControlPanel}.ToString()
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Load data from the table dh_retail_ntx_pe, and filter out the decommissioned sites
        #Create an empty array $CpCollection that stores control panels come from Prism Central
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CpCollection     = @()
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhPEs.Count)' PEs need to update" -DumpFile $DumpFile
        #foreach ($PC in $DhPCs | Where-Object {$_.tier -eq "Production"}) {
        foreach ($PC in $DhPCs) {
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now getting CPLs from '$($PC.Fqdn)'" -DumpFile $DumpFile
            #Get the service account and authentication string for calling PC to get the list of control panels
            $SvcAccount = Select-DhServiceAccount -Vars $Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for '$($PC.name)'" -DumpFile $DumpFile
                continue
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for '$($PC.name)'" -DumpFile $DumpFile
                continue
            }
            #Returned control panels are stored into the array $CpCollection
            $CpCollection += Get-NtxControlPanel -Fqdn $PC.fqdn -Auth $Auth
        }
        $DhPEs | Foreach-Object -ThrottleLimit 50 -Parallel {
            $Global:DumpFile = $using:DumpFile
            #Import functions defined in other modules
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $DumpFile -Value $Message
                    Exit 0
                }
            }
            #Import function defined out of the parallel loop
            ${function:Get-NtxControlPanel} = $using:GetNtxControlPanel
            #Get the timezone variable from control panel for each PEs
            #Locate corresponded Prism Central
            #Get the service account and authentication string for REST call
            #Set the default timezone by reference the Prism Central for deal with the case of when the timezone value of PE is not available
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $PE              = $_
            $PC              = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $UpdateDict      = $using:CollectionUpdate
            $SvcAccount      = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            $DefaultTimeZone = if ($PC.fqdn -match "ssp-eu-ntx") {
                "UTC+02:00"
            }elseif ($PC.fqdn -match "ssp-apac-ntx") {
                "UTC+08:00"
            }elseif ($PC.fqdn -match "ssp-china-ntx") {
                "UTC+08:00"
            }elseif ($PC.fqdn -match "ssp-na-ntx") {
                "UTC-05:00"
            }elseif ($PC.fqdn -match "ssp-russia-ntx") {
                "UTC+04:00"
            }elseif ($PC.fqdn -match "ssp-ppe-ntx") {
                "UTC+02:00"
            }else {
                "UTC+00:00"
            }
            #Create a object stores the mapping of PE and timezone
            $TzMap = [PSCustomObject]@{
                'fqdn'     = $PE.fqdn
                'timezone' = $DefaultTimeZone
            }
            #We can skip to get the timezone when the PE is not a production environment and take the default timezone
            if ($PC.tier -ne "Production") {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "The PE '$($PE.name)' is a non-production environment, let's skip" -DumpFile $using:DumpFile
                $UpdateDict.Add($TzMap)
                return
            }
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            #Find the control panel of current PE, and get its UUID
            $Cp = $using:CpCollection | Where-Object {$_.Name -match $PE.name}
            if (!$Cp) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "It's unable to get control panel for '$($PE.name)'" -DumpFile $using:DumpFile
                $UpdateDict.Add($TzMap)
                return
            }
            #Get the details of control panel through REST call with the UUID
            $PrismCall      = Get-NtxControlPanel -Fqdn $PC.fqdn -Uuid $Cp.Uuid -Auth $Auth
            $TzMap.timezone = if ($PrismCall.Timezone) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We've get the details of control panel for '$($PE.name)'" -DumpFile $using:DumpFile
                $PrismCall.Timezone
            }
            $UpdateDict.Add($TzMap)
        }
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pe]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxPe-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job
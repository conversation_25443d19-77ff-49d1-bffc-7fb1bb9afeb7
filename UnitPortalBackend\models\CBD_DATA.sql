-- ----------------------------
-- Table structure for dh_cbd
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[dh_cbd]') AND type IN ('U'))
	DROP TABLE [dbo].[dh_cbd]

CREATE TABLE
  dh_cbd (
    id varchar(100) NOT NULL,
    bu_type varchar(100) NULL,
    bu_code varchar(100) NULL,
    name varchar(100) NULL,
    street varchar(100) NULL,
    postal_code varchar(100) NULL,
    city varchar(100) NULL,
    country_code varchar(100) NULL,
    time_zone varchar(100) NULL,
    latitude float NULL,
    longitude float NULL,
  )
ALTER TABLE [dbo].[dh_cbd] ADD CONSTRAINT PK__dh_cbd__3213E83F48CFB61C PRIMARY KEY (id)

-- add columns 'cbd_id', 'bu_type', 'bu_code' to table 'dh_retail_ntx_pe'
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'cbd_id'
)
BEGIN
  ALTER TABLE dh_retail_ntx_pe ADD cbd_id VARCHAR(50) NULL;
END


IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'bu_type'
)
BEGIN
  ALTER TABLE dh_retail_ntx_pe ADD bu_type VARCHAR(50) NULL;
END


IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_retail_ntx_pe')
	and  name = 'bu_code'
)
BEGIN
  ALTER TABLE dh_retail_ntx_pe ADD bu_code VARCHAR(50) NULL;
END


-- add columns 'cbd_id', 'bu_type', 'bu_code' to table 'dh_wh_ntx_pe'
IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_pe')
	and  name = 'cbd_id'
)
BEGIN
  ALTER TABLE dh_wh_ntx_pe ADD cbd_id VARCHAR(50) NULL;
END


IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_pe')
	and  name = 'bu_type'
)
BEGIN
  ALTER TABLE dh_wh_ntx_pe ADD bu_type VARCHAR(50) NULL;
END


IF NOT EXISTS (
	select * from syscolumns
	where id  = object_id('dh_wh_ntx_pe')
	and  name = 'bu_code'
)
BEGIN
  ALTER TABLE dh_wh_ntx_pe ADD bu_code VARCHAR(50) NULL;
END
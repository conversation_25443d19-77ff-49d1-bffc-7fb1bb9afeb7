$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Launch-Job-HostIpmi(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        try {
            $Vars             = Load-Vars
            $DhHosts          = Select-DhWhNtxHost -Vars $Vars
            $DhOneViews       = Select-DhOneView -Vars $Vars
            $Ipmis            = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $CollectionUpdate = @()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        #Rolling call each OneView
        $DhOneViews | Foreach-Object -ThrottleLimit 5 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $using:DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on $($_.fqdn)" -DumpFile $using:DumpFile
            $OneView   = $_
            $DictIpmis = $using:Ipmis
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage "oneview_call" | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account" -DumpFile $using:DumpFile
                return
            }
            
            if ($OneViewCall1 = Rest-OneView-List-Server -Fqdn $OneView.fqdn -Username $SvcAccount.username -PWord (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted) -Domain 'ikea.com') {
                $OneViewCall1 = $OneViewCall1 | Where-Object {$_.state -ne "Unmanaged"}
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "'$($OneViewCall1.count)' IPMI profiles are available '$($OneView.fqdn)'" -DumpFile $using:DumpFile
                $OneViewCall1 | Foreach-Object {
                    $Ipmi = [PSCustomObject]@{
                        'sn'               = $_.serialNumber
                        'ipmi_version'     = $_.mpModel + " " + $_.mpFirmwareVersion
                        'bios_version'     = $_.romVersion
                        'smart_controller' = [PSCUstomObject]@{
                            'model'   = ($_.subResources.LocalStorage.data | where-object {$_.AdapterType -eq "SmartArray"} | Select-Object -First 1).Model
                            'version' = ($_.subResources.LocalStorage.data | where-object {$_.AdapterType -eq "SmartArray"} | Select-Object -First 1).FirmwareVersion.Current.VersionString
                        }
                    }
                    $DictIpmis.Add($Ipmi)
                }
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling '$($OneView.fqdn)' for list IPMIs" -DumpFile $using:DumpFile
                return
            }
        }
        foreach ($H in $DhHosts) {
            $L = [PSCustomObject]@{
                'sn'                 = $H.sn
                'ipmi_version'       = "NA"
                'bios_version'       = "NA"
                'controller_version' = "NA"
                'controller_model'   = "NA"
            }
            if ($Ipmi = $Ipmis | Where-Object {$_.sn -eq $H.sn}) {
                $L.ipmi_version       = if($null -ne $Ipmi.ipmi_version) {$Ipmi.ipmi_version} else {"NA"}
                $L.bios_version       = if($null -ne $Ipmi.bios_version) {$Ipmi.bios_version} else {"NA"}
                $L.controller_version = if($null -ne $Ipmi.smart_controller.version) {$Ipmi.smart_controller.version} else {"NA"}
                $L.controller_model   = if($null -ne $Ipmi.smart_controller.model) {$Ipmi.smart_controller.model} else {"NA"}
            }
            $CollectionUpdate += $L
        }
    } 
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_wh_ntx_host]" -DumpFile $DumpFile
        Update-Table-DhWhNtxHost-BySn -Vars $Vars -Collection $CollectionUpdate
    }
}
function Launch-Job-HostNic(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhWhNtxPc -Vars $Vars
            $DhPEs            = Select-DhWhNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $DhHosts          = Select-DhWhNtxHost -Vars $Vars | Where-Object {$_.status -eq "Running"}
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $NeedToUpdate     = @("Unknown", "")
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhHosts.Count)' hosts need to update" -DumpFile $DumpFile
        $DhHosts | Foreach-Object -ThrottleLimit 50 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $using:DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $H          = $_
            $UpdateDict = $using:CollectionUpdate
            $PE         = $using:DhPEs | Where-Object {$_.id -eq $H.pe_id}
            $PC         = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount -and ($SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage "nutanix_datafetch_prod" | Select-Object -First 1)) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for calling '$($PE.name)', we'll use the one of 'nutanix_datafetch_prod'" -DumpFile $using:DumpFile
            }elseif (!$SvcAccount) {
                Write-Console-Logs -Level ERROR -FunctionName (Get-FunctionName) -Message "No service account for calling '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level ERROR -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for calling '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $NicMap = [PSCustomObject]@{
                'sn'             = $H.sn
                'nic0_uuid'      = "Unknown"
                'nic0_mac'       = "Unknown"
                'nic0_speed'     = "0 Gbkps"
                'nic0_mtu'       = "Unknown"
                'nic0_sw_device' = "Unknown"
                'nic0_sw_port'   = "Unknown"
                'nic0_sw_vendor' = "Unknown"
                'nic0_sw_vlan'   = "Unknown"
                'nic1_uuid'      = "Unknown"
                'nic1_mac'       = "Unknown"
                'nic1_speed'     = "0 Gbkps"
                'nic1_mtu'       = "Unknown"
                'nic1_sw_device' = "Unknown"
                'nic1_sw_port'   = "Unknown"
                'nic1_sw_vendor' = "Unknown"
                'nic1_sw_vlan'   = "Unknown"
                'nic2_uuid'      = "Unknown"
                'nic2_mac'       = "Unknown"
                'nic2_speed'     = "0 Gbkps"
                'nic2_mtu'       = "Unknown"
                'nic2_sw_device' = "Unknown"
                'nic2_sw_port'   = "Unknown"
                'nic2_sw_vendor' = "Unknown"
                'nic2_sw_vlan'   = "Unknown"
                'nic3_uuid'      = "Unknown"
                'nic3_mac'       = "Unknown"
                'nic3_speed'     = "0 Gbkps"
                'nic3_mtu'       = "Unknown"
                'nic3_sw_device' = "Unknown"
                'nic3_sw_port'   = "Unknown"
                'nic3_sw_vendor' = "Unknown"
                'nic3_sw_vlan'   = "Unknown"
            }
            if ($PrismCall = Rest-Prism-v1-Get-HostNic -Fqdn $H.pe_fqdn -Uuid $H.uuid -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We've got NIC profile for '$($H.name)'" -DumpFile $using:DumpFile
                foreach($i in @(0..3)) {
                    $NicMap."$('nic' + $i + '_uuid')"      = $PrismCall[$i].uuid
                    $NicMap."$('nic' + $i + '_mac')"       = $PrismCall[$i].macAddress
                    $NicMap."$('nic' + $i + '_speed')"     = "" + ([int]$PrismCall[$i].linkSpeedInKbps / [Math]::Pow(1000, 2)) + " Gbps"
                    $NicMap."$('nic' + $i + '_mtu')"       = $PrismCall[$i].mtuInBytes
                    $NicMap."$('nic' + $i + '_sw_device')" = $PrismCall[$i].switchDeviceId
                    $NicMap."$('nic' + $i + '_sw_port')"   = $PrismCall[$i].switchPortId
                    $NicMap."$('nic' + $i + '_sw_vendor')" = $PrismCall[$i].switchVendorInfo
                    $NicMap."$('nic' + $i + '_sw_vlan')"   = $PrismCall[$i].switchVlanId
                }
            }else {
                Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to get NIC profile for '$($H.name)'" -DumpFile $using:DumpFile
            }
            $UpdateDict.Add($NicMap)
        } -UseNewRunspace
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_wh_ntx_host]" -DumpFile $DumpFile
        Update-Table-DhWhNtxHost-BySn -Vars $Vars -Collection $CollectionUpdate
    }    
}
Launch-Job-HostIpmi
Launch-Job-HostNic
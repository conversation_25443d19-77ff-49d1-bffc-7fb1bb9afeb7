class TaskStatus:
    NOT_STARTED = "Not Started"
    IN_PROGRESS = "In Progress"
    DONE = "Done"
    ERROR = "Error"
    WARN = "Warning"
    ABORT = "Aborted"
    SKIPPED = "Skipped"
    IN_CLEANUP = "In Cleanup"
    CLEANUP_DONE = "Cleanup Done"
    DONE_WITH_ERROR = "Done With Error"
    UPGRADING = "Upgrading"
    PRECHECKING = "Pre Checking"
    SPPUPGRADING = "SPP Upgrading"
    AOSUPGRADING = "AOS Upgrading"
    AHVUPGRADING = "AHV Upgrading"


class LcmTaskStatus(TaskStatus):
    UPGRADING = "Upgrading"
    PRE_CHECKING = "Pre Checking"
    SPP_UPGRADING = "SPP Upgrading"
    AOS_UPGRADING = "AOS Upgrading"
    AHV_UPGRADING = "AHV Upgrading"
    REPAIRING = "Repairing"


class NewClusterTaskStatus(TaskStatus):
    DEPLOYING_CLUSTER = "Deploying Cluster"
    SUB_TASKS_TRIGGERED = "Sub Tasks Triggered"
    SUB_TASKS_ERROR = "Sub Tasks Error"
    WAITING_USER_INPUT = "Waiting User Input"


class SeamlessLcmPlanStatus(TaskStatus):
    PLANNED = "Planned"
    CANCELED = "Canceled"
    COMPLETED = 'Completed'


class SeamlessLcmPlannedPEStatus(TaskStatus):
    PLANNED = "Planned"
    CANCELED = 'Canceled'
    IN_PROGRESS_STATUSES = (
        TaskStatus.NOT_STARTED,
        LcmTaskStatus.IN_PROGRESS,
        LcmTaskStatus.PRECHECKING,
        LcmTaskStatus.PRE_CHECKING,
        LcmTaskStatus.SPP_UPGRADING,
        LcmTaskStatus.AOS_UPGRADING,
        LcmTaskStatus.AHV_UPGRADING,
        LcmTaskStatus.UPGRADING,
        LcmTaskStatus.REPAIRING,
    )

from datetime import datetime
import uuid
import os
import shutil
import static.SETTINGS as SETTING
from models.database import db
from models.pm_models import ModelSL<PERSON>MLog, ModelNTXPMLog
from models.workload_models import ModelWorkloadTaskLog
from models.atm_models import ModelRetailNutanixAutomationSPPLCMTaskLog, \
    ModelRetailNutanixAutomationAOSLCMTaskLog, ModelNtxAutomationRotatePasswordTaskLog, \
    ModelNtxAutomationRenewCertificateTaskLog
from models.ntx_models import ModelNTXMOVELog
from models.ntx_models_wh import ModelWHNTXMOVELog
import logging


class DBLogging:
    def __init__(self, logdir='', logtype="NTX_PM", taskid=None, log_model=None, lasting_columns={}):
        self.logdir = logdir if logdir else SETTING.LOG_PATH
        self.taskid = taskid
        self.logtype = logtype
        self.log_model = log_model
        self.lasting_columns = lasting_columns

    def write_pm_log(self, loginfo, logdate=None, logseverity='info', taskid=None):
        if not taskid:
            taskid = self.taskid
        if self.logtype == 'SLI_PM':
            lo = ModelSLIPMLog()
        elif self.logtype == 'NTX_WL':
            lo = ModelWorkloadTaskLog()
        elif self.logtype == "NTX_PM":
            lo = ModelNTXPMLog()
        else:
            raise Exception("Unrecognized log type.")
        # lo = ModelSLIPMLog()
        if not logdate:
            logdate = datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
            # neec to limit the length of lo.info
        if len(loginfo) > 8000:
            loginfo = loginfo[0:7999]
        lo.tasktype, lo.logdate, lo.severity, lo.loginfo, lo.task_id = self.logtype, logdate, logseverity, loginfo, taskid  # noqa
        db.session.add(lo)
        db.session.commit()
        
    def write_move_log(self, loginfo, logdate=None, logseverity='info', taskid=None):
        if not taskid:
            taskid = self.taskid
        # if not clusterid:
        #     clusterid = self.clusterid
        if self.logtype == "NTX_MOVE":
            lo = ModelNTXMOVELog()
        elif self.logtype == "NTX_WHMOVE":
            lo = ModelWHNTXMOVELog()
        else:
            raise Exception("Unrecognized log type.")
        if not logdate:
            logdate = datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
        if len(loginfo) > 255:
            loginfo = loginfo[0:250]
        lo.tasktype, lo.logdate, lo.severity, lo.loginfo, lo.taskid = self.logtype, logdate, logseverity, loginfo, taskid  # noqa
        db.session.add(lo)
        db.session.commit()

    def write_lcm_log(self , loginfo, logdate = None , logseverity = 'info' , taskid = None ):
        if not taskid:
            taskid = self.taskid
        if self.logtype == 'SPP':
            lo = ModelRetailNutanixAutomationSPPLCMTaskLog()
        elif self.logtype == "AOS":
            lo = ModelRetailNutanixAutomationAOSLCMTaskLog()
        else:
            raise Exception("Unrecognized log type.")
        # lo = ModelSLIPMLog()
        if not logdate:
            logdate = datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S") 
        #neec to limit the length of lo.info
        if len(loginfo) > 8000:
            loginfo = loginfo[0:7999]
        lo.task_id, lo.task_type, lo.log_date, lo.severity, lo.log_info = taskid, self.logtype, logdate, logseverity, loginfo

        db.session.add(lo)
        db.session.commit()

    def write_resetpwd_log(self, loginfo, logdate=None, logseverity='info', taskid=None ):
        if not taskid:
            taskid = self.taskid
        lo = ModelNtxAutomationRotatePasswordTaskLog()
        # lo = ModelSLIPMLog()
        if not logdate:
            logdate = datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S") 
        #neec to limit the length of lo.info
        if len(loginfo) > 255:
            loginfo = loginfo[0:250]
        lo.task_type, lo.logdate, lo.severity, lo.loginfo, lo.task_id = "RESET_PWD", logdate, logseverity, loginfo, taskid
        db.session.add(lo)
        db.session.commit()

    def create_log_file(self, filename): #not in use so far.
        filename = filename if filename else str(uuid.uuid4())
        try:
            # self.logpath = self.logdir + '\\' + filename
            self.logpath = os.path.join(self.logdir, filename)
            fp = open(self.logpath, 'x')
            fp.close()
        except Exception:
            pass

    def write_log(self, loginfo, logdate=None, logseverity='info', taskid=None):
        if not taskid:
            taskid = self.taskid
        if not logdate:
            logdate = datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
        if len(loginfo) > 8000:
            loginfo = loginfo[0:7999]
        lo = self.log_model()
        lo.task_type, lo.logdate, lo.severity, lo.loginfo, lo.task_id = self.logtype, logdate, logseverity, loginfo, taskid
        # TODO: workaround for different table columns
        lo.log_date = logdate
        lo.log_info = loginfo
        db.session.add(lo)
        db.session.commit()

    def write_local_log(self):
        pass

    def write_renew_cert_log(self, loginfo, logdate=None , logseverity='info', taskid=None ):
        if not taskid:
            taskid = self.taskid
        if self.logtype == 'RENEWCERT':
            lo = ModelNtxAutomationRenewCertificateTaskLog()
        else:
            raise Exception("Unrecognized log type.")
        if not logdate:
            logdate = datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S") 
        #neec to limit the length of lo.info
        if len(loginfo) > 8000:
            loginfo = loginfo[0:7999]
        lo.task_id, lo.task_type, lo.log_date, lo.severity, lo.log_info = taskid, self.logtype, logdate, logseverity, loginfo

        db.session.add(lo)
        db.session.commit()

    def write(self, loginfo, logseverity='info', **kwargs):
        # Compatible for all column names...
        log = self.log_model()
        log.task_type, log.tasktype = self.logtype, self.logtype
        log_date = datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
        log.log_date, log.logdate = log_date, log_date
        log.task_id, log.taskid = self.taskid, self.taskid
        log_info = loginfo[0:7999] if len(loginfo) > 8000 else loginfo
        log.log_info, log.loginfo = log_info, log_info
        log.severity = logseverity
        # Extra columns
        for key, value in self.lasting_columns.items():
            setattr(log, key, value)
        for key, value in kwargs.items():
            setattr(log, key, value)
        db.session.add(log)
        db.session.commit()


class IntegratedLogger:
    def __init__(self, file_lg, db_lg):
        self.file_lg = file_lg
        self.db_lg = db_lg

    def write(self, msg, severity='info'):
        self.db_lg.write(loginfo=msg, logseverity=severity)
        getattr(self.file_lg, severity)(msg)


class LoggingFile:
    
    def __init__(self):
        self.path_main_log_file = SETTING.MAIN_LOG_FILE_PATH
        self.path_archive_log_folder = SETTING.LOG_ARCHIVE_FOLDER_PATH
        self.today_date = datetime.now().strftime("%Y-%m-%d")
        self.logging_info = []

    
    def perform_logging_info(self):
        """Get the logging information."""
        
        for log in self.logging_info:
            if log[0] == "error":
                logging.error(log[1])
                continue
            logging.info(log[0])
        

    def archive_main_log_file(self, recursive_counter = 0):
        self.logging_info.append(["starting task to archive flask_main log file... If file is larger than 5 mb it will be archived."])
        
        if not self._is_file_size_exceeds(self.path_main_log_file, 5):
            self.logging_info.append(["flask_main log file size is less than 5 mb or dosent exist yet, no need to archive."])
            return 
        
        self.logging_info.append(["Archiving flask_main in to flask_main_archive and creating a new one..."])
        
        
        if os.path.exists(self.path_main_log_file): # does the file exist?
            filename = os.path.basename(self.path_main_log_file)
            new_archive_filename = filename + "_" + self.today_date 
                
            if os.path.isdir(self.path_archive_log_folder): # check if archive folder exists. create it if not.
                # Check if the new archive filename already exists and get a unique filename
                new_archive_filename = self._get_unique_filename(self.path_archive_log_folder, new_archive_filename)
                #get the path to where the old file will be archived
                archive_path = os.path.join(self.path_archive_log_folder, new_archive_filename)
                # Create a backup of the main log file
                self.logging_info.append([f"Creating a backup of the main log file: {self.path_main_log_file}"])
                backup = self.path_main_log_file + self.today_date + ".bak"
                shutil.copy2(self.path_main_log_file, backup)
                # Move the main log file to the archive folder
                self.logging_info.append([f"Trying to move log file to archive: {archive_path}"])
                try:
                    shutil.move(self.path_main_log_file, archive_path)
                except Exception as e: 
                    self.logging_info.append(["error", f"Error in trying archive log file {str(e)}"])
                    self.logging_info.append(["Restoring the backup log file."])
                    # Restore the backup if an error occurs
                    if os.path.exists(backup):
                        shutil.copy2(backup, self.path_main_log_file)
                        os.remove(backup)  # Remove the backup file after restoring
                    else:
                        self.logging_info.append(["error", "Backup log file does not exist. Unable to restore."])
                        return 
                    self.logging_info.append([f"Restored log file from backup {backup}"])
                    return 
                
                self.logging_info.append([f"Log file moved to archive: {archive_path}"])
                # Remove the backup file after successful archiving
                os.remove(backup)
                self.logging_info.append([f"Backup file removed since archiving was successful: {backup} deleted"])

                # Create a new log file. Gets done in other function regardless of the outcome of this function. So could be taken out of here...
                try:
                    self.logging_info.append([f"Attemting to create a new log file: {self.path_main_log_file}"])
                    open(self.path_main_log_file, 'w').close()
                except Exception as e:
                    self.logging_info.append(["error", f"Error creating new log file: {str(e)}. Other setup function will prob get the job done!"])
                    return 
                self.logging_info.append([f"Archiving log file successfully: {archive_path}. Task completed."])
                    
                
            else:
                self.logging_info.append(["error", f"Archive log folder does not exist. Creating folder: {self.path_archive_log_folder}"])
                os.makedirs(self.path_archive_log_folder)
                if not os.path.isdir(self.path_archive_log_folder):
                    self.logging_info.append(["error", "Archive log folder does not exist and could not be created."])
                    return 
                self.logging_info.append(["Archive log folder created successfully. running the function from the beginning"])
                if recursive_counter > 1:
                    self.logging_info.append(["error", "Recursive counter exceeded. Exiting..."])
                    return 
                recursive_counter += 1
                self.archive_main_log_file(recursive_counter = recursive_counter)
                return
        else:
            self.logging_info.append(["error", "Main log folder does not exist. No need to archive."])
            return
            

    def _is_file_size_exceeds(self, file_path, size_limit_mb):
        """Check if the file size exceeds the given limit in MB."""
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            return file_size > size_limit_mb * 1024 * 1024  # Convert MB to bytes
        self.logging_info.append(["error", "Main log folder does not exist. Will be created later in other method. No need to archive "])
        return False

    
    def _get_unique_filename(self, folder_path, filename):
        """Generate a unique filename if the file already exists in the folder."""
        counter = 1
        unique_filename = filename
        while os.path.exists(os.path.join(folder_path, unique_filename)):
            unique_filename = filename + f"({counter})"
            counter += 1
        return unique_filename
            
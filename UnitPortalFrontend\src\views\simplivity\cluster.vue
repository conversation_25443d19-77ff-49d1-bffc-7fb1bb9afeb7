<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="5" >
        <el-col :span="4"  :offset="14" >
          <el-select    size="large"
            v-model="filter.selected_vc" multiple collapse-tags filterable placeholder="Filter the VC" style="width:100%;" >

            <el-option v-for="item in filter.vc_list" :key="item" :label="item.toLowerCase()" :value="item" style="font-size: large;"/>

          </el-select>
        </el-col>
        <el-col :span="4" >
          <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large"  @keyup.enter.native="filter_cluster_list"/>
        </el-col>
        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="filter_cluster_list">
            Search
          </el-button>
        </el-col>
       
      
    </el-row>
    </div>
    <el-table  :key="tableKey" 
                v-loading="listLoading" 
                :data="current_list" 
                border 
                fit 
                highlight-current-row 
                style="width: 100%;" 
                @sort-change="sortChange"
                >
      <el-table-column label="VC" prop="vc_fqdn" sortable="custom" align="center" min-width="8%" >
        <template slot-scope="{row}">
          <span>{{ row.vc_fqdn.toUpperCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Name" prop="name" min-width="8%" sortable="custom" align="center" >
        <template slot-scope="{row}">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>



      <el-table-column label="Node" prop="host_counts" min-width="4%" align="center" sortable="custom">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.host_counts }}</span>
        </template>
      </el-table-column>

      <el-table-column label="VM" align="center" prop="vm_counts" min-width="4%" sortable="custom">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.vm_counts }}</span>
        </template>
      </el-table-column>

      <el-table-column label="HA enabled" min-width="6%" align="center" >
        <template slot-scope="{row}">
          <span>{{ row.ha_enabled }}</span>
        </template>
      </el-table-column>
      <el-table-column label="DRS enabled" class-name="status-col" min-width="6%" align="center" >
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.drs_enabled }}</span>
        </template>
      </el-table-column>


    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" /> 


  </div>
</template>

<script>
import {GetClusterList, GetVCClusterCorrespondence} from '@/api/simplivity'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination


export default {
  name: 'PETable',
  components: { Pagination },
  directives: { waves },
  filters: {

  },
  data() {
    return {
      VCTypeKeyValue:null,
      tableKey: 0,
      all_cluster_list: null,
      filtered_list: null,
      current_list: null,
      page_list: null,
      filter:{
        vc_list:[],
        selected_vc:[],
        fuzzy_string:"",
      },
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: '',
        prism: '',
        status: '',
        sort: '+id'
      }
    }
  },
  computed: {
    total() {
      if(this.filtered_list){
        return this.filtered_list.length
      }
      else{
          return 0
      }
    }
  },
  created() {
    this.get_cluster_list()
    this.get_filter_data()
  },

  methods: {
    get_filter_data(){
      GetVCClusterCorrespondence(this.$store.getters.token).then(
        
        response =>{
          let vcoptions = response['data']
          this.VCTypeKeyValue = vcoptions.reduce((acc, cur) => {
          acc[cur.vc] = cur.cluster.sort()
          return acc
        }, {})
        this.filter.vc_list = Object.keys(this.VCTypeKeyValue)
        }
      )
    },

    get_cluster_list() {
      this.listLoading = true
      GetClusterList(this.$store.getters.token).then(response => {
        this.all_cluster_list = response.data
        this.filtered_list = this.all_cluster_list
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)
        this.listLoading = false
      })
    },

    remove_duplicate(arr) {
      //去除重复值
      const newArr = []
      arr.forEach(item => {
        if (!newArr.includes(item)) {
          newArr.push(item)
        }
      })
      return newArr
    },

    set_page(){
      // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start,end)
    },

    filter_cluster_list(){
      //根据过滤条件筛选表格显示内容
      //screen the table as per filters
      this.listQuery.page = 1
      let temp_list
      //filter selected pc first.
      if (this.filter.selected_vc.length){
        //No filter, so select all
        temp_list = this.all_cluster_list.filter((item)=>{
          console.log(item)
          return this.filter.selected_vc.includes(item['vc_fqdn'].toUpperCase())
        })
        this.filtered_list = temp_list
      }
      else{
        this.filtered_list = this.all_cluster_list
      }


      if(this.filter.fuzzy_string.trim().length){
        let temp_list = this.filtered_list
        let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
            for(let fuzzy of fuzzy_list){
              fuzzy = fuzzy.toString().toLowerCase()
              temp_list = temp_list.filter((k)=>{
              if( k.vc_fqdn.toString().toLowerCase().search(fuzzy)!= -1
                  || k.name.toLowerCase().search(fuzzy) != -1
              ){
                return true
              }
            })
            }
        this.filtered_list = temp_list
    }
      this.set_page()
    },

    sortChange(data) {
      const { prop, order } = data
      if(order==null){
        this.sortChange({prop:'id',order:'ascending'})
        return 
      }
      let flag_num = order=="ascending" ? 1 : -1
      this.filtered_list.sort((item1,item2)=>(
        (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
      ))

      this.set_page()
    },

    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    }
  }
}
</script>
<style lang="scss" scoped>
    .bigger_font {
      font-size: 16px;
    }
</style>
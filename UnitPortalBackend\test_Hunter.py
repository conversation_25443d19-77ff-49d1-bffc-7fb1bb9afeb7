#installed modules
import logging
import time
import multiprocessing
import concurrent.futures
import threading
import datetime
import re
import json
import uuid
from business.generic.commonfunc import Redfish, SSHConnect, OneView
from flask import Flask
# adding comment by charan
from flask import Flask,request,jsonify
from datetime import datetime
import time, logging, copy, random, string, json
# from update_vm_project import UpdateVmProject
from models.database import db
from static.DB_CONN import DPC_DB_USER, DPC_DB_PW, DPC_DB_HOST, DPC_DB_NAME, DPC_DB_DRIVE
from business.authentication.authentication import ServiceAccount, Vault
from business.distributedhosting.nutanix.cluster.lldpcheck import LLDPNetworkAnalyzer
from models.benchmark_models import ModelNtxBenchmark
from business.benchmark.benchmark import Benchmark
from business.distributedhosting.nutanix.automation.automation import Automation
from business.distributedhosting.nutanix.automation.password_relate import Sshkeys
# from models.atm_models import  ModelRetailNutanixAutoMaintenanceBenchmark
from business.distributedhosting.nutanix.nutanix import Prism, PrismElement, PrismCentral, NutanixCLI
app = Flask(__name__)
app.config["SQLALCHEMY_DATABASE_URI"] = (
    f"mssql+pyodbc://{DPC_DB_USER}:{DPC_DB_PW}@{DPC_DB_HOST}:1433/{DPC_DB_NAME}?driver={DPC_DB_DRIVE}"  # noqa
)
db.init_app(app)
app.app_context().push()


# ServiceAccount.create_service_account("8fc36a3b-2fe8-b891-a7f7-264dd5a5de73","8cf39974-7c72-a7c6-2b3b-123dc279bba0","nutanix_datafetch_d2")

# sa = ServiceAccount('nutanix_datafetch_d2').get_service_account()




# ssh = SSHConnect('modcn008-nxc001.ikea.com', 'admin', '.?6~;x%5)nKt-vuF')
# ssh.connect()
# ssh.invoke_shell()
# ssh.send_command("allssh sudo faillock --user nutanix --reset")
# ssh.send_command(".?6~;x%5)nKt-vuF")
# ssh.receive_output()


# cli = NutanixCLI(pe='retse550-nxc000.ikead2.com', pc='ssp-dhd2-ntx.ikead2.com', logger=logging)
# cli.unlock_account(prism_type='PE',prism_name='retse550-nxc000',account='admin')



# rf = Redfish('*************','administrator',"Bc*#ze4EXk$@00Gi")
# res, mes = rf.remove_ilo_certificate()
# print(res, mes)




clusters = {
'*************',
'*************',
'*************',
'*************'
}
print_lock = threading.Lock()
vault = Vault()
def reset_pcpe_connetion(cluster):
    try:
        # _res,sa = vault.get_secret(f"{cluster}/Site_Pe_Nutanix")
        ssh = SSHConnect(cluster, 'nutanix', 'nutanix/4u')
        ssh.connect(timeout=3)
        ssh.invoke_shell()
        # ssh.send_command("genesis restart")
        ssh.send_command("sudo sed -i '/^IPADDR/d; /^NETMASK/d; /^GATEWAY/d; /^BOOTPROTO=/s/none/dhcp/' /etc/sysconfig/network-scripts/ifcfg-eth0")
        time.sleep(10)
        ssh.send_command("ssh root@***********")
        # ssh.send_command("~/foundation/bin/foundation_service restart")
        time.sleep(5)
        ssh.send_command("sed -i '/^IPADDR/d; /^NETMASK/d; /^GATEWAY/d; /^BOOTPROTO=/s/none/dhcp/' /etc/sysconfig/network-scripts/ifcfg-br0")
        # ssh.send_command("ipmitool lan set 1 ipsrc dhcp")
        # ssh.send_command("ipmitool bmc reset cold")
        _output = ssh.receive_output()
        time.sleep(10)
        ssh.send_command("sudo shutdown -r now")
        
        # 使用线程锁保护打印输出
        with print_lock:
            print(f"Cluster {cluster}: {_output}")
            
        # time.sleep(10)
        # ssh.send_command("allssh 'genesis stop aplos aplos_engine mercury && cluster start'")
        # time.sleep(40)
        
        with print_lock:
            print(f"Completed reset for cluster: {cluster}")
            
    except Exception as e:
        with print_lock:
            print(f"Error processing cluster {cluster}: {str(e)}")

# 使用线程池执行任务
def main():
    # 可以根据需要调整最大线程数
    max_workers = 10
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务到线程池
        futures = [executor.submit(reset_pcpe_connetion, cluster) for cluster in clusters]
        
        # 等待所有任务完成
        concurrent.futures.wait(futures)

if __name__ == "__main__":
    main()




vault = Vault(tier= 'WAREHOUSE')
oneview = OneView("ssp-eu-wiab-ntx.ikea.com", "DSDE050-NXC001", Benchmark.get_bmk_by_id(bmk_id=27).get('vault').get('dc_labels').get('hpe_oneview'),  facility_type = "warehouse", vault = vault)
oneview_scope = Benchmark.get_bmk_by_id(bmk_id=27).get('systems').get('oneview_scope')
res, myscope = oneview.get_oneview_scopes(oneview_scope)


automaintenance_benchmark = ModelRetailNutanixAutoMaintenanceBenchmark.query.filter_by(tier = 'IKEADT').first()


sa = ServiceAccount('nutanix_pm').get_service_account()

vault = Vault('IKEAD2')
_res,data = vault.get_secret("RETSE550-NXC000/Site_Pe_Admin")
sa = {"username": data['username'],  "password": data['secret']}
renewsshkey = Sshkeys(pe='retse550-nxc000', pc='ssp-dhd2-ntx.ikead2.com', sa=sa, logger=logging)
res = renewsshkey.get_pe_keys()
print(res)





automation = Automation(pc='ssp-dhd2-ntx.ikead2.com', pe='retse550-nxc000', sa=None, logger=logging)
automation.reauth_pe_ad()

vault = Vault('IKEAD2')
upn_label = Benchmark.get_bmk_by_id(bmk_id=ModelNtxBenchmark.query.filter_by(tier = 'IKEAD2').first().id).get('vault').get('dc_labels').get('ad_query_upn')
res, sec = vault.get_secret(upn_label)

clusters = {
  "RETINC01-NXC000"
}
print_lock = threading.Lock()
vault = Vault()
def reset_pcpe_connetion(cluster):
    try:
        _res,sa = vault.get_secret(f"{cluster}/Site_Pe_Nutanix")
        ssh = SSHConnect(cluster, sa['username'], sa['secret'])
        ssh.connect(timeout=3)
        ssh.invoke_shell()
        ssh.send_command("/usr/local/nutanix/bin/nuclei remote_connection.reset_pe_pc_remoteconnection")
        time.sleep(10)
        _output = ssh.receive_output()
        
        # 使用线程锁保护打印输出
        with print_lock:
            print(f"Cluster {cluster}: {_output}")
            
        time.sleep(10)
        ssh.send_command("allssh 'genesis stop aplos aplos_engine mercury && cluster start'")
        time.sleep(40)
        
        with print_lock:
            print(f"Completed reset for cluster: {cluster}")
            
    except Exception as e:
        with print_lock:
            print(f"Error processing cluster {cluster}: {str(e)}")

# 使用线程池执行任务
def main():
    # 可以根据需要调整最大线程数
    max_workers = 10
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务到线程池
        futures = [executor.submit(reset_pcpe_connetion, cluster) for cluster in clusters]
        
        # 等待所有任务完成
        concurrent.futures.wait(futures)

if __name__ == "__main__":
    main()









analyzer = LLDPNetworkAnalyzer(
    username="nutanix",
    password="nutanix/4u"
)


cvm_ips = [
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "***********"
]

topology = analyzer.analyze_network_topology(cvm_ips)
print(topology)


# project = UpdateVmProject()
# project.update_vm_project('ssp-eu-ntx')



# if __name__ == "__main__":
#     config = ntnx_lcm_py_client.configuration.Configuration()
#     config.host = "retse124-nxc000.ikeadt.com"
#     config.port = 9440
#     config.max_retry_attempts = 1
#     config.backoff_factor = 3
#     config.username = "admin"
#     config.password = "DeGbB3F-E!KrB-1m"
#     client = ntnx_lcm_py_client.ApiClient(configuration=config)
#     statusApi = ntnx_lcm_py_client.StatusApi(api_client=client)
#     api_response = statusApi.get_status()

# # op = NutanixOperation('ssp-ppe-ntx.ikea.com','RETCN888-NXC000')
# # op.rotate_password(['cvm','oob'])
# class Ansible:
#     def __init__(self, sa=None, logger=logging) -> None:
#         self.logger = logger
#     def ansible_post(self,TemplateID,ansiblepayload=None):
#         ab = AnsibleAPI()
#         res = ab.check_ansible_template(TemplateID)
#         if not res:
#             return False
#         else:
#             print(f"ID {TemplateID} exist, we can start lunch stage now") 
#             jobid=ab.lunchAnsibleUniversalTemplate(TemplateID,ansiblepayload)
#             if not jobid:
#                 print('Failed to get jobid, unable to continue')
#                 return False
#             else:            
#                 results=ab.get_ansible_job_state(jobid)
#                 if  re.match('successful', results, re.I):
#                     logging.info('Template completed')
#                 elif re.match('failed',results,re.I):
#                     logging.info('Template failed')
#                 else:
#                     for i in range(10):
#                         time.sleep(60)
#                         results=ab.get_ansible_job_state(jobid)
#                         if re.match('successful', results, re.I):
#                             print('Template completed')
#                             break
#                         elif re.match('failed',results,re.I):
#                             print('Template failed')
#                             break
#                         else:
#                             continue
#                     else:
#                         print('Template is running for too long, it is considered as failed')



# class AnsibleAPI:
#     def __init__(self, sa=None, logger=logging) -> None:
#         _sa = sa if sa else ServiceAccount(usage=ServiceAccount.LIN_API).get_service_account()
#         self.logger = logger
#         self.rest = CommonRestCall(username=_sa['username'], password=_sa['password'])

#     def ansiblepayload(self,VMname,tier,OSVersion,DCType,groups):
#         ansible_payload = ({
#             "ikea_hosts": [{
#             "name": VMname,
#             "hostgroup": f"{tier}/{OSVersion}/{DCType}",
#             "variables": 
#             {
#                 "patch_responsible_email": "<EMAIL>",
#                 "owners": ["loctechansible","gdba"],
#                 "patch_autoreboot": False,
#                 "patch": False
#             },
#             "groups": groups
#             }]
#         })
#         return ansible_payload
    
#     def check_ansible_template(self,TemplateID):
#         url = f"https://ansible.ikea.com/api/v2/job_templates/{TemplateID}"
#         try:
#             res=self.rest.call_restapi(url,method="GET")
#             return True
#         except Exception:
#             self.logger.error(f'{TemplateID} Does not exist')
#             return False

#     def lunchAnsibleUniversalTemplate(self,TemplateID,ansiblepayload):
#         url = f"https://ansible.ikea.com/api/v2/job_templates/{TemplateID}/launch/"
#         res =self.rest.call_restapi(url,method="POST",payload=ansiblepayload)
#         if not res:
#             raise Exception(f'{TemplateID} Running Ansible wait procedure.')
#         else:
#             return res.json()["job"]
        
#     def get_ansible_job_state(self,jobid):
#         url = f"https://ansible.ikea.com/api/v2/jobs/?id={jobid}"
#         res=self.rest.call_restapi(url,method="GET")
#         if not res:
#             raise Exception(f'{jobid} is not correct.')
#         else:
#             return res.json()["results"][0]["status"]




# #test = AnsibleAPI()
# #test.check_ansible_template('319')
# # test.ansiblepayload("VMname","tier","OSVersion","DCType","groups")
# # result=test.get_ansible_job_state('6492962')
# # #result=test.lunchAnsibleUniversalTemplate()
# # print(result)
# def host_covert(data):
#     arr = []
#     for host in data:
#         ret = {}
#         for target in {'cvm', 'ahv', 'oob'}:
#             ret[target] = {'users': []}
#             new_dict = dict(filter(lambda item: item[0].startswith(target), host.items()))
#             if target == 'ahv':
#                 new_dict['ahv_ip'] = host['ip']

#             for k, v in new_dict.items():
#                 if isinstance(v, dict):
#                     v['group'] = k
#                     ret[target]['users'].append(v)
#                 else:
#                     ret[target]['ip'] = new_dict[target + '_ip']
#         arr.append(ret)
#     return arr

# # Get host_info
# def get_hosts(pc, pe):
#     op = NutanixOperation(pc, pe)
#     res, hosts = op.get_pe_info()
#     op.sa = ServiceAccount('nutanix_pm').get_service_account()
#     op._prism = PrismCentral(pc, op.sa)
#     tier = op._prism.get_prism_by_name_from_db(fqdn=pc)['tier']
#     if not res:
#         raise Exception('Can not get Hosts')
#     return hosts, tier

        
# class password_relate():           # noqa
#     def __init__(self, logger=logging) -> None:
#         self.logger = logger if logger else logging
        

#     def generate_newpassword(self,group):
#         length = SETTING.NEW_PWD_LENGTH[SETTING.PE_VAULT_LABEL[group]]
#         characters = string.ascii_letters + string.digits + string.punctuation
#         while True:
#             password = ''.join(random.choice(characters) for i in range(length))
#             if any(x.isdigit() for x in password) and any(x.islower() for x in password) and any(
#                     x.isupper() for x in password) and any(x in string.punctuation for x in password):
#                 break
#         return password
 
#     def ssh_generic_passreset(self,oob_user,oob_ip,ssh_ip, ssh_user, ssh_pass, target, new_pass):
#         ssh = SSHConnect(ssh_ip, ssh_user, ssh_pass)
#         try:
#             # ssh to AHV/CVM
#             ssh.connect()
#             ssh.invoke_shell()
#             if not target == 'oob':
#                 command = f"echo '{new_pass}' | sudo passwd --stdin {target}"
#             else:
#                 # This command use to list all ILO users
#                 command1 = "sudo ipmitool user list 1"
#                 ssh.send_command(command1)
#                 time.sleep(5)
#                 command_output = ssh.receive_output()
#                 self.logger.info(f'{command_output}')
#                 lines = command_output.strip().split('\n')
#                 name_to_find = 'Administrator'
#                 for line in lines[1:]:
#                     fields = line.split()
#                     if name_to_find in fields:
#                         # 获取 Administrator 行的 ID
#                         admin_id = fields[0]
#                         self.logger.info(f"ID for '{name_to_find}' is: {admin_id}")
#                         break
#                 else:
#                     self.logger.error(f"'{name_to_find}' not found in the data.")
#                 print('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^' + admin_id)
#                 command = f"sudo ipmitool user set password {admin_id} '{new_pass}'"
#             # send resetpassword command
#             ssh.send_command(command)
#             time.sleep(5)
#             output = ssh.recv()
#             # check if password reset successful
#             pattern = r'\bsuccessful\b'
#             if not re.search(pattern, output[1]):
#                 return False, "Password Reset failed. reason: " + output
#             print(target+'000000000000000000000000000000000000000')         
#             if not target == 'oob':
#                 print('1111111111111111111111111111')      
#                 ssh_test = SSHConnect(ssh_ip, ssh_user, new_pass)
#                 res, newssh = ssh_test.connect()
#                 if res is True:
#                     # if "all authentication tokens updated successfully" in output:
#                     print("Password Update Success")
#                     return True
#                 else:
#                     print('test shh connect error: ' + newssh)
#                     return res
#             else:
#                 print('********************************************************')
#                 rf = Redfish(oob_ip,oob_user,new_pass)
#                 token_status = rf.get_ilo_token(oob_ip,oob_user,new_pass)
#                 if token_status == 201:
#                     print('ILO password Update Success')
#                     return True,f"ILO password Update Success"
#                 else:
#                     print('Password Reset failed')
#                     return False, f"Password Reset failed. reason: " + token_status
#         except Exception as e:
#             print(f"The password reset has failed. Error: {e}")
#             return False, "SSH conncect exception."
#     ############### get user info ###########################
#     def host_convert(self, data):
#         arr = []
#         for host in data:
#             ret = {}
#             for target in {'cvm', 'ahv', 'oob'}:
#                 ret[target] = {'users': []}
#                 new_dict = dict(filter(lambda item: item[0].startswith(target), host.items()))
#                 if target == 'ahv':
#                     new_dict['ahv_ip'] = host['ip']
 
#                 for k, v in new_dict.items():
#                     if isinstance(v, dict):
#                         v['group'] = k
#                         ret[target]['users'].append(v)
#                     else:
#                         ret[target]['ip'] = new_dict[target + '_ip']
#             arr.append(ret)
#         return arr
 
#     # Get host_info
#     def get_hosts(self, pc, pe):
#         op = NutanixOperation(pc, pe)
#         res, hosts = op.get_pe_info()
#         op.sa = ServiceAccount('nutanix_pm').get_service_account()
#         op._prism = PrismCentral(pc, op.sa)
#         tier = op._prism.get_prism_by_name_from_db(fqdn=pc)['tier']
#         if not res:
#             raise Exception('Can not get Hosts')
#         return hosts, tier
 
    # pc、pe string, type list like ['cvm','ahv','oob']
 
# def change_pwd_public(self, pc, pe, types):
#     # pc,pe get hosts
#     hosts, tier = self.get_hosts(pc, pe)
#     # covert hosts
#     arr = self.host_covert(hosts)
#     # get user list by type list
#     pass_arr = {}
#     for target in types:
#         for result in arr:
#             if target == 'oob':
#                 ip = result['ahv']['ip']
#                 proxy_user = result['ahv']['users'][0]
#                 proxy_user['group'] = 'oob_admin'
#                 users = [proxy_user]
#                 oobip = result['oob']['ip']
#                 oobuser = result['oob']['users'][0]['username']
#             else:
#                 ip = result[target]['ip']
#                 users = result[target]['users']
#             for user in users:
#                 if target == 'oob':
#                     group_name = 'oob_admin'
#                 else:
#                     group_name = user['group']
#                 new_pass = pass_arr.get(group_name).get('password') if pass_arr.get(group_name) is not None else 'EMPTY'
#                 print('group_name: ' + group_name + ', new pass: ' + new_pass)
#                 if new_pass == 'EMPTY':
#                     # generate password & save same group password
#                     new_pass = self.generate_newpassword(group_name)
#                     pass_arr.setdefault(group_name, {'password': new_pass, 'username': user['username']})
#                 print(group_name, types, new_pass)
#                 # change pwd by ssh command
#                 print('>>>>>>>>>>>>>>' +ip,oobuser,oobip,user['username'], user['password'], group_name, new_pass)
#                 self.ssh_generic_passreset(oobuser,oobip,ip, user['username'], user['password'], target, new_pass)
#                 # if not res:
#                 # lg.write_resetpwd_log(loginfo = f'init db log  ')
#                 # raise Exception('change pwd with error: ' + message )
#                 #   return res, message
#                 # if res:
#                 # set_vault_token(tier,pe,user['username'], new_pass, user['group'])
#                 # else:
#                 #    print(message)
#                 # add log
#                 lg = DBLogging(logdir=SETTING.NTX_LOG_PATH, taskid=task_id, logtype="NTX_PM")
#                 lg.write_resetpwd_log(loginfo = f'init db log  ')
#                 # db.session.commit()
# #   db.session.close()
#     print(pass_arr)
#     for group, ele in pass_arr.items():
#             print('tier: ' + tier + ', pe: ' + pe + ', group: '+ group + ', username: ' + ele['username'] + ', pwd: ' + ele['password'])
#     self.set_vault_token(tier, pe, ele['username'], ele['password'], group)

# def set_vault_token(self,tier, pe, username, password, group):
#     label = pe + '/' + SETTING.PE_VAULT_LABEL[group]
#     _vault = Vault(tier)
#     _vault.set_vault_password(username, password, label)
# # real action
# # change_pwd_public(pc, pe, types)

# def rotate_password_all(self, pc, pe):
#     hosts, tier = self.get_hosts(pc, pe)
#     ahvips = []
#     cvmips = []
#     oobips = []
#     ahvusers = []
#     cvmusers = []
#     oobusers = []

#     for host in hosts:
#         if len(ahvusers) == 0:
#             ahv_elements = {key:value for key, value in host.items() if 'ahv_' in key and isinstance(value, dict)}
#             cvm_elements = {key:value for key, value in host.items() if 'cvm_' in key and isinstance(value, dict)}
#             oob_elements = {key:value for key, value in host.items() if 'oob_' in key and isinstance(value, dict)}
#             login_user = cvm_elements['cvm_nutanix']['username']
#             login_passwd = cvm_elements['cvm_nutanix']['password']
#             ahvusers = ahv_elements
#             cvmusers = cvm_elements
#             oobusers = oob_elements
#         ahvips.append(host['ip'])
#         cvmips.append(host['cvm_ip'])
#         oobips.append(host['oob_ip'])
                        
#     ##### start change AHV relate user passwd
#     for key, value in ahvusers.items():
#         target_user = value['username']
#         new_passwd = ps.generate_newpassword(key)
#         for ahv_ip in ahvips:
#             print(ahv_ip,login_user,login_passwd, target_user, new_passwd + "***************************************")
#             self.reset_passwd_ahv(ahv_ip,login_user,login_passwd, target_user, new_passwd)
#         self.set_vault_token(tier, pe, target_user, new_passwd, key)  
    
#         ##### start change ILO user passwd
#     for key, value in oobusers.items():
#         target_user = value['username']
#         new_passwd = ps.generate_newpassword(key)
#         for host in hosts:
#             print(host['cvm_ip'], login_user, login_passwd, target_user, host['oob_ip'], new_passwd + "***************************************")
#             self.reset_passwd_ilo(host['cvm_ip'] ,login_user, login_passwd, target_user, host['oob_ip'], new_passwd)
#         self.set_vault_token(tier, pe, target_user, new_passwd, key) 

#     ##### start change CVM relate user passwd
#     for key, value in cvmusers.items():
#         target_user = value['username']
#         new_passwd = ps.generate_newpassword(key)
#         for cvmip in cvmips:
#             self.reset_passwd_cvm(cvmip,login_user,login_passwd, target_user, new_passwd)
#         self.set_vault_token(tier, pe, target_user, new_passwd, key) 

#     ##### Start renew ssh_key for PE
#     sshkey = Sshkeys(pe=pe)
#     self.logger.info('check if ssh_key exist')
#     res = sshkey.check_ssh_keys()
#     publickey = res[0].get('key').replace("\r\n", "")
#     if publickey:
#         sshkey.delete_public_key()
#     publickey = sshkey.generate_ssh_key(private_key=f"{sshkey.sshpath}\\prvkey")

#     sshkey.install_public_key(publickey)



#     def reset_passwd_cvm(self, ssh_ip, ssh_user, ssh_pass, target_user, new_pass):
#         ssh = SSHConnect(ssh_ip, ssh_user, ssh_pass)
#         try:
#             # ssh to AHV/CVM
#             ssh.connect()
#             ssh.invoke_shell()
#             command = f"echo '{new_pass}' | sudo passwd --stdin {target_user}"
#             ssh.send_command(command)
#             time.sleep(5)
#             command_output = ssh.receive_output()
#             # check if password reset successful
#             pattern = r'\bsuccessfully\b'
#             if not re.search(pattern, command_output):
#                 return False, "Password Reset failed. reason: " + command_output
#             else:
#                 print("Password Update Success")
#                 return True
#             # ssh_test = SSHConnect(ssh_ip, ssh_user, new_pass)
#             # res, newssh = ssh_test.connect()
#             # if res is True:
#             #     # if "all authentication tokens updated successfully" in output:
#             #     print("Password Update Success")
#             #     return True
#             # else:
#             #     print('test shh connect error: ' + newssh)
#             #     return res
#         except Exception as e:
#             print(f"The password reset has failed. Error: {e}")
#             return False, "SSH conncect exception."
    
#     def reset_passwd_ahv(self, ssh_ip, ssh_user, ssh_pass, target_user, new_pass):
#         ssh = SSHConnect(ssh_ip, ssh_user, ssh_pass)
#         try:
#             # ssh to CVM
#             ssh.connect()
#             ssh.invoke_shell()
#             # jump to AHV mode
#             command = "ssh root@***********"
#             ssh.send_command(command)
#             time.sleep(5)
#             command1 = f"echo '{new_pass}' | sudo passwd --stdin {target_user}"
#             ssh.send_command(command1)
#             time.sleep(5)
#             command_output = ssh.receive_output()
#             # check if password reset successful
#             pattern = r'\bsuccessfully\b'
#             if not re.search(pattern, command_output):
#                 return False, "Password Reset failed. reason: " + command_output
#             else:
#                 print("Password Update Success")
#                 return True
#         except Exception as e:
#             print(f"The password reset has failed. Error: {e}")
#             return False, "SSH conncect exception."
   
#     def reset_passwd_ilo(self, ssh_ip, ssh_user, ssh_pass, oob_user, oob_ip, new_pass):
#         ssh = SSHConnect(ssh_ip, ssh_user, ssh_pass)
#         try:
#             # ssh to AHV
#             ssh.connect()
#             ssh.invoke_shell()
#             # jump to AHV mode
#             command = "ssh root@***********"
#             ssh.send_command(command)
#             time.sleep(5)
#             command1 = "sudo ipmitool user list 1"
#             ssh.send_command(command1)
#             time.sleep(5)
#             command_output = ssh.receive_output()
#             self.logger.info(f'{command_output}')
#             lines = command_output.strip().split('\n')
#             name_to_find = 'Administrator'
#             for line in lines[1:]:
#                 fields = line.split()
#                 if name_to_find in fields:
#                     # 获取 Administrator 行的 ID
#                     admin_id = fields[0]
#                     self.logger.info(f"ID for '{name_to_find}' is: {admin_id}")
#                     break
#             else:
#                 self.logger.error(f"'{name_to_find}' not found in the data.")
#             command = f"sudo ipmitool user set password {admin_id} '{new_pass}'"
#             ssh.send_command(command)
#             time.sleep(5)
#             output = ssh.recv()
#             # check if password reset successful
#             pattern = r'\bsuccessful\b'
#             if not re.search(pattern, output[1]):
#                 return False, "Password Reset failed. reason: " + output
#             rf = Redfish(oob_ip,oob_user,new_pass)
#             token_status = rf.get_ilo_token(oob_ip,oob_user,new_pass)
#             if token_status == 201:
#                 print('ILO password Update Success')
#                 return True,f"ILO password Update Success"
#             else:
#                 print('Password Reset failed')
#                 return False, f"Password Reset failed. reason: " + token_status
#         except Exception as e:
#             print(f"The password reset has failed. Error: {e}")
#             return False, "SSH conncect exception."
    


# ps = password_relate()
# ps.change_passwd_cvm_ahv('*************', 'nutanix', 'C4U4Fb4S!aYrRxYd2', 'C4U4Fb4S!aYrRxYd1')
# ps.change_pwd_public('ssp-ppe-ntx.ikea.com','RETCN888-NXC000', ['oob'])
# hosts,tier = ps.get_hosts('ssp-ppe-ntx.ikea.com','RETCN888-NXC000')
# arr = ps.host_convert(hosts)


       


# def rotate_password(self, types):           # noqa
#     def generate_newpassword(group):
#         length = SETTING.NEW_PWD_LENGTH[SETTING.PE_VAULT_LABEL[group]]
#         characters = string.ascii_letters + string.digits + string.punctuation
#         while True:
#             password = ''.join(random.choice(characters) for i in range(length))
#             if any(x.isdigit() for x in password) and any(x.islower() for x in password) and any(
#                     x.isupper() for x in password) and any(x in string.punctuation for x in password):
#                 break
#         return password

#     def ssh_generic_passreset(ssh_ip, ssh_user, ssh_pass, target, new_pass):
#         ssh = SSHConnect(ssh_ip, ssh_user, ssh_pass)
#         try:
#             # ssh to AHV/CVM
#             ssh.connect()
#             ssh.invoke_shell()
#             if not target == 'oob':
#                 command = f"echo '{new_pass}' | sudo passwd --stdin {target}"
#             else:
#                 # Find administrator User ID

#                 user_id = None
#                 ## This command use to list all ILO users
#                 command1 = "sudo ipmitool user list 1"
#                 ssh.send_command(command1)
#                 output1 = ssh.recv()
#                 for i in range(len(output1[1].split("\r\n"))):
#                     if "Administrator" in output1[1].split("\r\n")[i]:
#                         print(output1[1].split("\r\n")[i])
#                         print(output1[1].split("\r\n")[i][0])
#                         user_id = output1[1].split("\r\n")[i][0]
#                         break
#                 print(user_id)
#                 command = f"echo '{new_pass}' | sudo ipmitool user set password {user_id} "
#             # send resetpassword command
#             ssh.send_command(command)
#             output = ssh.recv()
#             # check if password reset successful
#             if "all authentication tokens updated successfully" in output:
#                 ssh_test = SSHConnect(ssh_ip, ssh_user, new_pass)
#                 res, newssh = ssh_test.connect()
#                 if res is True:
#                     # if "all authentication tokens updated successfully" in output:
#                     print("Password Update Success")
#                 else:
#                     print('test shh connect error: ' + newssh)
#                 return res
#             print("Password Reset failed.")
#             return False, "Password Reset failed. reason: " + output

#         except Exception as e:
#             print(f"The password reset has failed. Error: {e}")
#             return False, "SHH conncect exception."
#         finally:
#             ssh.close()
#             return True

#     ############### get user info ###########################
#     def host_covert(data):
#         arr = []
#         for host in data:
#             ret = {}
#             for target in {'cvm', 'ahv', 'oob'}:
#                 ret[target] = {'users': []}
#                 new_dict = dict(filter(lambda item: item[0].startswith(target), host.items()))
#                 if target == 'ahv':
#                     new_dict['ahv_ip'] = host['ip']

#                 for k, v in new_dict.items():
#                     if isinstance(v, dict):
#                         v['group'] = k
#                         ret[target]['users'].append(v)
#                     else:
#                         ret[target]['ip'] = new_dict[target + '_ip']
#             arr.append(ret)
#         return arr

#     # Get host_info
#     def get_hosts(pc, pe):
#         op = NutanixOperation(pc, pe)
#         res, hosts = op.get_pe_info()
#         op.sa = ServiceAccount('nutanix_pm').get_service_account()
#         op._prism = PrismCentral(pc, op.sa)
#         tier = op._prism.get_prism_by_name_from_db(fqdn=pc)['tier']
#         if not res:
#             raise Exception('Can not get Hosts')
#         return hosts, tier

#     # pc、pe string, type list like ['cvm','ahv','oob']

#     def change_pwd_public(pc, pe, types):
#         # pc,pe get hosts
#         hosts, tier = get_hosts(pc, pe)
#         # covert hosts
#         arr = host_covert(hosts)
#         # get user list by type list
#         pass_arr = {}
#         for target in types:
#             for result in arr:
#                 if target == 'oob':
#                     ip = result['ahv']['ip']
#                     proxy_user = result['ahv']['users'][0]
#                     proxy_user['group'] = 'oob_admin'
#                     users = [proxy_user]
#                 else:
#                     ip = result[target]['ip']
#                     users = result[target]['users']
#                 for user in users:
#                     if target == 'oob':
#                         group_name = 'oob_admin'
#                     else:
#                         group_name = user['group']
#                     new_pass = pass_arr.get(group_name).get('password') if pass_arr.get(group_name) is not None else 'EMPTY'
#                     print('group_name: ' + group_name + ', new pass: ' + new_pass)
#                     if new_pass == 'EMPTY':
#                         # generate password & save same group password
#                         new_pass = generate_newpassword(group_name)
#                         pass_arr.setdefault(group_name, {'password': new_pass, 'username': user['username']})
#                     print(group_name, types, new_pass)
#                     # change pwd by ssh command
#                     # print('>>>>>>>>>>>>>>' +ip, user['username'], user['password'], group_name, new_pass)
#                     ssh_generic_passreset(ip, user['username'], user['password'], target, new_pass)
#                     # if not res:
#                     # lg.write_resetpwd_log(loginfo = f'init db log  ')
#                     # raise Exception('change pwd with error: ' + message )
#                     #   return res, message
#                     # if res:
#                     # set_vault_token(tier,pe,user['username'], new_pass, user['group'])
#                     # else:
#                     #    print(message)
#                     # add log
#                     # lg = DBLogging(logdir=SETTING.NTX_LOG_PATH, taskid=task_id, logtype="NTX_PM")
#                     # lg.write_resetpwd_log(loginfo = f'init db log  ')
#                     # db.session.commit()
#     #   db.session.close()
#         print(pass_arr)
#         for group, ele in pass_arr.items():
#             # print('tier: ' + tier + ', pe: ' + pe + ', group: '+ group + ', username: ' + ele['username'] + ', pwd: ' + ele['password'])
#             set_vault_token(tier, pe, ele['username'], ele['password'], group)

#     def set_vault_token(tier, pe, username, password, group):
#         label = pe + '/' + SETTING.PE_VAULT_LABEL[group]
#         _vault = Vault(tier)
#         _vault.set_vault_password(username, password, label)
#     # real action
#     change_pwd_public(self.pc, self.pe, types)

# def rotate_password_all(self, types):
#     self.rotate_password(self.pc, self.pe, types)




#     ##########################################################
# class password_relate():           # noqa
#     def __init__(self, logger = logging) -> None:
#         self.logger = logger if logger else logging
#     def generate_newpassword(self,group):
#         length = SETTING.NEW_PWD_LENGTH[SETTING.PE_VAULT_LABEL[group]]
#         characters = string.ascii_letters + string.digits + string.punctuation
#         while True:
#             password = ''.join(random.choice(characters) for i in range(length))
#             if any(x.isdigit() for x in password) and any(x.islower() for x in password) and any(
#                     x.isupper() for x in password) and any(x in string.punctuation for x in password):
#                 break
#         return password
 
#     def ssh_generic_passreset(self,oob_user,oob_ip,ssh_ip, ssh_user, ssh_pass, target, new_pass):
#         ssh = SSHConnect(ssh_ip, ssh_user, ssh_pass)
#         try:
#             # ssh to AHV/CVM
#             ssh.connect()
#             ssh.invoke_shell()
#             if not target == 'oob':
#                 command = f"echo '{new_pass}' | sudo passwd --stdin {target}"
#             else:
#                 # This command use to list all ILO users
#                 command1 = "sudo ipmitool user list 1"
#                 ssh.send_command(command1)
#                 time.sleep(5)
#                 command_output = ssh.receive_output()
#                 lines = command_output.strip().split('\n')
#                 name_to_find = 'Administrator'
#                 for line in lines[1:]:
#                     fields = line.split()
#                     if name_to_find in fields:
#                         # 获取 Administrator 行的 ID
#                         admin_id = fields[0]
#                         print(f"ID for '{name_to_find}' is: {admin_id}")
#                         break
#                 else:
#                     print(f"'{name_to_find}' not found in the data.")
#                 print('^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^' + admin_id)
#                 command = f"sudo ipmitool user set password {admin_id} '{new_pass}'"
#             # send resetpassword command
#             ssh.send_command(command)
#             time.sleep(5)
#             output = ssh.recv()
#             # check if password reset successful
#             pattern = r'\bsuccessful\b'
#             if not re.search(pattern, output[1]):
#                 return False, "Password Reset failed. reason: " + output
#             print(target+'000000000000000000000000000000000000000')         
#             if not target == 'oob':
#                 print('1111111111111111111111111111')      
#                 ssh_test = SSHConnect(ssh_ip, ssh_user, new_pass)
#                 res, newssh = ssh_test.connect()
#                 if res is True:
#                     # if "all authentication tokens updated successfully" in output:
#                     print("Password Update Success")
#                     return True
#                 else:
#                     print('test shh connect error: ' + newssh)
#                     return res
#             else:
#                 print('********************************************************')
#                 rf = Redfish(oob_ip,oob_user,new_pass)
#                 token_status = rf.get_ilo_token(oob_ip,oob_user,new_pass)
#                 if token_status == 201:
#                     print('ILO password Update Success')
#                     return True,f"ILO password Update Success"
#                 else:
#                     print('Password Reset failed')
#                     return False, f"Password Reset failed. reason: " + token_status
#         except Exception as e:
#             print(f"The password reset has failed. Error: {e}")
#             return False, "SSH conncect exception."
#     ############### get user info ###########################
#     def host_covert(self,data):
#         arr = []
#         for host in data:
#             ret = {}
#             for target in {'cvm', 'ahv', 'oob'}:
#                 ret[target] = {'users': []}
#                 new_dict = dict(filter(lambda item: item[0].startswith(target), host.items()))
#                 if target == 'ahv':
#                     new_dict['ahv_ip'] = host['ip']
 
#                 for k, v in new_dict.items():
#                     if isinstance(v, dict):
#                         v['group'] = k
#                         ret[target]['users'].append(v)
#                     else:
#                         ret[target]['ip'] = new_dict[target + '_ip']
#             arr.append(ret)
#         return arr
 
#     # Get host_info
#     def get_hosts(self,pc, pe):
#         op = NutanixOperation(pc, pe)
#         res, hosts = op.get_pe_info()
#         op.sa = ServiceAccount('nutanix_pm').get_service_account()
#         op._prism = PrismCentral(pc, op.sa)
#         tier = op._prism.get_prism_by_name_from_db(fqdn=pc)['tier']
#         if not res:
#             raise Exception('Can not get Hosts')
#         return hosts, tier
 
#     # pc、pe string, type list like ['cvm','ahv','oob']
 
#     def change_pwd_public(self, pc, pe, types):
#         # pc,pe get hosts
#         hosts, tier = self.get_hosts(pc, pe)
#         # covert hosts
#         arr = self.host_covert(hosts)
#         # get user list by type list
#         pass_arr = {}
#         for target in types:
#             for result in arr:
#                 if target == 'oob':
#                     ip = result['ahv']['ip']
#                     proxy_user = result['ahv']['users'][0]
#                     proxy_user['group'] = 'oob_admin'
#                     users = [proxy_user]
#                     oobip = result['oob']['ip']
#                     oobuser = result['oob']['users'][0]['username']
#                 else:
#                     ip = result[target]['ip']
#                     users = result[target]['users']
#                 for user in users:
#                     if target == 'oob':
#                         group_name = 'oob_admin'
#                     else:
#                         group_name = user['group']
#                     new_pass = pass_arr.get(group_name).get('password') if pass_arr.get(group_name) is not None else 'EMPTY'
#                     print('group_name: ' + group_name + ', new pass: ' + new_pass)
#                     if new_pass == 'EMPTY':
#                         # generate password & save same group password
#                         new_pass = self.generate_newpassword(group_name)
#                         pass_arr.setdefault(group_name, {'password': new_pass, 'username': user['username']})
#                     print(group_name, types, new_pass)
#                     # change pwd by ssh command
#                     print('>>>>>>>>>>>>>>' +ip,oobuser,oobip,user['username'], user['password'], group_name, new_pass)
#                     self.ssh_generic_passreset(oobuser,oobip,ip, user['username'], user['password'], target, new_pass)
#                     # if not res:
#                     # lg.write_resetpwd_log(loginfo = f'init db log  ')
#                     # raise Exception('change pwd with error: ' + message )
#                     #   return res, message
#                     # if res:
#                     # set_vault_token(tier,pe,user['username'], new_pass, user['group'])
#                     # else:
#                     #    print(message)
#                     # add log
#                     # lg = DBLogging(logdir=SETTING.NTX_LOG_PATH, taskid=task_id, logtype="NTX_PM")
#                     # lg.write_resetpwd_log(loginfo = f'init db log  ')
#                     # db.session.commit()
#     #   db.session.close()
#         print(pass_arr)
#         for group, ele in pass_arr.items():
#              print('tier: ' + tier + ', pe: ' + pe + ', group: '+ group + ', username: ' + ele['username'] + ', pwd: ' + ele['password'])
#         self.set_vault_token(tier, pe, ele['username'], ele['password'], group)
 
#     def set_vault_token(self,tier, pe, username, password, group):
#         label = pe + '/' + SETTING.PE_VAULT_LABEL[group]
#         _vault = Vault(tier)
#         _vault.set_vault_password(username, password, label)
#         # real action
#         # change_pwd_public(pc, pe, types)

#     def rotate_password_all(self, types):
#         self.change_pwd_public(self.pc, self.pe, types)



# class TestAPI:
#     def __init__(self, pc,  sa=None, logger=logging) -> None:
#         # _sa = sa if sa else ServiceAccount(usage=ServiceAccount.NUTANIX_PM).get_service_account()
#         self.logger = logger
#         self.rest = CommonRestCall(username='<EMAIL>', password='!4Zv*!g1!t!*uSbn5pv029r8t08HuAC*')
#         self.pc = pc

#     def check_reachable(self, pe):
#         url = f"https://{self.pc}:9440/api/nutanix/v3/groups"
#         payload =  {
#             "entity_type": "cluster",
#             "filter_criteria": "include_pc==true",
#             "group_member_attributes": [
#                 {
#                 "attribute": "name"
#                 },
#                 {
#                 "attribute": "is_available"
#                 }
#                 ]
#             }
#         try:
#             res = self.rest.call_restapi(url,method="POST", payload=payload)
#             #access = res['group_results'][0]['entity_results'][0]['data'][1]['values'][0]['values'][0]
#             results =  res.json()['group_results'][0]['entity_results']
#             for result in results:
#                 print(result)
#                 if pe == result['data'][0]['values'][0]['values'][0]:
#                    status = result['data'][1]['values'][0]['values'][0]
#             print(status)
#             return (status)
#         except Exception:
#             self.logger.error(f'errorrrrrrrrrrrrrrrrr')
#             return False

# print(TestAPI('ssp-china-ntx.ikea.com').check_reachable('IICCN048-NXC001'))






# import os
# import subprocess


# class Sshkeys():
#     def __init__(self, pe=None, sa=None, logger=logging) -> None:
#         self.pe = PrismElement(pe=pe).pe
#         self.logger = logger
#         _sa = sa if sa else ServiceAccount(usage=ServiceAccount.NUTANIX_PM).get_service_account()
#         self.rest = CommonRestCall(username=_sa['username'], password=_sa['password'])
#         self.sshpath = self.init_tmp_folder()

#     def init_tmp_folder(self):
#         ssh_tmp_path = f'{SETTING.SSH_KEY_PATH}\\{self.pe}_{datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}'
#         if not os.path.exists(ssh_tmp_path):
#             print(111111)
#             os.mkdir(ssh_tmp_path)
#         return ssh_tmp_path
    
#     def generate_ssh_key(self, private_key, key_type="rsa", bits=2048):
        
#         if os.path.exists(private_key):
#             print(f"Removing existing key: {private_key}")
#             os.remove(private_key)
            
#         command = f"ssh-keygen -t {key_type} -b {bits} -f {private_key} -q -N ''"
        
#         try:
#             print(f"Generating SSH key: {command}")
#             subprocess.run(command, shell=True, check=True)
#         except subprocess.CalledProcessError as e:
#             print(f"Error generating key: {e}")
#             return None
            
#         public_key = f"{private_key}.pub"
        
#         if not os.path.exists(public_key):
#             print(f"Public key file not found: {public_key}")
#             return None
            
#         print(f"Key pair generated: {private_key}")
        
#         with open(f"{self.sshpath}\\{public_key}.pub", "r") as public_key:
#             public_key_str = public_key.read().replace("\n", "")
#         return public_key_str

#     def check_ssh_keys(self):
#         url = f"https://{self.pe}:9440/PrismGateway/services/rest/v1/cluster/public_keys"
#         try:
#             res=self.rest.call_restapi(url,method="GET")
#             return res.json()
#         except Exception:
#             self.logger.error(f'Can not find any keys!')
#             return False
        
#     def delete_public_key(self,keyname="Gateway"):
#         url = f"https://{self.pe}:9440/PrismGateway/services/rest/v1/cluster/public_keys/{keyname}"
#         try:
#             res=self.rest.call_restapi(url,method="DELETE")
#             return res
#         except Exception:
#             self.logger.error(f'SSH_kEY delete failed!')
#             return False
        
#     def install_public_key(self,public_key):
#         url = f"https://{self.pe}:9440/PrismGateway/services/rest/v1/cluster/public_keys"
#         payload =  {
#             "name" : "Gateway",
#             "key": {public_key},
#         }
#         try:
#             res=self.rest.call_restapi(url,method="POST",payload=payload)
#             return True
#         except Exception:
#             self.logger.error(f'Install failed!')
#             return False
    
        
# s = Sshkeys(pe="RETNO722-NXC000")
# s = PasswordRelate()
# res = s.check_ssh_keys()
# publickey,private_key = s.generate_ssh_key(private_key=f"{s.sshpath}\\private")
# s.delete_public_key()
# s.install_public_key(public_key=publickey)
# oob_ip = "retse999-nx7001oob.ikea.com"
# oob_user = "administrator"
# new_pass = "WRsbOel9z.8s|`r"
# rf = Redfish(oob_ip,oob_user,new_pass)
# res = rf.get_ilo_token()
# print(res.json())




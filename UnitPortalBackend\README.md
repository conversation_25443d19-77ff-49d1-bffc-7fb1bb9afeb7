# What's this?
This is a readme file for distributed hosting portal.

# What's the portal for?
We are providing our consumers services in below list:

1. RESTful API for executing power maintanence on both Nutanix/SimpliVity platform.

2. Strong solid data for both Nutanix/SimpliVity platform.

3. To be continue...

# Who's doing this?
We hope more and more coders in our unit can join this.

# Programming specification
## Powershell
### Naming Convention
* Variables naming: Use `CamelCase` to name your variables, eg: `MyName` , `PrismCentralName`...
* Functions naming: Use `Verb-Non` to name your functions, if you need more than 1 non in the function naming, use `CamelCase`, eg: `Get-PrismList`, `Call-NutanixApiV3`...
* Functions definition: 
    - Define the synopsis with a description and the comments about the core parameters.
    - Use 4 spaces '    '  or a tab for indentation in your code to make it more readable
    - Use 1 space at the both end of a symbol like '=', '-eq', ','
```
Function Get-PrismStatus{
    <#
    .SYNOPSIS
    Get status of a giving Prism.
    
    .DESCRIPTION
    Uses Nutanix API to get the status of a Prism.
    
    .PARAMETER Prism
    Can be and IP address, or fqdn.

    #>
    Param (
      [parameter(mandatory)] [string] $Prism
    )
    
    if($Prism){
        #here is one indentation
        $a , $b = 1 , 2
        while(1){
            #here is another indentation
        }
    }
    ...
    ...
}
```

# Run Flask Application
## Install Python Packages
Recommend to install for all users in the system python path.
```bash
# install all required packages
pip install -r requirements.txt --target="C:\Program Files\Python3\Lib\site-packages"
```
If the python path doesn't exist, use `where python` to find.

## Run Application

```bash
python app.py
```

Note: In order to register scheduled jobs with Admin privilege, please open a Command Prompt with "Run as administrater", then execute the command.

# Run Flask Application as a Windows Service
## (Temporary Workaround) Modify python built-in code
`<Your-Path>\Python\Python311\Lib\site-packages\click\utils.py`
Line 299:
```python
    if file:    # add this line
        file.write(out)  # type: ignore
        file.flush()
```


## Install & Start service
```bash
python windows_service.py install
python windows_service.py start
```
## Configure the service to start automatically when Windows boots

Steps
1. Open Task Manager
2. Right click on "UnitPortalBackend" and select "Open Services"
3. Right click on "Unit Portal Backend" -> Properties -> Startup type -> Select "Automatic"

## Swagger UI
{baseurl}/swagger-ui
Note:
To disable "Try it out" on swagger UI, add a line in:
`Lib/site-packages/flask_apispec/templates/swagger-ui.html`
```javascript
var ui = SwaggerUIBundle({
  url: "{{ url_for('flask-apispec.swagger-json') }}",
  // ...
  supportedSubmitMethods: ["get"]     <--here
})
window.ui = ui
```


# Run with Docker
1. Build docker image
```bash
docker build -t <image-name>:<version> .
# e.g.
docker build -t unit-portal-backend:1.0 .
```

2. Run container
```bash
docker run --name <application-name> -d -p <host-port>:<container-port> <image-name>:<version>
# e.g.
docker run --name UnitPortalBackend -d -p 5888:5888 unit-portal-backend:1.0
```
# Code Checkers
## Pylint
The following pylint chekcers are enabled:

* C0103, # (invalid-name): Function name "ScanPMTask" doesn't conform to snake_case naming style
    - Valid:
    ```bash
    Valid: def scan_pm_task()
    ```
    - Invalid:
    ```bash
    Invalid: def ScanPMTask()
    ```

* E0602, # (undefined-variable)
    - Valid:
    ```bash
    number = 1
    print(number) # number is defined
    ```
    - Invalid:
    ```bash
    print(number) # number is undefined
    ```

* R1705, # (no-else-return)
    - Valid:
    ```bash
    def func(a):
        if a > 0:
            return True
        return False
    ```
    - Invalid:
    ```bash
    def func(a):
        if a > 0:
            return True
        else:
            return False
    ```

* R1720, # (no-else-raise)
    - Valid:
    ```bash
    def func(a, b):
        if b == 0:
            raise ValueError("b can't be zero")
        return a / b
    ```
    - Invalid:
    ```bash
    def func(a, b):
        if b == 0:
            raise ValueError("b can't be zero")
        else:
            return a / b
    ```

* W0401, # (wildcard-import): from models.ntx_models import *
    - Valid:
    ```bash
    from scheduler import auto_maintenance_scheduler
    ```
    - Invalid:
    ```bash
    from models.ntx_models import *
    ```

* W0611, # (unused-import)
* W0612, # (unused-variable)
* W0613, # (unused-argument)
* W1309, # (f-string-without-interpolation)
    - Valid:
    ```bash
    a = 1
    print(f"number a is {a}")
    ```
    - Invalid:
    ```bash
    print(f"hello world")
    ```
## Flake8
The following flake8 checks are enabled:
* E101, # Indentation contains mixed spaces and tabs
* E111, # Indentation is not a multiple of four
    - Valid:
    ```bash
    def func(a):
        return a + 1
    ```
    - Invalid:
    ```bash
    def func(a):
      return a+1
    ```
* E117, # Over-indented
    - Valid:
    ```bash
    def func(a):
        return a + 1
    ```
    - Invalid:
    ```bash
    def func(a):
            return a+1
    ```
* E225, # Missing whitespace around operator
    - Valid:
    ```bash
    a = 1 + 2
    ```
    - Invalid:
    ```bash
    a=1+2
    ```
* E231, # Missing whitespace after ',', ';', or ':'
    - Valid:
    ```bash
    a, b = 1, 2
    ```
    - Invalid:
    ```bash
    a,b=1,2
    ```
* E301, # Expected 1 blank line, found 0 (between class functions)
* E302, # expected 2 blank lines, found 1 (between classes and functions ouside class)
* E305, # Expected 2 blank lines after end of function or class
* E401, # Multiple imports on one line

# GitHub Actions Runner Configuration

## Runner Persistence Setup

The GitHub Actions Runner has been configured as a system service to ensure it automatically starts after system reboots and runs continuously.

### SELinux Configuration

Since SELinux is enabled on the system, we created a custom SELinux policy to allow the Runner to operate normally:

```bash
# Generate allow rules
sudo ausearch -m avc -ts recent | audit2allow -M github_runner

# Apply the generated policy
sudo semodule -i github_runner.pp
```

### Service Configuration

**Install the Runner as a service**:
   ```bash
   # Install the service with the default service name
   sudo ./svc.sh install
   
   # Start the service
   sudo ./svc.sh start
   ```
   This creates a systemd service named `actions.runner._services.retsehbg-lx8901.service` (or similar, based on your configuration and hostname).

The Runner operates as a systemd service:

```bash
# Check service status
sudo systemctl status actions.runner._services.retsehbg-lx8901.service

# Restart service
sudo systemctl restart actions.runner._services.retsehbg-lx8901.service

# Ensure service starts on boot
sudo systemctl enable actions.runner._services.retsehbg-lx8901.service
```

### Monitoring and Maintenance

We've set up a periodic check script to ensure the Runner operates continuously:

```bash
# Check script location
/usr/local/bin/check-github-runner.sh

# Script content
#!/bin/bash
if ! sudo systemctl is-active --quiet actions.runner._services.retsehbg-lx8901.service; then
    echo "GitHub Actions Runner service is not running. Attempting to restart..."
    sudo systemctl restart actions.runner._services.retsehbg-lx8901.service
fi

# Required sudo permissions (add with visudo)
# hunhe ALL=(ALL) NOPASSWD: /usr/bin/systemctl status actions.runner._services.retsehbg-lx8901.service, /usr/bin/systemctl restart actions.runner._services.retsehbg-lx8901.service

# Cron configuration (in root's crontab - checks every 10 minutes)
*/10 * * * * /usr/local/bin/check-github-runner.sh
```

## Branch Management Automation

We use GitHub Actions workflows to automatically detect and manage stale branches.

### Stale Branch Detection Policy

The system regularly checks branches in the repository and processes them according to the following policy:

1. **Detection Thresholds**:
   - Branches inactive for over 60 days are marked as "stale"
   - Branches inactive for over 90 days may be automatically deleted

2. **Notification Process**:
   - When a branch is marked as stale, the system automatically emails the branch creator
   - Notifications include branch name, days since last activity, and recommended actions

3. **Automatic Deletion Conditions**:
   - By default, only stale branches that have been merged to target branches are automatically deleted
   - This behavior can be controlled via the `DELETE_MERGED_ONLY` environment variable

### Email Notification Details

The system sends two types of emails to branch creators:

1. **Stale Notification Email**:
   - Sent when a branch has been inactive for over 60 days
   - Reminds users that the branch will be deleted soon
   - Suggests updating the branch, deleting it, or confirming it has been merged

2. **Deletion Notification Email**:
   - Sent when a branch has been automatically deleted due to inactivity
   - Notifies users that the branch has been deleted
   - If the branch was merged, includes information about the target branches

### Workflow Configuration

The stale branch detection workflow is configured in the `.github/workflows/stale-branch-checker.yml` file:

- **Run Frequency**: Automatically runs on the 1st and 16th of each month
- **Manual Trigger**: Supports manual triggering through the GitHub UI
- **Environment Variables**:
  - `DAYS_THRESHOLD`: 60 (stale marking threshold)
  - `DELETE_THRESHOLD`: 90 (deletion threshold)
  - `AUTO_DELETE`: true (enables automatic deletion)
  - `DELETE_MERGED_ONLY`: true (only deletes merged branches)

### Local Testing

You can test stale branch detection locally using `startcheck.bat`:

```bash
# Windows
startcheck.bat

# Linux/Mac
python stale_branch_checker.py
```

Detection results will be saved in the `stale_branches.txt` file, including a list of stale branches, branch creators, last committers, and notification status.

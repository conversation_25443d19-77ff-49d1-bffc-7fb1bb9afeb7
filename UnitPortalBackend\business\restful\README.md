# RESTful API

This directory contains RESTful API resources for the UnitPortal application.

## Route Decorator

The `route` decorator in `route.py` provides a convenient way to:

1. Register routes with Flask-RESTful
2. Apply APILogTracer for logging
3. Integrate with <PERSON>wagger UI for API documentation

### Usage

```python
from flask_restful import MethodResource, Resource
from flask_apispec import doc
from .route import route

@route('/api/v1/example')
class RestfulExample(MethodResource):
    @doc(description='Example API endpoint', tags=['Example'])
    def get(self):
        return {"message": "Hello, World!"}
```

### Multiple URLs

You can also register multiple URLs for a single resource class:

```python
@route(['/api/v1/user', '/api/v1/user/<user_id>'])
class RestfulUser(MethodResource):
    @doc(description='List all users', tags=['User'])
    def get(self, user_id=None):
        if user_id is None:
            # Handle GET /api/v1/user (list all users)
            return {"message": "List all users"}
        else:
            # Handle GET /api/v1/user/<user_id> (get specific user)
            return {"user_id": user_id}
    
    ...
```

### Parameters

- `urls` (str or list): The URL path(s) for the route
- `swagger` (bool): Whether to add the route to Swagger UI documentation, specify False to disable
- `trace` (bool): Whether to enable APILogTracer, default is True

## Migration Guide

To migrate existing RESTful classes to use the decorator:

1. Import the route decorator:
   ```python
   from .route import route
   ```

2. Add the decorator to your resource class:
   ```python
   @route(['/api/v1/your-endpoint', '/api/v1/your-endpoint/<param>'])
   class YourResource(MethodResource):
        @doc(description='Your description', tags=['YourTag'])
       # Your methods here
   ```

3. Remove the `@APILogTracer()` decorator from individual methods (the route decorator will handle this).

4. Remove the route registration from the corresponding route file.

## Example

Before:
```python
# restful_example.py
class RestfulExample(Resource):
    def get(self):
        return {"message": "Hello, World!"}

# example_route.py
def example_route():
    api.add_resource(RestfulExample, '/api/v1/example')
```

After:
```python
# restful_example.py
@route('/api/v1/example')
class RestfulExample(MethodResource):
    @doc(description='Example API endpoint', tags=['Example'])
    def get(self):
        return {"message": "Hello, World!"}
``` 
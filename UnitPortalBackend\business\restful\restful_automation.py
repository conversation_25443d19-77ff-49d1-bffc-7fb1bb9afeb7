#installed modules
import os
import sys
import json
import logging
import traceback
import sqlalchemy
import werkzeug.exceptions as flaskex
import re
from sqlalchemy import func
from marshmallow import fields
from pytz import timezone
from datetime import datetime, timedelta
from flask import jsonify, current_app
from tzlocal import get_localzone
from webargs.flaskparser import use_args

from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.automation.automation_exception import SeamlessLcmClusterConflict
from business.distributedhosting.nutanix.cluster.cluster_specs import ClusterSpec
from business.distributedhosting.nutanix.cluster.delete_cluster_task import DeleteClusterTask
from business.distributedhosting.nutanix.automation.wiab_create_metro_volume_group import WiaBCreateMetroVolumeGroup
from business.distributedhosting.scheduler.atm_scheduler import auto_maintenance_scheduler
from models.benchmark_models import ModelNtxBenchmark, ModelNtxBenchmarkAutoMaintenanceStep, ModelNtxBenchmarkScheduler, \
    ModelNtxBenchmarkLcmVersion, ModelNtxBenchmarkLcmVersionSchema
from swagger.cluster_schema import CreateClusterSchema, DeleteClusterSchema
from business.distributedhosting.nutanix.maintenance import Maintenance
from models.ntx_models import ModelPrismElement, ModelPrismElementSchema
from models.ntx_models_wh import ModelWarehousePrismElement, ModelWarehousePrismElementSchema
from models.cluster_models import ModelClusterTask, ModelClusterTaskSchema, ModelWhClusterTask, ModelWhClusterTaskSchema
from business.distributedhosting.nutanix.automation.auto_maintenance import AutoMaintenance
from business.distributedhosting.nutanix.automation.seamless_lcm import SeamlessLcm, SeamlessLcmPlannedPe, \
    SeamlessLcmPlan
from business.distributedhosting.nutanix.cluster.create_cluster_task import CreateClusterTask
from business.distributedhosting.nutanix.automation.desired_state_config import DscPeAvailable
from swagger.auto_maintenance_schema import  AutoMaintenanceRequestSchema,   \
    AutoMaintenanceLockRequestSchema, AutoMaintenanceLogRequestSchema, AutoMaintenanceAbortSchema, AosLcmSchema
from swagger.seamless_lcm_schema import CreateSeamlessLcmPlanSchema, \
    CancelSeamlessLcmPlannedPeSchema, UpdateSeamlessLcmPlannedPeSchema, CancelSeamlessLcmPlanSchema, \
    UpdateSeamlessLcmPlanRouteSchema

from business.distributedhosting.nutanix.automation.wiab_metro_add_disk import WiabMetroAddDisk
from business.distributedhosting.nutanix.automation.wiab_metro_list_vgs import WiabMetroListVGs
from business.distributedhosting.nutanix.automation.wiab_metro_add_iscsi_client import WiabMetroAddIscsiClient
from swagger.metro_volume_group_schema import CreateMetroVGSchema, ListMetroVGSchema, AddMetroVGDiskSchema, AddMetroIscsiClientSchema
from business.distributedhosting.nutanix.automation.seamless_lcm_specs import SeamlessLcmSpec
from business.distributedhosting.nutanix.task_status import SeamlessLcmPlannedPEStatus

sys.path.insert(0, '..')
sys.path.insert(0, '../..')
from itertools import groupby
from flask_restful import Resource
from flask_apispec import use_kwargs, doc
from flask_apispec.views import MethodResource
from flask import abort, request, Response, send_file

#local files
from models.database import db
from business.authentication.authentication import User
from models.sli_models import ModelSLIClusterSchema, ModelSLICluster
from business.authentication.tokenvalidation import PrivilegeValidation
from business.distributedhosting.nutanix.automation.lcm import LCM, AosLCM
from business.generic.commonfunc import get_request_token, get_user_by_token, FileDownloader
from business.distributedhosting.nutanix.automation.automation import Automation
from models.atm_models import ModelRetailNutanixAutomationSPPLCMTaskLog, ModelRetailNutanixAutomationSPPLCMTask, \
    ModelRetailNutanixAutomationSPPLCMTaskLogSchema, ModelRetailNutanixAutomationSPPLCMTaskSchema, \
    ModelRetailNutanixAutomationAOSLCMTask, ModelRetailNutanixAutomationAOSLCMTaskLog, \
    ModelRetailNutanixAutomationAOSLCMTaskSchema, ModelRetailNutanixAutomationAOSLCMTaskLogSchema, \
    ModelNutanixSeamlessLcmPlannedPEs
from .route import route


@route('/api/v1/ntx/automation/lcm/spp/upgrade')
class RestfulUpgradeSPP(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_spp"})
    def post(self):
        try:
            token = get_request_token()
            if param := request.get_json(force=True):
                for _key in ['pc', 'pe', 'facility_type']:
                    if _key not in list(param.keys()):
                        raise Exception({'code': 400, 'message': f'Missing fields {_key}'}) #raise if pm details not given
            else:
                raise Exception({'code': 400, 'message': 'Missing fields'}) #raise if pm details not given
            #now we assume all the required field are provided.
            #check if pc and pe exists in DB
            if param['facility_type'] == 'retail':
                pe_exists = ModelPrismElement.query.filter_by(fqdn=param['pe']).filter_by(prism=param['pc']).one_or_none()
            else:
                pe_exists = ModelWarehousePrismElement.query.filter_by(fqdn=param['pe']).filter_by(prism=param['pc']).one_or_none()
                
            if not pe_exists:
                raise Exception("Cannot find the PE in the DB.")
                
            logging.info('pe exists, proceed with the SPP upgrade.')
            #now check if SPP upgrade task for the same PE already exists
            if _existing_job := ModelRetailNutanixAutomationSPPLCMTask.query.filter(
                    ModelRetailNutanixAutomationSPPLCMTask.pe == param['pe']).filter(
                    ModelRetailNutanixAutomationSPPLCMTask.status != 'done').filter(
                    ModelRetailNutanixAutomationSPPLCMTask.status != 'error').first():
                logging.info('SPP upgrading for this PE already started.')
                abort(500, f"SPP task already exists in this PE {param['pe']}")
            # automation = Automation(pc=param['pc'], pe=param['pe'], token=token)
            # automation.perform_spp_lcm(target_spp_version=param['spp_version'])
            lcm = LCM(pc=param['pc'], pe=param['pe'], token=token, facility_type=param['facility_type'])
            res, mes = lcm.create_task()   # need to optimize when it returns false
            result = {'result': res, 'message': mes}
            return Response(json.dumps(result), status=200, mimetype='application/json')
        except Exception as e:
            abort(500, f"Internal error, error: {str(e)}")


@route('/api/v1/ntx/automation/lcm/spp/tasks')
class RestfulSPPTasks(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_spp"})
    def get(self):
        try:
            taskinfo = ModelRetailNutanixAutomationSPPLCMTaskSchema(many=True).dump(ModelRetailNutanixAutomationSPPLCMTask.query.all())
            return jsonify(taskinfo)
        except Exception:
            abort(500, "Internal error")

@route('/api/v1/ntx/automation/lcm/spp/task/list/<facility_type>')
class RestfulGetSPPTaskList(MethodResource, Resource):
    @doc(description="Get SPP list", tags=['Automation'])
    @PrivilegeValidation(privilege={"role_lcm": "view_spp"})
    def get(self, facility_type, *args, **kwargs): # pylint: disable=W0613
        try:
            subq = db.session.query(
                ModelRetailNutanixAutomationSPPLCMTask.pe,
                func.max(ModelRetailNutanixAutomationSPPLCMTask.id).label("max_id")
            ).group_by(ModelRetailNutanixAutomationSPPLCMTask.pe).subquery()

            latest_tasks = db.session.query(ModelRetailNutanixAutomationSPPLCMTask).join(
                subq, ModelRetailNutanixAutomationSPPLCMTask.id == subq.c.max_id
            ).all()
            latest_task_dict = {task.pe: ModelRetailNutanixAutomationSPPLCMTaskSchema().dump(task) for task in latest_tasks}

            if facility_type == 'retail':
                _model = ModelPrismElement
                _schema = ModelPrismElementSchema
            else:
                _model = ModelWarehousePrismElement
                _schema = ModelWarehousePrismElementSchema
            _pe = _model.query.with_entities(
                _model.fqdn, _model.prism, _model.node_number, _model.foundation_version, _model.aos_version,
                _model.lcm_version, _model.spp_version, _model.id, _model.name, _model.bmk_id
            ).filter(_model.status != "Decommissioned").all()
            _pe_schema = _schema(many=True)
            pe_list = _pe_schema.dump(_pe)

            bmk_list = ModelNtxBenchmark.query.join(
                ModelNtxBenchmarkLcmVersion, ModelNtxBenchmark.bmk_lcm_version == ModelNtxBenchmarkLcmVersion.id
            ).with_entities(ModelNtxBenchmark.id, ModelNtxBenchmarkLcmVersion).all()
            bmk_dict = {bmk[0]: bmk[1] for bmk in bmk_list}

            for pe in pe_list:
                for key in ["latest_task", "latest_task_status", "latest_task_id", 
                            "latest_task_log_path", "latest_task_creater", "latest_task_date",
                            "spp_need_upgrade", "lcm_need_upgrade", "foundation_need_upgrade", 
                            "spp_target_version", "lcm_target_version", "foundation_target_version"]:
                    pe[key] = None
                latest_task = latest_task_dict.get(pe['fqdn'])
                if latest_task:
                    pe['latest_task'] = latest_task
                    pe['latest_task_status'] = latest_task.get('status')
                    pe['latest_task_id'] = latest_task.get('id')
                    pe['latest_task_log_path'] = latest_task.get('detail_log_path')
                    pe['latest_task_creater'] = latest_task.get('creater')
                    pe['latest_task_date'] = latest_task.get('create_date')
                # version data
                if pe.get('bmk_id') and pe["bmk_id"] in bmk_dict:
                    pe['spp_need_upgrade'] = LCM.need_upgrade(pe['spp_version'], bmk_dict[pe["bmk_id"]].spp_latest_version)
                    pe['lcm_need_upgrade'] = LCM.need_upgrade(pe['lcm_version'], bmk_dict[pe["bmk_id"]].lcm_version)
                    pe['foundation_need_upgrade'] = LCM.need_upgrade(pe['foundation_version'], bmk_dict[pe["bmk_id"]].foundation_version)
                    pe['spp_target_version'] = bmk_dict[pe["bmk_id"]].spp_latest_version
                    pe['lcm_target_version'] = bmk_dict[pe["bmk_id"]].lcm_version
                    pe['foundation_target_version'] = bmk_dict[pe["bmk_id"]].foundation_version

            pe_list = sorted(pe_list, key=lambda x: x['latest_task']['id'] if x['latest_task'] and isinstance(x['latest_task'], dict) and 'id' in x['latest_task'] else 0, reverse=True)
            return Response(json.dumps(pe_list), status=200, mimetype='application/json')
        except Exception as e:
            abort(500, f"Internal error: {str(e)}")



@route('/api/v1/ntx/automation/lcm/spp/task/status')
class RestfulGetSPPTaskStatus(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_spp"})
    def post(self):
        param = request.get_json(force=True)
        res = {}
        try:
            for task_id in param:
                try:
                    _res = {}
                    if _task := ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=task_id).one():
                        _res['status'] = _task.status
                        _logs = ModelRetailNutanixAutomationSPPLCMTaskLog.query.filter_by(task_id=task_id).order_by(
                            ModelRetailNutanixAutomationSPPLCMTaskLog.id.desc()).all()

                        _schema = ModelRetailNutanixAutomationSPPLCMTaskLogSchema(many=True)
                        _res['logs'] = _schema.dump(_logs)
                        _res['latest_log'] = ModelRetailNutanixAutomationSPPLCMTaskLogSchema().dump(ModelRetailNutanixAutomationSPPLCMTaskLog.query.filter_by(
                                                id=db.session.query(
                                                    sqlalchemy.func.max(ModelRetailNutanixAutomationSPPLCMTaskLog.id)
                                                ).filter(ModelRetailNutanixAutomationSPPLCMTaskLog.task_id == task_id).scalar()
                                            ).one())
                    res[task_id] = _res
                except Exception:
                    pass
            return Response(json.dumps(res), status=200, mimetype='application/json')
        except Exception:
            abort(500, str(repr(traceback.format_exception(sys.exception()))))


@route('/api/v1/ntx/automation/lcm/log/download')
class RestfulDownloadLCMLog(Resource): #need to optimize.
    def post(self):
        try:
            token = get_request_token()
            usr = User()  #get the user name , need to log it
            res, role = usr.get_role_by_token(token)
            if res:
                _ = role.username
            else:
                raise Exception({'code': 401, 'message': 'Username not found by the token.'}) # if cann't get the username, then quit
            param = request.get_json(force=True)
            filepath = param['filepath']
            if not filepath:
                raise Exception({'code': 400, 'message': 'Cannot download the log file due to lack of file path.'})
            return send_file(filepath)
        except Exception as e:
            if e.args:
                if type(e.args[0]) == dict:
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/ntx/automation/lcm/aos/task/list/<facility_type>')
class RestfulGetAOSTaskList(MethodResource, Resource):
    @doc(description="Get AOS Upgrade list", tags=['Automation'])
    @PrivilegeValidation(privilege={"role_lcm": "view_aos"})
    def get(self, facility_type):
        page = request.args.get('page', type=int)
        limit = request.args.get('limit', type=int)
        _pe_model, _pe_schema = (ModelPrismElement, ModelPrismElementSchema()) \
            if facility_type == FacilityType.RETAIL \
            else (ModelWarehousePrismElement, ModelWarehousePrismElementSchema())
        #Handle task and log first
        _aos_task_log = ModelRetailNutanixAutomationAOSLCMTaskLog.query.all()
        _aos_task_log_schema = ModelRetailNutanixAutomationAOSLCMTaskLogSchema(many=True)
        aos_task_log = _aos_task_log_schema.dump(_aos_task_log)
        groups = groupby(sorted(aos_task_log, key=lambda x: x['task_id']), key=lambda x: x['task_id'])
        log_result = {k: list(v) for k, v in groups}
        # log_result = {'51':[log1,log2...]}
        _aos_task = ModelRetailNutanixAutomationAOSLCMTask.query.all()
        _aos_task_schema = ModelRetailNutanixAutomationAOSLCMTaskSchema(many=True)
        aos_task = _aos_task_schema.dump(_aos_task)
        for _t in aos_task:
            if _t['id'] in log_result.keys():
                _t['logs'] = log_result[_t['id']]
            else:
                _t['logs'] = []
        groups = groupby(sorted(aos_task, key=lambda x: x['pe']), key=lambda x: x['pe'])
        task_result = {k: list(v) for k, v in groups}
        latest_planned_pe = db.session.query(
            ModelNutanixSeamlessLcmPlannedPEs.pe_fqdn,
            func.max(ModelNutanixSeamlessLcmPlannedPEs.planned_date).label("latest_date")
        ).filter(ModelNutanixSeamlessLcmPlannedPEs.status == SeamlessLcmPlannedPEStatus.PLANNED
        ).group_by(ModelNutanixSeamlessLcmPlannedPEs.pe_fqdn).subquery()
        result = db.session.query(     # result:[(<ModelPrismElement 1>, None), (<ModelPrismElement 2>, None)....]
            _pe_model,
            latest_planned_pe.c.latest_date
        ).outerjoin(
            latest_planned_pe,
            _pe_model.fqdn == latest_planned_pe.c.pe_fqdn
        ).all()
        pe_list = [{
            **_pe_schema.dump(pe),
            "scheduled_execution": planned_date.strftime("%Y-%m-%d,%H:%M:%S") if planned_date else None
        } for pe, planned_date in result]
        for pe in pe_list:
            if pe['fqdn'].upper() in task_result.keys():
                #if pe has tasks
                tasks = task_result[pe['fqdn'].upper()]
                latest_task, latest_log = None, None
                for _t in tasks:
                    if not latest_task:
                        latest_task = _t
                    else:
                        if latest_task['id'] < _t['id']:
                            latest_task = _t
                if latest_task:
                    logs = latest_task['logs']
                    for _l in logs:
                        if not latest_log:
                            latest_log = _l
                        else:
                            if latest_log['id'] < _l['id']:
                                latest_log = _l
                pe['latest_task'] = latest_task
                pe['latest_log'] = latest_log
                pe['latest_task_status'] = latest_task['status']
            else:
                pe['latest_task'] = None
                pe['latest_log'] = None
                pe['latest_task_status'] = None

        total = len(pe_list)
        if page is not None and limit is not None:
            start = (page - 1) * limit
            end = start + limit
            paged_pe_list = pe_list[start:end]
            return Response(json.dumps({"data": paged_pe_list, "total": total}), status=200, mimetype='application/json')
        return Response(json.dumps(pe_list), status=200, mimetype='application/json')


@route('/api/v1/ntx/automation/lcm/aos/tasks')
class RestfulAOSLCMTasks(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_aos"})
    def get(self):
        try:
            taskinfo = ModelRetailNutanixAutomationAOSLCMTaskSchema(many=True).dump(ModelRetailNutanixAutomationAOSLCMTask.query.all())
            return jsonify(taskinfo)
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/automation/maintenance')
class RestfulMaintenance(Resource):
    def post(self):
        try:
            param = request.get_json(force=True)
            required_keys = ['pe', 'target_IP']
            if not all(key in param for key in required_keys):
                missing_keys = ', '.join(key for key in required_keys if key not in param)
                abort(400, f'Missing fields: {missing_keys}')
            elif not param['target_IP']:
                abort(400, 'Need target_IP')
            # check if PC/PE exists in DB
            if _ := ModelPrismElement.query.filter_by(name=param['pe']).one():
            # Proceed with password changing logic here...
                logging.info('pe exists, start now')
            mt = Maintenance(pe=param['pe'])
            required_keys = ['target_IP']
            mt.Ncli_host_maintenance(param['target_type'], param['target_IP'])
            mt.Acli_host_maintenance(param['target_type'], param['target_IP'])

        except KeyError:
            abort(400, 'Incorrect JSON format or missing fields.')
        except Exception:
            # Catch any other exceptions that were not anticipated
            error_info = str(repr(traceback.format_exception(sys.exception())))
            logging.error(error_info)


@route('/api/v1/ntx/automation/lcm/aos/upgrade')
class RestfulUpgradeAOS(Resource):
    @use_kwargs(AosLcmSchema(many=True), location='json', apply=False)
    @PrivilegeValidation(privilege={"role_lcm": "view_aos"})
    def post(self):
        response = []
        param = request.get_json(force=True)
        for _ in param:
            try:
                aoslcm = AosLCM(pc=_['pc'], pe=_['pe'], facility_type=_['facility_type'])
                aoslcm.run(_)
                response.append({
                    "success": True,
                    "cluster": _['pe'],
                    "message": "Task has been created."
                })
            except Exception as e:
                response.append({
                    "success": False,
                    "cluster": _['pe'],
                    "message": f"Failed to start AOS LCM task. Reason: {str(e)}"
                })
        return response
    # def post(self):
    #     try:
    #         token = get_request_token()#{'Authorization':"bearer ****-*****-*****-****"}
    #         if param := request.get_json(force=True):
    #             for _key in ['pc', 'pe', 'aos_version', 'ahv_version']:
    #                 if _key not in list(param.keys()):
    #                     raise Exception({'code':400,'message':f'Missing fields {_key}'}) #raise if pm details not given
    #         else:
    #             raise Exception({'code':400,'message':'Missing fields'}) #raise if pm details not given
    #         #now we assume all the required field are provided.
    #         #check if pc and pe exists in DB
    #         if pe:=ModelPrismElement.query.filter_by(fqdn=param['pe']).filter_by(prism=param['pc']).all():
    #             print('pe exists, proceed with the AOS upgrade.')
    #         else:
    #             abort(500,f"This PE {param['pe']} is not in our database.")
    #         if task:=ModelRetailNutanixAutomationAOSLCMTask.query.filter(
    #             ModelRetailNutanixAutomationAOSLCMTask.pe==param['pe']).filter(
    #             ModelRetailNutanixAutomationAOSLCMTask.status!='done').filter(
    #             ModelRetailNutanixAutomationAOSLCMTask.status!='error').all():
    #             abort(500,f"This PE {param['pe']} is currently upgrading.")
    #         aos = AosLCM(pc=param['pc'], pe=param['pe'], token=token,
    #                      target_aos_version=param['aos_version'], target_ahv_version=param['ahv_version'])
    #         res, mes = aos.create_task()
    #         result = {'result':res, 'message':mes}
    #         return Response(json.dumps(result),status=200,mimetype='application/json')
    #     except Exception as e:
    #         abort(500,f"Internal error, error: {str(e)}")


@route('/api/v1/automation/cluster_list_move')
class RestfulGetClusterlistMove(Resource):
    def get(self):
        try:
            #Handle task and log first
            _sli_clusters_list = ModelSLICluster.query.all()
            _sli_clusters_list_schema = ModelSLIClusterSchema(many=True)
            sli_clusters_list = _sli_clusters_list_schema.dump(_sli_clusters_list)
            groups = groupby(sorted(sli_clusters_list, key=lambda x: x['task_id']), key=lambda x: x['task_id'])
            log_result = {k: list(v) for k, v in groups}
            # log_result = {'51':[log1,log2...]}
            _spp_task = ModelRetailNutanixAutomationSPPLCMTask.query.all()
            _spp_task_schema = ModelRetailNutanixAutomationSPPLCMTaskSchema(many=True)
            spp_task = _spp_task_schema.dump(_spp_task)
            for _t in spp_task:
                if _t['id'] in log_result.keys():
                    _t['logs'] = log_result[_t['id']]
                else:
                    _t['logs'] = []
            groups = groupby(sorted(spp_task, key=lambda x: x['pe']), key=lambda x: x['pe'])
            task_result = {k: list(v) for k, v in groups}
             # _task_list = {'retcn856-nxc000.ikea.com':[task1_withlog,task2_withlog,task3_withlog].....}

            _pe = ModelPrismElement.query.all()
            _pe_schema = ModelPrismElementSchema(many=True)
            pe_list = _pe_schema.dump(_pe)
            for pe in pe_list:
                if pe['fqdn'] in task_result.keys():
                    #if pe has tasks
                    tasks = task_result[pe['fqdn']]
                    latest_task, latest_log = None, None
                    for _t in tasks:
                        if not latest_task:
                            latest_task = _t
                        else:
                            if latest_task['id'] < _t['id']:
                                latest_task = _t
                    if latest_task:
                        logs = latest_task['logs']
                        for _l in logs:
                            if not latest_log:
                                latest_log = _l
                            else:
                                if latest_log['id'] < _l['id']:
                                    latest_log = _l
                    pe['tasks'] = tasks
                    pe['latest_task'] = latest_task
                    pe['latest_log'] = latest_log
                    pe['latest_task_status'] = latest_task['status']
                else:
                    pe['tasks'] = []
                    pe['latest_task'] = None
                    pe['latest_log'] = None
                    pe['latest_task_status'] = None
            # pe_list = [{'fqdn':'pe_name','tasks':[]}]
            return Response(json.dumps(pe_list), status=200, mimetype='application/json')
        except:
            abort(500, "Internal error")


# NOT IN USE
@route('/api/v1/automation/dsc_pe_availability')
class RestfulDesiredStateConfigPeAvailability(Resource):
    def get(self):
        param = request.get_json(force=True)
        dsc = DscPeAvailable(param['pe'])
        dsc.set_pe_availability()
        return {
            "message": "Task has been created."
        }


@route('/api/v1/ntx/automation/auto_maintenance')
class RestfulAutoMaintenance(Resource):
    @use_kwargs(AutoMaintenanceRequestSchema, location='json', apply=False)
    @PrivilegeValidation(privilege={"role_lcm": "view_atm"})
    def post(self):
        """
        Fire a job for a single cluster immediately & once (won't change the previous schedule setting)
        """
        param = request.get_json(force=True)
        existing_job = auto_maintenance_scheduler.get_job(param['pe'])
        if existing_job and existing_job.next_run_time < datetime.now(get_localzone()) + timedelta(hours=2):
            return "The next run time is in 2 hours, please wait for the next schedule.", 400
        AutoMaintenance(**param).trigger_atm_task(init_app=False, use_sa=False)

    @PrivilegeValidation(privilege={"role_lcm": "view_atm"})
    def get(self):
        return Response(json.dumps(AutoMaintenance.list_tasks()), status=200, mimetype='application/json')


@route('/api/v1/ntx/automation/auto_maintenance_jobs')
class RestfulAutoMaintenanceJobs(Resource):
    def post(self):
        # TODO: Need improve, now it takes 1min 11s
        """Register schedule jobs for all clusters"""
        auto_maintenance_scheduler.remove_all_jobs('job_store_atm')
        pe_list = ModelPrismElement.query.all() + ModelWarehousePrismElement.query.all()
        schedules = {}
        qr = db.session.query(
            ModelNtxBenchmark.id,
            ModelNtxBenchmark.facility,
            ModelNtxBenchmarkScheduler.id,
            ModelNtxBenchmarkScheduler.atm_schedule_json,
            ModelNtxBenchmarkAutoMaintenanceStep.steps_json,
        ).filter(ModelNtxBenchmarkScheduler.id == ModelNtxBenchmark.bmk_scheduler).filter(ModelNtxBenchmarkAutoMaintenanceStep.id == ModelNtxBenchmark.bmk_atm_step).all()
        for q in qr:
            bmk_id = q[0]
            facility = q[1]
            schedule_id = q[2]
            atm_schedule = json.loads(q[3])
            atm_steps = json.loads(q[4])
            if schedule_id not in schedules:
                schedules[schedule_id] = {
                    "schedule_setting": atm_schedule,
                    "facility_type": facility,
                    "pe_list": [],
                    "atm_steps": atm_steps            # TODO: use new bmk to decide if to run pwd/cert/dsc instead of name
                }
            for pe in pe_list:
                if pe.bmk_id == bmk_id:
                    schedules[schedule_id]["pe_list"].append(pe.fqdn)
        response = []
        for _schedule_id, s in schedules.items():
            schedule = s["schedule_setting"]
            day_list = schedule["days"].split(',')
            day = day_list[0]
            hour = schedule["hour"]
            minute = schedule["minute"]
            pes = s["pe_list"]
            steps = s["atm_steps"]
            for pe_fqdn in pes:
                param = {
                    "pe": pe_fqdn,
                    "facility_type": s["facility_type"],
                    "steps": {
                        "rotate_password": steps["rotate_password"],
                        "renew_certificate": steps["renew_certificate"],
                        "desired_state_config": steps["desired_state_config"],
                    }
                }
                auto_maintenance_scheduler.add_job(
                    jobstore='job_store_atm',
                    id=pe_fqdn,
                    func=AutoMaintenance(**param).trigger_atm_task,
                    kwargs={"init_app": True, "use_sa": True},
                    trigger='cron',
                    replace_existing=True,
                    day_of_week=day, hour=hour, minute=minute,
                    timezone=schedule["time_zone"]
                )
                added_job = auto_maintenance_scheduler.get_job(jobstore='job_store_atm', job_id=pe_fqdn)
                response.append({
                    "id": added_job.id,
                    "next_run_time": str(added_job.next_run_time),
                    "timezone": str(added_job.next_run_time.tzinfo),
                    "next_run_time_in_shanghai": str(added_job.next_run_time.astimezone(timezone('Asia/Shanghai')))
                })
                minute += schedule["interval_min"]
                if minute >= 60:
                    minute = 0
                    hour += 1
                if hour >= 24:
                    hour = 0
                    day = day_list[1]
        return response

    def get(self):
        response = []
        jobs = auto_maintenance_scheduler.get_jobs(jobstore='job_store_atm')
        for job in jobs:
            response.append({
                "id":                             job.id,
                "next_run_time":                  str(job.next_run_time),
                "timezone":                       str(job.next_run_time.tzinfo),
                "next_run_time_in_shanghai":      str(job.next_run_time.astimezone(timezone('Asia/Shanghai'))),
            })
        return response


@route('/api/v1/ntx/automation/auto_maintenance_log')
class RestfulAutoMaintenanceLog(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_atm"})
    @use_kwargs(AutoMaintenanceLogRequestSchema, location='json', apply=False)
    def post(self):
        try:
            param = request.get_json(force=True)
            return AutoMaintenance.get_brief_log(param['atm_type'], param['id']), 200
        except:
            error = str(repr(traceback.format_exception(sys.exception())))
            return error, 500


@route(['/api/v1/ntx/automation/auto_maintenance_lock', '/api/v1/ntx/automation/auto_maintenance_lock/<id>'])
class RestfulAutoMaintenanceLock(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_atm"})
    @use_kwargs(AutoMaintenanceLockRequestSchema, location='json', apply=False)
    def post(self):
        try:
            param = request.get_json(force=True)
            creater = get_user_by_token().username
            kwargs = {
                "pe_id"      : param['pe_id'],
                "start_time" : param['start_time'],
                "end_time"   : param['end_time'],
                "creater"    : creater,
                "description": param['description'],
            }
            AutoMaintenance.add_lock(**kwargs)
            return True, 200
        except sqlalchemy.exc.NoResultFound:
            raise flaskex.BadRequest(f"Can't find PE id {param['pe_id']} in unit portal database!")
        except sqlalchemy.exc.MultipleResultsFound:
            raise flaskex.BadRequest(f"Found multiple pe with id {param['pe_id']} in unit portal database!")

    @PrivilegeValidation(privilege={"role_lcm": "view_atm"})
    def get(self, id):
        #when user use get method, id is pe id
        try:
            return Response(json.dumps(AutoMaintenance.get_lock(id)), status=200, mimetype='application/json')
        except sqlalchemy.exc.NoResultFound:
            raise flaskex.BadRequest(f"Can't find PE id {id} in unit portal database!")
        except sqlalchemy.exc.MultipleResultsFound:
            raise flaskex.BadRequest(f"Found multiple pe with id {id} in unit portal database!")

    @PrivilegeValidation(privilege={"role_lcm": "view_atm"})
    def delete(self, id):
        #when user use delete method, id is auto maintenance id
        try:
            AutoMaintenance.remove_lock(id)
            return "Done", 200
        except sqlalchemy.exc.NoResultFound:
            raise flaskex.BadRequest(f"Can't find auto mainenance record id {id} in unit portal database!")
        except sqlalchemy.exc.MultipleResultsFound:
            raise flaskex.BadRequest(f"Found auto mainenance record with id {id} in unit portal database!")


@route('/api/v1/ntx/automation/auto_maintenance_history')
class RestfulAutoMaintenanceHistory(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_atm"})
    def post(self):
        try:
            param = request.get_json(force=True)
            return AutoMaintenance.get_atm_history_tasks(param["pe"]), 200
        except:
            return str(repr(traceback.format_exception(sys.exception()))), 500


@route('/api/v1/ntx/automation/auto_maintenance/abort')
class RestfulAutoMaintenanceAbort(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_atm"})
    @use_kwargs(AutoMaintenanceAbortSchema, location='json', apply=False)
    def post(self):
        try:
            aborter = get_user_by_token().username
            param = request.get_json(force=True)
            if param['type'] != "desired_state_config":
                return "only DSC can be aborted", 400
            return AutoMaintenance.abort_atm_task(task_type=param['type'], task_id=param['task_id'], user=aborter), 200
        except sqlalchemy.exc.NoResultFound:
            raise flaskex.BadRequest(f"Can't find task id {param['task_id']} in unit portal database!")
        except sqlalchemy.exc.MultipleResultsFound:
            raise flaskex.BadRequest(f"Found multiple task with id {param['task_id']} in unit portal database!")


@route('/api/v1/ntx/automation/disablevi')
class RestfulDisableHardwareVirtualization(Resource):
    def put(self):
        data = request.get_json(force=True)
        vmname = data['vmname']
        Automation.disable_hardware_virtualization(vmname)


@route('/api/v1/ntx/automation/new_cluster/<facility_type>')
class RestfulNewCluster(MethodResource, Resource):
    @use_kwargs(CreateClusterSchema(), location='json')
    def post(self, facility_type, *args, **kwargs):    # pylint: disable=W0613
        payload = request.get_json(force=True)
        try:
            task_id = CreateClusterTask(payload, facility_type).run()
            return {
                ClusterSpec.PE_NAME: payload[ClusterSpec.PE_NAME],
                "success": True,
                "message": "Initialize task succeeded.",
                "task_id": task_id
            }
        except flaskex.HTTPException as e:
            return {
                ClusterSpec.PE_NAME: payload[ClusterSpec.PE_NAME],
                "success": False,
                "message": f"Failed to initialize task. Reason: {str(e)}"
            }, e.code
        except Exception as e:
            return {
                ClusterSpec.PE_NAME: payload[ClusterSpec.PE_NAME],
                "success": False,
                "message": f"Failed to initialize task. Reason: {str(e)}"
            }, 500

    def get(self, facility_type):
        match facility_type:
            case FacilityType.RETAIL:
                # retail doesn't need much handling
                return ModelClusterTaskSchema(many=True).dump(ModelClusterTask.query.all())
            case FacilityType.WAREHOUSE:
                result = list()
                metro_tasks = ModelWhClusterTask.query.filter_by(is_metro_task=1).all()
                for _model_task in metro_tasks:
                    task = ModelWhClusterTaskSchema().dump(_model_task)
                    task["children"] = []
                    room_a = ModelWhClusterTaskSchema().dump(ModelWhClusterTask.query.filter_by(id=_model_task.room_a_task_id).first())
                    room_b = ModelWhClusterTaskSchema().dump(ModelWhClusterTask.query.filter_by(id=_model_task.room_b_task_id).first())
                    if room_a:
                        task["children"].append(room_a)
                    if room_b:
                        task["children"].append(room_b)

                    result.append(task)
                return result

    @use_kwargs(DeleteClusterSchema(), location='json')
    def delete(self, facility_type, *args, **kwargs):    # pylint: disable=W0613
        payload = request.get_json(force=True)
        try:
            DeleteClusterTask(payload, facility_type).run()
            return {
                "success": True,
                "message": "Initialize task succeeded.",
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to initialize task. Reason: {str(e)}"
            }


@route('/api/v1/ntx/automation/new_cluster_task/<facility_type>/<task_id>')
class RestfulNewClusterTaskDetail(Resource):
    def get(self, facility_type, task_id):
        if facility_type == "warehouse":
            metro_task = ModelWhClusterTaskSchema().dump(ModelWhClusterTask.query.filter_by(id=task_id).one())
            if metro_task.get("room_a_task_id"):
                metro_task["room_a"] = ModelWhClusterTaskSchema().dump(ModelWhClusterTask.query.filter_by(id=metro_task["room_a_task_id"]).one())
            if metro_task.get("room_b_task_id"):
                metro_task["room_b"] = ModelWhClusterTaskSchema().dump(ModelWhClusterTask.query.filter_by(id=metro_task["room_b_task_id"]).one())
            return metro_task
        return ModelClusterTaskSchema().dump(ModelClusterTask.query.filter_by(id=task_id).one())


@route('/api/v1/ntx/automation/new_cluster/log/<facility_type>/<task_id>')
class RestfulNewClusterTaskLog(Resource):
    def get(self, facility_type, task_id):
        try:
            if facility_type == "warehouse":
                log_path = ModelWhClusterTask.query.filter_by(id=task_id).one().detail_log_path
            else:
                log_path = ModelClusterTask.query.filter_by(id=task_id).one().detail_log_path
            res = list()
            with open(log_path) as f:
                logs = f.readlines()
                for log in logs:
                    severity = re.findall("\s(INFO|WARNING|ERROR)\s", log, re.I)
                    # severity is like ['INFO']/['WARNING']
                    res.append({
                        "severity" : severity[0] if severity else "INFO",
                        "message"  : log
                    })
            return res
        except Exception as e:
            abort(500, str(e))


@route(['/api/v1/ntx/automation/lcm/seamless_lcm/plans', '/api/v1/ntx/automation/lcm/seamless_lcm/plans/<plan_id>'])
class RestfulSeamlessLcmPlans(MethodResource, Resource):
    @PrivilegeValidation(privilege={"role_lcm": "manage_slcm"})
    @use_kwargs(CreateSeamlessLcmPlanSchema(), location='json')
    def post(self, *args, **kwargs):    # pylint: disable=W0613
        try:
            payload = request.get_json(force=True)
            response = SeamlessLcm().create_plan(payload)
            return response
        except SeamlessLcmClusterConflict as e:
            return {
                'conflict_clusters': e.conflict_clusters,
                'msg': e.msg
            }, e.code

    @PrivilegeValidation(privilege={"role_lcm": "manage_slcm"})
    @use_args(CancelSeamlessLcmPlanSchema(), location='view_args')
    def delete(self, args, **kwargs):    # pylint: disable=W0613
        """Cancel lcm plan by id"""
        return SeamlessLcm().cancel_plan(args.get("plan_id"))

    @PrivilegeValidation(privilege={"role_lcm": "view_slcm"})
    def get(self):
        return jsonify(SeamlessLcm.list_plans())

    @PrivilegeValidation(privilege={"role_lcm": "manage_slcm"})
    @use_kwargs(CreateSeamlessLcmPlanSchema(), location='json')     # same as CreateSeamlessLcmPlanSchema
    @use_args(UpdateSeamlessLcmPlanRouteSchema(), location='view_args')
    def put(self, args, **kwargs):    # pylint: disable=W0613
        """Update lcm plan"""
        payload = request.get_json(force=True)
        plan_id = args.get(SeamlessLcmSpec.PLAN_ID)
        try:
            return SeamlessLcmPlan().update_plan(int(plan_id), payload)
        except SeamlessLcmClusterConflict as e:
            return {
                'conflict_clusters': e.conflict_clusters,
                'msg': e.msg
            }, e.code


@route('/api/v1/ntx/automation/lcm/seamless_lcm/planned_pe')
class RestfulSeamlessLcmPlannedPE(MethodResource, Resource):
    @PrivilegeValidation(privilege={"role_lcm": "manage_slcm"})
    @use_kwargs(UpdateSeamlessLcmPlannedPeSchema(), location='json')
    def put(self, *args, **kwargs):    # pylint: disable=W0613
        """Update single planned pe"""
        payload = request.get_json(force=True)
        return SeamlessLcmPlannedPe().update_planned_pe(payload)

    @PrivilegeValidation(privilege={"role_lcm": "manage_slcm"})
    @use_kwargs(CancelSeamlessLcmPlannedPeSchema(), location='json')
    def delete(self, *args, **kwargs):    # pylint: disable=W0613
        """Delete single planned pe"""
        payload = request.get_json(force=True)
        return SeamlessLcmPlannedPe().cancel_planned_pe(payload)

    @PrivilegeValidation(privilege={"role_lcm": "view_slcm"})
    @use_kwargs({'download': fields.Boolean()}, location="query")
    def get(self, *args, **kwargs):    # pylint: disable=W0613
        # List all planned pes
        download = kwargs.get('download', False)
        res = SeamlessLcmPlannedPe().list_planned_pes()
        if not download:
            return res
        file_path = FileDownloader(data=res).download()
        logging.info(f"Downloading file {file_path}...")

        def gene():
            with open(file_path) as f:
                yield from f
            os.remove(file_path)

        res = current_app.response_class(gene(), mimetype='text/csv')
        res.headers.set('Content-Disposition', 'attachment', filename='planned_pes.csv')
        return res


@route('/api/v1/ntx/automation/lcm/seamless_lcm/target_version')
class RestfulSeamlessLcmTargetVersion(Resource):
    def get(self):
        schema = ModelNtxBenchmarkLcmVersionSchema(many=True)
        return schema.dump(ModelNtxBenchmarkLcmVersion.query.all())


@route('/api/v1/ntx/automation/metro_vg')
class RestfulMetroVG(MethodResource, Resource):
    @doc(description="Create Metro Volume Group", tags=["Automation"])
    @use_kwargs(CreateMetroVGSchema, location="json")
    def post(self, *args, **kwargs):  # pylint: disable=W0613
        payload = request.get_json(force=True)
        WiaBCreateMetroVolumeGroup(payload).run()


@route('/api/v1/ntx/automation/metro_list_vgs')
class RestfulMetroListVGs(MethodResource, Resource):
    @doc(description="List Metro Volume Group", tags=["Automation"])
    @use_kwargs(ListMetroVGSchema, location="json")
    def post(self, *args, **kwargs):  # pylint: disable=W0613
        payload = request.get_json(force=True)
        return WiabMetroListVGs(payload).run()


@route('/api/v1/ntx/automation/metro_add_disk')
class RestfulMetroAddDisk(MethodResource, Resource):
    @doc(description="Add Disk to Metro Volume Group", tags=["Automation"])
    @use_kwargs(AddMetroVGDiskSchema, location="json")
    def post(self, *args, **kwargs):  # pylint: disable=W0613
        payload = request.get_json(force=True)
        WiabMetroAddDisk(payload).run()


@route('/api/v1/ntx/automation/metro_add_iscsi_client')
class RestfulMetroAddIscsiClient(MethodResource, Resource):
    @doc(description="Add Iscsi Client to Metro Volume Group", tags=["Automation"])
    @use_kwargs(AddMetroIscsiClientSchema, location="json")
    def post(self, *args, **kwargs):  # pylint: disable=W0613
        payload = request.get_json(force=True)
        WiabMetroAddIscsiClient(payload).run()


@route('/api/v1/ntx/automation/new_cluster/abort/<facility_type>/<task_id>')
class RestfulAbortClusterSubTask(Resource):
    @PrivilegeValidation(privilege={"role_lcm": "view_atm"})
    def post(self, facility_type, task_id):
        try:
            payload = request.get_json(force=True) if request.content_length else {}
            abort_metro_task = payload.get('abortMetroTask', False)
            
            aborter = get_user_by_token().username
            logging.info(f"[ABORT] Starting abort process for task {task_id} by {aborter} (abort_metro_task: {abort_metro_task})")
            
            try:
                if facility_type == FacilityType.WAREHOUSE:
                    task = ModelWhClusterTask.query.filter_by(id=task_id).one()
                    task_schema = ModelWhClusterTaskSchema() 
                else:
                    task = ModelClusterTask.query.filter_by(id=task_id).one()
                    task_schema = ModelClusterTaskSchema()
            except sqlalchemy.exc.NoResultFound:
                return {"success": False, "message": f"Task {task_id} not found"}, 404

            task_data = task_schema.dump(task)
            cluster_task = CreateClusterTask(
                task_data,
                facility_type=facility_type
            )
            cluster_task.task = task
            cluster_task.setup_loggers()
            success, message = cluster_task.abort_task(aborter, abort_metro_task)
            return {"success": success, "message": message}, 200 if success else 500
                
        except Exception as e:
            error_msg = f"Failed to abort task: {str(e)}"
            logging.error(f"[ABORT] Error in RestfulAbortClusterSubTask: {error_msg}")
            logging.exception(e) 
            return {"success": False, "message": error_msg}, 500

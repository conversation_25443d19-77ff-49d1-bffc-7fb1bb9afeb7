2025-08-01 11:19:50,296 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 11:19:50,296 INFO params: None
2025-08-01 11:19:50,296 INFO User: <EMAIL>
2025-08-01 11:19:50,296 INFO payload: None
2025-08-01 11:19:50,296 INFO files: None
2025-08-01 11:19:50,296 INFO timeout: 30
2025-08-01 11:19:51,594 INFO Getting host list from RETSEELM-NXC000.
2025-08-01 11:19:51,594 INFO Got the host list from RETSEELM-NXC000.
2025-08-01 11:19:51,611 INFO Got the host list.
2025-08-01 11:19:51,611 INFO Getting vault from IKEAD2.
2025-08-01 11:19:52,140 INFO Getting Site_Pe_Nutanix.
2025-08-01 11:19:52,615 INFO Got Site_Pe_Nutanix.
2025-08-01 11:19:52,615 INFO Getting Site_Pe_Admin.
2025-08-01 11:19:53,086 INFO Got Site_Pe_Admin.
2025-08-01 11:19:53,086 INFO Getting Site_Oob.
2025-08-01 11:19:53,638 INFO Got Site_Oob.
2025-08-01 11:19:53,638 INFO Getting Site_Ahv_Nutanix.
2025-08-01 11:19:54,163 INFO Got Site_Ahv_Nutanix.
2025-08-01 11:19:54,163 INFO Getting Site_Ahv_Root.
2025-08-01 11:19:54,674 INFO Got Site_Ahv_Root.
2025-08-01 11:19:54,674 INFO Getting Site_Gw_Priv_Key.
2025-08-01 11:19:55,128 INFO Got Site_Gw_Priv_Key.
2025-08-01 11:19:55,128 INFO Getting Site_Gw_Pub_Key.
2025-08-01 11:19:55,597 INFO Got Site_Gw_Pub_Key.
2025-08-01 11:19:55,597 INFO Getting Site_Pe_Svc.
2025-08-01 11:19:56,077 INFO Got Site_Pe_Svc.
2025-08-01 11:20:01,187 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 11:20:01,187 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-01 11:20:01,187 INFO params: None
2025-08-01 11:20:01,187 INFO User: <EMAIL>
2025-08-01 11:20:01,187 INFO payload: None
2025-08-01 11:20:01,187 INFO files: None
2025-08-01 11:20:01,187 INFO timeout: 30
2025-08-01 11:20:03,035 INFO Got the VM list from RETSEELM-NXC000.
2025-08-01 11:20:18,602 INFO ****************************************************************************************************
2025-08-01 11:20:18,602 INFO *                                                                                                  *
2025-08-01 11:20:18,602 INFO *                                    Shutting down the cluster.                                    *
2025-08-01 11:20:18,602 INFO *                                                                                                  *
2025-08-01 11:20:18,602 INFO ****************************************************************************************************
2025-08-01 11:20:20,834 INFO SSH connecting to the CVM.
2025-08-01 11:20:21,228 INFO SSH connecting to ***********, this is the '1' try.
2025-08-01 11:20:23,789 INFO SSH connected to ***********.
2025-08-01 11:20:26,359 INFO Sending 'cluster stop' to the server.
2025-08-01 11:20:31,957 INFO Receiving the output .
2025-08-01 11:20:31,959 INFO Received the output: #SSH OUTPUT START#.
2025-08-01 11:20:31,959 INFO 

Nutanix Controller VM (CVM) is a virtual storage appliance.



Alteration of the CVM (unless advised by Nutanix Technical Support or

Support Portal Documentation) is unsupported and may result in loss

of User VMs or other data residing on the cluster.



Unsupported alterations may include (but are not limited to):



- Configuration changes / removal of files.

- Installation of third-party software/scripts not approved by Nutanix.

- Installation or upgrade of software packages from non-Nutanix

  sources (using yum, rpm, or similar).



** Notice: SSH will no longer be available in upcoming releases.      **  

** Nutanix Support may access the bash shell on an exceptional basis. **

Last login: Fri Aug  1 05:19:27 CEST 2025 from *********** on ssh

Last login: Fri Aug  1 05:20:23 2025 from **************


[?1h=[H[J[m[?1003l[?1006l[?2004l[1;1H[1;24r[1;1H[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[7m[60] 0:bash*                             "ntnx-cz20240j8s-a-cvm" 05:20 01-Aug-25[m[1;1H[m[?1003l[?1006l[?2004l[1;1H[1;24r[1;1H[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[7m[60] 0:bash*                             "ntnx-cz20240j8s-a-cvm" 05:20 01-Aug-25[m[1;1H

Nutanix Controller VM (CVM) is a virtual storage appliance.[4;1HAlteration of the CVM (unless advised by Nutanix Technical Support or

Support Portal Documentation) is unsupported and may result in loss

of User VMs or other data residing on the cluster.[8;1HUnsupported alterations may include (but are not limited to):[10;1H- Configuration changes / removal of files.

- Installation of third-party software/scripts not approved by Nutanix.

- Installation or upgrade of software packages from non-Nutanix

  sources (using yum, rpm, or similar).[15;1H** Notice: SSH will no longer be available in upcoming releases.      **  

** Nutanix Support may access the bash shell on an exceptional basis. **[18;1HYou are running inside a tmux session[20;1HOpen sessions:

[1;23r[23;80H




[16;1H1: 1 windows (created Tue Jul 29 11:37:09 2025) [80x23]

10: 1 windows (created Wed Jul 30 03:56:47 2025) [80x23]

11: 1 windows (created Wed Jul 30 03:57:20 2025) [80x23]

12: 1 windows (created Wed Jul 30 04:03:44 2025) [80x23]

13: 1 windows (created Wed Jul 30 04:14:31 2025) [80x23]

14: 1 windows (created Wed Jul 30 04:19:31 2025) [144x50]

15: 1 windows (created Wed Jul 30 04:26:02 2025) [80x23][1;24r[23;1H[1;23r[23;80H






[16;1H16: 1 windows (created Wed Jul 30 04:33:12 2025) [80x23]

17: 1 windows (created Wed Jul 30 04:33:45 2025) [80x23]

18: 1 windows (created Wed Jul 30 04:43:47 2025) [80x23]

19: 1 windows (created Wed Jul 30 04:44:20 2025) [80x23]

2: 1 windows (created Tue Jul 29 11:37:41 2025) [80x23]

20: 1 windows (created Wed Jul 30 04:47:54 2025) [80x23]

21: 1 windows (created Wed Jul 30 04:51:56 2025) [80x23][1;24r[23;1H[1;23r[23;80H











[11;1H22: 1 windows (created Wed Jul 30 04:52:30 2025) [80x23]

23: 1 windows (created Wed Jul 30 04:56:44 2025) [80x23]

24: 1 windows (created Wed Jul 30 04:57:46 2025) [144x50]

25: 1 windows (created Wed Jul 30 05:01:36 2025) [80x23]

26: 1 windows (created Wed Jul 30 05:02:09 2025) [80x23]

27: 1 windows (created Wed Jul 30 05:07:41 2025) [80x23]

28: 1 windows (created Wed Jul 30 05:09:45 2025) [144x50]

29: 1 windows (created Wed Jul 30 05:19:40 2025) [80x23]

3: 1 windows (created Tue Jul 29 11:43:47 2025) [209x49]

30: 1 windows (created Wed Jul 30 05:20:13 2025) [80x23]

31: 1 windows (created Wed Jul 30 05:24:43 2025) [80x23]

32: 1 windows (created Wed Jul 30 05:29:23 2025) [144x50][1;24r[23;1H[1;23r[23;80H




[18;1H33: 1 windows (created Wed Jul 30 05:31:59 2025) [80x23]

34: 1 windows (created Wed Jul 30 05:32:38 2025) [80x23]

35: 1 windows (created Wed Jul 30 05:44:46 2025) [80x23]

36: 1 windows (created Wed Jul 30 05:46:10 2025) [80x23]

37: 1 windows (created Wed Jul 30 05:48:20 2025) [80x23]

38: 1 windows (created Wed Jul 30 05:50:09 2025) [80x23][1;24r[23;57H[1;23r[23;80H




[19;1H39: 1 windows (created Wed Jul 30 05:56:14 2025) [80x23]

4: 1 windows (created Tue Jul 29 11:44:30 2025) [80x23]

40: 1 windows (created Wed Jul 30 05:57:16 2025) [80x23]

41: 1 windows (created Wed Jul 30 06:05:29 2025) [80x23][1;24r[23;1H[1;23r[23;80H



[19;1H42: 1 windows (created Wed Jul 30 06:06:36 2025) [80x23]

43: 1 windows (created Wed Jul 30 06:09:15 2025) [80x23]

44: 1 windows (created Wed Jul 30 07:05:27 2025) [80x23]

45: 1 windows (created Wed Jul 30 07:06:34 2025) [80x23][1;24r[23;1H[1;23r[23;80H






[16;1H46: 1 windows (created Wed Jul 30 07:09:38 2025) [80x23]

47: 1 windows (created Wed Jul 30 07:10:20 2025) [80x23]

48: 1 windows (created Wed Jul 30 07:14:45 2025) [80x23]

49: 1 windows (created Wed Jul 30 07:15:52 2025) [80x23]

5: 1 windows (created Tue Jul 29 11:48:42 2025) [80x23]

50: 1 windows (created Wed Jul 30 07:17:49 2025) [80x23]

51: 1 windows (created Wed Jul 30 07:19:14 2025) [80x23][1;24r[23;1H[1;23r[23;80H





[17;1H52: 1 windows (created Wed Jul 30 07:20:25 2025) [80x23]

53: 1 windows (created Wed Jul 30 07:23:29 2025) [80x23]

54: 1 windows (created Thu Jul 31 04:07:05 2025) [80x23]

55: 1 windows (created Thu Jul 31 04:07:38 2025) [80x23]

56: 1 windows (created Thu Jul 31 04:10:46 2025) [80x23]

57: 1 windows (created Thu Jul 31 04:11:40 2025) [80x23][1;24r[23;1H[1;23r[23;80H



[19;1H58: 1 windows (created Thu Jul 31 04:13:06 2025) [80x23]

59: 1 windows (created Fri Aug  1 05:15:10 2025) [80x23]

6: 1 windows (created Tue Jul 29 11:50:39 2025) [80x23]

60: 1 windows (created Fri Aug  1 05:20:26 2025) [80x23] (attached)[1;24r[23;1H[1;23r[23;80H


[20;1H7: 1 windows (created Tue Jul 29 20:06:16 2025) [80x23]

8: 1 windows (created Tue Jul 29 20:06:19 2025) [80x23]

9: 1 windows (created Tue Jul 29 20:08:40 2025) [80x23][1;24r[23;1H[1;23r[23;80H






[17;1HTo attach to other sessions, run 'tmux switchc -t num' where num is the session number

To list existing sessions, run 'tmux ls'

To terminate sessions, run 'tmux kill-session -t num' where num is the session number

Refer to tmux documentation for more information and commands.[1;24r[23;1Hnutanix@NTNX-CZ20240J8S-A-CVM:***********:~$ [1;23r[23;80H
[22;46Hcluster stop[1;24r[23;1H[1;23r[23;80H


[20;1H2025-08-01 03:20:30,722Z INFO MainThread zookeeper_session.py:272 cluster is attempting to connect to Zookeeper (unestablished session (object 0x7f1044a47d60)), host port list zk3:9876,zk1:9876,zk2:9876[1;24r[24;1H[7m[60] 0:python3.9*                        "ntnx-cz20240j8s-a-cvm" 05:20 01-Aug-25[m[23;1H[1;23r[23;80H


[20;1H2025-08-01 03:20:30,722Z INFO MainThread patterns.py:63 Creating a new instance for ZookeeperSession[('client_id', None), ('connection_timeout', None), ('host_port_list', 'zk3:9876,zk1:9876,zk2:9876'), ('use_zk_mt', None)][1;24r[23;1H[1;23r[23;80H


[20;1H2025-08-01 03:20:30,724Z INFO Dummy-1 zookeeper_session.py:933 ZK session establishment complete, session 0x19840c579d26468 (object 0x7f1044a47d60), negotiated timeout=20 secs[1;24r[23;1H[1;23r[23;80H



[19;1H2025-08-01 03:20:30,739Z INFO MainThread cluster:3607 Executing action stop on SVMs ***********,***********,***********

2025-08-01 03:20:30,739Z WARNING MainThread genesis_utils.py:406 Deprecated: use util.cluster.info.get_node_uuid() instead[1;24r[23;1H[1;23r[23;80H

[21;1H2025-08-01 03:20:30,739Z INFO MainThread patterns.py:65 Retrieved an existing instance for ZookeeperSession[1;24r[23;1H[1;23r[23;80H




[18;1H2025-08-01 03:20:30,752Z INFO MainThread cluster:3654 [20;1H***** CLUSTER NAME *****

RETSEELM-NXC000[1;24r[23;1H[1;23r[23;80H
[22;1HThis operation will stop the Nutanix storage services and any VMs using Nutanix storage will become unavailable. Do you want to proceed? (I agree/[N]): [1;24r[23;73H
2025-08-01 11:20:31,960 INFO #SSH OUTPUT END#

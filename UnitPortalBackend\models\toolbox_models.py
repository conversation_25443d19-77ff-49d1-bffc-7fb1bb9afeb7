from models.base_task_models import ModelBaseTask, ModelBaseTaskLog
from models.database import db, ma


class ModelToolboxTask(ModelBaseTask):
    __tablename__       = 'dh_toolbox_task'
    pe_name             = db.Column(db.String(100))
    task_type           = db.Column(db.String(50))



class ModelToolboxTaskSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelToolboxTask
        include_relationships = True
        load_instance = True


class ModelToolboxTaskLog(ModelBaseTaskLog):
    __tablename__       = 'dh_toolbox_task_log'
    task_type           = db.Column(db.String(50))


class ModelToolboxTaskLogSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelToolboxTaskLog
        include_relationships = True
        load_instance = True





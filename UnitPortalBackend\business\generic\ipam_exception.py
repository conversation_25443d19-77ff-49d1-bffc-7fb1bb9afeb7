from business.generic.base_up_exception import BaseUpException


class IpamException(BaseUpException):
    def __init__(self, msg):
        super().__init__(msg)


class ErrorFindingAddress(IpamException):
    def __init__(self, ip_or_name):
        super().__init__(f"Can't find '{ip_or_name}' on IPAM!")


class FailedToAssignIp(IpamException):
    def __init__(self, ip, fqdn):
        super().__init__(f"Failed to assign IP {ip} to {fqdn}!")


class IpNotUnderVlan(IpamException):
    def __init__(self, ip, vlan_id):
        super().__init__(f"IP {ip} is not under VLAN {vlan_id}!")


class IpOccupied(IpamException):
    def __init__(self, ip, fqdn):
        super().__init__(f"IP {ip} is occupied by {fqdn}")


class NoIpAvailableUnderSubnet(IpamException):
    def __init__(self, subnet):
        super().__init__(f"No IP is available in the subnet {subnet}!")


class VlanMissingUnderSubnet(IpamException):
    def __init__(self, vlan_id, subnet_name):
        super().__init__(f"Can't find vlan '{vlan_id}' under network {subnet_name}!")


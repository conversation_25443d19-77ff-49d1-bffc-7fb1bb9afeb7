2025-08-01 11:49:44,614 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 11:49:44,614 INFO params: None
2025-08-01 11:49:44,614 INFO User: <EMAIL>
2025-08-01 11:49:44,614 INFO payload: None
2025-08-01 11:49:44,614 INFO files: None
2025-08-01 11:49:44,614 INFO timeout: 30
2025-08-01 11:49:46,932 INFO Getting host list from RETSEELM-NXC000.
2025-08-01 11:49:46,932 INFO Got the host list from RETSEELM-NXC000.
2025-08-01 11:49:46,943 INFO Got the host list.
2025-08-01 11:49:46,943 INFO Getting vault from IKEAD2.
2025-08-01 11:49:47,490 INFO Getting Site_Pe_Nutanix.
2025-08-01 11:49:47,992 INFO Got Site_Pe_Nutanix.
2025-08-01 11:49:47,992 INFO Getting Site_Pe_Admin.
2025-08-01 11:49:48,512 INFO Got Site_Pe_Admin.
2025-08-01 11:49:48,512 INFO Getting Site_Oob.
2025-08-01 11:49:49,033 INFO Got Site_Oob.
2025-08-01 11:49:49,033 INFO Getting Site_Ahv_Nutanix.
2025-08-01 11:49:49,511 INFO Got Site_Ahv_Nutanix.
2025-08-01 11:49:49,511 INFO Getting Site_Ahv_Root.
2025-08-01 11:49:49,967 INFO Got Site_Ahv_Root.
2025-08-01 11:49:49,967 INFO Getting Site_Gw_Priv_Key.
2025-08-01 11:49:50,425 INFO Got Site_Gw_Priv_Key.
2025-08-01 11:49:50,425 INFO Getting Site_Gw_Pub_Key.
2025-08-01 11:49:50,947 INFO Got Site_Gw_Pub_Key.
2025-08-01 11:49:50,947 INFO Getting Site_Pe_Svc.
2025-08-01 11:49:51,413 INFO Got Site_Pe_Svc.
2025-08-01 11:49:58,513 INFO ****************************************************************************************************
2025-08-01 11:49:58,527 INFO *                                                                                                  *
2025-08-01 11:49:58,527 INFO *                                 Checking IPMI connection state.                                  *
2025-08-01 11:49:58,527 INFO *                                                                                                  *
2025-08-01 11:49:58,527 INFO ****************************************************************************************************
2025-08-01 11:49:58,527 INFO Checking RETSEELM-NX7001 ipmi ip: *************.
2025-08-01 11:49:58,814 INFO Good, RETSEELM-NX7001 ipmi is online.
2025-08-01 11:49:58,814 INFO Checking RETSEELM-NX7002 ipmi ip: *************.
2025-08-01 11:49:59,092 INFO Good, RETSEELM-NX7002 ipmi is online.
2025-08-01 11:49:59,092 INFO Checking RETSEELM-NX7003 ipmi ip: *************.
2025-08-01 11:49:59,389 INFO Good, RETSEELM-NX7003 ipmi is online.
2025-08-01 11:49:59,389 INFO All oob we got are online now, continue.
2025-08-01 11:50:13,624 INFO ****************************************************************************************************
2025-08-01 11:50:13,624 INFO *                                                                                                  *
2025-08-01 11:50:13,624 INFO *                            Checking the power state of *************.                            *
2025-08-01 11:50:13,624 INFO *                                                                                                  *
2025-08-01 11:50:13,624 INFO ****************************************************************************************************
2025-08-01 11:50:14,986 INFO It's on.
2025-08-01 11:50:21,720 INFO ****************************************************************************************************
2025-08-01 11:50:21,720 INFO *                                                                                                  *
2025-08-01 11:50:21,720 INFO *                            Checking the power state of *************.                            *
2025-08-01 11:50:21,720 INFO *                                                                                                  *
2025-08-01 11:50:21,720 INFO ****************************************************************************************************
2025-08-01 11:50:23,159 INFO It's on.
2025-08-01 11:50:26,030 INFO ****************************************************************************************************
2025-08-01 11:50:26,030 INFO *                                                                                                  *
2025-08-01 11:50:26,030 INFO *                            Checking the power state of *************.                            *
2025-08-01 11:50:26,030 INFO *                                                                                                  *
2025-08-01 11:50:26,030 INFO ****************************************************************************************************
2025-08-01 11:50:28,435 INFO It's on.
2025-08-01 11:50:35,670 INFO Checking RETSEELM-NX7001 cvm ip: ***********.
2025-08-01 11:50:35,963 INFO Good, RETSEELM-NX7001 cvm is online.
2025-08-01 11:50:35,963 INFO Checking RETSEELM-NX7002 cvm ip: ***********.
2025-08-01 11:50:36,255 INFO Good, RETSEELM-NX7002 cvm is online.
2025-08-01 11:50:36,255 INFO Checking RETSEELM-NX7003 cvm ip: ***********.
2025-08-01 11:50:36,542 INFO Good, RETSEELM-NX7003 cvm is online.
2025-08-01 11:50:36,543 INFO All cvm we got are online now, continue.
2025-08-01 11:50:58,914 INFO ****************************************************************************************************
2025-08-01 11:50:58,914 INFO *                                                                                                  *
2025-08-01 11:50:58,914 INFO *                                     Starting up the cluster.                                     *
2025-08-01 11:50:58,914 INFO *                                                                                                  *
2025-08-01 11:50:58,914 INFO ****************************************************************************************************
2025-08-01 11:50:58,914 INFO SSH connecting to the CVM.
2025-08-01 11:50:58,914 INFO SSH connecting to ***********, this is the '1' try.
2025-08-01 11:51:01,410 INFO SSH connected to ***********.
2025-08-01 11:51:02,531 INFO Sending 'cluster start' to the server.
2025-08-01 11:53:13,143 INFO Getting the cluster status.
2025-08-01 11:53:13,143 INFO SSH connecting to ***********, this is the '1' try.
2025-08-01 11:53:15,647 INFO SSH connected to ***********.
2025-08-01 11:53:16,767 INFO Sending 'cluster status' to the server.
2025-08-01 11:53:26,772 INFO Receiving the output .
2025-08-01 11:53:26,772 INFO Received the output: #SSH OUTPUT START#.
2025-08-01 11:53:26,772 INFO 

Nutanix Controller VM (CVM) is a virtual storage appliance.



Alteration of the CVM (unless advised by Nutanix Technical Support or

Support Portal Documentation) is unsupported and may result in loss

of User VMs or other data residing on the cluster.



Unsupported alterations may include (but are not limited to):



- Configuration changes / removal of files.

- Installation of third-party software/scripts not approved by Nutanix.

- Installation or upgrade of software packages from non-Nutanix

  sources (using yum, rpm, or similar).



** Notice: SSH will no longer be available in upcoming releases.      **  

** Nutanix Support may access the bash shell on an exceptional basis. **

Last login: Fri Aug  1 05:52:57 CEST 2025 from *********** on ssh

Last login: Fri Aug  1 05:53:15 2025 from **************


cluster status

[?1h=[H[J[m[?1003l[?1006l[?2004l[1;1H[1;24r[1;1H[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[7m[64] 0:bash*                             "ntnx-cz20240j8s-a-cvm" 05:53 01-Aug-25[m[1;1H[m[?1003l[?1006l[?2004l[1;1H[1;24r[1;1H[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[7m[64] 0:bash*                             "ntnx-cz20240j8s-a-cvm" 05:53 01-Aug-25[m[1;1H

Nutanix Controller VM (CVM) is a virtual storage appliance.[4;1HAlteration of the CVM (unless advised by Nutanix Technical Support or

Support Portal Documentation) is unsupported and may result in loss

of User VMs or other data residing on the cluster.[8;1HUnsupported alterations may include (but are not limited to):[10;1H- Configuration changes / removal of files.

- Installation of third-party software/scripts not approved by Nutanix.

- Installation or upgrade of software packages from non-Nutanix

  sources (using yum, rpm, or similar).[15;1H** Notice: SSH will no longer be available in upcoming releases.      **  

** Nutanix Support may access the bash shell on an exceptional basis. **[18;1HYou are running inside a tmux session[20;1HOpen sessions:

[1;23r[23;80H




[16;1H1: 1 windows (created Tue Jul 29 11:37:09 2025) [80x23]

10: 1 windows (created Wed Jul 30 03:56:47 2025) [80x23]

11: 1 windows (created Wed Jul 30 03:57:20 2025) [80x23]

12: 1 windows (created Wed Jul 30 04:03:44 2025) [80x23]

13: 1 windows (created Wed Jul 30 04:14:31 2025) [80x23]

14: 1 windows (created Wed Jul 30 04:19:31 2025) [144x50]

15: 1 windows (created Wed Jul 30 04:26:02 2025) [80x23][1;24r[23;1H[1;23r[23;80H






[16;1H16: 1 windows (created Wed Jul 30 04:33:12 2025) [80x23]

17: 1 windows (created Wed Jul 30 04:33:45 2025) [80x23]

18: 1 windows (created Wed Jul 30 04:43:47 2025) [80x23]

19: 1 windows (created Wed Jul 30 04:44:20 2025) [80x23]

2: 1 windows (created Tue Jul 29 11:37:41 2025) [80x23]

20: 1 windows (created Wed Jul 30 04:47:54 2025) [80x23]

21: 1 windows (created Wed Jul 30 04:51:56 2025) [80x23][1;24r[23;1H[1;23r[23;80H






[16;1H22: 1 windows (created Wed Jul 30 04:52:30 2025) [80x23]

23: 1 windows (created Wed Jul 30 04:56:44 2025) [80x23]

24: 1 windows (created Wed Jul 30 04:57:46 2025) [144x50]

25: 1 windows (created Wed Jul 30 05:01:36 2025) [80x23]

26: 1 windows (created Wed Jul 30 05:02:09 2025) [80x23]

27: 1 windows (created Wed Jul 30 05:07:41 2025) [80x23]

28: 1 windows (created Wed Jul 30 05:09:45 2025) [144x50][1;24r[23;1H[1;23r[23;80H




[18;1H29: 1 windows (created Wed Jul 30 05:19:40 2025) [80x23]

3: 1 windows (created Tue Jul 29 11:43:47 2025) [209x49]

30: 1 windows (created Wed Jul 30 05:20:13 2025) [80x23]

31: 1 windows (created Wed Jul 30 05:24:43 2025) [80x23]

32: 1 windows (created Wed Jul 30 05:29:23 2025) [144x50][1;24r[23;1H[1;23r[23;80H




[18;1H33: 1 windows (created Wed Jul 30 05:31:59 2025) [80x23]

34: 1 windows (created Wed Jul 30 05:32:38 2025) [80x23]

35: 1 windows (created Wed Jul 30 05:44:46 2025) [80x23]

36: 1 windows (created Wed Jul 30 05:46:10 2025) [80x23]

37: 1 windows (created Wed Jul 30 05:48:20 2025) [80x23][1;24r[23;1H[1;23r[23;80H



[19;1H38: 1 windows (created Wed Jul 30 05:50:09 2025) [80x23]

39: 1 windows (created Wed Jul 30 05:56:14 2025) [80x23]

4: 1 windows (created Tue Jul 29 11:44:30 2025) [80x23]

40: 1 windows (created Wed Jul 30 05:57:16 2025) [80x23][1;24r[23;1H[1;23r[23;80H




[18;1H41: 1 windows (created Wed Jul 30 06:05:29 2025) [80x23]

42: 1 windows (created Wed Jul 30 06:06:36 2025) [80x23]

43: 1 windows (created Wed Jul 30 06:09:15 2025) [80x23]

44: 1 windows (created Wed Jul 30 07:05:27 2025) [80x23]

45: 1 windows (created Wed Jul 30 07:06:34 2025) [80x23][1;24r[23;1H[1;23r[23;80H




[18;1H46: 1 windows (created Wed Jul 30 07:09:38 2025) [80x23]

47: 1 windows (created Wed Jul 30 07:10:20 2025) [80x23]

48: 1 windows (created Wed Jul 30 07:14:45 2025) [80x23]

49: 1 windows (created Wed Jul 30 07:15:52 2025) [80x23]

5: 1 windows (created Tue Jul 29 11:48:42 2025) [80x23][1;24r[23;1H[1;23r[23;80H


[20;1H50: 1 windows (created Wed Jul 30 07:17:49 2025) [80x23]

51: 1 windows (created Wed Jul 30 07:19:14 2025) [80x23]

52: 1 windows (created Wed Jul 30 07:20:25 2025) [80x23][1;24r[23;1H[1;23r[23;80H








[14;1H53: 1 windows (created Wed Jul 30 07:23:29 2025) [80x23]

54: 1 windows (created Thu Jul 31 04:07:05 2025) [80x23]

55: 1 windows (created Thu Jul 31 04:07:38 2025) [80x23]

56: 1 windows (created Thu Jul 31 04:10:46 2025) [80x23]

57: 1 windows (created Thu Jul 31 04:11:40 2025) [80x23]

58: 1 windows (created Thu Jul 31 04:13:06 2025) [80x23]

59: 1 windows (created Fri Aug  1 05:15:10 2025) [80x23]

6: 1 windows (created Tue Jul 29 11:50:39 2025) [80x23]

60: 1 windows (created Fri Aug  1 05:20:26 2025) [80x23][1;24r[23;1H[1;23r[23;80H


[20;1H61: 1 windows (created Fri Aug  1 05:23:00 2025) [80x23]

62: 1 windows (created Fri Aug  1 05:30:36 2025) [209x50]

63: 1 windows (created Fri Aug  1 05:51:03 2025) [80x23] (attached)

64: 1 windows (created Fri Aug  1 05:53:17 2025) [80x23] (attached)[1;24r[23;68H[1;23r[23;80H



[20;1H7: 1 windows (created Tue Jul 29 20:06:16 2025) [80x23]

8: 1 windows (created Tue Jul 29 20:06:19 2025) [80x23]

9: 1 windows (created Tue Jul 29 20:08:40 2025) [80x23][1;24r[23;1H[1;23r[23;80H






[17;1HTo attach to other sessions, run 'tmux switchc -t num' where num is the session number

To list existing sessions, run 'tmux ls'

To terminate sessions, run 'tmux kill-session -t num' where num is the session number

Refer to tmux documentation for more information and commands.[1;24r[23;1Hnutanix@NTNX-CZ20240J8S-A-CVM:***********:~$ 
2025-08-01 11:53:26,772 INFO #SSH OUTPUT END#
2025-08-01 11:53:58,358 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 11:53:58,358 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-01 11:53:58,358 INFO params: None
2025-08-01 11:53:58,358 INFO User: <EMAIL>
2025-08-01 11:53:58,358 INFO payload: None
2025-08-01 11:53:58,358 INFO files: None
2025-08-01 11:53:58,358 INFO timeout: 30
2025-08-01 11:54:01,046 INFO Got the VM list from RETSEELM-NXC000.
2025-08-01 11:54:11,232 INFO Start to set VM power state to 'ON', VM uuid: 14e51429-9f95-40b2-b00e-bf3e760f7fe4, VM name: retseelm-nxp001-1.
2025-08-01 11:54:11,232 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/14e51429-9f95-40b2-b00e-bf3e760f7fe4/set_power_state, method: POST, headers: None
2025-08-01 11:54:11,232 INFO params: None
2025-08-01 11:54:11,232 INFO User: <EMAIL>
2025-08-01 11:54:11,232 INFO payload: {'transition': 'ON'}
2025-08-01 11:54:11,232 INFO files: None
2025-08-01 11:54:11,232 INFO timeout: 30
2025-08-01 11:54:12,930 INFO Get task status attempting 1/5...
2025-08-01 11:54:12,930 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/191942f3-7cd9-41bd-9852-78535fbea65f, method: GET, headers: None
2025-08-01 11:54:12,930 INFO params: None
2025-08-01 11:54:12,930 INFO User: <EMAIL>
2025-08-01 11:54:12,930 INFO payload: None
2025-08-01 11:54:12,930 INFO files: None
2025-08-01 11:54:12,930 INFO timeout: 30
2025-08-01 11:54:14,653 INFO Task status: Running
2025-08-01 11:54:14,653 INFO Task is not ended, sleep 60s to retry... Task percentage: 1
2025-08-01 11:55:14,660 INFO Get task status attempting 2/5...
2025-08-01 11:55:14,660 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/191942f3-7cd9-41bd-9852-78535fbea65f, method: GET, headers: None
2025-08-01 11:55:14,661 INFO params: None
2025-08-01 11:55:14,661 INFO User: <EMAIL>
2025-08-01 11:55:14,661 INFO payload: None
2025-08-01 11:55:14,661 INFO files: None
2025-08-01 11:55:14,661 INFO timeout: 30
2025-08-01 11:55:15,931 INFO Task status: Succeeded
2025-08-01 11:55:15,931 INFO Successfully set VM power state to 'ON'.
2025-08-01 11:55:15,945 INFO Start to set VM power state to 'ON', VM uuid: 2a14b926-d7c8-4359-b705-a0520dadfe08, VM name: retseelm-nxp001-2.
2025-08-01 11:55:15,945 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/2a14b926-d7c8-4359-b705-a0520dadfe08/set_power_state, method: POST, headers: None
2025-08-01 11:55:15,945 INFO params: None
2025-08-01 11:55:15,945 INFO User: <EMAIL>
2025-08-01 11:55:15,946 INFO payload: {'transition': 'ON'}
2025-08-01 11:55:15,946 INFO files: None
2025-08-01 11:55:15,946 INFO timeout: 30
2025-08-01 11:55:18,217 INFO Get task status attempting 1/5...
2025-08-01 11:55:18,217 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/65a8e648-a724-4a6b-9100-ee9625f54a40, method: GET, headers: None
2025-08-01 11:55:18,217 INFO params: None
2025-08-01 11:55:18,217 INFO User: <EMAIL>
2025-08-01 11:55:18,217 INFO payload: None
2025-08-01 11:55:18,217 INFO files: None
2025-08-01 11:55:18,217 INFO timeout: 30
2025-08-01 11:55:20,086 INFO Task status: Running
2025-08-01 11:55:20,086 INFO Task is not ended, sleep 60s to retry... Task percentage: 1
2025-08-01 11:56:20,086 INFO Get task status attempting 2/5...
2025-08-01 11:56:20,086 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/65a8e648-a724-4a6b-9100-ee9625f54a40, method: GET, headers: None
2025-08-01 11:56:20,086 INFO params: None
2025-08-01 11:56:20,086 INFO User: <EMAIL>
2025-08-01 11:56:20,086 INFO payload: None
2025-08-01 11:56:20,086 INFO files: None
2025-08-01 11:56:20,086 INFO timeout: 30
2025-08-01 11:56:21,879 INFO Task status: Succeeded
2025-08-01 11:56:21,879 INFO Successfully set VM power state to 'ON'.
2025-08-01 11:56:21,900 INFO Start to set VM power state to 'ON', VM uuid: 4632716a-540e-400f-ac97-4815d0f7409d, VM name: auto_DND_calm_policy_engine_a0336651-215a-4bd5-bf5b-32724ad53039.
2025-08-01 11:56:21,900 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/4632716a-540e-400f-ac97-4815d0f7409d/set_power_state, method: POST, headers: None
2025-08-01 11:56:21,900 INFO params: None
2025-08-01 11:56:21,900 INFO User: <EMAIL>
2025-08-01 11:56:21,900 INFO payload: {'transition': 'ON'}
2025-08-01 11:56:21,900 INFO files: None
2025-08-01 11:56:21,900 INFO timeout: 30
2025-08-01 11:56:23,659 INFO Get task status attempting 1/5...
2025-08-01 11:56:23,659 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/979d8c4f-420b-4e3f-a63c-e28520911daa, method: GET, headers: None
2025-08-01 11:56:23,659 INFO params: None
2025-08-01 11:56:23,659 INFO User: <EMAIL>
2025-08-01 11:56:23,659 INFO payload: None
2025-08-01 11:56:23,659 INFO files: None
2025-08-01 11:56:23,659 INFO timeout: 30
2025-08-01 11:56:24,919 INFO Task status: Running
2025-08-01 11:56:24,919 INFO Task is not ended, sleep 60s to retry... Task percentage: 1
2025-08-01 11:57:24,924 INFO Get task status attempting 2/5...
2025-08-01 11:57:24,924 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/979d8c4f-420b-4e3f-a63c-e28520911daa, method: GET, headers: None
2025-08-01 11:57:24,924 INFO params: None
2025-08-01 11:57:24,924 INFO User: <EMAIL>
2025-08-01 11:57:24,924 INFO payload: None
2025-08-01 11:57:24,924 INFO files: None
2025-08-01 11:57:24,924 INFO timeout: 30
2025-08-01 11:57:26,812 INFO Task status: Succeeded
2025-08-01 11:57:26,812 INFO Successfully set VM power state to 'ON'.
2025-08-01 11:57:26,829 INFO Start to set VM power state to 'ON', VM uuid: 48f41f7a-79ae-4483-971f-d8b24d1e46fd, VM name: RETSEELM-NT1000.
2025-08-01 11:57:26,830 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/48f41f7a-79ae-4483-971f-d8b24d1e46fd/set_power_state, method: POST, headers: None
2025-08-01 11:57:26,830 INFO params: None
2025-08-01 11:57:26,830 INFO User: <EMAIL>
2025-08-01 11:57:26,830 INFO payload: {'transition': 'ON'}
2025-08-01 11:57:26,830 INFO files: None
2025-08-01 11:57:26,830 INFO timeout: 30
2025-08-01 11:57:28,714 INFO Get task status attempting 1/5...
2025-08-01 11:57:28,714 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/b86e2af3-723b-483a-a3d8-419fa852af60, method: GET, headers: None
2025-08-01 11:57:28,714 INFO params: None
2025-08-01 11:57:28,714 INFO User: <EMAIL>
2025-08-01 11:57:28,714 INFO payload: None
2025-08-01 11:57:28,714 INFO files: None
2025-08-01 11:57:28,714 INFO timeout: 30
2025-08-01 11:57:30,523 INFO Task status: Running
2025-08-01 11:57:30,523 INFO Task is not ended, sleep 60s to retry... Task percentage: 1
2025-08-01 11:58:30,525 INFO Get task status attempting 2/5...
2025-08-01 11:58:30,525 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/b86e2af3-723b-483a-a3d8-419fa852af60, method: GET, headers: None
2025-08-01 11:58:30,525 INFO params: None
2025-08-01 11:58:30,525 INFO User: <EMAIL>
2025-08-01 11:58:30,525 INFO payload: None
2025-08-01 11:58:30,525 INFO files: None
2025-08-01 11:58:30,525 INFO timeout: 30
2025-08-01 11:58:31,890 INFO Task status: Succeeded
2025-08-01 11:58:31,892 INFO Successfully set VM power state to 'ON'.
2025-08-01 11:58:31,905 INFO Start to set VM power state to 'ON', VM uuid: 9fe928d7-d96d-4e98-93ef-fb009480f4ff, VM name: retseelm-nxp001-3.
2025-08-01 11:58:31,906 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/9fe928d7-d96d-4e98-93ef-fb009480f4ff/set_power_state, method: POST, headers: None
2025-08-01 11:58:31,906 INFO params: None
2025-08-01 11:58:31,906 INFO User: <EMAIL>
2025-08-01 11:58:31,906 INFO payload: {'transition': 'ON'}
2025-08-01 11:58:31,906 INFO files: None
2025-08-01 11:58:31,907 INFO timeout: 30
2025-08-01 11:58:33,762 INFO Get task status attempting 1/5...
2025-08-01 11:58:33,762 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/b661ac61-b5d0-4507-b559-895d9e120c3b, method: GET, headers: None
2025-08-01 11:58:33,777 INFO params: None
2025-08-01 11:58:33,777 INFO User: <EMAIL>
2025-08-01 11:58:33,778 INFO payload: None
2025-08-01 11:58:33,778 INFO files: None
2025-08-01 11:58:33,778 INFO timeout: 30
2025-08-01 11:58:35,074 INFO Task status: Running
2025-08-01 11:58:35,074 INFO Task is not ended, sleep 60s to retry... Task percentage: 1
2025-08-01 11:59:35,084 INFO Get task status attempting 2/5...
2025-08-01 11:59:35,084 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/b661ac61-b5d0-4507-b559-895d9e120c3b, method: GET, headers: None
2025-08-01 11:59:35,084 INFO params: None
2025-08-01 11:59:35,084 INFO User: <EMAIL>
2025-08-01 11:59:35,084 INFO payload: None
2025-08-01 11:59:35,084 INFO files: None
2025-08-01 11:59:35,084 INFO timeout: 30
2025-08-01 11:59:36,405 INFO Task status: Succeeded
2025-08-01 11:59:36,405 INFO Successfully set VM power state to 'ON'.
2025-08-01 11:59:36,420 INFO Start to set VM power state to 'ON', VM uuid: ae546e3a-5ecb-4287-b900-0e4736d88205, VM name: RETSEELM-LX7300.
2025-08-01 11:59:36,421 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/ae546e3a-5ecb-4287-b900-0e4736d88205/set_power_state, method: POST, headers: None
2025-08-01 11:59:36,421 INFO params: None
2025-08-01 11:59:36,421 INFO User: <EMAIL>
2025-08-01 11:59:36,421 INFO payload: {'transition': 'ON'}
2025-08-01 11:59:36,421 INFO files: None
2025-08-01 11:59:36,421 INFO timeout: 30
2025-08-01 11:59:38,212 INFO Get task status attempting 1/5...
2025-08-01 11:59:38,212 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/1a683e79-1bb0-46da-a6be-57c1175747e9, method: GET, headers: None
2025-08-01 11:59:38,212 INFO params: None
2025-08-01 11:59:38,212 INFO User: <EMAIL>
2025-08-01 11:59:38,212 INFO payload: None
2025-08-01 11:59:38,212 INFO files: None
2025-08-01 11:59:38,212 INFO timeout: 30
2025-08-01 11:59:40,048 INFO Task status: Running
2025-08-01 11:59:40,049 INFO Task is not ended, sleep 60s to retry... Task percentage: 1
2025-08-01 12:00:40,050 INFO Get task status attempting 2/5...
2025-08-01 12:00:40,050 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/1a683e79-1bb0-46da-a6be-57c1175747e9, method: GET, headers: None
2025-08-01 12:00:40,050 INFO params: None
2025-08-01 12:00:40,050 INFO User: <EMAIL>
2025-08-01 12:00:40,050 INFO payload: None
2025-08-01 12:00:40,050 INFO files: None
2025-08-01 12:00:40,050 INFO timeout: 30
2025-08-01 12:00:41,961 INFO Task status: Succeeded
2025-08-01 12:00:41,961 INFO Successfully set VM power state to 'ON'.

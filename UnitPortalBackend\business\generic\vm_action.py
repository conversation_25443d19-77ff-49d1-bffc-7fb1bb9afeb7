import datetime
import logging
import re
import werkzeug.exceptions as flaskex
import sqlalchemy
from business.distributedhosting.nutanix.nutanix import PrismElement
from business.generic.commonfunc import setup_common_logger, CommonHelper
from models.ntx_models import ModelPrismElement, ModelPrismCentral, ModelRetailNutanixVMAction
from models.ntx_models_wh import ModelWarehousePrismElement, ModelWarehousePrismCentral
from business.generic.commonfunc import NutanixRest
from models.database import db
from business.authentication.authentication import Vault
import static.SETTINGS as SETTING
from business.generic.commonfunc import setup_common_logger, create_file, get_user_by_token




class VmAction:
    def __init__(self, pe, vm_name):
        self.vm_name = vm_name
        self.pe, pe_fqdn = CommonHelper.parse_pe_name_and_fqdn(pe)
        try:
            pe_info = ModelPrismElement.query.filter_by(fqdn=pe_fqdn).one()
            pc_info = ModelPrismCentral.query.filter_by(fqdn = pe_info.prism).one()
        except sqlalchemy.exc.NoResultFound:
            try:
                pe_info = ModelWarehousePrismElement.query.filter_by(fqdn=pe_fqdn).one()
                pc_info = ModelWarehousePrismCentral.query.filter_by(fqdn = pe_info.prism).one()
            except sqlalchemy.exc.NoResultFound:
                raise Exception("Can't find PC neither in Retail nor in Warehouse!")
        log_file_name = f'{self.vm_name}_{datetime.datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}'            
        if local_log_path := create_file(filepath=SETTING.VM_ACTION_LOG_PATH, filename=log_file_name):
            logging.info(f"Local log file created, path: {local_log_path}.")
        else:
            raise Exception('Failed to create local log file')
        self.logger = setup_common_logger(name=log_file_name, log_file=local_log_path)
        self.vault = Vault(tier=pc_info.tier)
        central_pe = re.sub(r'\..*$', '', pc_info.central_pe_fqdn)
        _res, self.pc_sa = self.vault.get_secret(f"{central_pe.upper()}/Site_Pc_Admin")
        self.rest_pc = NutanixRest(fqdn=pe_info.prism, username=self.pc_sa['username'], password=self.pc_sa['secret'], logger=self.logger)
        _res, pe_sa = self.vault.get_secret(f"{self.pe.upper()}/Site_Pe_Admin")
        self.rest_pe = PrismElement(pe=self.pe, sa={"username": pe_sa['username'], "password": pe_sa['secret']}, logger=self.logger)
        self.user = get_user_by_token()

    def check_vm_existence(self):
        _, vms = self.rest_pe.get_vm_list()
        vm_detail = {}
        for vm in vms:
            if vm['name'] == self.vm_name:
                vm_detail = type('VMObject', (object,), vm)()
                return True, vm_detail
        return False, (f"VM {self.vm_name} does not exist")

    def vm_power_action(self, action):
        res, vmdetail = self.check_vm_existence()
        if not res:
            self.logger.info(vmdetail)
            return False
        if action in ["ON", "OFF", "POWERCYCLE", "RESET", "PAUSE", "SUSPEND", "RESUME", "SAVE", "ACPI_SHUTDOWN", "ACPI_REBOOT"]:
            self.logger.info(f"VM {self.vm_name} power action: {action}")
            self.rest_pe.set_vm_power_state(vmdetail.uuid, action, self.vm_name)
            db.session.add(ModelRetailNutanixVMAction(pe=self.pe, vm_name=self.vm_name, action_type=action, user_name=self.user.username, time=datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")))
            db.session.commit()
        else:
            raise flaskex.BadRequest(f"Invalid power action {action}")
        
    
    def create_recovery_point(self):
        self.logger.info(f"Starting recovery point creation for VM {self.vm_name}")
        try:
            res, vmdetail = self.check_vm_existence()
            if not res:
                self.logger.info(vmdetail)
                return vmdetail
            self.logger.info(f"create recovery point for VM {self.vm_name}")
            payload = {
                "name": self.vm_name, 
                "recovery_point_type": "CRASH_CONSISTENT"
            }
            self.logger.debug(f"Sending recovery point creation request with payload: {payload}")
            result = self.rest_pc.prism_post(version='v3', request_url=f'/vms/{vmdetail.uuid}/snapshot', payload=payload).json()
            self.logger.info(f"Recovery point created successfully for VM {self.vm_name}")
            return f"vm recovery point created, name is {self.vm_name}, task_uuid: {result['task_uuid']}"
        except Exception as e:
            self.logger.error(f"Failed to create recovery point: {str(e)}")
            raise


    def check_recovery_point(self):
        res, vmdetail = self.check_vm_existence()
        if not res:
            self.logger.info(vmdetail)
            return vmdetail
        self.logger.info(f"check recovery point for VM {self.vm_name}")
        payload = {
            "entity_type": "vm_recovery_point",
            "group_member_attributes": [
                {
                    "attribute": "name"
                },
                {
                    "attribute": "creation_time_usecs"
                }
            ],
            "filter_criteria": f"entity_uuid=={vmdetail.uuid};snapshot_type!=LIVE"
        }
        res = self.rest_pc.prism_post(version='v3', request_url='/groups', payload=payload).json()
        filtered_vm = [
            entity for group in res['group_results']
            for entity in group['entity_results']
            if any(
                data['name'] == 'name'
                for data in entity['data']
            )
        ]
        if not filtered_vm:
            self.logger.info(f"No recovery points found for VM {self.vm_name}.")
            return []
        recovery_points = []
        for group in filtered_vm:
            recovery_point_name = None
            creation_time = None
            for entity in group['data']:
                if creation_time is None and entity['name'] == 'creation_time_usecs':
                    creation_time = int(entity['values'][0]['values'][0])  # get vm create time
                elif recovery_point_name is None and entity['name'] == 'name':
                    recovery_point_name = entity['values'][0]['values'][0]  # get vm name
            if creation_time is not None and recovery_point_name is not None:
                timestamp_seconds = creation_time / 1_000_000
                standard_time = datetime.datetime.fromtimestamp(timestamp_seconds)
                formatted_time = standard_time.strftime('%Y-%m-%d %H:%M:%S.%f')
                recovery_points.append(f"{formatted_time} - {recovery_point_name}")
        return recovery_points
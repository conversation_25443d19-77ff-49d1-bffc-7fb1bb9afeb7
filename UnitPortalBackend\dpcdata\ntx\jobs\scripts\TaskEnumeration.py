class TaskEnumeration:
    '''currently only states the ENUMs in use, for all ENUMs, check Microsoft official doc'''

    # TASK_TRIGGER_TYPE2 enumeration
    TASK_TRIGGER_DAILY = 2

    # TASK_ACTION_TYPE enumeration
    TASK_ACTION_EXEC = 0

    # TASK_CREATION enumeration
    TASK_CREATE = 2
    TASK_UPDATE = 4
    TASK_CREATE_OR_UPDATE = TASK_CREATE | TASK_UPDATE

    # TASK_LOGON_TYPE enumeration
    TASK_LOGON_NONE = 0
    TASK_LOGON_SERVICE_ACCOUNT = 5

    # TASK_ENUM_FLAGS enumeration
    TASK_ENUM_HIDDEN = 0     # Enumerates all tasks, including tasks that are hidden

    # TaskDefinition.Principal.RunLevel
    TASK_RUNLEVEL_HIGHEST = 1


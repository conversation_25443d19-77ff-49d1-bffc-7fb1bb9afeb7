from marshmallow import Schema, fields, validate

from business.distributedhosting.facility_type import FacilityType


class DscRequestSchema(Schema):
    pe = fields.Str(required=True)


class RenCertRequestSchema(Schema):
    pc = fields.Str(required=True)
    pe = fields.Str(required=True)


class AutoMaintenanceRequestSchema(Schema):
    class StepSchema(Schema):
        rotate_password = fields.Boolean(required=True)
        renew_certificate = fields.Boolean(required=True)
        desired_state_config = fields.Boolean(required=True)
    """
    {
        "pe_fqdn": "RETCN888-NXC000.IKEA.COM",
        "facility_type": "retail",
        "steps": {
            "rotate_password": true,
            "renew_certificate": true,
            "desired_state_config": true
        }
    }
    """
    pe_fqdn = fields.Str(required=True)
    facility_type = fields.Str(
        required=True, validate=validate.OneOf([FacilityType.RETAIL, FacilityType.WAREHOUSE])
    )
    steps = fields.Nested(StepSchema, required=True)


class AutoMaintenanceLockRequestSchema(Schema):
    """
    {
        pe: "pe",
        description: "description",
        start_time : "datetime_1",
        end_time   : "datetime_2",
    }
    """
    pe = fields.Str(required=True)
    start_time = fields.Integer(required=True)
    end_time = fields.Integer(required=True)


class AutoMaintenanceLogRequestSchema(Schema):
    """
    {
        atm_type: "string",
        id: int
    }
    """
    atm_type = fields.Str(required=True)
    id = fields.Integer(required=True)


class AutoMaintenanceAbortSchema(Schema):
    """
    {
        type    : [rotate_password|renew_certificate|desired_state_config],
        pid     : int,
        task_id : int
    }
    """
    type = fields.Str(required=True)
    pid = fields.Integer(required=True)
    task_id = fields.Integer(required=True)


class AosLcmSchema(Schema):
    pc = fields.Str(required=True)
    pe = fields.Str(required=True)
    target_ahv_version = fields.Str(required=True)
    target_aos_version = fields.Str(required=True)

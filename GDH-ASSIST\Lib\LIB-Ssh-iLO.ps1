function Ssh-iLO-Reset-BMC(){
    param(
        [string]
        $iLOAddress,
        [string]
        $iLOUsername,
        [string]
        $iLOPassword
    )
    try {
        $SshSession = New-SSHSession -ComputerName $iLOAddress `
                                     -Credential $(New-Object System.Management.Automation.PSCredential ($iLOUsername, $($iLOPassword | ConvertTo-SecureString -AsPlainText -Force))) `
                                     -AcceptKey:$true `
                                     -ErrorAction Ignore
        if ($SshSession) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're reseting the $iLOAddress"
            Invoke-SSHCommand -SSHSession $SshSession `
                              -Command 'reset map1'
            $SshSession.Disconnect()
        }else {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "It's unable to reset the $iLOAddress due to the session does not exist"
            return $null
        }
    }catch {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "$iLOAddress is being reset"
        return 1
    }
    return $null
}
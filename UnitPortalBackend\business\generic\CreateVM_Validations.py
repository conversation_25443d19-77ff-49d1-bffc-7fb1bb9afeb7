# Python Script
# Created by : <PERSON><PERSON>
# 20-Sept-2023
# <PERSON><PERSON><PERSON> to compare input data and check datatype and total value.retrun true
# or false as calculation validation.
# Script to take input value as string or int and assign it to
# list as return value.
# validation done based on the input values.each disc value should not cross
# 2000 and total sum of disc not more than 6000.
# input CPU value should not be more than limit value.
import logging
import re
import werkzeug.exceptions as flaskex
from models.workload_models import ModelWorkloadImage


class CreateVM_inputvalidation:

    def __init__(self, vm_spec, logger=logging):
        self.vm_spec = vm_spec
        self.logger = logger
        self.pattern = r"^\d+(,\d+)*$"

    # Compare the input string and validate isit integer or not and then
    # assign it to list variable as return value.

    def split_and_verify_integers(self, input_string):

        try:

            if re.match(self.pattern, input_string):
                # Split the input string by commas #
                if isinstance(input_string, str):
                    self.data_list = f"{input_string}".split(',')
                else:
                    self.data_list = input_string
                # Initialize a list to store the resultst.
                int_list = [int(item) for item in self.data_list]
                self.results = int_list
                # If all parts are integers, return the list of integers

                return {'status': True, "error": False,
                        "message": self.results}
            else:
                return {'status': False, "error": True,
                        "message": "The input format is wrong."}
        except ValueError:
            return {'status': False, "error": True,
                    "message": "The input format is wrong."}

    # Compare the input list and validate each list item is more than 2000 and
    # total sum of list items is more than 6000 accordingly return output.

    def sum_and_verify_inputlist(self, input_list):
        output = {}

        s_out = sum(input_list)
        s_val_check = all(x <= 2000 for x in input_list) and (s_out <= 6000)

        if not (s_val_check):
            output = {'status': s_val_check,

                      'error': True,
                      'message': 'input disk value is more than 2000 / Sum of all input disk more than 6000.'}
        else:
            output = {'status': s_val_check, 'error': False,


                      'message': 'Values are successfully validated'}

        return output

    # Validate the input value and limit and  return error if value
    # is more than limit as output message.
    def if_value_lt_limit(self, value, limit):

        try:
            result = {}

            strings = [value, limit]
            for string in strings:
                if re.match(self.pattern, string):
                    Flag = True
                else:
                    Flag = False

            if (Flag):
                value = str(value).strip()
                limit = str(limit).strip()
                if not ((value.isdigit() and limit.isdigit()) and
                        (int(value) < int(limit)) and (int(value) > 0)):

                    result = {

                        'status': False,
                        'error': True,
                        'message': 'Input values can not be Zero or CPU/Memory is more than limit size (' + limit + ')'}
                else:
                    result = {'status': True, 'error': False,


                              'message': 'Validation Sucessfull'}
            else:
                result = {'status': False, 'error': True,
                          'message': "The input format is wrong."}
            return result
        except ValueError:
            return {'status': False, "error": True,
                    "message": "The input format is wrong."}

    def verify_workload_image_type(self):
        image_os_type = ModelWorkloadImage().get_image_by_id(image_id=int(self.vm_spec['image_id'])).os_type
        if self.vm_spec["workload_type"] != image_os_type:
            raise flaskex.BadRequest("Workload type and image type doesn't match!")
        self.logger.info("Workload image type verification finished.")

    def verify_workload_boot_mode(self):
        workload_type = self.vm_spec["workload_type"]
        boot_mode = self.vm_spec["boot_mode"]
        if not (workload_type == "windows" and boot_mode == "SECURE_BOOT") \
                and not (workload_type == "linux" and boot_mode == "LEGACY") \
                and not (workload_type == 'network' and boot_mode == "LEGACY"):
            raise flaskex.BadRequest("Workload type and boot type doesn't match!")
        self.logger.info("Workload boot mode verification finished.")

    def verify_int_ids(self):
        try:
            int(self.vm_spec["vlan_id"])
            int(self.vm_spec["image_id"])
        except ValueError as e:
            raise flaskex.BadRequest(f"Vlan id or image id is not an integer! {e}")

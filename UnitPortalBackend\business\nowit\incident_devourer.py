"""
╔═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
║Module: incident_devourer                                                                                                              ║
║                                                                                                                                       ║
║Author:         Incident Devourer                                                                                                      ║
║Created:        2024-08-01                                                                                                             ║
║Last Updated:   2024-08-16                                                                                                             ║
║                                                                                                                                       ║
║Description:                                                                                                                           ║
║ The class IncidentDevourer contains the function 'trigger_incident_devourer', which serves as the entry point for the entire incident ║
║ handling script.                                                                                                                      ║
║                                                                                                                                       ║
║Usage:                                                                                                                                 ║
║Fetch GDH incidents without assignee, to match with incidents models via regular expression.                                            ║
║                                                                                                                                       ║
║Update Logs:                                                                                                                           ║
║-[Date]-[Author]-[Update content]                                                                                                      ║
╚═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
"""
import os
import re
import uuid
from base_path import application_path
from business.nowit.incident_handler_config import IncHandlerCfg
from .incident_handler_tools import IncidentHandlerTools
from .incident_handler_models import IncidentAnalyseModel
from flask import Flask
from business.generic.commonfunc import DBConfig, setup_common_logger
from models.models import db


class IncidentDevourer():
    
    def trigger_incident_devourer(self):
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        app.app_context().push()
        db.init_app(app)
        self.incident_handler_tools = IncidentHandlerTools()
        self.incident_analyse_model = IncidentAnalyseModel()
        incident_handler = os.path.join(application_path, "Log", "incidentHandler")
        logger = setup_common_logger(str(uuid.uuid4()), incident_handler + "\\IncidentHandlerLog")
        logger.info("[Incident-Handler]-Generating unit portal needs data for incident handler!")

        active_incidents = self.incident_handler_tools.get_gdh_active_incidents()
        resolved_inc_num = self.incident_handler_tools.get_resolved_inc_num()
        resolved_inc_num_today = self.incident_handler_tools.get_resolved_inc_num_today()
        unassigned_incidents = self.incident_handler_tools.get_unassigned_incidents()
        status = 'running'
        logger.info(f"writing inc summary data to db--{active_incidents}--{resolved_inc_num}--{resolved_inc_num_today}--{unassigned_incidents}--{status}")
        self.incident_handler_tools.commit_inc_handler_up_data(
            active_incidents,
            resolved_inc_num,
            resolved_inc_num_today,
            unassigned_incidents,
            status
        )
        no_assignee_incident_list = self.incident_handler_tools.get_gdh_active_incidents_no_assignee()
        handled_inc_list = self.incident_handler_tools.get_handled_inc()
        for incident in no_assignee_incident_list:
            inc_short_desc = (incident['short_description'])
            inc_num = incident['number']
            logger.info(f"Checking incidents--{inc_num}--{inc_short_desc}")
            inc_creation_time = incident['sys_created_on']
            svc_type = incident['business_service']
            
            if inc_num in handled_inc_list:
                logger.info(f"Skipping incident--{inc_num} as it is already handled today.")
                continue
            
            if 'Unnamed' in inc_short_desc or 'null' in inc_short_desc:
                self.incident_handler_tools.handle_null_pe(inc_num, inc_short_desc, svc_type)
                continue
            
            if re.search(IncHandlerCfg.cvm_reboot_pattern, inc_short_desc):    
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"Found target--{inc_num} is matched with CVM reboot model！")
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group()
                pe = re.search(IncHandlerCfg.NX_CLUSTER_NAME_PAT, orig_short_desc).group(1)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                self.incident_analyse_model.cvm_reboot_model(
                    inc_num,
                    orig_short_desc,
                    pe,
                    inc_creation_time,
                    svc_type
                    )
            elif re.search(IncHandlerCfg.nutanix_cluster_service_restart_pattern, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"Found target--{inc_num} is matched with nutanix cluster service restart model！")
                if 'Unnamed' in inc_short_desc or 'null' in inc_short_desc:
                    self.incident_handler_tools.handle_null_pe(inc_num, inc_short_desc, svc_type)
                    return
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group()
                pe = re.search(IncHandlerCfg.NX_CLUSTER_NAME_PAT, orig_short_desc).group()
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                self.incident_analyse_model.nutanix_cluster_service_restart_model(
                    inc_num,
                    orig_short_desc,
                    pe,
                    inc_creation_time,
                    svc_type
                    )
            elif re.search(IncHandlerCfg.wtp_host_connection_failure_pattern, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"Found target--{inc_num} is matched with WTP host connection model！")
                host_name = re.findall(IncHandlerCfg.wtp_host_connection_failure_pattern, inc_short_desc)[0]
                host_ip = self.incident_handler_tools.resolve_ip(host_name)
                orig_short_desc = re.search(IncHandlerCfg.wtp_host_connection_original_description_pattern, inc_short_desc).group(0)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                self.incident_analyse_model.wtp_host_connection_model(
                    inc_num,
                    orig_short_desc,
                    host_ip,
                    host_name,
                    svc_type
                    )

            elif re.search(IncHandlerCfg.nutanix_host_connection_failure_pattern, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"Found target--{inc_num} is matched with nutanix host connection failed model!")
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
                pe = re.search(IncHandlerCfg.NX_CLUSTER_NAME_PAT, orig_short_desc).group()
                host_ip = re.search(IncHandlerCfg.nutanix_host_connection_failure_pattern, orig_short_desc).group(2)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                self.incident_analyse_model.nutanix_host_connection_model(
                    inc_num,
                    orig_short_desc, 
                    pe, 
                    host_ip,
                    inc_creation_time,
                    svc_type
                    )
            
            elif re.search(IncHandlerCfg.nx_vm_not_found_pattern, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_desc = (incident['description'])
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                self.incident_analyse_model.vm_not_found_model(
                    inc_num,
                    orig_short_desc,
                    inc_desc,
                    svc_type
                    )
            elif re.search(IncHandlerCfg.nx_power_supply_down_pattern, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                try:
                    orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
                except AttributeError as error:
                    logger.error(f"Error in handling {inc_num} , error: {error}")
                    continue
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                # get the PE name and host SN
                if 'WiaB' in inc_short_desc:
                    pe = re.search(IncHandlerCfg.NX_WIAB_GET_PE_NAME_PAT, orig_short_desc).group(1)
                else:
                    pe = re.search(IncHandlerCfg.NX_GET_PE_NAME_PAT, orig_short_desc).group(1)
                sn = re.search(IncHandlerCfg.nx_SN_pattern, inc_short_desc).group(1)
                self.incident_analyse_model.ntx_power_supply_model(
                    inc_num,
                    orig_short_desc,
                    pe=pe, 
                    sn=sn,
                    svc_type=svc_type
                    )
            elif re.search(IncHandlerCfg.wtp_power_supply_loss_pattern, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                ilo_host = re.search(IncHandlerCfg.wtp_power_supply_host, inc_short_desc).group(1)
                self.incident_analyse_model.wtp_power_supply_model(
                    inc_num,
                    inc_short_desc,
                    ilo_host,
                    svc_type
                    )
                    
            elif re.search(IncHandlerCfg.nx_disk_offline_pattern, inc_short_desc): 
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_desc = (incident['description'])
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                pe = re.search(IncHandlerCfg.NX_GET_PE_NAME_PAT, orig_short_desc).group(1)
                disk_sn = re.search(IncHandlerCfg.nx_disk_sn_pattern, inc_desc).group(1)
                self.incident_analyse_model.disk_offline_model(
                    inc_num,
                    orig_short_desc,
                    pe,
                    disk_sn,
                    svc_type
                    )
            elif re.search(IncHandlerCfg.nx_ahv_incompatible_pattern, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                incident_description = (incident['description'])
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
                if 'WiaB' in inc_short_desc:
                    pe = re.search(IncHandlerCfg.NX_WIAB_GET_PE_NAME_PAT, orig_short_desc).group(1)
                else:
                    pe = re.search(IncHandlerCfg.NX_GET_PE_NAME_PAT, orig_short_desc).group(1)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                cluster_uuid = re.search(IncHandlerCfg.nx_inc_cluster_id, incident_description).group(1)
                self.incident_analyse_model.ahv_incompatible_model(
                    inc_num,
                    orig_short_desc,
                    pe,
                    cluster_uuid,
                    svc_type
                    )
            elif re.search(IncHandlerCfg.nx_host_link_down_pattern, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"Found target--{inc_num} is matched with nx host link down!")
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
                pe = re.search(IncHandlerCfg.nx_host_link_down_pattern, orig_short_desc).group(1)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                host = re.search(IncHandlerCfg.nx_host_link_down_pattern, orig_short_desc).group(2)
                self.incident_analyse_model.nx_host_link_down_model(
                    inc_num,
                    orig_short_desc,
                    pe,
                    host,
                    inc_creation_time,
                    svc_type
                    )
            
            elif re.search(IncHandlerCfg.nx_cvm_service_restart_pattern, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"Found target--{inc_num} is matched with CVM service(s) Restarting Frequently！")
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group()
                pe = re.search(IncHandlerCfg.nx_cvm_service_restart_pattern, orig_short_desc).group(1)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                self.incident_analyse_model.nx_cvm_srv_restart_model(
                    inc_num,
                    orig_short_desc,
                    pe,
                    inc_creation_time,
                    svc_type
                    ) 
                                      
            elif re.search(IncHandlerCfg.NX_PE_PC_CONNECTION_PATTERN, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.NX_PE_PC_CONNECTION_PATTERN,
                    IncHandlerCfg.NX_PE_PC_CONNECTION_UUID, 
                    svc_type
                )

            elif re.search(IncHandlerCfg.NX_PD_REPL_FAILED_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.NX_PD_REPL_FAILED_PAT,
                    IncHandlerCfg.NX_PD_REPL_FAILED_UUID,
                    svc_type
                )

            elif re.search(IncHandlerCfg.NX_IDF_NOT_SYNC_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.NX_IDF_NOT_SYNC_PAT,
                    IncHandlerCfg.NX_IDF_NOT_SYNC_UUID,
                    svc_type
                )
   
                
            elif re.search(IncHandlerCfg.NET_MAPPING_IVALID_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.NET_MAPPING_IVALID_PAT,
                    IncHandlerCfg.NX_NET_MAP_INVALID_UUID,
                    svc_type
                    )           

            elif re.search(IncHandlerCfg.NX_TIME_NOT_SYNC_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_description = (incident['description'])
                self.incident_handler_tools.add_prefix_handling(inc_num, inc_short_desc)
                self.incident_analyse_model.common_check_model(
                    inc_num,
                    inc_short_desc,
                    inc_description,
                    svc_type
                )

            elif re.search(IncHandlerCfg.NX_FANOUTPORT_PATTERN, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_description = (incident['description'])
                self.incident_handler_tools.add_prefix_handling(inc_num, inc_short_desc)
                self.incident_analyse_model.common_check_model(
                    inc_num,
                    inc_short_desc,
                    inc_description,
                    svc_type
                )

            elif re.search(IncHandlerCfg.NX_CVM_TIME_DIFF_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_description = (incident['description'])
                self.incident_handler_tools.add_prefix_handling(inc_num, inc_short_desc)
                self.incident_analyse_model.common_check_model(
                    inc_num,
                    inc_short_desc,
                    inc_description,
                    svc_type
                )

            elif re.search(IncHandlerCfg.NX_NTP_NOT_CONF_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_description = (incident['description'])
                self.incident_handler_tools.add_prefix_handling(inc_num, inc_short_desc)
                self.incident_analyse_model.common_check_model(
                    inc_num,
                    inc_short_desc,
                    inc_description,
                    svc_type
                )

            elif re.search(IncHandlerCfg.NX_NIC_FLAPS_INC_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_description = (incident['description'])
                self.incident_handler_tools.add_prefix_handling(inc_num, inc_short_desc)
                self.incident_analyse_model.common_check_model(
                    inc_num,
                    inc_short_desc,
                    inc_description,
                    svc_type
                )

            elif re.search(IncHandlerCfg.NX_LIC_VIO_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_description = (incident['description'])
                self.incident_handler_tools.add_prefix_handling(inc_num, inc_short_desc)
                self.incident_analyse_model.common_check_model(
                    inc_num,
                    inc_short_desc,
                    inc_description,
                    svc_type
                )

            elif re.search(IncHandlerCfg.NX_EXT4_DISK_ERR_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_description = (incident['description'])
                self.incident_handler_tools.add_prefix_handling(inc_num, inc_short_desc)
                self.incident_analyse_model.common_check_model(
                    inc_num,
                    inc_short_desc,
                    inc_description,
                    svc_type
                )

            elif re.search(IncHandlerCfg.WIAB_NTP_NOT_CONF_PAT, inc_short_desc):  # WiaB Host time not sync
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.add_prefix_handling(inc_num, inc_short_desc)
                inc_description = (incident['description'])
                self.incident_analyse_model.wiab_common_check_model(
                    inc_num,
                    inc_short_desc,
                    inc_description,
                    svc_type
                )

            elif re.search(IncHandlerCfg.WIAB_CVM_TIME_SYNC_PATTERN, inc_short_desc):  # WiaB CVM time not sync
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.add_prefix_handling(inc_num, inc_short_desc)
                inc_description = (incident['description'])
                self.incident_analyse_model.wiab_common_check_model(
                    inc_num,
                    inc_short_desc,
                    inc_description,
                    svc_type
                )

            elif re.search(IncHandlerCfg.ECOX_INC_PAT, inc_short_desc):
                #sth wrong with this model
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.add_prefix_handling(inc_num, inc_short_desc)
                self.incident_analyse_model.ecox_model(
                    inc_num,
                    inc_short_desc,
                    inc_creation_time,
                    svc_type
                    )
                
            elif re.search(IncHandlerCfg.NX_CVM_DISCONED_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_description = (incident['description'])
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                pe_name = re.search(IncHandlerCfg.NX_CLUSTER_NAME_PAT, orig_short_desc).group(1)
                ip = re.findall(IncHandlerCfg.IP_PATTERN, inc_description)
                ping_cvmip = ip[0]
                ssh_cvmip = ip[1]
                self.incident_analyse_model.cvm_disconnected_model(
                    inc_num,
                    orig_short_desc,
                    pe_name,
                    ping_cvmip,
                    ssh_cvmip,
                    svc_type
                    )
                
            elif re.search(IncHandlerCfg.NX_PULSE_INC_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                inc_description = (incident['description'])
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group(1)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                pe_name = re.search(IncHandlerCfg.NX_CLUSTER_NAME_PAT, orig_short_desc).group(1)
                ip = re.findall(IncHandlerCfg.IP_PATTERN, inc_description)[0]
                self.incident_analyse_model.connect_rest_model(
                    inc_num,
                    orig_short_desc,
                    pe_name,
                    ip,
                    svc_type
                    )
            
            elif re.search(IncHandlerCfg.NX_DISK_HIGH_INC_PAT, inc_short_desc):   
                logger.info(f"Handling with {inc_num}--{inc_short_desc}>")
                inc_description = (incident['description'])
                ip = re.findall(IncHandlerCfg.IP_PATTERN, inc_description) # list type
                cvm_ip = ip[0]
                self.incident_analyse_model.disk_usage_high_model(
                    inc_num,
                    inc_short_desc,
                    cvm_ip,
                    svc_type
                    )
            
            elif re.search(IncHandlerCfg.NX_META_DATA_INC_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}>")
                inc_description = (incident['description'])
                ip = re.findall(IncHandlerCfg.IP_PATTERN, inc_description)
                cvm_ip = ip[1]
                self.incident_analyse_model.metadata_ring_model(
                    inc_num,
                    inc_short_desc,
                    cvm_ip,
                    svc_type
                    )
                
            elif re.search(IncHandlerCfg.NX_SKIPPED_REPL_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"Found target--{inc_num} is matched with skipped replication of protection domain!")
                self.incident_analyse_model.sipped_repl_model(
                    inc_num,
                    inc_short_desc,
                    svc_type,
                    (re.search(r'snapshot\s+(\d+)', incident['description'])).group(1),
                    incident['description'],
                    IncHandlerCfg.NX_SKIPPED_REPL_UUID
                    )
                
            elif re.search(IncHandlerCfg.zookeeper_not_active_pattern, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.commit_incident_handler_log(IncHandlerCfg.INC_CHK, f"Found target--{inc_num} is matched with zookeeper not active model！")
                orig_short_desc = re.search(IncHandlerCfg.NX_INC_ORIG_DESC_PAT, inc_short_desc).group()
                pe = re.search(IncHandlerCfg.NX_CLUSTER_NAME_PAT, orig_short_desc).group(1)
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                self.incident_analyse_model.zookeeper_not_active_model(
                    inc_num,
                    orig_short_desc,
                    pe,
                    inc_creation_time,
                    svc_type
                    )

            elif re.search(IncHandlerCfg.DO_NOT_SUPPORT_RPO_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.DO_NOT_SUPPORT_RPO_PAT,
                    IncHandlerCfg.NX_NOT_SUPPORT_RPO_UUID,
                    svc_type
                    )
                
            elif re.search(IncHandlerCfg.CALM_TRIAL_EXPIRY_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.CALM_TRIAL_EXPIRY_PAT,
                    IncHandlerCfg.NX_CALM_TRIAL_EXPIRED_UUID,
                    svc_type
                    )
            
            elif re.search(IncHandlerCfg.REPLICATION_OF_PD_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.REPLICATION_OF_PD_PAT,
                    IncHandlerCfg.NX_REPL_OF_PD_UUID,
                    svc_type
                    )
            
            elif re.search(IncHandlerCfg.remote_site_connectivity_pattern, inc_short_desc): #remote connectivity model
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.remote_site_connectivity_pattern,
                    IncHandlerCfg.nx_connectivity_to_remote_uuid,
                    svc_type
                    )
            
            elif re.search(IncHandlerCfg.SIAB_HOST_DEFAULT_PW, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.SIAB_HOST_DEFAULT_PW,
                    IncHandlerCfg.SIAB_HOST_DEFAULT_PW_UUID,
                    svc_type  # add the missing  svc_type
                    )
                
            elif re.search(IncHandlerCfg.CONNECT_ISSUE_CLUSTER, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.CONNECT_ISSUE_CLUSTER,
                    IncHandlerCfg.CONNECT_ISSUE_CLUSTER_UUID,
                    svc_type
                )

                
            elif re.search(IncHandlerCfg.SIAB_IAM_AUTH_DB_ERR, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.SIAB_IAM_AUTH_DB_ERR,
                    "NTNX_IAMv2_Authn_Database_Connectivity_Error_Warning",
                    svc_type  # add the missing  svc_type
                    )

            elif re.search(IncHandlerCfg.WIAB_NIC_LINK_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                orig_short_desc = re.search(IncHandlerCfg.WIAB_INC_ORIG_PAT, inc_short_desc).group(0)
                inc_desc = (incident['description'])
                self.incident_handler_tools.add_prefix_handling(inc_num, orig_short_desc)
                pe = re.search(IncHandlerCfg.WIAB_PE_NAME_PAT, inc_desc).group(1)
                pc = re.search(IncHandlerCfg.PC_ADDRESS_PATTERN, inc_desc).group(1)
                pe_uuid = re.search(IncHandlerCfg.PE_UUID_PATTERN, inc_desc).group(1)
                host = re.search(IncHandlerCfg. WIAB_PE_NAME_PAT2, inc_short_desc).group(1)
                self.incident_analyse_model.wiab_host_link_down_model(
                    inc_num,
                    orig_short_desc,
                    pe,
                    pc,
                    pe_uuid,
                    host,
                    svc_type
                )
                
            elif re.search(IncHandlerCfg.CURR_MEMORY_CONFIG_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_handler_tools.common_handle_pc_alert_inc(
                    inc_short_desc,
                    inc_num,
                    incident['description'],
                    IncHandlerCfg.CURR_MEMORY_CONFIG_PAT,
                    "A111074",
                    svc_type,
                    ncc_chk = '111074'
                )

            elif re.search(IncHandlerCfg.WIAB_FANOUT_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_analyse_model.wiab_fanout_secure_port_model(
                    inc_num,
                    inc_short_desc,
                    incident['description'],
                    svc_type
                )

            elif re.search(IncHandlerCfg.IPMI_MISMATCH_PAT, inc_short_desc):
                logger.info(f"Handling with {inc_num}--{inc_short_desc}")
                self.incident_analyse_model.ipmi_ip_mismatch_model(
                    inc_num,
                    inc_short_desc,
                    incident['description'],
                    svc_type
                )

            else:
                logger.info(f'{inc_num}--{inc_short_desc}No this kind of case')
                matches = re.findall(IncHandlerCfg.NX_NO_MODEL_ORIG_DESC_PAT, inc_short_desc)
                orig_short_desc = [match.strip() for match in matches if match.strip()][0]
                self.incident_handler_tools.add_prefix_no_model(inc_num, orig_short_desc)

        return no_assignee_incident_list
    
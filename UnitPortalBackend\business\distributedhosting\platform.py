import logging
from models.sli_models import ModelSLICluster
from models.models import  ModelCountryCode
from business.authentication.authentication import ServiceAccount
from business.distributedhosting.nutanix.pm.nutanix_pm import NutanixOperation
from business.distributedhosting.simplivity.pm.simplivity_pm import SimplivityPM
from models.ntx_models import ModelPrismElement
import werkzeug.exceptions as flaskex


class Platform():
    def __init__(self, sa = None, logger=logging) -> None:
        if not sa:
            _sa = ServiceAccount(usage="nutanix_pm")
            self.sa = _sa.get_service_account()
        else:
            self.sa = sa
        self.logger = logger if logger else logging
    
    def check_site_existence(self, country_code, site_code):
        site_info = ModelCountryCode.query.filter_by(country_code = country_code).first()
        result = {'retail_nutanix': '', 'retail_simplivity': ''}
        if site_info:
            if site_info.retail_pc:
                ntx_pm = NutanixOperation(pc=site_info.retail_pc, pe=f"{country_code}{site_code}")
                res, msg = ntx_pm.if_cluster_exists()
                if res:
                    result['retail_nutanix'] = {"existence": True, "pe": msg, "pc": site_info.retail_pc}
                else:
                    result['retail_nutanix'] = {"existence": False, "pe": msg, "pc": site_info.retail_pc}
            if site_info.retail_vc:
                print(site_info.retail_vc)
                sli_pm = SimplivityPM(vc=site_info.retail_vc, cluster=f"RET{country_code}{site_code}", usage="simplivity_pm")
                # sli_pm = SimplivityPM(vc="vc-sefed01.ikea.com",cluster="RETSE268")
                res, msg = sli_pm.if_slicluster_exists()
                # res = 1
                if res:
                    result['retail_simplivity'] = {"existence": True, "cluster": msg.get('cluster') if isinstance(msg, dict) else msg, "vc": msg.get('vc') if isinstance(msg, dict) else msg}
                else:
                    result['retail_simplivity'] = {"existence": False, "cluster": msg.get('cluster') if isinstance(msg, dict) else msg, "vc": msg.get('vc') if isinstance(msg, dict) else msg}
            return True, result
        return False, "Country code doesn't exist."

    def check_site_existence_in_db(self, country_code, site_code):
        site_info = ModelCountryCode.query.filter_by(country_code=country_code).first()
        if not site_info:
            raise flaskex.InternalServerError(f"Country code {country_code} doesn't exist in the database.")
        pelist = ModelPrismElement.get_pe_by_codes(country_code, site_code)
        _pes = [pe.name for pe in pelist if pe.status != 'Decommissioned'] 
        cluster = ModelSLICluster.get_cluster_by_cluster_name(country_code, site_code)
        vc = None
        if site_info and site_info.retail_vc:
            if cluster and cluster.vc_fqdn:
                vc = cluster.vc_fqdn
            else:
                vc = site_info.retail_vc
        result = {
            'retail_nutanix': {
                'cluter_num': len(_pes),
                'existence': True if _pes else False,
                'pe': ";".join(_pes) if _pes else None,
                'pc': site_info.retail_pc if site_info and site_info.retail_pc else None
            },
            'retail_simplivity': {
                'existence': True if cluster and cluster.status == 'Y' else False,
                'cluster': cluster.name if cluster else None,
                'vc': vc
            }
        }
        return result

import request from '@/utils/request'
import axios from 'axios'
import { endpoint } from './endpoint'
// export function login(data) {
//   let res =  request({
//     url: '/vue-element-admin/user/login',
//     method: 'post',
//     data
//   })
//   return res
// }

// export function getInfo(token) {
//   return request({
//     url: '/vue-element-admin/user/info',
//     method: 'get',
//     params: { token }
//   })
// }

export function getInfo(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token},
  };
  let res =  request.post(`${endpoint}/role`, {token:token}, config)
  return res
}



export function login(data) {
  let res =  request.post(`${endpoint}/login`,{},{
    auth: {
      username: data.username,
      password: data.password
    }
  })
  return res
}
// Get Role
export function Getrolelist(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/role/list`,config)
  return res
}

// Get/Add/Edit/Delete User
export function GetUsersList(token , download = false) {
  if(download){
    var config = {
      headers: {'Authorization': 'Bearer ' + token},
      responseType:'blob'
    };
    let res =  request.get(`${endpoint}/user/list?download=true`,config)
    return res
  }
  else{
    var config = {
      headers: {'Authorization': 'Bearer ' + token}
    };
    let res =  request.get(`${endpoint}/user/list`,config)
    return res
  }

}

export function Adduser(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/user`,param.data,config)
  return res
}

export function EditUser(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res =  request.put(`${endpoint}/user`,param.data,config)
  return res
}

export function DeleteUser(param){

  let payload = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data   : param.data
  }
  let res =  request.delete(`${endpoint}/user`, payload)
  return res
}

// Get/Add/Edit/Delete Group

export function GetGroupList(token , download = false) {
  if(download){
    var config = {
      headers: {'Authorization': 'Bearer ' + token},
      responseType:'blob'
    };
    let res =  request.get(`${endpoint}/group?download=true`,config)
    return res
  }
  else{
    var config = {
      headers: {'Authorization': 'Bearer ' + token}
    };
    let res =  request.get(`${endpoint}/group`,config)
    return res
  }
}

export function AddGroup(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/group`,param.data,config)
  return res
}

export function EditGroup(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res =  request.put(`${endpoint}/group`,param.data,config)
  return res
}

export function DeleteGroup(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res =  request.delete(`${endpoint}/group`,{data:{payload:param.data,headers:config}})
  return res
}

export function GetUserRole(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res =  request.get(`${endpoint}/user/${param.user_id}`, config)
  return res
}

export function UpdateUserRole(param){
  var config ={
    headers: {'Authorization': 'Bearer ' + param.token}
  }
  let res =  request.put(`${endpoint}/user/role`, param.data, config)
  return res
}
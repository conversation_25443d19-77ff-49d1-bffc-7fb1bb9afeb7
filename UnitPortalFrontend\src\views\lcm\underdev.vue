<template>
  <div class="under-development">
    <h2>Be patient, Page is Under Development...</h2>
    <br />
    <div class="loader"></div>
  </div>
</template>

<script>
export default {
  name: "UnderDevelopment",
};
</script>

<style scoped>
.under-development {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh; 
  font-family: Arial, sans-serif;
}

.loader {
    padding: 35px;
  border: 10px solid #f3f3f3; /* Light grey */
  border-top: 10px solid #3498db; /* Blue */
  border-radius: 50%;
  width: 80px;
  height: 80px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
# Python Script
# Created by : <PERSON><PERSON>
# 6-Feb-2024
# Script to blackout server exists in Splunk during PM.
# we need to communicate Splunk API to perform this action.

import requests


class BlackOutPowerMaintenance:

    def __init__(self, api_url, api_url_get, token, Status_token):
        self.api_url = api_url
        self.api_url_get = api_url_get
        self.token = token
        self.Status_token = Status_token

    def splunk_blackout(self, blackout_data):
        # Define headers with authorization token
        headers = {
            "Authorization": f"Splunk {self.token}"
        }

        # Send POST request to Splunk endpoint
        response = requests.post(self.api_url, headers=headers, json=blackout_data)
        # print(response)

        # Check response status
        if response.status_code == 200:
            print("Blackout request successful.")
        else:
            print("Failed to set blackout mode.")
            print(response.text)

    def get_servers_status(self, Server_data):
        # Define headers with authorization token
        headers = {
            "Authorization": f"Splunk {self.Status_token}"
        }
        # Define search parameters
        params = {
            "search": Server_data,
            "output_mode": "json"
        }

        print(self.api_url_get, headers, params)
        # Send GET request to retrieve server status
        response = requests.get(self.api_url_get, headers=headers, params=params)
        # response = requests.get(self.api_url_get, headers=headers, json=self.Server_data)

        return response


# Example usage
api_url = "https://evcgcpsplunk.ikea.net:443/services/collector"
api_url_get = "https://rest-event.splunk.ingka.com/servicesNS/-/TA-itsi-rest-endpoint/blackout"
token = "c9959958-0704-4c32-8544-0ebf74352ce0"  # Splunk token
Status_token = "eyJraWQiOiJzcGx1bmsuc2VjcmV0IiwiYWxnIjoiSFM1MTIiLCJ2ZXIiOiJ2MiIsInR0eXAiOiJzdGF0aWMifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dd73cWWBBM_84gId1l1XeH9itnjrS9_dVbgIlQIo8r1HwYgTrPpjFKby1uIpx9maJF_8MoEi0DGpnpXgWm3-5Q"
search_query = "index=common_pub sourcetype='blackout:*'"

blackOutInstance = BlackOutPowerMaintenance(api_url, api_url_get, token, Status_token)

Server_data = {
    "event": {

        "resource": "RETCN888-NX7002",
        "owner": "Charan Rachaiah"
    }
}
blackout_data = {
    "event": {
        "resource": "RETCN888-NX7002",
        "duration": 500,
        "timezone": "UTC",
        "comment": "Sample blackout for Confluence",
        "owner": "Charan Rachaiah"
    }
}


# blackout_status = objclsblk.get_servers_status(api_url, token, Server_data)
# if blackout_status is not None:
#   print("Blackout status:")
#   for status in blackout_status:
#       print(status)


# objclsblk.get_blackout_status()

blackOutInstance.splunk_blackout(blackout_data)

# blackout_status = blackOutInstance.get_servers_status(Server_data)
# if blackout_status is not None:
#     print("Blackout status:")
# for status in blackout_status:
#     print(status)

# print(blackout_status)

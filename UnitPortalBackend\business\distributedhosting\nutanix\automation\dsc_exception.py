from business.generic.base_up_exception import BaseUpException


class DscException(BaseUpException):
    def __init__(self, msg):
        super().__init__(msg)


class RemoteSiteInUse(DscException):
    def __init__(self, msg):
        super().__init__(msg)


class OneViewServerAddFailed(DscException):
    def __init__(self, server_name):
        super().__init__(f"Failed to add server '{server_name}' to OneView")

import logging
import os
import socket

from apispec import APISpec
from apispec.ext.marshmallow import MarshmallowPlugin
from flask import Flask
from flask_cors import CORS
from flask_migrate import Migrate

from business.authentication.tokenvalidation import PrivilegeValidation
from business.distributedhosting.nutanix.automation.seamless_lcm import SeamlessLcm
from business.distributedhosting.scheduler.atm_scheduler import auto_maintenance_scheduler, atm_scheduler
from business.distributedhosting.scheduler.scheduler_config import SchedulerConfig
from business.loggings.loggings import LoggingFile
from business.distributedhosting.scheduler.seamless_lcm_scheduler import seamless_lcm_scheduler
from business.restful import distributedhosting, swagger_docs
from models.database import db
import static.SETTINGS as SETTING
from scheduler import add_scheduler
from business.generic.commonfunc import DBConfig

SM = 1 #it's used for incident handler shared memory


def initialize_folders():
    folders = [SETTING.LOG_PATH, SETTING.SLI_LOG_PATH, SETTING.NTX_LOG_PATH, SETTING.WORKLOAD_LOG_PATH, SETTING.LOG_ARCHIVE_FOLDER_PATH,
               SETTING.AUTOMATION_PATH, SETTING.SPP_LCM_LOG_PATH, SETTING.AOS_LCM_LOG_PATH, SETTING.CSV_TMP_PATH,
               SETTING.ROTATE_PASSWD_LOG_PATH, SETTING.AUTOMATION_LOG_PATH,
               SETTING.DSC_LOG_PATH, SETTING.MOVE_LOG_PATH, SETTING.RENEW_CERT_LOG_PATH, SETTING.CERT_TMP_PATH,
               SETTING.VAULT_PASSWD_HOME_PATH, SETTING.VAULT_PASSWD_PATH,
               SETTING.SSH_KEY_PATH,
               SETTING.CLUSTER_LOG_PATH,
               SETTING.METRO_VG_LOG_PATH,
               SETTING.DATA_FETCH_LOG_PATH,
               SETTING.INCIDENT_LOG_PATH,
               SETTING.UNLOCK_ACCOUNT_LOG_PATH,
               SeamlessLcm.LOG_DIR
               ]
    for folder in folders:
        if not os.path.exists(folder):
            os.mkdir(folder)


def prepare_app():
    initialize_folders()
    logging.basicConfig(filename=SETTING.MAIN_LOG_FILE_PATH, level=logging.INFO, format="%(asctime)s %(message)s",
                        filemode="a+")

    logger = logging.getLogger()  # 不加名称设置root logger
    logger.setLevel(logging.DEBUG)
    # add console output
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s: - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)
    ch.setFormatter(formatter)
    logger.addHandler(ch)

    app = Flask(__name__)

    app.register_blueprint(distributedhosting)
    # app.config['CORS_HEADERS'] = 'Content-Type'
    app.config['CORS_SUPPORTS_CREDENTIALS'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    app.config['SQLALCHEMY_TRACK_MODIFACATIONS'] = False

    # Configure APISpec
    app.config.update({
        'APISPEC_SPEC': APISpec(
            title='Unit Portal',
            version='v1',
            plugins=[MarshmallowPlugin()],
            openapi_version='2.0.0',
            securityDefinitions={
                'Bearer': {
                    'type': 'apiKey',
                    'name': 'Authorization',
                    'in': 'header',
                    'description': 'Enter your bearer token in the format "Bearer {token}"'
                },
                'BasicAuth': {
                    'type': 'basic',
                    'description': 'Basic Authentication for login endpoint only'
                }
            },
            security=[{'Bearer': []}]  # Default security is Bearer token
        ),
        'APISPEC_SWAGGER_URL': '/swagger/',  # URI to access API Doc JSON
        'APISPEC_SWAGGER_UI_URL': '/swagger-ui/'  # URI to access UI of API Doc
    })

    app.app_context().push()
    db.init_app(app)  # db is in models.py
    migerate = Migrate(app, db)     # noqa      # pylint: disable=W0612
    # CORS(app)
    CORS(app, resources={r"/*": {"origins": "*"}})
    # ma.init_app(app)

    swagger_docs.init_app(app)

    return app


def get_runtime():
    env = "test"
    hostname = socket.gethostname()
    if hostname == "NTXSEELM-NT3000":
        env = "production"
    elif hostname == "RETCNSOS-NT8765":
        env = "preproduction"
    else:
        env = "test"
    return {
        "host" : "0.0.0.0",
        "use_reloader" : False,
        "port" : 443 if env == "production" else 5000,
        "ssl_context" : (r'D:\UnitPortalBackend-main\dhupapiikeacom.pem', r'D:\UnitPortalBackend-main\dhupapiikeacom.key')
                            if env == "production"
                            else ((r'D:\UP-Curfu\UnitPortalBackend\flask.cer', r'D:\UP-Curfu\UnitPortalBackend\flask.key')
                            if env == "preproduction" else None)
        #ssl might not be necessary in container since there will be an extra layer where will be using ssl
    }


def run_app(app):
    app.run(
        **get_runtime()
    )


if __name__ == '__main__':
    log_file = LoggingFile()
    log_file.archive_main_log_file() # Touches logger file and needs to be handle before some services start
    app = prepare_app()
    log_file.perform_logging_info() # gives info from archive_main_log_file method to new main_log file
    app.config.from_object(SchedulerConfig())
    atm_scheduler.init_app(app)     # TODO
    auto_maintenance_scheduler.start()
    seamless_lcm_scheduler.start()
    # legacy scheduler
    add_scheduler()
    # from waitress import serve
    # serve(app, host="0.0.0.0", port=5000)
    # use different ports for different developer.

    @app.before_request
    def validate_token():
        PrivilegeValidation.validate_authorization()
    run_app(app)
else:
    # generate the app here, so flask db migrate can work.
    app = prepare_app()
    

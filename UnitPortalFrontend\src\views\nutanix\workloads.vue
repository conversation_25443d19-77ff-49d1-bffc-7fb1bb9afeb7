<template>
  <div class="app-container" style="height:100%;width:100%">
    <div class="filter-container">
        <el-row :gutter="5" >
          <el-col :span="3" >
            <el-button class="filter-item"  type="success" @click="handle_create">
              Create
            </el-button>
          </el-col>
          <el-col :span="4" :offset="17" >
            <el-button style='float:right' class="filter-item"  type="danger" >
              Delete
            </el-button>
          </el-col>
        </el-row>
    </div>
    Hover you mouse on ID to see SPECS
    <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="workloadtasklist"
        border
        fit
        highlight-current-row
        style="width: 100%;"
        @sort-change="sortChange"
        @row-click="handle_row_click"
        class='template-table'
        ref='pctable'
      >
  
        <!-- <el-table-column label="ID" min-width="3%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="ID" min-width="3%" align="center" >
          <template slot-scope="{row}">
          <el-popover placement="right" trigger="hover" title="SPECS">
            <div>
              Prism:{{ row.pc }}<br>
              Cluster:{{ row.pe }}<br>
              CPU:{{ row.cpu }}<br>
              Core:{{ row.cpu_core }}<br>
              Memory:{{ row.memory }}<br>
              Disk:{{ row.disk }}<br>
              Vlan:{{ row.vlan_id }}<br>
            </div>
            <span slot="reference">{{ row.id }}</span>
          </el-popover>
        </template>
        </el-table-column>
        <el-table-column label="VM name"  min-width="8%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="Cluster" class-name="status-col" min-width="10%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.pe }}</span>
          </template>
        </el-table-column>

        <el-table-column label="VM Type" min-width="5%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.workload_type }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="Image" class-name="status-col" min-width="7%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.image_name }}</span>
          </template>
        </el-table-column>

        <el-table-column  label="VlanID" min-width="5%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.vlan_id}}</span>
          </template>
        </el-table-column>

        <el-table-column label="Latest log(click to show all logs)" align="center" min-width="18%">
        <template slot-scope="{row}">
          <span>{{ row.latestlog.loginfo }}</span><span class="link-type"  @click="handleFetchBriefLog(row)">     More</span>
        </template>
      </el-table-column>
      
        <el-table-column label="Status" class-name="status-col" min-width="6%" align="center" >
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusFilter">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
        <el-table-column  label="Creater" min-width="5%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.creater}}</span>
          </template>
        </el-table-column>
    </el-table>
  
    <!-- <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="" /> -->
  
    <el-dialog  :visible.sync="dialogFormVisible" style="width:100%;  margin-left:2.5%; margin-top:-5%;padding:0px;" :close-on-click-modal="false">
      <div style="width:100%;text-align: center;font-size:35px;">Create VM</div>
      <div style="border-top: 0.1px solid rgb(185, 182, 182);width:100%;height:0;margin-top:1%"></div>
      <div style="height:100%;width:100%">
        <div style="width:60%;margin-top: 2%;float:left;border-right: 1px solid rgb(241, 234, 234)">

          <el-form ref="vm_form" :rules="rules" label-position="left" :model="config" style="width: 100%; ">

            <div class="label">
              Loacation
            </div>

            <el-form-item  prop="pc" class="form_item"> 
              <div class="form-label"><span>Prism</span></div>
              <el-select v-model="config.pc" placeholder="Prism" class="filter-item" style="width:60%">
                <el-option v-for="item in prismOptions" :key="item.key" :label="item.display_name+'('+item.key+')'" :value="item.display_name" />
              </el-select>
            </el-form-item>

            <el-form-item prop="pe" class="form_item" >
              <div class="form-label"><span>Cluster</span></div>
              <el-select v-model="config.pe" class="filter-item" placeholder="Cluster" filterable style="width:60%">
                <el-option v-for="item in peTypeKeyValue[config.pc]" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>

            <div class="label" >
              <span>Basic specs</span><el-button type="primary" style="height:20px;line-height:1px;float:right;margin-right: 20px;" >load</el-button>
              <div style="clear:both"></div>
            </div>

            <el-form-item  prop="name" class="form_item">
              <div class="form-label"><span>Name</span></div>
              <el-input v-model="config.name" maxlength="20" placeholder="VM name" show-word-limit type="text" style="width:60%" />
            </el-form-item>

            <el-form-item class="form_item">
              <div class="form-label"><span>OS </span></div>
              <el-radio v-model="config.workload_type" label="windows" >Windows</el-radio>
              <el-radio v-model="config.workload_type" label="linux" >Linux</el-radio>
              <el-radio v-model="config.workload_type" label="network" >Network appliance</el-radio>
            </el-form-item>

            <el-form-item  prop="image_id" class="form_item" > 
              <div class="form-label"><span>Image</span></div>
              <el-select v-model="config.image_id" placeholder="Image" class="filter-item" style="width:60%">
                <el-option v-for="item in imageOptions[config.workload_type]" :key="item.key" :label="'ID:'+item.key + ' Name:'+item.display_name" :value="item.key" />
              </el-select>
            </el-form-item>

            <div  class="form_item"> 
              <el-form-item  prop="cpu"   style="float:left; width:50%"> 
                <div class="form-label_half_left"><span>CPU</span></div>
                <el-input v-model="config.cpu" placeholder="CPU"  style="width:40%;"/>
              </el-form-item>

              <el-form-item  prop="cpu_core" style="float:left;"> 
                <div class="form-label_half_right"><span>Cores</span></div>
                <el-input v-model="config.cpu_core" placeholder="Cores" style="width:66%"/>
              </el-form-item>
              <div style="clear:both"></div>
            </div>

            <div  class="form_item"> 
              <el-form-item  prop="memory"  style="float:left;width:50%"> 
                <div class="form-label_half_left"><span>Ram</span></div>
                <el-input v-model="config.memory" placeholder="Ram"  style="width:40%;"/>
              </el-form-item>

              <el-form-item  prop="disk" style="float:left;"> 
                <div class="form-label_half_right"><span>Disk</span></div>
                <el-input v-model="config.disk" placeholder="Seprate by ',' (GB)" style="width:70%"/>
              </el-form-item>
              <div style="clear:both"></div>
            </div>

            <div  class="form_item"> 
              <el-form-item  prop="vlan_id"  style="float:left;width:50%"> 
                <div class="form-label_half_left"><span>Vlan</span></div>
                <el-input v-model="config.vlan_id" placeholder="Vlan"  style="width:40%;"/>
              </el-form-item>

              <el-form-item  prop="dns"  style="float:left;width:45%;"> 
                <el-checkbox v-model="config.dns" style="float:left;margin-left:10%"/>
                <div style="width:60%;float:left;margin-left:5%"><span>Register DNS</span></div>
              </el-form-item>

              <div style="clear:both"></div>
            </div>
          </el-form>


          <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="add_to_cart" style="float: right;margin-right: 20px;">
            Add to cart
          </el-button>
          </div>
        </div>
        <div style="width:38%;margin-top: 2%;float:left;height:100%">
          <div style="height:520px; overflow-y:auto;margin-bottom: 10px;">
            <div class="dh-card is-always-shadow" v-for="(vm,index) in cart">
              <div  class="dh-card-header ">
                  <div >
                    
                    <div class="vm-name " v-on:click="vm.display = !vm.display;">
                      {{vm.name}}
                    </div>

                    <div class="btn-groups">
                      <el-button class="dh-btn" type="danger" @click="remove_from_cart(vm)">x</el-button> 
                    </div>
                    
                    <div style="clear:both"></div>
                  </div>

              </div>
              <div class="collapse" v-collapsible="vm.display">
                <div class="collapse-whole"> prism: {{ vm.pc }}</div>
                <div class="collapse-whole"> cluster: {{ vm.pe }}</div>
                <div class="collapse-left"> cpu: {{ vm.cpu }}</div><div class="collapse-right"> cpu core: {{ vm.cpu_core }}</div>
                <div class="collapse-left"> ram: {{ vm.memory }}</div><div class="collapse-right"> vlan: {{ vm.vlan_id }}</div>
                <div class="collapse-whole"> image ID: {{ vm.image_id }}</div>
                <div class="collapse-whole"> register dns in ipam: {{ vm.dns }}</div>
                <div class="collapse-left"> os: {{ vm.workload_type }}</div><div class="collapse-right"> disk: {{ vm.disk }}</div>
                <div style="clear:both"></div>
              </div>
            </div>
          </div>
          <div style="height:40px; overflow-y:auto">
            <el-button type="success" @click="create_vm" style="float: right;margin-right: 10px;">
              Install
            </el-button>
          </div>
        </div>
        <div style="clear:both"></div>
      </div>
    </el-dialog>
  </div>
</template>

  <script>
  import {GetWorkloadInfo, GetWorkloadTaskList, CreateWorkload, AddPrism,GetPrismList, GetPCPECorrespondence} from '@/api/nutanix'
  import {GetSAList} from '@/api/common'
  import waves from '@/directive/waves' // waves directive
  import { parseTime } from '@/utils'
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination


  
  export default {
    name: 'WorkloadTable',
    components: { Pagination },
    directives: { waves },
    filters: {
      statusFilter(status) {
        let st = status.toLowerCase();
        const statusMap = {
          'not started': 'info',
          'done': 'success',
          'error': 'danger',
          'in progress':'primary'
        }
        return statusMap[st]
      }
    },
    data(){
      const disk_validator =(rule, value, callback)=>{//磁盘大小的验证方法
        if(value.match(/^[0-9]+$|[0-9]+(,[0-9]+)+/)){
          let disk_list = value.split(",")
          for (let _d of disk_list){
            if(parseInt(_d) > 10240){
              callback(new Error("You need a disk lager than 10TB?"))
            }
          }
          callback()
        }
        else if( value.trim()==''){
          callback(new Error("Disk is required."))
        }
        else{
          callback(new Error("Format should be '100,50,100'."))
        }
      }

      const vlan_validator =(rule, value, callback)=>{//vlan合规性的验证方法
        if(value.match(/^[0-9]+$/)){
          callback()
        }
        else{
          callback(new Error("Only numbers allowed."))
        }
      }

      return {
        displayDiv: false,
        prismtier:[
          'Production',
          'PreProduction',
          'IKEADT',
          'IKEAD2'
        ],
        cart:[],
        tableKey: 0,
        list: null,
        workloadtasklist : null,
        prismlist: null,
        total: 0,
        listLoading: true,
        listQuery: {
          page: 1,
          limit: 20,
          cluster: '',
          prism: '',
          status: '',
          sort: '+id'
        },
        statusToShowEditButton:['Not Started'],
        statusToShowAbortButton:['In Progress'],
        statusToShowDeleteButton:['Not Started','Done','Error','Aborted'],
        statusOptions: ['Not Started','In Progress','Done','Error','Aborted'],
        prismOptions:[],
        peTypeKeyValue:{},
        imageOptions:{},
        sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
        // statusOptions: ['published', 'draft', 'deleted'],
        ShowCreationDate: false,
        config:{
          pc                 : "SSP-PPE-NTX.IKEA.COM",
          pe                 : "RETCN888-NXC000.IKEA.COM",
          name               : "POSCN888-WS1451",
          workload_type      : "windows",
          image_id           : "6",
          cpu                : "1",
          cpu_core           : "2",
          vlan_id            : "135",
          dns                : false,
          memory             : "8",
          disk               : "150,100",
          description        : "POS client",
          display            : false,
          boot_mode          : '2',
          oscustomization_id : '0'
        },
        selectedrow:'',
        dialogFormVisible: false,
        dialogPvVisible: false,
        logdata: [],
        rules: {
          pc: { required: true, message: 'Prism is required', trigger: 'change' },
          pe: { required: true, message: 'Cluster is required', trigger: 'change' },
          name: {required: true, message: 'Name is required', trigger: 'change' },
          image: {required: true, message: 'Image is required', trigger: 'change' },
          cpu: {required: true, message: 'Cpu is required', trigger: 'change' },
          cpu_core: {required: true, message: 'Core is required', trigger: 'change' },
          memory: {required: true, message: 'Memory is required', trigger: 'change' },
          disk: {required: true, trigger: 'change', validator:disk_validator},
          vlan_id: {required: true, trigger: 'change', validator:vlan_validator},
          // timestamp: { type: 'date', required: true , trigger: 'change' , validator:validateTime}
        },
        downloadLoading: false,
        intervaljob:''
      }
    },
    created() {
      this.getPCList()
      this.getPEList()
      this.get_workload_task_list()
      this.get_workload_info()
    },
    methods: {      
      getPCList(){
        GetPrismList(this.$store.getters.token).then(response => {
          for(let pc of response['data']){
            pc['fqdn'].match(/.*-(.*)-.*/)
            let k = RegExp.$1
            this.prismOptions.push({"key": k.toUpperCase(), "display_name":pc['fqdn'].toUpperCase()})
          }
          })
      },    
      getPEList(){
        GetPCPECorrespondence(this.$store.getters.token).then(response => {
          this.peoptions = response['data']
          this.peTypeKeyValue = this.peoptions.reduce((acc, cur) => {
            acc[cur.pc] = cur.pe.sort()
            return acc
          }, {})
        })
      },
      get_workload_info(){
        GetWorkloadInfo(this.$store.getters.token).then(response => {
          let data = response.data
          let image = data.image
          let template = data.template
          let windows = []; let linux= []; let network = []
          for(let im of image){
            if (im.os_type == 'windows'){
              windows.push({'key':im.id,'display_name':im.name})
              continue
            }
            if (im.os_type == 'linux'){
              linux.push({'key':im.id,'display_name':im.name})
            }
            if (im.os_type == 'network'){
              network.push({'key':im.id,'display_name':im.name})
            }
          }
          this.imageOptions['windows'] = windows
          this.imageOptions['linux'] = linux
          this.imageOptions['network'] = network
        })
      },
      get_workload_task_list(){
        this.listLoading = true
        GetWorkloadTaskList(this.$store.getters.token).then(response => {
          this.workloadtasklist = response.data
          this.total = response.data.length
          this.listLoading = false
        })
      },
      handleFilter() {
        this.listQuery.page = 1
      },
      sortChange(data) {
        const { prop, order } = data
        if (prop === 'id') {
          this.sortByID(order)
        }
      },
      sortByID(order) {
        if (order === 'ascending') {
          this.listQuery.sort = '+id'
        } else {
          this.listQuery.sort = '-id'
        }
        this.handleFilter()
      },
      resetTemp() {
        let localtime = new Date()
        let utctime =new Date( localtime.getTime() + 60*1000*localtime.getTimezoneOffset())
      },
      handle_create() {
        // this.resetTemp()
        this.dialogFormVisible = true
        // this.$nextTick(() => {
        //   this.$refs['dataForm'].clearValidate()
        // })
      
        // CreateWorkloadTask(this.$store.getters.token)
      },
      handle_delete(){
        console.log( this.selectedrow)
         this.$confirm('You sure??', '?!', {
            confirmButtonText: 'YES',
            cancelButtonText: 'NO',
            type: 'danger'
          }).then(() => {
            this.$message({
              type: 'success',
              message: 'I am not gonna let you do this....'
            });
          }).catch(() => {
            this.$message({
              type: 'info',
              message: 'I am not gonna let you do this....'
            });          
          });
      },
      handle_row_click(row,column,event){
        this.selectedrow = row
      },
      // handleAbort(row){
      //   let payload = {
      //     data:row,
      //     token: this.$store.getters.token
      //   }
      //   AbortNTXPM(payload).then(() => {
      //         this.$notify({
      //           title: 'Success',
      //           message: 'PM aborted',
      //           type: 'info',
      //           duration: 2000
      //         })
      //       }
      //   )
      //   .catch((error)=>{
      //     this.$notify({
      //           title: 'Error',
      //           message: error.response.data.message,
      //           type: 'error',
      //           duration: 2000
      //       })
      //   })
      // },
      // handleDelete(row){
      //   let payload = {
      //     data:{id:row.id},
      //     token: this.$store.getters.token
      //   }
      //   DeleteNTXPM(payload).then(()=>{
      //     this.$notify({
      //           title: 'Success',
      //           message: 'PM task was deleted.',
      //           type: 'success',
      //           duration: 2000
      //         })
      //   })
      //   .catch((error)=>{
      //     this.$notify({
      //           title: 'Failed',
      //           message: error.response.data.message,
      //           type: 'error',
      //           duration: 2000
      //         })
      //   })
      // },
      add_to_cart(){
        this.$refs['vm_form'].validate((valid)=>{
          //as the rules, validate the form, to see if there is anything not valid
          if(valid){
            for(let item of this.cart){
              if  (this.config.name == item.name &&
                  this.config.prism == item.prism &&
                  this.config.cluster == item.cluster
              ){
                this.$message({
                  type: 'error',
                  message: 'Dupicate VM name in the same cluster.'
                });
                return
              }
            }
            this.cart.push(Object.assign({},this.config)) // need to use deep copy here, Object.assign()
          }
        })
      },
      remove_from_cart(vm){
        this.cart = this.cart.filter((item)=>{
          return (item.prism != vm.prism ||
            item.cluster != vm.cluster ||
            item.name != vm.name)
        })
      },
      create_vm(){
        let payload = {
          data:this.cart,
          token:this.$store.getters.token
        }
        CreateWorkload(payload)
        .then((response)=>{
          console.log(response)
          for (let vm of response['data']){
            console.log(vm)
            if(vm['flag']){
              this.$notify({
                title: vm["name"],
                message: vm["message"],
                type: 'success',
                duration: 5000
              })
            }
            else{
              this.$notify({
                title: vm["name"],
                message: vm["message"],
                type: 'error',
                duration: 5000
              })
            }
          }
        this.get_workload_task_list()
        })
        .catch((error)=>{
            this.$notify({
                title: 'Error',
                message: 'VM task failed',
                type: 'error',
                duration: 5000
              })
        })
        this.dialogFormVisible = false

      },
      // createPM() {
      //   let dt = this.temp.timestamp
      //   let scheduledate =new Date( dt.getTime() - 60*1000*dt.getTimezoneOffset()) // add timezone math 
      //   let payload = {
      //     data:{        
      //       "prism": this.temp.prism,
      //       "cluster": this.temp.cluster,
      //       "startnow": this.temp.datatimepickerdisabled,
      //       "scheduledate": scheduledate,
      //       "pmtype": this.temp.pmtype==1?"poweroff":"poweron",
      //       "description": this.temp.description ?this.temp.description:"webpage entrance"
      //     },
      //     token: this.$store.getters.token
      //   }
      //   this.$refs['dataForm'].validate((valid) => {
      //     if (valid) {
      //       CreateNTXPM(payload)
      //       .then(() => {
      //         this.$notify({
      //           title: 'Success',
      //           message: 'PM Created Successfully',
      //           type: 'success',
      //           duration: 2000
      //         })
      //         this.dialogFormVisible = false
      //         this.getList()
      //       })
      //       .catch((error) => {
      //         this.dialogFormVisible  = false
      //         this.$notify({
      //           title: 'Error',
      //           message: 'Failed to create PM.',
      //           type: 'error',
      //           duration: 2000
      //         })
      //       })
      //     }
      //   })
      //   return 0
      // },

      // updatePM() {
      //   let dt = this.temp.timestamp
      //   let scheduledate =new Date( dt.getTime() - 60*1000*dt.getTimezoneOffset()) // add timezone math 
      //   let payload = {
      //     data:{        
      //       "id": this.temp.id,
      //       "prism": this.temp.prism,
      //       "cluster": this.temp.cluster,
      //       "startdate":this.temp.datatimepickerdisabled?'startnow':scheduledate,
      //       "pmtype": this.temp.pmtype==1?"poweroff":"poweron",
      //       "description": this.temp.description ?this.temp.description:"webpage entrance"
      //     },
      //     token: this.$store.getters.token
      //   }
      //   this.$refs['dataForm'].validate((valid) => {
      //     if (valid) {
      //       UpdateNTXPM(payload)
      //       .then(() => {
      //         this.$notify({
      //           title: 'Success',
      //           message: ' PM Updated Successfully',
      //           type: 'success',
      //           duration: 2000
      //         })
      //         this.dialogFormVisible = false
      //         this.getList()
      //       })
      //       .catch((error) => {
      //         console.log(error)
      //         console.log(error.response)
      //         this.dialogFormVisible = false
      //         this.$notify({
      //           title: 'Error',
      //           message: error.response.data.message,
      //           type: 'error',
      //           duration: 2000
      //         })
      //       })
      //     }
      //   })
      // },
      // downloadLogFile(){
      //   if (!this.selectedrow.detaillogpath){
      //     this.$notify({
      //           title: 'Ooooops',
      //           message: 'No log yet!',
      //           type: 'info',
      //           duration: 2000
      //         })
      //   }
      //   let payload = {
      //     data:{  id:this.selectedrow.id,
      //             filepath:this.selectedrow.detaillogpath},
      //     token: this.$store.getters.token
      //   }
      //   DownloadNTXPMLog(payload)
      //   .then((response)=>{
      //     const href = URL.createObjectURL(response.data);
      //     // create "a" HTML element with href to file & click
      //     const link = document.createElement('a');
      //     link.href = href;
      //     link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
      //     document.body.appendChild(link);
      //     link.click();
      //     // clean up "a" element & remove ObjectURL
      //     document.body.removeChild(link);
      //     URL.revokeObjectURL(href);
      //   })
      // },
      handleFetchBriefLog(row) {
        this.selectedrow = row
        this.logdata = row.logs
        this.dialogPvVisible = true
      },
      formatJson(filterVal) {
        return this.list.map(v => filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        }))
      },
      getSortClass: function(key) {
        const sort = this.listQuery.sort
        return sort === `+${key}` ? 'ascending' : 'descending'
      },
      // add_prism(){
      //   console.log(this.temp)
      //   return
      //   let newsa = this.addprismform.newserviceaccount == '1'? false : true
      //   let sausername, sapassword
      //   if(newsa){
      //     sausername = this.addprismform.serviceaccountname
      //     sapassword = this.addprismform.serviceaccountpassword
      //   }
      //   let payload = {
      //     data:{        
      //       "fqdn": this.addprismform.fqdn,
      //       "tier": this.addprismform.tier,
      //       "newsa": newsa,
      //       "currentsa":this.addprismform.existingserviceaccount,
      //       "sausername":sausername,
      //       "sapassword":sapassword
      //     },
      //     token: this.$store.getters.token
      //   }
      //   console.log(payload)
      //   this.$refs['dataForm'].validate((valid) => {
      //     if (valid) {
      //       AddPrism(payload)
      //       .then(() => {
      //         this.$notify({
      //           title: 'Success',
      //           message: 'Prism added.',
      //           type: 'success',
      //           duration: 2000
      //         })
      //         this.dialogFormVisible = false
      //       })
      //       .catch((error) => {
      //         this.dialogFormVisible  = false
      //         this.$notify({
      //           title: 'Error',
      //           message: 'Failed to add Prism.',
      //           type: 'error',
      //           duration: 2000
      //         })
      //       })
      //     }
      //   })
      //   return 0
      // }
    },
    beforeDestroy(){
      clearInterval( this.intervaljob )
    }
  }
  </script>
  <style scoped>
   .template-table span{
    
       font-size: 17px
  }
  .show-pwd {
      position: absolute;
      right: 10px;
      top: 7px;
      font-size: 16px;
      cursor: pointer;
      user-select: none;
    }

  .label{
    margin-left:2%;
    margin-bottom:3%;
    width:96%;
    color:rgb(43, 35, 33);
    font-size:large;
    /* border-bottom:0.5px solid rgb(235, 230, 230) */
  }
  .form-label{
    color:gray;
    width:10%;
    float:left;
    margin-left:10%;
  }
  .form-label_half_left{
    color:gray;
    width:20%;
    float:left;
    margin-left:20%;
  }
  .form-label_half_right{
    color:gray;
    width:20%;
    float:left;
    margin-left:5%
  }
  .form_item{
    margin-left:1%;
  
  }
 body, html{
  height:100%
 }
  </style>

  <style lang="scss" >
    .el-dialog{
      .el-dialog__header{
        padding:0px;
      }
    }
    .el-form-item__error{
      margin-left:10%
    }
    .form-label_half_left ~ .el-form-item__error{
      margin-left:20%
    }
    .form-label_half_right ~ .el-form-item__error{
      margin-left:5%
    }
    .is-always-shadow {
      box-shadow: 1px 1px 3px 0.5px rgba(0, 0, 0, 0.1);
    } 
    .dh-card{
      border-radius: 4px;
      border: 1px solid #e6ebf5;
      background-color: #ffffff;
      overflow: hidden;
      color: #303133;
      transition: 0.3s;
      margin-left:3%;
      margin-bottom: 10px;
      width:96%;
    }

    .dh-card-header {
      border-radius: 4px;
      border: 1px solid #e6ebf5;
      background-color: #ffffff;
      overflow: hidden;
      color: #303133;
      transition: 0.3s;
      height:40px;
      width:100%;
      cursor:pointer;
    }
    .vm-name{
      width:92%;
      height:40px;
      float:left;
      font-size: large;
      text-align: center;
      padding:5px
    }
    .btn-groups{
      width:8%;
      height:40px;
      float:right;
    }
    .dh-btn{
      float:right;
      height:40px;
      border-radius: 0px;
    }
    .collapse-whole{
      float:left;
      margin-left:2%;
      width:100%
    }
    .collapse-left{
      float:left;
      margin-left:2%;
      width:40%
    }
    .collapse-right{
      float:right;
      margin-right:6%;
      width:50%
  }
   </style>
from business.authentication.tokenvalidation import PrivilegeValidation
from flask_restful import Resource
from sqlalchemy.sql import func
from models.database import db
from models.atm_models import ModelNtxAutomationRotatePasswordTask, ModelNtxAutomationDscTask, \
    ModelNtxAutomationRenewCertificateTask, ModelRetailNutanixAutomationAOSLCMTask, \
    ModelRetailNutanixAutomationSPPLCMTask
from models.cluster_models import ModelClusterTask
from models.ntx_models import ModelRetailMOVETask
from models.pm_models import ModelNTXPMTask, ModelSLIPMTask
from models.workload_models import ModelWorkloadTask
from .route import route


@route('/api/v1/statistic/tasks')
class RestfulTasks(Resource):
    @PrivilegeValidation(privilege={"role_dashboard": "view"})
    def get(self):
        """
        Get all tasks
        ---
        tags:
          - tasks
        responses:
          200:
            description: Get all tasks
        """
        tasks = {
            "ntx_pm": db.session.query(func.count(ModelNTXPMTask.id)).scalar(),
            "sli_pm": db.session.query(func.count(ModelSLIPMTask.id)).scalar(),
            "create_workload": db.session.query(func.count(ModelWorkloadTask.id)).filter(ModelWorkloadTask.task_type == "CREATE").scalar(),
            "delete_workload": db.session.query(func.count(ModelWorkloadTask.id)).filter(ModelWorkloadTask.task_type == "DELETE").scalar(),
            "rotate_password": db.session.query(func.count(ModelNtxAutomationRotatePasswordTask.id)).scalar(),
            "renew_certificate": db.session.query(func.count(ModelNtxAutomationRenewCertificateTask.id)).scalar(),
            "desired_state_config": db.session.query(func.count(ModelNtxAutomationDscTask.id)).scalar(),
            "aos_lcm": db.session.query(func.count(ModelRetailNutanixAutomationAOSLCMTask.id)).scalar(),
            "spp_lcm": db.session.query(func.count(ModelRetailNutanixAutomationSPPLCMTask.id)).scalar(),
            "move": db.session.query(func.count(ModelRetailMOVETask.id)).scalar(),
            "create_cluster": db.session.query(func.count(ModelClusterTask.id)).scalar(),
        }
        return tasks

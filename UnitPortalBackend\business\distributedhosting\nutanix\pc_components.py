import inspect
import uuid

import werkzeug.exceptions as flaskex
from business.distributedhosting.nutanix.nutanix import PrismCentral


class RestCategories(PrismCentral):
    """/api/prism/v4.0.a2/config/categories"""
    ENDPOINT = "/prism/v4.0.a2/config/categories"

    def create_category(self, payload):
        """v4.0.a2"""
        res, data = self.rest_pc.call_pc_post(self.__class__.ENDPOINT, payload=payload, api_version=4)
        if not res:
            raise flaskex.BadGateway(f"Failed to {inspect.currentframe().f_code.co_name.replace('_', ' ')}! Reason: {data}")
        return data


class RestNetwork(PrismCentral):
    """https://{pc-ip}:9440/api/networking/v4.0/config/subnets"""
    ENDPOINT = "/networking/v4.0/config/subnets"

    def get_networks(self):
        self.logger.info("Getting networks...")
        res, data = self.rest_pc.call_pc_get(self.__class__.ENDPOINT, api_version=4)
        if not res:
            raise flaskex.BadGateway(f"Failed to {inspect.currentframe().f_code.co_name.replace('_', ' ')}! Reason: {data}")
        return data

    def get_network(self, ext_id):
        self.logger.info(f"Getting network {ext_id}...")
        # Call low level function in order to get header
        url = f"{self.rest_pc.pc_version_url[4]}{self.__class__.ENDPOINT}/{ext_id}"
        res = self.rest_pc.rest.call_restapi(url=url, method="GET")
        if not res:
            raise flaskex.BadGateway(
                f"Failed to {inspect.currentframe().f_code.co_name.replace('_', ' ')}! Reason: {res.text}")
        return res

    def create_networks(self, name, vlan_id, cluster_uuid):
        self.logger.info("Creating network...")
        for arg_name, arg_value in locals().items():
            if arg_name != 'self':
                self.logger.info(f"{arg_name}={arg_value}")
        headers = {
            "NTNX-Request-Id": str(uuid.uuid4())
        }
        payload = {
            "name": name,
            "subnetType": "VLAN",
            "networkId": vlan_id,
            "clusterReference": cluster_uuid
        }
        res, data = self.rest_pc.call_pc_post(self.__class__.ENDPOINT, api_version=4, payload=payload, headers=headers)
        if not res:
            raise flaskex.BadGateway(f"Failed to {inspect.currentframe().f_code.co_name.replace('_', ' ')}! Reason: {data}")
        return data

    def update_networks(self, e_tag, name, network_uuid):
        self.logger.info("Updating network...")
        headers = {
            "If-Match": e_tag,
            "NTNX-Request-Id": str(uuid.uuid4())
        }
        payload = {
            "name": name,
            "subnetType": "VLAN",
        }
        res, data = self.rest_pc.call_pc_put(f"{self.__class__.ENDPOINT}/{network_uuid}", api_version=4, payload=payload, headers=headers)
        if not res:
            raise flaskex.BadGateway(f"Failed to {inspect.currentframe().f_code.co_name.replace('_', ' ')}! Reason: {data}")
        return data



class RestProtectionRules(PrismCentral):
    """/api/nutanix/v3/protection_rules"""
    ENDPOINT = "/protection_rules"

    def create_protection_rules(self, payload):
        res, data = self.rest_pc.call_pc_post(self.__class__.ENDPOINT, payload=payload)
        if not res:
            raise flaskex.BadGateway(f"Failed to {inspect.currentframe().f_code.co_name.replace('_', ' ')}! Reason: {data}")
        return data


class RestRecoveryPlans(PrismCentral):
    """/api/nutanix/v3/recovery_plans"""
    ENDPOINT = "/recovery_plans"

    def list_recovery_plans(self, length=50):
        res, data = self.rest_pc.call_pc_post(
            f"{self.__class__.ENDPOINT}/list", payload={"kind": "recovery_plan", "length": length, "offset": 0}
        )
        if not res:
            raise flaskex.BadGateway(f"Failed to {inspect.currentframe().f_code.co_name.replace('_', ' ')}! Reason: {data}")
        return data

    def create_recovery_plans(self, payload):
        res, data = self.rest_pc.call_pc_post(self.__class__.ENDPOINT, payload=payload)
        if not res:
            raise flaskex.BadGateway(f"Failed to {inspect.currentframe().f_code.co_name.replace('_', ' ')}! Reason: {data}")
        return data


class RestVolumeGroup(PrismCentral):
    """/api/volumes/v4.0/config/volume-groups"""
    ENDPOINT = "/volumes/v4.0/config/volume-groups"

    def create_volume_groups(self, payload):
        res, data = self.rest_pc.call_pc_post(self.__class__.ENDPOINT, payload=payload, api_version=4)
        if not res:
            raise flaskex.BadGateway(f"Failed to {inspect.currentframe().f_code.co_name.replace('_', ' ')}! Reason: {data}")
        return data
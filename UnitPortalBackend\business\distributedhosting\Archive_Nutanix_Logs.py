#Python Script
#Created by : <PERSON><PERSON>
#28-July-2023
#<PERSON>ript to extract all the data in a Nutanix log file which are older than 30 days from specified folders and copy to a text file then zipit with date_time format.
#Need to schedule this activity based on the input parameter(ex weekly/Mothly/quarterly/yearly).


import os
import shutil
import datetime
import win32com.client
import pathlib
#from datetime import datetime, date, timedelta
import os.path, time  
import zipfile


class Task_Archive_Nutanix_Log_Files:
    #Global declaration and assign values.
    global today
    global weekday
    #days_ago = datetime.datetime.now() - datetime.timedelta(days=55)
    
    def __init__(self):
        
        self.schedular = win32com.client.Dispatch('Schedule.Service')
        self.schedular.Connect()
        #root_folder = schedular.GetFolder('\\UnitPortal')
        self.task_def = self.schedular.NewTask(0)
        #shutil.make_archive(file2, 'zip', dir_name)
        self.arc_formats = shutil.get_archive_formats()
        self.today = datetime.datetime.now()
        self.weekday = self.today.weekday()
        #Action to get current date and time
        self.current_datetime = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        self.str_current_datetime = str(self.current_datetime)
        self.datetimedelta = datetime.timedelta(days = 30)
        self.archive_name = self.str_current_datetime+"_Backup_Nutanix_files"
        self.directory_path = 'C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\Log_Files'
        self.destination_folder = 'C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\Zip_files\\'
        self.target = 'C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\Logs_Archive'
   
# Trigger time
#Set_time = datetime.datetime(2022,9,4,18,44,0,500000)
    def task_trigger(self):
        set_time = datetime.now() + datetime.timedelta(minutes=1)
        TASK_TRIGGER_TIME = 1
        trigger = self.task_def.Triggers.Create(TASK_TRIGGER_TIME)
        trigger.StartBoundary = set_time.isoformat()
        
#Set value for day to run the script accordingly.(this function is not yet completed)   
    def task_run_weekly(dayvalue,self):
        if (weekday == dayvalue):
         #run my script 0 = monday , 1 = tuestday...
         true = 0
        else:
         true = 1

#Return all the sub directories files from the path.      
    def get_all_files_in_subdirectories(self,directory):
    
     all_files = []

     for root, dirs, files in os.walk(directory):
            for file_name in files:
                file_path = os.path.join(root, file_name)
                all_files.append(file_path)

     return all_files
    
#Return all the files older than 30 days.     
    def get_files_older_than_30_days(self,directory):
    
     days_ago = datetime.datetime.now() - datetime.timedelta(days = 30)
     #days_ago = self.current_datetime - self.datetimedelta
     older_files = []
    
     for root, dirs, files in os.walk(directory):
        for file_name in files:
            file_path = os.path.join(root, file_name)
            file_modification_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
            if file_modification_time < days_ago:
                older_files.append(file_path)
     return older_files
 
# Function to remove all files and folders in a directory
    def remove_files_and_folders(self,directory):
        
        for item in os.listdir(directory):
             item_path = os.path.join(directory, item)
             if os.path.isfile(item_path):
                os.remove(item_path)
             elif os.path.isdir(item_path):
                shutil.rmtree(item_path)        
        
#Action to open file and read and write to another file for archiving the data.
    def Archive_Nutanix_files(self):
            
     #directory_path = 'C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\Log_Files'

     all_files = self.get_all_files_in_subdirectories(self.directory_path)
     older_files = [file for file in all_files if os.path.isfile(file) and file in self.get_files_older_than_30_days(self.directory_path)]

     #print("Files older than 30 days:")
     
    # Destination path
     #target = 'C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\Logs_Archive'
     for file_path in older_files:
        shutil.move(file_path,self.target)
        
            
    #Action to Check for existing file in the zip folder which needs to be deleted.
     #destination_folder = 'C:\\IKEA Infra\\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\Zip_files\\'
     
     # Create the destination folder if it doesn't exist
     if not os.path.exists(self.destination_folder):
        os.makedirs(self.destination_folder)

    # Zip the source folder
     source_folder = self.target
     archive_path = os.path.join(self.destination_folder, f'{self.archive_name}.zip')
     with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for foldername, subfolders, filenames in os.walk(source_folder):
            for filename in filenames:
                file_path = os.path.join(foldername, filename)
                archive_file_path = os.path.relpath(file_path, source_folder)
                zipf.write(file_path, archive_file_path)
           
     print("\n")
     print("30 days old data from Nutanix file has been Zipped.")
    
    #Action to clear the Log data from Archive folder.
   
     self.remove_files_and_folders(self.target)
     #file_erase = open(self.target,'w')
     #file_erase.write("")
     #file_erase.close()
     print("Archive folder has been empted now:")
     
#Calling Class object to invoke the methods/defination.
funTaskArchive = Task_Archive_Nutanix_Log_Files()
funTaskArchive.Archive_Nutanix_files()
#funTaskArchive.remove_files_and_folders()
from marshmallow import Schema, fields, validates_schema, ValidationError, validate

from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.nutanix import NutanixOperation
from models.benchmark_models import ModelNtxBenchmark


class ListPeVmRequestSchema(Schema):
    pe = fields.Str(required=True)


class PeVmSchema(Schema):
    name = fields.Str()
    state = fields.Str()
    is_controller_vm = fields.Bool()
    uuid = fields.Str()


class UpdatePeRequestSchema(Schema):
    fqdn = fields.Str(required=True)
    status = fields.Str(validate=validate.OneOf(["Running", "Unreachable", "Decommissioned"]))
    remote_site_dsc = fields.Str()
    remote_site_runtime = fields.Str()
    remote_site_last = fields.Str()
    bandwidth_dsc = fields.Float(validate=validate.Range(min=0, min_inclusive=False))
    bmk_id = fields.Int()

    def validate_for_wh(self, data):
        unsupported_keys_for_wh = ['remote_site_dsc', 'bandwidth_dsc', 'remote_site_runtime', 'remote_site_last']
        for key in unsupported_keys_for_wh:
            if data.get(key):
                raise ValidationError(f"{key} is not applicable for {FacilityType.WAREHOUSE} facility type.")

    def validate_bmk_id(self, bmk_id):
        try:
            _ = ModelNtxBenchmark.query.filter_by(id=bmk_id).one()
        except Exception as _:
            raise ValidationError(f"The benchmark with ID {bmk_id} is not found in database.", 'bmk_id')

    @validates_schema
    def validate_data(self, data, **kwargs):   # pylint: disable=W0613
        # validate the PE exists in the database by checking the fqdn
        fqdn = data.get("fqdn")
        try:
            # Find the PE by calling NutanixOperation class with fqdn
            _, facility_type = NutanixOperation.get_pe_by_fqdn_from_db(fqdn=fqdn, return_facility_type=True)
        except Exception as _:
            # Raise a validation error if the PE is not found
            raise ValidationError(f"The PE {fqdn} is not found in database.")
        # validate update payload
        update_payload = {key: value for key, value in data.items() if key != 'fqdn'}
        if not update_payload:
            raise ValidationError("No fields to update. Please provide at least one field to update.")
        # validate specified fields for wh
        if facility_type == FacilityType.WAREHOUSE:
            self.validate_for_wh(data)
        # validate the remote_site_dsc exists in the database
        remote_site_dsc = data.get("remote_site_dsc")
        if remote_site_dsc:
            try:
                _ = NutanixOperation.get_pe_by_name_from_db(name=remote_site_dsc)
            except Exception as _:
                raise ValidationError(f"The remote site {remote_site_dsc} is not found in database.")
        bmk_id = data.get("bmk_id")
        if bmk_id:
            self.validate_bmk_id(bmk_id)


class ListClusterVmRequestSchema(Schema):
    pe = fields.Str(required=True)
    include_cvm = fields.Bool()
    is_available = fields.Bool()


class UnlockAccountSchema(Schema):
    pe_name = fields.Str(required=True, description="PE name")
    
    @validates_schema
    def validate_pe_name_contains_nxc(self, data, **kwargs):  # pylint: disable=W0613
        pe_name = data.get('pe_name', '')
        if 'NXC' not in pe_name.upper():
            raise ValidationError("PE Name should contain 'NXC', like 'RETCN888-NXC000'", 'pe_name')


class UpdateHostRequestSchema(Schema):
    uuid = fields.Str(required=True, description="UUID of the host to update")
    status = fields.Str(validate=validate.OneOf(["Running", "Unreachable", "Decommissioned"]), description="Status of the host")

    @validates_schema
    def validate_data(self, data, **kwargs):  # pylint: disable=W0613
        uuid = data.get("uuid")
        try:
            # Find the PE by calling NutanixOperation class with fqdn
            _ = NutanixOperation.get_host_by_uuid_from_db(uuid=uuid, return_facility_type=False)
        except Exception as _:
            # Raise a validation error if the PE is not found
            raise ValidationError(f"The host {uuid} is not found in database.")
        # validate update payload
        update_payload = {key: value for key, value in data.items() if key != 'uuid'}
        if not update_payload:
            raise ValidationError("No fields to update. Please provide at least one field to update.")
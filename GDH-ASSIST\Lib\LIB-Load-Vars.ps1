function Read-Var() {
    param (
        [switch] $IsOriginal
    )
    # Get base location
    # Read configuration and convert to PS object
    $Vars = Get-Content -Path $($MyInvocation.MyCommand.Module.ModuleBase + '\config.json') -Raw | ConvertFrom-Json -Depth 10
    if ($IsOriginal) {
        return $Vars
    } else {
        # Remove secrets from the object
        $Vars.PSObject.Properties.Remove('Secrets')
    }

    # Add GST account and Vault token to the object
    $Vars | Add-Member -NotePropertyName "GstAccount" -NotePropertyValue $(Read-GstAccount)

    $Tokens = Read-VaultToken
    $Vars.Vault.Retail | Add-Member -NotePropertyName "Token" -NotePropertyValue $($Tokens.Retail)
    $Vars.Vault.Warehouse | Add-Member -NotePropertyName "Token" -NotePropertyValue $($Tokens.Warehouse)
    
    return $Vars
}
function Read-GstAccount() {
    param (
        [switch] $ToClipboard,
        [string] [ValidateSet("D2", "DT", "PPE", "PROD")] $Tier = "PROD"
    )
    try {
        $ScrtTier = if ($Tier -in @("PPE", "PROD")) {
            "Default"
        } else {
            $Tier
        }
        $GSTUsername  = $Global:Secrets.GstAccount.$ScrtTier.Username
        $GSTEncrypted = $Global:Secrets.GstAccount.$ScrtTier.Encrypted
        $GSTPassword  = if ($IsWindows) {
            [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR((ConvertTo-SecureString $GSTEncrypted))))
        } else {
            $GSTEncrypted
        }
        $GstAccount     = [PSCustomObject]@{
            'username'  = $GSTUsername
            'password'  = $GSTPassword
        }
        if ($ToClipboard) {
            Set-Clipboard -Value $GSTPassword
            $GstAccount.password = "Only Available In Your Clipboard"
        }
        return $GstAccount
    } catch {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "The GST account is invalid, please run the command Update-GstAccount to fix"
        return $null
    }
}
function Read-VaultToken(){
    
    $Keys = @("Retail", "Warehouse")
    $Tokens = @{}

    foreach ($Key in $Keys) {
        try {
            $Encrypted = $Global:Secrets.VaultToken.$Key
            $Token     = if ($IsWindows) {
                [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR((ConvertTo-SecureString $Encrypted))))
            } else {
                $Encrypted
            }
            $Tokens.Add($Key, $Token)
        } catch {
            Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message "The vault token for $Key is empty, please run the command Update-VaultToken -Domain $Key to fix"
        }
    }
    return $Tokens
}
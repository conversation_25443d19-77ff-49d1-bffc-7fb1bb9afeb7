from flask import request, Response, abort, send_file
from flask_restful import Resource
from business.authentication.tokenvalidation import PrivilegeValidation
from .route import route
from models.toolbox_models import ModelToolboxTask, ModelToolboxTaskSchema, ModelToolboxTaskLog, ModelToolboxTaskLogSchema
import json
import os



@route('/api/v1/ntx/unlock_account/tasks')
class RestfulUnlockAccountTasks(Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_pe"})
    def get(self):
        try:
            tasks = ModelToolboxTask.query.filter_by(task_type="UNLOCK_ACCOUNT").order_by(ModelToolboxTask.id.desc()).all()
            task_schema = ModelToolboxTaskSchema(many=True)
            result = task_schema.dump(tasks)
            for task in result:
                latest_log = ModelToolboxTaskLog.query.filter_by(task_id=task['id']).order_by(ModelToolboxTaskLog.id.desc()).first()
                if latest_log:
                    log_schema = ModelToolboxTaskLogSchema()
                    task['latestlog'] = log_schema.dump(latest_log)
            
            return Response(json.dumps(result), status=200, mimetype='application/json')
        except Exception as e:
            abort(500, f"Failed to get unlock account tasks: {str(e)}")


@route('/api/v1/ntx/unlock_account/logs')
class RestfulUnlockAccountLogs(Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_pe"})
    def post(self):
        try:
            data = request.get_json(force=True)
            task_id = data.get('task_id')
            
            logs = ModelToolboxTaskLog.query.filter_by(task_id=task_id).order_by(ModelToolboxTaskLog.id.asc()).all()
            log_schema = ModelToolboxTaskLogSchema(many=True)
            result = log_schema.dump(logs)

            formatted_logs = []
            for log in result:
                formatted_logs.append({
                    "time": log.get("log_date"),
                    "message": log.get("log_info"),
                    "severity": log.get("severity")
                })
            
            return Response(json.dumps(formatted_logs), status=200, mimetype='application/json')
        except Exception as e:
            abort(500, f"Failed to get task logs: {str(e)}")


@route('/api/v1/ntx/unlock_account/log/download')
class RestfulDownloadUnlockAccountLog(Resource):
    @PrivilegeValidation(privilege={"role_ntx": "view_pe"})
    def post(self):
        try:
            data = request.get_json(force=True)
            filepath = data.get('filepath')
            
            if not filepath or not os.path.exists(filepath):
                abort(500, "Log file not found")
                
            return send_file(filepath, as_attachment=True)
        except Exception as e:
            abort(500, f"Failed to download log file: {str(e)}")
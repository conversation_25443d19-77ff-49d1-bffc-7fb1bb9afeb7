<template>
  <div class="app-container">
    <div class="filter-container">
      <el-popover placement="right-start"  width="200" trigger="click" ref="pop_operations" @show="toggleOperationarrow" @hide="toggleOperationarrow">
        <div class="trends_btn_wrap">
            <el-checkbox-group v-model="checkedOptions" >
              <el-checkbox style="margin-top: 5px;"
                  v-for="item in headerOptions" :key="item.label" :label="item" :value="item.value" :checked="item.checked" class="checkbox-line" @change="handleChecked($event,item)"
                >{{item.label}}</el-checkbox>
            </el-checkbox-group>
        </div>
      </el-popover>
      <el-popover placement="right-start"  width="200" trigger="click" ref="pop_column" @show="togglecolumnarrow" @hide="togglecolumnarrow">
          <div class="trends_btn_wrap">
              <el-checkbox-group v-model="checkedOptions" >
                  <el-checkbox style="margin-top: 5px;"
                    v-for="item in col_headerOptions" :key="item.label" :label="item" :value="item.value" :checked="item.checked" class="checkbox-line" @change="handleChecked($event,item)"
                  >{{item.label}}</el-checkbox>
              </el-checkbox-group>
          </div>
      </el-popover>
      <el-row :gutter="5" >
        <el-col :span="5" style='float:left;'>
          <el-button-group>
            <el-button v-popover:pop_operations  size="small" >Operations<i :class="handleoperarrow ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i></el-button>
            <el-button v-popover:pop_column size="small" >Column <i :class="handlecolarrow ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"></i></el-button>
          </el-button-group>
        </el-col>
        <el-col :span="4" :offset="7">
          <el-select    size="large"
            v-model="filter.selected_pe" multiple collapse-tags placeholder="Filter the PC" style="width:100%;" >
            <el-option v-for="item in filter.pc_list" :key="item" :label="item" :value="item" style="font-size: large;"/>
          </el-select>
        </el-col>
        <el-col :span="4" >
          <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " @keyup.enter.native="filter_ahv_list" size="large"/>
        </el-col>
        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item"  type="success" size="large" @click="download_ahv_list">
            Download
          </el-button>
        </el-col>
        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="filter_ahv_list">
            Search
          </el-button>
        </el-col>
    </el-row>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="current_list" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column label="ID" prop="id" sortable="custom" align="center" min-width="2%" >
        <template slot-scope="{row}">
          <span>{{current_list.indexOf(row)+1 }}</span>
        </template>
      </el-table-column>

      <el-table-column label="AHV" class-name="status-col" min-width="11%" align="center" sortable="custom" prop="name" >
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.name.toUpperCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column label="PE" min-width="10%" align="center" sortable="custom" prop="pe_name" v-if="isShowForm.pe_name">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.pe_name.toUpperCase() }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="DN." min-width="3%" align="center" sortable="custom" prop="disk_number" v-if="isShowForm.disk_number">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.disk_number }}</span>
        </template>
      </el-table-column>

      <el-table-column label="CPU" align="center" min-width="4%" sortable="custom" prop="cpu_core_number" v-if="isShowForm.cpu_core_number">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.cpu_core_number }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Memory" align="center" min-width="5%" sortable="custom" prop="memory" v-if="isShowForm.memory">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.memory }}</span>
        </template>
      </el-table-column>

      <el-table-column label="SN" align="center" min-width="8%" sortable="custom" prop="sn" v-if="isShowForm.sn">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.sn }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="AHV IP" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="ahv_ip" v-if="isShowForm.ahv_ip">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.ahv_ip }}</span>
        </template>
      </el-table-column>

      <el-table-column label="CVM IP" min-width="8%" align="center" sortable="custom" prop="cvm_ip" v-if="isShowForm.cvm_ip">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.cvm_ip }}</span>
        </template>
      </el-table-column>

      <el-table-column label="IPMI IP" min-width="10%" align="center" sortable="custom" prop="ipmi_ip" v-if="isShowForm.ipmi_ip">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.ipmi_ip }}</span>
        </template>
      </el-table-column>

      <el-table-column label="NIC0 Speed" min-width="10%" align="center" sortable="custom" prop="nic0_speed" v-if="isShowForm.nic0_speed">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.nic0_speed }}</span>
        </template>
      </el-table-column>

      <el-table-column label="NIC1 Speed" min-width="10%" align="center" sortable="custom" prop="nic1_speed" v-if="isShowForm.nic1_speed">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.nic1_speed }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="NIC0 MAC" min-width="10%" align="center" sortable="custom" prop="nic0_mac" v-if="isShowForm.nic0_mac">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.nic0_mac }}</span>
        </template>
      </el-table-column>

      <el-table-column label="NIC1 MAC" min-width="10%" align="center" sortable="custom" prop="nic1_mac" v-if="isShowForm.nic1_mac">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.nic1_mac }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Model" min-width="12%" align="center" sortable="custom" prop="model" v-if="isShowForm.model">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.model }}</span>
        </template>
      </el-table-column>

      <el-table-column label="UUID" min-width="20%" align="center" sortable="custom" prop="uuid" v-if="isShowForm.uuid">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.uuid }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Last Update" min-width="8%" align="center" sortable="custom" prop="last_update" v-if="isShowForm.last_update">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.last_update }}</span>
        </template>
      </el-table-column>

      <el-table-column label="IPMI Version" min-width="10%" align="center" sortable="custom" prop="ipmi_version" v-if="isShowForm.ipmi_version">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.ipmi_version }}</span>
        </template>
      </el-table-column>

      <el-table-column label="AHV Status" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="status" v-if="isShowForm.status">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.status }}</span>
        </template>
      </el-table-column>


    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" /> 


  </div>
</template>

<script>
import {GetNtxHostList} from '@/api/nutanix'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import {customized_search} from '@/utils/commonfunc'

const prismoptions = [
  { key: 'EU', display_name: 'SSP-EU-NTX.IKEA.COM' },
  { key: 'CN', display_name: 'SSP-CHINA-NTX.IKEA.COM' },
  { key: 'APAC', display_name: 'SSP-APAC-NTX.IKEA.COM' },
  { key: 'NA', display_name: 'SSP-NA-NTX.IKEA.COM' },
  { key: 'RU', display_name: 'SSP-RUSSIA-NTX.IKEA.COM' },
  { key: 'DT', display_name: 'SSP-DT-NTX.IKEADT.COM' },
  { key: 'PPE', display_name: 'SSP-PPE-NTX.IKEA.COM' },
]
const peoptions =[
  { pc: 'SSP-EU-NTX.IKEA.COM', pe:['RETDE068-NXC000.ikea.com','RETSEHBG-NXC000.ikea.com','RETDE124-NXC000.ikea.com','RETFR134-NXC000.ikea.com'] },
  { pc: 'SSP-CHINA-NTX.IKEA.COM', pe:['RETCN856-NXC000.ikea.com','RETCNCHN-NXC000.ikea.com','RETCN644-NXC000.ikea.com','RETCNSOS-NXC000.ikea.com'] },
  { pc: 'SSP-APAC-NTX.IKEA.COM', pe:['RETKR373-NXC000.ikea.com','RETJP509-NXC000.ikea.com','RETKR522-NXC000.ikea.com','RETKRSO-NXC000.ikea.com'] },
  { pc: 'SSP-NA-NTX.IKEA.COM', pe:['RETUS100-NXC000.ikea.com','RETCA040-NXC000.ikea.com','RETUS209-NXC000.ikea.com','RETUS374-NXC000.ikea.com'] },
  { pc: 'SSP-RUSSIA-NTX.IKEA.COM', pe:['RETRU401','RETRU403','RETRU551','RETRU513'] },
  { pc: 'SSP-DT-NTX.IKEADT.COM', pe:['RETSEELM-NXC000.ikea.com'] },
  { pc: 'SSP-PPE-NTX.IKEA.COM', pe:['RETSE999-NXC000.ikea.com','RETCN888-NXC000.ikea.com'] },
]


const peTypeKeyValue = peoptions.reduce((acc, cur) => {
  acc[cur.pc] = cur.pe
  return acc
}, {})
export default {
  name: 'PETable',
  components: { Pagination },
  directives: { waves },
  filters: {

  },
  data() {
    const validateTime =(rule, value, callback)=>{
      if(this.temp.datatimepickerdisabled){
        callback()
      }
      let currentdate = new Date()
      let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
      if (value < utctime){
        callback(new Error('Schedule date must be later then now.'))
      }else{
        let currnettime = utctime.getTime()
        let scheduletime = value.getTime()
        let timediff = scheduletime-currnettime
        if(timediff/1000/60 < 5){
          callback(new Error('Schedule date is too close from now.'))
        }else{
          callback()
        }
      }
      callback()
    }
    return {
      tableKey: 0,
      all_ahv_list: null,
      filtered_list: null,
      current_list: null,
      page_list: null,
      filter:{
        pc_list:[],
        selected_pe:[],
        fuzzy_string: "",
        // showsiab: false,
        // showwiab: false
      },
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: '',
        prism: '',
        status: '',
        sort: '+id'
      },
      statusToShowEditButton:['Not Started'],
      statusToShowAbortButton:['In Progress'],
      statusToShowDeleteButton:['Not Started','Done','Error','Aborted'],
      statusOptions: ['Not Started','In Progress','Done','Error','Aborted'],
      prismoptions,
      peTypeKeyValue,
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      // statusOptions: ['published', 'draft', 'deleted'],
      ShowCreationDate: false,
      temp: {
        id: '',
        timestamp: new Date(),
        cluster:'',
        prism: '',
        status: '',
        startnow: 1 ,
        datatimepickerdisabled:false,
        description: '',
        pmtype: 1
      },
      selectedrow:'',
      dialogFormVisible: false,
      dialogStatus: '',
      dialogPvVisible: false,
      handleoperarrow: false,
      handlecolarrow: false,
      logdata: [],
      rules: {
        prism: [{ required: true, message: 'prism is required', trigger: 'change' }],
        cluster: [{ required: true, message: 'cluster is required', trigger: 'change' }],
        timestamp: [{ type: 'date', required: true , trigger: 'change' , validator:validateTime}]
      },
      checkedOptions: [],
      headerOptions: [
        {
        label: 'Show Siab Only',
        value: 'showsiab',
        checked:true
        },{
        label: 'Show Wiab Only',
        value: 'showwiab',
        checked:true
        }
      ],
      isShowForm: {
        showsiab: true,
        showwiab: true,
        memory: true,
        cpu_core_number: true,
        name: true,
        disk_number: true,
        sn: true,
        ahv_ip: true,
        cvm_ip: true,
        ipmi_ip: true,
        aosversion: false,
        model: true,
        last_update: true,
        pe_name: false,
        status: false,
        uuid: true,
        ipmi_version: false,
        nic0_mac: false,
        nic1_mac: false,
        nic0_speed: false,
        nic1_speed: false,
      },
      col_headerOptions: [
        {
        label: 'Disk Num.', 
        value: 'disk_number',
        checked:true
        },{
        label: 'CPU',
        value: 'cpu_core_number',
        checked:true
        },{
        label: 'Memory',
        value: 'memory',
        checked:true
        },{
        label: 'Serial Number',
        value: 'sn',
        checked:true
        },{
        label: 'AHV IP',
        value: 'ahv_ip',
        checked:true
        },{
        label: 'CVM IP',
        value: 'cvm_ip',
        checked:true
        },{
        label: 'Ipmi IP',
        value: 'ipmi_ip',
        checked:true
        },{
        label: 'Model',
        value: 'model',
        checked:true
        },{
        label: 'AHV UUID',
        value: 'uuid',
        checked:true
        },{
        label: 'Last Update',
        value: 'last_update',
        checked:true
        },{
        label: 'PE',
        value: 'pe_name',
        checked:false
        },{
        label: 'AHV Status',
        value: 'status',
        checked:false
        },{
        label: 'Ipmi Version',
        value: 'ipmi_version',
        checked:false
        },{
        label: 'NIC0 MAC',
        value: 'nic0_mac',
        checked:false
        },{
        label: 'NIC1 MAC',
        value: 'nic1_mac',
        checked:false
        },{
        label: 'NIC0 Speed',
        value: 'nic0_speed',
        checked:false
        },{
        label: 'NIC1 Speed',
        value: 'nic1_speed',
        checked:false
        },

      ],
    }
  },
  computed: {
    total() {
      if(this.filtered_list){
        return this.filtered_list.length
      }
      else{
          return 0
      }
    }
  },
  created() {
    this.get_ahv_list()
  },
  methods: {
    togglecolumnarrow() {
        this.handlecolarrow = !this.handlecolarrow;
      },
      toggleOperationarrow() {
        this.handleoperarrow = !this.handleoperarrow;
      },
    get_ahv_list() {
      this.listLoading = true
      GetNtxHostList(this.$store.getters.token).then(response => {
        this.all_ahv_list = response.data.map((e)=>{
          e.disk_number=parseInt(e.disk_number)
          e.cpu_core_number=parseInt(e.cpu_core_number)
          return e
        })
        console.log(this.all_ahv_list)
        this.filtered_list = this.all_ahv_list
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)
        this.listLoading = false
        let all_prism_list = this.all_ahv_list.map((obj,index)=>{return obj['pe_name']})
        this.filter.pc_list = this.remove_duplicate(all_prism_list)
      })
    },
    remove_duplicate(arr) {
      //去除重复值
      const newArr = []
      arr.forEach(item => {
        if (!newArr.includes(item)) {
          newArr.push(item)
        }
      })
      return newArr
    },
    set_page(){
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start,end)
    },
    handleChecked(val,item) {
      console.log(val,item)
      this.isShowForm[item.value] = val
      this.filter_ahv_list()
    },
    filter_ahv_list(){
      //screen the table as per filters
      this.listQuery.page = 1
      let temp_list
      //filter selected pc first.
      if (this.filter.selected_pe.length){
        //No filter, so select all
        temp_list = this.all_ahv_list.filter((item)=>{
          return this.filter.selected_pe.includes(item['pe_name'])
        })
        this.filtered_list = temp_list
      }
      else{
        this.filtered_list = this.all_ahv_list
      }
      if (!this.isShowForm.showwiab && !this.isShowForm.showsiab) {
          this.filtered_list = [];
      }else if(this.isShowForm.showwiab && !this.isShowForm.showsiab) {
            temp_list = this.filtered_list.filter((item)=>{
                if (item['name'].search('DS') != -1 || item['name'].search('MOD') != -1) {
                  return true
                }
            })
            this.filtered_list = temp_list
      }else if(!this.isShowForm.showwiab && this.isShowForm.showsiab) {
        temp_list = this.filtered_list.filter((item)=>{
            if (item['name'].search('DS') == -1 && item['name'].search('MOD') == -1) {
            return true
          }
        })
        this.filtered_list = temp_list
      }else {
            this.filtered_list = this.all_ahv_list
          }

      if(this.filter.fuzzy_string.trim().length){
        let temp_list = this.filtered_list
        let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
            for(let fuzzy of fuzzy_list){
              fuzzy = fuzzy.toString().toLowerCase()
              temp_list = temp_list.filter((k)=>{
                if( 
                    customized_search(k.id,fuzzy)||
                    customized_search(k.name,fuzzy)||
                    customized_search(k.pe_name,fuzzy)||
                    customized_search(k.disk_number,fuzzy)||
                    customized_search(k.cpu_core_number,fuzzy)||
                    customized_search(k.ahv_ip,fuzzy)||
                    customized_search(k.cvm_ip,fuzzy)||
                    customized_search(k.ipmi_ip,fuzzy)||
                    customized_search(k.status,fuzzy)||
                    customized_search(k.sn,fuzzy)
                ){
                  return true
                }
              })
            }
        this.filtered_list = temp_list
      }


      this.set_page()
    },
    handleFilter() {
      this.listQuery.page = 1
    },
    sortChange(data) {
      const { prop, order } = data
      let intger_prop = ["disk_number","cpu_core_number"]

      if(order==null){
        this.sortChange({prop:'id',order:'ascending'})
        return 
      }
      let flag_num = order=="ascending" ? 1 : -1
      this.filtered_list.sort((item1,item2)=>(
        (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
      ))
      this.set_page()
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    download_ahv_list(){
      GetNtxHostList(this.$store.getters.token,  true)
      .then((response)=>{
        const href = URL.createObjectURL(response.data);
        // create "a" HTML element with href to file & click
        const link = document.createElement('a');
        link.href = href;
        link.setAttribute('download', "ahv_list"); //or any other extension
        document.body.appendChild(link);
        link.click();
        // clean up "a" element & remove ObjectURL
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      })
    },
  }
}
</script>
<style lang="scss" scoped>
    .bigger_font {
      font-size: 16px;
    }
</style>
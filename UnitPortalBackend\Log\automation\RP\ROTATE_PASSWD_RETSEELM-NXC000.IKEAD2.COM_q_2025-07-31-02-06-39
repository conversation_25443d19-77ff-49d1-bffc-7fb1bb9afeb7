2025-07-31 10:06:39,872 INFO Start to run the task.
2025-07-31 10:06:43,813 INFO Checking Maintenance Mode
2025-07-31 10:06:43,813 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-07-31 10:06:43,813 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-07-31 10:06:46,711 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-07-31 10:06:46,711 INFO SSH Executing '/home/<USER>/prism/cli/ncli host list --json=pretty'.
2025-07-31 10:06:47,568 INFO Waiting for 5 seconds for the execution.
2025-07-31 10:06:52,581 INFO stdout: b'{\n  "data" : [ {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::4",\n    "uuid" : "8a276a5a-9cd0-4702-8ed9-5699d07c192e",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH967RD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::60",\n        "diskUuid" : "ba87f2bb-2824-44e3-8240-75c32c2c4e80",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH967RD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH99N2D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::51",\n        "diskUuid" : "35ea81b9-7cfb-4924-9b45-f07a4cd6fc3f",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N2D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH7B71D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::53",\n        "diskUuid" : "65a27c83-885e-4672-be27-05971598814b",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7B71D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9726D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::61",\n        "diskUuid" : "f4390867-7871-494b-8fd2-65dfa19de512",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9726D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH9B3ED",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::58",\n        "diskUuid" : "9979a224-9d9e-42bc-a282-b1af4e2f9acd",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B3ED",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9421D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::52",\n        "diskUuid" : "01e76618-04df-4259-a761-7e0bab50887b",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9421D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH8DKKD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::56",\n        "diskUuid" : "e31d9325-c534-4c9e-a67e-65081ebff024",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8DKKD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98J3D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::57",\n        "diskUuid" : "c61322d0-febf-45eb-8293-8bb50ef57e95",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98J3D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307893",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::55",\n        "diskUuid" : "d55987bc-a972-43e0-8c49-380c1fed8ff0",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307893",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307888",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::54",\n        "diskUuid" : "514be4f9-c6dc-4f16-805f-8372704d5f1d",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307888",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7001",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8S",\n    "blockSerial" : "CZ20240J8S",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753432724207435,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "375000",\n      "controller_num_iops" : "83",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "261635",\n      "controller_num_write_io" : "839",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "892162",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "1187",\n      "controller_total_io_size_kbytes" : "7648",\n      "content_cache_hit_ppm" : "652064",\n      "controller_num_io" : "839",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "83",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1063",\n      "num_io" : "8",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "764",\n      "hypervisor_num_received_bytes" : "2099857117416",\n      "hypervisor_timespan_usecs" : "30006438",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "22",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "173",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "764",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "1563071903054",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "2",\n      "hypervisor_memory_usage_ppm" : "453912",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "83",\n      "total_io_time_usecs" : "1384",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "3",\n      "write_io_bandwidth_kBps" : "7",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "5",\n      "total_io_size_kbytes" : "94",\n      "io_bandwidth_kBps" : "9",\n      "content_cache_physical_memory_usage_bytes" : "3004546636",\n      "controller_timespan_usecs" : "10000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-483305412",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "625000",\n      "controller_avg_write_io_latency_usecs" : "1063",\n      "content_cache_logical_memory_usage_bytes" : "2521241224"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "6721089536",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "843431182336",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97646398322280",\n      "storage_tier.ssd.usage_bytes" : "************",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91529240819304",\n      "storage.usage_bytes" : "************",\n      "storage_tier.ssd.free_bytes" : "6117157502976"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::5",\n    "uuid" : "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH9453D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::63",\n        "diskUuid" : "55b9bef0-e64e-4b83-b56d-07424ac5a4fa",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9453D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH9B0JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::69",\n        "diskUuid" : "87299dda-79ad-4648-a18d-867837bdb061",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B0JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH947JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::66",\n        "diskUuid" : "21accc04-9438-4b1e-a735-121e6651e2c1",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH947JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9B2GD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::68",\n        "diskUuid" : "8fcec93c-f3a5-4a88-a18f-ad5a75a3d36e",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B2GD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH95WYD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::64",\n        "diskUuid" : "190548f4-f43c-40fd-8e0d-c805d972bf31",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH95WYD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9984D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::67",\n        "diskUuid" : "f73b0d65-6966-4e81-92fb-7815e0986aea",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9984D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH99N1D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::70",\n        "diskUuid" : "ebe3c979-d24f-46fb-9f2d-06b675e7f957",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N1D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98WLD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::65",\n        "diskUuid" : "3b10236d-18ef-48d1-b777-8f9f6b64ced8",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98WLD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N200095",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::72",\n        "diskUuid" : "4ce56fa0-31fa-45aa-9740-39f6561ab0e2",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N200095",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307878",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::71",\n        "diskUuid" : "064c52a5-ac8a-4f7f-ae98-967f225ddb32",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307878",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7002",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********30",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********30"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8R",\n    "blockSerial" : "CZ20240J8R",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753430337659671,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "227272",\n      "controller_num_iops" : "77",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "265842",\n      "controller_num_write_io" : "1556",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "1770775",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "453",\n      "controller_total_io_size_kbytes" : "13558",\n      "content_cache_hit_ppm" : "969094",\n      "controller_num_io" : "1556",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "85",\n      "num_write_iops" : "1",\n      "controller_num_random_io" : "0",\n      "num_iops" : "2",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1138",\n      "num_io" : "22",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "677",\n      "hypervisor_num_received_bytes" : "2411526702293",\n      "hypervisor_timespan_usecs" : "30135058",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "58",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "89",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "677",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "1609594893794",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "5",\n      "hypervisor_memory_usage_ppm" : "384785",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "77",\n      "total_io_time_usecs" : "1963",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "5",\n      "write_io_bandwidth_kBps" : "38",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "8",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "17",\n      "total_io_size_kbytes" : "446",\n      "io_bandwidth_kBps" : "44",\n      "content_cache_physical_memory_usage_bytes" : "3869751572",\n      "controller_timespan_usecs" : "20000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-572767596",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "772727",\n      "controller_avg_write_io_latency_usecs" : "1138",\n      "content_cache_logical_memory_usage_bytes" : "3296983976"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "6760009728",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "890976698368",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97594853789288",\n      "storage_tier.ssd.usage_bytes" : "************",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91529201899112",\n      "storage.usage_bytes" : "************",\n      "storage_tier.ssd.free_bytes" : "6065651890176"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::6",\n    "uuid" : "34ff0abd-9dee-4e7d-9481-4716d64569b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH96JDD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::41",\n        "diskUuid" : "2c172a78-3626-4861-bfff-98d6d79fef49",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH96JDD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH995TD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::43",\n        "diskUuid" : "94de93d2-5e8f-44d7-85d7-c414177670a7",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH995TD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH991DD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::46",\n        "diskUuid" : "4d3bffc9-fa0d-4ff3-bd95-436cf68ab516",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH991DD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9825D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::45",\n        "diskUuid" : "ea324991-7bb3-49a9-84f6-7c30e1c938a5",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9825D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH8XYHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::48",\n        "diskUuid" : "5f14642e-c45b-4efc-b5ee-95c532192c22",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8XYHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH99MHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::44",\n        "diskUuid" : "5758e233-4edc-4045-8446-0625e9ba05c0",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99MHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH9ADHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::47",\n        "diskUuid" : "dbeb0a0e-ff11-4b98-a2b7-a45bad3407fd",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9ADHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH7XGHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::42",\n        "diskUuid" : "08dba0c0-cd98-4d75-a131-a60323ba43b5",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7XGHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307864",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::50",\n        "diskUuid" : "39e3bde6-2802-4ad2-825c-a0d61ae9fb35",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307864",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307881",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::49",\n        "diskUuid" : "a032c48d-3866-4fd6-88af-1f6a25fe5ac3",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307881",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7003",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********31",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********31"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8Q",\n    "blockSerial" : "CZ20240J8Q",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 2,\n    "bootTimeInUsecs" : 1753435095314546,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "0",\n      "controller_num_iops" : "537",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "538",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "289751",\n      "controller_num_write_io" : "5375",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "2648166",\n      "controller_total_read_io_size_kbytes" : "4",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "186",\n      "content_cache_num_lookups" : "303",\n      "controller_total_io_size_kbytes" : "47084",\n      "content_cache_hit_ppm" : "1000000",\n      "controller_num_io" : "5376",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "83",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "492",\n      "num_io" : "1",\n      "controller_num_read_io" : "1",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "4708",\n      "hypervisor_num_received_bytes" : "1549312457523",\n      "hypervisor_timespan_usecs" : "29612679",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "3",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "5591",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "4708",\n      "controller_write_io_ppm" : "999813",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "2603030744165",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "0",\n      "hypervisor_memory_usage_ppm" : "401974",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "537",\n      "total_io_time_usecs" : "5591",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "4",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "0",\n      "write_io_bandwidth_kBps" : "1",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "8",\n      "controller_avg_read_io_latency_usecs" : "538",\n      "num_write_io" : "1",\n      "total_io_size_kbytes" : "19",\n      "io_bandwidth_kBps" : "1",\n      "content_cache_physical_memory_usage_bytes" : "2931333556",\n      "controller_timespan_usecs" : "10000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-480422596",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "1000000",\n      "controller_avg_write_io_latency_usecs" : "492",\n      "content_cache_logical_memory_usage_bytes" : "2450910960"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "5881331712",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "1034599661568",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97440121800296",\n      "storage_tier.ssd.usage_bytes" : "808676909056",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91530080577128",\n      "storage.usage_bytes" : "814558240768",\n      "storage_tier.ssd.free_bytes" : "5910041223168"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  } ],\n  "status" : 0\n}\n'
2025-07-31 10:06:52,621 INFO All good, no hosts are set as NCLI maintenance inside this cluster.
2025-07-31 10:06:52,621 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-07-31 10:06:52,621 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-07-31 10:06:55,164 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-07-31 10:06:55,164 INFO SSH Executing '/usr/local/nutanix/bin/acli -o json host.list'.
2025-07-31 10:06:56,001 INFO Waiting for 5 seconds for the execution.
2025-07-31 10:07:01,009 INFO stdout: b'{"data": [{"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "8a276a5a-9cd0-4702-8ed9-5699d07c192e", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "34ff0abd-9dee-4e7d-9481-4716d64569b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}], "error": null, "status": 0}\n'
2025-07-31 10:07:01,009 INFO This seems a very old AOS version...
2025-07-31 10:07:01,009 INFO This seems a very old AOS version...
2025-07-31 10:07:01,011 INFO This seems a very old AOS version...
2025-07-31 10:07:01,046 INFO All good, no hosts are set as ACLI maintenance inside this cluster.
2025-07-31 10:07:01,091 INFO Checking CVM status
2025-07-31 10:07:01,546 INFO Trying to SSH to the RETSEELM-NXC000.IKEAD2.COM.
2025-07-31 10:07:01,546 INFO First try with username/password.
2025-07-31 10:07:01,546 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-07-31 10:07:04,100 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-07-31 10:07:10,196 INFO Sending 'cluster status |grep -v UP' to the server.
2025-07-31 10:07:29,206 INFO CVM IP:*********** Status:Up
2025-07-31 10:07:29,206 INFO CVM IP:*********** Status:Up
2025-07-31 10:07:29,206 INFO CVM IP:*********** Status:Up
2025-07-31 10:07:29,206 INFO Great, all CVM status are Up
2025-07-31 10:07:29,300 INFO Calling restapi, URL: https://retseelm-nxc000.ikead2.com:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-07-31 10:07:29,300 INFO params: None
2025-07-31 10:07:29,300 INFO User: <EMAIL>
2025-07-31 10:07:29,300 INFO payload: None
2025-07-31 10:07:29,300 INFO files: None
2025-07-31 10:07:29,300 INFO timeout: 30
2025-07-31 10:07:30,694 INFO Getting host list from retseelm-nxc000.
2025-07-31 10:07:30,694 INFO Got the host list from retseelm-nxc000.
2025-07-31 10:07:30,694 INFO Got the host list.
2025-07-31 10:07:30,694 INFO Getting vault from IKEAD2.
2025-07-31 10:07:31,334 INFO Getting Site_Pe_Nutanix.
2025-07-31 10:07:31,799 INFO Got Site_Pe_Nutanix.
2025-07-31 10:07:31,799 INFO Getting Site_Pe_Admin.
2025-07-31 10:07:32,246 INFO Got Site_Pe_Admin.
2025-07-31 10:07:32,247 INFO Getting Site_Oob.
2025-07-31 10:07:32,736 INFO Got Site_Oob.
2025-07-31 10:07:32,736 INFO Getting Site_Ahv_Nutanix.
2025-07-31 10:07:33,186 INFO Got Site_Ahv_Nutanix.
2025-07-31 10:07:33,186 INFO Getting Site_Ahv_Root.
2025-07-31 10:07:33,701 INFO Got Site_Ahv_Root.
2025-07-31 10:07:33,703 INFO Getting Site_Gw_Priv_Key.
2025-07-31 10:07:34,220 INFO Got Site_Gw_Priv_Key.
2025-07-31 10:07:34,220 INFO Getting Site_Gw_Pub_Key.
2025-07-31 10:07:34,717 INFO Got Site_Gw_Pub_Key.
2025-07-31 10:07:34,717 INFO Getting Site_Pe_Svc.
2025-07-31 10:07:35,169 INFO Got Site_Pe_Svc.
2025-07-31 10:07:35,219 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-07-31 10:07:35,301 INFO Connecting to CVM *********** for AHV password updates
2025-07-31 10:07:35,301 INFO SSH connecting to ***********, this is the '1' try.
2025-07-31 10:07:37,300 INFO SSH connected to *********** with SSHKEY.
2025-07-31 10:07:38,430 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-07-31 10:07:43,470 INFO Start reset AHV user nutanix password from CVM ***********
2025-07-31 10:07:43,516 INFO unlocking nutanix account
2025-07-31 10:07:43,516 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-07-31 10:07:56,524 INFO Sending '*' to the server.
2025-07-31 10:08:12,062 INFO AHV User nutanix Password Update Success
2025-07-31 10:08:12,159 INFO Start reset AHV user root password from CVM ***********
2025-07-31 10:08:12,200 INFO unlocking root account
2025-07-31 10:08:12,201 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-07-31 10:08:22,702 INFO Sending '*' to the server.
2025-07-31 10:08:38,239 INFO AHV User root Password Update Success
2025-07-31 10:08:38,279 INFO Connecting to CVM *********** for AHV password updates
2025-07-31 10:08:38,280 INFO SSH connecting to ***********, this is the '1' try.
2025-07-31 10:08:40,261 INFO SSH connected to *********** with SSHKEY.
2025-07-31 10:08:41,396 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-07-31 10:08:46,432 INFO Start reset AHV user nutanix password from CVM ***********
2025-07-31 10:08:46,489 INFO unlocking nutanix account
2025-07-31 10:08:46,489 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-07-31 10:08:58,494 INFO Sending '*' to the server.
2025-07-31 10:09:14,037 INFO AHV User nutanix Password Update Success
2025-07-31 10:09:14,094 INFO Start reset AHV user root password from CVM ***********
2025-07-31 10:09:14,132 INFO unlocking root account
2025-07-31 10:09:14,132 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-07-31 10:09:24,634 INFO Sending '*' to the server.
2025-07-31 10:09:40,173 INFO AHV User root Password Update Success
2025-07-31 10:09:40,211 INFO Connecting to CVM *********** for AHV password updates
2025-07-31 10:09:40,212 INFO SSH connecting to ***********, this is the '1' try.
2025-07-31 10:09:42,210 INFO SSH connected to *********** with SSHKEY.
2025-07-31 10:09:43,295 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-07-31 10:09:48,339 INFO Start reset AHV user nutanix password from CVM ***********
2025-07-31 10:09:48,379 INFO unlocking nutanix account
2025-07-31 10:09:48,379 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-07-31 10:10:00,383 INFO Sending '*' to the server.
2025-07-31 10:10:15,938 INFO AHV User nutanix Password Update Success
2025-07-31 10:10:15,985 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Ahv_Nutanix
2025-07-31 10:10:16,659 INFO Saving token completed.
2025-07-31 10:10:16,703 INFO Start reset AHV user root password from CVM ***********
2025-07-31 10:10:16,741 INFO unlocking root account
2025-07-31 10:10:16,741 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-07-31 10:10:27,242 INFO Sending '*' to the server.
2025-07-31 10:10:42,770 INFO AHV User root Password Update Success
2025-07-31 10:10:42,807 INFO Saving token to Vault... Username: root, label: RETSEELM-NXC000/Site_Ahv_Root
2025-07-31 10:10:43,484 INFO Saving token completed.
2025-07-31 10:10:43,521 INFO Start reset ILO administrator password for RETSEELM-NXC000
2025-07-31 10:10:43,521 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-07-31 10:10:45,481 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM with SSHKEY.
2025-07-31 10:10:47,093 INFO Sending '*' to the server.
2025-07-31 10:11:05,602 INFO ILO password Update Success
2025-07-31 10:11:05,640 INFO Saving token to Vault... Username: administrator, label: RETSEELM-NXC000/Site_Oob
2025-07-31 10:11:06,328 INFO Saving token completed.
2025-07-31 10:11:06,368 INFO Start reset CVM Nutanix password
2025-07-31 10:11:06,368 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-07-31 10:11:12,608 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 10:11:12,608 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '2' try.
2025-07-31 10:11:18,770 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 10:11:18,770 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '3' try.
2025-07-31 10:11:25,011 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 10:11:25,011 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '4' try.
2025-07-31 10:11:31,185 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 10:11:31,185 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '5' try.
2025-07-31 10:11:37,378 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 10:11:37,378 ERROR We've retried for 5 times, still not connected, aborting.
2025-07-31 10:11:37,378 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-07-31 10:11:39,372 INFO SSH connected to RETSEELM-NXC000.ikead2.com with SSHKEY.
2025-07-31 10:11:40,527 INFO unlocking nutanix account
2025-07-31 10:11:40,527 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.
2025-07-31 10:11:54,005 INFO Sending '*' to the server.
2025-07-31 10:12:04,512 INFO nutanix Password Update Success
2025-07-31 10:12:04,565 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Pe_Nutanix
2025-07-31 10:12:05,192 INFO Saving token completed.
2025-07-31 10:12:05,726 INFO taking a 30S extra powernap
2025-07-31 10:12:35,724 INFO Start reset admin password
2025-07-31 10:12:35,725 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-07-31 10:12:40,091 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 10:12:40,091 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '2' try.
2025-07-31 10:12:44,433 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 10:12:44,433 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '3' try.
2025-07-31 10:12:50,598 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 10:12:50,598 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '4' try.
2025-07-31 10:12:56,790 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 10:12:56,791 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '5' try.
2025-07-31 10:13:03,040 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 10:13:03,040 ERROR We've retried for 5 times, still not connected, aborting.
2025-07-31 10:13:03,040 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-07-31 10:13:05,023 INFO SSH connected to RETSEELM-NXC000.ikead2.com with SSHKEY.
2025-07-31 10:13:06,155 INFO unlocking admin account
2025-07-31 10:13:06,155 INFO Sending 'allssh sudo faillock --user admin --reset' to the server.
2025-07-31 10:13:19,633 INFO Sending '*' to the server.
2025-07-31 10:13:30,155 INFO admin Password Update Success
2025-07-31 10:13:30,192 INFO Saving token to Vault... Username: admin, label: RETSEELM-NXC000/Site_Pe_Admin
2025-07-31 10:13:30,826 INFO Saving token completed.
2025-07-31 10:13:30,866 INFO This is a central PE, start to reset PCVM password...
2025-07-31 10:13:30,867 INFO Resetting password for Site_Pc_Nutanix...
2025-07-31 10:13:31,812 INFO taking a 30S extra powernap
2025-07-31 10:14:01,802 INFO Start reset PCVM Site_Pc_Nutanix password
2025-07-31 10:14:01,803 INFO SSH connecting to ***********2, this is the '1' try.
2025-07-31 10:14:04,313 INFO SSH connected to ***********2.
2025-07-31 10:14:05,483 INFO unlocking nutanix account
2025-07-31 10:14:05,484 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.
2025-07-31 10:14:16,464 INFO Sending '*' to the server.
2025-07-31 10:14:26,986 INFO nutanix Password Update Success
2025-07-31 10:14:27,557 INFO Resetting password for Site_Pc_Admin...
2025-07-31 10:14:28,506 INFO taking a 30S extra powernap
2025-07-31 10:14:58,488 INFO Start reset PCVM Site_Pc_Admin password
2025-07-31 10:14:58,488 INFO SSH connecting to ***********2, this is the '1' try.
2025-07-31 10:15:00,997 INFO SSH connected to ***********2.
2025-07-31 10:15:02,145 INFO unlocking admin account
2025-07-31 10:15:02,145 INFO Sending 'allssh sudo faillock --user admin --reset' to the server.
2025-07-31 10:15:13,125 INFO Sending '*' to the server.
2025-07-31 10:15:23,647 INFO admin Password Update Success
2025-07-31 10:15:24,284 INFO Start reset 1-click-nutanix password for PE RETSEELM-NXC000
2025-07-31 10:15:24,762 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-07-31 10:15:24,762 INFO params: None
2025-07-31 10:15:24,762 INFO User: admin
2025-07-31 10:15:24,764 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-07-31 10:15:24,764 INFO files: None
2025-07-31 10:15:24,764 INFO timeout: None
2025-07-31 10:15:26,851 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-07-31 10:15:26,851 INFO params: None
2025-07-31 10:15:26,851 INFO User: admin
2025-07-31 10:15:26,851 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-07-31 10:15:26,851 INFO files: None
2025-07-31 10:15:26,851 INFO timeout: None
2025-07-31 10:15:29,299 INFO This is centrol PE, Start reset 1-click-nutanix password for Pc ssp-dhd2-ntx.ikead2.com
2025-07-31 10:15:30,086 INFO Saving token to Vault... Username: 1-click-nutanix, label: RETSEELM-NXC000/Site_Pe_Svc
2025-07-31 10:15:30,811 INFO Saving token completed.
2025-07-31 10:15:30,850 INFO ****************************************************************************************************
2025-07-31 10:15:30,850 INFO *                                                                                                  *
2025-07-31 10:15:30,850 INFO *                                          Renew SSH key                                           *
2025-07-31 10:15:30,850 INFO *                                                                                                  *
2025-07-31 10:15:30,850 INFO ****************************************************************************************************
2025-07-31 10:15:31,850 INFO Check if ssh_key exist
2025-07-31 10:15:31,850 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: GET, headers: None
2025-07-31 10:15:31,850 INFO params: None
2025-07-31 10:15:31,850 INFO User: admin
2025-07-31 10:15:31,850 INFO payload: None
2025-07-31 10:15:31,850 INFO files: None
2025-07-31 10:15:31,851 INFO timeout: None
2025-07-31 10:15:33,598 INFO SSH key exist, we need replace it.
2025-07-31 10:15:33,599 INFO Deleting public key...
2025-07-31 10:15:33,599 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys/Gateway, method: DELETE, headers: None
2025-07-31 10:15:33,600 INFO params: None
2025-07-31 10:15:33,600 INFO User: admin
2025-07-31 10:15:33,600 INFO payload: None
2025-07-31 10:15:33,600 INFO files: None
2025-07-31 10:15:33,600 INFO timeout: None
2025-07-31 10:15:35,433 INFO Delete public key finished.
2025-07-31 10:15:35,433 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: GET, headers: None
2025-07-31 10:15:35,433 INFO params: None
2025-07-31 10:15:35,434 INFO User: admin
2025-07-31 10:15:35,434 INFO payload: None
2025-07-31 10:15:35,434 INFO files: None
2025-07-31 10:15:35,434 INFO timeout: None
2025-07-31 10:15:36,893 INFO SSH key exist, we need replace it.
2025-07-31 10:15:36,894 INFO Deleting public key...
2025-07-31 10:15:36,894 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys/Gateway, method: DELETE, headers: None
2025-07-31 10:15:36,894 INFO params: None
2025-07-31 10:15:36,894 INFO User: admin
2025-07-31 10:15:36,894 INFO payload: None
2025-07-31 10:15:36,894 INFO files: None
2025-07-31 10:15:36,894 INFO timeout: None
2025-07-31 10:15:38,485 INFO Delete public key finished.
2025-07-31 10:15:38,522 INFO Generating ssh_key
2025-07-31 10:15:38,522 INFO Generating SSH key: ssh-keygen -t rsa -b 2048 -f c:\Dev\UnitPortalBackend\tmp\sshkey\RETSEELM-NXC000_2025-07-31-02-15-31\prvkey -q -N "" -m PEM
2025-07-31 10:15:38,773 INFO Key pair generated: c:\Dev\UnitPortalBackend\tmp\sshkey\RETSEELM-NXC000_2025-07-31-02-15-31\prvkey
2025-07-31 10:15:38,785 INFO Installing public key...
2025-07-31 10:15:38,785 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: POST, headers: None
2025-07-31 10:15:38,786 INFO params: None
2025-07-31 10:15:38,786 INFO User: admin
2025-07-31 10:15:38,786 INFO payload: {'name': 'Gateway', 'key': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDHWiVzyOcmJu6fWM1Pdul3Bo4YC8DZ9blGm6vGf5rrBvIZD8NARIsShSVVLN5fXBkstkLF9NL/9Cg9jIjsM5XE+hlib38gICF5XJiUL4p2n+RztFjO720qnlLq4hJ2O8U0fjKnC5lvKZwp7WVIc8rSIH33Cg+PZyvNzSvH5aOl07IIJbEuSiByzXXzvlcz5eU1+A26vbcZvEfvP6GAMVEqvImzy8lM/nPM6JcDUtCt/zO9+R/tfI0yge66Ajdk3MTRORSXGOB17BEGrVtcpOJ+EXNGTTOtEoAwkQa3e1tWMA3Yfs6As1x0Te90RACZL0w+ma4hCS1JJtPdJMfzljtD ikea\\hunhe@ITCNSHG-NB0436'}
2025-07-31 10:15:38,786 INFO files: None
2025-07-31 10:15:38,786 INFO timeout: None
2025-07-31 10:15:40,539 INFO Install public key finished.
2025-07-31 10:15:40,539 INFO Installing public key...
2025-07-31 10:15:40,539 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: POST, headers: None
2025-07-31 10:15:40,540 INFO params: None
2025-07-31 10:15:40,540 INFO User: admin
2025-07-31 10:15:40,540 INFO payload: {'name': 'Gateway', 'key': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDHWiVzyOcmJu6fWM1Pdul3Bo4YC8DZ9blGm6vGf5rrBvIZD8NARIsShSVVLN5fXBkstkLF9NL/9Cg9jIjsM5XE+hlib38gICF5XJiUL4p2n+RztFjO720qnlLq4hJ2O8U0fjKnC5lvKZwp7WVIc8rSIH33Cg+PZyvNzSvH5aOl07IIJbEuSiByzXXzvlcz5eU1+A26vbcZvEfvP6GAMVEqvImzy8lM/nPM6JcDUtCt/zO9+R/tfI0yge66Ajdk3MTRORSXGOB17BEGrVtcpOJ+EXNGTTOtEoAwkQa3e1tWMA3Yfs6As1x0Te90RACZL0w+ma4hCS1JJtPdJMfzljtD ikea\\hunhe@ITCNSHG-NB0436'}
2025-07-31 10:15:40,540 INFO files: None
2025-07-31 10:15:40,540 INFO timeout: None
2025-07-31 10:15:42,079 INFO Install public key finished.
2025-07-31 10:15:42,119 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Gw_Priv_Key
2025-07-31 10:15:42,756 INFO Saving token completed.
2025-07-31 10:15:42,795 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Gw_Pub_Key
2025-07-31 10:15:43,556 INFO Saving token completed.
2025-07-31 10:15:43,691 INFO Task is in 'Done' status.

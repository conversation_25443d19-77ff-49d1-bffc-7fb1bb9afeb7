2025-08-01 13:05:29,667 INFO Start to run the task.
2025-08-01 13:05:29,679 INFO ****************************************************************************************************
2025-08-01 13:05:29,679 INFO *                                                                                                  *
2025-08-01 13:05:29,679 INFO *                                        Check VM existence                                        *
2025-08-01 13:05:29,679 INFO *                                                                                                  *
2025-08-01 13:05:29,690 INFO ****************************************************************************************************
2025-08-01 13:05:29,690 INFO Checking if vm already exists in the PE cluster.
2025-08-01 13:05:29,690 INFO Checking if RETSEELM-NT1800 existed in PE.
2025-08-01 13:05:29,690 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 13:05:29,690 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms?sortCriteria=vm_name&searchString=RETSEELM-NT1800, method: GET, headers: None
2025-08-01 13:05:29,690 INFO params: None
2025-08-01 13:05:29,690 INFO User: <EMAIL>
2025-08-01 13:05:29,690 INFO payload: None
2025-08-01 13:05:29,690 INFO files: None
2025-08-01 13:05:29,690 INFO timeout: 30
2025-08-01 13:05:31,532 INFO Got the VM list from RETSEELM-NXC000.
2025-08-01 13:05:31,532 INFO RETSEELM-NT1800 doesn't exist in RETSEELM-NXC000.
2025-08-01 13:05:31,544 INFO RETSEELM-NT1800 not exists in Cluster RETSEELM-NXC000.IKEAD2.COM, move on...
2025-08-01 13:05:31,551 INFO Checking if vm already exists in the inventory AD/Tower.
2025-08-01 13:05:31,569 INFO Calling restapi, URL: https://thorshammereu.ikea.com/api/runmethod/robo/get-computer/prefix=RETSEELM-NT1800&client=yes, method: GET, headers: None
2025-08-01 13:05:31,569 INFO params: None
2025-08-01 13:05:31,569 INFO User: L-LTTHOR-A-ITSEELM
2025-08-01 13:05:31,569 INFO payload: None
2025-08-01 13:05:31,569 INFO files: None
2025-08-01 13:05:31,569 INFO timeout: None
2025-08-01 13:05:32,968 INFO VM 'RETSEELM-NT1800' doesn't exist in AD, continue...
2025-08-01 13:05:32,973 INFO Checking if vm already exists in IPAM.
2025-08-01 13:05:32,987 INFO Start to check if RETSEELM-NT1800.IKEAD2.COM existed in IPAM...
2025-08-01 13:05:32,987 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NT1800.IKEAD2.COM', method: GET, headers: None
2025-08-01 13:05:32,987 INFO params: None
2025-08-01 13:05:32,987 INFO User: <EMAIL>
2025-08-01 13:05:32,987 INFO payload: None
2025-08-01 13:05:32,987 INFO files: None
2025-08-01 13:05:32,987 INFO timeout: 30
2025-08-01 13:05:33,944 INFO 'RETSEELM-NT1800' not exists in IPAM, continue...
2025-08-01 13:05:33,959 INFO ****************************************************************************************************
2025-08-01 13:05:33,959 INFO *                                                                                                  *
2025-08-01 13:05:33,959 INFO *                                              Sizing                                              *
2025-08-01 13:05:33,960 INFO *                                                                                                  *
2025-08-01 13:05:33,960 INFO ****************************************************************************************************
2025-08-01 13:05:33,977 INFO Sizing, check if cluster has enough capacity for this VM.
2025-08-01 13:05:33,977 INFO Get a list of existing hosts from RETSEELM-NXC000.IKEAD2.COM
2025-08-01 13:05:33,977 INFO Calling /hosts through v1 API using GET method
2025-08-01 13:05:33,977 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 13:05:33,977 INFO params: None
2025-08-01 13:05:33,977 INFO User: <EMAIL>
2025-08-01 13:05:33,977 INFO payload: None
2025-08-01 13:05:33,977 INFO files: None
2025-08-01 13:05:33,977 INFO timeout: None
2025-08-01 13:05:35,223 INFO Get cluster details from RETSEELM-NXC000.IKEAD2.COM
2025-08-01 13:05:35,223 INFO Calling /cluster through v1 API using GET method
2025-08-01 13:05:35,223 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster, method: GET, headers: None
2025-08-01 13:05:35,223 INFO params: None
2025-08-01 13:05:35,223 INFO User: <EMAIL>
2025-08-01 13:05:35,223 INFO payload: None
2025-08-01 13:05:35,223 INFO files: None
2025-08-01 13:05:35,223 INFO timeout: None
2025-08-01 13:05:36,558 INFO Get a list of existing user VMs from RETSEELM-NXC000.IKEAD2.COM
2025-08-01 13:05:36,558 INFO Calling /vms through v2 API using GET method
2025-08-01 13:05:36,558 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms, method: GET, headers: None
2025-08-01 13:05:36,558 INFO params: None
2025-08-01 13:05:36,566 INFO User: <EMAIL>
2025-08-01 13:05:36,566 INFO payload: None
2025-08-01 13:05:36,566 INFO files: None
2025-08-01 13:05:36,566 INFO timeout: None
2025-08-01 13:05:38,336 INFO The cluster is a 3 node(s) cluster
2025-08-01 13:05:38,336 INFO Fetching capacity from node RETSEELM-NX7001
2025-08-01 13:05:38,336 INFO Storage of node RETSEELM-NX7001 is 44.68 TiB
2025-08-01 13:05:38,336 INFO Number of cores on node RETSEELM-NX7001 is 20
2025-08-01 13:05:38,336 INFO Memory install on node RETSEELM-NX7001 is 377.08 GiB
2025-08-01 13:05:38,336 INFO Fetching capacity from node RETSEELM-NX7002
2025-08-01 13:05:38,336 INFO Storage of node RETSEELM-NX7002 is 44.68 TiB
2025-08-01 13:05:38,336 INFO Number of cores on node RETSEELM-NX7002 is 20
2025-08-01 13:05:38,336 INFO Memory install on node RETSEELM-NX7002 is 377.08 GiB
2025-08-01 13:05:38,336 INFO Fetching capacity from node RETSEELM-NX7003
2025-08-01 13:05:38,336 INFO Storage of node RETSEELM-NX7003 is 44.68 TiB
2025-08-01 13:05:38,336 INFO Number of cores on node RETSEELM-NX7003 is 20
2025-08-01 13:05:38,336 INFO Memory install on node RETSEELM-NX7003 is 345.58 GiB
2025-08-01 13:05:38,336 INFO Number of nodes in this cluster is 3
2025-08-01 13:05:38,336 INFO Total storage capacity on this cluster is 134.04 TiB
2025-08-01 13:05:38,336 INFO total number of CPU cores on cluster is 60
2025-08-01 13:05:38,336 INFO Total memory capacity on this cluster is 1099.74 GiB
2025-08-01 13:05:38,336 INFO Resilient storage capacity on this cluster is 84.************** TiB
2025-08-01 13:05:38,336 INFO Number of resilient physical CPU cores is 40
2025-08-01 13:05:38,336 INFO Number of resilient physical CPU cores accounting CVMs is 34
2025-08-01 13:05:38,336 INFO Number of resilient virtual CPU cores (assuming 1:4 ratio) is 136
2025-08-01 13:05:38,336 INFO Resilient memory capacity on this cluster is 722.************* GiB
2025-08-01 13:05:38,336 INFO Resilient memory capacity accounting CVMs on this cluster is 658.************* GiB
2025-08-01 13:05:38,336 INFO Utilized storage of cluster is 0.97 TiB
2025-08-01 13:05:38,336 INFO There are 6 VMs on this cluster
2025-08-01 13:05:38,336 INFO Number of virtual cores used by 6 VMs that are powered on is 66
2025-08-01 13:05:38,336 INFO Memory used by 6 VMs that are powered on is 264.0 GiB
2025-08-01 13:05:38,336 INFO Available storage for new VM provisioning is 83.************** TiB
2025-08-01 13:05:38,336 INFO Available vCPU cores for new VM provisioning is 70
2025-08-01 13:05:38,336 INFO Available memory for new VM provisioning is 394.************* GiB
2025-08-01 13:05:38,358 INFO ****************************************************************************************************
2025-08-01 13:05:38,358 INFO *                                                                                                  *
2025-08-01 13:05:38,358 INFO *                                Checking workload network on NTX.                                 *
2025-08-01 13:05:38,358 INFO *                                                                                                  *
2025-08-01 13:05:38,358 INFO ****************************************************************************************************
2025-08-01 13:05:38,378 INFO Checking PE network by VlanId=793
2025-08-01 13:05:38,378 INFO Getting network list from RETSEELM-NXC000
2025-08-01 13:05:38,378 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/networks, method: GET, headers: None
2025-08-01 13:05:38,378 INFO params: None
2025-08-01 13:05:38,378 INFO User: <EMAIL>
2025-08-01 13:05:38,378 INFO payload: None
2025-08-01 13:05:38,378 INFO files: None
2025-08-01 13:05:38,378 INFO timeout: 30
2025-08-01 13:05:40,308 INFO Got the network list from RETSEELM-NXC000.
2025-08-01 13:05:40,308 INFO Vlan 793 is found
2025-08-01 13:05:40,324 INFO The network is found, the UUID is 9531e569-3bec-4d92-8581-6209bea747db
2025-08-01 13:05:40,339 INFO ****************************************************************************************************
2025-08-01 13:05:40,339 INFO *                                                                                                  *
2025-08-01 13:05:40,339 INFO *                                           Check image                                            *
2025-08-01 13:05:40,339 INFO *                                                                                                  *
2025-08-01 13:05:40,339 INFO ****************************************************************************************************
2025-08-01 13:05:40,352 INFO Verifying workload image existence in DB and on cluster.
2025-08-01 13:05:40,359 INFO Checking if RHELx_AUTO image
2025-08-01 13:05:40,359 INFO Validating image 'ICC_2k16_v0015-RETSEELM-NXC000' existence in PE 'RETSEELM-NXC000'...
2025-08-01 13:05:40,359 INFO Start to find the image ICC_2k16_v0015-RETSEELM-NXC000 from RETSEELM-NXC000
2025-08-01 13:05:40,359 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/images, method: GET, headers: None
2025-08-01 13:05:40,359 INFO params: None
2025-08-01 13:05:40,359 INFO User: <EMAIL>
2025-08-01 13:05:40,359 INFO payload: None
2025-08-01 13:05:40,359 INFO files: None
2025-08-01 13:05:40,359 INFO timeout: 30
2025-08-01 13:05:41,765 INFO Getting image list from RETSEELM-NXC000
2025-08-01 13:05:41,765 INFO Got the image list from RETSEELM-NXC000.
2025-08-01 13:05:41,765 INFO The image ICC_2k16_v0015-RETSEELM-NXC000 is found in RETSEELM-NXC000
2025-08-01 13:05:41,775 INFO The image is found in PE
2025-08-01 13:05:41,776 INFO ****************************************************************************************************
2025-08-01 13:05:41,776 INFO *                                                                                                  *
2025-08-01 13:05:41,776 INFO *                                            Update DNS                                            *
2025-08-01 13:05:41,776 INFO *                                                                                                  *
2025-08-01 13:05:41,776 INFO ****************************************************************************************************
2025-08-01 13:05:41,787 INFO Start to assign IP to workload on IPAM...
2025-08-01 13:05:41,787 INFO Start to assign ip for RETSEELM-NT1800.IKEAD2.COM...
2025-08-01 13:05:41,787 INFO Start to find parent subnet by fqdn 'RETSEELM-NXC000.IKEAD2.COM' on IPAM...
2025-08-01 13:05:41,787 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-01 13:05:41,787 INFO params: None
2025-08-01 13:05:41,787 INFO User: <EMAIL>
2025-08-01 13:05:41,787 INFO payload: None
2025-08-01 13:05:41,787 INFO files: None
2025-08-01 13:05:41,787 INFO timeout: 30
2025-08-01 13:05:43,029 INFO ipam_object: <Response [200]>
2025-08-01 13:05:43,029 INFO Parent subnet name: IT NOC DH2 (ELM) DH1 (HBG) IKEAD2
2025-08-01 13:05:43,029 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=parent_subnet_name='IT NOC DH2 (ELM) DH1 (HBG) IKEAD2', method: GET, headers: None
2025-08-01 13:05:43,029 INFO params: None
2025-08-01 13:05:43,029 INFO User: <EMAIL>
2025-08-01 13:05:43,029 INFO payload: None
2025-08-01 13:05:43,029 INFO files: None
2025-08-01 13:05:43,029 INFO timeout: 30
2025-08-01 13:05:48,550 INFO Finding free address in subnet id: 91747
2025-08-01 13:05:48,550 INFO Assigning ip...
2025-08-01 13:05:48,550 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rpc/ip_find_free_address, method: GET, headers: None
2025-08-01 13:05:48,550 INFO params: {'subnet_id': '91747', 'max_find': 30}
2025-08-01 13:05:48,550 INFO User: <EMAIL>
2025-08-01 13:05:48,550 INFO payload: None
2025-08-01 13:05:48,550 INFO files: None
2025-08-01 13:05:48,550 INFO timeout: 30
2025-08-01 13:05:49,652 INFO Checking if ************ is already in use...
2025-08-01 13:05:49,905 INFO IP ************ is not able to use.
2025-08-01 13:05:49,905 INFO Checking if ************ is already in use...
2025-08-01 13:05:53,240 INFO IP ************ is able to use.
2025-08-01 13:05:53,240 INFO kwargs: {'url': 'https://IPAM.IKEA.COM/rest/ip_add', 'method': 'POST', 'params': {'hostaddr': '************', 'site_id': 2, 'name': 'retseelm-nt1800.ikead2.com', 'add_flag': 'new_only', 'ip_name_class': 'IKEA/Server_Distribute', 'ip_class_parameters': 'hostname=RETSEELM-NT1800&dns_update=1&__eip_dns_update_inheritance_property=set&ikea_server_type=Windows&ikea_network_ip_statement= This is a VM initially installed on Nutanix cluster :RETSEELM-NXC000.IKEAD2.COM.&ikea_in_pci=0'}, 'verify': False}
2025-08-01 13:05:55,260 INFO IP ************ assigned to RETSEELM-NT1800.IKEAD2.COM successfully. ip_id is 106249830
2025-08-01 13:05:55,305 INFO IP ************ assigned to workload successfully. ip_id: 106249830
2025-08-01 13:06:05,370 INFO Start to register machine RETSEELM-NT1800 in Thor's Hammer...
2025-08-01 13:06:05,371 INFO Calling restapi, URL: https://thorshammereu.ikea.com/api/runmethod/robo/new-computer/fixed=RETSEELM-NT1800, method: GET, headers: None
2025-08-01 13:06:05,371 INFO params: None
2025-08-01 13:06:05,371 INFO User: L-LTTHOR-A-ITSEELM
2025-08-01 13:06:05,371 INFO payload: None
2025-08-01 13:06:05,371 INFO files: None
2025-08-01 13:06:05,371 INFO timeout: None
2025-08-01 13:06:06,908 INFO Register machine RETSEELM-NT1800 in Thor's Hammer succeeded!
2025-08-01 13:06:06,908 INFO Start to make profile 'TF' under 'RETSEELM'
2025-08-01 13:06:06,908 INFO Calling restapi, URL: https://thorshammereu.ikea.com/api/runmethod/robo/make-profile/Profiletype=TF&prefix=RETSEELM, method: GET, headers: None
2025-08-01 13:06:06,908 INFO params: None
2025-08-01 13:06:06,908 INFO User: L-LTTHOR-A-ITSEELM
2025-08-01 13:06:06,908 INFO payload: None
2025-08-01 13:06:06,908 INFO files: None
2025-08-01 13:06:06,908 INFO timeout: None
2025-08-01 13:06:09,945 INFO Succeeded to make profile 'TF' on site 'RETSEELM'.
2025-08-01 13:06:14,950 INFO Start to add host profile Alias%20SET_INSTALL_TIME_FRAME, machine name: RETSEELM-NT1800
2025-08-01 13:06:14,951 INFO Calling restapi, URL: https://thorshammereu.ikea.com/api/runmethod/robo/add-profile/Profilename=Alias%20SET_INSTALL_TIME_FRAME&computerName=RETSEELM-NT1800, method: GET, headers: None
2025-08-01 13:06:14,951 INFO params: None
2025-08-01 13:06:14,952 INFO User: L-LTTHOR-A-ITSEELM
2025-08-01 13:06:14,952 INFO payload: None
2025-08-01 13:06:14,952 INFO files: None
2025-08-01 13:06:14,952 INFO timeout: None
2025-08-01 13:06:16,600 INFO Succeeded to add host profile.
2025-08-01 13:06:16,603 INFO template_path: c:\Dev\UnitPortalBackend\static\templates\create_vm_by_pe_payload.jinja2
2025-08-01 13:06:16,654 INFO Getting network list from RETSEELM-NXC000
2025-08-01 13:06:16,654 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/networks, method: GET, headers: None
2025-08-01 13:06:16,654 INFO params: None
2025-08-01 13:06:16,654 INFO User: <EMAIL>
2025-08-01 13:06:16,654 INFO payload: None
2025-08-01 13:06:16,654 INFO files: None
2025-08-01 13:06:16,654 INFO timeout: 30
2025-08-01 13:06:18,474 INFO Got the network list from RETSEELM-NXC000.
2025-08-01 13:06:18,474 INFO Vlan 793 is found
2025-08-01 13:06:18,475 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/storage_containers, method: GET, headers: None
2025-08-01 13:06:18,475 INFO params: None
2025-08-01 13:06:18,475 INFO User: <EMAIL>
2025-08-01 13:06:18,475 INFO payload: None
2025-08-01 13:06:18,475 INFO files: None
2025-08-01 13:06:18,475 INFO timeout: 30
2025-08-01 13:06:20,275 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms, method: POST, headers: None
2025-08-01 13:06:20,275 INFO params: None
2025-08-01 13:06:20,275 INFO User: <EMAIL>
2025-08-01 13:06:20,275 INFO payload: {'boot': {'secure_boot': True, 'uefi_boot': True, 'disk_address': {'device_bus': 'scsi', 'device_index': 0}}, 'machine_type': 'Q35', 'memory_mb': 16384, 'name': 'RETSEELM-NT1800', 'num_cores_per_vcpu': 1, 'num_vcpus': 4, 'power_state': 'UNKNOWN', 'vm_disks': [{'disk_address': {'device_bus': 'scsi', 'device_index': 1}, 'is_cdrom': False, 'vm_disk_create': {'size': ************, 'storage_container_uuid': '62cf1853-5f93-41e0-a204-99fb9f7b4b45'}}, {'disk_address': {'device_bus': 'scsi', 'device_index': 0}, 'is_cdrom': False, 'vm_disk_clone': {'disk_address': {'vmdisk_uuid': '0d0d95cc-c8e6-4bde-a832-bca95ca577b6'}, 'minimum_size': 107374182400}}], 'vm_nics': [{'network_uuid': '9531e569-3bec-4d92-8581-6209bea747db'}]}
2025-08-01 13:06:20,276 INFO files: None
2025-08-01 13:06:20,276 INFO timeout: 30
2025-08-01 13:06:22,243 INFO Task created, task_uuid: 2e4f6b70-1771-41af-b08b-437752afc849
2025-08-01 13:06:22,243 INFO Get task status attempting 1/5...
2025-08-01 13:06:22,243 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/2e4f6b70-1771-41af-b08b-437752afc849, method: GET, headers: None
2025-08-01 13:06:22,244 INFO params: None
2025-08-01 13:06:22,244 INFO User: <EMAIL>
2025-08-01 13:06:22,244 INFO payload: None
2025-08-01 13:06:22,244 INFO files: None
2025-08-01 13:06:22,244 INFO timeout: 30
2025-08-01 13:06:24,005 INFO Task status: Succeeded
2025-08-01 13:06:24,005 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/2e4f6b70-1771-41af-b08b-437752afc849, method: GET, headers: None
2025-08-01 13:06:24,005 INFO params: None
2025-08-01 13:06:24,005 INFO User: <EMAIL>
2025-08-01 13:06:24,006 INFO payload: None
2025-08-01 13:06:24,006 INFO files: None
2025-08-01 13:06:24,006 INFO timeout: 30
2025-08-01 13:06:25,736 INFO Create VM by PE API succeeded! VM uuid: aa6b545b-8140-4387-a315-6fa4021a86d5
2025-08-01 13:06:25,767 INFO Start to update activation code, machine name: RETSEELM-NT1800, VM uuid: aa6b545b-8140-4387-a315-6fa4021a86d5
2025-08-01 13:06:25,767 INFO Calling restapi, URL: https://thorshammereu.ikea.com/api/runmethod/robo/update-computer/computername=RETSEELM-NT1800&activationCode=aa6b545b-8140-4387-a315-6fa4021a86d5, method: GET, headers: None
2025-08-01 13:06:25,767 INFO params: None
2025-08-01 13:06:25,767 INFO User: L-LTTHOR-A-ITSEELM
2025-08-01 13:06:25,768 INFO payload: None
2025-08-01 13:06:25,768 INFO files: None
2025-08-01 13:06:25,768 INFO timeout: None
2025-08-01 13:06:32,677 INFO Succeeded to update activation code.
2025-08-01 13:06:32,688 INFO Start to set VM power state to 'ON', VM uuid: aa6b545b-8140-4387-a315-6fa4021a86d5, VM name: None.
2025-08-01 13:06:32,689 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/aa6b545b-8140-4387-a315-6fa4021a86d5/set_power_state, method: POST, headers: None
2025-08-01 13:06:32,689 INFO params: None
2025-08-01 13:06:32,689 INFO User: <EMAIL>
2025-08-01 13:06:32,689 INFO payload: {'transition': 'ON'}
2025-08-01 13:06:32,689 INFO files: None
2025-08-01 13:06:32,689 INFO timeout: 30
2025-08-01 13:06:34,523 INFO Get task status attempting 1/5...
2025-08-01 13:06:34,523 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/73956079-ae07-4f78-9d72-2b93768bf72d, method: GET, headers: None
2025-08-01 13:06:34,523 INFO params: None
2025-08-01 13:06:34,523 INFO User: <EMAIL>
2025-08-01 13:06:34,523 INFO payload: None
2025-08-01 13:06:34,523 INFO files: None
2025-08-01 13:06:34,523 INFO timeout: 30
2025-08-01 13:06:36,304 INFO Task status: Running
2025-08-01 13:06:36,304 INFO Task is not ended, sleep 60s to retry... Task percentage: 1
2025-08-01 13:07:36,305 INFO Get task status attempting 2/5...
2025-08-01 13:07:36,305 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/73956079-ae07-4f78-9d72-2b93768bf72d, method: GET, headers: None
2025-08-01 13:07:36,305 INFO params: None
2025-08-01 13:07:36,305 INFO User: <EMAIL>
2025-08-01 13:07:36,305 INFO payload: None
2025-08-01 13:07:36,305 INFO files: None
2025-08-01 13:07:36,305 INFO timeout: 30
2025-08-01 13:07:37,587 INFO Task status: Succeeded
2025-08-01 13:07:37,588 INFO Successfully set VM power state to 'ON'.
2025-08-01 13:07:37,625 INFO Start to get deployment status for RETSEELM-NT1800 in Thor's Hammer...
2025-08-01 13:07:37,626 INFO Calling restapi, URL: https://thorshammereu.ikea.com/api/runmethod/robo/Test-DeploymentFinished/computerName=RETSEELM-NT1800, method: GET, headers: None
2025-08-01 13:07:37,626 INFO params: None
2025-08-01 13:07:37,626 INFO User: L-LTTHOR-A-ITSEELM
2025-08-01 13:07:37,627 INFO payload: None
2025-08-01 13:07:37,627 INFO files: None
2025-08-01 13:07:37,627 INFO timeout: None
2025-08-01 13:07:39,470 INFO Waiting job status to become success...Current error: Not everything installed yet
2025-08-01 13:07:39,470 INFO Looping '1' out of '120'
2025-08-01 13:07:39,470 INFO Sleeping '120' extra seconds each loop

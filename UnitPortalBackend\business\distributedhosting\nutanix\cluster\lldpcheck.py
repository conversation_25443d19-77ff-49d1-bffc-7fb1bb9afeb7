import json
import paramiko
from typing import List, Optional
import time
import re
import threading
from business.distributedhosting.nutanix.cluster.create_cluster_exception import NodeNotMatchCvm


class LLDPNetworkAnalyzer:
    def __init__(self, task):
        if not hasattr(task, 'check_ahv_cvm_dhcp_and_free_ip_for_wh'):
            raise ValueError(
                "Task object must implement 'check_ahv_cvm_dhcp_and_free_ip_for_wh' method"
            )
        self.cluster_task = task
        self.username = "nutanix"
        self.password = "nutanix/4u"
        self.ilg = task.ilg

    def get_ahv_cvm_info(self, scan_oob_data):
        cvm_dhcps = self.cluster_task.check_ahv_cvm_dhcp_and_free_ip_for_wh()
        if len(cvm_dhcps) > len(scan_oob_data):
            raise NodeNotMatchCvm(len(cvm_dhcps), len(scan_oob_data))
        topology = self.check_switches(cvm_dhcps, scan_oob_data)
        self.ilg.write(f"Found {len(topology)} available nodes:")
        for node in topology:
            self.ilg.write(
                f"Node info - CVM: {node['cvm_ip']}, "
                f"Host: {node['host_ip']}, "
                f"IPMI: {node['ipmi_ip']} (SN: {node['ipmi_sn']}), "
                f"Switch: {node['switch']}"
            )
        
        return topology

    def get_switch_info(self, cvm_ip: str) -> Optional[dict]:
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(cvm_ip, username=self.username, password=self.password)
            
            channel = ssh.invoke_shell()
            time.sleep(2)

            channel.send('ssh root@***********\n')
            time.sleep(2)
            while channel.recv_ready():
                _ = channel.recv(4096)

            host_ip = self.get_host_ip(channel, cvm_ip)
            ipmi_ip = self.get_ipmi_ip(channel, cvm_ip)
            switches = self.get_ahv_lldp_info(channel, cvm_ip)
            return {
                'switches': switches,
                'host_ip': host_ip,
                'ipmi_ip': ipmi_ip
            }
        except Exception as e:
            self.ilg.write(f"Failed to get switch info for {cvm_ip}: {str(e)}")
            return None

    def check_switches(self, cvm_ips: List[str], scan_oob_data) -> List[dict]:
        ip_info = {}
        result = []
        ipmi_sn_map = {item['ip']: item['sn'] for item in scan_oob_data}
        log_lock = threading.Lock()
        tasks = []  # For storing task information

        def _get_switch_info_thread(cvm_ip):
            self.cluster_task.init_flask_app()
            try:
                info = self.get_switch_info(cvm_ip)
                if info and info['switches']:
                    ip_info[cvm_ip] = {
                        'switches': set(info['switches']),
                        'host_ip': info['host_ip'],
                        'ipmi_ip': info['ipmi_ip']
                    }
                    # Store results in tasks list
                    tasks.append({
                        'cvm_ip': cvm_ip,
                        'info': info
                    })
            except Exception as e:
                with log_lock:
                    self.ilg.write(f"Error getting info for {cvm_ip}: {str(e)}")

        # Create and start all threads
        thread_pool = []
        for cvm_ip in cvm_ips:
            t = threading.Thread(
                target=_get_switch_info_thread,
                kwargs={'cvm_ip': cvm_ip}
            )
            t.start()
            thread_pool.append(t)

        # Wait for all threads to complete
        for t in thread_pool:
            t.join()

        # Output logs in original cvm_ips order
        for cvm_ip in cvm_ips:
            task = next((t for t in tasks if t['cvm_ip'] == cvm_ip), None)
            if task:
                info = task['info']
                self.ilg.write(f"Getting switch info for {cvm_ip}")
                self.ilg.write(f"Successfully got information for CVM {cvm_ip}:")
                self.ilg.write(f"  - Host IP: {info['host_ip']}")
                self.ilg.write(f"  - IPMI IP: {info['ipmi_ip']}")
                self.ilg.write(f"  - Switch Names: {info['switches']}")
                self.ilg.write("--------------------------------------------------")

        # Process IPs and their switch connections
        processed_ips = set()
        for cvm_ip1, info1 in ip_info.items():
            if cvm_ip1 in processed_ips:
                continue
                
            switch_name = sorted(info1['switches'])[0]  # Use first switch
            result.append({
                'cvm_ip': cvm_ip1,
                'host_ip': info1['host_ip'],
                'ipmi_ip': info1['ipmi_ip'],
                'ipmi_sn': ipmi_sn_map[info1['ipmi_ip']],
                'switch': switch_name
            })
            processed_ips.add(cvm_ip1)
            
            # Find other IPs connected to the same switches
            for cvm_ip2, info2 in ip_info.items():
                if cvm_ip2 not in processed_ips and info1['switches'] == info2['switches']:
                    result.append({
                        'cvm_ip': cvm_ip2,
                        'host_ip': info2['host_ip'],
                        'ipmi_ip': info2['ipmi_ip'],
                        'ipmi_sn': ipmi_sn_map[info2['ipmi_ip']],
                        'switch': switch_name
                    })
                    processed_ips.add(cvm_ip2)

        return result

    def get_ipmi_ip(self, channel, cvm_ip: str) -> Optional[str]:
        try:
            # Send command to get IPMI IP
            channel.send('sudo ipmitool lan print | grep "IP Address " | grep -v "Source"\n')
            time.sleep(2)
            
            # Get fresh output
            output = ""
            while channel.recv_ready():
                output += channel.recv(4096).decode('utf-8')
            
            # Remove ANSI escape sequences
            ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
            output = ansi_escape.sub('', output)
            
            # Extract IP address
            ip_pattern = r'IP Address\s+:\s+(\d+\.\d+\.\d+\.\d+)'
            match = re.search(ip_pattern, output)
            
            if match:
                ip = match.group(1)
                return ip
            return None
            
        except Exception as e:
            self.ilg.write(f"Failed to get IPMI IP for CVM {cvm_ip}: {str(e)}", severity="error")
            return None


    def get_host_ip(self, channel, cvm_ip: str) -> Optional[str]:

        try:
            channel.send('ifconfig br0 | grep inet\n')
            time.sleep(2)
            
            output = ""
            while channel.recv_ready():
                output += channel.recv(4096).decode('utf-8')
            
            ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
            output = ansi_escape.sub('', output)

            ip_pattern = r'inet\s+(\d+\.\d+\.\d+\.\d+)'
            match = re.search(ip_pattern, output)
            
            if match:
                ip = match.group(1)
                return ip
            return None
            
        except Exception as e:
            self.ilg.write(f"Get host ip failed for CVM {cvm_ip}: {str(e)}", severity="error")
            return None


    def get_ahv_lldp_info(self, channel, cvm_ip: str) -> Optional[List[str]]:

        try:
            # Send LLDP command
            channel.send('lldpcli show neighbors -f json\n')
            time.sleep(2)
            
            # Get output
            output = ""
            while channel.recv_ready():
                output += channel.recv(4096).decode('utf-8')
            
            # Parse JSON data
            try:
                json_start = output.find('{')
                json_end = output.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = output[json_start:json_end]
                    lldp_data = json.loads(json_str)
                    
                    # Extract switch names
                    switch_names = []
                    if 'lldp' in lldp_data and 'interface' in lldp_data['lldp']:
                        for interface in lldp_data['lldp']['interface']:
                            for if_data in interface.values():
                                if 'chassis' in if_data:
                                    switch_name = list(if_data['chassis'].keys())[0]
                                    switch_names.append(switch_name)
                    
                    return switch_names
                    
            except json.JSONDecodeError as e:
                self.ilg.write(f"Failed to parse LLDP JSON: {str(e)}")
                self.ilg.write(f"Raw output: {output}")
                return None
                
        except Exception:
            self.ilg.write(f"Failed to get LLDP info for CVM {cvm_ip}, Please check NIC interface status on the ILO", severity="error")
            return None
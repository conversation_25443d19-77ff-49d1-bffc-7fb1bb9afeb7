2025-08-01 11:23:57,041 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:23:57,042 INFO Getting the cluster list from PC.
2025-08-01 11:23:57,042 INFO Getting cluster list from ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:23:57,043 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/api/nutanix/v3/clusters/list, method: POST, headers: None
2025-08-01 11:23:57,043 INFO params: None
2025-08-01 11:23:57,043 INFO User: <EMAIL>
2025-08-01 11:23:57,043 INFO payload: {'kind': 'cluster'}
2025-08-01 11:23:57,043 INFO files: None
2025-08-01 11:23:57,043 INFO timeout: None
2025-08-01 11:24:18,605 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EDF2BF1150>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:24:18,605 WARNING Call api failed, going to do the 2 retry...
2025-08-01 11:24:39,676 WARNING Call api has exception: HTTPSConnectionPool(host='ssp-dhd2-ntx.ikead2.com', port=9440): Max retries exceeded with url: /api/nutanix/v3/clusters/list (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001EDF27DA410>, 'Connection to ssp-dhd2-ntx.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:24:39,676 WARNING Call api failed, going to do the 3 retry...
2025-08-01 11:24:48,699 CRITICAL User [q] is aborting this PM task.
2025-08-01 11:24:48,700 CRITICAL PM task 1530 aborted by [q].

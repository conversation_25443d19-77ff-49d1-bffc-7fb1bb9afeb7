from apscheduler.executors.pool import ProcessPoolExecutor
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore

from business.distributedhosting.nutanix.automation.seamless_lcm_specs import SeamlessLcmSpec
from business.distributedhosting.nutanix.cluster.cluster_specs import ClusterSpec
from business.generic.commonfunc import DBConfig

class SchedulerConfig(object):
    # SCHEDULER_TIMEZONE = 'Europe/Paris'  # 配置时区
    SCHEDULER_API_ENABLED = True  # 调度器开关
    # 配置mysql
    SQLALCHEMY_DATABASE_URI = DBConfig()()
    # job存储位置
    SCHEDULER_JOBSTORES = {
        ClusterSpec.JOB_STORE: SQLAlchemyJobStore(tablename=ClusterSpec.JOB_STORE, url=SQLALCHEMY_DATABASE_URI),
        SeamlessLcmSpec.JOB_STORE: SQLAlchemyJobStore(tablename=SeamlessLcmSpec.JOB_STORE, url=SQLALCHEMY_DATABASE_URI),
        'job_store_atm': SQLAlchemyJobStore(tablename='job_store_atm', url=SQLALCHEMY_DATABASE_URI),
    }
    # 线程池配置
    SCHEDULER_EXECUTORS = {
        # 'default': {'type': 'threadpool', 'max_workers': 2}
        'processpool': ProcessPoolExecutor(1)
    }

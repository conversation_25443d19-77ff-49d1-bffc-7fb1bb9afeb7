2025-07-31 13:40:18,963 INFO Start to run the task.
2025-07-31 13:40:18,995 INFO ****************************************************************************************************
2025-07-31 13:40:18,995 INFO *                                                                                                  *
2025-07-31 13:40:18,995 INFO *                                        Check VM existence                                        *
2025-07-31 13:40:19,005 INFO *                                                                                                  *
2025-07-31 13:40:19,010 INFO ****************************************************************************************************
2025-07-31 13:40:19,053 INFO Checking if vm already exists in the PE cluster.
2025-07-31 13:40:19,053 INFO Checking if RETSEELM-LX7300 existed in PE.
2025-07-31 13:40:19,053 INFO Getting VM list from RETSEELM-NXC000.
2025-07-31 13:40:19,053 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms?sortCriteria=vm_name&searchString=RETSEELM-LX7300, method: GET, headers: None
2025-07-31 13:40:19,055 INFO params: None
2025-07-31 13:40:19,055 INFO User: <EMAIL>
2025-07-31 13:40:19,055 INFO payload: None
2025-07-31 13:40:19,055 INFO files: None
2025-07-31 13:40:19,055 INFO timeout: 30
2025-07-31 13:40:20,696 INFO Got the VM list from RETSEELM-NXC000.
2025-07-31 13:40:20,696 INFO RETSEELM-LX7300 doesn't exist in RETSEELM-NXC000.
2025-07-31 13:40:20,733 INFO RETSEELM-LX7300 not exists in Cluster RETSEELM-NXC000.IKEAD2.COM, move on...
2025-07-31 13:40:20,772 INFO Checking if vm already exists in the inventory AD/Tower.
2025-07-31 13:40:22,164 INFO FQDN 'RETSEELM-LX7300.IKEAD2.COM' not exists in Tower Inventory, continue...
2025-07-31 13:40:22,210 INFO Checking if vm already exists in IPAM.
2025-07-31 13:40:22,230 INFO Start to check if RETSEELM-LX7300.IKEAD2.COM existed in IPAM...
2025-07-31 13:40:22,230 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-LX7300.IKEAD2.COM', method: GET, headers: None
2025-07-31 13:40:22,230 INFO params: None
2025-07-31 13:40:22,230 INFO User: <EMAIL>
2025-07-31 13:40:22,230 INFO payload: None
2025-07-31 13:40:22,230 INFO files: None
2025-07-31 13:40:22,230 INFO timeout: 30
2025-07-31 13:40:23,629 INFO 'RETSEELM-LX7300' not exists in IPAM, continue...
2025-07-31 13:40:23,673 INFO ****************************************************************************************************
2025-07-31 13:40:23,674 INFO *                                                                                                  *
2025-07-31 13:40:23,674 INFO *                                              Sizing                                              *
2025-07-31 13:40:23,674 INFO *                                                                                                  *
2025-07-31 13:40:23,674 INFO ****************************************************************************************************
2025-07-31 13:40:23,715 INFO Sizing, check if cluster has enough capacity for this VM.
2025-07-31 13:40:23,716 INFO Get a list of existing hosts from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:40:23,716 INFO Calling /hosts through v1 API using GET method
2025-07-31 13:40:23,716 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-07-31 13:40:23,716 INFO params: None
2025-07-31 13:40:23,716 INFO User: <EMAIL>
2025-07-31 13:40:23,716 INFO payload: None
2025-07-31 13:40:23,716 INFO files: None
2025-07-31 13:40:23,716 INFO timeout: None
2025-07-31 13:40:25,485 INFO Get cluster details from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:40:25,485 INFO Calling /cluster through v1 API using GET method
2025-07-31 13:40:25,485 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster, method: GET, headers: None
2025-07-31 13:40:25,485 INFO params: None
2025-07-31 13:40:25,485 INFO User: <EMAIL>
2025-07-31 13:40:25,485 INFO payload: None
2025-07-31 13:40:25,485 INFO files: None
2025-07-31 13:40:25,485 INFO timeout: None
2025-07-31 13:40:27,311 INFO Get a list of existing user VMs from RETSEELM-NXC000.IKEAD2.COM
2025-07-31 13:40:27,312 INFO Calling /vms through v2 API using GET method
2025-07-31 13:40:27,312 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms, method: GET, headers: None
2025-07-31 13:40:27,312 INFO params: None
2025-07-31 13:40:27,312 INFO User: <EMAIL>
2025-07-31 13:40:27,312 INFO payload: None
2025-07-31 13:40:27,312 INFO files: None
2025-07-31 13:40:27,312 INFO timeout: None
2025-07-31 13:40:29,059 INFO The cluster is a 3 node(s) cluster
2025-07-31 13:40:29,059 INFO Fetching capacity from node RETSEELM-NX7001
2025-07-31 13:40:29,059 INFO Storage of node RETSEELM-NX7001 is 44.68 TiB
2025-07-31 13:40:29,059 INFO Number of cores on node RETSEELM-NX7001 is 20
2025-07-31 13:40:29,059 INFO Memory install on node RETSEELM-NX7001 is 377.08 GiB
2025-07-31 13:40:29,059 INFO Fetching capacity from node RETSEELM-NX7002
2025-07-31 13:40:29,059 INFO Storage of node RETSEELM-NX7002 is 44.68 TiB
2025-07-31 13:40:29,059 INFO Number of cores on node RETSEELM-NX7002 is 20
2025-07-31 13:40:29,059 INFO Memory install on node RETSEELM-NX7002 is 377.08 GiB
2025-07-31 13:40:29,059 INFO Fetching capacity from node RETSEELM-NX7003
2025-07-31 13:40:29,059 INFO Storage of node RETSEELM-NX7003 is 44.68 TiB
2025-07-31 13:40:29,059 INFO Number of cores on node RETSEELM-NX7003 is 20
2025-07-31 13:40:29,059 INFO Memory install on node RETSEELM-NX7003 is 345.58 GiB
2025-07-31 13:40:29,059 INFO Number of nodes in this cluster is 3
2025-07-31 13:40:29,059 INFO Total storage capacity on this cluster is 134.04 TiB
2025-07-31 13:40:29,059 INFO total number of CPU cores on cluster is 60
2025-07-31 13:40:29,059 INFO Total memory capacity on this cluster is 1099.74 GiB
2025-07-31 13:40:29,059 INFO Resilient storage capacity on this cluster is 84.************** TiB
2025-07-31 13:40:29,059 INFO Number of resilient physical CPU cores is 40
2025-07-31 13:40:29,059 INFO Number of resilient physical CPU cores accounting CVMs is 34
2025-07-31 13:40:29,059 INFO Number of resilient virtual CPU cores (assuming 1:4 ratio) is 136
2025-07-31 13:40:29,059 INFO Resilient memory capacity on this cluster is 722.************* GiB
2025-07-31 13:40:29,059 INFO Resilient memory capacity accounting CVMs on this cluster is 658.************* GiB
2025-07-31 13:40:29,059 INFO Utilized storage of cluster is 0.95 TiB
2025-07-31 13:40:29,059 INFO There are 5 VMs on this cluster
2025-07-31 13:40:29,059 INFO Number of virtual cores used by 5 VMs that are powered on is 62
2025-07-31 13:40:29,059 INFO Memory used by 5 VMs that are powered on is 248.0 GiB
2025-07-31 13:40:29,059 INFO Available storage for new VM provisioning is 83.************** TiB
2025-07-31 13:40:29,059 INFO Available vCPU cores for new VM provisioning is 74
2025-07-31 13:40:29,059 INFO Available memory for new VM provisioning is 410.************* GiB
2025-07-31 13:40:29,104 INFO ****************************************************************************************************
2025-07-31 13:40:29,105 INFO *                                                                                                  *
2025-07-31 13:40:29,105 INFO *                                Checking workload network on NTX.                                 *
2025-07-31 13:40:29,105 INFO *                                                                                                  *
2025-07-31 13:40:29,105 INFO ****************************************************************************************************
2025-07-31 13:40:29,255 INFO Checking PE network by VlanId=793
2025-07-31 13:40:29,255 INFO Getting network list from RETSEELM-NXC000
2025-07-31 13:40:29,255 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/networks, method: GET, headers: None
2025-07-31 13:40:29,255 INFO params: None
2025-07-31 13:40:29,255 INFO User: <EMAIL>
2025-07-31 13:40:29,255 INFO payload: None
2025-07-31 13:40:29,255 INFO files: None
2025-07-31 13:40:29,255 INFO timeout: 30
2025-07-31 13:40:30,549 INFO Got the network list from RETSEELM-NXC000.
2025-07-31 13:40:30,590 INFO The network is NOT created, check if other tasks are running with the same network requirements.
2025-07-31 13:40:30,665 INFO The current task owns the lowest id, will create network with the name RETSEELM-793-Undefine
2025-07-31 13:40:30,667 INFO Creating network RETSEELM-793-Undefine with vlan 793 in RETSEELM-NXC000
2025-07-31 13:40:30,667 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/networks, method: POST, headers: None
2025-07-31 13:40:30,668 INFO params: None
2025-07-31 13:40:30,668 INFO User: <EMAIL>
2025-07-31 13:40:30,668 INFO payload: {'name': 'RETSEELM-793-Undefine', 'vlan_id': '793'}
2025-07-31 13:40:30,668 INFO files: None
2025-07-31 13:40:30,668 INFO timeout: 30
2025-07-31 13:40:33,534 INFO ****************************************************************************************************
2025-07-31 13:40:33,534 INFO *                                                                                                  *
2025-07-31 13:40:33,534 INFO *                                           Check image                                            *
2025-07-31 13:40:33,534 INFO *                                                                                                  *
2025-07-31 13:40:33,534 INFO ****************************************************************************************************
2025-07-31 13:40:33,575 INFO Verifying workload image existence in DB and on cluster.
2025-07-31 13:40:33,599 INFO Checking if RHELx_AUTO image
2025-07-31 13:40:33,599 INFO Validating image 'RHEL9.2-RETSEELM-NXC000' existence in PE 'RETSEELM-NXC000'...
2025-07-31 13:40:33,599 INFO Start to find the image RHEL9.2-RETSEELM-NXC000 from RETSEELM-NXC000
2025-07-31 13:40:33,599 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/images, method: GET, headers: None
2025-07-31 13:40:33,599 INFO params: None
2025-07-31 13:40:33,599 INFO User: <EMAIL>
2025-07-31 13:40:33,599 INFO payload: None
2025-07-31 13:40:33,605 INFO files: None
2025-07-31 13:40:33,605 INFO timeout: 30
2025-07-31 13:40:35,457 INFO Getting image list from RETSEELM-NXC000
2025-07-31 13:40:35,457 INFO Got the image list from RETSEELM-NXC000.
2025-07-31 13:40:35,493 INFO Image doesn't exist on PE, will upload.
2025-07-31 13:40:35,493 INFO Start to upload the image to PE...
2025-07-31 13:40:35,493 INFO Prepare to upload image RHEL9.2 to RETSEELM-NXC000
2025-07-31 13:40:35,493 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/containers, method: GET, headers: None
2025-07-31 13:40:35,493 INFO params: None
2025-07-31 13:40:35,493 INFO User: <EMAIL>
2025-07-31 13:40:35,493 INFO payload: None
2025-07-31 13:40:35,493 INFO files: None
2025-07-31 13:40:35,493 INFO timeout: 30
2025-07-31 13:40:37,243 INFO Getting container list from RETSEELM-NXC000
2025-07-31 13:40:37,244 INFO Got the container list from RETSEELM-NXC000.
2025-07-31 13:40:37,244 INFO The container SelfServiceContainer is found
2025-07-31 13:40:37,244 INFO The container SelfServiceContainer is located, its UUID is 62cf1853-5f93-41e0-a204-99fb9f7b4b45
2025-07-31 13:40:37,275 INFO Get the image RHEL9.2-RETSEELM-NXC000 from D:\Image\rhel-92-401006d-20240601T053114
2025-07-31 13:40:37,276 INFO Upload the image RHEL9.2-RETSEELM-NXC000 to RETSEELM-NXC000
2025-07-31 13:40:37,276 INFO Uploading image RHEL9.2-RETSEELM-NXC000 to RETSEELM-NXC000
2025-07-31 13:40:37,276 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/api/nutanix/v0.8/images, method: POST, headers: None
2025-07-31 13:40:37,276 INFO params: None
2025-07-31 13:40:37,276 INFO User: <EMAIL>
2025-07-31 13:40:37,276 INFO payload: {'name': 'RHEL9.2-RETSEELM-NXC000', 'annotation': 'RHEL9.2-RETSEELM-NXC000', 'imageType': 'DISK_IMAGE', 'imageImportSpec': {'containerUuid': '62cf1853-5f93-41e0-a204-99fb9f7b4b45', 'url': 'D:\\Image\\rhel-92-401006d-20240601T053114'}}
2025-07-31 13:40:37,276 INFO files: None
2025-07-31 13:40:37,276 INFO timeout: 30
2025-07-31 13:40:38,606 INFO It's uploading the image, the task id is c1530aca-ba83-47cc-97a4-b85165955520
2025-07-31 13:40:38,607 INFO Get task status attempting 1/120...
2025-07-31 13:40:38,607 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/c1530aca-ba83-47cc-97a4-b85165955520, method: GET, headers: None
2025-07-31 13:40:38,607 INFO params: None
2025-07-31 13:40:38,607 INFO User: <EMAIL>
2025-07-31 13:40:38,607 INFO payload: None
2025-07-31 13:40:38,607 INFO files: None
2025-07-31 13:40:38,607 INFO timeout: 30
2025-07-31 13:40:40,392 INFO Task status: Failed
2025-07-31 13:40:40,507 ERROR Task failed. Detail: ['Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\base_up_task.py", line 88, in start_task\n    self.task_process()\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\workload\\workload.py", line 187, in task_process\n    self.get_wl_image(_pc, _pe)\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\workload\\workload.py", line 284, in get_wl_image\n    self.upload_wl_image_to_pe(_pe=_pe)  # Upload image to PE\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\workload\\workload.py", line 256, in upload_wl_image_to_pe\n    raise ImageUploadFailed(in_progress=True, image_name=image_pe_name, original_err=msg)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', "TypeError: ImageUploadFailed.__init__() missing 1 required positional argument: 'init'\n"]

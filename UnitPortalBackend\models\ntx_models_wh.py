# import sqlalchemy
# import werkzeug.exceptions as flaskex
from .database import db, ma
from sqlalchemy.ext.hybrid import hybrid_method
import sqlalchemy
import werkzeug.exceptions as flaskex


class ModelWHMOVETask(db.Model):
    __tablename__      = 'dh_wh_ntx_move_task'
    id                 = db.Column(db.Integer , primary_key=True)
    pe                 = db.Column(db.String(100))
    cluster            = db.Column(db.String(100))
    createdate         = db.Column(db.String(100))
    status             = db.Column(db.String(100))
    datasyncstatus     = db.Column(db.String(100))
    pid                = db.Column(db.Integer)
    creater            = db.Column(db.String(100))
    detaillogpath      = db.Column(db.String(255))
    tasktype           = db.Column(db.String(100))
    
    def check_ifmovetask_existence(self, tasktype, pe):
        return ModelWHMOVETask.query.filter(ModelWHMOVETask.status == "In Progress", ModelWHMOVETask.tasktype == tasktype, ModelWHMOVETask.pe == pe).first()



class ModelWHMOVETaskSchema(ma.Schema):
    class Meta:
        fields = ('id', 'pe', 'cluster', 'createdate', 'status', 'datasyncstatus', 'pid', 'creater', 'detaillogpath', 'tasktype')


class ModelWHNTXMOVELog(db.Model):
    __tablename__ = 'dh_wh_ntx_move_log'
    id            = db.Column(db.Integer, primary_key=True)
    tasktype      = db.Column(db.String(50))
    logdate       = db.Column(db.String(50))
    severity      = db.Column(db.String(50))
    # cluster_id = db.Column(db.String(50))
    loginfo       = db.Column(db.String(8000))
    taskid        = db.Column(db.Integer)


class ModelWHNTXMOVELogSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWHNTXMOVELog
        load_instance = True
   
        
class ModelWHNTXMOVE(db.Model):
    __tablename__      = 'dh_wh_ntx_move'
    id                 = db.Column(db.Integer , primary_key=True)
    pe                 = db.Column(db.String(100))
    prism              = db.Column(db.String(100))
    cluster            = db.Column(db.String(100))
    moveipa            = db.Column(db.String(100))
    moveipb            = db.Column(db.String(100))
    createdate         = db.Column(db.String(100))
    movedevop          = db.Column(db.String(100))
    datasyncstatus     = db.Column(db.String(100))
    stagepreparation   = db.Column(db.String(100))
    stagecutover       = db.Column(db.String(100))
    status             = db.Column(db.String(100))
    planuuid           = db.Column(db.String(100))
    syncstatus           = db.Column(db.String(100))
    
    def check_ifcluster_existence(self, cluster_name):
        return ModelWHNTXMOVE.query.filter(ModelWHNTXMOVE.status == "Y", ModelWHNTXMOVE.cluster == cluster_name).first()
   
    @hybrid_method    
    def get_syncing_task_move(self):
        cluster = ModelWHNTXMOVE.query.filter(ModelWHNTXMOVE.stagepreparation != "Done", ModelWHNTXMOVE.stagepreparation != "Error", ModelWHNTXMOVE.syncstatus == "Syncing").all()
        return cluster


class ModelWHNTXMOVESchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWHNTXMOVE
        load_instance = True


class ModelWarehousePrismCentral(db.Model):
    __tablename__        = 'dh_wh_ntx_pc'
    id                   = db.Column(db.Integer, primary_key=True)
    fqdn                 = db.Column(db.String(50), unique=True, nullable=False)
    lcm_version          = db.Column(db.String(200))
    calm_version         = db.Column(db.String(200))
    cmu_version          = db.Column(db.String(200))
    epsilon_version      = db.Column(db.String(200))
    flowsecurity_Version = db.Column(db.String(200))
    licensing_version    = db.Column(db.String(200))
    ncc_version          = db.Column(db.String(200))
    pc_version           = db.Column(db.String(200))
    uuid                 = db.Column(db.String(200))
    cluster_number       = db.Column(db.Integer)
    ip                   = db.Column(db.String(50))
    tier                 = db.Column(db.String(50))
    domain               = db.Column(db.String(50))
    darksite             = db.Column(db.String(50))
    central_pe_fqdn      = db.Column(db.String(200))
    service_account      = db.Column(db.String(50))
    cert_expiry_date     = db.Column(db.String(100))
    oneview_region       = db.Column(db.String(255))
    bmk_id               = db.Column(db.Integer)
    last_update          = db.Column(db.String(50))


class ModelWarehousePrismCentralSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWarehousePrismCentral
        load_instance = True


class ModelWarehousePrismElement(db.Model):
    __tablename__                = 'dh_wh_ntx_pe'
    id                           = db.Column(db.Integer, primary_key=True)
    name                         = db.Column(db.String(50))
    fqdn                         = db.Column(db.String(100), unique=True, nullable=False)
    country_code                 = db.Column(db.String(50))
    site_code                    = db.Column(db.String(50))
    node_number                  = db.Column(db.Integer)
    vm_number                    = db.Column(db.Integer)
    cvm_number                   = db.Column(db.Integer)
    aos_version                  = db.Column(db.String(100))
    ahv_version                  = db.Column(db.String(100))
    uuid                         = db.Column(db.String(100))
    remote_site                  = db.Column(db.String(100))
    remote_backup_number         = db.Column(db.String(100))
    status                       = db.Column(db.String(50))
    prism                        = db.Column(db.String(50))
    timezone                     = db.Column(db.String(50))
    total_memory                 = db.Column(db.Integer)
    total_storage                = db.Column(db.Integer)
    total_cpu                    = db.Column(db.Integer)
    pc_id                        = db.Column(db.Integer)
    foundation_version           = db.Column(db.String(50))
    lcm_version                  = db.Column(db.String(50))
    ncc_version                  = db.Column(db.String(50))
    spp_version                  = db.Column(db.String(100))
    license_category             = db.Column(db.String(100))
    license_class                = db.Column(db.String(100))
    license_cores_capacity       = db.Column(db.String(100))
    license_flash_capacity       = db.Column(db.String(100))
    license_hdd_capacity         = db.Column(db.String(100))
    license_cores_licensed       = db.Column(db.String(100))
    license_flash_licensed       = db.Column(db.String(100))
    license_hdd_licensed         = db.Column(db.String(100))
    cert_expiry_date             = db.Column(db.String(100))
    is_central_pe                = db.Column(db.Boolean)
    site_type                    = db.Column(db.String(100))
    cbd_id                       = db.Column(db.String(50))
    bu_type                      = db.Column(db.String(50))
    bu_code                      = db.Column(db.String(50))
    bmk_id                       = db.Column(db.Integer)
    last_update                  = db.Column(db.String(50))
    default_bandwidth_limit_mbps = db.Column(db.Float)
    
    def check_ifntx_existence_db (self, cluster_name):
        return ModelWarehousePrismElement.query.filter(ModelWarehousePrismElement.status == "Running", ModelWarehousePrismElement.name == cluster_name).first()
    
    @hybrid_method
    def get_prism_by_pe_name(self, pe_fqdn):
        try:
            return ModelWarehousePrismElement.query.filter_by(fqdn=pe_fqdn).one().prism
        except sqlalchemy.exc.NoResultFound as e:
            raise flaskex.InternalServerError(f"Can't find pe with fqdn {pe_fqdn}! Original error: {e}")
        except sqlalchemy.exc.MultipleResultsFound as e:
            raise flaskex.InternalServerError(f"Multiple pe info found with fqdn {pe_fqdn}! Original error: {e}")


class ModelWarehousePrismElementSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWarehousePrismElement
        load_instance = True


class ModelWarehouseVmwareVMs(db.Model):
    __tablename__           = 'dh_wh_vmware_vms'
    id                      = db.Column(db.Integer, primary_key=True)
    name                    = db.Column(db.String(50))
    version                 = db.Column(db.String(100), unique=True, nullable=False)
    folder_id               = db.Column(db.String(50))
    folder                  = db.Column(db.String(50))
    resourcepool_id         = db.Column(db.Integer)
    persistent_id           = db.Column(db.Integer)
    used_space_gb           = db.Column(db.Integer)
    provisioned_space_gb    = db.Column(db.String(100))
    memory_total_gb         = db.Column(db.String(100))
    harestart_priority      = db.Column(db.String(100))
    num_cpu                 = db.Column(db.String(100))
    cores_persocket         = db.Column(db.String(100))
    haisolation_response    = db.Column(db.String(50))
    guest_id                = db.Column(db.String(50))
    host_id                 = db.Column(db.String(50))
    vmhost_name             = db.Column(db.String(100))
    power_state             = db.Column(db.String(100))
    guest_name              = db.Column(db.String(100))
    vm_id                   = db.Column(db.Integer)
    create_date             = db.Column(db.String(50))


class ModelWarehouseVmwareVMsSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWarehouseVmwareVMs
        load_instance = True
    
        
class ModelWarehouseNutanixHost(db.Model):
    __tablename__       = 'dh_wh_ntx_host'
    id                  = db.Column(db.Integer, primary_key=True)
    name                = db.Column(db.String(255))
    uuid                = db.Column(db.String(255))
    pe_name             = db.Column(db.String(100))
    pe_fqdn             = db.Column(db.String(100))
    pe_uuid             = db.Column(db.String(100))
    disk_number         = db.Column(db.Integer)
    sn                  = db.Column(db.String(100))
    model               = db.Column(db.String(100))
    memory              = db.Column(db.String(100))
    cpu_core_number     = db.Column(db.Integer)
    cpu_model           = db.Column(db.String(50))
    ahv_ip              = db.Column(db.String(100))
    cvm_ip              = db.Column(db.String(100))
    ipmi_ip             = db.Column(db.String(100))
    ipmi_version        = db.Column(db.String(100))
    status              = db.Column(db.String(50))
    pe_id               = db.Column(db.Integer)
    bios_version        = db.Column(db.String(50))
    controller_version  = db.Column(db.String(50))
    nic0_uuid           = db.Column(db.String(100))
    nic0_mac            = db.Column(db.String(100))
    nic0_speed          = db.Column(db.String(100))
    nic0_mtu            = db.Column(db.String(100))
    nic0_sw_device      = db.Column(db.String(100))
    nic0_sw_port        = db.Column(db.String(100))
    nic0_sw_vendor      = db.Column(db.String(255))
    nic0_sw_vlan        = db.Column(db.String(100))
    nic1_uuid           = db.Column(db.String(100))
    nic1_mac            = db.Column(db.String(100))
    nic1_speed          = db.Column(db.String(100))
    nic1_mtu            = db.Column(db.String(100))
    nic1_sw_device      = db.Column(db.String(100))
    nic1_sw_port        = db.Column(db.String(100))
    nic1_sw_vendor      = db.Column(db.String(255))
    nic1_sw_vlan        = db.Column(db.String(100))
    controller_model    = db.Column(db.String(50))
    nic2_uuid           = db.Column(db.String(100))
    nic2_mac            = db.Column(db.String(100))
    nic2_speed          = db.Column(db.String(100))
    nic2_mtu            = db.Column(db.String(100))
    nic2_sw_device      = db.Column(db.String(100))
    nic2_sw_port        = db.Column(db.String(100))
    nic2_sw_vendor      = db.Column(db.String(255))
    nic2_sw_vlan        = db.Column(db.String(100))
    nic3_uuid           = db.Column(db.String(100))
    nic3_mac            = db.Column(db.String(100))
    nic3_speed          = db.Column(db.String(100))
    nic3_mtu            = db.Column(db.String(100))
    nic3_sw_device      = db.Column(db.String(100))
    nic3_sw_port        = db.Column(db.String(100))
    nic3_sw_vendor      = db.Column(db.String(255))
    nic3_sw_vlan        = db.Column(db.String(100))
    last_update         = db.Column(db.String(50))


class ModelWarehouseNutanixHostSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWarehouseNutanixHost
        load_instance = True
  
        
class ModelWarehouseNutanixVM(db.Model):
    __tablename__ = 'dh_wh_ntx_vm'
    id            = db.Column(db.Integer, primary_key=True)
    name          = db.Column(db.String(255))
    uuid          = db.Column(db.String(255))
    pe_name       = db.Column(db.String(100))
    pe_fqdn       = db.Column(db.String(100))
    pe_uuid       = db.Column(db.String(100))
    host_name     = db.Column(db.String(100))
    host_uuid     = db.Column(db.String(100))
    description   = db.Column(db.String(1000))
    os            = db.Column(db.String(255))
    type          = db.Column(db.String(255))
    disk          = db.Column(db.String(1000))
    memory        = db.Column(db.String(100))
    cpu_core      = db.Column(db.Integer)
    power_state   = db.Column(db.String(50))
    ip            = db.Column(db.String(1000))
    boot_type     = db.Column(db.String(100))
    network_vlan  = db.Column(db.String(100))
    is_cvm        = db.Column(db.Boolean)
    status        = db.Column(db.String(50))
    pe_id         = db.Column(db.Integer)
    last_update   = db.Column(db.String(50))


class ModelWarehouseNutanixVMSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWarehouseNutanixVM
        load_instance = True
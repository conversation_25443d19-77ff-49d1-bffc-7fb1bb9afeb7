import re
from collector.collectors.base_collector import BaseCollector
from business.distributedhosting.nutanix.nutanix import PrismElement
from business.distributedhosting.nutanix.pe_components import RestRemoteSite

from business.distributedhosting.nutanix.protection_domain import ProtectionDomain

'''
This collector will collect the remote site infomation from all PE.
It will update 4 fields in dh_retail_ntx_pe table
1. remote_backup_number
2. remote_site_runtime
3. remote_site_last
4. default_bandwidth_limit_mbps
For the concrete requirements, see the link below.
https://confluence.build.ingka.ikea.com/display/TECHNOLOGY/Remote+sites
'''


class RSCollector(BaseCollector):
    # RS = Remote Site
    def __init__(self, sa=None) -> None:
        super().__init__(sa)
        self.vault_instance = dict()

    def collect(self):
        # Get PE list from database (in running  status)
        pe_list = PrismElement.get_retail_pe_list_from_db(status="Running")
        # {'id': 456, 'name': 'RETJP697-NXC000', 'fqdn': 'retjp697-nxc000.ikea.com', 'prism':'ssp-apac-ntx.ikea.com', 'tier':"PRODUCTION"...}
        # Loop the PE list
        all_pe = dict()
        for _pe in pe_list[1:5]:
            # Get PE protection domain
            # We are suppposed to use CVM/AHV local accounts to do the query
            all_pe[_pe['name']] = dict()
            try:
                account = self.get_pe_svc_account(
                    pe=_pe,
                    vault=self.get_vault_instance(_pe)
                )
                latest_rs_data = self.get_rs_data(pe=_pe, account=account)
                all_pe[_pe['name']] = latest_rs_data
            except Exception as e:
                print(e)
        print(all_pe)

    def get_rs_data(self, pe, account):
        # pe is {'id': 456, 'name': 'RETJP697-NXC000', 'fqdn': 'retjp697-nxc000.ikea.com', 'prism':'ssp-apac-ntx.ikea.com', 'tier':"PRODUCTION"...}
        # account is {'username':'abc', 'secret':'secret','description':'XDXDXD'}
        # get related pd first, and then get the Remote site
        res = dict()
        related_pd = self.get_related_pd(pe=pe, account=account)
        # related_pd is an object
        rs_list = related_pd.get('remote_site_names', None)
        if not rs_list:
            raise Exception("We found the protection domain, but it's not pointing to any remote site??")
        # rs_list is like ['RS_RETSEHBG-NXC000','RS_RETSE012-NXC000',..]
        if len(rs_list) > 1:
            print("too many remote sites configured.")
        rs_runtime = ";".join([_rs.replace("RS_", "") for _rs in rs_list])
        res['remote_site_runtime'] = rs_runtime
        # get remote backup number
        snapshots = self.get_related_snapshot(pe=pe, account=account)
        res['remote_backup_number'] = len(snapshots)
        # get default bandwidth
        _rs = RestRemoteSite(pe=pe['name'], sa={"username": account['username'], "password": account['secret']})
        rs = _rs.get_remote_site_detail(site_name=rs_list[0])
        res['default_bandwidth_limit_mbps'] = rs['bandwidthPolicy']['defaultBandwidthLimit']/1000000
        return res

    def get_related_snapshot(self, pe, account):
        _pe = PrismElement(pe=pe['name'], sa={"username": account['username'], "password": account['secret']})
        res, remote_site_dr_snapshot = _pe.get_remote_site_dr_snapshot()
        if not res:
            raise Exception("Failed to get snapshots.")
        snapshots = remote_site_dr_snapshot.get('entities', None)
        return [_snapshot for _snapshot in snapshots if re.search(pe['name'], _snapshot['protectionDomainName'], re.I)]

    def get_related_pd(self, pe, account):
        print(f'getting pd of {pe["name"]}')
        pd = ProtectionDomain(pe=pe['name'], sa={"username": account['username'], "password": account['secret']})
        pds = pd.get_pds()
        pd_list = pds.get('entities', None)
        if not pd_list:
            raise Exception("Failed to get 'entities' of the api result.")
        match_regex_1 = pe['name']
        match_regex_2 = "gold_ccg" if pe['node_number'] > 1 else "silver_ccg"
        # find out the pd that has the correct name.
        # it should be like PE_name-Gold/Silver_CCG
        related_pd = [
            _pd for _pd in pd_list
            if (re.search(match_regex_1, _pd['name'], re.I) and re.search(match_regex_2, _pd['name'], re.I))
        ]
        if not related_pd:
            raise Exception("We didn't find a pd that matches {match_regex_1} and {match_regex_2}")
        if len(related_pd) > 1:
            print("too many pd detected!!!")
        return related_pd[0]

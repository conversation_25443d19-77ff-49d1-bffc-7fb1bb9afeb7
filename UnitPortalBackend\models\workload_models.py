import json

import sqlalchemy
import logging
from sqlalchemy.ext.hybrid import hybrid_property
import werkzeug.exceptions as flaskex
from business.distributedhosting.nutanix.task_status import TaskStatus
from sqlalchemy.ext.hybrid import hybrid_method

from models.auth_models import ModelSubRoleMarketplace
from models.database import db, ma
from static.WORKLOAD_SPEC import WorkloadSpec, TemplateSpec


class ModelWorkloadTemplate(db.Model):
    __tablename__      = 'dh_retail_ntx_workload_template'
    id                 = db.Column(db.Integer, primary_key=True)
    name               = db.Column(db.String(255))
    workload_type      = db.Column(db.String(255))
    cpu                = db.Column(db.Integer)
    cpu_core           = db.Column(db.Integer)
    memory             = db.Column(db.Integer)
    disk               = db.Column(db.String(255))
    boot_mode          = db.Column(db.String(255))
    naming_convention  = db.Column(db.String(255))
    vlan_id            = db.Column(db.Integer)
    description        = db.Column(db.String(255))
    image_id           = db.Column(db.Integer)
    image_name         = db.Column(db.String(255))
    linux_payload      = db.Column(db.String(8000))
    app_package        = db.Column(db.String(255))
    oscustomization_id = db.Column(db.Integer)
    owner_id           = db.Column(db.Integer)
    update_dns         = db.Column(db.Boolean)

    @hybrid_method
    def get_template_by_id(self, template_id):
        try:
            template = ModelWorkloadTemplate.query.filter_by(id=template_id).one()
            return template
        except sqlalchemy.exc.NoResultFound as e:
            msg = f"Can't find template with id {template_id}!"
            logging.error(msg)
            raise e
        except sqlalchemy.exc.MultipleResultsFound as e:
            db.session.rollback()
            msg = f"Multiple template found with id {template_id}!"
            logging.error(msg)
            raise e

    @hybrid_method
    def create(self, specs):
        try:
            new_template = ModelWorkloadTemplate(**specs)
            db.session.add(new_template)
            db.session.commit()
            return new_template
        except sqlalchemy.exc.IntegrityError as e:
            raise flaskex.Conflict(
                f"Template with name '{specs[TemplateSpec.TEMPLATE_NAME]}' already exists! Error: {e}")

    @hybrid_method
    def bulk_delete_by_id(self, template_ids):
        """
        1. Delete rows from table `dh_retail_ntx_workload_template`
        2. Delete template id from `role_marketplace` - `create_wl_scope`
        """
        ModelWorkloadTemplate.query.filter(ModelWorkloadTemplate.id.in_(template_ids)).delete()
        sub_roles = ModelSubRoleMarketplace.query.filter(ModelSubRoleMarketplace.create_wl_scope != None).all()
        if not sub_roles:
            logging.info("This template is not included in any roles...")
        for role in sub_roles:
            try:
                create_wl_scope = json.loads(role.create_wl_scope)
                if not create_wl_scope:
                    continue
                for template_id in template_ids:
                    if template_id in create_wl_scope["template_ids"]:
                        create_wl_scope["template_ids"].remove(template_id)
                role.create_wl_scope = json.dumps(create_wl_scope)
            except Exception as e:
                logging.error(f"Delete template from role {role.id}, please check.")
        db.session.commit()

    @hybrid_method
    def update(self, specs):
        template = ModelWorkloadTemplate.query.filter_by(id=specs[WorkloadSpec.TEMPLATE_ID]).one()
        for key, value in specs.items():
            if key == WorkloadSpec.TEMPLATE_ID:
                continue
            elif key == WorkloadSpec.DISK:
                value = ','.join(str(x) for x in value)
            setattr(template, key, value)
        try:
            db.session.commit()
        except sqlalchemy.exc.IntegrityError as e:
            raise flaskex.Conflict(f"Template with name '{specs[TemplateSpec.TEMPLATE_NAME]}' already exists! Error: {e}")

        return ModelWorkloadTemplateSchema().dump(template)


class ModelWorkloadTemplateSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWorkloadTemplate
        load_instance = True


class ModelWorkloadOSCustomization(db.Model):
    __tablename__ = 'dh_retail_ntx_workload_oscustomization'
    id            = db.Column(db.Integer, primary_key=True)
    name          = db.Column(db.String(255))
    path          = db.Column(db.String(255))


class ModelWorkloadOSCustomizationSchema(ma.Schema):
    class Meta:
        fields = ('id', 'name', 'path')


class ModelWorkloadImage(db.Model):
    __tablename__ = 'dh_retail_ntx_workload_image'
    id            = db.Column(db.Integer, primary_key=True)
    name          = db.Column(db.String(255))
    path          = db.Column(db.String(255))
    md5           = db.Column(db.String(255))
    os_type       = db.Column(db.String(255))
    alias         = db.Column(db.String(255))

    def get_image_by_id(self, image_id):
        try:
            image = ModelWorkloadImage.query.filter_by(id=image_id).one()
            return image  ## image.id, image.name, image.path, image.md5, image.os_type, image.alias
        except sqlalchemy.exc.NoResultFound as e:
            msg = f"Can't find image with id {image_id}!"
            logging.error(msg)
            raise flaskex.InternalServerError(f"{msg}. Error: {e}")

    def update_image_by_id(self, image_id, **kwargs):
        try:
            image = ModelWorkloadImage.query.filter_by(id=image_id).one()
            for k, v in kwargs.items():
                setattr(image, k, v)
            db.session.commit()
        except sqlalchemy.exc.NoResultFound as e:
            db.session.rollback()
            msg = f"Can't find image with id {image_id}!"
            logging.error(msg)
            return {"message": msg, "error_message": str(e)}, 500


class ModelWorkloadImageSchema(ma.Schema):
    class Meta:
        fields = ('id', 'name', 'path', 'md5', 'os_type', 'alias')


class ModelWorkloadTask(db.Model):
    __tablename__      = 'dh_retail_ntx_workload_task'
    id                 = db.Column(db.Integer, primary_key=True)
    use_template       = db.Column(db.Boolean)
    template_id        = db.Column(db.Integer)
    name               = db.Column(db.String(255))
    workload_type      = db.Column(db.String(255))
    cpu                = db.Column(db.Integer)
    cpu_core           = db.Column(db.Integer)
    memory             = db.Column(db.Integer)
    disk               = db.Column(db.String(255))
    boot_mode          = db.Column(db.String(255))
    naming_convention  = db.Column(db.String(255))
    subnet_ip          = db.Column(db.String(50))
    vlan_id            = db.Column(db.Integer)
    image_id           = db.Column(db.Integer)
    image_name         = db.Column(db.String(255))
    oscustomization_id = db.Column(db.Integer)
    pc                 = db.Column(db.String(255))
    pe                 = db.Column(db.String(255))
    detail_log_path    = db.Column(db.String(255))
    status             = db.Column(db.String(255))
    creater            = db.Column(db.String(50))
    create_date        = db.Column(db.String(50))
    description        = db.Column(db.String(255))
    ip                 = db.Column(db.String(255))
    linux_payload      = db.Column(db.String(8000))
    app_package        = db.Column(db.String(255))
    pid                = db.Column(db.Integer)
    update_dns         = db.Column(db.Boolean)
    task_type          = db.Column(db.String(10))

    def __init__(self, **entries):
        # NOTE: Do not call superclass
        #       (which is otherwise a default behaviour).
        # super(User, self).__init__(**entries)

        self.__dict__.update(entries)

    @hybrid_method
    def check_iftask_existence(self, vm_name):
        return ModelWorkloadTask.query.filter(
            ModelWorkloadTask.status.in_([TaskStatus.NOT_STARTED, TaskStatus.IN_PROGRESS, TaskStatus.IN_CLEANUP]),
            ModelWorkloadTask.name == vm_name).first()


class ModelWorkloadTaskSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWorkloadTask
        load_instance = True


class ModelWorkloadTaskLog(db.Model):
    __tablename__ = 'dh_retail_ntx_workload_task_log'
    id = db.Column(db.Integer, primary_key=True)
    logdate = db.Column(db.String(50))
    severity = db.Column(db.String(50))
    loginfo = db.Column(db.String(255))
    task_id = db.Column(db.Integer, db.ForeignKey('dh_retail_ntx_workload_task.id'))


class ModelWorkloadTaskLogSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelWorkloadTaskLog
        load_instance = True


class ModelWorkloadNetwork(db.Model):
    __tablename__ = 'dh_retail_ntx_workload_network'
    id            = db.Column(db.Integer, primary_key=True)
    vlan_id       = db.Column(db.Integer, unique=True)
    vlan_name     = db.Column(db.String(255))
    site_type     = db.Column(db.String(255))

    @hybrid_method
    def create_new_network(self, vlan_id=None, vlan_name=None, site_type=None):
        try:
            new_network = ModelWorkloadNetwork(vlan_id=vlan_id, vlan_name=vlan_name, site_type=site_type)
            db.session.add(new_network)
            db.session.commit()
            return new_network
        except sqlalchemy.exc.IntegrityError:
            raise flaskex.Conflict(f"Network '{vlan_id}' already exists!")


class ModelWorkloadNetworkSchema(ma.Schema):
    class Meta:
        fields = ('id', 'vlan_id', 'vlan_name', 'site_type')

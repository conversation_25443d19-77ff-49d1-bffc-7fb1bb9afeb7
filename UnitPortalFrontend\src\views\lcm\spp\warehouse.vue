<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="5" >
        <el-col :span="2"  :offset="12" style="margin-top: 15px;" >
            <span style="margin: 0 6px 0 6px;float:right">Auto refresh</span>
            <el-checkbox v-model="filter.auto_refresh" style="float:right;margin-left:10%" @change="change_auto_refresh" />
          </el-col>
        <el-col :span="4"  >
          <el-select size="large"
            v-model="filter.selected_pc" multiple collapse-tags placeholder="Filter the PC" style="width:100%;" >
            <el-option v-for="item in filter.pc_list" :key="item" :label="item" :value="item" style="font-size: large;"/>
          </el-select>
        </el-col>
        <el-col :span="4" >
          <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large" @keyup.enter.native="filter_pe_list" />
        </el-col>
        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="filter_pe_list">
            Search
          </el-button>
        </el-col>
      </el-row>

    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="current_list" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column label="ID" prop="id" sortable="custom" align="center" min-width="2%" v-show="false">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="PC" class-name="status-col" min-width="10%" align="center" sortable="custom" prop="prism">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.prism.toUpperCase()}}</span>
        </template>
      </el-table-column>

      <el-table-column label="PE" min-width="6%" align="center" sortable="custom" prop="name">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.fqdn.toUpperCase() |pe_shrinker}}</span>
        </template>
      </el-table-column>

      <el-table-column label="Host" min-width="3%" align="center" sortable="custom" prop="node_number">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.node_number}}</span>
        </template>
      </el-table-column>

      <el-table-column label="Latest log" min-width="16%" align="center"  prop="latest_log">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.latest_log|log_filter }}</span><span class="link-type" v-if="row.latest_log!=null"  @click="show_brief_log(row)">     More</span>
        </template>
      </el-table-column>

      <el-table-column label="Foundation" class-name="status-col" min-width="5%" align="center" sortable="custom" prop="foundation_version" >
        <template slot-scope="{row}">
          <el-tag :type="row.foundation_need_upgrade?'danger':'success'" class="bigger_font">
            {{ row.foundation_version }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="LCM" min-width="4%" align="center" sortable="custom" prop="lcm_version">
        <template slot-scope="{row}">
          <el-tag  :type="row.lcm_need_upgrade?'danger':'success' " class="bigger_font">
            {{ row.lcm_version }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="SPP" min-width="6%" align="center" sortable="custom" prop="spp_version">
        <template slot-scope="{row}">
          <el-tag :type="row.spp_need_upgrade?'danger':'success'" class="bigger_font">
            {{ row.spp_version }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="Status" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="latest_task_status" >
        <template slot-scope="{row}">
          <el-tag :type="row.latest_task_status | task_status_filter" class="bigger_font">
            {{ row.latest_task_status==null ? 'No task yet' : row.latest_task_status}}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="Creater" class-name="status-col" min-width="4%" align="center" sortable="custom" prop="latest_task_creater" >
        <template slot-scope="{row}">
          <span class="bigger_font">
            {{ row.latest_task?row.latest_task_creater:"N/A" }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="Date" prop="latest_task_date"  sortable="custom" align="center" min-width="5%" >
        <template slot-scope="{row}">
          <span>{{ row.latest_task?row.latest_task_date:"N/A" }}</span>
        </template>
      </el-table-column>


      <el-table-column label="Operations"  min-width="4%" align="center" >
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="upgrade_spp(row)">
            Upgrade SPP
          </el-button>
        </template>
      </el-table-column>


    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" />

    <el-dialog :visible.sync="dialogPvVisible" :title="'LCM Log(brief)'" >

      <el-table :data="logdata" border fit highlight-current-row style="width: 100%" max-height="500" >
        <el-table-column prop="key" label="log date"  min-width="25%" >
          <template slot-scope="{row}">
            <span :class="row.severity=='error'?'log_table_error':''">{{ row.log_date }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pv" label="log info" min-width="55%"  >
          <template slot-scope="{row}">
            <span :class="row.severity=='error'?'log_table_error':''">{{ row.log_info }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pv" label="log severity"  min-width="10%"  >
          <template slot-scope="{row}">
            <span :class="row.severity=='error'?'log_table_error':''">{{ row.severity }}</span>
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button type="warning" @click="download_log_file()">Download Detail Log</el-button>
        <el-button type="primary" @click="dialogPvVisible = false">OK</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import {UpgradeSPP, GetPEWithSPPTask, DownloadLCMUpgradeLog, GetSPPTaskStatus} from  '@/api/automation'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import { Checkbox } from 'element-ui';

export default {
  name: 'SPPTable',
  components: { Pagination },
  directives: { waves },
  filters: {
    log_filter(log){
      if(log==null){
        return "No log yet"
      }
      else{
        return log['log_info']
      }
    },
    task_status_filter(status){
      if(status==null){
        return 'primary'
      }else{
        if(status=='Done'){
          return 'success'
        }
        else if(status=='Error'){
          return 'danger'
        }
        else if (status=='In Progress'){
          return 'primary'
        }
      }
    },
    pc_shrinker(fqdn){
      if(fqdn.match(/.*-(.*)-.*/)){
        return   RegExp.$1||fqdn
      }

    },
    pe_shrinker(fqdn){
      return fqdn.replace(".IKEA.COM","")||fqdn
    }
  },
  data() {
    const validateTime =(rule, value, callback)=>{
      if(this.temp.datatimepickerdisabled){
        callback()
      }
      let currentdate = new Date()
      let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
      if (value < utctime){
        callback(new Error('Schedule date must be later then now.'))
      }else{
        let currnettime = utctime.getTime()
        let scheduletime = value.getTime()
        let timediff = scheduletime-currnettime
        if(timediff/1000/60 < 5){
          callback(new Error('Schedule date is too close from now.'))
        }else{
          callback()
        }
      }
      callback()
    }
    return {
      facilityType: 'warehouse',
      tableKey: 0,
      all_pe_list: null,
      filtered_list: null,
      current_list: null,
      page_list: null,
      cached_log:{},
      filter:{
        pc_list:[],
        selected_pc:[],
        fuzzy_string:"",
        auto_refresh:true,
        auto_refresh_interval:""
      },
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: '',
        prism: '',
        status: '',
        sort: '+id'
      },
      statusToShowEditButton:['Not Started'],
      statusToShowAbortButton:['In Progress'],
      statusToShowDeleteButton:['Not Started','Done','Error','Aborted'],
      statusOptions: ['Not Started','In Progress','Done','Error','Aborted'],
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      // statusOptions: ['published', 'draft', 'deleted'],
      ShowCreationDate: false,
      temp: {
        id: '',
        timestamp: new Date(),
        cluster:'',
        prism: '',
        status: '',
        startnow: 1 ,
        datatimepickerdisabled:false,
        description: ''
      },
      selectedrow:'',
      dialogFormVisible: false,
      dialogStatus: '',
      dialogPvVisible: false,
      logdata: [],
      rules: {
        prism: [{ required: true, message: 'prism is required', trigger: 'change' }],
        cluster: [{ required: true, message: 'cluster is required', trigger: 'change' }],
        timestamp: [{ type: 'date', required: true , trigger: 'change' , validator:validateTime}]
      }
    }
  },
  computed: {
    total() {
      if(this.filtered_list){
        return this.filtered_list.length
      }
      else{
          return 0
      }
    }
  },
  created() {
    this.get_task_list()
    this.change_auto_refresh()
  },
  methods: {
    get_task_list() {
      this.listLoading = true
      GetPEWithSPPTask(this.$store.getters.token, this.facilityType).then(response => {
        this.all_pe_list = response.data
        this.filtered_list = this.all_pe_list
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)
        this.listLoading = false
        let all_prism_list = this.all_pe_list.map((obj,index)=>{return obj['prism']})
        this.filter.pc_list = this.remove_duplicate(all_prism_list)
      })
    },
    remove_duplicate(arr) {
      //去除重复值
      const newArr = []
      arr.forEach(item => {
        if (!newArr.includes(item)) {
          newArr.push(item)
        }
      })
      return newArr
    },
    set_page(){
      // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start,end)
    },
    filter_pe_list(){
      //根据过滤条件筛选表格显示内容
      //screen the table as per filters
      this.listQuery.page = 1
      let temp_list
      //filter selected pc first.
      if (this.filter.selected_pc.length){
        //No filter, so select all
        temp_list = this.all_pe_list.filter((item)=>{
          return this.filter.selected_pc.includes(item['prism'].toLowerCase())
        })
        this.filtered_list = temp_list
      }
      else{
        this.filtered_list = this.all_pe_list
      }


      if(this.filter.fuzzy_string.trim().length){
        let temp_list = this.filtered_list
        let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
        //去除空格 并以空格分割成数组
        //remove space, and split into array by space 
        for(let fuzzy of fuzzy_list){
          fuzzy = fuzzy.toString().toLowerCase()
          temp_list = temp_list.filter((k)=>{
            let combined_string = (k.id?k.id.toString().toLowerCase():'') + (k.prism?k.prism.toString().toLowerCase():'')+
                                  (k.fqdn?k.fqdn.toString().toLowerCase():'')+
                                  (k.foundation_version?k.foundation_version.toString().toLowerCase():'')+
                                  (k.lcm_version?k.lcm_version.toString().toLowerCase():'')+
                                  (k.spp_version?k.spp_version.toString().toLowerCase():'')+
                                  (k.latest_task_status?k.latest_task_status.toString().toLowerCase():'')
            if( combined_string.search(fuzzy)!= -1){
              return true
            }
          })
        }

        this.filtered_list = temp_list
      }


      this.set_page()
    },
    handleFilter() {
      this.listQuery.page = 1
    },
    sortChange(data) {
      const { prop, order } = data
      if(order==null){
        this.sortChange({prop:'id',order:'ascending'})
        return 
      }
      let flag_num = order=="ascending" ? 1 : -1
      this.filtered_list.sort((item1,item2)=>{
        let prop1 = item1[prop]?item1[prop]:''
        let prop2 = item2[prop]?item2[prop]:''        
        return (prop1 > prop2) ? flag_num*1 : ((prop1 < prop2) ? flag_num*-1 : 0)
      })
      this.set_page()
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    upgrade_spp(row){
      let payload = {
        token: this.$store.getters.token,
        data : {
          pc : row.prism,
          pe : row.fqdn,
          facility_type: this.facilityType,
        }
      }
      UpgradeSPP(payload).then(response => {
        if(response.data.result){
          this.$notify({
              title: 'Success',
              message: 'Task has been created.',
              type: 'success',
              duration: 10000
          })
        }
        else{
          this.$notify({
              title: 'OOOOOps....',
              message: response.data.message,
              type: 'error',
              duration: 10000
          })
        }
      })      
      .catch((error)=>{
        this.$notify({
              title: 'Error',
              message: error.response.data.message,
              type: 'error',
              duration: 10000
          })
      })
    },
    show_brief_log(row){
      this.selectedrow = row
      let payload = {
        token: this.$store.getters.token,
        data : [this.selectedrow.latest_task_id]
      }
      GetSPPTaskStatus(payload).then(
          response=>{
            if(!Object.keys(response.data).length){
                this.$notify({
                title: 'OOOOOps....',
                message: "Failed to get the brief log.",
                type: 'error',
                duration: 10000
              })
              return 
            }
            if(Object.keys(response.data).includes(this.selectedrow.latest_task_id.toString())){
              this.logdata = response.data[this.selectedrow.latest_task_id]['logs']
            }
            this.dialogPvVisible = true
          }
        ).catch(
          err=>{
            this.$notify({
              title: 'OOOOOps....',
              message: "Geting error when trying to open the brief log page.."+err,
              type: 'error',
              duration: 30000
            })
          }
        )

    },
    download_log_file(){
      let payload = {
        data:{  id:this.selectedrow.latest_task_id,
                filepath:this.selectedrow.latest_task_log_path},
        token: this.$store.getters.token
      }
      DownloadLCMUpgradeLog(payload)
      .then((response)=>{
        const href = URL.createObjectURL(response.data);
        // create "a" HTML element with href to file & click
        const link = document.createElement('a');
        link.href = href;
        link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
        document.body.appendChild(link);
        link.click();
        // clean up "a" element & remove ObjectURL
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      })
    },
    change_auto_refresh(){
      if(this.filter.auto_refresh){
        this.filter.auto_refresh_interval = setInterval(
          this.refresh_spp_log,
          60000
        )
      }
      else{
        clearInterval(this.filter.auto_refresh_interval)
      }
    },
    refresh_spp_log(){
      console.log("refreshing spp")
      let _re = /(done|Error)/i
      //get the tasks that are not ERROR OR DONE
      let in_progress_list = this.all_pe_list.filter((pe)=>{
          if(pe.latest_task_status){
            return !pe.latest_task_status.match(_re)
          }
          else{
            return false
          }
      })
      console.log(in_progress_list)
      if(in_progress_list){
        let _pe = in_progress_list.map(pe=> pe.latest_task_id)
        let payload = {
          token: this.$store.getters.token,
          data : _pe
        }
        GetSPPTaskStatus(payload).then(
          response=>{
            Object.keys(response.data).forEach(

              _key =>{
                // {"86":{
                //  "status":"Error","latest_log":"ooohhh nonono","logs":[{},{}]
                // }}
                  this.all_pe_list.forEach(pe=>{
                  if(pe.latest_task_id==_key){
                    pe.latest_task_status = response.data[_key]['status']
                    pe.latest_log =  response.data[_key]['latest_log']
                    this.cached_log[_key] = response.data[_key]['logs']
                  }
                })
              }
            )

            this.filter_pe_list()
          }
        )
      }
    }
  }
}
</script>
<style lang="scss" scoped>
    .bigger_font {
      font-size: 13px;
    }
    .log_table_error{
      color: red
    }
</style>  


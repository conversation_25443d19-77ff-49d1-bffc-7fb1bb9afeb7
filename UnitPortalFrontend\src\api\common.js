import request from '@/utils/request'
import axios from 'axios'
import { endpoint } from './endpoint'

export function GetSAList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/sa/list`, config)
  return res
}

export function AddPrism(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  console.log(param)
  let res =  request.post(`${endpoint}/ntx/prism`,param.data,config)
  return res
}

export function CreateSA(payload) {
  // var config = {
  //   headers: {'Authorization': 'Bearer ' + param['token']}
  // };
  let res =  request.post(`${endpoint}/sa`, payload)//, config)
  return res
}

export function DeleteSA(saId) {
  let res =  request.delete(`${endpoint}/sa/${saId}`)//, config)
  return res
}

export function GetRoleList(token) {
  var config = {
    headers: {'Authorization': 'Bearer ' + token}
  };
  let res =  request.get(`${endpoint}/role/list`,config)
  return res
}

export function CreateRole(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.post(`${endpoint}/rbac_role`,param.data,config)
  return res
}

export function GetRole(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.get(`${endpoint}/rbac_role/${param['role_id']}`, config)
  return res
}

export function UpdateRole(param) {
  var config = {
    headers: {'Authorization': 'Bearer ' + param['token']}
  };
  let res =  request.put(`${endpoint}/rbac_role`, param.data, config)
  return res
}

export function DeleteRole(param) {

  let payload = {
    headers: {'Authorization': 'Bearer ' + param['token']},
    data   : param.data
  }
  let res =  request.delete(`${endpoint}/rbac_role`, payload)
  return res
}

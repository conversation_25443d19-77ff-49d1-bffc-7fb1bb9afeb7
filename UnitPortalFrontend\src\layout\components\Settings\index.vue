<template>
  <div class="drawer-container">
    <!-- <div> -->
      <h2 class="drawer-title">Feedback to DH UnitPortal</h2>

      <!-- <div class="drawer-item">
        <span>Theme Color</span>
        <theme-picker style="float: right;height: 26px;margin: -3px 8px 0 0;" @change="themeChange" />
      </div>

      <div class="drawer-item">
        <span>Open Tags-View</span>
        <el-switch v-model="tagsView" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>Fixed Header</span>
        <el-switch v-model="fixedHeader" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>Sidebar Logo</span>
        <el-switch v-model="sidebarLogo" class="drawer-switch" />
      </div> -->
      <div :class="currenttextshow? 'drawer-item-showtext' : 'drawer-item'">
        <h3>Overall, is this tool useful?</h3>
        <div style="position: relative; left:15%;padding-top:4px">
          <star-rating @update:rating="handleRatingChange" :rounded-corners="true" :animate="true" :clearable="true" :key="refreshKey" :increment=0.5 :star-size="45" :padding="10"></star-rating>
        </div>
        <div style="margin-top:6px;" v-show="currenttextshow">{{currentRatingText}}</div>
      </div>
      
    <!-- </div> -->
    <div>
      <div :class="currenttextshow? 'drawer-item-showtext' : 'drawer-item'">
          <span style="font-size: 19px;">Any ideas or requests?</span>
          <el-input
            type="textarea"
            :rows="5"
            maxlength="500"
            resize="none"
            show-word-limit
            placeholder="Please input your comments here..."
            v-model="comments">
          </el-input>
        </div>
    </div>
    <div>
      <div class=drawer-item>
        <el-checkbox v-model="anonymous">anonymous</el-checkbox>
        <!-- <el-button type="primary" style="position:absolute; right: 130px;" @click=jumpto()>
          More details
        </el-button> -->
        <el-button type="primary" style="position:absolute; right: 20px;" @click=create_comment()>
          Submit
        </el-button>
      </div>
      <div class=drawer-item>
        <span class="link-type" style="font-size:14px; float: left; margin-left: 6px;"  @click="jumpto('mycomments')"><span class="link-type el-icon-d-arrow-left"/>  My Comments</span>
        <span class="link-type " style="font-size:14px; float: right; margin-right: 6px;padding-top: 4px; "  @click="jumpto('detailcomments')"> More <span class="link-type el-icon-d-arrow-right"/> </span>
        <!-- <el-button type="primary" style="position:absolute; right: 20px;" @click=create_comment()>
          Submit
        </el-button> -->
      </div>
    </div>
  </div>
</template>

<script>
import {GetCommentList, CreatCommentRecord} from '@/api/upwidgets'
import ThemePicker from '@/components/ThemePicker'
// import VueStarRating from 'vue-star-rating'
import StarRating from '../starrate/star-rating.vue'
// require('vue-star-rating')

export default {
  components: {
    ThemePicker,
    StarRating
   },
  data() {
    return {
      comments: '',
      rating: 0,
      anonymous: false,
      currenttextshow: false,
      refreshKey: 0 
      // currentRatingText:''
    }
  },
  computed: {
    currentRatingText: {
      get() {
        if (this.rating) {
          this.currenttextshow = true
          let curtext = eval(this.rating) <3? this.rating +" stars? Anything must be mistaken?" : "Thanks, You are so nice! ";
          return curtext
        }
        }
    },
    fixedHeader: {
      get() {
        return this.$store.state.settings.fixedHeader
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'fixedHeader',
          value: val
        })
      }
    },
    tagsView: {
      get() {
        return this.$store.state.settings.tagsView
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'tagsView',
          value: val
        })
      }
    },
    sidebarLogo: {
      get() {
        return this.$store.state.settings.sidebarLogo
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'sidebarLogo',
          value: val
        })
      }
    }
  },
  watch: {
    addEventClick() {
      window.addEventListener('click', this.closeSidebar)
    },
    show(value) {
      console.log(value)
      console.log(this.clickNotClose)
      if (value && !this.clickNotClose) {
        this.addEventClick()
      }
      if (value) {
        addClass(document.body, 'showRightPanel')
      } else {
        removeClass(document.body, 'showRightPanel')
      }
    }
  },
  methods: {
    addEventClick() {
      window.addEventListener('click', this.closeSidebar)
    },
    jumpto(id) {
      this.$router.push({
      path:'/administration/comment',
        query: {
          id:id
        //Ensure that each click, query is not the same
        //to ensure that refresh the view
        // t: +new Date()
      }
      })
    },
    handleRefresh() {
      // 点击刷新按钮，改变 refreshKey 的值触发组件的重新渲染
      this.refreshKey++
    },
    handleRatingChange(newRating) {
      this.rating = newRating
    },
    themeChange(val) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'theme',
        value: val
      })
    },
    get_commentlist() {
      GetCommentList(this.$store.getters.token).then(response => {
       })
    },
    create_comment() {
      let payload = {
        data: {
          "star": this.rating,
          "comment": this.comments,
          "anonymous": this.anonymous? 1:0
        },
        token: this.$store.getters.token
      }
      CreatCommentRecord(payload).then(() => {
        this.$notify({
              title: 'Success',
              message: 'Successed to create the comment.',
              type: 'success',
              duration: 2000
            })
            this.comments = ''
            this.anonymous = false
            this.currenttextshow = false
            this.handleRefresh()
            if(this.$route.path=='/administration/comment'){
              window.location.reload()}
          })
      .catch((error)=>{
        this.$notify({
              title: 'Error',
              message: 'Failed to create the comment...',
              type: 'error',
              duration: 2000
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.passport-login-tip-container {
    position: fixed;
    font-family: -apple-system, SF UI Text, Arial, PingFang SC, Hiragino Sans GB, Microsoft YaHei, WenQuanYi Micro Hei, sans-serif;
    bottom: 24px;
    right: 4px;
    width: 368px;
    padding: 24px 16px;
    background: #fff;
    color: #555666;
    box-shadow: 0px 0px 10px 2px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    z-index: 9999;
  }
.drawer-container {
  padding: 20px 18px;
  font-size: 14px;
  line-height: 1.5;
  height: 440px;
  word-wrap: break-word;
  // overflow: hidden;
  position: fixed;
    font-family: -apple-system, SF UI Text, Arial, PingFang SC, Hiragino Sans GB, Microsoft YaHei, WenQuanYi Micro Hei, sans-serif;
    bottom: 0;
    right: 0;
    width: 400px;
    // padding: 24px 16px;
    background: #fff;
    color: #555666;
    box-shadow: 0px 0px 10px 2px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    z-index: 9999;

  .drawer-title {
    margin-bottom: 8px;
    color: rgba(0, 0, 0, .85);
    font-size: 28px;
    line-height: 20px;
  }

  .drawer-item {
    color: rgba(0, 0, 0, .65);
    font-size: 14px;
    padding: 4px 0;
    overflow: hidden;
        
  }
  .drawer-item-showtext {
    color: rgba(0, 0, 0, .65);
    font-size: 14px;
    padding: 3px 0;
  }

  .drawer-switch {
    float: right
  }
//   body {
//   font-family: 'Raleway', sans-serif;
// }

  .custom-text {
    font-weight: bold;
    font-size: 1.9em;
    border: 1px solid #cfcfcf;
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 5px;
    color: #999;
    background: #fff;
  }
  
}
</style>

name: Stale Branch Checker

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 1,16 * *'  # Run twice a month (1st and 15th day at midnight)

jobs:
  check-stale-branches:
    runs-on: test_hunter

    steps:
      - name: Checkout repository
        run: |
          find . -mindepth 1 -delete
          
          git clone "https://x-access-token:${{ github.token }}@git.build.ingka.ikea.com/${{ github.repository }}.git" .
          git checkout ${{ github.ref_name }}
          git fetch --all
        shell: /usr/bin/bash -e {0}

      - name: Generate stale_branch_checker.py
        run: |
          cat > stale_branch_checker.py << 'EOF'
          #!/usr/bin/env python3
          import os
          import sys
          import subprocess
          import re
          from datetime import datetime, timedelta
          import requests
          import json
          import smtplib
          from email.mime.text import MIMEText
          from email.mime.multipart import MIMEMultipart

          # Setup basic logging to stdout
          def log(message, level="INFO"):
              print(f"{level}: {message}")

          def run_command(command):
              """Run a shell command and return its output"""
              log(f"Running command: {command}", "DEBUG")
              try:
                  process = subprocess.Popen(
                      command, 
                      stdout=subprocess.PIPE, 
                      stderr=subprocess.PIPE, 
                      shell=True, 
                      universal_newlines=True,
                      encoding='utf-8',  # Explicitly specify UTF-8 encoding
                      errors='replace'   # Replace characters that cannot be decoded
                  )
                  stdout, stderr = process.communicate()
                  if process.returncode != 0:
                      log(f"Error executing command: {command}", "ERROR")
                      log(f"Error: {stderr}", "ERROR")
                      return None
                  return stdout.strip()
              except Exception as e:
                  log(f"Exception executing command: {command}", "ERROR")
                  log(f"Exception: {str(e)}", "ERROR")
                  return None

          def get_all_branches():
              """Get all branches in the repository"""
              output = run_command("git branch -r")
              if not output:
                  return []
              
              branches = []
              for line in output.split('\n'):
                  branch = line.strip()
                  # Skip HEAD reference
                  if "HEAD" not in branch:
                      branches.append(branch)  # Keep the full branch name including 'origin/'
              
              return branches

          def get_branch_name(full_branch):
              """Extract branch name without remote prefix"""
              if '/' in full_branch:
                  return full_branch.split('/', 1)[1]
              return full_branch

          def get_last_commit_date(branch):
              """Get the date of the last commit on a branch"""
              output = run_command(f"git log -1 --format=%cd --date=iso {branch}")
              if not output:
                  return None
              
              try:
                  # Parse the date string
                  date_str = output.strip()
                  # Handle different date formats
                  if ' +' in date_str:
                      date_str = date_str.split(' +')[0]
                  return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
              except Exception as e:
                  log(f"Error parsing date for branch {branch}: {e}", "ERROR")
                  return None

          def get_github_repo_info():
              """Get GitHub repository owner and name from remote URL"""
              remote_url = run_command("git config --get remote.origin.url")
              if not remote_url:
                  log("Could not get remote URL", "ERROR")
                  return None, None
              
              # Handle different URL formats
              # HTTPS: https://github.com/owner/repo.git
              # SSH: **************:owner/repo.git
              if remote_url.startswith("https://"):
                  parts = remote_url.split("/")
                  owner = parts[-2]
                  repo = parts[-1].replace(".git", "")
              elif remote_url.startswith("git@"):
                  parts = remote_url.split(":")
                  owner_repo = parts[1].replace(".git", "")
                  owner, repo = owner_repo.split("/")
              else:
                  log(f"Unsupported remote URL format: {remote_url}", "ERROR")
                  return None, None
              
              return owner, repo

          def get_pull_requests(owner, repo, token):
              """Get all pull requests for a repository using GitHub API"""
              if not token:
                  log("No GitHub token provided. API rate limits may apply.", "WARNING")
              
              headers = {
                  "Accept": "application/vnd.github.v3+json"
              }
              if token:
                  headers["Authorization"] = f"token {token}"
              
              # Use enterprise GitHub API endpoint
              url = f"https://git.build.ingka.ikea.com/api/v3/repos/{owner}/{repo}/pulls?state=all&per_page=100"
              log(f"Fetching pull requests from: {url}", "DEBUG")
              
              all_prs = []
              page = 1
              
              while True:
                  response = requests.get(f"{url}&page={page}", headers=headers)
                  if response.status_code != 200:
                      log(f"Error fetching pull requests: {response.status_code} - {response.text}", "ERROR")
                      break
                  
                  prs = response.json()
                  if not prs:
                      break
                  
                  all_prs.extend(prs)
                  page += 1
                  
                  # Check if we've reached the last page
                  if "Link" not in response.headers or 'rel="next"' not in response.headers["Link"]:
                      break
              
              log(f"Found {len(all_prs)} pull requests", "INFO")
              
              # Create a dictionary mapping branch names to PR info
              pr_dict = {}
              for pr in all_prs:
                  branch_name = pr["head"]["ref"]
                  pr_dict[branch_name] = {
                      "number": pr["number"],
                      "title": pr["title"],
                      "state": pr["state"],
                      "created_at": pr["created_at"],
                      "updated_at": pr["updated_at"],
                      "merged_at": pr["merged_at"],
                      "url": pr["html_url"]
                  }
              
              return pr_dict

          def is_branch_merged(branch_name, target_branch):
              """Check if a branch is merged into the target branch"""
              try:
                  # First ensure we have the latest target branch and source branch
                  run_command(f"git fetch origin {target_branch}:refs/remotes/origin/{target_branch}")
                  run_command(f"git fetch origin {branch_name}:refs/remotes/origin/{branch_name}")
                  
                  # Check if branches exist
                  branch_head = run_command(f"git rev-parse origin/{branch_name} 2>/dev/null")
                  target_head = run_command(f"git rev-parse origin/{target_branch} 2>/dev/null")
                  
                  if not branch_head or not target_head:
                      log(f"Branch {branch_name} or {target_branch} does not exist", "DEBUG")
                      return False
                  
                  # First check if branches have common history
                  # Using git merge-base --fork-point might be more reliable but requires newer Git versions
                  # Here we use git rev-list to check for common commits
                  common_commits = run_command(f"git rev-list --max-count=1 origin/{branch_name} origin/{target_branch}")
                  
                  if not common_commits:
                      log(f"Branches {branch_name} and {target_branch} have no common history", "DEBUG")
                      return False
                  
                  # Use platform-independent way to check if branch is merged
                  # Don't use grep, process the output in Python instead
                  contains_check_cmd = f"git branch -r --contains origin/{branch_name}"
                  contains_output = run_command(contains_check_cmd)
                  
                  if contains_output and f"origin/{target_branch}" in contains_output:
                      log(f"Branch {branch_name} is merged into {target_branch} (contains check)", "DEBUG")
                      return True
                  
                  # If the above method fails, try using merge-base
                  merge_base = run_command(f"git merge-base origin/{branch_name} origin/{target_branch} 2>/dev/null")
                  
                  if not merge_base:
                      log(f"Could not determine if {branch_name} is merged into {target_branch}", "DEBUG")
                      return False
                  
                  # If merge-base equals branch head, the branch is merged into target
                  is_merged = merge_base == branch_head
                  log(f"Branch {branch_name} is{' ' if is_merged else ' not '}merged into {target_branch}", "DEBUG")
                  return is_merged
              except Exception as e:
                  log(f"Error checking if {branch_name} is merged into {target_branch}: {e}", "ERROR")
                  return False

          def get_all_target_branches():
              """Get 'main' and the latest bugfix branch as target branches"""
              all_branches = get_all_branches()
              target_branches = []
              bugfix_branches = []
              
              # Define more flexible bugfix branch pattern, supporting 2-4 segment version numbers
              import re
              # Match bugfix_x, bugfix_x.y, bugfix_x.y.z, bugfix_x.y.z.w formats
              bugfix_pattern = re.compile(r'^bugfix_(\d+)(?:\.(\d+))?(?:\.(\d+))?(?:\.(\d+))?$')
              
              for branch in all_branches:
                  branch_name = get_branch_name(branch)
                  
                  # Check if it's the main branch
                  if branch_name == 'main':
                      # Verify if branch is accessible
                      if run_command(f"git rev-parse --verify origin/{branch_name} 2>/dev/null"):
                          target_branches.append(branch_name)
                          log(f"Added main branch as target", "DEBUG")
                      else:
                          log(f"Main branch exists but cannot be verified, skipping", "WARNING")
                  
                  # Check if it's a bugfix branch
                  match = bugfix_pattern.match(branch_name)
                  if match:
                      # Extract version number parts, default missing parts to 0
                      version_parts = []
                      for part in match.groups():
                          version_parts.append(int(part) if part is not None else 0)
                      
                      # Ensure there are 4 version number parts
                      while len(version_parts) < 4:
                          version_parts.append(0)
                      
                      major, minor, patch, build = version_parts
                      
                      # Verify if branch is accessible
                      if run_command(f"git rev-parse --verify origin/{branch_name} 2>/dev/null"):
                          # Store branch and version info as tuple
                          bugfix_branches.append((branch_name, major, minor, patch, build))
                          log(f"Found bugfix branch: {branch_name} with version {major}.{minor}.{patch}.{build}", "DEBUG")
                      else:
                          log(f"Branch {branch_name} exists but cannot be verified, skipping", "WARNING")
              
              # If bugfix branches were found, sort by version number and select the latest
              if bugfix_branches:
                  # Sort by version number (from high to low)
                  bugfix_branches.sort(key=lambda x: (x[1], x[2], x[3], x[4]), reverse=True)
                  
                  # Only add the first branch after sorting (highest version)
                  highest_branch = bugfix_branches[0][0]
                  target_branches.append(highest_branch)
                  log(f"Added highest bugfix branch as target: {highest_branch}", "DEBUG")
              
              return target_branches

          def get_branch_creator(branch_name):
              """Get the creator's name and email for a branch"""
              try:
                  log(f"Getting creator for branch: {branch_name}", "DEBUG")

                  first_commit_cmd = f"git log --reverse -n 1 --format=%H origin/{branch_name}"
                  first_commit = run_command(first_commit_cmd)
                  
                  if not first_commit:
                      log(f"Could not determine first commit for branch {branch_name}", "WARNING")
                      return None
                  
                  log(f"First commit for branch {branch_name}: {first_commit}", "DEBUG")
                  
                  name_cmd = f"git show -s --format=%an {first_commit}"
                  email_cmd = f"git show -s --format=%ae {first_commit}"
                  
                  name = run_command(name_cmd)
                  email = run_command(email_cmd)
                  
                  if name and email:
                      log(f"Creator for branch {branch_name}: {name} <{email}>", "DEBUG")
                      return {"name": name, "email": email}
                  else:
                      log(f"Could not determine branch creator for {branch_name}", "WARNING")
                      log(f"Name command result: '{name}'", "DEBUG")
                      log(f"Email command result: '{email}'", "DEBUG")
                      return None
              except Exception as e:
                  log(f"Exception getting branch creator for {branch_name}: {str(e)}", "ERROR")
                  import traceback
                  log(f"Traceback: {traceback.format_exc()}", "ERROR")
                  return None

          def get_branch_last_committer(branch_name):
              """Get the last committer's name and email for a branch"""
              try:
                  name_cmd = f"git log -1 --format=%an origin/{branch_name}"
                  email_cmd = f"git log -1 --format=%ae origin/{branch_name}"
                  
                  name = run_command(name_cmd)
                  email = run_command(email_cmd)
                  
                  if name and email:
                      log(f"Last committer for branch {branch_name}: {name} <{email}>", "DEBUG")
                      return {"name": name, "email": email}
                  else:
                      log(f"Could not determine last committer for branch {branch_name}", "WARNING")
                      return None
              except Exception as e:
                  log(f"Exception getting last committer for branch {branch_name}: {str(e)}", "ERROR")
                  return None

          def send_email_notification(recipient, branch_name, days_stale, repo_name, delete_threshold=90):
              """Send email notification to branch owner using IKEA's SMTP server"""
              smtp_server = "smtp-gw.ikea.com"
              smtp_port = 25
              sender = "<EMAIL>"
              
              # Calculate days remaining before deletion
              days_remaining = max(delete_threshold - days_stale, 0)
              
              try:
                  msg = MIMEMultipart()
                  msg['From'] = sender
                  msg['To'] = recipient
                  msg['Subject'] = f"[Action Required] Stale branch notification: {branch_name}"
                  
                  body = f"""
                  <html>
                  <body>
                  <p>Hello,</p>
                  
                  <p>This is an automated notification to inform you that your branch <strong>{branch_name}</strong> 
                  in the <strong>{repo_name}</strong> repository has not been updated in <strong>{days_stale}</strong> days.</p>
                  
                  <p>Please take one of the following actions:</p>
                  <ul>
                      <li>If the branch is still needed: Make a commit to the branch to keep it active</li>
                      <li>If the branch is no longer needed: Delete the branch</li>
                      <li>If the branch has been merged: It can be safely deleted</li>
                  </ul>
                  
                  <p>Stale branches will be automatically deleted after {delete_threshold} days of inactivity. Your branch will be deleted in <strong>{days_remaining}</strong> days if no action is taken.</p>
                  
                  <p>Thank you,<br>
                  DevOps Team</p>
                  </body>
                  </html>
                  """
                  
                  msg.attach(MIMEText(body, 'html'))
                  
                  with smtplib.SMTP(smtp_server, smtp_port) as server:
                      server.send_message(msg)
                  
                  log(f"Email notification sent to {recipient} for branch {branch_name}", "INFO")
                  return True
              except Exception as e:
                  log(f"Failed to send email notification: {str(e)}", "ERROR")
                  return False

          def send_branch_deleted_notification(recipient, branch_name, days_stale, repo_name, merged_to=None):
              """Send notification that branch has been deleted"""
              smtp_server = "smtp-gw.ikea.com"
              smtp_port = 25
              sender = "<EMAIL>"
              
              try:
                  msg = MIMEMultipart()
                  msg['From'] = sender
                  msg['To'] = recipient
                  msg['Subject'] = f"[Notice] Branch deleted: {branch_name}"
                  
                  merged_info = ""
                  if merged_to:
                      merged_info = f" It was merged into: <strong>{', '.join(merged_to)}</strong>."
                  
                  body = f"""
                  <html>
                  <body>
                  <p>Hello,</p>
                  
                  <p>This is an automated notification to inform you that your branch <strong>{branch_name}</strong> 
                  in the <strong>{repo_name}</strong> repository has been deleted as it had not been updated in <strong>{days_stale}</strong> days.{merged_info}</p>
                  
                  <p>Please check the status of any other branches under your name to prevent automatic deletion.</p>
                  
                  <p>Thank you,<br>
                  DevOps Team</p>
                  </body>
                  </html>
                  """
                  
                  msg.attach(MIMEText(body, 'html'))
                  
                  with smtplib.SMTP(smtp_server, smtp_port) as server:
                      server.send_message(msg)
                  
                  log(f"Branch deletion notification sent to {recipient} for branch {branch_name}", "INFO")
                  return True
              except Exception as e:
                  log(f"Failed to send branch deletion notification: {str(e)}", "ERROR")
                  return False

          def delete_branch(branch_name):
              """Delete a branch from the remote repository"""
              try:
                  # First check if the branch exists
                  check_cmd = f"git rev-parse --verify origin/{branch_name} 2>/dev/null"
                  if not run_command(check_cmd):
                      log(f"Branch {branch_name} does not exist or cannot be verified", "WARNING")
                      return False
                  
                  # Delete the branch
                  delete_cmd = f"git push origin --delete {branch_name}"
                  result = run_command(delete_cmd)
                  
                  if result is None:
                      log(f"Failed to delete branch {branch_name}", "ERROR")
                      return False
                  
                  log(f"Successfully deleted branch {branch_name}", "INFO")
                  return True
              except Exception as e:
                  log(f"Exception deleting branch {branch_name}: {str(e)}", "ERROR")
                  return False

          def main():
              # Get environment variables
              token = os.environ.get('GITHUB_TOKEN', '')
              days_threshold = int(os.environ.get('DAYS_THRESHOLD', 60))  # Stale threshold
              delete_threshold = int(os.environ.get('DELETE_THRESHOLD', 90))  # Delete threshold
              auto_delete = os.environ.get('AUTO_DELETE', 'false').lower() == 'true'
              delete_merged_only = os.environ.get('DELETE_MERGED_ONLY', 'true').lower() == 'true'
              
              log(f"Stale threshold: {days_threshold} days", "INFO")
              log(f"Delete threshold: {delete_threshold} days", "INFO")
              log(f"Auto delete enabled: {auto_delete}", "INFO")
              log(f"Delete merged branches only: {delete_merged_only}", "INFO")
              
              # Make sure we're in a git repository
              if not os.path.exists('.git'):
                  log(f"Error: Current directory is not a git repository", "ERROR")
                  sys.exit(1)
              
              # Fetch the latest changes
              log("Fetching the latest changes from remote...", "INFO")
              run_command("git fetch --all")
              
              # Get GitHub repository information
              owner, repo = get_github_repo_info()
              if not owner or not repo:
                  log("Could not determine GitHub repository information", "ERROR")
                  sys.exit(1)
              
              log(f"GitHub repository: {owner}/{repo}", "INFO")
              
              # Get pull requests
              pr_dict = get_pull_requests(owner, repo, token)
              
              # Get all branches
              log("Getting all branches...", "INFO")
              all_branches = get_all_branches()
              log(f"Found {len(all_branches)} branches", "DEBUG")
              
              # Get target branches (auto-detect)
              target_branches = get_all_target_branches()
              log(f"Target branches for merge check: {', '.join(target_branches)}", "INFO")
              
              # Calculate the cutoff dates
              stale_cutoff_date = datetime.now() - timedelta(days=days_threshold)
              delete_cutoff_date = datetime.now() - timedelta(days=delete_threshold)
              log(f"Stale cutoff date: {stale_cutoff_date}", "INFO")
              log(f"Delete cutoff date: {delete_cutoff_date}", "INFO")
              
              # List to store stale branches
              stale_branches = []
              deleted_branches = []
              
              # Check each branch
              for branch in all_branches:
                  branch_name = get_branch_name(branch)
                  log(f"Checking branch: {branch_name}", "DEBUG")
                  
                  # Skip target branches
                  if branch_name in target_branches:
                      log(f"  Skipping target branch: {branch_name}", "DEBUG")
                      continue
                  
                  # Get the last commit date
                  last_commit_date = get_last_commit_date(branch)
                  if not last_commit_date:
                      log(f"  Could not determine last commit date for {branch_name}", "DEBUG")
                      continue
                  
                  log(f"  Last commit date: {last_commit_date}", "DEBUG")
                  
                  # Check if the branch is stale
                  if last_commit_date < stale_cutoff_date:
                      days_since_last_commit = (datetime.now() - last_commit_date).days
                      log(f"  Branch is stale: {days_since_last_commit} days since last commit", "DEBUG")
                      
                      # Check if the branch has a PR
                      pr_info = None
                      if branch_name in pr_dict:
                          pr_info = pr_dict[branch_name]
                          log(f"  Branch has PR: #{pr_info['number']} - {pr_info['title']} ({pr_info['state']})", "DEBUG")
                      
                      # Check if the branch is merged into any target branch
                      merged_to = []
                      for target in target_branches:
                          if is_branch_merged(branch_name, target):
                              merged_to.append(target)
                              log(f"  Branch is merged into {target}", "DEBUG")
                      
                      # Get the branch creator information
                      creator_info = get_branch_creator(branch_name)
                      
                      # Get the last committer information (for reporting only)
                      committer_info = get_branch_last_committer(branch_name)
                      
                      # Initialize email_sent variable
                      email_sent = False
                      
                      # Check if branch should be deleted
                      should_delete = False
                      delete_reason = ""
                      
                      if auto_delete:
                          if last_commit_date < delete_cutoff_date:
                              if delete_merged_only:
                                  if merged_to:
                                      should_delete = True
                                      delete_reason = f"Branch is older than {delete_threshold} days and merged to {', '.join(merged_to)}"
                              else:
                                  should_delete = True
                                  delete_reason = f"Branch is older than {delete_threshold} days"
                      
                      # Delete branch if conditions are met
                      deleted = False
                      if should_delete:
                          log(f"  Deleting branch {branch_name}: {delete_reason}", "INFO")
                          deleted = delete_branch(branch_name)
                          if deleted:
                              deleted_branches.append({
                                  'name': branch_name,
                                  'days_since_last_commit': days_since_last_commit,
                                  'reason': delete_reason
                              })
                              
                              # Send deletion notification instead of stale notification
                              email_sent = False
                              if creator_info and creator_info['email']:
                                  log(f"  Sending deletion notification to {creator_info['email']}", "INFO")
                                  email_sent = send_branch_deleted_notification(
                                      creator_info['email'],
                                      branch_name,
                                      days_since_last_commit,
                                      f"{owner}/{repo}",
                                      merged_to
                                  )
                                  if email_sent:
                                      log(f"  Deletion notification sent successfully", "INFO")
                                  else:
                                      log(f"  Failed to send deletion notification", "WARNING")
                      else:
                          # Only send stale notification if branch is not deleted
                          if creator_info and creator_info['email']:
                              log(f"  Sending email notification to {creator_info['email']}", "INFO")
                              email_sent = send_email_notification(
                                  creator_info['email'],
                                  branch_name,
                                  days_since_last_commit,
                                  f"{owner}/{repo}",
                                  delete_threshold
                              )
                              if email_sent:
                                  log(f"  Email notification sent successfully", "INFO")
                              else:
                                  log(f"  Failed to send email notification", "WARNING")
                          else:
                              log(f"  Could not determine branch creator for notification", "WARNING")
                      
                      # Add to the list
                      stale_branches.append({
                          'name': branch_name,
                          'full_name': branch,
                          'last_commit_date': last_commit_date.strftime('%Y-%m-%d %H:%M:%S'),
                          'days_since_last_commit': days_since_last_commit,
                          'pr_number': pr_info['number'] if pr_info else None,
                          'pr_title': pr_info['title'] if pr_info else None,
                          'pr_state': pr_info['state'] if pr_info else None,
                          'pr_url': pr_info['url'] if pr_info else None,
                          'pr_merged': pr_info['merged_at'] is not None if pr_info else None,
                          'merged_to': merged_to,
                          'creator': creator_info['name'] if creator_info else "Unknown",
                          'creator_email': creator_info['email'] if creator_info else "unknown",
                          'committer': committer_info['name'] if committer_info else "Unknown",
                          'committer_email': committer_info['email'] if committer_info else "unknown",
                          'notification_sent': email_sent,
                          'deleted': deleted,
                          'should_delete': should_delete,
                          'delete_reason': delete_reason if should_delete else ""
                      })
                      
                      log(f"  Added to stale branches list", "DEBUG")
                  else:
                      log(f"  Branch is not stale", "DEBUG")
              
              # Sort by days since last commit (descending)
              stale_branches.sort(key=lambda x: x['days_since_last_commit'], reverse=True)
              
              # Format for output
              output_lines = []
              for branch in stale_branches:
                  merged_to = ", ".join(branch['merged_to']) if branch['merged_to'] else "None"
                  notification_status = "✓" if branch.get('notification_sent', False) else "✗"
                  deleted_status = "✓" if branch.get('deleted', False) else "✗"
                  
                  # Add creator and committer to the output
                  output_lines.append(f"| {branch['name']} | {branch['last_commit_date']} | {branch['days_since_last_commit']} | {merged_to} | {branch['creator']} | {branch['committer']} | {notification_status} | {deleted_status} |")
              
              # Add summary
              summary = f"Found {len(stale_branches)} stale branches that haven't been updated in {days_threshold} days."
              if deleted_branches:
                  summary += f" Deleted {len(deleted_branches)} branches."
              print(f"\n{summary}")
              
              # Write to a file for review
              with open('stale_branches.txt', 'w') as f:
                  f.write(f"Stale Branches (not updated in {days_threshold} days):\n")
                  f.write(f"{'Branch Name':<40} {'Last Commit Date':<20} {'Days Since Last Commit':<25} {'Merged To':<20} {'Notification':<12} {'Deleted':<8}\n")
                  f.write("=" * 130 + "\n")
                  
                  for branch in stale_branches:
                      merged_to = ", ".join(branch['merged_to']) if branch['merged_to'] else "None"
                      notification = "Sent" if branch.get('notification_sent', False) else "Not sent"
                      deleted = "Yes" if branch.get('deleted', False) else "No"
                      creator = f"{branch.get('creator', 'Unknown')} <{branch.get('creator_email', 'unknown')}>"
                      committer = f"{branch.get('committer', 'Unknown')} <{branch.get('committer_email', 'unknown')}>"
                      
                      f.write(
                          f"{branch['name']:<40} {branch['last_commit_date']:<20} {branch['days_since_last_commit']:<25} "
                          f"{merged_to:<20} {notification:<12} {deleted:<8}\n"
                      )
                      f.write(f"  Branch creator: {creator}\n")
                      f.write(f"  Last committer: {committer}\n")
                      if branch.get('should_delete', False):
                          f.write(f"  Delete reason: {branch.get('delete_reason', '')}\n")
                  
                  if deleted_branches:
                      f.write("\n\nDeleted Branches:\n")
                      f.write(f"{'Branch Name':<40} {'Days Since Last Commit':<25} {'Reason':<50}\n")
                      f.write("=" * 115 + "\n")
                      
                      for branch in deleted_branches:
                          f.write(f"{branch['name']:<40} {branch['days_since_last_commit']:<25} {branch['reason']:<50}\n")
              
              # Print markdown table with creator, committer, and deletion status columns
              if stale_branches:
                  print("\n## Stale Branches\n")
                  print("| Branch Name | Last Commit Date | Days Since Last Commit | Merged To | Creator | Committer | Notification | Deleted |")
                  print("|------------|-----------------|----------------------|----------|---------|-----------|-------------|---------|")
                  for line in output_lines:
                      print(line)
              
              # Print deleted branches summary
              if deleted_branches:
                  print("\n## Deleted Branches\n")
                  print("| Branch Name | Days Since Last Commit | Reason |")
                  print("|------------|----------------------|--------|")
                  for branch in deleted_branches:
                      print(f"| {branch['name']} | {branch['days_since_last_commit']} | {branch['reason']} |")
              
              log(f"Total stale branches found: {len(stale_branches)}", "INFO")
              log(f"Total branches deleted: {len(deleted_branches)}", "INFO")
              log(f"Report saved to stale_branches.txt", "INFO")

          if __name__ == "__main__":
              main()
          EOF

      - name: Run stale branch checker
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          DAYS_THRESHOLD: 60  # Stale threshold (90 days)
          DELETE_THRESHOLD: 90  # Delete threshold (120 days)
          AUTO_DELETE: "true"  # Enable auto-delete
          DELETE_MERGED_ONLY: "true"  # Only delete branches that have been merged
        run: |
          python stale_branch_checker.py

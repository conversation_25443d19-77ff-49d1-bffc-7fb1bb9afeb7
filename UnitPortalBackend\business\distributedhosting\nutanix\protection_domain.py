import logging
import time

import werkzeug.exceptions as flaskex

from business.authentication.authentication import ServiceAccount
from business.generic.commonfunc import NutanixAPI


class ProtectionDomain:
    def __init__(self, pe, sa=None, logger=logging):
        self.logger = logger
        _sa = sa if sa else ServiceAccount(usage="nutanix_pm").get_service_account()
        self.rest_pe = NutanixAPI(pe=pe, username=_sa['username'], password=_sa['password'], logger=self.logger)

    def add_vm_to_pd(self, vm_uuid, pd_name):
        self.logger.info(f"Start to add VM [{vm_uuid}] to protection domain [{pd_name}]...")
        # check if vm already exists in pd
        if self.is_vm_in_pd(vm_uuid, pd_name):
            return
        payload = {"uuids": [vm_uuid]}
        res, data = self.rest_pe.call_pe_v2_post(f"/protection_domains/{pd_name}/protect_vms", payload=payload)
        if not res:
            raise flaskex.BadGateway(f"Failed to add VM to protection domain! Reason: {data}")
        # check if vm added to pd successfully
        if not self.is_vm_in_pd(vm_uuid, pd_name):
            raise flaskex.BadGateway(f"Add VM to PD failed, VM doesn't exist in VM! Reason: {data}")

    def remove_vms_from_pd(self, vm_uuid_list, pd_name):
        self.logger.info(f"Start to remove VM {vm_uuid_list} from protection domain [{pd_name}]...")
        payload = {"vmIds": vm_uuid_list}
        res, data = self.rest_pe.call_pe_post(f"/protection_domains/{pd_name}/remove_entities", payload=payload)
        if not res:
            raise flaskex.BadGateway(f"Failed to remove VM from protection domain! Reason: {data}")
        self.logger.info(f"Succeeded to remove VM {vm_uuid_list} from protection domain [{pd_name}].")

    def is_vm_in_pd(self, vm_uuid, pd_name):
        self.logger.info(f"Start to check if VM [{vm_uuid}] exists in protection domain [{pd_name}]...")
        pd_info = self.get_pd(pd_name)
        for vm in pd_info["vms"]:
            if vm.get("vm_id") == vm_uuid:
                self.logger.info(f"VM [{vm_uuid}] exists in protection domain [{pd_name}]!")
                return True
        self.logger.info(f"VM [{vm_uuid}] doesn't exist in protection domain [{pd_name}]!")
        return False

    def get_pd(self, pd_name):
        res, data = self.rest_pe.call_pe_get(f"/protection_domains/{pd_name}", api_version=2)
        if not res:
            raise flaskex.BadGateway(f"Failed to get protection domain! Reason: {data}")
        return data

    def get_pds(self):
        res, data = self.rest_pe.call_pe_get("/protection_domains", api_version=2)
        if not res:
            raise flaskex.BadGateway(f"Failed to get protection domain list! Reason: {data}")
        return data

    def delete_pd(self, pd_name):
        self.logger.info(f"Deleting pd {pd_name}")
        res, data = self.rest_pe.call_pe_delete("/protection_domains", api_version=1)
        if not res:
            raise flaskex.BadGateway(f"Failed to delete pd {pd_name}! Reason: {data}")
        return data

    def is_pd_existing(self, name):
        self.logger.info(f"Start to check if protection domain [{name}] exists...")
        res, data = self.rest_pe.call_pe_get(f"/protection_domains/{name}", api_version=2)
        if not res:
            if isinstance(data, str):
                return False
            if "does not exist" in data["message"]:
                self.logger.info(f"Protection domain [{name}] doesn't exist!")
                return False
            self.logger.warning("Failed to get protection domain info, assume it doesn't exist...")
            return False
        self.logger.info(f"Protection domain [{name}] exists!")
        return True

    def create_pd(self, name):
        self.logger.info(f"Start to create protection domain [{name}]...")
        _res, data = self.rest_pe.call_pe_v2_post("/protection_domains", payload={"value": name})
        if not self.is_pd_existing(name):
            raise flaskex.InternalServerError(f"Create pd failed! Reason: {data['message']}")
        self.logger.info("Create protection finished.")

    def activate_pd(self, name):
        pass

    def add_schedules_to_pd(self, name):
        self.logger.info(f"Start to add schedules to protection domain [{name}]...")
        user_start_time_in_usecs = int(time.time() * 1000000)      # microseconds
        schedules = [
            {
                "type": "DAILY",
                "every_nth": 1,
                "retention_policy": {
                    "local_max_snapshots": 35,
                    "remote_max_snapshots": {}      # TODO: temporarily empty here
                }
            },
            {
                "type": "HOURLY",
                "every_nth": 2,
                "retention_policy": {
                    "local_max_snapshots": 24,
                    "remote_max_snapshots": {}
                }
            }
        ]
        schedules_to_add = []
        # Check if schedules are already existing in the PD, since nutanix will directly add a duplicate schedule
        for c in schedules:
            if not self.is_schedule_existing(c, name):
                c["user_start_time_in_usecs"] = user_start_time_in_usecs
                schedules_to_add.append(c)
        if not schedules_to_add:        # Send request with body '[]' will return an error
            self.logger.info("Schedules are already existing in pd, won't create.")
            return
        res, data = self.rest_pe.call_pe_v2_post(f"/protection_domains/{name}/schedules/add_list", payload=schedules_to_add)
        if not res:
            raise flaskex.BadGateway(f"Failed to add schedule for pd {name}! Reason: {data['message']}")

    def get_pd_schedules(self, name):
        self.logger.info(f"Start to get existing schedules of protection domain [{name}]...")
        res, data = self.rest_pe.call_pe_get(f"/protection_domains/{name}/schedules", api_version=2)
        if not res:
            raise flaskex.BadGateway(f"Failed to retrieve all snapshot schedules from pd {name}! Reason: {data}")
        self.logger.info(f"Existing schedules of protection domain [{name}]: {data}")
        return data

    def add_pd_schedule(
        self,
        pd_name,
        interval_type,
        interval,
        local_max_snaps,
        remote_max_snaps,
        remote_site_name,
        user_start_time_in_usecs
    ):
        self.logger.info(f"Start to create schedule for protection domain [{pd_name}]...")
        payload = [
            {
                "type": interval_type,
                "every_nth": interval,
                "retention_policy": {
                    "local_max_snapshots": local_max_snaps,
                    "remote_max_snapshots": {
                        remote_site_name: remote_max_snaps
                    } if remote_max_snaps > 0 and remote_site_name else {}
                },
                "user_start_time_in_usecs": user_start_time_in_usecs
            }]
        res, data = self.rest_pe.call_pe_v2_post(f"/protection_domains/{pd_name}/schedules/add_list", payload=payload)
        if not res:
            raise flaskex.BadGateway(f"Failed to add schedule! Reason: {data}")
        return data

    def delete_pd_schedule(self, pd_name, schedule_uuid):
        self.logger.info(f"Start to delete schedule {schedule_uuid} from protection domain [{pd_name}]...")
        res, data = self.rest_pe.call_pe_delete(f"/protection_domains/{pd_name}/schedules/{schedule_uuid}", api_version=1)
        if not res:
            raise flaskex.BadGateway(
                f"Failed to delete schedule! Reason: {data['message']}")
        return data

    def delete_all_existing_schedules(self, pd_name):
        schedules_to_delete = self.get_pd_schedules(pd_name)
        for s in schedules_to_delete:
            self.delete_pd_schedule(pd_name, s["id"])

    def is_schedule_existing(self, schedule, name):
        self.logger.info(f"Start to check if schedule exists in protection domain [{name}]...")
        existing_schedules = self.get_pd_schedules(name)

        def _is_schedule_existing(s, e_s):
            # Recursively compare key and value. If value is a dict, recurse
            for k, v in s.items():
                if v == e_s.get(k):
                    continue
                elif isinstance(e_s.get(k), dict) and _is_schedule_existing(v, e_s.get(k)):
                    continue
                else:
                    return False
            return True

        # for _ in existing_schedules:
        #     if _is_schedule_existing(schedule, _):
        #         return True
        # return False
        return any([_is_schedule_existing(schedule, _) for _ in existing_schedules])

    def get_pd_snapshots(self, pd_name):
        res, data = self.rest_pe.call_pe_get(f"/protection_domains/{pd_name}/dr_snapshots?fullDetails=true&projection=stats%2Calerts&filterCriteria=state!%3DEXPIRED")
        if not res:
            raise flaskex.BadGateway(f"Failed to get snapshots in pd {pd_name}! Reason: {data}")
        return data

    def remove_pd_single_snapshot(self, pd_name, snap_id):
        self.logger.info(f"Deleting snapshot in PD '{pd_name}' with ID '{snap_id}'")
        res, data = self.rest_pe.call_pe_delete(
            f"/protection_domains/{pd_name}/dr_snapshots/{snap_id}", api_version=1)
        if not res:
            raise flaskex.BadGateway(f"Failed to delete snapshot {snap_id} in pd {pd_name}! Reason: {data}")
        return data
    
    def get_unprotected_vms_in_pd(self):
        res, data = self.rest_pe.call_pe_get("/protection_domains/unprotected_vms", api_version=2)
        if not res:
            raise flaskex.BadGateway(f"Failed to get unprotection domain list! Reason: {data}")
        return data


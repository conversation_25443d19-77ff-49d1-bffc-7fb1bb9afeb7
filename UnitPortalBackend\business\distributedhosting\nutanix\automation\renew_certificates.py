import os
import logging
import datetime
import re

# local file
from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.generic.base_up_exception import VaultGetSecretFailed
import static.SETTINGS as SETTING
from business.authentication.authentication import ServiceAccount, Vault
from business.distributedhosting.nutanix.nutanix import PrismCentral, PrismElement
from business.generic.commonfunc import Redfish
from models.ntx_models import ModelPrismCentral, ModelPrismElement
from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
from models.atm_models import ModelNtxAutomationRenewCertificateTask, \
    ModelNtxAutomationRenewCertificateTaskSchema, ModelNtxAutomationRenewCertificateTaskLog, \
    ModelNtxAutomationRenewCertificateTaskLogSchema
from business.generic.sslrequest import SslRequest
from business.generic.keyfactor import KeyFactorAPI
import werkzeug.exceptions as flaskex
from business.benchmark.benchmark import Benchmark


class RenewCertificates(BaseUpTask):
    LOG_DIR = SETTING.RENEW_CERT_LOG_PATH
    LOG_TYPE = "RENEW_CERT"
    TASK_TYPE = "RENEW_CERT"
    """
    A class to handle the renewal of certificates for Nutanix Prism Elements (PE) and Prism Central (PC).
    Attributes:
        pe (str): The fully qualified domain name (FQDN) of the Prism Element.
        facility_type (str): The type of facility (e.g., RETAIL, WAREHOUSE).
        model_pe (Model): The model class for the Prism Element.
        model_pc (Model): The model class for the Prism Central.
        pe_benchmark (dict): Benchmark information for the Prism Element.
        pc (str): The fully qualified domain name (FQDN) of the Prism Central.
        is_central_pe (bool): Indicates if the Prism Element is a central PE.
        domain (str): The DNS zone of the system.
        pc_benchmark (dict): Benchmark information for the Prism Central.
        vault (Vault): Vault instance for accessing secrets.
        pe_svc_account (dict): Service account credentials for the Prism Element.
        oob_svc_account (dict): Service account credentials for the Out-Of-Band (OOB) management.
        pc_svc_account (dict): Service account credentials for the Prism Central.
        kf_sa (dict): Service account credentials for KeyFactor.
        logger (Logger): Logger instance for logging.
        cert_tmp_path (str): Temporary path for storing certificate files.
        task (Task): Current task instance.
        is_task (bool): Indicates if a task is currently running.
    Methods:
        init_svc_account(cluster_info):
            Initializes service accounts for PE, OOB, and PC.
        init_tmp_folder():
            Initializes a temporary folder for storing certificate files.
        init_task():
            Initializes a task for renewing certificates.
        run(param):
            Runs the certificate renewal process.
        setup_loggers():
            Sets up loggers for the renewal process.
        _write_log(log_info, log_severity=None):
            Writes log information with specified severity.
        start_task():
            Starts the renewal task in a subprocess.
        renew_certificate():
            Renews certificates for PE, PC, and OOB.
        get_renew_plan(pc, pe):
            Generates a plan for renewing certificates.
        need_to_renew(source='px', exp_date=None, max_days=None):
            Checks if a certificate needs to be renewed based on its expiry date.
        check_pe_cert(organization_name=None, oob_issued_by=None):
            Checks if the PE certificate needs to be renewed based on the organization name.
        renew_pe_cert(pe):
            Renews the certificate for the Prism Element.
        renew_oob_cert(oob_name=None, oob_ip=None):
            Renews the certificate for the Out-Of-Band (OOB) management.
        renew_pc_cert(pc):
            Renews the certificate for the Prism Central.
    """
    def __init__(self, pe, facility_type):
        self.pe = pe
        self.facility_type = facility_type.lower()
        if self.facility_type == FacilityType.RETAIL:
            model_pe = ModelPrismElement
            model_pc = ModelPrismCentral
        if self.facility_type == FacilityType.WAREHOUSE:
            model_pe = ModelWarehousePrismElement
            model_pc = ModelWarehousePrismCentral
        pe_cluster_info = model_pe.query.filter_by(fqdn=self.pe).one()
        self.pe_benchmark = Benchmark().get_bmk_by_id(bmk_id=pe_cluster_info.bmk_id, real_fw_rules=True)
        if not self.pe_benchmark:
            raise flaskex.InternalServerError(f"Cannot find benchmark for {self.pe}!")
        self.pc = f"{self.pe_benchmark['systems']['self_service_name']}.{self.pe_benchmark['systems']['dns_zone']}"
        self.is_central_pe = pe_cluster_info.is_central_pe
        self.domain = self.pe_benchmark['systems']['dns_zone']
        if self.is_central_pe:
            pc_cluster_info = model_pc.query.filter_by(fqdn=self.pc).one()
            self.pc_benchmark = Benchmark().get_bmk_by_id(bmk_id=pc_cluster_info.bmk_id, real_fw_rules=True)
            if not self.pc_benchmark:
                raise flaskex.InternalServerError(f"Cannot find benchmark for {self.pc}!")
        self.vault = Vault(tier=self.pe_benchmark['tier'], \
                           sa=ServiceAccount(usage=self.pe_benchmark['vault']['service_account']).get_service_account(), \
                           engine=self.pe_benchmark['vault']['engine'], \
                           namespace=f"{self.pe_benchmark['vault']['master_namespace']}/{self.pe_benchmark['vault']['tier_namespace']}", \
                           url=self.pe_benchmark['endpoint']['endpoints']['vault']['endpoint'])
        self.pe_svc_account, self.oob_svc_account, self.pc_svc_account = self.init_svc_account(pe_cluster_info)
        self.kf_sa = ServiceAccount(usage=self.pe_benchmark['certificate']['service_account']).get_service_account()
        self.cert_tmp_path = self.init_tmp_folder()
        self.is_task = False
        super().__init__(
            ModelNtxAutomationRenewCertificateTask, ModelNtxAutomationRenewCertificateTaskSchema,
            ModelNtxAutomationRenewCertificateTaskLog, ModelNtxAutomationRenewCertificateTaskLogSchema
        )
        self.task_identifier = self.pe
        self.task_duplicated_kwargs = {"pe": self.pe}
        self.task_info = {"pe": self.pe}
    
    def init_svc_account(self, cluster_info):
        pe_svc_label = f"{cluster_info.name}/{self.pe_benchmark['vault']['site_labels']['site_pe_svc']}"
        oob_svc_label = f"{cluster_info.name}/{self.pe_benchmark['vault']['site_labels']['site_oob']}"
        pc_svc_label = f"{cluster_info.name}/{self.pe_benchmark['vault']['central_labels']['site_pc_svc']}"
        # getting pe svc account, 1-click-nutanix
        logging.info("Getting access credential of PE")
        res, data = self.vault.get_secret(pe_svc_label)
        if not res:
            raise VaultGetSecretFailed(pe_svc_label)
        pe_svc_account = {"username": data["username"], "password": data["secret"]}
        # getting oob svc account, admin
        logging.info("Getting access credential of OOB")
        res, data = self.vault.get_secret(oob_svc_label)
        if not res:
            raise VaultGetSecretFailed(oob_svc_label)
        oob_svc_account = {"username": data["username"], "password": data["secret"]}
        # getting pc svc account, when the pe is a central pe, 1-click-nutanix
        pc_svc_account = None
        if self.is_central_pe:
            logging.info("Getting access credential of PC")
            res, data = self.vault.get_secret(pc_svc_label)
            if not res:
                raise VaultGetSecretFailed(pc_svc_label)
            pc_svc_account = {"username": data["username"], "password": data["secret"]}
        return pe_svc_account, oob_svc_account, pc_svc_account

    def init_tmp_folder(self):
        cert_tmp_path = os.path.join(SETTING.CERT_TMP_PATH, f'{self.pe}_{datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")}')
        if not os.path.exists(cert_tmp_path):
            os.mkdir(cert_tmp_path)
        return cert_tmp_path

    def _write_log(self, log_info, log_severity=None):
        if log_severity not in ["info", "warning", "error", "title"]:
            log_severity = "info"
        if log_severity == "info":
            self.logger.info(log_info)
        if log_severity == "warning":
            self.logger.warning(log_info)
        if log_severity == "error":
            self.logger.error(log_info)
        if log_severity == "title":
            self.logger.title(log_info)
        if self.is_task:
            self.db_logger.write_renew_cert_log(loginfo=log_info, logseverity=log_severity)

    def task_process(self):
        self.renew_certificate()

    def renew_certificate(self):
        _pc = PrismCentral(pc=self.pc, sa=self.pc_svc_account, logger=self.logger) if self.is_central_pe else None
        _pe = PrismElement(pe=self.pe, sa=self.pe_svc_account, logger=self.logger)
        self._write_log(log_info="Generating the plan for certificate renewal", log_severity="title")
        plan = self.get_renew_plan(pc=_pc, pe=_pe)
        for p in plan:
            if p['object'] == "pe":
                self.renew_pe_cert(pe=_pe)
            if p['object'] == "pc":
                self.renew_pc_cert(pc=_pc)
            if p['object'] == "oob":
                self.renew_oob_cert(oob_name=p['oob_name'], oob_ip=p['oob_ip'])
    
    def get_renew_plan(self, pc: PrismCentral, pe: PrismElement):
        plan = list()
        # Validate the PE certificate expiry date
        self._write_log(log_info=f"Getting certificate expiry date of {self.pe}")
        res, data = pe.get_ssl_cert()
        if not res:
            raise flaskex.InternalServerError(f"Failed to get the certificate expiry date of {self.pe}, quit! Reason: {data}")
        if self.need_to_renew(source="px", exp_date=data['expiryDate'], max_days=self.pe_benchmark['certificate']['max_renewal_days_before_expiry']) or self.check_pe_cert(organization_name = data['organizationName']):
            self._write_log(log_info=f"We will renew certificate for {self.pe}")
            plan.append({'object': "pe"})
        else:
            self._write_log(log_info=f"We do not need to renew certificate for {self.pe}")
        # Validate the PC certificate expiry date
        if self.is_central_pe:
            self._write_log(log_info=f"{self.pe} is a central PE, we need to check the certificate expiry date of {self.pc}")
            self._write_log(log_info=f"Getting certificate expiry date of {self.pc}")
            res, data = pc.get_ssl_cert()
            if not res:
                raise flaskex.InternalServerError(f"Failed to get the certificate expiry date of {self.pc}, quit! Reason: {data}")
            if self.need_to_renew(source="px", exp_date=data['expiryDate'], max_days=self.pc_benchmark['certificate']['max_renewal_days_before_expiry']) or self.check_pe_cert(organization_name = data['organizationName']):
                self._write_log(log_info=f"We will renew the certificate for {self.pc}")
                plan.append({'object': "pc"})
            else:
                self._write_log(log_info=f"We do not need to renew certificate for {self.pc}")
        else:
            self._write_log(log_info=f"{self.pe} is NOT a central PE, we skip to check the certificate expiry date of {self.pc}")
        # Validate the Oob certificate expiry date
        self._write_log(log_info=f"Getting the host list of {self.pe}")
        res, host_list = pe.get_host_list()
        if not res:
            raise flaskex.InternalServerError(f"Failed to get the host list, quit! Reason: {host_list}")
        for host in host_list:
            self._write_log(log_info=f"Validate the certificate for {host['name']} OOB")
            _rf = Redfish(ip=host['oob_ip'], username=self.oob_svc_account['username'], password=self.oob_svc_account["password"], timeout=60)
            res, data = _rf.get_cert_info()
            if not res:
                self._write_log(log_info=f"Failed to request for certificate information of {host['name']} OOB, skip! Reason: {data}", log_severity="error")
                continue
            if self.need_to_renew(source="oob", exp_date=data.get('valid_until'), max_days=self.pe_benchmark['certificate']['max_renewal_days_before_expiry']) or self.check_pe_cert(oob_issued_by=data['issued_by']):
                self._write_log(log_info=f"We will renew the certificate for {host['name']} OOB")
                plan.append({'object': "oob", 'oob_name': f"{host['name']}OOB", 'oob_ip': host['oob_ip']})
        return plan

    def need_to_renew(self, source='px', exp_date=None, max_days=None):
        self._write_log(log_info="Check if need to renew the certificate", log_severity="title")
        month_int = {
            'Jan': "01",
            'Feb': "02",
            'Mar': "03",
            'Apr': "04",
            'May': "05",
            'Jun': "06",
            'Jul': "07",
            'Aug': "08",
            'Sep': "09",
            'Oct': "10",
            'Nov': "11",
            'Dec': "12"
        }
        def _convert_cert_date_px(date_str):
            #Converts date from 'Mon Feb 17 17:02:08 PST 2025' to date object in '20250217' format
            #date_str = "Mon Feb 17 17:02:08 PST 2025"
            date_str_array = date_str.split(" ")
            #date_str_arry = ['Mon', 'Feb', '17', '17:02:08', 'PST', '2025']
            return datetime.datetime.strptime(f"{date_str_array[5]}{month_int[date_str_array[1]]}{date_str_array[2]}", '%Y%m%d')
        def _convert_cert_date_oob(date_str):
            #Converts date from 'May 28 06:49:36 2025 GMT' to date object in '20250217' format
            #date_str = "May 28 06:49:36 2025 GMT"
            date_str_array = re.split(r'\s+', date_str)
            #date_str_arry = ['May', '28', '06:49:36', '2025', 'GMT']
            return datetime.datetime.strptime(f"{date_str_array[3]}{month_int[date_str_array[0]]}{date_str_array[1]}", '%Y%m%d')
        exp_date = _convert_cert_date_px(date_str=exp_date) if source == 'px' else _convert_cert_date_oob(date_str=exp_date)
        self._write_log(log_info=f"Converted expiry date is {exp_date}")
        if (exp_date - datetime.datetime.now()).days <= int(max_days):
            self._write_log(log_info=f"Less than {max_days} days we have, need to renew")
            return True
        self._write_log(log_info=f"More than {max_days} days we have, do NOT need to renew")
        return False

    def check_pe_cert(self, organization_name=None, oob_issued_by=None):
        if organization_name and "IKEA" not in organization_name:
            self._write_log(log_info=f"Checking the certificate for {self.pe}", log_severity="title")
            return True
        if oob_issued_by and "IKEA" not in oob_issued_by:
            return True
        return False

    def renew_pe_cert(self, pe):
        self._write_log(log_info=f"Start to renew certificate for {self.pe}", log_severity="title")
        self._write_log(log_info=f"Preparing the SANs payload for {self.pe}")
        kwargs = {
            'org': "IKEA",
            'ou': "Infrastructure Engineering",
            'country': "SE",
            'cn': f"{self.pe}",
            'dns_names': [],
            'ip_addrs': []
        }
        self._write_log(log_info=f"Getting the cluster profile of {self.pe}")
        pe_detail = pe.get_cluster_detail()
        if not pe_detail:
            raise flaskex.InternalServerError(f"Failed to get the {self.pe} profile, quit!")
        self._write_log(log_info=f"Getting the VMs list of {self.pe}")
        res, vm_list = pe.get_vm_list(details=True)
        if not res:
            raise flaskex.InternalServerError(f"Failed to get the VMs list from {self.pe}, quit! Reason: {vm_list}")
        self._write_log(log_info=f"Getting the host list of {self.pe}")
        res, host_list = pe.get_host_list()
        if not res:
            raise flaskex.InternalServerError(f"Failed to get the host list, quit! Reason: {host_list}")
        self._write_log(log_info=f"Adding names and IPs for {self.pe}")
        kwargs['dns_names'].append(pe_detail['name']) #RETSE995-NXC000
        kwargs['dns_names'].append(self.pe) #RETSE995-NXC000.ikea.com
        kwargs['dns_names'].append(pe_detail['name'].replace("NXC", "NXD")) #RETSE995-NXD000
        kwargs['dns_names'].append(self.pe.upper().replace("NXC", "NXD")) #RETSE995-NXD000.ikea.com
        kwargs['ip_addrs'].append(pe_detail['cluster_external_ipaddress'])
        kwargs['ip_addrs'].append(pe_detail['cluster_external_data_services_ipaddress'])
        self._write_log(log_info=f"Adding names for CVMs of {self.pe}")
        for vm in vm_list:
            if vm['is_controller_vm']:
                cvm_machine_name = vm['name'] #NTNX-RETSE995-NX7001-CVM
                cvm_machine_fqdn = f"{cvm_machine_name}.{self.pe_benchmark['systems']['dns_zone']}" #NTNX-RETSE995-NX7001-CVM.ikea.com
                cvm_host_name = vm['name'].replace("NTNX-", "").replace("-CVM", "CVM") #RETSE995-NX7001CVM
                cvm_host_fqdn = f"{cvm_host_name}.{self.pe_benchmark['systems']['dns_zone']}" #RETSE995-NX7001CVM.ikea.com
                kwargs['dns_names'].append(cvm_machine_name)
                kwargs['dns_names'].append(cvm_machine_fqdn)
                kwargs['dns_names'].append(cvm_host_name)
                kwargs['dns_names'].append(cvm_host_fqdn)
        self._write_log(log_info=f"Adding IPs for CVMs of {self.pe}")
        for host in host_list:
            kwargs['ip_addrs'].append(host['cvm_ip'])
        self._write_log(log_info=f"SANs payload for {self.pe} is {kwargs}")
        self._write_log(log_info=f"Writing the private key file to {self.cert_tmp_path}\\{pe_detail['name']}.pem")
        prv_key = SslRequest.generate_private_key(prv_file=os.path.join(self.cert_tmp_path, f"{pe_detail['name']}.pem"))
        if not prv_key:
            raise flaskex.InternalServerError(f"Failed to generate the private key for {self.pe}, quit!")
        self._write_log(log_info=f"Generating the CSR file to {self.cert_tmp_path}\\{pe_detail['name']}.csr")
        csr_rsp = SslRequest.generate_csr(prv_key=prv_key, csr_file=os.path.join(self.cert_tmp_path, f"{pe_detail['name']}.csr"), **kwargs)
        if not csr_rsp:
            raise flaskex.InternalServerError(f"Failed to generate CSR for {pe_detail['name']}, quit!")
        csr_str = ""
        with open(os.path.join(self.cert_tmp_path, f"{pe_detail['name']}.csr"), "r") as csr_file:
            csr_str = csr_file.read().replace("\n", "")
        self._write_log(log_info=f"Calling KeyFactorAPI to request certificate for {self.pe}")
        _kf = KeyFactorAPI(fqdn=self.pe_benchmark['endpoint']['endpoints']['pki']['endpoint'], username=self.kf_sa['username'], password=self.kf_sa['password'])
        res, data = _kf.enroll_certificate_by_csr(csr_str=csr_str, template=self.pe_benchmark['certificate']['template'], \
                                                  cert_authority=self.pe_benchmark['certificate']['authority'], \
                                                  cert_ownergroup=self.pe_benchmark['certificate']['cert_owner_group'], \
                                                  email_contact=self.pe_benchmark['certificate']['email_contact'], \
                                                  include_chain=False)
        if not res:
            raise flaskex.InternalServerError(f"Failed to generate the certificate for {self.pe}, quit! Reason: {data}")
        self._write_log(log_info=f"Writing the certificate file to {self.cert_tmp_path}\\{pe_detail['name']}.cer")
        with open(os.path.join(self.cert_tmp_path, f"{pe_detail['name']}.cer"), "w") as cer_file:
            cer_file.write(data)
        self._write_log(log_info=f"Writing the chain file to {self.cert_tmp_path}\\chain")
        with open(os.path.join(self.cert_tmp_path, "chain"), "w") as chain_file:
            chain_file.write(self.pe_benchmark['certificate']['chain'])
        self._write_log(log_info=f"Uploading the certificate to {self.pe}")
        res = pe.upload_ssl_cert(keyfile=os.path.join(self.cert_tmp_path, f"{pe_detail['name']}.pem"), certfile=os.path.join(self.cert_tmp_path, f"{pe_detail['name']}.cer"), chainfile=os.path.join(self.cert_tmp_path, "chain"))
        if res:
            self._write_log(log_info=f"Successfully update the certificate for PE {self.pe}")
            return
        raise flaskex.InternalServerError(f"Failed to update the certificate for PE {self.pe}")

    def renew_oob_cert(self, oob_name=None, oob_ip=None):
        self._write_log(log_info=f"Start to renew certificate for {oob_name}", log_severity="title")
        kwargs = {
            'city': "EMEA",
            'cn': f"{oob_name}.{self.pe_benchmark['systems']['dns_zone']}",
            'org': "IKEA",
            'state': "Sockerbruket",
            'ou': "Digital Technology Engineer",
            'country': "SE"
        }
        self._write_log(log_info=f"SANs payload for {oob_name} is {kwargs}")
        self._write_log(log_info=f"Generating CSR file for {oob_name}")
        _rf = Redfish(ip=oob_ip, username=self.oob_svc_account['username'], password=self.oob_svc_account["password"], timeout=60)
        res, data = _rf.generate_ilo_csr(**kwargs)
        if not res:
            self._write_log(log_info=f"Failed to request for generate CSR for {oob_name}, skip! Reason: {data}", log_severity="error")
            return
        self._write_log(log_info=f"Writing the CSR file to {self.cert_tmp_path}\\{oob_name}.csr")
        res, data = _rf.download_ilo_csr(csr_file=os.path.join(self.cert_tmp_path, f"{oob_name}.csr"))
        if not res:
            self._write_log(log_info=f"Failed to write CSR file for {oob_name}, we skip it! Reason: {data}", log_severity="error")
            return
        csr_str = ""
        with open(os.path.join(self.cert_tmp_path, f"{oob_name}.csr"), "r") as csr_file:
            csr_str = csr_file.read().replace("\n", "")
        self._write_log(log_info=f"Calling KeyFactorAPI to request certificate for {oob_name}")
        _kf = KeyFactorAPI(fqdn=self.pe_benchmark['endpoint']['endpoints']['pki']['endpoint'], username=self.kf_sa['username'], password=self.kf_sa['password'])
        res, data = _kf.enroll_certificate_by_csr(csr_str=csr_str, template=self.pe_benchmark['certificate']['template'], \
                                                  cert_authority=self.pe_benchmark['certificate']['authority'], \
                                                  cert_ownergroup=self.pe_benchmark['certificate']['cert_owner_group'], \
                                                  email_contact=self.pe_benchmark['certificate']['email_contact'], \
                                                  include_chain=False)
        if not res:
            self._write_log(log_info=f"Failed to generate certificate for {oob_name}, we skip it! Reason: {data}", log_severity="error")
            return
        self._write_log(log_info=f"Writing certificate for {oob_name} to {self.cert_tmp_path}\\{oob_name}.cer")
        with open(os.path.join(self.cert_tmp_path, f"{oob_name}.cer"), "w") as cer_file:
            cer_file.write(data.replace("\r\n", "\n"))
        self._write_log(log_info=f"Upload certificate to {oob_name}")
        res, data = _rf.install_ilo_cert(certfile=os.path.join(self.cert_tmp_path, f"{oob_name}.cer"))
        if res:
            self._write_log(log_info=f"Succefully update the certificate for {oob_name}")
            return
        self._write_log(log_info=f"Failed to update the certificate for {oob_name}, we skip it! Reason: {data}", log_severity="error")

    def renew_pc_cert(self, pc: PrismCentral):
        self._write_log(log_info=f"Start to renew certificate for {self.pc}", log_severity="title")
        self._write_log(log_info=f"Preparing the SANs payload for {self.pc}")
        kwargs = {
            'org': "IKEA",
            'ou': "Infrastructure Engineering",
            'country': "SE",
            'cn': f"{self.pc}",
            'dns_names': [],
            'ip_addrs': []
        }
        self._write_log(log_info=f"Getting PC profile of {self.pc}")
        pc_detail = pc.get_cluster_detail()
        if not pc_detail:
            raise flaskex.InternalServerError(f"Failed to get {self.pc} profile, quit!")
        self._write_log(log_info=f"Adding names and IPs for {self.pc}")
        kwargs['dns_names'].append(pc_detail['name']) #retse999-nxp001
        kwargs['dns_names'].append(f"{pc_detail['name']}.{self.domain}") #retse999-nxp001.ikea.com
        kwargs['dns_names'].append(f'{self.pc.replace(f".{self.domain}", "")}') #SSP-PPE-NTX
        kwargs['dns_names'].append(self.pc) #SSP-PPE-NTX.ikea.com
        kwargs['ip_addrs'].append(pc_detail['clusterExternalIPAddress']) #**********
        self._write_log(log_info=f"SANs payload for {self.pc} is {kwargs}")
        self._write_log(log_info=f"Writing the private key file to {self.cert_tmp_path}\\{pc_detail['name']}.pem")
        prv_key = SslRequest.generate_private_key(prv_file=os.path.join(self.cert_tmp_path, f"{pc_detail['name']}.pem"))
        if not prv_key:
            raise flaskex.InternalServerError(f"Failed to generate the private key for {self.pc}, quit!")
        self._write_log(log_info=f"Generating the CSR file to {self.cert_tmp_path}\\{pc_detail['name']}.csr")
        csr_rsp = SslRequest.generate_csr(prv_key=prv_key, csr_file=os.path.join(self.cert_tmp_path, f"{pc_detail['name']}.csr"), **kwargs)
        if not csr_rsp:
            raise flaskex.InternalServerError(f"Failed to generate CSR for {pc_detail['name']}, quit!")
        csr_str = ""
        with open(os.path.join(self.cert_tmp_path, f"{pc_detail['name']}.csr"), "r") as csr_file:
            csr_str = csr_file.read().replace("\n", "")
        self._write_log(log_info=f"Calling KeyFactorAPI to request certificate for {self.pc}")
        _kf = KeyFactorAPI(fqdn=self.pc_benchmark['endpoint']['endpoints']['pki']['endpoint'], username=self.kf_sa['username'], password=self.kf_sa['password'])
        res, data = _kf.enroll_certificate_by_csr(csr_str=csr_str, template=self.pc_benchmark['certificate']['template'], \
                                                  cert_authority=self.pc_benchmark['certificate']['authority'], \
                                                  cert_ownergroup=self.pc_benchmark['certificate']['cert_owner_group'], \
                                                  email_contact=self.pc_benchmark['certificate']['email_contact'], \
                                                  include_chain=False)
        if not res:
            raise flaskex.InternalServerError(f"Failed to generate the certificate for {self.pc}, quit! Reason: {data}")
        self._write_log(log_info=f"Writing the certificate file to {self.cert_tmp_path}\\{pc_detail['name']}.cer")
        with open(os.path.join(self.cert_tmp_path, f"{pc_detail['name']}.cer"), "w") as cer_file:
            cer_file.write(data)
        if not os.path.exists(os.path.join(self.cert_tmp_path, "chain")):    
            self._write_log(log_info=f"Writing the chain file to {self.cert_tmp_path}\\chain")
            with open(os.path.join(self.cert_tmp_path, "chain"), "w") as chain_file:
                chain_file.write(self.pc_benchmark['certificate']['chain'])
        self._write_log(log_info=f"Uploading the certificate to {self.pc}")
        res = pc.upload_ssl_cert(keyfile=os.path.join(self.cert_tmp_path, f"{pc_detail['name']}.pem"), certfile=os.path.join(self.cert_tmp_path, f"{pc_detail['name']}.cer"), chainfile=os.path.join(self.cert_tmp_path, "chain"))
        if res:
            self._write_log(log_info=f"Successfully update the certificate for PC {self.pc}")
            return
        raise flaskex.InternalServerError(f"Failed to update the certificate for PC {self.pc}")

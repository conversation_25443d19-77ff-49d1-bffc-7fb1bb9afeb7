from flask_restful import Resource
from flask import request, jsonify
from flask_apispec import use_kwargs
import werkzeug.exceptions as flaskex
import traceback
import sys
import logging
import sqlalchemy
from business.benchmark.benchmark import Benchmark
from swagger.benchmark_schema import ListBenchmarkRequestSchema, BenchmarkLinkedClusterSchema
from .route import route


@route('/api/v1/benchmark/<int:bmk_id>')
class RestfulBenchmark(Resource):
    def get(self, bmk_id):
        return jsonify(Benchmark().get_bmk_by_id(bmk_id=bmk_id, real_fw_rules=True))


@route('/api/v1/benchmark/list')
class RestfulListBenchmark(Resource):
    @use_kwargs(ListBenchmarkRequestSchema, location='json')
    def post(self, **kwargs):   # pylint: disable=W0613
        try:
            if filters := request.get_json(force=True):
                return jsonify(Benchmark().get_bmk_list(is_full=True, **filters))
        except Exception as e:
            return {"error": str(e)}, 500


@route('/api/v1/benchmark/brief_list')
class RestfulBriefListBenchmark(Resource):
    def get(self): 
        return jsonify(Benchmark.get_bmk_brief_list())


@route('/api/v1/benchmark/list_with_vlan_config')
class RestfulListBenchmarkWithVlanConfig(Resource):
    def get(self):
        return jsonify(Benchmark().get_bmk_list_with_vlan_config())


@route('/api/v1/benchmark/backbone')
#this api is only for frontend
class RestfulBenchmarkBackbone(Resource):
    def post(self):
        bmk_id = request.get_json(force=True)
        # if no bmk_id is provided, return the structure without any real data,
        # if bmk_id is provided, return the beautified bmk structure with real data
        try:
            return jsonify(Benchmark.generate_bmk_frontend_tree(bmk_id["bmk_id"])
                           if bmk_id else Benchmark.get_bmk_backbone())
        except sqlalchemy.exc.NoResultFound as e:
            logging.error(str(repr(traceback.format_exception(sys.exception()))))
            return {"error": str(e)}, 500


@route('/api/v1/benchmark/linked_cluster')
class RestfulBenchmarkLinkedCluster(Resource):
    @use_kwargs(BenchmarkLinkedClusterSchema, location="json")
    def post(self, *args, **kwargs):  # pylint: disable=W0613
        bmk_id = request.get_json(force=True)
        print(bmk_id)
        try:
            return jsonify(Benchmark.get_linked_cluster(bmk_id["bmk_id"]))
        except sqlalchemy.exc.NoResultFound as e:
            logging.error(str(repr(traceback.format_exception(sys.exception()))))
            return {"error": f"Benchmark id not found. Error message: {e}."}, 500


@route('/api/v1/benchmark/sub_bmk_label')
#this api is only for frontend
class RestfulBenchmarkSubBMKLabel(Resource):
    def post(self, *args, **kwargs): # pylint: disable=W0613
        bmk_id = request.get_json(force=True)
        try:
            return jsonify(Benchmark.get_single_bmk_detail(bmk_id["bmk_id"]))
        except sqlalchemy.exc.NoResultFound as e:
            return {"error": str(e)}, 500


@route('/api/v1/benchmark/all_cluster')
class RestfulBenchmarkAllCluster(Resource):
    def get(self):
        try:
            return jsonify(Benchmark.get_all_cluster())
        except sqlalchemy.exc.NoResultFound as e:
            logging.error(str(repr(traceback.format_exception(sys.exception()))))
            return {"error": f"Benchmark id not found. Error message: {e}."}, 500


@route('/api/v1/benchmark/update/<int:bmk_id>')
class RestfulUpdateBenchmark(Resource):
    def post(self, bmk_id):
        detail = request.get_json(force=True)
        # return {"error": "Benchmark id not found."}, 500
        return jsonify(Benchmark.update_bmk(bmk_id=bmk_id, detail=detail))
    
        
@route('/api/v1/benchmark/sub_bmk_backbone')
class RestfulGetSubBenchmarkBackbone(Resource):
    def get(self):
        # detail = request.get_json(force=True)
        # print(detail)
        # return {"error": "Benchmark id not found."}, 500
        return jsonify(Benchmark.get_sub_bmk_backbone())


@route('/api/v1/benchmark/sub_bmk_detail')
class RestfulGetSubBenchmarkDetail(Resource):
    def post(self):
        sub_bmk = request.get_json(force=True)
        # print(detail)
        # return {"error": "Benchmark id not found."}, 500
        return jsonify(Benchmark.get_sub_bmk_detail(sub_bmk["sub_bmk_type"], sub_bmk["sub_bmk_id"]))


@route('/api/v1/benchmark/sub_bmk/update')
class RestfulUpdateSubBenchmark(Resource):
    def put(self):
        try:
            sub_bmk = request.get_json(force=True)
            # print(detail)
            # return {"error": "Benchmark id not found."}, 500
            return jsonify(Benchmark.update_sub_bmk(sub_bmk["sub_bmk_type"], sub_bmk    ["sub_bmk_id"], sub_bmk["sub_bmk_detail"]))
        except flaskex.Conflict as e:
            logging.error(str(repr(traceback.format_exception(sys.exception()))))
            return {"error": str(e)}, 409


@route('/api/v1/benchmark/sub_bmk/create')
class RestfulCreateSubBenchmark(Resource):
    def post(self):
        try:
            # This will raise an exception if the sub benchmark already exists
            # or if the data is invalid.
            # The exception will be caught and handled by the Flask error handler.
            sub_bmk = request.get_json(force=True)
            # print(detail)
            # return {"error": "Benchmark id not found."}, 500
            return jsonify(Benchmark.create_sub_bmk(sub_bmk["sub_bmk_type"], sub_bmk["sub_bmk_detail"]))
        except flaskex.Conflict as e:
            logging.error(str(repr(traceback.format_exception(sys.exception())))) 
            return {"error": str(e)}, 409
        except Exception as e:
            logging.error(str(repr(traceback.format_exception(sys.exception()))))
            return {"error": str(e)}, 500


@route('/api/v1/benchmark/sub_bmk/delete')
class RestfulDeleteSubBenchmark(Resource):
    def post(self):
        try:
            sub_bmk = request.get_json(force=True)
            return jsonify(Benchmark.delete_sub_bmk(sub_bmk["sub_bmk_type"], sub_bmk["sub_bmk_id"]))
        except flaskex.NotFound as e:
            logging.error(str(repr(traceback.format_exception(sys.exception()))))
            return {"error": str(e)}, 404
        except Exception as e:
            logging.error(str(repr(traceback.format_exception(sys.exception()))))
            return {"error": str(e)}, 500
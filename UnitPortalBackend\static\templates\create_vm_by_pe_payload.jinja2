{
    "boot": {
        {% if (workload_type == 'linux') or (workload_type == 'network') %}
        "secure_boot": false,
        "uefi_boot": false,
        {% else %}
        "secure_boot": true,
        "uefi_boot": true,
        {% endif %}
        "disk_address": {
            "device_bus": "scsi",
            "device_index": 0
        }
    },
    {% if (workload_type == 'linux') or (workload_type == 'network') %}
    "machine_type": "PC",
    {% else %}
    "machine_type": "Q35",
    {% endif %}
    "memory_mb": {{ memory_size_mib }},
    "name": "{{ vm_name }}",
    "num_cores_per_vcpu": {{ num_cores_per_vcpu }},
    "num_vcpus": {{ num_vcpus }},
    "power_state": "UNKNOWN",
    {% if workload_type == 'linux' -%}
    "vm_customization_config": {
        "userdata": {{ user_data }}                     {# don't add "" #}
    },
    {%- endif %}
    "vm_disks": [
        {% for disk_size in disks %}
        {
            "disk_address": {
                "device_bus": "scsi",
                "device_index": {{ loop.index }}       {# index start from 1 #}
            },
            "is_cdrom": false,
            "vm_disk_create": {
                "size": {{ disk_size }},
                "storage_container_uuid": "{{ storage_container_uuid }}"
            }
        },
        {% endfor %}
        {
            "disk_address": {
                "device_bus": "scsi",
                "device_index": 0
            },
            "is_cdrom": false,
            "vm_disk_clone": {
            "disk_address": {
                "vmdisk_uuid": "{{ image_vm_disk_uuid }}"
            },
                "minimum_size": {{ system_disk_size }}
            }
        }
    ],
    "vm_nics": [
        {
            "network_uuid": "{{ network_uuid }}"
        }
    ]
}
<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <sidebar class="sidebar-container" />
    <div :class="{hasTagsView:needTagsView}" class="main-container" >
      <div :class="{'fixed-header':fixedHeader}">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <right-panel v-if="showSettings">
        <settings />
      </right-panel>
    </div>
  </div>
</template>

<script>

import RightPanel from '@/components/RightPanel'
import { AppMain, Navbar, Settings, Sidebar, TagsView, StarRating } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'

export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    <PERSON><PERSON><PERSON><PERSON>,
    Settings,
    Sidebar,
    TagsView,
    StarRating
  },
  data(){
    return {
      background_image: require('@/assets/logo/ikea-logo2.webp'),
    }
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      showSettings: state => state.settings.showSettings,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/mixin.scss";
  @import "~@/styles/variables.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;

    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$sideBarWidth});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px)
  }

  .mobile .fixed-header {
    width: 100%;
  }

  .passport-login-tip-container {
    position: fixed;
    font-family: -apple-system, SF UI Text, Arial, PingFang SC, Hiragino Sans GB, Microsoft YaHei, WenQuanYi Micro Hei, sans-serif;
    bottom: 24px;
    right: 24px;
    width: 368px;
    padding: 24px 16px;
    background: #fff;
    color: #555666;
    box-shadow: 0px 0px 10px 2px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    z-index: 9999;
  }
  .addWidth {
    height: 100vh!important;
    right: 0px!important;
    width: 580px!important;
    min-width:580px!important;
    position: absolute;
    z-index: 11!important;
    top: 0px!important;
    bottom: 0px!important;
    bottom: calc(30px + 1.767rem);
  }
  
  .m-slidebar{
    height: 46vh!important;
    // transition: all ,500ms ;
  // transition: 0.5s ease-in-out;;
    // transform: translate(90%);
    // transition: width 8s;
    // transition: all ,500ms ;
    right: -580px;    
    width: 580px;
    min-width: 580px;
    position: fixed;   
    z-index: 11;
    // transition: width 0.8s ease-in-out 0.1;
    transition: width 1s;
    transition: all 0.2s;
    // transition: opacity .3s cubic-bezier(.7, .3, .1, 1);
    // height: calc(100vh - 2.5rem);
    background: white;
    box-shadow: 0px 5px 5px rgba(11,2,5,0.1);
    top: 0px;
    bottom: 0px;
    .m-closeBtn{
      position: absolute;
      width: 30px;
      height: 138px;
      left: -30px;
      // background-image: linear-gradient(135deg, #5e60eb 3%, #4282fa 100%),linear-gradient(#edf2fc, #edf2fc);
      box-shadow: 0px 5px 5px rgba(11,2,5,0.1);
      border-radius: 6px 0px 0px 6px;
      text-align: center;
      line-height: 40px;
      font-size: 18px;
      bottom: 320px;
      cursor: pointer;
      i {
        font-size: 24px;
        line-height: 48px;
      }
    }
  }
  .handle-button {
    bottom: 320px;
    width: 28px;
    height: 138px;
    position: absolute;
    top: 0;
    left: -28px;
    text-align: center;
    font-size: 18px;
    border-radius: 6px 0 0 6px !important;
    z-index: 0;
    pointer-events: auto;
    cursor: pointer;
    color: #fff;
    line-height: 48px;
    i {
      font-size: 24px;
      line-height: 48px;
    }
  }
  .sticky-button-text {
    writing-mode: vertical-rl;
    // text-orientation: upright;
    // white-space: nowrap;\
    position: absolute;
    margin-left: -32px;
    margin-top: 40px;
    color: #fff;
    transform: rotate(180deg);
    writing-mode: vertical-rl;
  }
</style>



import logging
import multiprocessing
import os
import sys
import time
import traceback
import uuid
from datetime import datetime
import psutil
import sqlalchemy
from flask import Flask

from business.authentication.authentication import ServiceAccount
from business.distributedhosting.nutanix.base_up_task_property import BaseUpTaskProperty
from business.distributedhosting.nutanix.task_status import TaskStatus
from business.generic.base_up_exception import BaseUpException
from business.generic.commonfunc import get_user_by_token, create_file, setup_common_logger
from business.loggings.loggings import DBLogging, IntegratedLogger
from models.database import db
from werkzeug.exceptions import InternalServerError, Conflict, HTTPException
from business.generic.commonfunc import DBConfig


class BaseUpTask:
    """
    function entrance: run() -> spawn_subprocess() -> start_task() -> task_process()

    Attributes:
        TASK_ONGOING_STATUS (list): Used together with self.task_duplicated_kwargs, to check if there are duplicate tasks with ongoing status.
        FINAL_STATUS (list): After self.task_process is finished, check if the task is in a final status, if not, set task status to TaskStatus.DONE
    """
    LOG_DIR = ""
    LOG_TYPE = ""
    TASK_TYPE = ""
    EXISTING_TASK_ALLOWED = False
    TASK_ONGOING_STATUS = [TaskStatus.NOT_STARTED, TaskStatus.IN_PROGRESS, TaskStatus.IN_CLEANUP]
    FINAL_STATUS = [TaskStatus.DONE_WITH_ERROR, TaskStatus.SKIPPED, ]

    def __init__(self, task_model, task_model_schema, log_model, log_model_schema, payload=None):
        self.task_model = task_model
        self.task_model_schema = task_model_schema
        self.log_model = log_model
        self.log_model_schema = log_model_schema
        self.payload = payload
        self.task = None
        self.logger = None
        self.db_logger = None
        self.ilg = None
        # Properties to check if there's already an existing ongoing task, like pe name
        self.task_duplicated_kwargs = {}
        # Task info to be saved in db
        self.task_info = {}
        # A string to identify a task, used in the log file name,
        # e.g. task_identifier is pe name -> log file name: CREATE_CLUSTER_{task_identifier}_q_2024-07-30-06-58-42
        self.task_identifier = ""

    @staticmethod
    def init_flask_app():
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)
        app.app_context().push()

    def run(self, use_sa=False):
        if self.EXISTING_TASK_ALLOWED and self.payload.get(BaseUpTaskProperty.TASK_ID):
            self.task = self.find_existing_task(self.payload[BaseUpTaskProperty.TASK_ID])
        else:
            self.check_task_existence()
            self.task = self.create_task_in_db(use_sa=use_sa)
        task_id = self.task.id
        try:
            self.spawn_subprocess(self.start_task, task_id, init_app=False)
            return task_id
        except Exception as e:
            error_info = str(repr(traceback.format_exception(sys.exception())))
            logging.error(error_info)
            raise e

    def start_task(self):
        logging.info(f"In subprocess task, task_id: {self.task.id}")
        self.init_flask_app()
        # Re-set self.task for the sub process
        self.task = self.task_model.query.filter_by(id=self.task.id).one()
        self.setup_loggers()
        try:
            self.set_task_status(TaskStatus.IN_PROGRESS)
            self.ilg.write("Start to run the task.")
            self.task_process()
            if self.task.status not in self.FINAL_STATUS:
                self.set_task_status(TaskStatus.DONE)
            self.ilg.write(f"Task is in '{self.task.status}' status.")
        except BaseUpException as e:
            self.set_task_status(e.status)
            self.ilg.write(f"Task failed. Detail: {e}", severity="error")
        except HTTPException as e:
            self.set_task_status(TaskStatus.ERROR)
            self.ilg.write(f"Task failed. Detail: {e}", severity="error")
        except Exception:
            self.set_task_status(TaskStatus.ERROR)
            error_info = str(repr(traceback.format_exception(sys.exception())))
            self.ilg.write(f"Task failed. Detail: {error_info}", severity="error")

    def task_process(self):
        # To be implemented in subclass
        pass

    def check_task_existence(self):
        logging.info("Checking if task already exists in database...")
        result = self.task_model.query.filter_by(**self.task_duplicated_kwargs).filter(
            self.task_model.status.in_(self.TASK_ONGOING_STATUS)).all()
        if result:
            raise Conflict(
                f"Task {self.task_duplicated_kwargs} already has task in status {self.TASK_ONGOING_STATUS}! "
                "Please wait for previous task to be done.")
        logging.info(f"Task {self.task_duplicated_kwargs} does not exist.")

    def create_task_in_db(self, use_sa=False):
        logging.info("Start to create task in database...")
        self.task_info[BaseUpTaskProperty.CREATE_DATE] = datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
        if use_sa:
            self.task_info[BaseUpTaskProperty.CREATOR] = \
                ServiceAccount(usage=ServiceAccount.NUTANIX_PM).get_service_account()['username']
        else:
            self.task_info[BaseUpTaskProperty.CREATOR] = get_user_by_token().username
        self.task_info[BaseUpTaskProperty.STATUS] = TaskStatus.NOT_STARTED
        for _ in range(3):
            try:
                task_to_add = self.task_model(**self.task_info)
                db.session.add(task_to_add)
                db.session.commit()
                time.sleep(1)
                logging.info("Task created successfully! "
                             f"Task type: {self.task_model()}. "
                             f"Task detail: {self.task_model_schema().dump(task_to_add)}")
                return task_to_add
            except sqlalchemy.exc.DBAPIError as e:
                logging.warning("SQL error occurred when creating task, trying to rollback and create again...")
                logging.warning(f"Original Error: {e}")
                db.session.rollback()
        raise InternalServerError("Failed to create task in db... Please check log.")

    def find_existing_task(self, existing_task_id):
        existing_task = self.task_model.query.filter_by(id=existing_task_id).one()
        return existing_task

    def spawn_subprocess(self, target_function, task_id, args=[], kwargs={}, init_app=True, check_process_amount=True):
        if init_app:
            self.init_flask_app()
        if args:
            if not isinstance(args, list):
                raise InternalServerError("The param 'args' should be list type!")
            p = multiprocessing.Process(target=target_function, args=args)
        elif kwargs:
            if not isinstance(kwargs, dict):
                raise InternalServerError("The param 'kwargs' should be dict type!")
            p = multiprocessing.Process(target=target_function, kwargs=kwargs)
        else:
            p = multiprocessing.Process(target=target_function)
        if check_process_amount:
            self.check_process_amount()
        logging.info(f"Spawning a subprocess to do the task, task_id: {task_id}")
        p.start()
        self.store_pid_into_db(p.pid, self.task_model, task_id)
        logging.info(f"Subprocess spawn successfully, pid: {p.pid}")

    @staticmethod
    def check_process_amount(limit=30):
        logging.info("Checking process amount on server...")
        main_pid = os.getpid()
        main_process = psutil.Process(main_pid)
        child_processes = main_process.children(recursive=True)
        count = 0
        for c in child_processes:
            if 'python' in c.name().lower():
                count += 1
            if count >= limit:
                raise InternalServerError(
                    f"Processes on the server already reached limit ({limit})! Please wait for previous tasks to be done.")
        logging.info(f"Current processes running on server: {count}, limit is {limit}")

    @staticmethod
    def store_pid_into_db(pid, task_model, task_id):
        _task = task_model.query.filter_by(id=task_id).first()
        _task.pid = pid
        db.session.commit()

    def setup_loggers(self, log_dir=None, log_name=None, origin_log_path=None):
        if self.task.detail_log_path:
            logging.info(f"Resuming an existing task, log path: {self.task.detail_log_path}")
            log_path = self.task.detail_log_path
        else:
            if not log_dir:
                log_dir = self.LOG_DIR
            if not log_name:
                log_name = (f"{self.TASK_TYPE}_{self.task_identifier if self.task_identifier else self.task.name}_"
                            f"{self.task.creater}_{datetime.utcnow().strftime('%Y-%m-%d-%H-%M-%S')}")
            log_path = os.path.join(log_dir, log_name)
        if origin_log_path:
            log_path = origin_log_path
        self.db_logger = DBLogging(logdir=log_dir, taskid=self.task.id, logtype=self.LOG_TYPE, log_model=self.log_model)
        if not os.path.exists(log_path):
            log_path = create_file(filepath=self.LOG_DIR, filename=log_name)
            self.db_logger.write(f"Log file created: {log_path}")
        else:
            self.db_logger.write(f"Using existing log path: {log_path}")
        self.set_task_property(BaseUpTaskProperty.DETAIL_LOG_PATH, log_path)

        self.logger = setup_common_logger(str(uuid.uuid4()), log_path)
        self.ilg = IntegratedLogger(file_lg=self.logger, db_lg=self.db_logger)

    def set_task_status(self, status):
        self.set_task_property(BaseUpTaskProperty.STATUS, status)

    def set_task_property(self, key, value):
        setattr(self.task, key, value)
        db.session.commit()

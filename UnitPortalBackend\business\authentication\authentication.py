# installed modules
import sys
from io import String<PERSON>
from typing import List
import base64
from datetime import datetime
import logging
import re
import json

import paramiko
import requests
import sqlalchemy
from sqlalchemy import and_
from ldap3 import Server, Connection, ALL
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
from Crypto.Util.Padding import pad, unpad
from werkzeug.exceptions import BadRequest
import traceback
import werkzeug.exceptions as flaskex
import threading

from business.generic.base_up_exception import VaultGetSecretFailed
# local file
# from business.authentication.group import Group

from business.generic.commonfunc import compare_time_diff
from models.auth_models import ModelServiceAccount, ModelRoleSchema, ModelRole, ModelUser, ModelUserGroupRoleMapping, \
    ModelUserGroupRoleMappingSchema, model_mapping, model_schma_mapping, ModelServiceAccountSchema, ModelUserSchema
from models.database import db
import static.SETTINGS as SETTING


class LdapAdmin(object):
    def __init__(self, username, password):
        """
        init
        """
        self.host = SETTING.LDAP_DOMAIN_CONTROLLER
        self.use_ssl = True
        self.username = username
        self.password = password
        self.login()

    def login(self):
        """
        登录
        :return:
        """
        server = Server(host=self.host, use_ssl=self.use_ssl, connect_timeout=15, get_info=ALL)
        self.conn = Connection(server, user=self.username, password=self.password, lazy=False)
        res = self.conn.bind()
        return res

    def search_group(self, group_name):
        # return all users of the group and the subgroup
        members = []
        users = []
        if not self.login():
            return False
        group_info = self.search_group_in_ad(group_name)
        members += self.get_members(group_info)
        while members:
            cur_member = members.pop(0)
            try:
                cur_member_cn = [_.split('=')[1] for _ in cur_member.split(',') if _.split('=')[0] == 'CN'][0]
            except IndexError:
                logging.error(f"Current member {cur_member} doesn't have valid CN.")
                continue
            if self.is_member_a_group(cur_member_cn):
                members += self.get_members(self.search_group_in_ad(cur_member_cn))
            else:
                cur_sAMAccountName = self.get_user_sAMAccountName_by_cn(cur_member_cn)      # pylint: disable=invalid-name
                users.append(f"{cur_sAMAccountName}@ikea.com")
        return set(users)

    def search_group_in_ad(self, group_name):
        self.conn.search(
            search_base='DC=ikea,DC=com',
            search_filter=f'(&(objectClass=group)(name={group_name}))',
            search_scope="SUBTREE",
            attributes=['member', 'objectClass']
        )
        return self.get_conn_json_response()

    def search_user_in_ad(self, user):
        self.conn.search(
            search_base='DC=ikea,DC=com',
            search_filter=f'(&(objectClass=user)(sAMAccountName={user}))',
            search_scope="SUBTREE",
            # attributes=['member', 'objectClass']
        )
        return self.get_conn_json_response()


    def get_members(self, r):
        return r["entries"][0]["attributes"]["member"]

    def is_member_a_group(self, member):
        r = self.search_group_in_ad(member)
        return r.get('entries') and 'group' in r.get('entries')[0]["attributes"]["objectClass"]

    def get_user_sAMAccountName_by_cn(self, cn):    # pylint: disable=invalid-name
        self.conn.search(
            search_base='DC=ikea,DC=com',
            search_filter=f'(&(name={cn}))',
            search_scope="SUBTREE",
            attributes=['sAMAccountName', 'objectClass']
        )
        res = self.get_conn_json_response()
        return res["entries"][0]["attributes"]["sAMAccountName"]

    def get_conn_json_response(self):
        return json.loads(self.conn.response_to_json(self.conn.response))

    def is_group_existing_in_ad(self, group_name):
        group_info = self.search_group_in_ad(group_name)
        return group_info.get("entries")

    def is_user_existing_in_ad(self, user):
        user_info = self.search_user_in_ad(user)
        return user_info.get("entries")



class User(object):
    def __init__(self):
        """
        init
        """
        pass

    def get_user_info_from_db(self):
        userlist = ModelUserSchema(many=True).dump(ModelUser.query.all())
        users_report = []
        for user_detail in userlist:
            role_list = []
            single_user_detailinfo = {}
            user_role = user_detail['role']
            if re.match(r'\D', user_role):
                single_user_detailinfo['roles'] = user_role if user_role else "Null"
            else:
                for roleid in user_role.split(','):
                    userrole = ModelRole.query.filter_by(id=roleid).all()
                    role_list.append(userrole[0].name)
                single_user_detailinfo['roles'] = ';'.join(role_list) if role_list else "Null"
            role_mapping = ModelUserGroupRoleMapping.query.all()
            # print(user_detail['id'])
            # user_role = UserRole(user_id=user_detail['id'])
            # user_privileges = user_role.get_all_privileges()
            # print(user_privileges)
            # role_group_names = [role_group_name for role_group_name in user_privileges]
            # for role_group_name in role_group_names:
            #     privileges_list = privileges_list + [privilege_name for privilege_name in user_privileges[role_group_name] if user_privileges[role_group_name][privilege_name] =="full"]       # noqa
            single_user_detailinfo['id'] = user_detail['id']
            single_user_detailinfo['role_number'] = len([mapping for mapping in role_mapping 
                                                     if (mapping.user_id == user_detail['id'] and not mapping.from_group)])
            #get direct role mappings, not from group mapping
            single_user_detailinfo['username'] = user_detail['username']
            single_user_detailinfo['domain'] = user_detail['domain'] if user_detail['domain'] else "Null"
            single_user_detailinfo['lastlogondate'] =user_detail['lastlogondate'] if user_detail['lastlogondate'] else "Null"       # noqa
            # single_user_detailinfo['roles']    = ';'.join(privileges_list) if privileges_list else "Null"
            users_report.append(single_user_detailinfo)
        return users_report

    def get_user_by_name(self, name):
        user = ModelUser.query.filter_by(username=name).first()
        return user

    def if_user_exist(self, name):
        try:
            users = ModelUser.query.filter_by(username=name).all()
            if len(users) == 1:
                return True, '1 user'
            if len(users) > 1:
                return False, f'duplicate users :{name}.'
            return False, f'user {name} not found.'
        except Exception:
            return False, str(repr(traceback.format_exception(sys.exception())))

    def validate_local_user(self, name, pwd):
        try:
            user = ModelUser.query.filter_by(username=name).one()
            if user.pwd != pwd:
                raise flaskex.Unauthorized("Wrong password!")
        except sqlalchemy.exc.NoResultFound:
            raise flaskex.BadRequest(f"Can't find user {name} in unit portal database!")
        except sqlalchemy.exc.MultipleResultsFound:
            raise flaskex.BadRequest(f"Found multiple user with name '{name}' in unit portal database!")

    def get_role_by_token(self, token):
        users = ModelUser.query.filter_by(token=token).all()
        if len(users) == 1:
            if compare_time_diff(users[0].lastlogondate):
                return False, 'token has expired, please login again'
            users[0].lastlogondate = datetime.utcnow().strftime('%Y-%m-%d,%H:%M:%S')
            db.session.commit()
            return True, users[0]
        if len(users) > 1:
            logging.error(f'duplicate token :{token}')
            return False, 'Found duplicate user with same token in database!'
        logging.error(f'user {token} not found')
        return False, "Can't find user from database! Please use correct token!"

    def update_token_to_user(self, uid, token):
        users = ModelUser.query.filter_by(id=uid).first()
        users.token = token
        users.lastlogondate = datetime.utcnow().strftime('%Y-%m-%d,%H:%M:%S')
        db.session.commit()
        return users

    @classmethod
    def bulk_create_by_user_names(cls, user_names):
        """Not using bulk_save_objects because 'user_ids' are required to return"""
        user_ids = []
        for user_name in user_names:
            user = ModelUser.query.filter_by(username=user_name).scalar()
            if not user:
                user = ModelUser().create_new_user(name=user_name)
            user_ids.append(user.id)
        return user_ids

        # existing_users = ModelUser.query.filter(ModelUser.username.in_(user_names))
        # existing_user_names = [user.username for user in existing_users]
        # db.session.bulk_save_objects([
        #     ModelUser(username=user_name, role="pm_user", domain="ikea.com")
        #     for user_name in user_names
        #     if user_name not in existing_user_names
        # ])

    def create_new_user(self, username):
        logging.info(f"Start to create new user '{username}...")
        new_user = ModelUser().create_new_user(username)
        logging.info("User created")
        return new_user


class ServiceAccount:
    NUTANIX_PM = "nutanix_pm"
    NUTANIX_WH_DT = "wh_nutanix_datafetch_dt"
    THORS_HAMMER = "thors_hammer"
    LIN_API = "lin_api"
    CVM_DEFAULT = "cvm_default"
    AHV_DEFAULT = "ahv_default"
    ILO_DEFAULT = "ilo_default"

    def __init__(self, usage="nutanix_pm"):
        """
        init
        """
        self.usage = usage

    def get_service_account(self, usage=None, sa = None):
        if not usage:
            usage = self.usage
        if not sa:
            _sa = ModelServiceAccount.query.filter_by(usage=usage).first()      # first() will not raise exception but return None when no result found
        else:
            _sa = sa
        if not _sa:
            msg = f"Can't find service account with usage '{usage}' in database!"
            logging.error(msg)
            raise flaskex.InternalServerError(msg)
        username, iv, key, crypted = _sa.username, _sa.iv, _sa.key, _sa.crypted
        b64_iv = iv.encode("ascii")  # turn str into base64 byte
        _iv = base64.b64decode(b64_iv)  # turn base64 byte into byte
        b64_key = key.encode("ascii")
        _key = base64.b64decode(b64_key)
        b64_crypted = crypted.encode("ascii")
        _crypted = base64.b64decode(b64_crypted)
        cipher = AES.new(_key, AES.MODE_CBC, _iv)  # AES only take byte as parameters.
        pwd = unpad(padded_data=cipher.decrypt(_crypted), block_size=16)
        # fernet = Fernet(_key.encode())
        # _ac = fernet.decrypt(ac.encode()).decode()
        # _ss = fernet.decrypt(ss.encode()).decode()
        return {"username": username, "password": pwd.decode("ascii")}

    @staticmethod
    def create_service_account(username, password, usage):
        password_byte = password.encode("ascii")
        # generate the key for encryption, 16 bytes
        _key = get_random_bytes(16)
        # pad the password, make it multiples of 16
        password_byte = pad(password_byte, 16)
        cipher = AES.new(_key, AES.MODE_CBC)
        crypted = cipher.encrypt(password_byte)
        iv = cipher.iv
        # sk = Fernet.generate_key() #key is bytes
        # fernet = Fernet(sk) #init the Fernet
        # ac = fernet.encrypt(username.encode())#username should be string
        # ss = fernet.encrypt(password.encode())
        payload = {
            "username": username,
            "iv": base64.b64encode(iv),
            "key": base64.b64encode(_key),
            "crypted": base64.b64encode(crypted),
            "usage": usage
        }
        sa = ModelServiceAccount(**payload)
        db.session.add(sa)
        db.session.commit()

    @staticmethod
    def delete_service_account(sa_id):
        ModelServiceAccount.query.filter_by(id=sa_id).delete()
        db.session.commit()

    @staticmethod
    def get_service_account_list():
        _sa_list = ModelServiceAccount.query.all()
        saschema = ModelServiceAccountSchema(many=True)
        sa_list = saschema.dump(_sa_list)
        return sa_list


class Vault():
    def __init__(self, tier="PRODUCTION", sa=None, engine=None, namespace=None, url=None, usage=None, thread_safe = False) -> None:
        self.tier = tier
        self.threading = thread_safe
        if self.threading:
            self.token_usage_count = 0 # Thread-safe counter for token usage
            self.lock_token = threading.Lock() 
        
        if not sa:
            """
            usage: 
            vault_production    ->  retail prod
            vault_preproduction ->  retail preprod
            vault_ikead2        ->  retail d2
            vault_ikeadt        ->  retail dt
            vault_warehouse     ->  warehouse prod
            vault_warehousedt   ->  warehouse dt
            """
            if not usage:
                _sa = ServiceAccount(usage="vault_" + self.tier.lower())
                self.sa = _sa.get_service_account()
            else:
                _sa = ServiceAccount(usage)
                self.sa = _sa.get_service_account()
        else:
            self.sa = sa
        if not engine:
            self.engine = SETTING.VAULT[self.tier.upper()]['ENGINE']
        else:
            self.engine = engine
        if not namespace:
            self.namespace = SETTING.VAULT[self.tier.upper()]['NAMESPACE']
        else:
            self.namespace = namespace
        if not url:
            self.baseurl = SETTING.VAULT['URL']
        else:
            self.baseurl = url
        res, token = self.get_token()
        if res:
            self.token = token
        else:
            raise Exception(f"Failed to get Vault token. Caused by {token}")

    def get_token(self):
        try:
            url = f"https://{self.baseurl}/v1/auth/approle/login"
            headers = {
                "X-Vault-Namespace": self.namespace
            }
            payload = {
                "role_id": self.sa['username'],
                "secret_id": self.sa['password']
            }
            tk = requests.post(url=url, headers=headers, json=payload)
            data = (tk.json())['auth']['client_token']
            return True, data
        except Exception:
            return False, str(repr(traceback.format_exception(sys.exception())))

    def get_secret(self, secret):
        if self.threading:
            with self.lock_token: # Needed for handling thread safety and guarantee logical output
                if self.token_usage_count > 196: # max number of token usage is 200?
                    res, token = self.get_token()
                    if res:
                        self.token = token
                    else:
                        return False, token
                    self.token_usage_count = 0
                self.token_usage_count += 1
        try:
            url = f"https://{self.baseurl}/v1/{self.engine}/data/{secret}"
            headers = {
                "X-Vault-Namespace": self.namespace,
                "X-Vault-Token": self.token}
            sec = requests.get(url=url, headers=headers, verify=False)
            data = (sec.json())['data']['data']
            return True, data
        except Exception:
            return False, str(repr(traceback.format_exception(sys.exception())))

    def set_vault_password(self, username, password, label, description='reset pwd with system'):
        try:
            url = f"https://{self.baseurl}/v1/{self.engine}/data/{label}"
            headers = {
                "X-Vault-Namespace": self.namespace,
                "X-Vault-Token": self.token
            }
            payload = {
                "data": {
                    "username": username,
                    "secret": password,
                    "description": description,
                    "last_autoRotate": datetime.utcnow().strftime('%m-%d-%Y %H:%M:%S')
                }
            }
            # print(payload)
            tk = requests.put(url=url, headers=headers, json=payload)
            data = (tk.json())['data']['version']
            return True, data
        except Exception as e:
            return False, str(e)

    def generate_pubkey_from_privkey(self, label):
        res, data = self.get_secret(label)
        if not res:
            raise VaultGetSecretFailed(label)
        public_key = None
        if isinstance(data, dict) and data.get('secret', '').startswith('-----BEGIN RSA PRIVATE KEY-----'):
            public_key = paramiko.RSAKey(file_obj=StringIO(data['secret']))
        return public_key


class UserRole:
    def __init__(self, user_id=None) -> None:
        self.user_id = user_id      # TODO: remove

    def get_frontend_privilege(self):
        # Only return the privilege of [view]
        return self.merge_roles_privileges(only_view=True)

    def get_all_privileges(self):
        """Return the privileges of [view, create, delete]"""
        return self.merge_roles_privileges(only_view=False)

    def merge_roles_privileges(self, only_view=False):
        mappings = ModelUserGroupRoleMapping().query.filter_by(user_id=self.user_id).all()
        role_ids = [mapping.role_id for mapping in mappings]
        privilege = dict()
        roles = ModelRole.query.filter(ModelRole.id.in_(role_ids)).all()
        # roles is list of Modelrole
        sub_role_names = [_ for _ in ModelRole.__table__.columns.keys() if _.startswith("role_")]
        # sub_role_names = ['role_dashboard', 'role_pm', 'role_ntx', 'role_sli', 'role_mkt', 'role_administration']
        for sub_role_name in sub_role_names:
            role_detail = dict()
            sub_role_ids = set([getattr(role, sub_role_name) for role in roles])
            # sub_role_ids is a set of sub role ids. {32, 1, 3, 4, 31}
            model = model_mapping[sub_role_name]
            # model is the corresponding sub role model
            sub_roles = model().query.filter(model.id.in_(sub_role_ids)).all()
            sub_role_columns = \
                [_ for _ in model.__table__.columns.keys() if _.startswith("view") and not _.endswith("scope")] \
                if only_view \
                else [_ for _ in model.__table__.columns.keys() if _ != "id" and not (_.startswith("view") and _.endswith("scope"))]       # noqa
            sub_role_detail = dict()
            for column_name in sub_role_columns:
                # e.g. column_name has 2 formats: create_ntx_pm / create_ntx_pm_scope
                column_values = set([getattr(sub_role, column_name) for sub_role in sub_roles])
                # column_values = {'full', 'part'}
                # column_values = {'', '{"only_self_created": false, "clusters": ["cluster1", "cluster2"], "available_clusters_pattern": [".*CN7.*"]}', None, '{"only_self_created": false, "clusters": ["RETCN888-NXC000.IKEA.COM", "cluster2"], "available_clusters_pattern": [".*CN8.*"]}'}
                if column_name.endswith('_scope') and not column_name.startswith("view") and any(column_values):
                    # handle column_name is a scope
                    try:
                        merged_scope = self.merge_scope(column_name, column_values)
                    except json.decoder.JSONDecodeError:
                        msg = "The scope content can't decode to json. Please contact administrator."
                        logging.error(f"{msg}, {traceback.print_exc()}")
                        return {"message": msg, "error_message": None}, 500
                    sub_role_detail[column_name] = merged_scope
                else:
                    # handle column_name that is not a scope
                    sub_role_detail[column_name] = self.merge_view_access(column_values)
            is_all_empty = True
            for _ in sub_role_detail.values():
                if _ != "empty" and _ is not False:
                    is_all_empty = False
                    break
            if is_all_empty:
                role_detail = False
            # if not any([_ for _ in sub_role_detail.values()]):
            #     # role_detail = False
            #     continue
            else:
                role_detail.update(sub_role_detail)
            privilege[sub_role_name] = role_detail
        return privilege

    def merge_view_access(self, access_set):
        access_priority = ["full", "part", "empty"]
        for access in access_priority:
            if access in access_set:
                return self.parse_access_to_boolean(access)
        return False

    def parse_access_to_boolean(self, access):
        return access
        # return access == "full" or access == "part"

    def merge_scope(self, column_name, scopes):
        def merge_scope_for_create_wl():
            scope_list = []
            print(scopes)
            for _scope in scopes:
                if _scope and json.loads(_scope):
                    # if _scope is string 'null', json will load null ...which will break the logic
                    try:
                        scope_list.extend([json.loads(_scope)])  # need to put json into a list for extending
                    except:
                        continue
            for scope in scope_list:
                for key, value in scope.items():
                    if key not in merged_scope:
                        if isinstance(value, list):
                            merged_scope[key] = value
                        else:
                            merged_scope[key] = [value]
                    elif isinstance(value, list):
                        merged_scope[key].extend(value)
                    else:
                        merged_scope[key].append(value)
            for key, value in merged_scope.items():
                merged_scope[key] = list(set(value))
                if 'all' in value:
                    merged_scope[key] = 'all'
                elif True in value or False in value:
                    merged_scope[key] = True if True in value else False
            return merged_scope

        # logging.debug(f"column_name: {column_name}, start to merge scope.")
        merged_scope = {}
        # specially deal with create_wl_scope
        if column_name == "create_wl_scope":
            return merge_scope_for_create_wl()
        only_self_created_clusters, available_clusters, available_clusters_pattern = [], [], []
        # scopes = {'', '{"only_self_created": false, "clusters": ["cluster1", "cluster2"], "available_clusters_pattern": [".*CN7.*"]}', None, '{"only_self_created": false, "clusters": ["RETCN888-NXC000.IKEA.COM", "cluster2"], "available_clusters_pattern": [".*CN8.*"]}'}
        for scope in scopes:
            if scope:
                scope = json.loads(scope)
            if not scope:   # scope = 'null' -> scope = json.loads(scope) = None
                continue
            if scope.get("only_self_created"):
                only_self_created_clusters.extend(scope.get("clusters"))
            elif scope.get("clusters"):
                available_clusters.extend(scope.get("clusters"))
            if scope.get("available_clusters_pattern"):
                available_clusters_pattern.extend(scope.get("available_clusters_pattern"))
        merged_scope["available_clusters"] = list(set(available_clusters))
        if not column_name.startswith("create"):
            merged_scope["only_self_created_clusters"] = [
                _ for _ in set(only_self_created_clusters) if _ not in set(available_clusters)
            ]
        if not column_name.startswith("abort") and not column_name.startswith("delete"):
            merged_scope["available_clusters_pattern"] = list(set(available_clusters_pattern))
        return merged_scope

    def update_user_roles(self, user_id, role_ids):
        try:
            logging.info("Updating user roles...")
            role_ids_to_delete, role_ids_to_add = self._filter_changed_roles(user_id, role_ids)
            UserGroupRoleMapping.delete_user_mappings_by_role_ids(user_id, role_ids_to_delete)
            UserGroupRoleMapping.add_mappings_for_group_users(0, [user_id], role_ids_to_add)
            db.session.commit()
            logging.info("Update succeeded.")
        except sqlalchemy.exc.SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"Failed to update user roles! Error: {e}")
            raise flaskex.InternalServerError(f"Failed to update user roles! Error: {e}")

    def _filter_changed_roles(self, user_id, role_ids):
        """compare the new roles with db, return role_ids_to_add and role_ids_to_delete"""
        existing_roles = ModelUserGroupRoleMapping.query.filter_by(user_id=user_id).all()
        existing_role_ids = [role.role_id for role in existing_roles]
        role_ids_to_delete = [role_id for role_id in existing_role_ids if role_id not in role_ids]
        role_ids_to_add = [role_id for role_id in role_ids if role_id not in existing_role_ids]
        return role_ids_to_delete, role_ids_to_add


class Role:
    def get_role_list(self):
        roleschema = ModelRoleSchema(many=True)
        role_list = ModelRole.query.all()
        result = roleschema.dump(role_list)
        return result

    def get_rbac_role(self, role_id):
        role = ModelRole.query.filter_by(id=role_id).one()
        #if there is no such role_id, it will raise error
        res = {
            "name": role.name,
            "description": role.description
        }
        priv = dict()
        for _sub in role.sub_role_names: # get the sub role names
            sub_role = None
            if sub_role_id := getattr(role, _sub, None): # if sub role id is not None, query the subrole table
                sub_role = model_mapping[_sub].query.filter_by(id=sub_role_id).first()
            if (not sub_role) or (not sub_role_id):
                sub_role = model_mapping[_sub]()
                db.session.add(sub_role)
                db.session.commit()
                setattr(role, _sub, sub_role.id)
                db.session.commit()
            sub_role_schma = model_schma_mapping[_sub]()
            _dict = sub_role_schma.dump(sub_role)
            priv[_sub] = _dict  
        res["priv"] = priv
        return res    

    def create_rbac_role(self, kwargs):
        role_name = kwargs["name"]
        description = kwargs["description"]
        roles = kwargs["privilege"]
        sub_role_details = dict()
        try:
            for sub_role_name, accesses in roles.items():
                model = model_mapping[sub_role_name]
                verified_accesses = self.handle_accesses(accesses)
                new_sub_role = model(**verified_accesses)
                db.session.add(new_sub_role)
                db.session.commit()
                sub_role_details[sub_role_name] = new_sub_role.id
            new_role = ModelRole(name=role_name, description=description, **sub_role_details)
            db.session.add(new_role)
            db.session.commit()
        except sqlalchemy.exc.IntegrityError:
            db.session.rollback()
            msg = f"Role name '{role_name}' already existed."
            logging.error(msg)
            raise flaskex.Conflict(msg)
        except sqlalchemy.exc.SQLAlchemyError as e:
            db.session.rollback()
            msg = f"Failed to create role! Error: {e}"
            logging.error(msg)
            raise flaskex.InternalServerError(msg)

    def handle_accesses(self, accesses):
        verified_accesses = {}
        for key, value in accesses.items():
            if not key.endswith("_scope"):
                verified_accesses[key] = value
                scope_key = f"{key}_scope"
                if scope_key in accesses and value == "full" or value == "empty":
                    verified_accesses[scope_key] = None
                elif value == "part":
                    if key.startswith("view"):
                        continue
                    elif not accesses.get(scope_key):
                        raise BadRequest(f"{scope_key} can't be empty when {key} is 'part'.")
                    verified_accesses[scope_key] = json.dumps(accesses[scope_key])
        return verified_accesses

    def update_rbac_role(self, role_id, description, privilege):
        try:
            role = ModelRole.query.filter_by(id=role_id).one()
            role.description = description
            sub_role_names = ModelRole().sub_role_names
            for sub_role_name in sub_role_names:
                sub_role_id = getattr(role, sub_role_name)
                if not sub_role_id:
                    continue
                sub_role_model = model_mapping.get(sub_role_name)
                sub_role = sub_role_model.query.filter_by(id=sub_role_id).one()
                cur_sub_role_access = privilege[sub_role_name]
                verified_accesses = self.handle_accesses(cur_sub_role_access)
                for key, value in verified_accesses.items():
                    setattr(sub_role, key, value if isinstance(value, str) else json.dumps(value))
            db.session.commit()
        except sqlalchemy.exc.SQLAlchemyError as e:
            db.session.rollback()
            logging.error(f"Failed to update role! Error: {e}")
            raise flaskex.InternalServerError(f"Failed to update role! Error: {e}")


# class GroupRoleMapping:
#     def __init__(self, group_id=None, group_name=None):
#         self.group_id = group_id
#         self.group_name = group_name
#         self.group = Group(group_id, group_name)
#         self.group_member_names = self.group.get_all_users_from_ad(group_name)
#
#     # NOT IN USE
#     # def add_mappings_for_group(self, role_ids):
#     #     """
#     #     Assign roles to a group with updating following tables:
#     #     1. `group` if group not existing in table
#     #     2. `users` (add group members and sub-group members)
#     #     3. `group_role_mapping`
#     #     4. `user_group_role_mapping`
#     #     """
#     #     # TODO: will the input param role_ids include existing roles? yes
#     #     try:
#     #         # insert into `group`
#     #         group_model = self.group.create()
#     #         self.group_id = group_model.id
#     #         self.group_name = group_model.group_name
#     #         # insert into `users` and save user_ids for later
#     #         user_ids = User.bulk_create_by_user_names(self.group_member_names)
#     #         # insert into `group_role_mapping`
#     #         self._add_mappings_for_group(role_ids)
#     #         # insert into `user_group_role_mapping`
#     #         self.group.user_group_role_mapping.add_mappings_for_group_users(user_ids, role_ids)
#     #         db.session.commit()
#     #     except sqlalchemy.exc.SQLAlchemyError as e:
#     #         db.session.rollback()
#     #         raise e
#
#     def update_group_role_mapping(self, role_ids):
#         """
#         Update a group's roles by overwrite, update below tables:
#         1. `group` if group not existing in table
#         2. `group_role_mapping`
#         3. `users` (add group members and sub-group members)
#         4. `user_group_role_mapping`
#         """
#         try:
#             # insert into `group`
#             group_model = self.group.create_new_group()
#             self.group_id = group_model.id
#             self.group_name = group_model.group_name
#             role_ids_to_delete, role_ids_to_add = self.filter_roles(role_ids)
#             # delete then add rows in `group_role_mapping`
#             self._delete_group_role_mappings(role_ids_to_delete)
#             self._add_mappings_for_group(role_ids_to_add)
#             # add rows in `users`
#             user_ids = User.bulk_create_by_user_names(self.group.get_all_users_from_ad(self.group_name))
#             # delete then add rows in `user_group_role_mapping`
#             self.group.user_group_role_mapping.delete_by_role_ids(role_ids_to_delete)
#             UserGroupRoleMapping().add_mappings_for_group_users(self.group_id, user_ids, role_ids_to_add)
#             db.session.commit()
#         except sqlalchemy.exc.SQLAlchemyError as e:
#             db.session.rollback()
#             raise e
#
#     def filter_roles(self, role_ids):
#         existing_roles = ModelGroupRoleMapping.query.filter_by(group_id=self.group_id).all()
#         existing_role_ids = [role.role_id for role in existing_roles]
#         role_ids_to_delete = [role_id for role_id in existing_role_ids if role_id not in role_ids]
#         role_ids_to_add = [role_id for role_id in role_ids if role_id not in existing_role_ids]
#         return role_ids_to_delete, role_ids_to_add
#
#     def _add_mappings_for_group(self, role_ids):
#         """add roles for one group, should exclude the existing group_role_mappings"""
#         existing_mappings = ModelGroupRoleMapping.query.filter(
#             and_(ModelGroupRoleMapping.group_id == self.group_id, ModelGroupRoleMapping.role_id.in_(role_ids))
#         ).all()
#         existing_role_ids = [_.role_id for _ in existing_mappings]
#
#         db.session.bulk_save_objects(
#             [
#                 ModelGroupRoleMapping(role_id=role_id, group_id=self.group_id)
#                 for role_id in role_ids
#                 if role_id not in existing_role_ids
#             ]
#         )
#
#     def _delete_group_role_mappings(self, roles_to_delete):
#         # delete(ModelGroupRoleMapping).where(
#         #     and_(ModelGroupRoleMapping.role_id.in_(roles_to_delete), ModelGroupRoleMapping.group_id == self.group_id)
#         # )
#         ModelGroupRoleMapping.query.filter(
#             and_(ModelGroupRoleMapping.role_id.in_(roles_to_delete), ModelGroupRoleMapping.group_id == self.group_id)
#         ).delete()
#
#     def delete(self, role_ids):
#         self._delete_group_role_mappings(role_ids)
#         self.group.user_group_role_mapping.delete_by_role_ids(role_ids)
#         db.session.commit()


class UserGroupRoleMapping:
    def __init__(self, group_id):
        self.group_id = group_id

    @staticmethod
    def get_mapping_list():
        mapping_schema = ModelUserGroupRoleMappingSchema(many=True)
        mapping__list = ModelUserGroupRoleMapping.query.all()
        result = mapping_schema.dump(mapping__list)
        return result

    @staticmethod
    def add_mappings_for_group_users(group_id, user_ids: List, role_ids_to_add):
        for role_id in role_ids_to_add:
            existing_mappings = ModelUserGroupRoleMapping.query.filter(
                and_(
                    ModelUserGroupRoleMapping.group_id == group_id,
                    ModelUserGroupRoleMapping.user_id.in_(user_ids),
                    ModelUserGroupRoleMapping.role_id == role_id
                )
            ).all()
            existing_mappings_user_ids = [_.user_id for _ in existing_mappings]

            db.session.bulk_save_objects(
                [
                    ModelUserGroupRoleMapping(role_id=role_id, user_id=user_id, group_id=group_id)
                    for user_id in user_ids
                    if user_id not in existing_mappings_user_ids
                ]
            )

    def delete_by_role_ids(self, roles_to_delete):
        # delete(ModelUserGroupRoleMapping).where(
        #     and_(
        #       ModelUserGroupRoleMapping.role_id.in_(roles_to_delete),
        #       ModelUserGroupRoleMapping.group_id == self.group_id
        #     )
        # )
        ModelUserGroupRoleMapping.query.filter(
            and_(
                ModelUserGroupRoleMapping.role_id.in_(roles_to_delete),
                ModelUserGroupRoleMapping.group_id == self.group_id
            )
        ).delete()

    @staticmethod
    def delete_user_mappings_by_role_ids(user_id, roles_to_delete):
        ModelUserGroupRoleMapping.query.filter(
            and_(
                ModelUserGroupRoleMapping.role_id.in_(roles_to_delete),
                ModelUserGroupRoleMapping.user_id == user_id
            )
        ).delete()
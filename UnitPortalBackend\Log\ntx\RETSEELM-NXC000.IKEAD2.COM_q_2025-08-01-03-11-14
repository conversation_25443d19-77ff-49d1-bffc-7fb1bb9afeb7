2025-08-01 11:11:16,957 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:11:16,957 INFO Getting the cluster list from PC.
2025-08-01 11:11:16,957 INFO Getting cluster list from ssp-dhd2-ntx.ikead2.com.
2025-08-01 11:11:16,957 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/api/nutanix/v3/clusters/list, method: POST, headers: None
2025-08-01 11:11:16,958 INFO params: None
2025-08-01 11:11:16,958 INFO User: <EMAIL>
2025-08-01 11:11:16,958 INFO payload: {'kind': 'cluster'}
2025-08-01 11:11:16,968 INFO files: None
2025-08-01 11:11:16,968 INFO timeout: None
2025-08-01 11:11:18,956 INFO Got the cluster list from PC, searching the target cluster RETSEELM-NXC000 in the list.
2025-08-01 11:11:18,956 INFO RETSEELM-NXC000 exists in ssp-dhd2-ntx.ikead2.com, continue.
2025-08-01 11:11:22,346 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 11:11:22,346 INFO params: None
2025-08-01 11:11:22,346 INFO User: <EMAIL>
2025-08-01 11:11:22,346 INFO payload: None
2025-08-01 11:11:22,346 INFO files: None
2025-08-01 11:11:22,346 INFO timeout: 30
2025-08-01 11:11:24,209 INFO Getting host list from RETSEELM-NXC000.
2025-08-01 11:11:24,209 INFO Got the host list from RETSEELM-NXC000.
2025-08-01 11:11:24,209 INFO Got the host list.
2025-08-01 11:11:24,209 INFO Getting vault from IKEAD2.
2025-08-01 11:11:24,864 INFO Getting Site_Pe_Nutanix.
2025-08-01 11:11:25,347 INFO Got Site_Pe_Nutanix.
2025-08-01 11:11:25,347 INFO Getting Site_Pe_Admin.
2025-08-01 11:11:25,814 INFO Got Site_Pe_Admin.
2025-08-01 11:11:25,814 INFO Getting Site_Oob.
2025-08-01 11:11:26,339 INFO Got Site_Oob.
2025-08-01 11:11:26,339 INFO Getting Site_Ahv_Nutanix.
2025-08-01 11:11:26,814 INFO Got Site_Ahv_Nutanix.
2025-08-01 11:11:26,814 INFO Getting Site_Ahv_Root.
2025-08-01 11:11:27,303 INFO Got Site_Ahv_Root.
2025-08-01 11:11:27,303 INFO Getting Site_Gw_Priv_Key.
2025-08-01 11:11:27,837 INFO Got Site_Gw_Priv_Key.
2025-08-01 11:11:27,837 INFO Getting Site_Gw_Pub_Key.
2025-08-01 11:11:28,308 INFO Got Site_Gw_Pub_Key.
2025-08-01 11:11:28,308 INFO Getting Site_Pe_Svc.
2025-08-01 11:11:28,776 INFO Got Site_Pe_Svc.
2025-08-01 11:11:33,820 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 11:11:33,821 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-01 11:11:33,821 INFO params: None
2025-08-01 11:11:33,821 INFO User: <EMAIL>
2025-08-01 11:11:33,821 INFO payload: None
2025-08-01 11:11:33,821 INFO files: None
2025-08-01 11:11:33,821 INFO timeout: 30
2025-08-01 11:11:35,645 INFO Got the VM list from RETSEELM-NXC000.
2025-08-01 11:11:46,446 INFO Start to set VM power state to 'acpi_shutdown', VM uuid: 14e51429-9f95-40b2-b00e-bf3e760f7fe4, VM name: retseelm-nxp001-1.
2025-08-01 11:11:46,446 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/14e51429-9f95-40b2-b00e-bf3e760f7fe4/set_power_state, method: POST, headers: None
2025-08-01 11:11:46,446 INFO params: None
2025-08-01 11:11:46,446 INFO User: <EMAIL>
2025-08-01 11:11:46,446 INFO payload: {'transition': 'acpi_shutdown'}
2025-08-01 11:11:46,446 INFO files: None
2025-08-01 11:11:46,446 INFO timeout: 30
2025-08-01 11:11:48,375 INFO Get task status attempting 1/5...
2025-08-01 11:11:48,375 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/e2194a25-497b-4b57-9e3f-ef5958a533ae, method: GET, headers: None
2025-08-01 11:11:48,375 INFO params: None
2025-08-01 11:11:48,375 INFO User: <EMAIL>
2025-08-01 11:11:48,375 INFO payload: None
2025-08-01 11:11:48,375 INFO files: None
2025-08-01 11:11:48,375 INFO timeout: 30
2025-08-01 11:11:50,136 INFO Task status: Succeeded
2025-08-01 11:11:50,136 INFO Successfully set VM power state to 'acpi_shutdown'.
2025-08-01 11:11:51,955 INFO Start to set VM power state to 'acpi_shutdown', VM uuid: 2a14b926-d7c8-4359-b705-a0520dadfe08, VM name: retseelm-nxp001-2.
2025-08-01 11:11:51,955 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/2a14b926-d7c8-4359-b705-a0520dadfe08/set_power_state, method: POST, headers: None
2025-08-01 11:11:51,955 INFO params: None
2025-08-01 11:11:51,955 INFO User: <EMAIL>
2025-08-01 11:11:51,955 INFO payload: {'transition': 'acpi_shutdown'}
2025-08-01 11:11:51,956 INFO files: None
2025-08-01 11:11:51,956 INFO timeout: 30
2025-08-01 11:11:53,704 INFO Get task status attempting 1/5...
2025-08-01 11:11:53,704 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/7976ee91-85d2-4bb2-8427-4bf568a48cd1, method: GET, headers: None
2025-08-01 11:11:53,704 INFO params: None
2025-08-01 11:11:53,704 INFO User: <EMAIL>
2025-08-01 11:11:53,705 INFO payload: None
2025-08-01 11:11:53,705 INFO files: None
2025-08-01 11:11:53,705 INFO timeout: 30
2025-08-01 11:11:55,457 INFO Task status: Succeeded
2025-08-01 11:11:55,457 INFO Successfully set VM power state to 'acpi_shutdown'.
2025-08-01 11:11:55,481 INFO Start to set VM power state to 'acpi_shutdown', VM uuid: 4632716a-540e-400f-ac97-4815d0f7409d, VM name: auto_DND_calm_policy_engine_a0336651-215a-4bd5-bf5b-32724ad53039.
2025-08-01 11:11:55,481 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/4632716a-540e-400f-ac97-4815d0f7409d/set_power_state, method: POST, headers: None
2025-08-01 11:11:55,481 INFO params: None
2025-08-01 11:11:55,481 INFO User: <EMAIL>
2025-08-01 11:11:55,481 INFO payload: {'transition': 'acpi_shutdown'}
2025-08-01 11:11:55,481 INFO files: None
2025-08-01 11:11:55,482 INFO timeout: 30
2025-08-01 11:11:57,198 INFO Get task status attempting 1/5...
2025-08-01 11:11:57,198 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/d899650e-1802-47f1-a475-c2948878ea4b, method: GET, headers: None
2025-08-01 11:11:57,198 INFO params: None
2025-08-01 11:11:57,199 INFO User: <EMAIL>
2025-08-01 11:11:57,199 INFO payload: None
2025-08-01 11:11:57,199 INFO files: None
2025-08-01 11:11:57,199 INFO timeout: 30
2025-08-01 11:11:58,899 INFO Task status: Succeeded
2025-08-01 11:11:58,899 INFO Successfully set VM power state to 'acpi_shutdown'.
2025-08-01 11:11:58,918 INFO Start to set VM power state to 'acpi_shutdown', VM uuid: 9fe928d7-d96d-4e98-93ef-fb009480f4ff, VM name: retseelm-nxp001-3.
2025-08-01 11:11:58,918 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms/9fe928d7-d96d-4e98-93ef-fb009480f4ff/set_power_state, method: POST, headers: None
2025-08-01 11:11:58,918 INFO params: None
2025-08-01 11:11:58,918 INFO User: <EMAIL>
2025-08-01 11:11:58,918 INFO payload: {'transition': 'acpi_shutdown'}
2025-08-01 11:11:58,918 INFO files: None
2025-08-01 11:11:58,918 INFO timeout: 30
2025-08-01 11:12:00,650 INFO Get task status attempting 1/5...
2025-08-01 11:12:00,650 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/tasks/987bcd67-ae60-4446-9894-3e904b3f39b5, method: GET, headers: None
2025-08-01 11:12:00,650 INFO params: None
2025-08-01 11:12:00,650 INFO User: <EMAIL>
2025-08-01 11:12:00,650 INFO payload: None
2025-08-01 11:12:00,650 INFO files: None
2025-08-01 11:12:00,650 INFO timeout: 30
2025-08-01 11:12:02,599 INFO Task status: Succeeded
2025-08-01 11:12:02,600 INFO Successfully set VM power state to 'acpi_shutdown'.
2025-08-01 11:15:02,633 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 11:15:02,633 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-01 11:15:02,633 INFO params: None
2025-08-01 11:15:02,633 INFO User: <EMAIL>
2025-08-01 11:15:02,635 INFO payload: None
2025-08-01 11:15:02,635 INFO files: None
2025-08-01 11:15:02,635 INFO timeout: 30
2025-08-01 11:15:04,533 INFO Got the VM list from RETSEELM-NXC000.
2025-08-01 11:15:04,533 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 11:15:04,533 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-01 11:15:04,533 INFO params: None
2025-08-01 11:15:04,533 INFO User: <EMAIL>
2025-08-01 11:15:04,533 INFO payload: None
2025-08-01 11:15:04,533 INFO files: None
2025-08-01 11:15:04,533 INFO timeout: 30
2025-08-01 11:15:05,845 INFO Got the VM list from RETSEELM-NXC000.
2025-08-01 11:15:05,904 INFO ****************************************************************************************************
2025-08-01 11:15:05,904 INFO *                                                                                                  *
2025-08-01 11:15:05,904 INFO *                                    Shutting down the cluster.                                    *
2025-08-01 11:15:05,905 INFO *                                                                                                  *
2025-08-01 11:15:05,905 INFO ****************************************************************************************************
2025-08-01 11:15:05,905 INFO SSH connecting to the CVM.
2025-08-01 11:15:05,906 INFO SSH connecting to ***********, this is the '1' try.
2025-08-01 11:15:08,764 INFO SSH connected to ***********.
2025-08-01 11:15:09,898 INFO Sending 'cluster stop' to the server.
2025-08-01 11:15:13,908 INFO Receiving the output .
2025-08-01 11:15:13,908 INFO Received the output: #SSH OUTPUT START#.
2025-08-01 11:15:13,908 INFO 

Nutanix Controller VM (CVM) is a virtual storage appliance.



Alteration of the CVM (unless advised by Nutanix Technical Support or

Support Portal Documentation) is unsupported and may result in loss

of User VMs or other data residing on the cluster.



Unsupported alterations may include (but are not limited to):



- Configuration changes / removal of files.

- Installation of third-party software/scripts not approved by Nutanix.

- Installation or upgrade of software packages from non-Nutanix

  sources (using yum, rpm, or similar).



** Notice: SSH will no longer be available in upcoming releases.      **  

** Nutanix Support may access the bash shell on an exceptional basis. **

Last login: Fri Aug  1 05:14:45 CEST 2025 from *********** on ssh

Last login: Fri Aug  1 05:15:08 2025 from **************


cluster stop

[?1h=[H[J[m[?1003l[?1006l[?2004l[1;1H[1;24r[1;1H[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[7m[59] 0:bash*                             "ntnx-cz20240j8s-a-cvm" 05:15 01-Aug-25[m[1;1H[m[?1003l[?1006l[?2004l[1;1H[1;24r[1;1H[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[K

[7m[59] 0:bash*                             "ntnx-cz20240j8s-a-cvm" 05:15 01-Aug-25[m[1;1H

Nutanix Controller VM (CVM) is a virtual storage appliance.[4;1HAlteration of the CVM (unless advised by Nutanix Technical Support or

Support Portal Documentation) is unsupported and may result in loss

of User VMs or other data residing on the cluster.[8;1HUnsupported alterations may include (but are not limited to):[10;1H- Configuration changes / removal of files.

- Installation of third-party software/scripts not approved by Nutanix.

- Installation or upgrade of software packages from non-Nutanix

  sources (using yum, rpm, or similar).[15;1H** Notice: SSH will no longer be available in upcoming releases.      **  

** Nutanix Support may access the bash shell on an exceptional basis. **[18;1HYou are running inside a tmux session[20;1HOpen sessions:

[1;23r[23;80H




[16;1H1: 1 windows (created Tue Jul 29 11:37:09 2025) [80x23]

10: 1 windows (created Wed Jul 30 03:56:47 2025) [80x23]

11: 1 windows (created Wed Jul 30 03:57:20 2025) [80x23]

12: 1 windows (created Wed Jul 30 04:03:44 2025) [80x23]

13: 1 windows (created Wed Jul 30 04:14:31 2025) [80x23]

14: 1 windows (created Wed Jul 30 04:19:31 2025) [144x50]

15: 1 windows (created Wed Jul 30 04:26:02 2025) [80x23][1;24r[23;1H[1;23r[23;80H








[14;1H16: 1 windows (created Wed Jul 30 04:33:12 2025) [80x23]

17: 1 windows (created Wed Jul 30 04:33:45 2025) [80x23]

18: 1 windows (created Wed Jul 30 04:43:47 2025) [80x23]

19: 1 windows (created Wed Jul 30 04:44:20 2025) [80x23]

2: 1 windows (created Tue Jul 29 11:37:41 2025) [80x23]

20: 1 windows (created Wed Jul 30 04:47:54 2025) [80x23]

21: 1 windows (created Wed Jul 30 04:51:56 2025) [80x23]

22: 1 windows (created Wed Jul 30 04:52:30 2025) [80x23]

23: 1 windows (created Wed Jul 30 04:56:44 2025) [80x23][1;24r[23;1H[1;23r[23;80H









[13;1H24: 1 windows (created Wed Jul 30 04:57:46 2025) [144x50]

25: 1 windows (created Wed Jul 30 05:01:36 2025) [80x23]

26: 1 windows (created Wed Jul 30 05:02:09 2025) [80x23]

27: 1 windows (created Wed Jul 30 05:07:41 2025) [80x23]

28: 1 windows (created Wed Jul 30 05:09:45 2025) [144x50]

29: 1 windows (created Wed Jul 30 05:19:40 2025) [80x23]

3: 1 windows (created Tue Jul 29 11:43:47 2025) [209x49]

30: 1 windows (created Wed Jul 30 05:20:13 2025) [80x23]

31: 1 windows (created Wed Jul 30 05:24:43 2025) [80x23]

32: 1 windows (created Wed Jul 30 05:29:23 2025) [144x50][1;24r[23;1H[1;23r[23;80H






[16;1H33: 1 windows (created Wed Jul 30 05:31:59 2025) [80x23]

34: 1 windows (created Wed Jul 30 05:32:38 2025) [80x23]

35: 1 windows (created Wed Jul 30 05:44:46 2025) [80x23]

36: 1 windows (created Wed Jul 30 05:46:10 2025) [80x23]

37: 1 windows (created Wed Jul 30 05:48:20 2025) [80x23]

38: 1 windows (created Wed Jul 30 05:50:09 2025) [80x23]

39: 1 windows (created Wed Jul 30 05:56:14 2025) [80x23][1;24r[23;1H[1;23r[23;80H






[16;1H4: 1 windows (created Tue Jul 29 11:44:30 2025) [80x23]

40: 1 windows (created Wed Jul 30 05:57:16 2025) [80x23]

41: 1 windows (created Wed Jul 30 06:05:29 2025) [80x23]

42: 1 windows (created Wed Jul 30 06:06:36 2025) [80x23]

43: 1 windows (created Wed Jul 30 06:09:15 2025) [80x23]

44: 1 windows (created Wed Jul 30 07:05:27 2025) [80x23]

45: 1 windows (created Wed Jul 30 07:06:34 2025) [80x23][1;24r[23;1H[1;23r[23;80H






[16;1H46: 1 windows (created Wed Jul 30 07:09:38 2025) [80x23]

47: 1 windows (created Wed Jul 30 07:10:20 2025) [80x23]

48: 1 windows (created Wed Jul 30 07:14:45 2025) [80x23]

49: 1 windows (created Wed Jul 30 07:15:52 2025) [80x23]

5: 1 windows (created Tue Jul 29 11:48:42 2025) [80x23]

50: 1 windows (created Wed Jul 30 07:17:49 2025) [80x23]

51: 1 windows (created Wed Jul 30 07:19:14 2025) [80x23][1;24r[23;1H[1;23r[23;80H






[16;1H52: 1 windows (created Wed Jul 30 07:20:25 2025) [80x23]

53: 1 windows (created Wed Jul 30 07:23:29 2025) [80x23]

54: 1 windows (created Thu Jul 31 04:07:05 2025) [80x23]

55: 1 windows (created Thu Jul 31 04:07:38 2025) [80x23]

56: 1 windows (created Thu Jul 31 04:10:46 2025) [80x23]

57: 1 windows (created Thu Jul 31 04:11:40 2025) [80x23]

58: 1 windows (created Thu Jul 31 04:13:06 2025) [80x23][1;24r[23;1H[1;23r[23;80H




[18;1H59: 1 windows (created Fri Aug  1 05:15:10 2025) [80x23] (attached)

6: 1 windows (created Tue Jul 29 11:50:39 2025) [80x23]

7: 1 windows (created Tue Jul 29 20:06:16 2025) [80x23]

8: 1 windows (created Tue Jul 29 20:06:19 2025) [80x23]

9: 1 windows (created Tue Jul 29 20:08:40 2025) [80x23][1;24r[23;1H[1;23r[23;80H






[17;1HTo attach to other sessions, run 'tmux switchc -t num' where num is the session number

To list existing sessions, run 'tmux ls'

To terminate sessions, run 'tmux kill-session -t num' where num is the session number

Refer to tmux documentation for more information and commands.[1;24r[23;1Hnutanix@NTNX-CZ20240J8S-A-CVM:***********:~$ 
2025-08-01 11:15:13,909 INFO #SSH OUTPUT END#
2025-08-01 11:15:13,909 ERROR We didn't get the correct output from 'cluster stop', abort.

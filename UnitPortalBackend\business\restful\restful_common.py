import os.path
from flask import abort, request, send_file
from flask_restful import Resource
from flask_apispec import doc
from business.generic.commonfunc import get_request_token
from business.authentication.authentication import User
from static.SETTINGS import ALLOWED_DOWNLOAD_PATH_LIST
from .route import route


class RestfulPMLog(Resource):     # need to optimize.
    def post(self):
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res , role = usr.get_role_by_token(token)
            if res:
                _username = role.username # ignore
            else:
                raise Exception({'code': 401, 'message': 'Username not found by the token.'}) # if cann't get the username, then quit
            param = request.get_json(force=True)
            filepath = param['filepath']
            if not filepath:
                raise Exception({'code': 400, 'message': 'Cannot download the log file due to lack of file path.'})
            return send_file(filepath)
        except Exception as e:
            if e.args:
                if type(e.args[0]) == dict:
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/download', swagger=False)
class RestfulDownload(Resource):
    @doc(description="Download logs", tags=['View Resources'])
    def post(self):
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res , role = usr.get_role_by_token(token)
            if res:
                _username = role.username # ignore
            else: 
                raise Exception({'code': 401, 'message': 'Username not found by the token.'}) # if cann't get the username, then quit
            param = request.get_json(force=True)
            filepath = param['filepath']
            if not filepath:
                raise Exception({'code': 400, 'message': 'Cannot download the log file due to lack of file path.'})
            filepath = os.path.normpath(filepath) # Normalize the file path to 'xx\\xx\\' format
            filepath = os.path.abspath(filepath) # Convert to absolute path instead of 'xx\\..\\xx' format
            if os.path.isdir(filepath):
                raise Exception({'code': 400, 'message': f'{filepath} is a directory'})
            dir_name = os.path.dirname(filepath) # Get the parent directory of the file needed to download
            dir_name = os.path.normcase(dir_name) #Convert upper cases to lower
            if dir_name not in ALLOWED_DOWNLOAD_PATH_LIST:
                raise Exception({'code': 400, 'message': f'It is not allowed to download from {dir_name}'})
            if not os.path.isfile(filepath): # Validate if the path points to an exist file
                raise Exception({'code': 400, 'message': f'The file {filepath} does not exist'})
            return send_file(filepath)
        except Exception as e:
            if e.args:
                if type(e.args[0]) == dict:
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))
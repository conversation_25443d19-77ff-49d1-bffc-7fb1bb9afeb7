<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="5" >
        <el-col :span="4"  :offset="12" >
          <el-select    size="large"
            v-model="filter.selected_pc" multiple collapse-tags placeholder="Filter the Cluster" style="width:100%;" >

            <el-option v-for="item in filter.pc_list" :key="item" :label="item" :value="item" style="font-size: large;"/>

          </el-select>
        </el-col>
        <el-col :span="4" >
          <el-input v-model="filter.fuzzy_string" placeholder="Fuzzy search, eg: SE " @keyup.enter.native="filter_host_list" size="large"/>
        </el-col>
        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item"  type="success" size="large" @click="download_host_list">
            Download
          </el-button>
        </el-col>
        <el-col :span="2" style='float:right;'>
          <el-button style='float:right;width:100%' class="filter-item"  type="primary" size="large" @click="filter_host_list">
            Search
          </el-button>
        </el-col>

       
      
    </el-row>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="current_list" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column label="ID" prop="id" sortable="custom" align="center" min-width="4%" >
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Host Name" class-name="status-col" min-width="10%" align="center" sortable="custom" prop="name">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.name.toUpperCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Cluster" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="parent">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.parent.toUpperCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Version" min-width="3%" align="center" sortable="custom" prop="version">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.version.toUpperCase() }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Build." min-width="4%" align="center" sortable="custom" prop="build">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.build }}</span>
        </template>
      </el-table-column>

      <el-table-column label="CPU" align="center" min-width="2%" sortable="custom" prop="num_cpu">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.num_cpu }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Memory" align="center" min-width="3%" sortable="custom" prop="memory_total_gb">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.memory_total_gb}}</span>
        </template>
      </el-table-column>

      <el-table-column label="Model" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="model" >
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.model }}</span>
        </template>
      </el-table-column>
      <el-table-column label="SN" class-name="status-col" min-width="6%" align="center" sortable="custom" prop="serialnumber" >
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.serialnumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Processor_Type" min-width="12%" align="center" sortable="custom" prop="processor_type">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.processor_type }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="set_page" /> 


  </div>
</template>

<script>
import {GetSliHostsList} from '@/api/simplivity'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  name: 'PETable',
  components: { Pagination },
  directives: { waves },
  filters: {

  },
  data() {
    const validateTime =(rule, value, callback)=>{
      if(this.temp.datatimepickerdisabled){
        callback()
      }
      let currentdate = new Date()
      let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
      if (value < utctime){
        callback(new Error('Schedule date must be later then now.'))
      }else{
        let currnettime = utctime.getTime()
        let scheduletime = value.getTime()
        let timediff = scheduletime-currnettime
        if(timediff/1000/60 < 5){
          callback(new Error('Schedule date is too close from now.'))
        }else{
          callback()
        }
      }
      callback()
    }
    return {
      tableKey: 0,
      all_pe_list: null,
      filtered_list: null,
      current_list: null,
      page_list: null,
      filter:{
        pc_list:[],
        selected_pc:[],
        fuzzy_string:"",
      },
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: '',
        prism: '',
        status: '',
        sort: '+id'
      },
      sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
      // statusOptions: ['published', 'draft', 'deleted'],
      ShowCreationDate: false,
      temp: {
        id: '',
        timestamp: new Date(),
        cluster:'',
        prism: '',
        status: '',
        startnow: 1 ,
        datatimepickerdisabled:false,
        description: '',
        pmtype: 1
      },
      selectedrow:'',
      dialogFormVisible: false,
      dialogStatus: '',
      dialogPvVisible: false,
      logdata: [],
      rules: {
        prism: [{ required: true, message: 'prism is required', trigger: 'change' }],
        cluster: [{ required: true, message: 'cluster is required', trigger: 'change' }],
        timestamp: [{ type: 'date', required: true , trigger: 'change' , validator:validateTime}]
      }
    }
  },
  computed: {
    total() {
      if(this.filtered_list){
        return this.filtered_list.length
      }
      else{
          return 0
      }
    }
  },
  created() {
    this.get_pe_list()
  },
  methods: {
    get_pe_list() {
      this.listLoading = true
      GetSliHostsList(this.$store.getters.token).then(response => {
        this.all_pe_list = response.data
        this.filtered_list = this.all_pe_list
        let page = this.listQuery.page
        let limit = this.listQuery.limit
        let start , end
        if(page*limit>=this.total){
          start = (page-1)*limit
          end = this.total
        }
        else{
          start = (page-1)*limit
          end = page * limit
        }
        this.current_list = this.filtered_list.slice(start,end)
        this.listLoading = false
        let all_prism_list = this.all_pe_list.map((obj,index)=>{return obj['parent']})
        this.filter.pc_list = this.remove_duplicate(all_prism_list)
      })
    },
    remove_duplicate(arr) {
      //去除重复值
      const newArr = []
      arr.forEach(item => {
        if (!newArr.includes(item)) {
          newArr.push(item)
        }
      })
      return newArr
    },
    set_page(){
      // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start,end)
    },
    filter_host_list(){
      //根据过滤条件筛选表格显示内容
      //screen the table as per filters
      this.listQuery.page = 1
      let temp_list
      //filter selected pc first.
      if (this.filter.selected_pc.length){
        //No filter, so select all
        temp_list = this.all_pe_list.filter((item)=>{
          return this.filter.selected_pc.includes(item['parent'].toLowerCase())
        })
        this.filtered_list = temp_list
      }
      else{
        this.filtered_list = this.all_pe_list
      }
      if(this.filter.fuzzy_string.trim().length){
        let temp_list = this.filtered_list
        let fuzzy_list = this.filter.fuzzy_string.trim().split(/\s+/)
          for(let fuzzy of fuzzy_list){
            fuzzy = fuzzy.toString().toLowerCase()
            temp_list = temp_list.filter((k)=>{
            if( k.id.toString().toLowerCase().search(fuzzy)!= -1
                || k.name.toLowerCase().search(fuzzy) != -1
                || k.parent.toLowerCase().search(fuzzy) != -1
                || k.version.toString().toLowerCase().search(fuzzy) != -1
                || k.build.toString().toLowerCase().search(fuzzy) != -1
                || k.memory_total_gb.toLowerCase().search(fuzzy) != -1
                || k.num_cpu.toLowerCase().search(fuzzy) != -1
                || k.model.toString().toLowerCase().search(fuzzy) != -1
                || k.processor_type.toString().toLowerCase().search(fuzzy) != -1
                || k.serialnumber.toString().toLowerCase().search(fuzzy) != -1
            ){
              return true
            }
          })
        }
        this.filtered_list = temp_list
      }
      this.set_page()
    },
    handleFilter() {
      this.listQuery.page = 1
    },
    sortChange(data) {
      const { prop, order } = data
      if(order==null){
        this.sortChange({prop:'id',order:'ascending'})
        return 
      }
      let flag_num = order=="ascending" ? 1 : -1
      this.filtered_list.sort((item1,item2)=>(
        (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
      ))
      this.set_page()
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    download_host_list(){
      GetSliHostsList(this.$store.getters.token,  true)
      .then((response)=>{
        console.log(response)
        const href = URL.createObjectURL(response.data);
        // create "a" HTML element with href to file & click
        const link = document.createElement('a');
        link.href = href;
        link.setAttribute('download', "host_list"); //or any other extension
        document.body.appendChild(link);
        link.click();
        // clean up "a" element & remove ObjectURL
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      })
    },
  }
}
</script>
<style lang="scss" scoped>
    .bigger_font {
      font-size: 16px;
    }
</style>
import json
from flask_restful import Resource
from flask import abort, request, Response

from business.generic.commonfunc import  get_request_token
from business.distributedhosting.upwidgets import ConsumerFeedback, WebPageTracer, ApiUtilization, \
    WebPageTracer, ApiUtilizationLog, TasksCounter
from .route import route


@route('/api/v1/upwidgets/feedback')
class RestfulComments(Resource):
    def post(self):
        try:
            param = request.get_json(force=True)
            token = get_request_token()
            _comment = ConsumerFeedback(token=token)
            star = param['star']
            comment = param['comment']
            anonymous = param['anonymous']
            _comment.creat_feedback_record(comment, star, anonymous)
            res = {'roles': 'Comments add successfully !'}
            return Response(json.dumps(res), status=200, mimetype='application/json')
            # raise Exception("Failed to add comments.")
        except Exception as e:
            abort(500, f'Failed to add comments, error:{str(e)}')

    def put(self):
        try:
            param = request.get_json(force=True)
            token = get_request_token()
            _comment = ConsumerFeedback(token=token)
            print(param)
            # print(**param)
            # return
            _comment.update_comments(param)
            res = {'roles': 'Comments add successfully !'}
            return Response(json.dumps(res), status=200, mimetype='application/json')
            # raise Exception("Failed to add comments.")
        except Exception as e:
            abort(500, f'Failed to add comments, error:{str(e)}')

    def get(self):
        try:
            token = get_request_token()
            _comment = ConsumerFeedback(token=token)
            if comment_list := _comment.get_comments():
                return comment_list
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/upwidgets/webpage')
class RestfulWebPageTracer(Resource):
    def post(self):
        try:
            param = request.get_json(force=True)
            pagename = param['pagename']
            token = get_request_token()
            pagetracer = WebPageTracer(token=token)
            if _pagetracer := pagetracer.webpagecounter(pagename):
                return _pagetracer
        except Exception:
            abort(500, "Internal error")

    def get(self):
        try:
            starttime = request.args.get('starttime', type=str)
            endtime = request.args.get('endtime', type=str)
            token = get_request_token()
            pagelist = WebPageTracer(token=token)
            if page_list := pagelist.get_webcount_list(starttime=starttime, endtime=endtime ):
                return page_list
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/upwidgets/taskscounter')
class RestfulTasksCounter(Resource):
    def get(self):
        try:
            starttime = request.args.get('starttime', type=str)
            endtime = request.args.get('endtime', type=str)
            token = get_request_token()
            taskcounter = TasksCounter(token=token)
            if _apiuti := taskcounter.task_counter(starttime=starttime, endtime=endtime):
                return _apiuti
        except Exception:
            abort(500, "Internal error")


@route('/api/v1/upwidgets/apiutilization')
class RestfulAPIUtilizationList(Resource):
    def get(self):
        try:
            token = get_request_token()
            apiuti = ApiUtilization(token=token)
            if _apiuti := apiuti.get_apiutil_list():
                return _apiuti
        except Exception:
            abort(500, "Internal error")
            

@route('/api/v1/upwidgets/apiutilizationlog')
class RestfulAPIUtilizationLogList(Resource):
    def get(self):
        try:
            starttime = request.args.get('starttime', type=str)
            endtime = request.args.get('endtime', type=str)
            ismore = request.args.get('ismore', type=bool)
            token = get_request_token()
            apiuti = ApiUtilizationLog(token=token)
            if _apiuti := apiuti.get_apiutillog_list( starttime=starttime, endtime=endtime, ismore=ismore):
                return _apiuti
        except Exception:
            abort(500, "Internal error")


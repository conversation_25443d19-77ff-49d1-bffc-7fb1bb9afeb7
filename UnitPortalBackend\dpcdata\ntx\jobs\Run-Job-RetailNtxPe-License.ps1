$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Launch-Job(){
    Begin {
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Load data from the table dh_retail_ntx_pe
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $LicensedCategory = @("Starter", "Pro", "Ultimate")
            $LicenseCategory  = $LicensedCategory + "No_License"
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhPEs.Count)' PEs need to update" -DumpFile $DumpFile
        #Rolling call PEs exist in the table
        $DhPEs | Foreach-Object -ThrottleLimit 50 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $PE         = $_
            $UpdateDict = $using:CollectionUpdate
            $PC         = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for '$($PE.name)'" -DumpFile $DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            #Construct a data model which stores data needs to be updated into the table
            $LicenseMap = [PSCustomObject]@{
                'fqdn'                   = $PE.fqdn
                'license_category'       = "Unknown"
                'license_class'          = "Unknown"
                'license_cores_capacity' = "0"
                'license_flash_capacity' = "0 Tib"
                'license_hdd_capacity'   = "0 Tib"
                'license_cores_licensed' = "0"
                'license_flash_licensed' = "0 Tib"
                'license_hdd_licensed'   = "0 Tib"
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Calling '$($PE.fqdn)' for getting license details" -DumpFile $using:DumpFile
            if ($PrismCall1 = Rest-Prism-v1-Get-License -Fqdn $PE.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Got the license details of '$($PE.fqdn)'" -DumpFile $using:DumpFile
                if ($PrismCall1.licenseDTO) {
                    $LicenseMap.license_category       =      $PrismCall1.licenseDTO.category
                    $LicenseMap.license_class          =      $PrismCall1.licenseDTO.licenseClass
                    $LicenseMap.license_cores_capacity = "" + [int]$PrismCall1.licenseDTO.capacity.num_cores
                    $LicenseMap.license_flash_capacity = "" + [double]$PrismCall1.licenseDTO.capacity.ssd_size_tebibytes + " Tib"
                    $LicenseMap.license_hdd_capacity   = "" + [double]$PrismCall1.licenseDTO.capacity.hdd_size_tebibytes + " Tib"
                    $LicenseMap.license_cores_licensed = "" + [int]$PrismCall1.licenseDTO.capacityLicensed.num_cores
                    $LicenseMap.license_flash_licensed = "" + [double]$PrismCall1.licenseDTO.capacityLicensed.ssd_size_tebibytes + " Tib"
                    $LicenseMap.license_hdd_licensed   = "" + [double]$PrismCall1.licenseDTO.capacityLicensed.hdd_size_tebibytes + " Tib"
                }else {
                    $LicenseMap.license_category       =      $PrismCall1.category
                    $LicenseMap.license_class          =      $PrismCall1.licenseClass
                    $LicenseMap.license_cores_capacity = "" + [int]$PrismCall1.capacity.num_cores
                    $LicenseMap.license_flash_capacity = "" + [double]$PrismCall1.capacity.ssd_size_tebibytes + " Tib"
                    $LicenseMap.license_hdd_capacity   = "" + [double]$PrismCall1.capacity.hdd_size_tebibytes + " Tib"
                    $LicenseMap.license_cores_licensed = "" + [int]$PrismCall1.capacityLicensed.num_cores
                    $LicenseMap.license_flash_licensed = "" + [double]$PrismCall1.capacityLicensed.ssd_size_tebibytes + " Tib"
                    $LicenseMap.license_hdd_licensed   = "" + [double]$PrismCall1.capacityLicensed.hdd_size_tebibytes + " Tib"
                }
            }else {
                Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to get the license details of '$($PE.fqdn)'" -DumpFile $using:DumpFile
                if ($PE.license_category.ToString() -in $using:LicenseCategory) {
                    Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to fetching license details of '$($PE.fqdn)' in present, it was available what is '$($PE.license_category)', we will keep instead of update it" -DumpFile $using:DumpFile
                    return
                }
            }
            $UpdateDict.Add($LicenseMap)
        } -UseNewRunspace
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pe]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxPe-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job
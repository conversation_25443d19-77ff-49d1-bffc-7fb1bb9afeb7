from flask import Flask
import logging
from business.generic.commonfunc import DBConfig
from models.models import db
from collector.collector import Collector<PERSON>un<PERSON>


def scan_collector_task():

    app = Flask(__name__)
    # CollectorRunner will use db operations and SQLAchemy needs to know how to connect to the database.
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    db.init_app(app)
    logging.info("Scaning collector Sync tasks............")
    with app.app_context(): 
        collect = CollectorRunner()
        collect.collect()
        db.session.remove()
        db.engine.dispose()
        db.session.close()
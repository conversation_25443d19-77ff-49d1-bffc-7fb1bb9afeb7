function Connect-NtnxSsh(){
    param (
        [string] [ValidateSet("PROD", "PPE", "DT", "D2")]                                    $Tier = "PROD",
        [string] [Parameter(ParameterSetName = 'Cluster')]                                   $Site,
        [string] [ValidateSet("NXC000", "NXC001", 
                              "NXC002", "NXC003")] [Parameter(ParameterSetName = 'Cluster')] $NXC = "NXC000",
        [string] [Parameter(ParameterSetName = 'Prism')]                                     $Prism,
        [string]                                                                             $CVM
    )
    Begin {
        if (!$Prism) {
            $Prism = $Site + "-" + $NXC
        }
        $Endpoint = if (!$CVM){
            $Prism
        } else {
            $Site + "-NX" + $CVM + "cvm"
        }
        if (!(Test-Connection -ResolveDestination $Endpoint)) {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "$Endpoint is unresolvable"
            return
        }
        # $KnownHosts = "$($ENV:USERPROFILE)\.ssh\known_hosts"
        $PrivKeyFile = $Global:Init + "/$(New-Guid)"
    }
    Process {
        if ($Prism -match "^DS") {
            $Unit = "WiaB"
        } else {
            $Unit = "SiaB"
        }
        
        $PrivKey = & $("Get-SecretFor" + $Unit) -Tier $Tier -Prism $Prism -Secret Site_Gw_Priv_Key -Display:$true
        if ($PrivKey){
            Set-Content -Path $PrivKeyFile -Value $PrivKey.Password -Force
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Launch SSH session to $Endpoint"
            if ($IsWindows) {
                Start-Process -FilePath "pwsh.exe" -ArgumentList "-NoExit", "-Command", "ssh.exe -i $($PrivKeyFile) nutanix@$Endpoint"
            } else {
                Invoke-Expression "chmod 600 $($PrivKeyFile)"
                Invoke-Expression "ssh -i $($PrivKeyFile) nutanix@$($Endpoint)"
            }
            Start-Sleep(15)
            # Remove-Item -Path $PrivKeyFile -Force
        }
    }
    End {
        return   
    }
}
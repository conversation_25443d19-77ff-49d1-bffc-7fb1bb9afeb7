2025-08-01 11:28:51,540 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 11:28:51,540 INFO params: None
2025-08-01 11:28:51,540 INFO User: <EMAIL>
2025-08-01 11:28:51,540 INFO payload: None
2025-08-01 11:28:51,540 INFO files: None
2025-08-01 11:28:51,540 INFO timeout: 30
2025-08-01 11:29:12,615 WARNING Call api has exception: HTTPSConnectionPool(host='retseelm-nxc000.ikead2.com', port=9440): Max retries exceeded with url: /PrismGateway/services/rest/v1/hosts (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000138E3459F90>, 'Connection to retseelm-nxc000.ikead2.com timed out. (connect timeout=30)'))
2025-08-01 11:29:12,625 WARNING Call api failed, going to do the 2 retry...
2025-08-01 11:29:33,654 WARNING Call api has exception: HTTPSConnectionPool(host='retseelm-nxc000.ikead2.com', port=9440): Max retries exceeded with url: /PrismGateway/services/rest/v1/hosts (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000138E357F590>, 'Connection to retseelm-nxc000.ikead2.com timed out. (connect timeout=30)'))
2025-08-01 11:29:33,654 WARNING Call api failed, going to do the 3 retry...
2025-08-01 11:29:54,729 WARNING Call api has exception: HTTPSConnectionPool(host='retseelm-nxc000.ikead2.com', port=9440): Max retries exceeded with url: /PrismGateway/services/rest/v1/hosts (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000138E35893D0>, 'Connection to retseelm-nxc000.ikead2.com timed out. (connect timeout=30)'))
2025-08-01 11:29:54,729 WARNING Call api failed, going to do the 4 retry...
2025-08-01 11:30:15,781 WARNING Call api has exception: HTTPSConnectionPool(host='retseelm-nxc000.ikead2.com', port=9440): Max retries exceeded with url: /PrismGateway/services/rest/v1/hosts (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000138E358B150>, 'Connection to retseelm-nxc000.ikead2.com timed out. (connect timeout=30)'))
2025-08-01 11:30:15,781 WARNING Call api failed, going to do the 5 retry...
2025-08-01 11:30:36,826 WARNING Call api has exception: HTTPSConnectionPool(host='retseelm-nxc000.ikead2.com', port=9440): Max retries exceeded with url: /PrismGateway/services/rest/v1/hosts (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000138E358CE10>, 'Connection to retseelm-nxc000.ikead2.com timed out. (connect timeout=30)'))
2025-08-01 11:30:36,826 WARNING Call api failed, going to do the 6 retry...
2025-08-01 11:30:36,826 ERROR Call api still failed after 6 retries...
2025-08-01 11:30:36,826 INFO Getting host list from RETSEELM-NXC000.
2025-08-01 11:30:38,075 INFO Start to renew authentication of PE.
2025-08-01 11:30:39,732 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/authconfig/directories, method: GET, headers: None
2025-08-01 11:30:39,732 INFO params: None
2025-08-01 11:30:39,732 INFO User: admin
2025-08-01 11:30:39,734 INFO payload: None
2025-08-01 11:30:39,734 INFO files: None
2025-08-01 11:30:39,734 INFO timeout: None
2025-08-01 11:31:00,778 WARNING Call api has exception: HTTPSConnectionPool(host='retseelm-nxc000.ikead2.com', port=9440): Max retries exceeded with url: /PrismGateway/services/rest/v1/authconfig/directories (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000138E3DA3B90>, 'Connection to retseelm-nxc000.ikead2.com timed out. (connect timeout=None)'))
2025-08-01 11:31:00,778 WARNING Call api failed, going to do the 2 retry...

import re
import smtplib
import socket
import time
from email.mime.text import MIMEText

from business.authentication.authentication import Vault, ServiceAccount
from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.nutanix import PrismElement, NutanixOperation, NutanixCLI, VM_Check
from business.distributedhosting.nutanix.workload.workload_exceptions import Tower<PERSON>obFailed, TowerJobFailedPipDbPG14, \
    TowerJobFailedDb, ThorsHammerException
from business.generic.thors_hammer_api import ThorsHammerAPI
from business.generic.base_up_exception import SSHFailed, VaultGetSecretFailed
from business.generic.commonfunc import SSHConnect, split_pe_into_parts, test_pinging_by_output
from business.generic.ipam import Ipam
from business.generic.ipam_api import IpamAPI
from business.generic.linux import Tower, Ansible
from business.loggings.loggings import IntegratedLogger
from models.ntx_models import ModelPrismCentral
from models.ntx_models_wh import ModelWarehousePrismCentral
from static.WORKLOAD_SPEC import WorkloadSpec
import static.SETTINGS as SETTING


class AppPackage:
    def __init__(self, vm_spec, ntx_sa, logger, db_logger, domain, facility_type):
        self.vm_spec = vm_spec
        self.ntx_sa = ntx_sa
        self.logger = logger
        self.db_logger = db_logger
        self.ilg = IntegratedLogger(file_lg=self.logger, db_lg=db_logger)
        self.domain = domain
        _sa = ServiceAccount(usage="nutanix_pm").get_service_account()
        self.ipam_api = IpamAPI(username=_sa['username'], password=_sa['password'], logger=logger)
        self.thors_hammer = ThorsHammerAPI(logger=self.logger)
        self.ansible = Ansible(logger=self.logger)
        self.pe_api_handler = PrismElement(vm_spec[WorkloadSpec.PE], ntx_sa, self.logger)
        self.packages = self.vm_spec[WorkloadSpec.PACKAGES]
        self.vm_uuid = self.vm_spec[WorkloadSpec.VM_UUID]
        self.vm_fqdn = f"{self.vm_spec[WorkloadSpec.VM_NAME].lower()}.{self.domain}"
        self.facility_type = facility_type

    def _get_pe_secret(self):
        pe_name = NutanixOperation.get_pe_by_fqdn_from_db(fqdn=self.vm_spec[WorkloadSpec.PE]).name
        self.logger.info(f"Facility type: {self.facility_type}")
        if self.facility_type == FacilityType.RETAIL:
            pc_info = ModelPrismCentral.query.filter_by(fqdn=self.vm_spec[WorkloadSpec.PC]).one()
            vault = Vault(tier=pc_info.tier)
        elif self.facility_type == FacilityType.WAREHOUSE:
            pc_info = ModelWarehousePrismCentral.query.filter_by(fqdn=self.vm_spec[WorkloadSpec.PC]).one()
            vault_sa = ServiceAccount(usage="vault_warehouse").get_service_account()
            vault = Vault(tier=pc_info.tier, sa=vault_sa)
        label = f"{pe_name.upper()}/{SETTING.PE_VAULT_LABEL['cvm_nutanix']}"
        res, secret_info = vault.get_secret(label)
        if not res:
            raise VaultGetSecretFailed(label)
        return secret_info["username"], secret_info["secret"]

    @classmethod
    @property
    def supported_packages(cls):
        return [f for f in dir(cls) if not f.startswith('_') and not f.endswith('_packages')]

    def install_packages(self):
        for p in self.packages:
            if not p:
                continue
            f = getattr(self, p)
            self.logger.title(f"Executing Application Add-on Packages, type: '{p}")
            f()
            self.logger.info(f"App Package deployment '{p}' finished")

    def fsol(self):
        site_code = self.vm_spec[WorkloadSpec.VM_NAME].split("-")[0]        # site_code: RETSE999
        machine_name = self.vm_spec[WorkloadSpec.VM_NAME]
        self.logger.info("Thorshammer Site Prep 'FSOL'")
        self.thors_hammer.make_profile(site_code, "FSOL")
        self.logger.info(f"Thorshammer work done on '{machine_name}'")
        self.logger.info("Assigning Profiles to FSOL / NT0001")
        profiles = [
            f"Alias ICC6_{machine_name}",
            "App ICC6_FSOL",
            "Prt ICC6_FSOL",
            "Alias SET_INSTALL_TIME_FRAME"
        ]
        for p in profiles:
            self.thors_hammer.add_host_profile(machine_name, p)
        subscriptions = [
            "Cylance Optics",
            "WDS - 2016 deployment Servers",
            "WDS - 2016 Drivers",
            "Splunkforwarder",
        ]
        for s in subscriptions:
            self.thors_hammer.add_host_subscription(machine_name, s)
        self.logger.info("Creating CNAME for FSOL")
        dns_domain = self._get_dns_domain(self.vm_spec[WorkloadSpec.IP_ID])
        cnames = [f"{machine_name}.om.{dns_domain}"]
        self._add_cnames(cnames)

    def lip_1(self):
        app_name = "LIP App 1"
        group_name = "LIP"
        self._add_vm_to_affinity_group(app_name, group_name)

    def lip_2(self):
        def _get_store_code(bu_code):
            bu_code_length = len(bu_code)
            if bu_code == "SO":
                store_code = bu_code
            elif bu_code_length == 3:
                self.logger.info("Converting 3 digit site code to 5 for LIP names")
                store_code = f"00{bu_code}"
            elif bu_code_length == 4:
                self.logger.info("Converting 4 digit site code to 5 for LIP names")
                store_code = f"0{bu_code}"
            elif bu_code_length == 5:
                self.logger.info("5 Digit site code, nice no changes needed")
                store_code = bu_code
            else:
                self.logger.error(f"Arrg Site codes with length '{bu_code_length}' is not supported for LIP")
            return store_code

        app_name = "LIP App 2"
        group_name = "LIP"
        self._add_vm_to_affinity_group(app_name, group_name)
        self.logger.info(f"Creating Additional IPAM records '{app_name}'")
        _, country_code, bu_code = split_pe_into_parts(self.vm_spec[WorkloadSpec.VM_NAME])
        store_code = _get_store_code(bu_code)
        dns_domain = self._get_dns_domain(self.vm_spec[WorkloadSpec.IP_ID])
        if bu_code != "SO":
            site_type = "STO"
            segments = ["", "-a01", "-m01", "-m02"]
            for segment in segments:
                fqdn = f"lipp-12c2-{country_code}-{site_type}-{store_code}{segment}.{dns_domain}"
                alias = f"sapp-12c2-{country_code}-{site_type}-{store_code}{segment}.{dns_domain}"
                self.logger.info(f"Checking fqdn '{fqdn}' existing records.")
                is_fqdn_existing = self.ipam_api.get_ipam_object_by_fqdn(fqdn).status_code == 200
                if is_fqdn_existing:
                    self.logger.warning(f"IPAM Record already exists '{fqdn}'")
                    continue
                self.logger.info(f"Creating IPAM Record '{fqdn}'")
                ipam = Ipam(logger=self.logger)
                ipam.assign_ip(self.vm_spec[WorkloadSpec.VLAN_ID], self.vm_spec[WorkloadSpec.PE], fqdn, [alias])
        else:
            self.logger.info("SO is single VM with '6' CNames.")
            self.logger.info("Multiple Aliases via IPAM is not supported.")
            self.logger.info("We are using SOlid DNS here.")
            self.logger.info("Generating Record Names.")
            site_type = "SO"
            prefix_list = ["sapp", "lipp"]
            segments = ["", "-a01", "-m01"]
            cnames = [
                f"{prefix}-12c2-{country_code}-{site_type}{segment}.{dns_domain}"
                for prefix in prefix_list
                for segment in segments
            ]
            self.logger.info(f"Creating CNAMEs: {cnames}")
            self._add_cnames(cnames)

    def lan_com(self):
        # TODO: implement when extra NICs are supported
        self.logger.info("The app packages are not implemented yet...")
        return

    def local_solution(self):
        site_code = self.vm_spec[WorkloadSpec.VM_NAME].split("-")[0]
        self.thors_hammer.make_profile(site_code, "LS")
        self.logger.info(f"Thorshammer work done on {self.vm_spec[WorkloadSpec.VM_NAME]}")

    def mhs(self):
        # app_name = "MHS"
        self.logger.info("Confirmed with Jerry Zou, no need to add create IPAM record anymore.")

    def mhs_app(self):
        app_name = "MHS APP"
        self.logger.info(f"Creating Additional IPAM records '{app_name}'")
        self.logger.info(f"Registering Extra '{app_name}' records in IPAM")
        # TODO: handle server with special prefix
        _, _country_code, bu_code = split_pe_into_parts(self.vm_spec[WorkloadSpec.VM_NAME])
        if not bu_code:
            self.logger.warning("Cannot get the BU code, weird, will skip alias registration.")
            return
        dns_domain = self._get_dns_domain(self.vm_spec[WorkloadSpec.IP_ID])
        if test_pinging_by_output(f"mhs-{bu_code}.{self.domain}", retries=1):
            self.logger.warning(f"DNS CNAME 'mhs-{bu_code}.{self.domain}' already exists, skipping alias creation...")
            return
        alias_1 = f"mhs-{bu_code}.{dns_domain}"
        if len(bu_code) >= 5:
            alias_2 = f"itm{bu_code}.{dns_domain}"
        else:
            alias_2 = f"itm{'0'*(5-len(bu_code))}{bu_code}.{dns_domain}"
        self.logger.info(f"Creating Alias Record {alias_1}, {alias_2}.")
        ip_id = self.vm_spec[WorkloadSpec.IP_ID]
        self.ipam_api.ip_alias_add(ip_id, alias_1)
        self.ipam_api.ip_alias_add(ip_id, alias_2)

    def mhs_db(self):
        # app_name = "MHS PG DB"
        self.logger.info("Confirmed with Jerry Zou, no need to add create IPAM record anymore.")

    def mhs_pg14_db(self):
        # app_name = "MHS PG14 DB"
        try:
            self._run_pg14_tower_job(cluster_name_prefix="itmhspg", app_name="MHS")
        except TowerJobFailed as e:
            raise TowerJobFailedDb(e.job_id)

    def palo_alto_1(self):
        self._palo_alto(1)

    def palo_alto_2(self):
        self._palo_alto(2)

    def _palo_alto(self, pa_number):
        app_name = f"Palo Alto Fw {pa_number}"
        self.logger.title(f"Executing Application Add-on Packages, type: '{app_name}'")
        self.logger.info("Adding NICs")
        _vmcheck = VM_Check(pe=self.vm_spec[WorkloadSpec.PE], vm=self.vm_fqdn, logger=self.logger)
        vlan_ids = [0, 115, 113]
        self.ilg.write("Sleep 5 min before powering off the VM....")
        time.sleep(300)
        _vmcheck.add_nic_vm(vm_uuid=self.vm_uuid, vlanids=vlan_ids)
        self.logger.info("Set NIC to trunk")
        _vmcheck.set_nic_trunked(trunk_id=0, facility_type=self.facility_type)
        self.logger.info("Adding Serial Port For Palo Alto VMs")
        pe_host_ip = self.pe_api_handler.get_cluster_detail().get("cluster_external_ipaddress")
        pe_username, pe_password = self._get_pe_secret()
        res, ssh = SSHConnect(pe_host_ip, pe_username, pe_password).connect()
        if not res:
            raise SSHFailed(pe_host_ip)
        ssh.exec_command(f"/usr/local/nutanix/bin/acli -o json vm.serial_port_create {self.vm_uuid} type=kServer index=0")
        time.sleep(15)
        self.ilg.write("Sleep 5 min before Resetting VM power state....")
        time.sleep(300)
        self.pe_api_handler.set_vm_power_state(self.vm_uuid, "RESET")
        self._add_vm_to_affinity_group(app_name, group_name="PaloAlto")
        self.logger.info(f"App Package deployment '{app_name}' finished")

    def pbr(self):
        tower = Tower(logger=self.logger, db_logger=self.db_logger)
        tower.get_tower_template(9709, "job")
        payload = {
            "extra_vars": {
                "server_name": self.vm_fqdn
            }
        }
        tower.execute_tower_template(9709, payload, "job")

    def pip_2(self):
        app_name = "PIP App"
        self.logger.info(f"Creating Additional IPAM records '{app_name}'")
        ip_id = self.vm_spec[WorkloadSpec.IP_ID]
        # TODO: handle server with special prefix
        _, _, bu_code = split_pe_into_parts(self.vm_spec[WorkloadSpec.VM_NAME])
        ip_class_parameters_dict = Ipam(logger=self.logger).get_ip_class_parameters_dict(ip_id)
        dns_domain = ip_class_parameters_dict['domain']
        alias = f"lip{bu_code}.{dns_domain}"
        self.logger.info(f"We are going to create alias: {alias} for {self.vm_fqdn}, but let us check if alias already exists first.")
        # Check if the old pip app server has already been registered with the CNAME lip***.ikea.com, if yes, then return.
        try:
            existing_ip = socket.gethostbyname(alias)
            if existing_ip:
                self.logger.info(f"Alias {alias} is already assigned to ip '{existing_ip}', Skipping alias creation")
                self.logger.info(f"App Package deployment '{app_name}' finished")
                return
        except socket.gaierror:
            self.logger.info(f"Alias {alias} doesn't exist, Registering Extra '{app_name}' records in IPAM")
            self.logger.info("Creating Alias Record.")
            self.ipam_api.ip_alias_add(ip_id, alias)
            self.logger.info(f"App Package deployment '{app_name}' finished")

    def pip_db_pg14(self):
        try:
            self._run_pg14_tower_job(cluster_name_prefix="itpg", app_name="PIP", enable_relaunch=True)
        except TowerJobFailed as e:
            self.logger.warning("PIP DB tower job failed after relaunch. Need more investigation from DB team.")
            recipients = [
                "<EMAIL>", "<EMAIL>", "<EMAIL>"]
            cc = ["<EMAIL>", "<EMAIL>"]
            self.logger.info(f"Sending mail to recipients {recipients}.")
            body = f"""
Hi,

Tower template {e.template_id} with job ID {e.job_id} still failed after relaunch.

Please investigate: https://tower.ikea.com/#/jobs/workflow/{e.job_id}/output.
            """
            msg = MIMEText(body, "plain")
            msg['Subject'] = f"PIP DB Ansible Failure - {self.vm_fqdn} - JOB ID: {e.job_id}"
            msg['From'] = "<EMAIL>"
            msg['To'] = ",".join(recipients)
            msg['CC'] = ",".join(cc)
            with smtplib.SMTP("smtp-gw.ikea.com") as s:
                s.sendmail("<EMAIL>", recipients + cc, msg.as_string())
            self.logger.info("Mail sent successfully.")
            raise TowerJobFailedPipDbPG14(e.job_id)

    def pos(self):
        # app_name = "iPOS server"
        prefix = self.vm_spec[WorkloadSpec.VM_NAME].split('-')[0]
        self._pos(prefix, self.vm_spec[WorkloadSpec.VM_NAME])

    def _pos(self, prefix, machine_name):
        self.logger.info("Thorshammer Site Prep 'iPOS'")
        self.thors_hammer.make_profile(prefix, "POS")
        self.logger.info(f"Thorshammer work done on '{machine_name}")
        self.logger.info("Assigning Profiles to POS")
        profiles = [
            "Alias ICC5_Reboot_Weekly",
            "App iPos ALL modules SQL2019",
            # "App iPos ALL modules", upgrade to **SQL2019
            "App Nutanix VirtIO Driver",
        ]
        self._add_host_profiles(machine_name, profiles)
        subscriptions = [
            "Splunkforwarder"
        ]
        self._add_host_subscriptions(machine_name, subscriptions)
        self.logger.info("Adding L-DISCOVE-A-GLOBAL to server admin group.")
        self.thors_hammer.add_user_to_admin_computer_group("L-DISCOVE-A-GLOBAL", machine_name)
        self.logger.info("Let's wake this baby up.")
        self.logger.info("Sending 'WAKEUP' to idem.")
        self.thors_hammer.control_agent_status(machine_name, False)
        self.thors_hammer.get_deployment_status(machine_name, profilec="App iPos ALL modules SQL2019", retries=90)
        self.logger.info("Restoring Installation TimeFrame to site default")
        self.thors_hammer.remove_profile(machine_name, "Alias SET_INSTALL_TIME_FRAME")
        self.logger.info("Thor Installation Completed with Success!")

    def _test_deployment_finished(self, machine_name, profilec, retries=90, retry_interval=120):
        i = 0
        while i < retries:
            deployment_res = self.thors_hammer.test_deployment_finished(machine_name, profilec).json()
            if deployment_res["Success"]:
                self.logger.info("All modules installed. Continues with the next step")
                break
            error = deployment_res["Error"]
            if error and "not found in assigned software" not in error:
                self.logger.error(f"Something wrong in the deployment. Detail: {error}")
                i += 9
            else:
                self.logger.info("We found the job. Not done yet!")
            self.logger.info(f"Sleep '{retry_interval}' secs...")
            time.sleep(retry_interval)
        raise ThorsHammerException(deployment_res.json()["Error"], 'Modules installation failed!')

    def pos_client(self):
        # Empty
        pass

    def ts_1(self):
        site_code = self.vm_spec[WorkloadSpec.VM_NAME].split("-")[0]
        _, country_code, _ = split_pe_into_parts(self.vm_spec[WorkloadSpec.VM_NAME])
        machine_name = self.vm_spec[WorkloadSpec.VM_NAME]
        self.thors_hammer.make_profile(site_code, "TS")
        self.logger.info(f"Thorshammer work done on '{machine_name}")
        profiles = [
            f"Alias ICC5_{machine_name}",
            # f"App ICC5_TS_{country_code}",
            f"Alias ICC5_{machine_name}",
            "Alias ICC5_Reboot_Daily",
            "Alias IWTS6 Member RDSH",
        ]
        self._add_host_profiles(machine_name, profiles)
        subscriptions = [
            "IKEA Management Utility",
            "IWTS 6.0_2302",
            "Splunkforwarder"
        ]
        self._add_host_subscriptions(machine_name, subscriptions)
        self.thors_hammer.get_deployment_status(machine_name, subscription="IWTS 6.0_2302")
        self._add_host_profiles(machine_name, [f"App ICC5_TS_{country_code}"])
        self.thors_hammer.get_deployment_status(machine_name, profilec=f"App ICC5_TS_{country_code}")
        self.thors_hammer.remove_profile(machine_name, "Alias SET_INSTALL_TIME_FRAME")
        self.logger.info("Thor Installation Completed with Success!")

    def ts_2(self):
        self.ts_1()

    def wlc_1(self):
        _vmcheck = VM_Check(pe=self.vm_spec[WorkloadSpec.PE], vm=self.vm_fqdn, logger=self.logger)
        vlanids = [157, 1001]
        _vmcheck.add_nic_vm(vm_uuid=self.vm_uuid, vlanids=vlanids)
        _vmcheck.set_nic_trunked(trunk_id=157, facility_type=self.facility_type)
        app_name = "WLC 1"
        group_name = "WirelessController"
        self._add_vm_to_affinity_group(app_name, group_name)

    def wlc_2(self):
        _vmcheck = VM_Check(pe=self.vm_spec[WorkloadSpec.PE], vm=self.vm_fqdn, logger=self.logger)
        vlanids = [157, 1001]
        _vmcheck.add_nic_vm(vm_uuid=self.vm_uuid, vlanids=vlanids)
        _vmcheck.set_nic_trunked(trunk_id=157, facility_type=self.facility_type)
        app_name = "WLC 2"
        group_name = "WirelessController"
        self._add_vm_to_affinity_group(app_name, group_name)

    def wlc_so_1(self):
        _vmcheck = VM_Check(pe=self.vm_spec[WorkloadSpec.PE], vm=self.vm_fqdn, logger=self.logger)
        vlanids = [357, 1001]
        _vmcheck.add_nic_vm(vm_uuid=self.vm_uuid, vlanids=vlanids)
        _vmcheck.set_nic_trunked(trunk_id=357, facility_type=self.facility_type)
        app_name = "WLC 1"
        group_name = "WirelessController"
        self._add_vm_to_affinity_group(app_name, group_name)

    def wlc_so_2(self):
        _vmcheck = VM_Check(pe=self.vm_spec[WorkloadSpec.PE], vm=self.vm_fqdn, logger=self.logger)
        vlanids = [357, 1001]
        _vmcheck.add_nic_vm(vm_uuid=self.vm_uuid, vlanids=vlanids)
        _vmcheck.set_nic_trunked(trunk_id=357, facility_type=self.facility_type)
        app_name = "WLC 2"
        group_name = "WirelessController"
        self.logger.info(f"Executing Application Add-on Packages, type: '{app_name}'")
        self._add_vm_to_affinity_group(app_name, group_name)
        self.logger.info(f"App Package deployment '{app_name}' finished")

    def sapp_app(self):
        # vm name is with 5 digit site code, e.g. RSE00123-NTxxxx, RSE01234, , RSE12345
        ip_id = self.vm_spec[WorkloadSpec.IP_ID]
        dns_domain = self._get_dns_domain(ip_id)
        digit = self.vm_spec[WorkloadSpec.VM_NAME].split("-")[0][-5:]   # Assume the VM name is already with 5 digit
        _, country_code, _ = split_pe_into_parts(self.vm_spec[WorkloadSpec.VM_NAME])
        alias = f"sapp-{country_code}-sto-{digit}.{dns_domain}"
        self.logger.info(f"Creating Alias Record {alias}.")
        self.ipam_api.ip_alias_add(ip_id, alias)

    def sapp_db(self):
        # LX2040
        try:
            # cluster_name prod: ITDCIPGYYYYY, TEST: TEDCIPGYYYYY
            # Hardcode sapp db password
            db_pass = ServiceAccount(usage="SAPP_DB_PASS").get_service_account()["password"]
            self._run_pg14_tower_job(cluster_name_prefix="itdcipg", app_name="DCI", db_pass=db_pass)
        except TowerJobFailed as e:
            raise TowerJobFailedDb(e.job_id)

    def _get_dns_domain(self, ip_id):
        ip_class_parameters_dict = Ipam(logger=self.logger).get_ip_class_parameters_dict(ip_id)
        dns_domain = ip_class_parameters_dict['domain']
        return dns_domain

    def _add_vm_to_affinity_group(self, app_name, group_name, check_group_existence=True):
        ntx_cli = NutanixCLI(pc=self.vm_spec[WorkloadSpec.PC], pe=self.vm_spec[WorkloadSpec.PE], logger=self.logger, ntx_sa=self.ntx_sa)
        group = ntx_cli.get_acli_vm_group(group_name)
        if check_group_existence:
            self.logger.info(f"Checking for ACI VM Group presence: '{group_name}'.")
            if not group:
                self.logger.info(f"Creating Anti Affinity Group '{group_name}'")
                ntx_cli.new_acli_vm_group(group_name)
                group = ntx_cli.get_acli_vm_group(group_name)
        ntx_cli.set_acli_group_affinity(group.get("uuid"))
        self.logger.info(f"Adding VM to Anti Affinity Group '{app_name}'")
        ntx_cli.add_acli_vm_to_group(group, self.vm_uuid)

    def _add_host_profiles(self, machine_name, profiles):
        for p in profiles:
            self.thors_hammer.add_host_profile(machine_name, p)

    def _add_host_subscriptions(self, machine_name, subscriptions):
        for s in subscriptions:
            self.thors_hammer.add_host_subscription(machine_name, s)

    def _is_same_cluster(self, vm_name, pe):
        return vm_name.split("-")[0].lower() == pe.split("-")[0].lower()

    def _run_pg14_tower_job(self, cluster_name_prefix, app_name, enable_relaunch=False, db_pass=None):
        tower_pg_id = "8715"
        tower = Tower(logger=self.logger, db_logger=self.db_logger)
        tower.get_tower_template(tower_pg_id)
        _, country_code, store_code = split_pe_into_parts(self.vm_spec[WorkloadSpec.PE])
        cluster = self.vm_spec[WorkloadSpec.VM_NAME].split("-")[0]
        if bool(re.search(r"\d{5}", cluster)):      # 5 digits
            digits = re.split(r'(\d+)', cluster)[1]
            cluster_name = f"{cluster_name_prefix}{digits}"
        elif store_code.isdecimal():
            cluster_name = f"{cluster_name_prefix}{store_code}"
        else:
            cluster_name = f"{cluster_name_prefix}{country_code}{store_code}"
        tower_payload = {
            "credential_passwords": {},
            "extra_vars": {
                "hostname": self.vm_fqdn,
                "cluster_name": cluster_name,
                "hostnames": self.vm_fqdn,
                "service": app_name,
                "provider": "postgres",
                "edition": "CE",
                "version": "14",
                "port": "5432",
                "barman_backup_rootdir": "/backup",
                "app_name": app_name,
                "aws_region": "euseelm",  # TODO: will change in future..
            }
        }
        if db_pass:
            tower_payload["extra_vars"]["DB_PASS"] = db_pass
        tower.execute_tower_template(tower_pg_id, tower_payload, retries=60, enable_relaunch=enable_relaunch)    # 60 * 120sec = 2 hours
        self.logger.info("Launch job succeeded.")
    
    def _add_cnames(self, cnames):
        """
        Add CNAME records for the given list of CNAMEs.
        """
        ip_class_parameters_dict = Ipam(logger=self.logger).get_ip_class_parameters_dict(self.vm_spec[WorkloadSpec.IP_ID])
        dns_name = ip_class_parameters_dict['dns_name']
        for cname in cnames:
            self.logger.info(f"Checking CNAME '{cname}' existing records.")
            dns_rr_list = self.ipam_api.dns_rr_list("CNAME", cname)
            if dns_rr_list.status_code == 204:
                self.logger.info(f"The CNMAE '{cname}' does not exist, creating it now.")
                self.ipam_api.dns_rr_add(fqdn=cname.lower(), rr_type="CNAME", value=self.vm_fqdn.lower(), dns_name=dns_name)
            else:
                self.logger.info(f"We cannot create C Name '{cname}' as it already exists.")
                existing_value1 = dns_rr_list[0]['value1']
                self.logger.info(f"Its Pointing towards : '{existing_value1}'")
                if existing_value1 != self.vm_fqdn:
                    self.logger.error("This CName its target is different than mine. Manual DNS Cleanup required.")
                else:
                    self.logger.warning("DNS Records for this target already exist")
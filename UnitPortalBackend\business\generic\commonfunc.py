# installed modules
import base64
import json
import os
import re
import uuid
import logging
from datetime import datetime, timezone, UTC
import psutil
import requests
import paramiko
import sys
import traceback
import time

import requests.cookies
from ping3 import ping
from urllib3.exceptions import ConnectTimeoutError, InsecureRequestWarning
import werkzeug.exceptions as flaskex
import pandas as pd
from flask import request
import static.SETTINGS as SETTING
from business.generic.base_up_exception import SSHFailed
from models.auth_models import ModelUser
from models.ntx_models import ModelPrismCentral, ModelRetailNutanixOneview, ModelPrismElement
from base_path import application_path
from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement


def string_to_base64(s):
    byte_str = s.encode("ascii")
    byte_base64 = base64.b64encode(byte_str)
    str_base64 = byte_base64.decode("ascii")
    return str_base64


def base64_to_string(str_base64):
    byte_base64 = str_base64.encode("ascii")
    byte_str = base64.b64decode(byte_base64)
    s = byte_str.decode("ascii")
    return s


def string_to_utc(s):  # take care of special string "2023-02-16,14:05:11"
    # crappy function, not sure what I am doing...
    year, month, day, hour, minute, second = s[0:4], s[5:7], s[8:10], s[11:13], s[14:16], s[17:19]
    return int(year), int(month), int(day), int(hour), int(minute), int(second)


def compare_time_diff(startdate, enddate=None,
                      timediff=30):  # check if enddate - startdate is bigger then 30 minutes, which means token expired
    # crappy function, not sure what I am doing...
    if not startdate:
        return False
    sdyear, sdmonth, sdday, sdhour, sdminute, sdsecond = string_to_utc(startdate)
    sd = datetime(sdyear, sdmonth, sdday, sdhour, sdminute, sdsecond, tzinfo=timezone.utc)
    if enddate:
        edyear, edmonth, edday, edhour, edminute, edsecond = string_to_utc(enddate)
        ed = datetime(edyear, edmonth, edday, edhour, edminute, edsecond, tzinfo=timezone.utc)
    else:
        ed = datetime.utcnow().replace(tzinfo=UTC)
    delta = ed - sd
    return delta.seconds > timediff * 60


def create_file(filepath, filename=None):
    filename = filename if filename else str(uuid.uuid4())
    try:
        # logpath = filepath + '\\' + filename
        logpath = os.path.join(filepath, filename)
        fp = open(logpath, 'w+')        # pylint: disable=R1732
        fp.close()
        return logpath
    except Exception:
        return False


def terminate_process_by_id(pid):
    try:
        p = psutil.Process(pid)
        if p.is_running():
            p.kill()
            return True, f'Process {pid} has been terminated.'
        return False, "PID found, but not running."
    except psutil.NoSuchProcess:
        return False, "PID NOT Found."
    except Exception as e:
        return False, str(e)


def setup_logger(name, log_file, level=logging.INFO):
    # create new loggers
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
    handler = logging.FileHandler(log_file, 'a')
    handler.setFormatter(formatter)
    logger = logging.getLogger(name)
    logger.setLevel(level)
    logger.addHandler(handler)
    return logger





def test_pinging(server):
    import subprocess
    import platform
    param = '-n' if platform.system().lower() == 'windows' else '-c'
    command = ['ping', param, '1', server]
    return subprocess.call(command, stdout=subprocess.DEVNULL) == 0


def test_pinging_by_output(server, retries=1):
    # can ping: True
    # cannot ping: False
    import subprocess
    import platform
    param = '-n' if platform.system().lower() == 'windows' else '-c'
    command = ['ping', param, '1', server]
    for _ in range(retries):
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        process.wait()
        output = bytes.decode(process.stdout.read())
        if "request timed out" not in output.lower() and "destination host unreachable" not in output.lower():
            return True
    return False


def check_ping(server, count=1):
    """
    response = 0: Network active
    response != 0: Network inactive
    """
    import platform
    response = os.system(f"ping {'-n' if platform.system().lower() == 'windows' else '-c'} {count} {server}")
    return response == 0


def ping3_with_retry(ip, retries=1, timeout=4):
    for _attempt in range(retries):
        response = ping(ip, timeout=timeout)
        if response:
            return True
    return False


def get_request_token():
    headers = request.headers.getlist('Authorization')
    headers = headers[0].lower()
    token = headers.replace("bearer", "").strip()  # {'Authorization':"bearer ****-*****-*****-****"}
    if token:
        return token
    raise Exception(f"Failed to get token from {request}")


def get_user_by_token():
    user_token = get_request_token()
    user = ModelUser.query.filter_by(token=user_token).first()
    if not user:
        raise flaskex.Unauthorized("Cann't find a user by this token, try re-login maybe ?")
    return user


def convert_GiB_to_bytes(input_size):           # noqa     # pylint: disable=C0103
    return input_size * (1024 ** 3)


def is_json(str: str):
    try:
        json.loads(str)
    except ValueError:
        return False
    return True


class CommonLogger(logging.Logger):
    super(logging.Logger)

    def title(self, msg):
        if len(msg) < 98:
            self.info("*" * 100)
            self.info("*" + " " * 98 + "*")
            space_before = (100 - 2 - len(msg)) // 2
            space_behind = 100 - 2 - space_before - len(msg)
            self.info("*" + " " * space_before + msg + " " * space_behind + "*")
            self.info("*" + " " * 98 + "*")
            self.info("*" * 100)
        else:
            self.info("*" * 100)
            self.info(msg)
            self.info("*" * 100)


def setup_common_logger(name, log_file=None, level=logging.INFO):
    # create new loggers
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
    logger = CommonLogger(name)
    logger.setLevel(level)
    if not log_file:
        ch = logging.StreamHandler()
        ch.setLevel(logging.DEBUG)
        ch.setFormatter(formatter)
    else:
        ch = logging.FileHandler(log_file, 'a')
        ch.setFormatter(formatter)
    logger.addHandler(ch)
    return logger

# logging = setup_common_logger(str(datetime.timestamp(datetime.now())))


class CommonRestCall:
    def __init__(self, username=None, password=None, token=None, logger=logging, timeout=None):
        self.username = username
        self.password = password
        self.token = token
        self.logger = logger if logger else logging
        self.timeout = timeout

    def call_restapi(self, url, method="GET", params=None, headers=None, payload=None, is_json=True, files=None, retry=5, timeout=None, cookies = None):
        i = 1
        self.logger.info(f"Calling restapi, URL: {url}, method: {method}, headers: {headers}")
        self.logger.info(f"params: {params}")
        self.logger.info(f"User: {self.username}")
        log_payload = payload.copy() if isinstance(payload, dict) else payload
        if isinstance(log_payload, dict):
            for key in list(log_payload.keys()):
                if 'password' in key.lower() and log_payload[key] is not None:
                    log_payload[key] = '*****'
        self.logger.info(f"payload: {log_payload}")
        self.logger.info(f"files: {files}")
        self.logger.info(f"timeout: {timeout if timeout else self.timeout}")

        while i < retry + 1:
            try:
                kwargs = {
                    'method': method,
                    'url': url,
                    'params': params,
                    'verify': False,
                    'timeout': timeout if timeout else self.timeout
                }
                if (self.username and self.password):
                    kwargs['auth'] = (self.username, self.password)
                if headers:
                    kwargs['headers'] = headers
                if payload:
                    if is_json:
                        kwargs['json'] = payload
                    else:
                        kwargs['data'] = payload
                if files:
                    kwargs['files'] = files
                if cookies:
                    kwargs['cookies'] = cookies
                res = requests.request(**kwargs)
                if res.ok:
                    return res
                self.logger.warning(f"Response content: {res.content}")
                i += 1
                if i >= retry + 1:
                    raise flaskex.BadGateway(f"Out of retry times when calling {url}. Response: {res.content}")
                self.logger.warning(f"API response is not ok, going to do the {i} retry...")
            except flaskex.BadGateway as e:
                raise e
            except ConnectTimeoutError as e:
                i += 1
                self.logger.warning("API call timeout.")
                self.logger.warning(e)
            except Exception as e:
                i += 1
                self.logger.warning(f"Call api has exception: {str(e)}")
                self.logger.warning(f"Call api failed, going to do the {i} retry...")
        self.logger.error(f"Call api still failed after {i} retries...")
        raise flaskex.BadGateway(f"Failed to send API request to Upstream Service: {url}")
    
    @staticmethod
    def is_url_responding(url):
        return requests.options(url).ok


class NutanixRest():
    def __init__(self, fqdn=None, username=None, password=None, logger=None, retry=5) -> None:
        self.prism_url = {
            'v1': f"https://{fqdn}:9440/PrismGateway/services/rest/v1",
            'v2': f"https://{fqdn}:9440/PrismGateway/services/rest/v2.0",
            'v3': f"https://{fqdn}:9440/api/nutanix/v3",
            'v0.8': f"https://{fqdn}:9440/api/nutanix/v0.8"
        }
        self.lcm_url = {
            'v1': f"https://{fqdn}:9440/lcm/v1.r0.b1",
            'v4': f"https://{fqdn}:9440/api/lcm/v4.0.a1"
        }
        self.genesis_url = f"https://{fqdn}:9440/PrismGateway/services/rest/v1/genesis"
        if not (username and password):
            raise Exception("Missing authentication")
        self.username, self.password = username, password
        self.logger = logger
        self.retry = retry
        self.rest = CommonRestCall(username=self.username, password=self.password, logger=self.logger)
    
    def prism_call(self, version, request_url, **kwargs):
        self.logger.info(f"Calling {request_url} through {version} prism api using {kwargs.get('method')} method")
        kwargs['url'] = f"{self.prism_url[version]}{request_url}"
        resp = self.rest.call_restapi(**kwargs)
        return resp

    def lcm_call(self, version, request_url, **kwargs):
        self.logger.info(f"Calling {request_url} through {version} lcm api using {kwargs.get('method')} method")
        kwargs['url'] = f"{self.lcm_url[version]}{request_url}"
        return self.rest.call_restapi(**kwargs)
    
    def genesis_call(self, hash_payload, **kwargs):
        self.logger.info(f"Calling genesis api using {kwargs.get('method')} method")
        kwargs['url'] = self.genesis_url
        kwargs['payload'] = {
            'value': json.dumps(hash_payload)
        }
        return self.rest.call_restapi(**kwargs)
    
    def prism_get(self, version='v1', request_url=None):
        self.logger.info(f"Calling {request_url} through {version} API using GET method")
        url = f"{self.prism_url[version]}{request_url}"
        resp = self.rest.call_restapi(url=url, method="GET", retry=self.retry)
        return resp

    def prism_post(self, version='v1', request_url=None, payload=None, is_json=True, files=None):
        self.logger.info(f"Calling {request_url} through {version} API using POST method")
        url = f"{self.prism_url[version]}{request_url}"
        resp = self.rest.call_restapi(url=url, method="POST", payload=payload, is_json=is_json, files=files, retry=self.retry)
        return resp

    def prism_put(self, version='v1', request_url=None, payload=None):
        self.logger.info(f"Calling {request_url} through {version} API using PUT method")
        url = f"{self.prism_url[version]}{request_url}"
        resp = self.rest.call_restapi(url=url, method="PUT", payload=payload, retry=self.retry)
        return resp

    def prism_delete(self, version='v1', request_url=None):
        self.logger.info(f"Calling {request_url} through {version} API using DELETE method")
        url = f"{self.prism_url[version]}{request_url}"
        resp = self.rest.call_restapi(url=url, method="DELETE", retry=self.retry)
        return resp
    
    def lcm_get(self, version='v1', request_url=None):
        url = f"{self.lcm_url[version]}{request_url}"
        resp = self.rest.call_restapi(url=url, method="GET", retry=self.retry)
        return resp

    def lcm_post(self, version='v1', request_url=None, payload=None):
        url = f"{self.lcm_url[version]}{request_url}"
        resp = self.rest.call_restapi(url=url, method="POST", payload=payload, retry=self.retry)
        return resp

    def genesis_get(self):
        pass

    def genesis_post(self, hash_payload=None, payload=None):
        url = self.genesis_url
        if hash_payload:
            payload = {
                'value': json.dumps(hash_payload)
            }
        resp = self.rest.call_restapi(url=url, method="POST", payload=payload, retry=self.retry)
        return resp
    
    
class NutanixAPI:
    def __init__(self, pc=None, pe=None, username=None, password=None, logger=logging, retry=5, timeout=None, collector = False, pe_fqdn = None) -> None:
        if pc:
            self.pc_version_url = {
                0.8: f"https://{pc}:9440/api/nutanix/v0.8",
                1: f"https://{pc}:9440/PrismGateway/services/rest/v1",
                2: f"https://{pc}:9440/PrismGateway/services/rest/v2.0",
                3: f"https://{pc}:9440/api/nutanix/v3",
                4: f"https://{pc}:9440/api",
            }
        if pe:
            if collector:
                if pe_fqdn:
                    self.pe_fqdn = pe_fqdn
                else:
                    _, self.pe_fqdn = CommonHelper.parse_pe_name_and_fqdn_collector(pe) 

            else:
                _, self.pe_fqdn = CommonHelper.parse_pe_name_and_fqdn(pe)
            self.url_pe_rest = f"https://{self.pe_fqdn}:9440/api/nutanix/v3"
            self.url_pe_v0_8_rest = f"https://{self.pe_fqdn}:9440/api/nutanix/v0.8"
            self.url_pe_v1_rest = f"https://{self.pe_fqdn}:9440/PrismGateway/services/rest/v1"
            self.url_pe_v2_rest = f"https://{self.pe_fqdn}:9440/PrismGateway/services/rest/v2.0"
            self.url_pe_v4_rest = f"https://{self.pe_fqdn}:9440/api/lcm/v4.0.a1"
            # self.url_pe_v3_rest = f"https://{pe}:9440/api/nutanix/v3"
        if not (username and password):
            return False, "Missing authentication."
        self.username = username
        self.password = password
        self.logger = logger
        self.retry = retry
        self.rest = CommonRestCall(username, password, logger=self.logger, timeout=timeout)

    def call_v1(self):
        pass

    def call_pc_post(self, request_url, payload, api_version=3, headers=None):
        url = f"{self.pc_version_url[api_version]}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="POST", payload=payload, headers=headers)
            if res.status_code in [200, 201, 202]:  # there shouldn't be any 20* response status in nutanix....
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)
        
    def call_pc_v1_post(self, request_url, headers=None, payload=None, is_json=True, files=None):
        url = f"{self.pc_version_url[1]}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="POST", headers=headers, payload=payload, is_json=is_json, files=files)
            if res.status_code in (200, 201):
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)

    def call_pc_put(self, request_url, payload, api_version=3, headers=None):
        url = f"{self.pc_version_url[api_version]}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="PUT", payload=payload, headers=headers)
            if res.status_code in [200, 201, 202]:  # there shouldn't be any 20* response status in nutanix....
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)

    def call_pc_get(self, request_url, api_version=3, retry=5):
        url = f"{self.pc_version_url[api_version]}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="GET", retry=retry)
            if res.status_code in (200, 201):
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)

    def call_pc_delete(self, request_url, api_version=3):
        url = f"{self.pc_version_url[api_version]}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="DELETE")
            if res.ok:
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)

    def call_pe_get(self, request_url, api_version=1, retries=5):
        pe_url = self.url_pe_v1_rest
        if api_version == 0.8:
            pe_url = self.url_pe_v0_8_rest
        elif api_version == 2:
            pe_url = self.url_pe_v2_rest
        url = f"{pe_url}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="GET", retry=retries)
            if res.status_code in (200, 201):
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)

    def call_pe_v0_8_post(self, request_url, payload=None):
        url = f"{self.url_pe_v0_8_rest}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="POST", payload=payload)
            if res.status_code == 200 or res.status_code == 201:
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)

    def call_pe_post(self, request_url, headers=None, payload=None, is_json=True, files=None):
        url = f"{self.url_pe_v1_rest}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="POST", headers=headers, payload=payload, is_json=is_json, files=files)
            if res.status_code in (200, 201):
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)

    def call_pe_put(self, request_url, payload=None, api_version=1, retry=None):
        pe_url = self.url_pe_v1_rest
        if api_version == 2:
            pe_url = self.url_pe_v2_rest
        url = f"{pe_url}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="PUT", payload=payload, retry=retry if retry else self.retry)
            if res.ok:
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)

    def call_pe_patch(self, request_url, payload=None, api_version=1):
        pe_url = self.url_pe_v1_rest
        if api_version == 2:
            pe_url = self.url_pe_v2_rest
        url = f"{pe_url}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="PATCH", payload=payload)
            if res.ok:
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)

    def call_pe_v2_post(self, request_url, payload):
        url = f"{self.url_pe_v2_rest}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="POST", payload=payload)
            if res.status_code in (200, 201, 202):
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)

    def call_pe_delete(self, request_url, api_version=2):
        pe_url = self.url_pe_v1_rest
        if api_version == 2:
            pe_url = self.url_pe_v2_rest
        url = f"{pe_url}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="DELETE")
            if res.ok:
                if res.status_code == 204:
                    return True, None
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)
        
    def call_pc_v3_put(self, request_url, payload):
        url = f"{self.pc_version_url[3]}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="PUT", payload=payload)
            if res.status_code == 200 or res.status_code == 201:
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)
        
    def call_pe_v3_post(self, request_url, payload):
        url = f"{self.url_pe_rest}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="POST", payload=payload)
            if res.status_code == 200 or res.status_code == 201 or res.status_code == 202:
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)      
        
    def call_pe_v3_get(self, request_url):
        url = f"{self.url_pe_rest}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="GET")
            if res.status_code == 200 or res.status_code == 201:
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)
        
    def call_pe_v4_post(self, request_url, payload):
        url = f"{self.url_pe_v4_rest}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="POST", payload=payload)
            if res.status_code in (200, 201, 202):
                return True, res.json()
            return False, res.json()
        except Exception as e:
            return False, str(e)  
   
        
class SSHConnect():
    def __init__(self, host, username, password, public_key=None, retry=5, logger=logging) -> None:
        self.ssh = paramiko.SSHClient()
        self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.host = host
        self.username = username
        self.password = password
        self.public_key = public_key
        self.retry = retry
        self.logger = logger
        self.channel = None

    def connect(self, timeout = None):
        res, ssh, i = False, None, 1
        while i <= self.retry:  # retry for self.retry times, in case hiccups
            try:
                # if self.ssh.get_transport() and self.ssh.get_transport().is_alive():
                #     return True, self.ssh
                self.logger.info(f"SSH connecting to {self.host}, this is the '{i}' try.")
                kwargs = {
                    "hostname": self.host,
                    "username": self.username,
                    "password": self.password,
                    "timeout": timeout
                }
                self.ssh.connect(**kwargs)
                self.logger.info(f"SSH connected to {self.host}.")
                return True, self.ssh
            except TimeoutError:
                res, ssh = False, "Connection time out, please verify if the host is online."
            except paramiko.ssh_exception.AuthenticationException:
                res, ssh = False, "Username/Password invalid, please verify."
            except Exception:
                res, ssh = False, str(repr(traceback.format_exception(sys.exception())))
            self.logger.warning(f"Connection failed due to {ssh}, retry.")
            i += 1
        self.logger.error(f"We've retried for {self.retry} times, still not connected, aborting.")
        return res, ssh
    
    def connect_sshkey(self):
        res, ssh, i = False, None, 1
        while i <= self.retry:  # retry for self.retry times, in case hiccups
            try:
                # if self.ssh.get_transport() and self.ssh.get_transport().is_alive():
                #     return True, self.ssh
                self.logger.info(f"SSH connecting to {self.host}, this is the '{i}' try.")
                self.ssh.connect(self.host, username=self.username, pkey=self.public_key)
                self.logger.info(f"SSH connected to {self.host} with SSHKEY.")
                return True, self.ssh
            except TimeoutError:
                res, ssh = False, "Connection time out, please verify if the host is online."
            except paramiko.ssh_exception.AuthenticationException:
                res, ssh = False, "SSH_KEY invalid, please verify."
            except Exception:
                res, ssh = False, str(repr(traceback.format_exception(sys.exception())))
            self.logger.warning(f"Connection failed due to {ssh}, retry.")
            i += 1
        self.logger.error(f"We've retried for {self.retry} times, still not connected, aborting.")
        return res, ssh
    
    def exec_command(self, command, wait=5):
        #send command and get output, no stream, no env variable
        try:
            if 'pass' in command.lower():
                masked_command = '*' * len(command)
                self.logger.info(f"SSH Executing '{masked_command}'.")
            else:
                self.logger.info(f"SSH Executing '{command}'.")
            stdin, stdout, _ = self.ssh.exec_command(command)
            self.logger.info(f"Waiting for {wait} seconds for the execution.")
            time.sleep(wait)
            stdin.close()
            o = stdout.read()
            self.logger.info(f"stdout: {o}")
            return o
        except Exception:
            self.logger.error("Got an exception when executing the command.")
            self.logger.error(str(repr(traceback.format_exception(sys.exception()))))
            return False

    def invoke_shell(self):
        try:
            if (not self.channel) or (self.channel and self.channel.closed):
                self.channel = self.ssh.invoke_shell()
            return True
        except Exception:
            self.logger.error(f"Failed to invoke the shell, error: {str(repr(traceback.format_exception(sys.exception())))}.")
            return False

    def send_command(self, command):
        if 'pass' in command.lower():
            masked_command = '*'
            self.logger.info(f"Sending '{masked_command}' to the server.")
        else:
            self.logger.info(f"Sending '{command}' to the server.")
        try:
            self.channel.send((f"{command}\n").encode())
        except Exception as e:
            self.logger.error(f"Failed to send the command, error: {str(e)}.")
            raise e

    def receive_output(self):
        try:
            output = ""
            while True:
                if self.channel.recv_ready():
                    time.sleep(0.5)
                    output += self.channel.recv(1024).decode("utf-8")
                else:
                    break
            return output
        except Exception as e:
            self.logger.error(f"Failed to receive output, error: {str(e)}.")
            return output
        
    def recv(self, length=9999, write_log=True):
        try:
            self.logger.info("Receiving the output .")
            if self.channel.recv_ready():
                output = self.channel.recv(length).decode()  # output is byte
                if write_log:
                    self.logger.info("Received the output: #SSH OUTPUT START#.")
                    self.logger.info(f"{output}")
                    self.logger.info("#SSH OUTPUT END#")
                return True, output
            self.logger.info("No output.")
            return True, ''
        except Exception as e:
            return False, str(e)

    def connect_by_pwd_or_key(self):
        self.logger.info(f"Trying to SSH to the {self.host}.")
        self.logger.info("First try with username/password.")
        res, ssh_client = self.connect()
        if res:
            return ssh_client
        self.logger.info("Failed to connect with username/password, try with key.")
        res, ssh_client = self.connect_sshkey()
        if res:
            return ssh_client
        raise SSHFailed(self.host)



class Redfish():
    def __init__(self, ip, username, password, logger=logging, retry=5, timeout=None):
        self.ip = ip
        self.username = username
        self.password = password
        self.basic_url = f"https://{ip}/redfish/v1/"
        self.json_url = f"https://{ip}/json/"
        self.logger = logger
        self.retry = retry
        self.rest = CommonRestCall(username=self.username, password=self.password, logger=self.logger, timeout=timeout)

    def call_redfish_get(self, request_url=None):
        url = f"{self.basic_url}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="GET", retry=self.retry)
            if res.status_code in (200, 201):
                self.logger.info("Got the response with OK")
                return True, res.json()
            self.logger.warning("Got the response with error")
            return False, res.json()
        except Exception:
            return False, str(repr(traceback.format_exception(sys.exception())))
    
    def call_json_get(self, request_url=None):
        url = f"{self.json_url}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="GET")
            if res.status_code in (200, 201):
                self.logger.info("Got the response with OK")
                return True, res.json()
            self.logger.warning("Got the response with error")
            return False, res.json()
        except Exception:
            return False, str(repr(traceback.format_exception(sys.exception())))
    
    def call_redfish_post(self, request_url, headers=None, payload=None, is_json=True, files=None):
        url = f"{self.basic_url}{request_url}"
        try:
            res = self.rest.call_restapi(url=url, method="POST", headers=headers, payload=payload, is_json=is_json, files=files)
            if res.status_code in (200, 201):
                self.logger.info("Got the response with OK")
                return True, res.json()
            self.logger.warning("Got the response with error")
            return False, res.json()
        except Exception:
            return False, str(repr(traceback.format_exception(sys.exception())))

    def get_ilo_state(self):
        self.logger.info(f"Getting ilo:{self.ip} state through redfish api")
        request_url = "Systems/1/"
        res, data = self.call_redfish_get(request_url=request_url)
        if res and data:
            return True, data
        return False, data

    def change_power_state(self, state):
        self.logger.info("Changing AHV state through redfish api.")
        request_url = "Systems/1/Actions/ComputerSystem.Reset"
        json = {
            'ResetType': state
        }
        res, data = self.call_redfish_post(request_url=request_url, payload=json)
        if res and data:
            return True, data
        return False, data
    
    def get_ilo_token(self, timeout=5):
        url = f"{self.basic_url}SessionService/Sessions"
        headers = {
            'Content-Type': "application/json;charset=UTF-8"
        }
        json = {
            'UserName': self.username,
            'Password': self.password
        }
        resp = self.rest.call_restapi(url=url, method="POST", headers=headers, payload=json, timeout=timeout, retry=self.retry)
        return resp.headers.get('X-Auth-Token')
    
    def generate_ilo_csr(self, **kwargs):
        self.logger.info("Generating CSR through redfish api.")
        request_url = "managers/1/securityservice/httpscert/Actions/HpeHttpsCert.GenerateCSR/"
        json = {
            'City': f"{kwargs.get('city')}",
            'CommonName': f"{kwargs.get('cn')}",
            'OrgName': f"{kwargs.get('org')}",
            'State': f"{kwargs.get('state')}",
            'OrgUnit': f"{kwargs.get('ou')}",
            'Country': f"{kwargs.get('country')}",
            'IncludeIP': True
        }
        res, data = self.call_redfish_post(request_url=request_url, payload=json)
        if res and data:
            return True, data
        return False, data
    
    def download_ilo_csr(self, csr_file=None, retry=10):
        self.logger.info(f"Download CSR file to {csr_file} through redfish api.")
        request_url = "managers/1/securityservice/httpscert/"
        i = 1
        while i < retry:
            self.logger.info(f"Trying to download CSR file, it's the '{i}' try.")
            res, data = self.call_redfish_get(request_url=request_url)
            if res and data.get('CertificateSigningRequest'):
                self.logger.info(f"Got CSR raw data, write into the file '{csr_file}'")
                with open(csr_file, "w") as csr_key:
                    csr_key.write(data['CertificateSigningRequest'])
                self.logger.info("CSR file downloaded.")
                return True, data
            self.logger.warning("Unable to get CSR raw data, waiting for generated, will re-try in 30 seconds.")
            i += 1
            time.sleep(30)
        self.logger.error(f"Out of '{retry}' times try, failed to download CSR file.")
        return False, data
    
    def install_ilo_cert(self, certfile=None):
        self.logger.info("Installing SSL certificate redfish api.")
        request_url = "managers/1/securityservice/httpscert/Actions/HpeHttpsCert.ImportCertificate/"
        with open(certfile, "r") as cert:
            json = {
                'Certificate': f"{cert.read()}"
            }
            res, data = self.call_redfish_post(request_url=request_url, payload=json)
        if res and data:
            self.logger.info("Successfully installed SSL certificate")
            return True, data
        self.logger.error("Failed to installed SSL certificate")
        return False, data
    

    def call_redfish_delete(self, request_url, headers=None):
        """
        Call Redfish API with DELETE method
        Args:
            request_url: API endpoint
            headers: Optional request headers
        Returns:
            tuple: (bool, response_data)
        """
        url = f"{self.basic_url}{request_url}"
        try:
            res = self.rest.call_restapi(
                url=url, 
                method="DELETE", 
                headers=headers
            )
            if res.status_code in (200, 201, 204):  # DELETE 通常返回 204
                self.logger.info("Got the response with OK")
                return True, res.json() if res.content else {}
            self.logger.warning("Got the response with error")
            return False, res.json() if res.content else {}
        except Exception:
            return False, str(repr(traceback.format_exception(sys.exception())))

    def remove_ilo_certificate(self):
        """
        Remove the existing HTTPS certificate from iLO using Redfish API.
        DELETE /redfish/v1/managers/{item}/securityservice/httpscert/
        Note: The removal of the TLS/SSL iLO certificate triggers an immediate iLO reset.
        """
        self.logger.info("Removing SSL certificate via redfish api.")
        request_url = "managers/1/securityservice/httpscert/"
        
        res, data = self.call_redfish_delete(request_url=request_url)
        if res and data:
            self.logger.info("Successfully removed SSL certificate, waiting for iLO reset...")
            time.sleep(100)
            return True, data
            
        self.logger.error(f"Failed to remove SSL certificate: {data}")
        return False, data

    def set_ad_ldap(self, sec, domain):
        # _, sec = self.vault.get_secret("A_Central-Production/AdQuery")
        request_url = "AccountService"
        payload = {
            "LDAP": {
                "AccountProviderType": "LDAPService",  
                "Authentication": {  
                    "AuthenticationType": "UsernameAndPassword",  
                    "Password": f"{sec['secret']}",  
                    "Username": f"{sec['username']}" 
                },  
                "ServiceAddresses": [  
                    domain  
                ],
                "ServiceEnabled": True
            }
        }
        url = f"{self.basic_url}{request_url}"
        resp = self.rest.call_restapi(url=url, method="PATCH", payload=payload, retry=self.retry)
        return resp
    
    def setad_create_group(self, remote_group):
        request_url = "AccountService"
        payload = {
            "ActiveDirectory": {
                "RemoteRoleMapping": [
                    {
                        "LocalRole": "administrator",
                        "RemoteGroup": remote_group
                    }
                ]
            }
        }
        url = f"{self.basic_url}{request_url}"
        resp = self.rest.call_restapi(url=url, method="PATCH", payload=payload, retry=self.retry)
        self.logger.info("Redfish Group Created Executed")
        return resp
    
    def get_ilo_object(self, request_url=None):
        res, data = self.call_redfish_get(request_url=request_url)
        if res:
            # domain = data['LDAP']['ServiceAddresses'][0]
            # user_name = data['LDAP']['Authentication']['Username']
            return res, data
        raise Exception("Failed to get iLO Object")
    
    def get_role_id(self, remote_group_to_find=None):
        _res, data = self.get_ilo_object(request_url="AccountService")
        for mapping in data['LDAP']['RemoteRoleMapping']:
            if mapping['RemoteGroup'] == remote_group_to_find:
                role_id = mapping['LocalRole']
                return role_id
        raise Exception(f"No mapping found for remote group {remote_group_to_find}")

    def set_ad_roles(self, roleid):
        payload = {
            "AssignedPrivileges": [
                "Login",
                "ConfigureSelf",
                "ConfigureManager",
                "ConfigureUsers"
            ],
            "OemPrivileges": [
                "RemoteConsolePriv",
                "VirtualMediaPriv",
                "VirtualPowerAndResetPriv",
                "HostBIOSConfigPriv",
                "HostNICConfigPriv",
                "HostStorageConfigPriv"
            ]
        }
        request_url = f"AccountService/Roles/{roleid}"
        url = f"{self.basic_url}{request_url}"
        resp = self.rest.call_restapi(url=url, method="PATCH", payload=payload, retry=self.retry)
        return resp
    
    def retrieve_network_stack(self):
        self.logger.info(f"Retrieving RedFish Network Stack '{self.ip}'")
        session_key = self.get_ilo_token()
        cookie = {
            'sessionKey': session_key,
            'Domain': self.ip
        }
        headers = {
            'Authorization': f'X-Auth-Token {session_key}'
        }
        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
        response = requests.get(f"https://{self.ip}/json/network_general/interface/0", headers=headers, cookies=cookie, verify=False)
        return response.json(), session_key, cookie

    def get_ilo_dnsname(self):
        request_url = "Managers/1/EthernetInterfaces/1/"
        _res, data  = self.get_ilo_object(request_url=request_url)
        return data['HostName']

    def update_ilo_dnsname(self, dns_name):
        request_url = "json/network_general/"
        res, session_key, cookie = self.retrieve_network_stack()
        res['dns_name']  = dns_name
        res["session_key"] = session_key
        res["method"] = "set_general"
        response = self.rest.call_restapi(url=f"https://{self.ip}/{request_url}", method="POST",  cookies=cookie, payload=res, retry=self.retry)
        if response and response.status_code == 200:
            self.logger.info("Great, ILO dnsname updated.")
            return
        raise Exception("error, update_ilo_dnsname failed!")

    def set_ilo_servername(self, hostname):
        request_url = "Systems/1/"
        url = f"{self.basic_url}{request_url}"
        payload = {"HostName": hostname}
        resp = self.rest.call_restapi(url=url, method="PATCH", payload=payload, retry=self.retry)
        if resp:
            self.logger.info("ILO ServerName set successfull.")
            return resp
        raise Exception("We got some errors,please check with Admin")

    def get_ilo_hostname(self):
        request_url = "Systems/1/"
        _res, session_key, _cookie = self.retrieve_network_stack()
        url = f"{self.basic_url}{request_url}"
        resp = self.rest.call_restapi(url=url, headers={'X-Auth-Token' : session_key}, retry=self.retry)
        if resp:
            self.logger.info("Get ILO HostName successfull.")
            return resp.json()['HostName']
        raise Exception("We got some errors,please check with Admin")
        
    def get_ilo_server_info(self):
        session_key = self.get_ilo_token()
        cookie = {
            'sessionKey': session_key,
            'Domain': self.ip
        }
        headers = {
            'Authorization': f'X-Auth-Token {session_key}'
        }
        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
        response = requests.get(f"https://{self.ip}/json/overview", headers=headers, cookies=cookie, verify=False)
        return response.json()


    def get_ilo_dns(self):
        request_url = "Managers/1/EthernetInterfaces/1"
        _res, data  = self.get_ilo_object(request_url=request_url)
        return data['StaticNameServers']

    def set_ilo_dns(self, oob_dns):
        request_url = "Managers/1/EthernetInterfaces/1"
        url = f"{self.basic_url}{request_url}"
        payload = {
            "StaticNameServers": oob_dns
            }
        resp = self.rest.call_restapi(url=url, method="PATCH", payload=payload, retry=self.retry)
        if resp.status_code == 200:
            self.logger.info("DNS config completed.")#TODO: change to ilog
        else:
            self.logger.error(f"DNS config failed: {resp.status_code}")

    def ilo_reset(self):
        request_url = "Managers/1/Actions/Manager.Reset"
        url = f"{self.basic_url}{request_url}"
        payload = {
            "ResetType": "GracefulRestart"
        }
        headers = {
            'Content-Type': "application/json;charset=UTF-8"
        }
        resp = self.rest.call_restapi(url=url, method="POST", payload=payload, headers= headers, retry=self.retry)
        if resp.status_code == 200:
            self.logger.info("iLO reset successful")
        else:
            self.logger.error(f"reset failed,statuscode: {resp.status_code}")

    def config_static_ip(self, address, subnet_mask, gateway):
        request_url = "Managers/1/EthernetInterfaces/1/"
        url = f"{self.basic_url}{request_url}"
        payload = {
            "IPv4Addresses": [{
                "Address": address,
                "SubnetMask": subnet_mask,
                "Gateway": gateway
            }]
        }
        resp = self.rest.call_restapi(url=url, method="PATCH", payload=payload, retry=self.retry)
        if resp.status_code == 200:
            self.logger.info("static IP config completed.")
        else:
            self.logger.error(f"set static ip failed: {resp.status_code}")
    
    def get_cert_info(self):
        self.logger.info("Getting certificate through redfish JSON api")
        request_url = "certificate"
        res, data = self.call_json_get(request_url=request_url)
        if res and data:
            return True, data
        return False, data

    def update_ilo_object(self, request_url, payload):
        url = f"{self.basic_url}{request_url}"
        try:
            resp = self.rest.call_restapi(
                url=url,
                method="PATCH",
                payload=payload,
                retry=self.retry
            )
            if resp.status_code == 200:
                self.logger.info("ILO object updated successfully")
                return True, resp.json()
            self.logger.error(f"Failed to update ILO object: status code {resp.status_code}")
            return False, resp.json()
        except Exception as e:
            self.logger.error(f"Failed to update ILO object at {url}: {str(e)}")
            return False, str(e)


class OneView():

    def __init__(self, pc, pe, label, vault, facility_type, logger=logging, retry=5):
        self.pc = pc
        self.pe = pe
        self.label = label
        self.vault = vault
        self.model_pc = ModelPrismCentral
        if facility_type == "warehouse":
            self.model_pc = ModelWarehousePrismCentral
        self.endpoint = ModelRetailNutanixOneview.query.filter_by(region = self.model_pc.query.filter_by(fqdn=self.pc).first().oneview_region).first().fqdn
        self.retry = retry
        _res, data = vault.get_secret(label)
        # from business.authentication.authentication import Vault
        # self.vault = Vault(tier="PRODUCTION")
        # _, data = self.vault.get_secret("A_Central-Production/HpeOneview")
        self.username = data['username']
        self.password = data['secret']
        self.logger = logger if logger else logging
        self.rest = CommonRestCall(username=self.username, password=self.password, logger=logger, timeout=30)
        self.session = self.create_session_with_token()
    
    def get_oneview_token(self):
        url = f"https://{self.endpoint}/rest/login-sessions"
        headers = { "X-API-Version" : "1400" }
        payload = {
                "userName" : self.username,
                "password" : self.password,
                "authLoginDomain" : "LOCAL",
                "loginMsgAck" : None
            }
        resp = self.rest.call_restapi(url=url, method="POST", payload=payload, headers=headers).json()
        return resp['sessionID']###Return Token
    
    def create_session_with_token(self):
        session_key = self.get_oneview_token()
        session = requests.Session()
        cookie = requests.cookies.create_cookie(domain=self.endpoint, name="sessionID", value=session_key)
        session.cookies.set_cookie(cookie)
        return session

    def get_oneview_session(self):
        url = f"https://{self.endpoint}/rest/sessions"
        headers = {"X-API-Version": "1400", "Session-ID": self.session.cookies["sessionID"]}
        response = self.session.get(url, headers=headers, verify=False)
        return response

    def get_oneview_scopes(self, oneview_scope):
        url = f"https://{self.endpoint}/rest/index/resources?category=scopes&start=0&count=10000"
        headers = {"X-API-Version" : "2000", "auth" : self.session.cookies["sessionID"]}
        response = self.session.get(url, headers=headers, verify=False).json()
        for item in response['members']:
            if item["name"] == oneview_scope:
                self.logger.info(f"URI: {item['uri']}")
                return response, item["uri"]
        self.logger.error(f"Scope {oneview_scope} not found in the response")
        raise  flaskex.InternalServerError(f"Scope {oneview_scope} not found in the response")

    def remove_oneview_server(self, uuid):
        url = f"https://{self.endpoint}/rest/server-hardware/{uuid}?force=true"
        headers = {"X-API-Version" : "2200", "auth" : self.session.cookies["sessionID"]}
        response = self.session.delete(url, headers=headers, verify=False)
        return response

    def add_oneview_server(self, server_name, scope_uri, ilo_user_name, ilo_password):
        self.logger.info(f'oneview_scope: {scope_uri}.')
        url = f'https://{self.endpoint}/rest/server-hardware'
        payload = {
                "hostname": server_name,
                "username": ilo_user_name,
                "password": ilo_password,
                "force": True,
                "licensingIntent": "OneViewStandard",
                "configurationState": "Monitored",
                "initialScopeUris": [scope_uri]
            }
        headers = {"X-API-Version": "1400", "auth" : self.session.cookies["sessionID"]}
        self.logger.info(f'I will add host {server_name} to OneView, wil need some times.')
        resp = self.rest.call_restapi(url=url, method="POST", payload=payload, headers=headers)
        return resp
    
    def get_oneview_servers_hardware(self):
        all_servers = [] 
        url = f'https://{self.endpoint}/rest/index/resources?category=server-hardware&start=0&count=100'
        next_url = url
        while next_url:
            headers = {
                "X-API-Version": "2000",
                "auth": self.session.cookies["sessionID"]
            }
            response = self.session.get(url, headers=headers, verify=False)
            servers = response.json() 
            all_servers.extend(servers.get('members', []))
            next_url = servers.get('nextPageUri')
            if next_url:
                url = f'https://{self.endpoint}{next_url}'
        return all_servers
    
    def get_server_from_oneview_by_uuid(self, uuid):
        url = f"https://{self.endpoint}/rest/server-hardware/{uuid}"
        headers = {"X-API-Version" : "5200", "auth" : self.session.cookies["sessionID"]}
        response = self.session.get(url, headers=headers, verify=False)
        return response
    
    # not done and if needed to be done.
    def get_device_data_for_server_from_oneview_by_uuid(self, uuid):

        url = f'https://{self.endpoint}/rest/server-hardware/{uuid}/devices'
        headers = {"X-API-Version" : "1200", "auth" : self.session.cookies["sessionID"]}
        servers = self.session.get(url, headers=headers, verify=False).json()
        return servers

    def get_oneview_servers_hardware_collector(self, start = None, fixed_amount_returns = False, oneview_url_with_less_bytes = False): # fetch size is so that one extra call to server is skipped..
        '''
        fixed_amount_returns is the amount of servers you want to get. If you want to get as many as possible at once set False.
        If url = True you will get: url = f'https://{self.endpoint}/rest/index/resources?category=server-hardware&start={start}&count=1000'
        else: you will get: url = f'https://{self.endpoint}/rest/server-hardware?start={start}&count=1000&expand=all
        '''

        def fetch_data(start):
            if fixed_amount_returns:
                fetch_size = fixed_amount_returns
                if fetch_size > 320:
                    # currently the max fetch size is 320
                    fetch_size = 320
            else:
                fetch_size = 320 # can have unlimited since fixed amount returns is not set
            if oneview_url_with_less_bytes:
                url = f'https://{self.endpoint}/rest/index/resources?category=server-hardware&start={start}&count={fetch_size}'#take out!!
            else:
                url = f'https://{self.endpoint}/rest/server-hardware?start={start}&count={fetch_size}&expand=all'
            headers = {"X-API-Version": "2000", "auth": self.session.cookies["sessionID"]}
            servers = self.session.get(url, headers=headers, verify=False).json()
            return servers

        all_servers = []

        # Initial fetch
        servers = fetch_data(start)
        count = servers["count"]
        start += count
        all_servers.extend(servers["members"])


        if fixed_amount_returns:
            if count < fixed_amount_returns:
                return all_servers
        elif count == 0:
            return all_servers


        if fixed_amount_returns:
            while not count < fixed_amount_returns:
                servers = fetch_data(start)
                count = servers["count"]
                start += count
                all_servers.extend(servers["members"])
        else:
            while count > 0:
                servers = fetch_data(start)
                count = servers["count"]
                start += count
                all_servers.extend(servers["members"])

        return all_servers
    
    def query(self, request_url, **filters):
        i = 0
        while i < self.retry:
            try:
                url = f"https://{self.endpoint}/rest/{request_url}"
                param = {"WHERE": ""}
                for key in filters:
                    if param['WHERE']:
                        param['WHERE'] += f" and {key}='{filters[key]}'"
                    else:
                        param['WHERE'] += f"{key}='{filters[key]}'"
                output = requests.get(url=url, auth=(self.username, self.password), params=param, verify=False)
                return output
            except Exception as e:
                i += 1
                self.logger.warning(f"We have some issue when querying ipam, {str(e)}")

    def get_ipam_object_by_fqdn(self, fqdn: str) -> requests.Response:
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_address_list?WHERE=name='{fqdn}'",
            "method": "GET",
        }
        res = self.rest.call_restapi(**kwargs)
        return res

    def get_subnets_by_parent_subnet_name(self, parent_subnet_name: str) -> requests.Response:
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_block_subnet_list?WHERE=parent_subnet_name='{parent_subnet_name}'",
            "method": "GET",
        }
        return self.rest.call_restapi(**kwargs)

    def get_subnets_by_subnet_id(self, subnet_id):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_block_subnet_list?WHERE=subnet_id%3D%27{subnet_id}%27",
            "method": "GET"
        }
        return self.rest.call_restapi(**kwargs)

    def ip_block_subnet_list(self, parent_subnet_id=None, parent_subnet_name=None, subnet_id=None, start_hostaddr=None):
        url = f"https://{self.endpoint}/rest/ip_block_subnet_list"
        if parent_subnet_id:
            url += f"?WHERE=parent_subnet_id='{parent_subnet_id}'"
        elif parent_subnet_name:
            url += f"?WHERE=parent_subnet_name='{parent_subnet_name}'"
        elif subnet_id:
            url += f"?WHERE=subnet_id%3D%27{subnet_id}%27"
        elif start_hostaddr:
            url += f"?WHERE=start_hostaddr%3D%27{start_hostaddr}%27"
        kwargs = {
            "url": url,
            "method": "GET"
        }
        return self.rest.call_restapi(**kwargs)


    def dns_rr_add(self, rr_type, fqdn, value, dns_name):
        kwargs = {
            "url": f"dns_rr_add?rr_name={fqdn}&rr_type={rr_type}&value1={value}&dns_name={dns_name}",
            "method": "POST"
        }
        return self.rest.call_restapi(**kwargs)

    def dns_rr_count(self, rr_type, rr_full_name):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/dns_rr_count?WHERE=rr_type='{rr_type}' and rr_full_name='{rr_full_name}'",
            "method": "GET"
        }
        return self.rest.call_restapi(**kwargs)

    def dns_rr_list(self, rr_type, rr_full_name):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/dns_rr_list?WHERE=rr_type='{rr_type}' and rr_full_name='{rr_full_name}'",
            "method": "GET"
        }
        return self.rest.call_restapi(**kwargs)

    def ip_find_free_address(self, subnet_id: str) -> requests.Response:
        kwargs = {
            "url": f"https://{self.endpoint}/rpc/ip_find_free_address",
            "method": "GET",
            "params": {"subnet_id": subnet_id}
        }
        return self.rest.call_restapi(**kwargs)

    def ip_add(self, hostaddr, site_id, fqdn, vm_name, workload_type, pe, description=""):
        # fqdn      retse995-nt8888.ikea.com
        # vm_name   retse995-nt8888
        if workload_type.lower() == 'linux':
            server_type = 'Linux'
        elif workload_type.lower() == 'windows':
            server_type = 'Windows'
        if re.match(r'.*-NXC', vm_name):
            ip_statement = "This is the Nutanix Cluster Record."
        elif re.match(r'.*-NXD', vm_name):
            ip_statement = f"This is the Nutanix Data Services IP Record for cluster :{pe}"
        elif re.match(r'.*-NXP', vm_name):
            ip_statement = f"This is the Nutanix Prism Central Record for cluster :{pe}"
        elif re.match(r'OOB', vm_name):
            ip_statement = f"This is an OOB record that belongs with Nutanix cluster :{pe}"
        elif re.match(r'CVM', vm_name):
            ip_statement = f"This is an CVM record that belongs with Nutanix cluster :{pe}"
        elif re.match(r'.*-NX7', vm_name):
            ip_statement = f"This is an AHV record that belongs with Nutanix cluster :{pe}"
        else:
            ip_statement = f"{description} This is a VM initially installed on Nutanix cluster :{pe}."
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_add",
            "method": "POST",
            "params": {
                "hostaddr": hostaddr,
                "site_id": int(site_id),
                "name": fqdn.lower(),
                "add_flag": "new_only",
                "ip_name_class": "IKEA/Server_Distribute",
                "ip_class_parameters": f"hostname={vm_name}&dns_update=1&__eip_dns_update_inheritance_property=set&ikea_server_type={server_type}&ikea_network_ip_statement={ip_statement}&ikea_in_pci=0"
            },
            "retry": 1
        }
        return self.rest.call_restapi(**kwargs)

    # def ip_add(self, ip_id, ip_class_parameters: str):
    #     kwargs = {
    #         "url": f"https://{self.endpoint}/rest/ip_add",
    #         "method": "PUT",
    #         "params": {
    #             "ip_id": ip_id,
    #             "ip_class_parameters": ip_class_parameters
    #         }
    #     }
    #     return self.rest.call_restapi(**kwargs)

    def ip_alias_add(self, ip_id: str, alias: str) -> requests.Response:
        # Can only add 1 alias once
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_alias_add",
            "method": "POST",
            "params": {
                "ip_name": alias,
                "ip_id": ip_id,
                "add_flag": "new_only",
            }
        }
        return self.rest.call_restapi(**kwargs)

    def ip_alias_count(self, ip_id):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_alias_count",
            "method": "GET",
            "params": {
                "ip_id": ip_id
            }
        }
        return self.rest.call_restapi(**kwargs)

    def ip_alias_list(self, ip_id):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_alias_list",
            "method": "GET",
            "params": {
                "ip_id": ip_id
            }
        }
        return self.rest.call_restapi(**kwargs)

    def ip_address_list(self, name=None, subnet_id=None, name_pattern=None, hostaddr=None):
        if name:
            url = f"https://{self.endpoint}/rest/ip_address_list?WHERE=name='{name}'"
        if subnet_id:
            url = f"https://{self.endpoint}/rest/ip_address_list?WHERE=subnet_id='{subnet_id}'"
        if name_pattern:
            url = f"https://{self.endpoint}/rest/ip_address_list?WHERE=name LIKE '{name_pattern}'"
        if hostaddr:
            url = f"https://{self.endpoint}/rest/ip_address_list?WHERE=hostaddr='{hostaddr}'"
        kwargs = {
            "url": url,
            "method": "GET",
        }
        return self.rest.call_restapi(**kwargs)

    def ip_address_info(self, ip_id):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/ip_address_info?ip_id={ip_id}",
            "method": "GET",
        }
        return self.rest.call_restapi(**kwargs)

    def ip_delete(self, ip_id=None, hostaddr=None, site_id=None):
        url = f"https://{self.endpoint}/rest/ip_delete?"
        if ip_id:
            url += f"ip_id={ip_id}"
        elif hostaddr and site_id:
            url += f"hostaddr={hostaddr}&site_id={site_id}"
        kwargs = {
            "url": url,
            "method": "DELETE"
        }
        return self.rest.call_restapi(**kwargs)

    def dns_server_list(self):
        kwargs = {
            "url": f"https://{self.endpoint}/rest/dns_server_list",
            "method": "GET",
        }
        return self.rest.call_restapi(**kwargs)


class FileDownloader:
    def __init__(self, data, file_format="csv"):
        self.data = data
        self.file_format = file_format

    def download(self):
        file_path = self.create_tmp_file()
        return file_path

    def create_tmp_file(self):
        # file_path = f"{SETTING.CSV_TMP_PATH}\\tmp_{int(datetime.timestamp(datetime.now()))}.{self.file_format}"
        file_path = os.path.join(SETTING.CSV_TMP_PATH, f"tmp_{int(datetime.timestamp(datetime.now()))}.{self.file_format}")
        df = pd.DataFrame(self.data)
        if self.file_format == "csv":
            df.to_csv(file_path, index=None)
        elif self.file_format == "excel":
            pass
        return file_path


def split_pe_into_parts(pe_name, add_to_digit_length=0):
    tmp_name = pe_name.split('-')[0]
    if len(tmp_name) == 6:                          # IT**SO
        bu = tmp_name[0:2]
        country_code = tmp_name[2:4]
        bu_code = tmp_name[4:]
    elif len(tmp_name) == 7:
        if bool(re.search(r"\d{4}", tmp_name)):      # RUS1008: R-US-1008
            bu = tmp_name[0]
            country_code = tmp_name[1:3]
            bu_code = tmp_name[3:]
        elif bool(re.search(r'\d{3}', tmp_name)):    # ITNL012: IT-NL-012
            bu = tmp_name[0:2]
            country_code = tmp_name[2:4]
            bu_code = tmp_name[4:]
        else:                                       # RET**SO: RET-**-SO
            bu = tmp_name[0:3]
            country_code = tmp_name[3:5]
            bu_code = tmp_name[5:]
    elif len(tmp_name) == 8:
        if re.search(r'\d{5}', tmp_name):    # RES12852: R-ES-12852
            bu = tmp_name[0]
            country_code = tmp_name[1:3]
            bu_code = tmp_name[3:]
        else:                               # RETDEBER: RET-DE-BER
            bu = tmp_name[0:3]
            country_code = tmp_name[3:5]
            bu_code = tmp_name[5:]
    if add_to_digit_length != 0:
        bu = bu[0]
        bu_code = f"{'0' * (add_to_digit_length - len(bu_code))}{bu_code}"
    return bu, country_code, bu_code


class CommonHelper:
    @staticmethod
    def parse_pe_name_and_fqdn(pe_name_or_fqdn):
        if not pe_name_or_fqdn or pe_name_or_fqdn == "NA":
            return None, None
        if re.search(r'\.(ikea|ikeadt|ikead2)\.com', pe_name_or_fqdn, re.IGNORECASE):    # "RETCN888-NXC000.ikea.com"
            pe_name = pe_name_or_fqdn.split(".")[0]
            pe_fqdn = pe_name_or_fqdn
        else:                                                                       # "RETCN888-NXC000"
            pe_name = pe_name_or_fqdn
            pe = ModelPrismElement.query.filter_by(name=pe_name_or_fqdn).first()
            if not pe:
                pe = ModelWarehousePrismElement.query.filter_by(name=pe_name_or_fqdn).one()
            pe_fqdn = pe.fqdn
        return pe_name, pe_fqdn

    @staticmethod # work only for prod
    def parse_pe_name_and_fqdn_collector(pe_or_fqdn):
        if not pe_or_fqdn:
            return None, None

        if re.search(r'\.(ikea|ikeadt|ikead2)\.com', pe_or_fqdn, re.IGNORECASE):    # "RETCN888-NXC000.ikea.com"
            pe_name = pe_or_fqdn.split(".")[0]
            pe_fqdn = pe_or_fqdn

        else:                                                                   # "RETCN888-NXC000"
            pe_name = pe_or_fqdn
            pe_fqdn = CommonHelper.nslookup(pe_name) # Replacing db connection with socket module only work for prod...

        return pe_name, pe_fqdn

    @staticmethod
    def nslookup(domain_name):
        import socket
        try:
            ip_address = socket.gethostbyname(domain_name)
            values = socket.gethostbyaddr(ip_address)
            # Close the socket explicitly after use to release system resources
            socket.socket(socket.AF_INET, socket.SOCK_STREAM).close()            
            return values[0]
        except Exception:         
            return None


# Database configuration constants
DB_URI_SCHEMA = r"mssql+pyodbc://{db_user}:{db_pw}@{db_host}/{db_name}?driver={db_drive}"


class DBConfig:
    def __call__(self):
        # Get environment variables
        dpc_db_host = os.environ.get("DPC_DB_HOST")
        dpc_db_user = os.environ.get("DPC_DB_USER")
        dpc_db_pw = os.environ.get("DPC_DB_PW")
        dpc_db_name = os.environ.get("DPC_DB_NAME")
        dpc_db_drive = os.environ.get("DPC_DB_DRIVE")

        # If all environment variables are set, use them
        if all([dpc_db_host, dpc_db_user, dpc_db_pw, dpc_db_name, dpc_db_drive]):
            return DB_URI_SCHEMA.format(
                db_user=dpc_db_user,
                db_pw=dpc_db_pw,
                db_host=dpc_db_host,
                db_name=dpc_db_name,
                db_drive=dpc_db_drive
            )

        # Try to get from DB_CONN.py if it exists
        db_conn_path = os.path.join(application_path, 'static', 'DB_CONN.py')
        if os.path.isfile(db_conn_path):
            from static.DB_CONN import (
                DPC_DB_HOST, DPC_DB_USER, DPC_DB_PW, DPC_DB_NAME, DPC_DB_DRIVE
            )  # pylint: disable=C0415
            dpc_db_host = os.environ.get("DPC_DB_HOST", DPC_DB_HOST)
            dpc_db_user = os.environ.get("DPC_DB_USER", DPC_DB_USER)
            dpc_db_pw = os.environ.get("DPC_DB_PW", DPC_DB_PW)
            dpc_db_name = os.environ.get("DPC_DB_NAME", DPC_DB_NAME)
            dpc_db_drive = os.environ.get("DPC_DB_DRIVE", DPC_DB_DRIVE)
            return DB_URI_SCHEMA.format(
                db_user=dpc_db_user,
                db_pw=dpc_db_pw,
                db_host=dpc_db_host,
                db_name=dpc_db_name,
                db_drive=dpc_db_drive
            )

        raise Exception("DB configuration not found. Please set environment variables or provide DB_CONN.py file.")

#cloud-config

# Set hostname and FQDN
hostname: @@{VM_Name}@@
fqdn: @@{VM_Fqdn}@@
manage_etc_hosts: false
ssh_pwauth: true
write_files:
  - path: /etc/sysconfig/network-scripts/ifcfg-ens3
    content: |
      # Static IP Configured
      DEVICE=ens3
      NAME=ens3
      TYPE=Ethernet
      BOOTPROTO=static
      IPADDR=@@{VM_IP}@@
      NETMASK=@@{VM_SubnetMask}@@
      GATEWAY=@@{VM_GW}@@
      ONBOOT=yes
      DOMAIN=@@{VM_DnsDomain}@@
      DNS1=@@{VM_DNS1}@@
      DNS2=@@{VM_DNS2}@@
  - path: /etc/sysconfig/network
    content: |
      NETWORKING=yes
      NETWORKING_IPV6=no

runcmd:
  - for i in $(nmcli con | awk {'print $2'} | grep -v UUID); do echo nmcli con del $i; done
  - nmcli con reload
  - nmcli con up ens3
  - touch /etc/cloud/cloud-init.disabled
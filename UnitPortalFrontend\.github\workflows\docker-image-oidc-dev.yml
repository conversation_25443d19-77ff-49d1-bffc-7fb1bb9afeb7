name: Docker Build DHUP Frontend OIDC dev

on: workflow_dispatch

permissions:
  id-token: write
  contents: read

jobs:
  Build_and_push_image_to_Artifactory:
    runs-on: mgke-prod
    steps:
      - name: Checkout local repository
        uses: actions/checkout@v4
        with:
          ref: 'dev_containerizing'
          clean: 'true'
          fetch-tags: 'true'
      
      - name: Setup Node npm
        uses: actions/setup-node@v3
      
      - name: Setup JFrog CLI
        uses: jfrog/setup-jfrog-cli@v4
        id: login
        env:
          JF_URL: https://artifactory.build.ingka.ikea.com
        with:
          oidc-provider-name: "ghes-prod"
          oidc-audience: "jfrog-github"

      - name: Login to Artifactory
        uses: docker/login-action@v3
        with:
          registry: artifactory.build.ingka.ikea.com
          username: ${{ steps.login.outputs.oidc-user }}
          password: ${{ steps.login.outputs.oidc-token }}
          
      - name: Push to artifactory
        uses: docker/build-push-action@v6
        with:
          push: true
          tags: artifactory.build.ingka.ikea.com/distributedhostingcodecommunity-dhup-docker-dev-local/dhup-frontend-test:latest
          context: .

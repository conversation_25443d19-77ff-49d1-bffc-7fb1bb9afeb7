import json
import re
import sys
import time
import traceback
from json import JSONDecodeError
import werkzeug.exceptions as flaskex
from datetime import datetime, timezone
from dateutil import tz
import pytz

from business.authentication.authentication import Vault
from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.automation.automation import Automation
from business.distributedhosting.nutanix.automation.dsc_exception import RemoteSiteInUse, OneViewServerAddFailed
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.distributedhosting.nutanix.nutanix import PrismElement, PrismCentral, NutanixCLI
from business.distributedhosting.nutanix.pe_components import RestAuthConfig, StorageContainer, RestSMTP, RestHA, \
    RestCluster, RestEulas, RestPulse, RestGenesis, RestProgressMonitor, RestRemoteSite
from business.distributedhosting.nutanix.protection_domain import ProtectionDomain
from business.generic.base_up_exception import <PERSON><PERSON>ith<PERSON><PERSON>r, TaskSkipped, VaultGetSecretFailed
from business.generic.commonfunc import <PERSON><PERSON><PERSON><PERSON>, <PERSON>fish, SSHConnect
from business.generic.ipam import <PERSON>pam
from business.loggings.loggings import IntegratedLogger
from static.SETTINGS import DSC_LOG_PATH
from business.generic.ipam_api import IpamAPI
from models.ntx_models import ModelPrismElement, ModelRetailNutanixHost
from models.ntx_models_wh import ModelWarehousePrismElement
from models.atm_models import ModelNtxAutomationDscTask, ModelNtxAutomationDscTaskSchema, \
    ModelNtxAutomationDscTaskLog, ModelNtxAutomationDscTaskLogSchema
from business.benchmark.benchmark import Benchmark
from business.authentication.authentication import ServiceAccount


class DesiredStateConfig(BaseUpTask):
    LOG_DIR = DSC_LOG_PATH
    LOG_TYPE = "DSC"
    TASK_TYPE = "DSC"

    def __init__(self, pe, facility_type):
        super().__init__(
            ModelNtxAutomationDscTask, ModelNtxAutomationDscTaskSchema,
            ModelNtxAutomationDscTaskLog, ModelNtxAutomationDscTaskLogSchema
        )
        self.pe = pe
        self.facility_type = facility_type.lower()
        self.task_identifier = self.pe
        self.task_duplicated_kwargs = {"pe": self.pe}
        self.task_info = {"pe": self.pe}

    def task_process(self):
        if self.facility_type == FacilityType.RETAIL:
            configs = [
                DscMaintenanceMode,
                DscIpamHealth,
                DscCvmRam,
                DscVmRbac,
                DscStorageConfig,
                DscSmtp,
                DscDnsNtp,
                DscHa,
                DscAuthConfig,
                DscAhvHostname,
                DscCvmHostname,
                DscOob,
                DscPulse,
                DscSyncBackupSettings,
            ]
        if self.facility_type == FacilityType.WAREHOUSE:
            configs = [
                DscMaintenanceMode,
                DscIpamHealth,
                DscCvmRam,
                DscVmRbac,
                DscStorageConfig,
                DscSmtp,
                DscDnsNtp,
                DscHa,
                DscAuthConfig,
                DscAhvHostname,
                DscCvmHostname,
                DscOob,
                DscPulse,
            ]
        done_with_error = False
        ignore_error = True     # TODO
        for c in configs:
            try:
                step = c.__name__.split('Dsc')[1]
                self.ilg.write(f"Configuring {step}...", severity="title")
                c(self.pe, self.logger, self.db_logger, self.facility_type).configure()
                self.ilg.write(f"{step}: Done")
            except TaskSkipped as e:
                raise e
            except Exception as e:
                if not ignore_error:
                    raise e
                error_msg = str(repr(traceback.format_exception(sys.exception())))
                self.ilg.write(error_msg, severity='error')
                continue_msg = "Failed on current step, but will continue the other steps..."
                self.ilg.write(continue_msg, severity='warning')
                done_with_error = True
        if done_with_error:
            raise DoneWithError()


class DscBase:
    """
    The Base class for Dsc classes, providing some basic functions
    """
    def __init__(self, pe, logger, db_logger, facility_type):
        self.pe = pe
        self.logger = logger
        self.db_logger = db_logger
        self.ilg = IntegratedLogger(file_lg=logger, db_lg=db_logger)
        self.facility_type = facility_type.lower()
        if self.facility_type == FacilityType.RETAIL:
            self.pe_model = ModelPrismElement
        if self.facility_type == FacilityType.WAREHOUSE:
            self.pe_model = ModelWarehousePrismElement
        cluster_info = self.pe_model.query.filter_by(fqdn=self.pe).first()
        self.benchmark = Benchmark().get_bmk_by_id(bmk_id=cluster_info.bmk_id, real_fw_rules=True)
        if not self.benchmark:
            raise flaskex.InternalServerError(f"Cannot find benchmark for {self.pe}!")
        self.cluster_info = cluster_info
        self.dns_zone = self.benchmark['systems']['dns_zone']
        self.pc = f"{self.benchmark['systems']['self_service_name']}.{self.benchmark['systems']['dns_zone']}"
        self.pc_ip = self.benchmark['systems']['pc_cluster_ip']
        self.desired_state = self.benchmark['desire_state']['config_items']
        self.vault = Vault(tier=self.benchmark['tier'], \
                           sa=ServiceAccount(usage=self.benchmark['vault']['service_account']).get_service_account(), \
                           engine=self.benchmark['vault']['engine'], \
                           namespace=f"{self.benchmark['vault']['master_namespace']}/{self.benchmark['vault']['tier_namespace']}", \
                           url=self.benchmark['endpoint']['endpoints']['vault']['endpoint'])
        self.is_central_pe = cluster_info.is_central_pe
        self.pe_svc_account, self.pc_svc_account = self.init_svc_account(cluster_info)
        self.rest_pe = PrismElement(self.pe, self.pe_svc_account, self.logger)
        self.public_key = self.vault.generate_pubkey_from_privkey(
            f"{self.pe.split('.')[0].upper()}/{self.benchmark['vault']['site_labels']['site_gw_priv_key']}")

    def init_svc_account(self, cluster_info):
        pe_svc_label = f"{cluster_info.name}/{self.benchmark['vault']['site_labels']['site_pe_svc']}"
        pc_svc_label = f"{cluster_info.name}/{self.benchmark['vault']['central_labels']['site_pc_svc']}"
        res, data = self.vault.get_secret(pe_svc_label)
        if not res:
            raise VaultGetSecretFailed(pe_svc_label)
        pe_svc_account = {"username": data["username"], "password": data["secret"]}
        pc_svc_account = None
        if self.is_central_pe:
            res, data = self.vault.get_secret(pc_svc_label)
            if not res:
                raise VaultGetSecretFailed(pc_svc_label)
            pc_svc_account = {"username": data["username"], "password": data["secret"]}
        return pe_svc_account, pc_svc_account

    @staticmethod
    def is_config_enabled(config):      # pylint: disable=W0613
        return config == 1 or config == "1"
        # return True

    def check_if_enabled(self, config):
        enabled = DscBase.is_config_enabled(self.desired_state[config])
        if not enabled:
            self.logger.info(f"Desired State '{config}' has been disabled inside this site profile.")
            return

    def match_output(self, _conn, desired_output, retry_interval=10, retries=12):
        # FOR SSH interact usage
        for i in range(retries):
            self.logger.info(f"Getting output... '{i}' retry.")
            _, output = _conn.recv()
            if re.match(desired_output, output):
                self.logger.info(f"Got the desired output {desired_output}!")
                return
            self.logger.info(f"Sleep {retry_interval}s..")
            time.sleep(retry_interval)
        raise flaskex.InternalServerError(f"Failed to match desired output from output! Last output: {output}")


class DscIpamBase(DscBase):
    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        self.ipam = Ipam(logger=logger)
        self.ahv_cvm_subnet_params = None
        self.nx7x0x_prefix = self.pe.split('-')[0] + r"-NX700\d{1}"
        # NXC000 -> NX700x, NXC001 -> NXC710x, NXC002 -> NXC720x, ...
        number = self.pe.split('-')[1].split('.')[0].lower().split('nxc00')[1]
        self.nx7x0x_prefix = self.pe.split('-')[0] + f"-NX7{number}" + r"0\d{1}"

    def get_ahv_cvm_subnet(self):
        ipam_obj = self.ipam.rest_ipam.ip_address_list(self.pe).json()
        if not ipam_obj:
            raise flaskex.InternalServerError(f"Can't find '{self.pe}' on IPAM!")
        subnet_id = ipam_obj[0]["subnet_id"]
        ahv_cvm_subnet = self.ipam.rest_ipam.get_subnets_by_subnet_id(subnet_id).json()
        self.ahv_cvm_subnet = ahv_cvm_subnet
        self.logger.info(f"AHV / CVM Subnet: '{subnet_id}' finding the rest.")
        if not ahv_cvm_subnet:
            raise flaskex.InternalServerError("AHV / CVM Subnet cannot be found. Panic!")
        if len(ahv_cvm_subnet) > 1:
            raise flaskex.InternalServerError("More than 1 subnet found.")
        # if not ahv_cvm_subnet or not oob_subnet:
        #     raise flaskex.InternalServerError("We cannot find the AHV / CVM or OOB Subnets in IPAM, whats up doc?")
        return ahv_cvm_subnet[0]["subnet_class_parameters"].split('&')

    def get_oob_subnet_vlan(self):
        pass

    # def get_oob_network_by_pe(self):
    #     oob_subnet = self.find_subnet_by_vlan(
    #         type="Oob", vlan_id=self.deployment["OOBVLAN"], parent_subnet=self.ahv_cvm_subnet["parent_subnet_name"])
    #     self.oob_subnet = oob_subnet

    # def find_subnet_by_vlan(self, type, vlan_id, parent_subnet_name):
    #     self.logger.title(f"Locating '{type}' Vlan")
    #     child_subnet = []
    #     if len(self.deployment[f"{type}NetworkAddress"]) >= 5:
    #         # TODO
    #         nw = self.deployment[f"{type}NetworkAddress"]
    #         self.logger.info(f"This Deployment Profile has a hard coded deployment network '{nw}'")
    #         child_subnet = self.ipam.rest_ipam.ip_block_subnet_list(start_hostaddr=nw)
    #     else:
    #         subnets = self.ipam.rest_ipam.ip_block_subnet_list(parent_subnet_name=parent_subnet_name).json()
    #         for item in subnets:
    #             if item["vlmvlan_vlan_id"] == vlan_id:
    #                 child_subnet.append(item)
    #                 break
    #     if not child_subnet:
    #         raise flaskex.InternalServerError(f"Can't find subnet with vlan_id {vlan_id}")
    #     if len(child_subnet) > 1:
    #         # TODO
    #         pass
    #     return child_subnet[0]

    # def get_ahv_cvm_detail(self):
    #     self.logger.title("Retrieving Cluster Reservation")
    #     ahv_cvm_records = self.ipam.rest_ipam.get_subnets_by_subnet_id(self.ahv_cvm_subnet["subnet_id"])
    #     cluster_record = [r for r in ahv_cvm_records if r.lower().startswith(self.pe.lower())]
    #     if len(cluster_record) > 1:
    #         raise flaskex.InternalServerError(f"This IPAM is a mess, we found '{len(cluster_record)}' records.")
    #     elif not cluster_record:
    #         raise flaskex.InternalServerError("Cluster Record is not found.")
    #     ahv_pattern = self.pe.split('-')[0] + r"-NX7\d+\.ikea\.com"
    #     ahv_nodes = [n for n in ahv_cvm_records if re.match(ahv_pattern, n["name"], re.IGNORECASE)]
    #     cvm_pattern = self.pe.split('-')[0] + r"-NX7\d+cvm\.ikea\.com"
    #     cvm_nodes = [n for n in ahv_cvm_records if re.match(cvm_pattern, n["name"], re.IGNORECASE)]
    #     oob_records = self.ipam.rest_ipam.get_subnets_by_subnet_id(self.oob_subnet["subnet_id"])
    #     oob_pattern = self.pe.split('-')[0] + r"-NX7\d+oob\.ikea\.com"
    #     oob_nodes = [n for n in ahv_cvm_records if re.match(oob_pattern, n["name"], re.IGNORECASE)]
    #     if not oob_nodes:
    #         raise flaskex.InternalServerError("We cannot find the OOB Records in IPAM.")

    def get_infra_dns_servers(self):
        infra_dns_servers = self._get_value_from_ahv_cvm_subnet_params("ikea_infra_dns_1=") + self._get_value_from_ahv_cvm_subnet_params("ikea_infra_dns_2=")
        if not infra_dns_servers:
            self.logger.warning("This subnet does not have Infra DNS servers, Running NSB will cause problems on this site.")
            infra_dns_servers = self._get_value_from_ahv_cvm_subnet_params("_dns_server")
        return infra_dns_servers

    def get_dns_servers(self):
        dns_servers = self._get_value_from_ahv_cvm_subnet_params("ikea_dns_server=") + self._get_value_from_ahv_cvm_subnet_params("ikea_dns_server_2=")
        if not dns_servers:
            dns_servers = self._get_value_from_ahv_cvm_subnet_params("_dns_server")
        return dns_servers

    def get_ntp_servers(self):
        ntp_servers = self._get_value_from_ahv_cvm_subnet_params("ntp_server")[0].replace("%20", "").replace("%2C", ",")
        return ntp_servers.split(",")

    def _get_value_from_ahv_cvm_subnet_params(self, key):
        result = []
        for param in self.ahv_cvm_subnet_params:
            if key in param:
                value = param.split('=')[1]
                if value:       # to avoid the empty value
                    result.append(value)
        return result

    def get_cvm_objects_on_ipam(self):
        pattern = f"{self.nx7x0x_prefix}cvm.{self.dns_zone}"
        return self.get_objects_by_pattern(pattern)

    def get_ahv_objects_on_ipam(self):
        pattern = f"{self.nx7x0x_prefix}.{self.dns_zone}"
        return self.get_objects_by_pattern(pattern)

    def get_oob_objects_on_ipam(self):
        pattern = f"{self.nx7x0x_prefix}oob.{self.dns_zone}"
        ilo_subnet_id = self.get_ilo_subnet_id()
        objects_under_ilo = self.ipam.rest_ipam.ip_address_list(subnet_id=ilo_subnet_id).json()
        object_list = []
        for o in objects_under_ilo:
            if re.match(pattern, o["name"], re.IGNORECASE):
                object_list.append(o)
        return object_list

    def get_ilo_subnet_id(self):
        pe_ipam_object = self.ipam.rest_ipam.get_ipam_object_by_fqdn(self.pe).json()
        if not pe_ipam_object:
            raise flaskex.InternalServerError(f"Can't find PE '{self.pe}' host on IPAM!")
        parent_subnet_id = pe_ipam_object[0]["parent_subnet_id"]
        if self.benchmark['deployment']['oob_network_address']:
            subnets_under_parent_subnet = self.ipam.rest_ipam.ip_block_subnet_list(
                start_hostaddr=self.benchmark['deployment']['oob_network_address']).json()
        else:
            subnets_under_parent_subnet = self.ipam.rest_ipam.ip_block_subnet_list(parent_subnet_id=parent_subnet_id).json()
        for s in subnets_under_parent_subnet:
            # if s["vlmvlan_name"] == "NutanixILO-FW":
            if int(s["vlmvlan_vlan_id"]) == int(self.benchmark['vlan_config']['oob_vlan']):
                return s["subnet_id"]
        self.ilg.write(
            f"Can't find vlan_id '{self.benchmark['vlan_config']['oob_vlan']}' under subnet_id {parent_subnet_id}, "
            "but we will do another try...",
            severity="warning"
        )
        network_pattern = f"%{self.pe.split('-')[0]}-%oob%"
        oob_object_list = self.ipam.rest_ipam.ip_address_list(name_pattern=network_pattern).json()
        if not oob_object_list:
            raise flaskex.InternalServerError(f"Can't find network by name pattern {network_pattern}!")
        self.ilg.write(f"Succeeded to find the network by name pattern {network_pattern}")
        return oob_object_list[0]["subnet_id"]

    def get_objects_by_pattern(self, pattern):
        # Get objects under the same subnet with pe, and filter required ones by pattern
        pe_ipam_object = self.ipam.rest_ipam.get_ipam_object_by_fqdn(self.pe).json()
        if not pe_ipam_object:
            raise flaskex.InternalServerError(f"Can't find PE '{self.pe}' host on IPAM!")
        subnet_id = pe_ipam_object[0]["subnet_id"]
        objects = self.ipam.rest_ipam.ip_address_list(subnet_id=subnet_id).json()
        object_list = []
        for o in objects:
            if re.match(pattern, o["name"], re.IGNORECASE):
                object_list.append(o)
        return object_list


class DscIpamHealth(DscIpamBase):
    """Ipam health"""
    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        self.oob_objects = self.get_oob_objects_on_ipam()
        self.cvm_objects = self.get_cvm_objects_on_ipam()
        self.ahv_objects = self.get_ahv_objects_on_ipam()

    def configure(self):
        self.check_ipam_health()
        self.check_ipam_integration()

    def check_ipam_health(self):
        self.ilg.write("Checking IPAM Health")
        _, host_list = self.rest_pe.get_host_list()
        real_ahv_hosts_count = len(host_list)
        ipam_cvm_count = len(self.cvm_objects)
        ipam_ahv_count = len(self.ahv_objects)
        ipam_oob_count = len(self.oob_objects)
        if ipam_oob_count != real_ahv_hosts_count or \
                ipam_cvm_count != real_ahv_hosts_count or \
                ipam_ahv_count != real_ahv_hosts_count:
            raise flaskex.InternalServerError(
                f"Node records on IPAM and NTX don't match! Actual hosts: {real_ahv_hosts_count}, "
                f"Oob records: {ipam_oob_count}, Ahv records: {ipam_ahv_count}, Cvm records: {ipam_cvm_count}"
            )
        self.ilg.write("Node records on IPAM and NTX match.")

    def check_ipam_integration(self):
        self.ilg.write("Checking IPAM integration...")
        objects = self.oob_objects + self.cvm_objects + self.ahv_objects
        self.ilg.write(f"Let's check '{len(objects)}' IP records, DNS update status")
        for o in objects:
            self.logger.info(f"Pulling Ipam record with IP {o['hostaddr']}")
            status = Ipam.get_value_from_class_parameters("dns_update", o["ip_class_parameters"])
            if int(status) != 0:
                self.logger.info("This record is already enabled.")
                continue
            self.logger.info("This record does not have its DNS Flag enabled.")
            self.logger.info("Updating this record.")
            self.ipam.rest_ipam.ip_edit(o["ip_id"], "dns_update=1&__eip_dns_update_inheritance_property=set")
        self.ilg.write("Records are all enabled.")


class DscMaintenanceMode(DscBase):
    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        self.ntx_cli = NutanixCLI(pc=self.pc, pe=self.pe, logger=self.logger, vault=self.vault)

    def configure(self):
        in_maintenance = self.check_maintenance_mode_api()
        if in_maintenance:
            raise TaskSkipped("Hosts are in Maintenance, skipping this dsc task...")
        self.check_cvm_status()

    def check_maintenance_mode(self):
        self.ilg.write("Checking Maintenance Mode")
        host_list = self.ntx_cli.get_ncli_host()["data"]
        for host in host_list:
            if host["hypervisorState"] != "kAcropolisNormal":
                self.ilg.write("Some hosts are set as NCLI maintenance mode...", "error")
                return True
        self.ilg.write("All good, no hosts are set as NCLI maintenance inside this cluster.")

        host_list = self.ntx_cli.get_acli_host()["data"]
        for host in host_list:
            if 'node_state' in host:
                self.logger.info("This seems a very old AOS version...")
                if not host.get("schedulable"):  # 'schedulable' == False
                    self.ilg.write("Some hosts are set as ACLI maintenance mode...", "error")
                    return True
            elif host.get("node_state") != "AcropolisNormal":
                self.ilg.write("Some hosts are set as ACLI maintenance mode...", "error")
                return True
        self.ilg.write("All good, no hosts are set as ACLI maintenance inside this cluster.")
        return False

    def check_cvm_status(self):
        self.ilg.write("Checking CVM status")
        pe_name = self.pe.split('.')[0]
        _res, ssh_pass = self.vault.get_secret(f"{pe_name.upper()}/{self.benchmark['vault']['site_labels']['site_pe_nutanix']}")
        ssh_connect = SSHConnect(self.pe, self.benchmark['vault']['site_labels']['site_pe_nutanix_username'], ssh_pass['secret'], self.public_key, 1, self.logger)
        ssh_connect.connect_by_pwd_or_key()
        ssh_connect.invoke_shell()
        time.sleep(5)
        command = "cluster status |grep -v UP"
        ssh_connect.send_command(command)
        time.sleep(15)
        stdout = ssh_connect.receive_output()
        matches = re.findall(r'CVM: (\d+\.\d+\.\d+\.\d+) (\w+)', stdout)
        if not matches:
            raise flaskex.InternalServerError("Cluster status is wrong.")
        for match in matches:
            self.logger.info(f"CVM IP:{match[0]} Status:{match[1]}")
            if match[1] != "Up":
                raise flaskex.InternalServerError("Not all CVM status are Up")
        self.logger.info("Great, all CVM status are Up")

    def check_maintenance_mode_api(self):
        """
        Checks host and CVM maintenance mode using the Nutanix v2.0 API.
        """
        self.ilg.write("Checking Maintenance Mode via v2.0 API (/hosts)")
        # self.rest_pe.rest_pe is the NutanixAPI object.
        # We use call_pe_get, specifying api_version=2.
        res, data = self.rest_pe.rest_pe.call_pe_get("/hosts", api_version=2)
        if not res:
            self.ilg.write(f"Failed to get host list from Prism Element via v2.0 API: {data}", severity="error")
            return None # Return a distinct value for failure
        for host in data.get('entities', []):
            host_name = host.get('name', 'Unknown Host')
            # 1. Check CVM maintenance mode status from the v2.0 API field
            cvm_in_maintenance = host.get('host_in_maintenance_mode', False)
            if cvm_in_maintenance:
                self.ilg.write(f"API Check: CVM on host '{host_name}' is in maintenance mode (host_in_maintenance_mode: True).", "error")
                return True
            # 2. Check AHV (hypervisor) maintenance mode status from the v2.0 API field
            hypervisor_state = host.get('hypervisor_state', 'UNKNOWN')
            if hypervisor_state != 'kAcropolisNormal':
                self.ilg.write(f"API Check: Hypervisor on host '{host_name}' is not in a normal state (hypervisor_state: {hypervisor_state}).", "error")
                return True
        self.ilg.write("API Check: All good, no hosts or CVMs are in maintenance mode.")
        return False


class DscPeAvailable(DscBase):
    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        pc = self.pe_model.get_prism_by_pe_name(self.pe)
        self.rest_pc = PrismCentral(pc, logger=self.logger)

    def set_pe_availability(self):
        is_available = self.check_pe_available()
        if is_available:
            self.logger.info("PE is available, no action needed.")
            return
        self.reset_pe_pc_remote_connection()
        self.logger.info("Sleep 300s to wait the reconnection take effect...")
        # time.sleep(300)
        # is_available = self.check_pe_available()
        # if not is_available:
        #     raise flaskex.InternalServerError("Connection status is still invalid!")

    def check_pe_available(self):
        self.logger.info(f"Checking if PE '{self.pe}' is available...")
        is_pe_available = self.rest_pc.check_pe_reachable(self.pe)
        self.logger.info(f"Connection status: {is_pe_available}")
        return is_pe_available

    def reset_pe_pc_remote_connection(self):
        username, secret = self.vault.get_secret(
            f"{self.pe.upper()}/{self.benchmark['vault']['site_labels']['site_pe_nutanix']}"
        )
        self.logger.info("Start to reset PE PC remote connection...")
        host = f"{self.pe.split('-')[0]}-NX7001CVM.IKEA.COM"
        _conn = SSHConnect(host=host, username=username, password=secret, retry=1, logger=self.logger)
        _conn.connect_by_pwd_or_key()
        # stdin, stdout, stderr = ssh.exec_command("ncli  multicluster get-cluster-state")
        _conn.invoke_shell()
        _conn.send_command("nuclei remote_connection.reset_pe_pc_remoteconnection")
        self.logger.info("Sleep 30 sec for the output...")
        time.sleep(30)
        output = _conn.receive_output()
        if "PE-PC remote connection reset successfully completed" not in output:
            raise flaskex.InternalServerError(f"Failed to reset PE PC remote connection! Last output: {output}")

        _conn.send_command("allssh 'genesis stop acropolis anduril aplos aplos_engine mercury'")
        self.logger.info("Sleep 50 sec for the output...")
        time.sleep(50)
        _conn.send_command('allssh "genesis start"')
        # self.match_output(_conn, r"(.|\n)*Genesis is already running(.|\n)*")
        time.sleep(30)
        _conn.invoke_shell()
        _conn.send_command("cluster start")
        self.match_output(_conn, r"(.|\n)*MainThread cluster\:\d+ Success(.|\n)*")

    def match_output(self, _conn, desired_output, retry_interval=10, retries=12):
        for i in range(retries):
            self.logger.info(f"Getting output... '{i}' retry.")
            _, output = _conn.recv()
            if re.match(desired_output, output):
                self.logger.info(f"Got the desired output {desired_output}!")
                return
            self.logger.info(f"Sleep {retry_interval}s..")
            time.sleep(retry_interval)
        raise flaskex.InternalServerError(f"Failed to match desired output from output! Last output: {output}")


class DscCvmRam(DscIpamBase):
    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        self.rest_genesis = RestGenesis(self.pe, self.pe_svc_account, self.logger)

    def configure(self):
        self.check_if_enabled("cvm_ram")
        self.logger.info("Checking CVM RAM Desired state...")
        ntx_cvm_nodes = self._check_cvm_ram()
        ipam_cvm_nodes = self.get_cvm_objects_on_ipam()
        if not self._cvm_need_update(ipam_cvm_nodes, ntx_cvm_nodes):
            self.ilg.write("CVM RAM Desired state already met.")
            return
        self.ilg.write("Updating CVM ram...")
        self._update_cvm_ram()
        time.sleep(5)
        self.ilg.write("Checking update result...")
        self._monitor_task(node_count=len(ntx_cvm_nodes))

    def _cvm_need_update(self, ipam_cvm_nodes, ntx_nodes):
        desired_cvm_ram_gb = self.benchmark['deployment']['cvm_ram_gb']
        for cvm in ipam_cvm_nodes:
            ip = cvm["hostaddr"]
            if not ntx_nodes.get(ip):
                self.ilg.write(f"Can't get CVM info with IP '{ip}', skip this CVM...", severity="warning")
                continue
            current_ram = ntx_nodes[ip]["memory"]
            if current_ram < desired_cvm_ram_gb:
                self.logger.info(f"This node '{ip}' has less ram than desired, correction required.")
                return True
        return False

    def _check_cvm_ram(self):
        current_config = self.rest_genesis.call_genesis({
            ".oid": "ClusterManager",
            ".method": "get_cluster_cvm_params_map",
            ".kwargs": {}
        })
        ntx_nodes = json.loads(current_config['value'])[".return"]
        return ntx_nodes

    def _update_cvm_ram(self):
        self.rest_genesis.call_genesis({
            ".oid": "ClusterManager",
            ".method": "reconfig_cvm",
            ".kwargs": {
                "cvm_reconfig_json": {
                    "target_memory_in_gb": self.benchmark['deployment']['cvm_ram_gb']
                }
            }
        })

    def _monitor_task(self, node_count):
        # TODO: filter last 1 day tasks
        rest = RestProgressMonitor(self.pe, self.pe_svc_account, self.logger)
        result = rest.list_progress(params="hasSubTaskDetail=false&count=500&page=1&filterCriteria=internal_task==false;(display_failures%3D%3D%5Bno_val%5D%2Cdisplay_failures%3D%3Dtrue%2C(display_failures%3D%3Dfalse%3Bstatus!%3DkFailed))%3B(status%3D%3DkRunning)")["entities"]
        running_tasks = [e for e in result if e["status"] == "Running" and e["operation"] == "Cvmreconfig"]
        if running_tasks:
            self.ilg.write("CVM Config task is running.")
            self.ilg.write(f"This task requires a rolling restart of all '{node_count}' CVMs, This will take a while.")
            minutes = node_count * 10
            self.ilg.write(f"Sleeping {minutes} minutes...")
            time.sleep(minutes*60)


class DscVmRbac(DscBase):

    def configure(self):
        if not self.is_central_pe:
            self.ilg.write("This is not a Central Site, This step is desired to only run on central sites, skip...")
            return
        self.check_if_enabled("role_vm_rbac")
        atm = Automation(pc=self.pc, logger=self.logger, sa=self.pc_svc_account)
        atm.dsc_acp()


class DscHa(DscBase):
    def configure(self):
        self.check_if_enabled("ha_reservation")
        _, ahv_hosts = self.rest_pe.get_host_list()
        node_number = len(ahv_hosts)
        if node_number < 2:
            self.ilg.write(f"HA cannot be enabled on '{node_number}' node clusters.")
            return
        rest_ha = RestHA(self.pe, self.pe_svc_account, self.logger)
        ha_status = rest_ha.get_config().get("num_host_failures_to_tolerate")
        if ha_status >= 1:
            self.ilg.write("HA Reservation is already in desired state.")
            return
        self.ilg.write("HA Reservation Current State is not desired. Start to enable HA Reservation.")
        rest_ha.enable_ha()
        self.ilg.write("Sleeping '2' minutes.")
        time.sleep(120)
        # TODO: check task status?


class DscOob(DscBase):
    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        _res, self.ilo_sa = self.vault.get_secret(f"{self.cluster_info.name.upper()}/Site_Oob")
        _res, self.ahv_data = self.vault.get_secret(f"{self.cluster_info.name.upper()}/Site_Ahv_Root")
        self.ldap_domain = self.benchmark['systems']['ldap_domain']
        ipam_sa = ServiceAccount(ServiceAccount.NUTANIX_PM).get_service_account()
        self.rest_ipam = IpamAPI(username=ipam_sa['username'], password=ipam_sa['password'], logger=self.logger)
        self.ipam = Ipam(logger=self.logger)
        self.oneview = OneView(
            self.pc, self.pe, self.benchmark['vault']['dc_labels']['hpe_oneview'], facility_type=self.facility_type, vault=self.vault
        )
        oneview_scope = self.benchmark['systems']['oneview_scope']
        _res, self.myscope = self.oneview.get_oneview_scopes(oneview_scope)
        self.logger.info(f"oneview_scope is {oneview_scope}, myscope is {self.myscope}")
        oneview_servers = self.oneview.get_oneview_servers_hardware()
        self.serial_to_server = {server['attributes']['serialNumber']: server for server in oneview_servers}

    def configure(self):
        self.check_if_enabled("oob_health")
        self.config_oob()

    def config_oob(self):
        """
        Configures Out-of-Band (OOB) management for Nutanix cluster nodes.
        
        Main functionalities:
        1. Gets host list and retrieves network configuration from first host
        2. For each host in the cluster:
           - Verifies and updates IPMI address if needed
           - Checks iLO status and accessibility 
           - Configures iLO DNS settings and server name
           - Ensures proper OneView integration and monitoring
           
        Flow:
        1. Retrieves network info (gateway, netmask, DNS) from IPAM for OOB subnet
        2. Iterates through each host to:
           - Compare and sync IPMI addresses between IPAM, AHV and actual config
           - Validate iLO connectivity and status
           - Configure iLO settings (DNS, hostname, LDAP)
           - Add/Update server in OneView monitoring
        
        Handles errors per-host to ensure entire cluster configuration continues
        even if individual host configuration fails.
        """
        _, ahv_hosts = self.rest_pe.get_host_list()
        first_host = ahv_hosts[0]#TODO 如果Host1在IPAM中未注册，可能会导致后续的IPAM查询失败
        first_host_name = first_host['name'] + 'oob.' + self.ldap_domain
        ipam_object = self.rest_ipam.ip_address_list(name=first_host_name).json()[0]
        oob_subnet = self.rest_ipam.get_subnets_by_subnet_id(ipam_object['subnet_id']).json()[0]
        if ipam_object.get('ip_class_parameters'):
            ahv_cvm_gw = Ipam.get_value_from_class_parameters("gateway", ipam_object['ip_class_parameters'])
        elif ipam_object.get('subnet_class_parameters'):
            ahv_cvm_gw = Ipam.get_value_from_class_parameters("gateway", ipam_object['subnet_class_parameters'])
        oob_netmask = Ipam.calculate_netmask(oob_subnet)
        oob_dns = [
            Ipam.get_value_from_class_parameters("ikea_dns_server", oob_subnet["subnet_class_parameters"]),
            Ipam.get_value_from_class_parameters("ikea_dns_server_2", oob_subnet["subnet_class_parameters"])
        ]

        for host in ahv_hosts:
            try:
                self.ilg.write(f"Checking OOB for {host['name']}", severity='title')
                host_name = host['name'] + 'oob.' + self.ldap_domain
                response = self.rest_ipam.ip_address_list(name=host_name)
                if response.status_code == 204 or not response.content:
                    self.logger.info(f"No IPAM record found for {host_name}, creating new record...")
                    subnet_id = oob_subnet['subnet_id']
                    site_id = oob_subnet['site_id']
  
                    # Assign a new IP address from IPAM
                    self.logger.info(f"Assigning new IP address from IPAM for {host_name}...")
                    _, ipmi_address_from_ipam = self.ipam._assign_ip(
                        subnet_id=subnet_id,
                        site_id=site_id,
                        fqdn=host_name,
                        pe=self.pe
                    )
                    self.logger.info(f"Created new IPAM record with IP: {ipmi_address_from_ipam}")
                else:
                    ipam_objects = response.json()
                    ipmi_address_from_ipam = ipam_objects[0]['hostaddr']

                ipmi_address = host['oob_ip']
                ahv_address = host['ip']
                ssh = SSHConnect(ahv_address, self.ahv_data['username'], self.ahv_data['secret'], logger=self.logger)
                ipmi_address_from_ahv = self.get_ip_address(ahv_address, ssh)
                if ipmi_address_from_ahv != ipmi_address_from_ipam and ipmi_address != ipmi_address_from_ipam:
                    self.logger.info(f"OOB for {host['name']} need update ipmi address.")
                    self.update_ipmi_address(ahv_address, ipmi_address_from_ipam, oob_netmask, ahv_cvm_gw, ssh)
                self.logger.info(f"OOB for {host['name']} is reachable.")
                self.logger.info("Checking iLO status...")
                ilo_data = self.check_ilo_status(host, ipmi_address_from_ipam)
                if ilo_data is None:
                    self.logger.warning(f"Skipping OOB configuration for {host['name']} due to ILO status check failure")
                    continue
                self.logger.info(f"Checking ILO name and DNS name for {host['name']}")
                self.config_single_ilo(host, ipmi_address_from_ipam, self.ilo_sa['username'], self.ilo_sa['secret'], self.benchmark, oob_dns, self.ldap_domain)
                self.logger.info("Oneview part")
                self.logger.info("Check if the OOB is in oneview")
                res = self.oneview.get_server_from_oneview_by_uuid(ilo_data['UUID'])
                if res and res.ok:
                    server_data = res.json()
                    if server_data.get('state') == 'Monitored':
                        self.logger.info("OOB is in oneview and state is normal")
                    else:
                        self.logger.info("OOB is in oneview but state is not normal, need to reconfigure")
                        self.add_oob_to_oneview(ilo_data['SerialNumber'], host['name'], ipmi_address_from_ipam, 
                                              self.ilo_sa['username'], self.ilo_sa['secret'], 
                                              self.myscope, self.serial_to_server, self.ldap_domain)
                else:
                    self.logger.info("OOB is not in oneview, let's add it")
                    self.add_oob_to_oneview(ilo_data['SerialNumber'], host['name'], ipmi_address_from_ipam, self.ilo_sa['username'], self.ilo_sa['secret'], self.myscope, self.serial_to_server, self.ldap_domain)
                self.ilg.write(f"OOB configuration is done for {host['name']}.")
            except OneViewServerAddFailed as _e:
                self.logger.error(f"Failed to add server {host['name']} to OneView. Continuing with next server...")
                continue
            except Exception as e:
                self.logger.error(f"Unexpected error while configuring OOB for {host['name']}: {str(e)}")
                continue


    def config_single_ilo(self, host, oob_newip, ilo_user_name, ilo_password, bmk_res, oob_dns, domain):
        oob_name = f"{host['name']}OOB"
        need_reset = False

        #Set server name and DNS name
        self.logger.info(f"Setting server name and DNS name for {oob_name}")
        redfish = Redfish(oob_newip, ilo_user_name, ilo_password, logger=self.logger)
        current_servername = redfish.get_ilo_hostname()
        if not current_servername or current_servername != oob_name:
            redfish.set_ilo_servername(oob_name)
            need_reset = True

        current_ilo_dnsname = redfish.get_ilo_dnsname()
        if not current_ilo_dnsname or current_ilo_dnsname != oob_name:
            redfish.update_ilo_dnsname(oob_name)
            need_reset = True

        # Check if LDAP group exists
        res, data = redfish.get_ilo_object(request_url="AccountService")
        remote_group = bmk_res.get('group_mapping').get('pe_site_admin_group')
        if not (res and data['LDAP']['RemoteRoleMapping'][0]['RemoteGroup'] == remote_group):
            self.logger.info("Ilo Group is missing, adding construct.")
            redfish.setad_create_group(remote_group)
            need_reset = True
        else:
            self.logger.info("Group Already exists.")

        # Check LDAP binding and set if necessary
        ad_query_label = bmk_res.get('vault').get('dc_labels').get('ad_query_upn')
        res, vault_data = self.vault.get_secret(f"{ad_query_label}")
        ilo_join_username = vault_data['username'].split('@')[0]
        if (
            res and data['LDAP']['ServiceAddresses'][0] != domain or 
            data['LDAP']['ServiceEnabled'] != True or 
            data['LDAP']['Authentication']['Username'] != ilo_join_username
        ):
            res, sec = self.vault.get_secret(bmk_res.get('vault').get('dc_labels').get('ad_query'))
            redfish.set_ad_ldap(sec, domain)
            need_reset = True
        else:
            self.logger.info("LDAP Binding already in desired state.")

        # Set AD roles if necessary
        role_id = redfish.get_role_id(remote_group_to_find=remote_group)
        _res, data = redfish.get_ilo_object(request_url=f"AccountService/Roles/{role_id}")

        desired_oem_priv = [
            "RemoteConsolePriv", "VirtualMediaPriv", "VirtualPowerAndResetPriv", 
            "HostBIOSConfigPriv", "HostNICConfigPriv", "HostStorageConfigPriv"
        ]
        desired_assigned_priv = ["Login", "ConfigureSelf", "ConfigureManager", "ConfigureUsers"]

        compare_oem = set(data["OemPrivileges"]) != set(desired_oem_priv)
        compare_assigned = set(data["AssignedPrivileges"]) != set(desired_assigned_priv)

        if compare_oem or compare_assigned:
            redfish.set_ad_roles(roleid=role_id)
            need_reset = True
        else:
            self.logger.info("Privileges are already correct.")

        # Configure ilo DNS
        current_ilo_dns = redfish.get_ilo_dns()
        current_ilo_dns_set = set(current_ilo_dns)
        if any(server not in current_ilo_dns_set for server in oob_dns):
            redfish.set_ilo_dns(oob_dns)
            need_reset = True
        else:
            self.logger.info("DNS is already correct.")

        self.logger.info("Checking NTP configuration...")
        if self.check_and_configure_ilo_ntp(redfish):
            need_reset = True

        if need_reset:
            redfish.ilo_reset()
            self.logger.info("Start ILO reset, Waiting for OOB to come back up.")
            time.sleep(60)
        else:
            self.logger.info("No configuration changes made, skipping ILO reset.")

    def check_and_configure_ilo_ntp(self, redfish):
        try:
            target_ntp_servers = self.benchmark['systems']['ntp_servers'][:2]
            res, datetime_data = redfish.get_ilo_object("Managers/1/DateTime")
            if not res:
                self.logger.error("Failed to get DateTime configuration")
                return False

            self.logger.info(f"Current DateTime configuration: {datetime_data}")
            current_ntp_servers = datetime_data.get('NTPServers', [])
            if sorted(current_ntp_servers) != sorted(target_ntp_servers):
                self.logger.info("NTP servers do not match desired configuration...")
                payload = {
                    "StaticNTPServers": target_ntp_servers
                }
                res, _ = redfish.update_ilo_object(
                    "Managers/1/DateTime",
                    payload
                )
                if not res:
                    self.logger.error("Failed to update NTP configuration")
                    return False

                self.logger.info("Successfully updated NTP configuration")
                return True

            self.logger.info("NTP configuration is already in desired state")
            return False

        except Exception as e:
            self.ilg.write(f"Error configuring NTP: {str(e)}", severity="error")
            return False


    def check_ilo_status(self, host, ipmi_address_from_ipam):
        try:
            redfish = Redfish(ipmi_address_from_ipam, self.ilo_sa['username'], self.ilo_sa['secret'], retry=1)
            result, ilo_data = redfish.get_ilo_state()
            if not result:
                self.logger.info("Failed to authenticate with ILO using vault credentials")
                self.logger.info("Attempting to reset ILO password to match vault...")
                ssh = SSHConnect(host['ip'], self.ahv_data['username'], self.ahv_data['secret'], logger=self.logger)
                ssh.connect()
                command = f"ipmitool user set password 2 '{self.ilo_sa['secret']}'"
                res = ssh.exec_command(command).decode('utf-8')
                if "successful" not in res.lower():
                    self.logger.error(f"Failed to set ILO password: {res}")
                    return None

                self.logger.info("ILO password has been reset to match vault credentials")
                time.sleep(30)
                success, ilo_data = redfish.get_ilo_state()
                if not success or ilo_data is None:
                    self.logger.error("Failed to connect to ILO after password reset, Please check with Administrator")
                    return None

            if ilo_data is None or ilo_data.get('Status', {}).get('State') != 'Enabled':
                self.logger.info(f"OOB for {host['name']} state not good, let's reset it.")
                ssh = SSHConnect(host['ip'], self.ahv_data['username'], self.ahv_data['secret'], logger=self.logger)
                ssh.connect()
                ssh.exec_command("ipmitool bmc reset cold")
                time.sleep(60)
                success, ilo_data = redfish.get_ilo_state()
                if success and ilo_data and ilo_data.get('Status', {}).get('State') == 'Enabled':
                    self.logger.info(f"OOB for {host['name']} state is now enabled.")
                else:
                    self.logger.error(f"Failed to enable OOB for {host['name']}.")
                    return None

            self.logger.info("ILO status is good.")
            return ilo_data

        except Exception as e:
            self.logger.error(f"Error checking ILO status for {host['name']}: {str(e)}")
            return None


    def update_ipmi_address(self, host, ip, ipmimask, ipmigw, ssh):
        self.logger.info(f"Updating IPMI address for host {host}...")
        ssh = SSHConnect(host, self.ahv_data['username'], self.ahv_data['secret'], logger=self.logger)
        ssh.connect()
        self.logger.info("Setting Correct IPMI Mode:'static'")
        ssh.exec_command("ipmitool lan set 1 ipsrc static")
        ssh.exec_command(f"ipmitool lan set 1 ipaddr {ip}")
        ssh.exec_command(f"ipmitool lan set 1 netmask {ipmimask}")
        ssh.exec_command(f"ipmitool lan set 1 defgw ipaddr {ipmigw}")
        ssh.exec_command("ipmitool bmc reset cold")
        self.logger.info("Waiting for BMC reset to complete...")
        time.sleep(60)
        self.logger.info(f"change oob ip to static mode done for host {host}")

    def add_oob_to_oneview(self, serialnumber, oob_name, oob_newip, ilo_user_name, ilo_password, myscope, serial_to_server, domain):
        self.logger.info("Starting OneView Configuration")
        server = serial_to_server.get(serialnumber)
        if server:
            if server.get('scopeUris') and server['scopeUris'][0] and server['scopeUris'][0] != myscope:
                self.logger.info("This server is not inside my scope, attempting move.")
            elif server['name'].upper() != oob_name.upper()+"."+domain.upper():
                self.logger.info("This server name is not matching a server listed inside my scope.")
            else:
                self.logger.info("This server is already inside my scope!")
                return
            remove = True
            add = True
        else:
            self.logger.info("All Good and Fresh, lets add this server.")
            add = True
            remove = False
        if remove:
            self.logger.info("Lets remove it first.")
            self.oneview.remove_oneview_server(server['attributes']['uuid'])
            self.logger.info("Waiting 120 seconds for OneView to catch up.")
            time.sleep(120)
        if add:
            self.logger.info("Welcome back, proceeding with server addition.")
            max_retries = 3
            for retry in range(max_retries):
                try:
                    resp = self.oneview.add_oneview_server(oob_newip, myscope, ilo_user_name, ilo_password)
                    if not resp.ok:
                        self.logger.warning(f"OneView API call failed on attempt {retry + 1}: {resp.status_code}")
                        continue

                    self.logger.info(f"Attempt {retry + 1}: {oob_name} added to OneView, waiting 120 seconds to check the result.")
                    time.sleep(120)

                    redfish = Redfish(oob_newip, ilo_user_name, ilo_password, logger=self.logger)
                    _res, ilo_data = redfish.get_ilo_state()
                    if not _res or not ilo_data or 'UUID' not in ilo_data:
                        self.logger.warning(f"Failed to get valid iLO state on attempt {retry + 1}")
                        continue
                    res = self.oneview.get_server_from_oneview_by_uuid(ilo_data['UUID'])
                    
                    if res and res.ok:
                        self.logger.info(f"Successfully added {oob_name} to OneView.")
                        return

                    if retry < max_retries - 1:
                        self.logger.warning(f"Attempt {retry + 1} failed to add {oob_name} to OneView, retrying...")
                        continue
                except Exception as e:
                    self.logger.error(f"Error during attempt {retry + 1}: {str(e)}")
            self.logger.error(f"All attempts to add {oob_name} to OneView failed, please check the logs.")
            raise OneViewServerAddFailed(oob_name)

    def get_ip_address(self, ahv_address, ssh):
        try:
            res, _ = ssh.connect()
            if not res:
                self.logger.error(f"Failed to connect to {ahv_address}")
                return None
            try:
                ssh.invoke_shell()
                ssh.send_command('sudo ipmitool lan print | grep "IP Address " | grep -v "Source"\n')
                time.sleep(2)
                output = ""
                output = ssh.receive_output()
                output = re.sub(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])', '', output)
                match = re.search(r'IP Address\s+:\s+(\d+\.\d+\.\d+\.\d+)', output)
                if match:
                    return match.group(1)
                self.logger.error(f"Failed to extract IP address from output: {output}")
                return None
            finally:
                ssh.ssh.close()
        except Exception as e:
            self.logger.error(f"Error getting IPMI IP for host {ahv_address}: {str(e)}")
            return None


class DscAuthConfig(DscBase):
    ROLE_CLUSTER_ADMIN = "ROLE_CLUSTER_ADMIN"
    ROLE_USER_ADMIN = "ROLE_USER_ADMIN"
    ROLE_CLUSTER_VIEWER = "ROLE_CLUSTER_VIEWER"

    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        # self.ldap_domain = ModelPrismCentral.query.filter_by(fqdn=self.pc).one().domain     # ikea.com
        self.ldap_domain = self.benchmark['systems']['ldap_domain']
        self.desired_auth_config_name = self.ldap_domain.split(".")[0].lower()              # ikea
        # _, info = self.vault.get_secret(f"A_Central-{self.tier}/AdQuery")
        _, info = self.vault.get_secret(f"{self.benchmark['vault']['dc_labels']['ad_query_upn']}")
        # TODO: current sa doesn't have access to A_Central-PreProduction/AdQuery
        self.ldap_username = info["username"]
        self.ldap_password = info["secret"]
        self.rest_ac = RestAuthConfig(sa=self.pe_svc_account, pe=self.pe, logger=self.logger)

    def configure(self):
        self.check_if_enabled("ldap")
        self.configure_ldap()
        self.configure_role_mapping()
        if self.is_central_pe:
            self.rest_ac = RestAuthConfig(sa=self.pc_svc_account, pe=self.pc, logger=self.logger, is_central_pe=True)
            self.ilg.write("Repeating for Central PE")
            self.configure_ldap(px_mode="PC")
            self.configure_role_mapping(px_mode="PC")

    def configure_public_keys(self):
        key_name = "Gateway"
        rest = RestCluster(self.pe, self.pe_svc_account, self.logger)
        keys = rest.get_public_keys()
        if keys:
            for k in keys:
                if key_name in k["name"]:
                    self.logger.info("Good, Pe Public Key exists.")
                    return
        self.logger.warning("Lets try to install the public key.")
        public_key = self.vault.get_secret(f"{self.pe.split('.')[0].upper()}/{self.benchmark['vault']['site_labels']['site_gw_pub_key']}")
        rest.add_public_key(key=public_key, name=key_name)
        self.logger.info("Sleeping '3' Minutes after Key install.")
        time.sleep(180)
        keys = rest.get_public_keys()
        if keys:
            for k in keys:
                if key_name in k["name"]:
                    self.logger.info("Install public key succeeded!")
                    return
        raise flaskex.InternalServerError(f"Install public key failed! Current keys on PE: {keys}")

    def configure_ldap(self, px_mode="PE", ):
        self.ilg.write("Configuring LDAP...")
        self.logger.info(f"Desired auth config name: {self.desired_auth_config_name}")
        existing_config = self.rest_ac.get_auth_config()
        """existing_config = {
            "auth_type_list": [
                "LOCAL",
                "DIRECTORY_SERVICE"
            ],
            "directory_list": [
                {
                    "directory_type": "ACTIVE_DIRECTORY",
                    "connection_type": "LDAP",
                    "directory_url": "ldaps://ikea.com:636",
                    "domain": "ikea.com",
                    "name": "IKEA",
                    "group_search_type": "NON_RECURSIVE",
                    "service_account_username": "ikea\\S-LTNXAD-A-ITSEELM"
                }
            ]
        }"""
        # assume there will be at most 1 directory configuration
        if existing_config["directory_list"]:
            existing_domain = existing_config["directory_list"][0]["domain"]
            existing_name = existing_config["directory_list"][0]["name"]
            existing_sa_username = existing_config["directory_list"][0]["service_account_username"]
            if self._is_directory_config_all_match(existing_domain, existing_name, existing_sa_username):
                self.ilg.write(f"{px_mode} is already Joined towards '{self.ldap_domain}'.")
                self.ilg.write(f"Username is already set towards '{self.ldap_username}'")
                return
            if not self._is_directory_name_match(existing_name) and self.is_central_pe == 0:
                self.ilg.write(f"Auth Config Name mismatch, removing AuthConfig '{existing_name}'")
                self.rest_ac.remove_auth_config(existing_name)
            elif not self._is_directory_sa_username_match(existing_sa_username):
                self.logger.info("Username mismatch, replacing LDAP Config.")
                self.logger.info(f"Current username: '{existing_sa_username}'")
                role_mappings = self.rest_ac.get_role_mappings(existing_name)
                for r in role_mappings:
                    self.rest_ac.delete_role_mapping(auth_config_name=existing_name, role=r["role"], entity_type=r["entityType"])
                self.rest_ac.update_auth_config(
                    domain_name=self.ldap_domain, ldap_user=self.ldap_username, ldap_pass=self.ldap_password,
                    ldap_fqdn=self.ldap_domain, ldap_port="636",
                    recursive=self.benchmark['group_mapping']['domain_group_search_mode']
                )
                return
            else:
                self.logger.error("Something is wrong with the LDAP config, but updating seems impossible.")
                raise flaskex.BadGateway("We cannot delete PC LDAP Bindings.")
        self.logger.info(f"Adding the cluster towards '{self.ldap_domain}'.")
        self.rest_ac.add_auth_config(
            domain_name=self.ldap_domain, ldap_user=self.ldap_username, ldap_pass=self.ldap_password,
            ldap_fqdn=self.ldap_domain, ldap_port="636",
            recursive=self.benchmark['group_mapping']['domain_group_search_mode']
        )
        self.logger.info(f"'{px_mode}' Auth Join Success.")
        self.logger.info("Taking a powernap, waiting on settings to be applied.")
        time.sleep(15)

    def _is_directory_config_all_match(self, existing_domain, existing_name, existing_sa_username):
        ldap_username_part = self.ldap_username.split('@')[0].lower()
        if '\\' in existing_sa_username:
            existing_sa_username_part = existing_sa_username.split('\\')[1].lower()
        elif '@' in existing_sa_username:
            existing_sa_username_part = existing_sa_username.split('@')[0].lower()
        else:
            existing_sa_username_part = existing_sa_username.lower()
        return (
            self.ldap_domain.lower() == existing_domain.lower()
            and self.desired_auth_config_name.lower() in existing_name.lower()
            and ldap_username_part == existing_sa_username_part
        )


    def _is_directory_name_match(self, existing_name):
        return self.desired_auth_config_name.lower() == existing_name.lower()

    def _is_directory_sa_username_match(self, existing_sa_username):
        return self.ldap_username.lower() == existing_sa_username.lower()

    def _prepare_role_mapping(self, ac_name, px_mode="PE"):
        """
        return example
        desired_role_mappings: {
            "ROLE_USER_ADMIN":                  ["NXadmin"],
            "ROLE_CLUSTER_ADMIN":               ["NXadmin"],
            (optional)"ROLE_CLUSTER_VIEWER":    ["NXadmin"]
        }
        existing_role_mappings: {
            "ROLE_USER_ADMIN": {
                "entityType":                   "GROUP",
                "entityValue":                  ["NXadmin"],
            },
            "ROLE_CLUSTER_ADMIN": {
                "entityType":                   "GROUP",
                "entityValue":                  ["NXadmin"],
            },
            (optional)"ROLE_CLUSTER_VIEWER": {
                "entityType":                   "GROUP",
                "entityValue":                  ["NXadmin"],
            },
        }
        """
        # if self.benchmark.var_groups.startswith("Replace"):      # TODO: this scenario is not existing?
        #     pass
        # else:
        #     group_search_value = self.benchmark.pe_site_admin_group
        # pe_admin_group = ModelRetailNutanixAutoMaintenanceBenchmark.query.filter(
        #     and_(ModelRetailNutanixAutoMaintenanceBenchmark.name.like(f'%{group_search_value}%'), ModelRetailNutanixAutoMaintenanceBenchmark.name.like('%-CN'))
        # ).scalar().value.split(",")
        pe_site_admin_group = self.benchmark['group_mapping']['pe_site_admin_group'].split(",")
        self.logger.info(f"We are setting up RoleMappings using group: '{pe_site_admin_group}'")
        desired_pe_entity = sorted(pe_site_admin_group)
        desired_pc_entity = sorted([self.benchmark['group_mapping']['pc_site_viewer_group']])
        desired_entity_type = "GROUP"
        desired_role_mappings = {
            (self.ROLE_USER_ADMIN, desired_entity_type): desired_pe_entity,
            (self.ROLE_CLUSTER_ADMIN, desired_entity_type): desired_pe_entity,
        }
        if px_mode == "PC":
            desired_role_mappings.update(
                {(self.ROLE_CLUSTER_VIEWER, desired_entity_type): desired_pc_entity}
            )

        response = self.rest_ac.get_role_mappings(ac_name)
        existing_role_mappings = {}
        for _ in response:
            key = (_["role"], _["entityType"])
            value = _["entityValues"]
            existing_role_mappings[key] = value
        return desired_role_mappings, existing_role_mappings

    def configure_role_mapping(self, px_mode="PE"):
        self.ilg.write(f"Configuring the Role Mappings for '{self.desired_auth_config_name}'")
        self.logger.info("Getting Groups that need to be bound.")
        ac_name = self.rest_ac.get_auth_config()["directory_list"][0]["name"]
        desired_rps, existing_rps = self._prepare_role_mapping(ac_name, px_mode)

        # delete
        for (existing_role, existing_entity_type), existing_entity_values in existing_rps.items():
            if (existing_role, existing_entity_type) not in desired_rps:
                self.ilg.write(
                    f"Role {existing_role} with entity type {existing_entity_type} should not exist, deleting...")
                self.rest_ac.delete_role_mapping(ac_name, existing_role, existing_entity_type)
        # add and update
        for (desired_role, desired_entity_type), desired_entity_values in desired_rps.items():
            if (desired_role, desired_entity_type) not in existing_rps:
                self.ilg.write(
                    f"Role {desired_role} with entity type {desired_entity_type} doesn't exist, creating...")
                self.rest_ac.add_role_mapping(ac_name, desired_entity_values, desired_role)
                continue
            existing_entity_values = sorted(existing_rps[(desired_role, desired_entity_type)])
            if desired_entity_values != existing_entity_values:
                self.ilg.write(
                    f"Role {desired_role} with entity type {desired_entity_type} entityValues is wrong, updating...")
                self.logger.info(f"existing_entity_values: {existing_entity_values}")
                self.logger.info(f"desired_entity_values: {desired_entity_values}")
                # Note: can only update the role mappings with existing role + entity_type
                self.rest_ac.update_role_mapping(ac_name, desired_entity_values, desired_role)
                continue

            self.logger.info(f"Role {desired_role} with entity type {desired_entity_type} is already in desired state.")

        # verify result
        auth_config = self.rest_ac.get_auth_config()
        if auth_config["directory_list"][0]["domain"] != self.ldap_domain:
            self.ilg.write("Failed to setup rolemapping!", severity="error")


class DscDnsNtp(DscIpamBase):
    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        self.rest = RestCluster(self.pe, self.pe_svc_account, self.logger)
        self.ahv_cvm_subnet_params = self.get_ahv_cvm_subnet()

    def configure(self):
        self.check_if_enabled("dns_ntp_config")
        self.configure_ntp()
        self.configure_dns()
        if self.is_central_pe:
            self.rest = RestCluster(self.pe, self.pe_svc_account, self.logger, is_central_pe=True)
            self.ilg.write("Repeating for Central PE")
            self.configure_ntp()
            self.configure_dns()

    def configure_ntp(self):
        self.ilg.write("Configuring NTP...")
        target_ntp_servers = self.get_ntp_servers()
        current_ntp_servers = self.rest.get_ntp_servers()
        self.logger.info(f"Target NTP servers: {target_ntp_servers}")
        self.logger.info(f"Current NTP servers: {current_ntp_servers}")
        if current_ntp_servers:
            has_diff = sorted(target_ntp_servers) != sorted(current_ntp_servers)
            if not has_diff:
                self.ilg.write("NTP is already as desired.")
                return
            self.ilg.write("NTP Config not desired, correcting. Cleaning Current list...")
            self.rest.remove_ntp_servers(current_ntp_servers)
        self.ilg.write("Adding Desired NTP servers...")
        self.rest.add_ntp_servers(target_ntp_servers)
        output = self.rest.get_ntp_servers()
        self.ilg.write(f"NTP Setup Success: '{output}")

    def configure_dns(self):
        self.ilg.write("Configuring DNS...")
        target_dns_servers = self.get_infra_dns_servers()
        current_dns_servers = self.rest.get_dns_servers()
        self.logger.info(f"Target DNS servers: {target_dns_servers}")
        self.logger.info(f"Current DNS servers: {current_dns_servers}")
        if current_dns_servers:
            has_diff = sorted(target_dns_servers) != sorted(current_dns_servers)
            if not has_diff:
                self.ilg.write("DNS is already as desired.")
                return
            self.ilg.write("DNS Config not desired, correcting. Cleaning Current list...")
            self.rest.remove_dns_servers(current_dns_servers)
        self.ilg.write("Adding Desired DNS servers...")
        self.rest.add_dns_servers(target_dns_servers)
        try:
            output = self.rest.get_dns_servers()
            self.ilg.write(f"DNS Setup Success: '{output}")
        except Exception:
            self.ilg.write("Get dns servers failed, but continue!", severity="warning")


class DscAhvHostname(DscIpamBase):

    def configure(self):
        self.ilg.write("Configure AHV Hostnames")
        self.check_if_enabled("ahv_host_names")
        _, ntx_ahv_hosts = self.rest_pe.get_host_list()
        ipam_ahv_nodes = self.get_ahv_objects_on_ipam()
        ipam_cvm_nodes = self.get_cvm_objects_on_ipam()
        ntx_mapping = {n["ip"]: n["name"] for n in ntx_ahv_hosts}
        ipam_mapping = {i["hostaddr"]: i["name"] for i in ipam_ahv_nodes}
        for ip, ipam_name in ipam_mapping.items():
            if ip not in ntx_mapping:
                self.logger.error(f"This IP: '{ip}' Cannot be found inside Prism...")
                continue
            ntx_name = ntx_mapping[ip]
            if ntx_name.lower() in ipam_name.lower():
                self.logger.info("Current node is good, skipping...")
                continue
            desired_name = ipam_name.upper().split(".")[0]
            self.ilg.write(f"This host name: '{ntx_name}' isn't desired, Changing host name towards '{desired_name}'")
            self.ssh_change_hostname(cvm_ip=ipam_cvm_nodes[0]["hostaddr"], host_ip=ip, host_name=desired_name)

    def ssh_change_hostname(self, cvm_ip, host_ip, host_name):
        _, data = self.vault.get_secret(
            f"{self.pe.split('.')[0].upper()}/{self.benchmark['vault']['site_labels']['site_pe_nutanix']}"
        )
        _conn = SSHConnect(host=cvm_ip, username=data['username'], password=data['secret'], public_key=self.public_key, retry=1, logger=self.logger)
        _conn.connect_by_pwd_or_key()
        _conn.exec_command(f"/usr/local/nutanix/bin/change_ahv_hostname --host_ip={host_ip} --host_name={host_name}")


class DscCvmHostname(DscIpamBase):
    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        _, secret_info = self.vault.get_secret(f"{pe.split('.')[0].upper()}/{self.benchmark['vault']['site_labels']['site_pe_nutanix']}")
        self.username, self.secret = secret_info['username'], secret_info['secret']

    def configure(self):
        self.ilg.write("Configure CVM Hostnames")
        _, ahv_hosts = self.rest_pe.get_host_list()
        node_number = len(ahv_hosts)
        if node_number <= 1:
            self.ilg.write("CVM Rename on Single nodes is not supported. Aborting...")
            return
        self.check_if_enabled("cvm_host_names")
        res, data = self.rest_pe.get_vm_list(details=True)
        if not res:
            raise flaskex.BadGateway(f"Failed to get vm list from NTX! Reason: {data}")
        ntx_cvms = [vm for vm in data if vm["is_controller_vm"]]
        ipam_cvms = self.get_cvm_objects_on_ipam()
        ipam_mapping = {i["hostaddr"]: i["name"] for i in ipam_cvms}

        for i, (ip, ipam_name) in enumerate(ipam_mapping.items()):
            _ = 0
            for cvm in ntx_cvms:
                _ += 1
                cvm_detail = cvm["detail"]
                if ip in cvm_detail["ipAddresses"]:
                    ntx_name = cvm_detail["vmName"]
                    true_name = self._parse_cvm_name_by_ipam_cvm_name(ipam_name)
                    if ntx_name == true_name:
                        self.logger.info("Current node name on NTX is equal to IPAM, skipping...")
                        break
                    self.logger.info(
                        f"This host name: '{ntx_name}' isn't desired, Changing host name towards '{true_name}'")
                    self._ssh_change_cvm_hostname(cvm_ip=ip, new_name=true_name)                                      # console
                    cvm_ips = list(ipam_mapping.keys())
                    connect_cvm_ip = cvm_ips[1 if i == 0 else 0]
                    self._ssh_change_cvm_display_name(target_cvm_ip=ip, connect_cvm_ip=connect_cvm_ip, new_name=true_name)      # WEB UI
                    break
                if _ == len(ntx_cvms):
                    self.logger.error(f"This IP: '{ip}' Cannot be found inside Prism...")

    def _parse_cvm_name_by_ipam_cvm_name(self, ipam_cvm_name):
        # ipam_cvm_name:    retse999-nx7003cvm.ikea.com
        # ipam_prefix:      RETSE999-NX7003CVM
        ipam_prefix = ipam_cvm_name.split(".")[0].upper()
        self.logger.info(f"Working with '{ipam_prefix}'")
        if ipam_prefix.endswith("-CVM"):
            true_name = f"NTNX-{ipam_prefix}"
        elif ipam_prefix.endswith("CVM"):
            name_arr = ipam_prefix.split("CVM")
            if len(name_arr) > 2:
                raise flaskex.InternalServerError(f"CVM name format error: {ipam_prefix}")
            true_name = f"NTNX-{name_arr[0]}-CVM"
        else:
            true_name = f"NTNX-{ipam_prefix}-CVM"
        self.logger.info(f"The name to set to NTX cvm: '{true_name}'")
        return true_name

    def _ssh_change_cvm_hostname(self, cvm_ip, new_name):
        _conn = SSHConnect(host=cvm_ip, username=self.username, password=self.secret, public_key=self.public_key, retry=1, logger=self.logger)
        ssh_client = _conn.connect_by_pwd_or_key()
        _, stdout, _ = ssh_client.exec_command("echo $HOSTNAME")
        cur_hostname = stdout.read()
        if cur_hostname.strip().decode("utf-8").lower() == new_name.lower():
            self.logger.info(f"Shell is already responding with '{cur_hostname}'")
            return False
        _conn.connect_by_pwd_or_key()
        _conn.invoke_shell()
        _conn.send_command(f"sudo /usr/local/nutanix/cluster/bin/change_cvm_hostname {new_name}")
        time.sleep(5)
        _conn.send_command("y")
        self.logger.info("Waiting on CVM Restart, sleeping '5' minutes.")
        time.sleep(300)
        return True

    def _ssh_change_cvm_display_name(self, target_cvm_ip, connect_cvm_ip, new_name):
        self.logger.info(f"Building Session towards cvm: '{connect_cvm_ip}'")
        self.logger.info(f"Renaming CVM Hostname towards '{new_name}' Target CVM IP: '{target_cvm_ip}'")
        _conn = SSHConnect(host=connect_cvm_ip, username=self.username, password=self.secret, retry=1, logger=self.logger)
        res, _ = _conn.connect()
        if not res:
            raise Exception("Failed to connect to PE.")
        self.logger.info(f"Renaming CVM Displayname '{new_name}' using CVM IP: '{target_cvm_ip}'" )
        _conn.invoke_shell()
        _conn.send_command(f"/usr/local/nutanix/cluster/bin/change_cvm_display_name --cvm_ip={target_cvm_ip} --cvm_name={new_name}")
        time.sleep(10)
        _, output = _conn.recv()
        if "ERROR" in output:
            raise flaskex.InternalServerError(f"Failed to change cvm display name!  Last output: {output}")
        _conn.send_command("Y")
        self.logger.info("Waiting '7' mins on CVM Restart.")
        time.sleep(400)
        self.logger.info("Rename Completed")


class DscSmtp(DscBase):

    def configure(self):
        self.check_if_enabled("smtp_config")
        sender_email = f"{self.pe.split('.')[0]}.{self.benchmark['recipient']['sender_email']}"
        smtp_server = self.benchmark['recipient']['smtp_server']
        if smtp_server == "Not-Implemented":
            self.logger.warn("Not-Implemented")
            return
        rest_smtp = RestSMTP(self.pe, self.pe_svc_account, self.logger)
        rest_smtp.set_smtp_config(
            smtp_sender=sender_email, smtp_server=smtp_server, security=self.benchmark['recipient']['smtp_security']
        )
        rest_smtp.set_alert_config(
            smtp_receiver=self.benchmark['recipient']['alert_receiver_email'], smtp_sender=sender_email, smtp_server=smtp_server
        )
        if DscBase.is_config_enabled(self.is_central_pe):
            self.logger.info("PC specific process not implemented yet.")
            return


class DscStorageConfig(DscBase):
    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        self.rest_sc = StorageContainer(self.pe, self.pe_svc_account, self.logger)

    def configure(self):
        self.check_if_enabled("storage_config")
        containers = self.rest_sc.list_storage_containers()["entities"]
        self.check_vdisks(containers)
        self.configure_compression(containers)

    def check_vdisks(self, containers):
        self.ilg.write("Checking vdisks...")
        default = None
        for c in containers:
            if re.match(r".*default.*|.*OS01.*", c["name"], re.IGNORECASE) \
                    and not re.match(r".*Do_Not_Use.*", c["name"], re.IGNORECASE):
                default = c
                break
        if not default:
            self.ilg.write("State of the default container is already as desired.")
            return
        vdisks = self.rest_sc.list_vdisks(default["storage_container_uuid"])
        entities = vdisks.get("entities")
        if entities:
            self.ilg.write(
                f"There are '{len(entities)}' VDisks present inside this container. We cannot change this...."
                "Please migrate them out to rename.",
                severity="warning"
            )
            return
        self.ilg.write(f"Default storage Container is not at its desired state, current name: {default['name']}")
        self.ilg.write("Correcting State...")
        default["name"] = f"Do_Not_Use-{default['name']}"
        self.rest_sc.update_container(default)

    def configure_compression(self, containers):
        self.ilg.write("Configuring storage containers...")
        user_containers = [c for c in containers if c['name'] not in self.benchmark['storage']['container_rules']['exceptions']]
        for c in user_containers:
            existing_state = c['compression_enabled']
            existing_delay = c["compression_delay_in_secs"]
            if self.benchmark['storage']['container_rules']['compression_enabled'] != existing_state:
                c["compression_enabled"] = self.benchmark['storage']['container_rules']['compression_enabled']
                if self.benchmark['storage']['container_rules']['compression_enabled']:
                    c["compression_delay_in_secs"] = self.benchmark['storage']['container_rules']['compression_delay_sec']
            elif int(self.benchmark['storage']['container_rules']['compression_delay_sec']) != int(existing_delay):
                c["compression_delay_in_secs"] = int(self.benchmark['storage']['container_rules']['compression_delay_sec'])
            else:
                continue
            self.rest_sc.update_container(c)
        # TODO: validate result?


class DscPulse(DscBase):

    def configure(self):
        self.check_if_enabled("pulse")
        self.configure_eula()
        self.configure_pulse()
        self.configure_remote_diagnostics(host=self.pe, vault_secret_name=f"{self.pe.split('.')[0].upper()}/{self.benchmark['vault']['site_labels']['site_pe_nutanix']}")
        if self.is_central_pe:
            self.configure_remote_diagnostics(self.pc_ip, vault_secret_name=f"{self.pe.split('.')[0].upper()}/{self.benchmark['vault']['central_labels']['site_pc_nutanix']}")

    def configure_eula(self):
        self.ilg.write("Configure User License Agreement Acceptance...")
        rest = RestEulas(self.pe, self.pe_svc_account, self.logger)
        eulas = rest.list_eulas()["entities"]
        for eula in eulas:
            if eula.get('enabled') and eula.get('userDetailsList') and eula['userDetailsList'][0].get("username") == self.benchmark['eula']['name']:
                self._write_log("Already completed Eula Settings.")
                return
        rest.accept_eulas(username=self.benchmark['eula']['name'], company_name=self.benchmark['eula']['company'], job_title=self.benchmark['eula']['role'])

    def configure_pulse(self):
        self.ilg.write("Configuring Pulse...")
        rest = RestPulse(self.pe, self.pe_svc_account, self.logger)
        rest.set(enable=True)
        # TODO: check state?

    def configure_remote_diagnostics(self, host, vault_secret_name):
        self.ilg.write("Configuring Remote Diagnostics...")
        if not self.benchmark['deployment']['remote_diag_disable']:
            self.ilg.write("Desired setting for remote diagnostics is 'enable', so will remain enable.")
            return
        self.logger.info(f"Disabling remote diagnostics for {host}...")
        _res, data = self.vault.get_secret(vault_secret_name)
        username, password = data["username"], data['secret']
        ssh_connect = SSHConnect(host=host, username=username, password=password, public_key=self.public_key, retry=1, logger=self.logger)
        ssh_connect.connect_by_pwd_or_key()
        ssh_connect.invoke_shell()
        time.sleep(3)
        ssh_connect.send_command("/home/<USER>/ncc/bin/nusights/set_remote_diagnostics_status --enable=false")
        self.logger.info("Checking result")
        ssh_connect.send_command("/usr/local/nutanix/cluster/bin/zkcat /appliance/logical/nusights/collectors/kCommand/override_config")
        time.sleep(2)
        _, output = ssh_connect.recv()
        if not output:
            self.ilg.write("We got issues setting remote diagnostics..", severity="error")
            return
        try:
            command_result = json.loads(output[output.index('{'):output.rindex('}')+1])
            if not command_result["rcc_enabled"]:
                self.ilg.write("Remote diagnostics has been disabled.")
            else:
                self.ilg.write("Looks like remote diagnostics is still enabled after all this...", severity="warning")
        except (ValueError, JSONDecodeError) as e:
            self.ilg.write(f"I cannot read this output, bad programming. Original output: {output}", severity="warning")
            self.ilg.write(f"Original error: {e}", severity="warning")


class DscSyncBackupSettings(DscBase):
    def __init__(self, pe, logger, db_logger, facility_type):
        super().__init__(pe=pe, logger=logger, db_logger=db_logger, facility_type=facility_type)
        self.rest_pc = PrismCentral(self.pc, self.pc_svc_account, self.logger)
        self.pe_name = self.cluster_info.name
        self.remote_site_dsc = self.cluster_info.remote_site_dsc
        self.pd = ProtectionDomain(self.pe, self.pe_svc_account, self.logger)
        self.rest_rs_source = RestRemoteSite(self.pe, self.pe_svc_account, self.logger)
        if self.remote_site_dsc == 'NA':
            self.ilg.write("Current site has no remote site configured, skip...")
            return
        self.rs_target_site_sa = self._init_target_site_sa()
        self.rest_rs_target = RestRemoteSite(self.remote_site_dsc, self.rs_target_site_sa, self.logger)

    def _init_target_site_sa(self):
        pe_svc_label = f"{self.remote_site_dsc}/Site_Pe_Svc"
        res, data = self.vault.get_secret(pe_svc_label)
        if not res:
            raise VaultGetSecretFailed(pe_svc_label)
        return {"username": data["username"], "password": data["secret"]}

    def configure(self):
        # self.check_if_enabled("Backup")
        # TODO: handle the scenario when remote_site == 'NA': still need to configure local schedule
        source_site_detail, target_site_detail = self.prepare_source_and_target_sites()
        if not source_site_detail or not target_site_detail:
            self.ilg.write("No remote site is configured, skipping...")
            return
        self.configure_remote_sites(source_site_detail, target_site_detail)
        self.configure_pd(target_site_detail, source_site_detail)

    def prepare_source_and_target_sites(self):
        def _rebuild_structure(s):
            result = {
                "entity_id": s["uuid"],
                "name": s["name"],
                "external_ip_address": s["cluster_external_ipaddress"]
            }
            return result

        remote_site_name = self.cluster_info.remote_site_dsc
        if not remote_site_name or remote_site_name == "NA":
            return None, None
        repl_target = self.rest_rs_target.get_cluster_detail()
        repl_source = self.rest_rs_source.get_cluster_detail()
        if not repl_target and remote_site_name != "NA":
            self.ilg.write(
                "Please check the cluster name, is this an old cluster that was recently migrated to a new name?",
                severity="error")
            raise flaskex.InternalServerError(
                f"The replication target cluster: '{remote_site_name}' could not be found!!")
        return _rebuild_structure(repl_source), _rebuild_structure(repl_target)

    def configure_remote_sites(self, source_detail, target_detail):
        self.ilg.write("Configure Remote Sites...")
        # STEP 1: preparation
        daytime_bandwidth_bps, nighttime_bandwidth_bps = self._calculate_bandwidth()
        bw_policy_start_time, bw_policy_end_time = self._calculate_bandwidth_policy_time()
        source_network_mappings, target_network_mappings = self._calculate_network_mappings()

        # STEP 2: Configure RS for both target site and source site
        self.logger.info(f"Configuring Remote Site for Target {target_detail['name']}")
        self._configure_rs(
            target_detail, source_detail,
            daytime_bandwidth_bps, nighttime_bandwidth_bps, bw_policy_start_time, bw_policy_end_time,
            target_network_mappings, self.rest_rs_target
        )
        self.logger.info(f"Configuring Remote Site for Source {source_detail['name']}")
        self._configure_rs(
            source_detail, target_detail,
            daytime_bandwidth_bps, nighttime_bandwidth_bps, bw_policy_start_time, bw_policy_end_time,
            source_network_mappings, self.rest_rs_source
        )
        # STEP 3: cleanups
        self.ilg.write("Cleaning up remote sites...")
        self.logger.info(f"Cleaning Remote - Remote Site on Target {target_detail['name']}")
        self._clean_remote_sites(target_detail, source_detail, self.rest_rs_target)
        self.logger.info(f"Cleaning Local - Remote Site on Source {source_detail['name']}")
        self._clean_remote_sites(source_detail, target_detail, self.rest_rs_source)

    def _configure_rs(
        self, local_site_detail, remote_site_detail,
        daytime_bandwidth_bps, nighttime_bandwidth_bps, bw_policy_start_time, bw_policy_end_time,
        network_mappings, rest_rs
    ):
        expected_remote_site_name = f"RS_{remote_site_detail['name']}"
        existing_remote_sites = rest_rs.get_remote_sites()
        if expected_remote_site_name not in [_["name"] for _ in existing_remote_sites["entities"]]:
            self.logger.info(f"Creating Remote Site on Target {local_site_detail['name']}")
            rest_rs.create_remote_site(
                remote_site_name=expected_remote_site_name, target_ip=remote_site_detail["external_ip_address"]
            )
            time.sleep(10)
            exists = False
        else:
            self.logger.info(f"Remote site {expected_remote_site_name} already configured for {local_site_detail['name']}")
            exists = True
        self.logger.info("Updating remote sites...")
        site_detail = rest_rs.get_remote_site_detail(expected_remote_site_name)
        try:
            rest_rs.update_remote_site(
                bw_policy_start_time=bw_policy_start_time, bw_policy_end_time=bw_policy_end_time,
                bw_cap_day=daytime_bandwidth_bps, bw_cap_night=nighttime_bandwidth_bps,
                nw_map_obj=network_mappings,
                remote_site_obj=site_detail,
                enable_compression=self.benchmark['backup']['rs_compression_enabled'],
                cvm_list=self.fetch_cvm_ip(site_detail),
                exists=exists
            )
        except RemoteSiteInUse as e:
            self.ilg.write(f"Ignore this error. Error message: {str(e)}", severity="warning")
            return

    def fetch_cvm_ip(self, site_detail):
        rs_name = site_detail["name"]
        start = rs_name.find("RS_")
        end = rs_name.find("-NXC")
        if start == -1 or end == -1:
            self.logger.warning(f"Remote site {rs_name} unable to identify ")
            return
        start += len("RS_")
        site_code = rs_name[start:end]
        match_records = ModelRetailNutanixHost.query.filter(ModelRetailNutanixHost.name.like(f'%{site_code}%')).all()
        if not match_records:
            self.logger.warning("No matching records found in host table for site_code:", site_code)
            return
        cvm_ip = [record.cvm_ip for record in match_records]
        return cvm_ip

    def _calculate_bandwidth(self):
        default_backup_bw = 3.375      # TODO: default value?
        try:
            remote_bw = float(self.cluster_info.bandwidth_dsc)
        except TypeError:
            self.logger.warning(
                f"Backup bandwidth value stored in database: {self.cluster_info.bandwidth_dsc}, "
                f"cannot convert it to float, use a default value: {default_backup_bw}.")
            remote_bw = default_backup_bw
        if not remote_bw:
            self.logger.info(f"Remote Bandwidth is {remote_bw}, use a default value: {default_backup_bw}.")
            remote_bw = default_backup_bw
        self.logger.info("Bandwidth math...")
        daytime_bw_bps = remote_bw * 1000000 * self.benchmark['backup']['day_capability_percentage'] / 100
        nighttime_bw_bps = remote_bw * 1000000 # the night time bandwidth would be give to default_bandwidth_limit, should be the same with bandwidth_dsc in the database
        self.logger.info(f"Max Bandwidth for the slowest site = {remote_bw} mbps")
        self.logger.info(f"DayTime Bandwidth will be set to {daytime_bw_bps / 1000 / 1000} mbps")
        self.logger.info(f"NightTime Bandwidth will be set to {nighttime_bw_bps / 1000 / 1000} mbps")
        return daytime_bw_bps, nighttime_bw_bps

    def _calculate_bandwidth_policy_time(self):
        self.logger.info(f"Calculating Night in Country Code {self.cluster_info.country_code}")
        self.logger.info(f"Site is using timezone {self.cluster_info.timezone}")
        site_tz = datetime.strptime(self.cluster_info.timezone, "UTC%z").tzinfo
        converted_start_time = datetime.strptime(str(self.benchmark['backup']['rs_bw_policy_start_time']), "%H:%M:%S") \
            .replace(tzinfo=site_tz).astimezone(timezone.utc)
        converted_end_time = datetime.strptime(str(self.benchmark['backup']['rs_bw_policy_end_time']), "%H:%M:%S") \
            .replace(tzinfo=site_tz).astimezone(timezone.utc)

        # minute -> second -> millisecond -> microsecond
        bw_policy_start_time_usec = (converted_start_time.hour * 60 + converted_start_time.minute) * 60 * 1000 * 1000
        bw_policy_end_time_usec = (converted_end_time.hour * 60 + converted_end_time.minute) * 60 * 1000 * 1000
        self.logger.info(f"Converted start time = {datetime.strftime(converted_start_time, '%H:%M:%S')}")
        self.logger.info(f"Converted end time = {datetime.strftime(converted_end_time, '%H:%M:%S')}")
        return bw_policy_start_time_usec, bw_policy_end_time_usec

    def _calculate_network_mappings(self):
        source_networks = self.rest_rs_source.get_network_list(details=True)
        target_networks = self.rest_rs_target.get_network_list(details=True)
        self.logger.info("Building a network mapping per VLAN.")
        source_maps, target_maps = [], []
        for n1 in source_networks:
            for n2 in target_networks:
                if n2["vlan_id"] == n1["vlan_id"]:
                    source_maps.append({
                        "source": n1["name"],
                        "target": n2["detail"]
                    })
                    target_maps.append({
                        "source": n2["detail"],
                        "target": n1["name"],
                    })
        self.logger.info(f"We have {len(target_maps)} network mappings")
        return source_maps, target_maps

    def _clean_remote_sites(self, local_site, remote_site, rest_rs):
        existing_remote = rest_rs.get_remote_sites()

        expected_rs_name = f"RS_{remote_site['name']}"
        not_my_sites = [rs for rs in existing_remote["entities"] if expected_rs_name not in rs["name"]]
        sites_to_delete = [s for s in not_my_sites if s.get('remote_ip_ports').get(local_site['external_ip_address'])]
        for site in sites_to_delete:
            self.logger.info(f"This remote site is going to be removed : '{site['name']}'")
            rest_rs.delete_remote_site(rs_name=site["name"])

    def configure_pd(self, source_site_detail, target_site_detail):
        self.ilg.write("Configuring PDs with VMs and Schedules...")

        self._check_existing_pd_amount()
        if self.cluster_info.remote_site_dsc == "NA":
            target_site_name = None
        else:
            existing_rs_list = self.rest_rs_source.get_remote_sites()
            remote_site = None
            for rs in existing_rs_list["entities"]:
                if rs["name"] != self.pe_name and self.cluster_info.remote_site_dsc in rs["name"]:
                    remote_site = rs
                    break
            if not remote_site:
                raise flaskex.InternalServerError(f"Can't find desired remote site for {source_site_detail['name']}!")
            self.logger.info(f"Using: '{remote_site['name']}' Site Name")
            site_detail = self.rest_rs_source.get_remote_site_detail(remote_site['name'])
            remote_cluster_ip_list = list(site_detail["remoteIpPorts"].keys())
            if not remote_cluster_ip_list:
                self.logger.warning("Can't find remote site ip, this will fail workload is the chain has been changed")
                target_site_name = target_site_detail["name"]
            else:
                target_site_uuid = site_detail["uuid"]
                res, cluster_list = self.rest_pc.get_cluster_list(filter=f"uuid=={target_site_uuid}")
                if not res:
                    raise flaskex.BadGateway(f"Failed to list pc clusters! Detail: {cluster_list}")
                # after filtering, there ought to be only 1 result
                target_site_name = cluster_list[0]["name"]
        self.logger.info(f"We are creating the Protection Domain inside this '{self.cluster_info.node_number}' NODE Cluster")
        remote_node_count = self._get_remote_node_count()
        pd_suffixes = ["Gold_CCG"]
        if self.cluster_info.node_number <= 2 or (remote_node_count != 0 and remote_node_count <= 2):
            self.logger.info("The above cluster type does not support RPO < 6 Hours.")
            self.logger.info("We are creating a Silver Based Policy with all VMs")
            self.logger.info(f"Local Cluster has {self.cluster_info.node_number} NODE(s)")
            self.logger.info(f"Remote Cluster has {remote_node_count} NODE(s)")
            pd_suffixes = ["Silver_CCG"]
        self._add_vm_to_pd(target_site_name, pd_suffixes)
        self._clean_pds()

    def _check_existing_pd_amount(self):
        existing_pds = self.pd.get_pds()
        if existing_pds["metadata"]["count"] > 2:
            existing_pd_names = [pd['name'] for pd in existing_pds['entities']]
            self.logger.warning(f"Existing PDs: {existing_pd_names}")
            raise flaskex.InternalServerError(f"Existing PDs: {existing_pd_names}. PD amount > 2, this is wrong, please check on Nutanix!")
        self.logger.info("PD amount is in correct situation.")

    def _get_remote_node_count(self):
        if self.cluster_info.remote_site_dsc == "NA":
            remote_node_count = 0
        else:
            res, hosts = self.rest_rs_target.get_host_list()
            if not res:
                raise flaskex.BadGateway(f"Failed to get the host list, quit! Reason: {hosts}")
            remote_node_count = len(hosts) if res else 1
        return remote_node_count

    def _add_vm_to_pd(self, target_site_name, pd_suffixes):
        res, data = self.rest_pe.get_vm_list(details=True)
        if not res:
            raise flaskex.BadGateway(f"Failed to get vm list from NTX! Reason: {data}")
        user_vms = [vm for vm in data if not vm["is_controller_vm"]]
        vm_id_list = [vm["detail"]["vmId"] for vm in user_vms]      # vm_id = "xxxxxxx::<vm_uuid>"
        vm_uuid_list = [vm["detail"]["uuid"] for vm in user_vms]
        for suffix in pd_suffixes:
            pd_name = f"{self.pe_name}-{suffix}"        # e.g.pd_name = {cluster_name}-Gold_CCG
            self.logger.info(f"Adding VM to Protection domain {pd_name}")
            if self.pd.is_pd_existing(pd_name):
                self.logger.info(f"PD {pd_name} already exists, clean schedules first and re-add later...")
                self.pd.delete_all_existing_schedules(pd_name)
            else:
                self.logger.info(f"PD {pd_name} doesn't exist, create a new one...")
                self.pd.create_pd(pd_name)
            pd_info = self.pd.get_pd(pd_name)
            existing_vms_in_pd = [_["vm_id"] for _ in pd_info["vms"]]   # only key "vm_id" in response
            vms_to_remove = [vm_id for vm_id in existing_vms_in_pd if vm_id not in vm_uuid_list]
            for vm_id in vm_id_list:
                for _ in vms_to_remove:
                    if _ in vm_id:
                        continue
                if vm_id in vms_to_remove:
                    vms_to_remove.remove(vm_id)
            if vms_to_remove:
                self.pd.remove_vms_from_pd(vms_to_remove, pd_name)
                self.pd.delete_all_existing_schedules(pd_name)
                self.logger.info("Removal completed, sleep 10 secs...")
                time.sleep(10)
            self.logger.info("Adding VM to PD.")
            vms_to_add = [vm_uuid for vm_uuid in vm_uuid_list if vm_uuid not in existing_vms_in_pd]
            self.logger.info(f"VMs to add: {vms_to_add}")
            for vm_id in vms_to_add:
                try:
                    self.pd.add_vm_to_pd(vm_id, pd_name)
                except flaskex.BadGateway as e:
                    self.ilg.write(
                        "Failed to add VM to PD! Checking if it's already protected by other PDs...", "warning"
                    )
                    self.ilg.write(f"Original error: {e}", severity="warning")
                    if not self._is_vm_already_protected_by_pd(vm_id):
                        raise flaskex.BadGateway(
                            f"Adding VM {vm_id} to PD failed, meanwhile it's not protected by any other PDs.")
            self._add_schedules(pd_name, target_site_name, suffix)

    def _is_vm_already_protected_by_pd(self, vm_id):
        pd_list = self.pd.get_pds()
        for pd in pd_list:
            if self.pd.is_vm_in_pd(vm_id, pd["name"]):
                return True
        return False

    def _add_schedules(self, pd_name, target_site_name, pd_suffix):
        self.logger.info("Adding schedules to PD")
        if pd_suffix == "Gold_CCG":
            schedules = [{
              "IntervalType": "HOURLY",
              "Interval": 2,
              "LocalCount": 24,
              "RemoteCount": 0
            }, {
              "IntervalType": "DAILY",
              "Interval": 1,
              "LocalCount": 35,
              "RemoteCount": 7
            }]
        elif pd_suffix == "Silver_CCG":
            schedules = [{
              "IntervalType": "HOURLY",
              "Interval": 6,
              "LocalCount": 35,
              "RemoteCount": 1
            }, {
              "IntervalType": "DAILY",
              "Interval": 1,
              "LocalCount": 35,
              "RemoteCount": 7
            }]

        def _generate_start_time():
            # today 0:30 in target timezone -> time in current timezone -> microsec timestamp
            target_tz_str = self.cluster_info.timezone     # assume timezone are always like "UTC+08:00"
            target_timezone = tz.tzstr(target_tz_str)
            local_time = datetime.now(tz=target_timezone).replace(hour=0, minute=30, second=0, microsecond=0).astimezone(tz.tzlocal())
            timestamp_usec = datetime.timestamp(local_time) * 1_000_000
            self.logger.info(f"target timezone: {target_tz_str}")
            self.logger.info(f"local_time: {local_time}")
            return timestamp_usec

        for s in schedules:
            self.pd.add_pd_schedule(
                pd_name, s["IntervalType"], s["Interval"], s["LocalCount"],
                s["RemoteCount"], f"RS_{target_site_name}" if target_site_name else None,
                _generate_start_time()
            )

    def _clean_pds(self):
        if not self.benchmark['backup']['snapshot_expire_interval_days']:
            return
        self.logger.title("Lets do some PD Cleanups.")
        self.logger.info("Pulling PDs with no schedules....")
        existing_pds_with_no_schedule = [pd for pd in self.pd.get_pds()['entities'] if len(pd["cron_schedules"]) == 0]
        self.logger.info(f"We have {len(existing_pds_with_no_schedule)} pds with no schedule.")
        for pd in existing_pds_with_no_schedule:
            pd_name = pd["name"]
            self.logger.info(f"Checking snaptshots inside PD {pd_name}...")
            snapshots = self.pd.get_pd_snapshots(pd_name)
            for snap in snapshots["entities"]:
                if self._is_snapshot_too_old(snap):
                    self.pd.remove_pd_single_snapshot(pd_name, snap["snapshotId"])
        self.logger.info("PD cleanup done.")

    def _if_delete_pd(self, snapshots):
        self.logger.info("Checking snapshots inside PD to decide if we need to delete PD...")
        if len(snapshots["entities"]) == 0:
            self.logger.info("No snapshots existing in PD, PD will be deleted...")
            return True
        expire_days = abs(self.benchmark['backup']['snapshot_expire_interval_days'])
        self.logger.info(
            f"Checking if all the snapshots are older than '{expire_days}' days...")
        for snap in snapshots["entities"]:
            snap_date_create_time = datetime.fromtimestamp(snap["snapshotCreateTimeUsecs"] / 1000000)
            # if any one of the snap's create time shorter than 60 days, will not delete the pd
            snapshot_age_days = (datetime.now(pytz.timezone('UTC')) - snap_date_create_time.replace(tzinfo=pytz.timezone('UTC'))).days
            if abs(snapshot_age_days) <= expire_days:
                self.logger.info(f"Current snapshot age: {abs(snapshot_age_days)} days.")
                self.logger.info(
                    f"Since there is at least one snapshot is younger than {expire_days} days old, will not delete the PD.")
                return False
        self.logger.info(f"All snapshots are older than {expire_days} days old, the PD will be deleted.")
        return True

    def _is_snapshot_too_old(self, snap, life_expectancy=None):
        # age is in days
        if not life_expectancy:
            life_expectancy = abs(self.benchmark['backup']['snapshot_expire_interval_days'])
        self.logger.info(f"Checking the snapshot age... snapshot id: {snap['snapshotId']}")
        self.logger.info(f"Snapshots older than {life_expectancy} days will be deleted.")
        snap_date_create_time = datetime.fromtimestamp(snap["snapshotCreateTimeUsecs"] / 1000000)
        snapshot_age = (
            datetime.now(pytz.timezone('UTC')) - snap_date_create_time.replace(tzinfo=pytz.timezone('UTC'))
        ).days
        self.logger.info(f"Current snapshot age: {abs(snapshot_age)} days.")
        return snapshot_age > life_expectancy

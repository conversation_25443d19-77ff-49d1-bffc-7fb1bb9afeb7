$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}

function Launch-Job(){
    Begin {
        #Import required modules from the project folder
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        #Load basic variable that contains required for DB connection
        #Load data from the table dh_retail_ntx_pc
        #Load data from the table dh_retail_ntx_pe
        #Create an empty array CollectionUpdate which stores data those already exsits in the table and needs to be update
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhRetailNtxPc -Vars $Vars
            $DhPEs            = Select-DhRetailNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CollectionUpdate = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We have '$($DhPEs.Count)' PEs need to update" -DumpFile $DumpFile
        #Rolling call PEs exist in the table
        $DhPEs | Foreach-Object -ThrottleLimit 50 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We're now working on '$($_.name)'" -DumpFile $using:DumpFile
            $PE         = $_
            $UpdateDict = $using:CollectionUpdate
            $PC         = $using:DhPCs | Where-Object {$_.id -eq $PE.pc_id}
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -Pword (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication for '$($PE.name)'" -DumpFile $using:DumpFile
                return
            }
            $LcmMap = [PSCustomObject]@{
                'fqdn'               = $PE.fqdn
                'foundation_version' = "NA"
                'lcm_version'        = "NA"
                'spp_version'        = "NA"
            }
            $LcmApiVer = 'v4'
            if ($LcmFwCall = Rest-Genesis-Get-LcmFw -Fqdn $PE.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "LCM version of '$($PE.name)' is '$($LcmFwCall.semantic_version)'" -DumpFile $using:DumpFile
                $LcmMap.lcm_version = $LcmFwCall.semantic_version
            }
            switch ($LcmMap.lcm_version) {
                '2.4.5.2' {
                    $LcmApiVer = 'v1'
                }
                '2.5.0.2' {
                    $LcmApiVer = 'v4'
                }
            }
            Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "We'll call LCM Api '$($LcmApiVer)' towards '$($PE.name)' for getting Foundation version" -DumpFile $using:DumpFile
            if ($LcmCall = & $('Rest-Lcm-' + $LcmApiVer + '-Get-LcmEntity') -Fqdn $PE.fqdn -Auth $Auth) {
                switch ($LcmApiVer) {
                    'v1' {
                        $LcmMap.foundation_version = ($LcmCall | Where-Object {$_.entity_model -eq "Foundation"}).version | Get-Unique
                        $LcmMap.spp_version        = ($LcmCall | Where-Object {$_.entity_class -eq "SPP"}).version | Get-Unique
                    }
                    'v4' {
                        $LcmMap.foundation_version = ($LcmCall | Where-Object {$_.entityModel -eq "Foundation"}).version | Get-Unique
                        $LcmMap.spp_version        = ($LcmCall | Where-Object {$_.entityClass -eq "SPP"}).version | Get-Unique
                    }
                }
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "The Foundation version of $($PE.name) is $($LcmMap.foundation_version)" -DumpFile $DumpFile
                Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "The SPP version of $($PE.name) is $($LcmMap.spp_version)" -DumpFile $DumpFile
            }
            $UpdateDict.Add($LcmMap)
        } -UseNewRunspace
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_retail_ntx_pe]" -DumpFile $DumpFile
        Update-Table-DhRetailNtxPe-ByFqdn -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job
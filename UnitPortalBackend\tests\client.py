import os

import pytest
from flask import Flask

from models.database import db
from business.restful.blueprint import distributedhosting
from business.generic.commonfunc import DBConfig
os.environ.setdefault('DPC_DB_NAME', 'DPCDB_UnitTest')


@pytest.fixture()
def app():
    app = Flask(__name__)
    app.register_blueprint(distributedhosting)
    app.config.update({
        # 'static_url_path': '/tests/static',
        "TESTING": True,
        'SQLALCHEMY_DATABASE_URI': DBConfig()()
    })
    app.app_context().push()
    db.init_app(app)
    yield app


@pytest.fixture()
def client(app):
    return app.test_client()


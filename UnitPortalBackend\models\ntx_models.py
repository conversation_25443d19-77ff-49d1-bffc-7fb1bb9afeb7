import sqlalchemy
import werkzeug.exceptions as flaskex
from sqlalchemy.ext.hybrid import hybrid_method

from models.database import db, ma


class ModelPrismCentral(db.Model):
    __tablename__        = 'dh_retail_ntx_pc'
    id                   = db.Column(db.Integer, primary_key=True)
    fqdn                 = db.Column(db.String(50), unique=True, nullable=False)
    lcm_version          = db.Column(db.String(200))
    calm_version         = db.Column(db.String(200))
    cmu_version          = db.Column(db.String(200))
    epsilon_version      = db.Column(db.String(200))
    flowsecurity_Version = db.Column(db.String(200))
    licensing_version    = db.Column(db.String(200))
    ncc_version          = db.Column(db.String(200))
    pc_version           = db.Column(db.String(200))
    uuid                 = db.Column(db.String(200))
    cluster_number       = db.Column(db.Integer)
    ip                   = db.Column(db.String(50))
    tier                 = db.Column(db.String(50))
    domain               = db.Column(db.String(50))
    darksite             = db.Column(db.String(50))
    central_pe_fqdn      = db.Column(db.String(200))
    service_account      = db.Column(db.String(50))
    cert_expiry_date     = db.Column(db.String(100))
    oneview_region       = db.Column(db.String(50))
    bmk_id               = db.Column(db.Integer)
    last_update          = db.Column(db.String(50))
    # timezone        = db.Column(db.String(200))
    # pes = db.relationship('ModelPrismElement' , backref = 'pc' , cascade = "all, delete-orphan")


class ModelPrismCentralSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelPrismCentral
        load_instance = True


class ModelPrismElement(db.Model):
    __tablename__                   = 'dh_retail_ntx_pe'
    id                              = db.Column(db.Integer, primary_key=True)
    name                            = db.Column(db.String(50))
    fqdn                            = db.Column(db.String(100), unique=True, nullable=False)
    country_code                    = db.Column(db.String(50))
    site_code                       = db.Column(db.String(50))
    node_number                     = db.Column(db.Integer)
    vm_number                       = db.Column(db.Integer)
    cvm_number                      = db.Column(db.Integer)
    aos_version                     = db.Column(db.String(100))
    ahv_version                     = db.Column(db.String(100))
    uuid                            = db.Column(db.String(100))
    remote_site_dsc                 = db.Column(db.String(100))
    remote_backup_number            = db.Column(db.String(100))
    status                          = db.Column(db.String(50))
    prism                           = db.Column(db.String(50))
    timezone                        = db.Column(db.String(50))
    total_memory                    = db.Column(db.Integer)
    total_storage                   = db.Column(db.Integer)
    total_cpu                       = db.Column(db.Integer)
    pc_id                           = db.Column(db.Integer)
    foundation_version              = db.Column(db.String(50))
    lcm_version                     = db.Column(db.String(50))
    ncc_version                     = db.Column(db.String(50))
    spp_version                     = db.Column(db.String(100))
    license_category                = db.Column(db.String(100))
    license_class                   = db.Column(db.String(100))
    license_cores_capacity          = db.Column(db.String(100))
    license_flash_capacity          = db.Column(db.String(100))
    license_hdd_capacity            = db.Column(db.String(100))
    license_cores_licensed          = db.Column(db.String(100))
    license_flash_licensed          = db.Column(db.String(100))
    license_hdd_licensed            = db.Column(db.String(100))
    cert_expiry_date                = db.Column(db.String(100))
    is_central_pe                   = db.Column(db.Boolean)
    site_type                       = db.Column(db.String(100))
    bandwidth_dsc                   = db.Column(db.Float)
    darksite_bandwidth              = db.Column(db.String(50))
    remote_site_last                = db.Column(db.String(100))
    bandwidth_runtime               = db.Column(db.Float)
    remote_site_runtime             = db.Column(db.String(50))
    cbd_id                          = db.Column(db.String(50))
    bu_type                         = db.Column(db.String(50))
    bu_code                         = db.Column(db.String(50))
    bmk_id                          = db.Column(db.Integer)
    last_update                     = db.Column(db.String(50))

    @hybrid_method
    def get_pe_by_codes(self, country_code, site_code):
        try:
            pe = ModelPrismElement.query.filter_by(country_code=country_code, site_code=site_code).all()
            return pe
        except sqlalchemy.exc.MultipleResultsFound as e:
            db.session.rollback()
            msg = f"Multiple pe info found with country_code {country_code} and site_code {site_code}! Original error: {e}"
            raise flaskex.InternalServerError(msg)
        
    def check_ifntx_existence_db (self, cluster_name):
        return ModelPrismElement.query.filter(ModelPrismElement.status == "Running", ModelPrismElement.name == cluster_name).first()

    @hybrid_method
    def get_prism_by_pe_name(self, pe_fqdn):
        try:
            return ModelPrismElement.query.filter_by(fqdn=pe_fqdn).one().prism
        except sqlalchemy.exc.NoResultFound as e:
            raise flaskex.InternalServerError(f"Can't find pe with fqdn {pe_fqdn}! Original error: {e}")
        except sqlalchemy.exc.MultipleResultsFound as e:
            raise flaskex.InternalServerError(f"Multiple pe info found with fqdn {pe_fqdn}! Original error: {e}")


class ModelPrismElementSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelPrismElement
        load_instance = True


class ModelRetailNutanixVM(db.Model):
    __tablename__ = 'dh_retail_ntx_vm'
    id            = db.Column(db.Integer, primary_key=True)
    name          = db.Column(db.String(255))
    uuid          = db.Column(db.String(255))
    pe_name       = db.Column(db.String(100))
    pe_fqdn       = db.Column(db.String(100))
    pe_uuid       = db.Column(db.String(100))
    host_name     = db.Column(db.String(100))
    host_uuid     = db.Column(db.String(100))
    description   = db.Column(db.String(1000))
    os            = db.Column(db.String(255))
    type          = db.Column(db.String(255))
    disk          = db.Column(db.String(1000))
    memory        = db.Column(db.String(100))
    cpu_core      = db.Column(db.Integer)
    power_state   = db.Column(db.String(50))
    ip            = db.Column(db.String(1000))
    boot_type     = db.Column(db.String(100))
    network_vlan  = db.Column(db.String(100))
    is_cvm        = db.Column(db.Boolean)
    status        = db.Column(db.String(50))
    pe_id         = db.Column(db.Integer)
    last_update   = db.Column(db.String(50))


class ModelRetailNutanixVMSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelRetailNutanixVM
        load_instance = True


class ModelRetailNutanixHost(db.Model):
    __tablename__       = 'dh_retail_ntx_host'
    id                  = db.Column(db.Integer, primary_key=True)
    name                = db.Column(db.String(255))
    uuid                = db.Column(db.String(255))
    pe_name             = db.Column(db.String(100))
    pe_fqdn             = db.Column(db.String(100))
    pe_uuid             = db.Column(db.String(100))
    disk_number         = db.Column(db.Integer)
    sn                  = db.Column(db.String(100))
    model               = db.Column(db.String(100))
    memory              = db.Column(db.String(100))
    cpu_core_number     = db.Column(db.Integer)
    cpu_model           = db.Column(db.String(50))
    ahv_ip              = db.Column(db.String(100))
    cvm_ip              = db.Column(db.String(100))
    ipmi_ip             = db.Column(db.String(100))
    ipmi_version        = db.Column(db.String(100))
    status              = db.Column(db.String(50))
    pe_id               = db.Column(db.Integer)
    bios_version        = db.Column(db.String(50))
    controller_version  = db.Column(db.String(50))
    controller_model    = db.Column(db.String(50))
    nic0_uuid           = db.Column(db.String(100))
    nic0_mac            = db.Column(db.String(100))
    nic0_speed          = db.Column(db.String(100))
    nic0_mtu            = db.Column(db.String(100))
    nic0_sw_device      = db.Column(db.String(100))
    nic0_sw_port        = db.Column(db.String(100))
    nic0_sw_vendor      = db.Column(db.String(255))
    nic0_sw_vlan        = db.Column(db.String(100))
    nic1_uuid           = db.Column(db.String(100))
    nic1_mac            = db.Column(db.String(100))
    nic1_speed          = db.Column(db.String(100))
    nic1_mtu            = db.Column(db.String(100))
    nic1_sw_device      = db.Column(db.String(100))
    nic1_sw_port        = db.Column(db.String(100))
    nic1_sw_vendor      = db.Column(db.String(255))
    nic1_sw_vlan        = db.Column(db.String(100))
    last_update         = db.Column(db.String(50))


class ModelRetailNutanixHostSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelRetailNutanixHost
        load_instance = True


class ModelRetailNutanixOneview(db.Model):
    __tablename__   = 'dh_retail_ntx_oneview'
    id              = db.Column(db.Integer, primary_key=True)
    fqdn            = db.Column(db.String(255))
    region          = db.Column(db.String(50))
    service_account = db.Column(db.Integer)


class ModelRetailNutanixOneviewSchema(ma.Schema):
    class Meta:
        fields = ('id', 'fqdn', 'region', 'service_account')


class ModelRetailNutanixState(db.Model):
    __tablename__    = 'dh_retail_ntx_state'
    id               = db.Column(db.Integer, primary_key=True)
    pe_uuid          = db.Column(db.String(100))
    pe_id            = db.Column(db.Integer)
    pe_name          = db.Column(db.String(100))
    pc_id            = db.Column(db.Integer)
    pc_name          = db.Column(db.String(100))
    date             = db.Column(db.String(100))
    cpu_avg          = db.Column(db.String(100))
    cpu_avg_7_days   = db.Column(db.String(100))
    cpu_avg_15_days  = db.Column(db.String(100))
    cpu_max          = db.Column(db.String(100))
    cpu_max_7_days   = db.Column(db.String(100))
    cpu_max_15_days  = db.Column(db.String(100))
    mem_avg          = db.Column(db.String(100))
    mem_avg_7_days   = db.Column(db.String(100))
    mem_avg_15_days  = db.Column(db.String(100))
    mem_max          = db.Column(db.String(100))
    mem_max_7_days   = db.Column(db.String(100))
    mem_max_15_days  = db.Column(db.String(100))
    disk_avg         = db.Column(db.String(100))
    disk_avg_7_days  = db.Column(db.String(100))
    disk_avg_15_days = db.Column(db.String(100))
    disk_max         = db.Column(db.String(100))
    disk_max_7_days  = db.Column(db.String(100))
    disk_max_15_days = db.Column(db.String(100))


class ModelRetailNutanixStateSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'pe_uuid', 'pe_id', 'pe_name', 'date', 'cpu_avg', 'cpu_avg_7_days', 'cpu_avg_15_days', 'cpu_max',
            'cpu_max_7_days', 'cpu_max_15_days', 'mem_avg', 'mem_avg_7_days', 'mem_avg_15_days', 'disk_avg',
            'disk_avg_7_days', 'disk_avg_15_days', 'disk_max', 'disk_max_7_days', 'disk_max_15_days'
        )


class ModelRetailNutanixSizing(db.Model):
    __tablename__    = 'dh_retail_ntx_sizing'
    id               = db.Column(db.Integer, primary_key=True)
    pe_uuid          = db.Column(db.String(100))
    pe_id            = db.Column(db.Integer)
    pe_name          = db.Column(db.String(100))
    pc_id            = db.Column(db.Integer)
    pc_name          = db.Column(db.String(100))
    date             = db.Column(db.String(100))
    used_cpu         = db.Column(db.String(100))
    total_cpu        = db.Column(db.String(100))
    used_memory      = db.Column(db.String(100))
    total_memory     = db.Column(db.String(100))
    used_storage     = db.Column(db.String(100))
    total_storage    = db.Column(db.String(100))


class ModelRetailNutanixSizingSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'pe_uuid', 'pe_id', 'pe_name', 'pc_id', 'date', 'used_cpu', 'total_cpu', 'used_memory',
            'total_memory', 'used_storage', 'total_storage'
        )


class ModelNTXMOVELog(db.Model):
    __tablename__ = 'dh_retail_ntx_move_log'
    id = db.Column(db.Integer, primary_key=True)
    tasktype = db.Column(db.String(50))
    logdate = db.Column(db.String(50))
    severity = db.Column(db.String(50))
    # cluster_id = db.Column(db.String(50))
    loginfo = db.Column(db.String(8000))
    taskid = db.Column(db.Integer, db.ForeignKey('dh_retail_sli_cluster.id'))


class ModelNTXMOVELogSchema(ma.Schema):
    class Meta:
        fields = ('id', 'tasktype', 'logdate', 'severity', 'loginfo', 'taskid')


class ModelRetailMOVETask(db.Model):
    __tablename__      = 'dh_retail_ntx_move_task'
    id                 = db.Column(db.Integer , primary_key=True)
    pe                 = db.Column(db.String(100))
    cluster            = db.Column(db.String(100))
    createdate        = db.Column(db.String(100))
    status             = db.Column(db.String(100))
    datasyncstatus   = db.Column(db.String(100))
    planuuid          = db.Column(db.String(100))
    pid                = db.Column(db.Integer)
    creater            = db.Column(db.String(100))
    detaillogpath    = db.Column(db.String(255))
    tasktype           = db.Column(db.String(100))
    
    def check_ifmovetask_existence (self, cluster_name, tasktype):
        return ModelRetailMOVETask.query.filter(ModelRetailMOVETask.status == "In Progress", ModelRetailMOVETask.tasktype == tasktype, ModelRetailMOVETask.cluster == cluster_name).first()


class ModelRetailMOVETaskSchema(ma.Schema):
    class Meta:
        fields = ('id', 'pe', 'cluster', 'createdate', 'status', 'datasyncstatus', 'planuuid', 'pid', 'creater', 'detaillogpath', 'tasktype')


class ModelRetailNutanixVMAction(db.Model):
    __tablename__ = 'dh_retail_ntx_vm_action'
    id = db.Column(db.Integer, primary_key=True)
    pe = db.Column(db.String(50))
    vm_name = db.Column(db.String(50))
    action_type = db.Column(db.String(50))
    user_name = db.Column(db.String(50))
    time = db.Column(db.String(50))
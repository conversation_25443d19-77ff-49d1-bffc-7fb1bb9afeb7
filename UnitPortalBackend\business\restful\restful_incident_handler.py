import json
import traceback
import sys
import os
import logging
from flask import Response, abort, request, current_app
from flask_restful import Resource
from business.nowit.incident_devourer import IncidentDevourer
from business.nowit.incident_handler_tools import IncidentHandlerTools
from business.nowit.incident_analysis_transaction import IncidentTransdata
from flask_apispec import use_kwargs
from swagger.incident_analysis_schema import CheckTimeStampSchema, CheckModelSchema
from business.generic.commonfunc import FileDownloader
from .route import route


@route('/api/v1/incident-handler')
class RestfulIncidentHandler(Resource):
    def get(self):
        try:
            print('Incident-Handler starts hunting for incidents!')
            incident_devourer = IncidentDevourer()
            run_incident_devourer = incident_devourer.trigger_incident_devourer()
            if run_incident_devourer:
                return Response(json.dumps(run_incident_devourer), status=200, mimetype='application/json')
            abort(500, 'Request is failed!')
        except Exception as e:
            print(str(repr(traceback.format_exception(sys.exception()))))
            return e
        
        
@route('/api/v1/incident-handler/active-incidents')
class RestfulGetActiveIncidents(Resource):
    def get(self):
        try:
            inc_handler_tools = IncidentHandlerTools()
            get_gdh_active_incidents = inc_handler_tools.get_gdh_active_incidents() 
            if get_gdh_active_incidents:
                return Response(json.dumps(get_gdh_active_incidents), status=200, mimetype='application/json')
            abort(500, 'Request is failed!')
        except Exception as e:
            print(str(repr(traceback.format_exception(sys.exception()))))
            return e


@route('/api/v1/incident-handler/unassigned-incidents')
class RestfulGetUnassingedIncidents(Resource):
    def get(self):
        try:
            inc_handler_tools = IncidentHandlerTools()
            get_unassinged_incidents = inc_handler_tools.get_unassigned_incidents() 
            if get_unassinged_incidents:
                return Response(json.dumps(get_unassinged_incidents), status=200, mimetype='application/json')
            abort(500, 'Request is failed!')
        except Exception as e:
            print(str(repr(traceback.format_exception(sys.exception()))))
            return e      
        
        
@route('/api/v1/incident-handler/total-resolved-incidents')
class RestfulGetTotalResolvedIncNum(Resource):
    def get(self):
        try:
            inc_handler_tools = IncidentHandlerTools()
            get_total_resolved_inc_num = inc_handler_tools.get_resolved_inc_num() 
            if get_total_resolved_inc_num:
                return Response(json.dumps(get_total_resolved_inc_num), status=200, mimetype='application/json')
            abort(500, 'Request is failed!')
        except Exception as e:
            print(str(repr(traceback.format_exception(sys.exception()))))
            return e
        
        
@route('/api/v1/incident-handler/resolved-incidents-today')
class RestfulGetResolvedIncNumToday(Resource):
    def get(self):
        try:
            inc_handler_tools = IncidentHandlerTools()
            get_resolved_inc_num_today = inc_handler_tools.get_resolved_inc_num_today() 
            if get_resolved_inc_num_today:
                return Response(json.dumps(get_resolved_inc_num_today), status=200, mimetype='application/json')
            abort(500, 'Request is failed!')
        except Exception as e:
            print(str(repr(traceback.format_exception(sys.exception()))))
            return e


@route('/api/v1/incident_analysis_custom')
class RestfulGetAnalysisCustomData(Resource):
    """ get the incident count by custom date"""
    @use_kwargs(CheckTimeStampSchema(), location="json")
    def post(self, *args, **kwargs): # pylint: disable=W0613
        try:
            body = request.get_json(force=True)
            start_time = body["start_time"]
            end_time = body["end_time"]
            incident_analysis = IncidentTransdata()
            custom_data = incident_analysis.get_chart_by_custom(start_timestamp=start_time, end_timestamp=end_time)
            if custom_data:
                return Response(custom_data, status=200, mimetype='application/json')
            abort(500, 'Request is failed!')
        except Exception as e:
            print(str(repr(traceback.format_exception(sys.exception()))))
            return e


@route('/api/v1/incident_sheet')
class RestfulGetAnalysisIncSheet(Resource):
    """ get the incident info from db"""
    @use_kwargs(CheckModelSchema(), location='json')
    def post(self, *args, **kwargs): # pylint: disable=W0613
        try:
            body = request.get_json(force=True)
            start_time = body['start_time']  # timestamp int/length10
            end_time = body['end_time']
            model_name = body["model_name"]
            incident_analysis = IncidentTransdata()
            inc_sheet = incident_analysis.get_inc_info_sheet(start_timestamp=start_time, end_timestamp=end_time,
                                                             model_name=model_name)
            if inc_sheet:
                return Response(inc_sheet, status=200, mimetype='application/json')
            abort(500, 'Request is failed!')
        except Exception as e:
            print(str(repr(traceback.format_exception(sys.exception()))))
            return e


@route('/api/v1/incident_sheet_download')
class RestfulDownloadAnalysisData(Resource):
    """ download the incident sheet"""
    @use_kwargs(CheckTimeStampSchema(), location="json")
    def post(self, *args, **kwargs): # pylint: disable=W0613
        try:
            body = request.get_json(force=True)
            start_time = body['start_time']  # timestamp int/length10
            end_time = body['end_time']
            incident_analysis = IncidentTransdata()
            res = incident_analysis.get_download_sheet(start_timestamp=start_time, end_timestamp=end_time)
            file_path = FileDownloader(data=res).download()
            logging.info(f"Downloading file {file_path}...")

            def gene():
                with open(file_path) as f:
                    yield from f
                os.remove(file_path)

            res = current_app.response_class(gene(), mimetype='text/csv')
            res.headers.set('Content-Disposition', 'attachment', filename='data.csv')
            return res
        except Exception as e:
            print(str(repr(traceback.format_exception(sys.exception()))))
            return e


@route('/api/v1/incident-handler/unit-portal-incident-data')
class RestfulGetUnitPortalIncData(Resource):
    def get(self):
        try:
            inc_handler_tools = IncidentHandlerTools()
            get_unit_portal_inc_data = inc_handler_tools.generate_unit_portal_incident_handler_data() 
            if get_unit_portal_inc_data:
                return Response(json.dumps(get_unit_portal_inc_data), status=200, mimetype='application/json')
            abort(500, 'Request is failed!')
        except Exception as e:
            print(str(repr(traceback.format_exception(sys.exception()))))
            return e

#Python Script
#Created by : <PERSON><PERSON>
#26-June-2023
#<PERSON><PERSON>t to extract all the data in a flaskfile copy to a text file then zipit with date_time format.
#Need to schedule this activity weekly wise.


import os
import shutil
#import schedule
import time
import win32com.client
from datetime import datetime


class Task_Archive_FlaskFiles:
    #Global declaration and assign values.
    global today
    global weekday
    
    def __init__(self):
        
        self.schedular = win32com.client.Dispatch('Schedule.Service')
        self.schedular.Connect()
        #root_folder = schedular.GetFolder('\\UnitPortal')
        self.task_def = self.schedular.NewTask(0)
        #shutil.make_archive(file2, 'zip', dir_name)
        self.arc_formats = shutil.get_archive_formats()
        self.today = datetime.now()
        self.weekday = self.today.weekday()
        #Action to get current date and time
        self.current_datetime = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        self.str_current_datetime = str(self.current_datetime)
   
# Trigger time
#set_time = datetime.datetime(2022,9,4,18,44,0,500000)
    def task_trigger(self):
        set_time = datetime.datetime.now() + datetime.timedelta(minutes=1)
        TASK_TRIGGER_TIME = 1
        trigger = self.task_def.Triggers.Create(TASK_TRIGGER_TIME)
        trigger.StartBoundary = set_time.isoformat()
        
# set value for day to run the script accordingly.(this function is not yet completed)   
    def task_run_weekly(dayvalue,self):
        if (weekday == dayvalue):
         #run my script 0 = monday , 1 = tuestday...
         true = 0
        else:
         true = 1
         

        
#Action to open file and read and write to another file for archiving the data.
    def Archive_files(self):
        
     #Action to Check for existing file in the zip folder which needs to be deleted.
     folder = 'C:\\IKEA Infra\\Technology Retail\\Support_Competence\\Zip_files\\'
    
     if os.path.isdir(folder):
         if not os.listdir(folder):
          print("Directory is empty..will create a new archive file!!")
         else:    
            print("Directory is not empty..Old file will be deleted!!")
            for f in os.listdir(folder):
             os.remove(os.path.join(folder, f))       
     else:
        print("Given directory doesn't exist")
        
     #open file1 in reading mode
     file1 = open('C:\\IKEA Infra\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\Log\\flask_main','r')

     #Action to  create a file object with date and time name along with extension
     file_name = self.str_current_datetime+"_Backup_file"
     file2 = open('C:\\IKEA Infra\\Technology Retail\\Support_Competence\\Zip_files\\'+file_name, 'w+')

     #read from flask file and write to file2
     for line in file1:
      file2.write(line)

     #Close file1 and file2
     file1.close()
     file2.close()

     #Action to archive/zip the output file in a folder.
     shutil.make_archive('C:\\IKEA Infra\\Technology Retail\\Support_Competence\\'+file_name, 'zip', 'C:\\IKEA Infra\\Technology Retail\\Support_Competence\\Zip_files')
     
     #Action to clear the data from Flask file.
     file_erase = open('C:\\IKEA Infra\Technology Retail\\UnitPortal_Backend\\UnitPortalBackend\\Log\\flask_main','w')
     file_erase.write("")
     file_erase.close()


     #print the files after creating file2, check the output
     print("\n")
     print("data from flask file has been archived.")
     

funTaskArchive = Task_Archive_FlaskFiles()
funTaskArchive.Archive_files()
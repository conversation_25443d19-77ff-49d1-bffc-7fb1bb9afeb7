from models.database import db, ma
from sqlalchemy.sql import func
from sqlalchemy import DateTime
from sqlalchemy import and_
from sqlalchemy.ext.hybrid import hybrid_method
from sqlalchemy.ext.hybrid import hybrid_property
import werkzeug.exceptions as flaskex
import sqlalchemy
import logging


class ModelUser(db.Model):
    __tablename__          = "users"
    id                     = db.Column(db.Integer, primary_key=True)
    username               = db.Column(db.String(100), unique=True, nullable=False)
    pwd                    = db.Column(db.String(50))
    role                   = db.Column(db.String(50))
    lastlogondate          = db.Column(db.String(100))
    lastmove               = db.Column(db.String(50))
    domain                 = db.Column(db.String(50))
    token                  = db.Column(db.String(100))
    location               = db.Column(db.String(100))
    memo_id                = db.Column(db.String(100))
    profile                = db.Column(db.String(1000))

    @hybrid_method
    def create_new_user(self, name, role="pmuser", domain="ikea.com"):
        try:
            name = name.lower()
            if "@ikea.com" in name:
                memo_id = name.split("@ikea.com")[0]
            else:
                memo_id = name
                name += "@ikea.com"
            new_user = ModelUser(username=name, role=role, domain=domain, memo_id=memo_id)
            db.session.add(new_user)
            db.session.commit()
            return new_user
        except sqlalchemy.exc.IntegrityError:
            raise flaskex.Conflict(f"User '{name}' already exists!")

    @hybrid_method
    def delete_user(self, user_id):
        # delete a user and corresponding role mappings
        logging.info(f"Start to delete user with id '{user_id}...")
        try:
            user = ModelUser.query.filter_by(id=user_id).all()
            if not user:
                logging.warning(f"No user is found by id '{user_id}'.")
            ModelUser.query.filter_by(id=user_id).delete()
            logging.info("Deleting user related role mappings...")
            mappings = ModelUserGroupRoleMapping.query.filter_by(user_id=user_id).all()
            if not mappings:
                logging.warning(f"No role mapping is found by user id '{user_id}'.")
            ModelUserGroupRoleMapping.query.filter_by(user_id=user_id).delete()
            db.session.commit()
            logging.info("User and corresponding role mappings are deleted.")
        except Exception as e:
            db.session.rollback()
            raise flaskex.InternalServerError(f"Delete user failed. Error: {e}")

    # @hybrid_method
    # def update_user(self, user_name, roleid, is_single_transaction=True):
    #     try:
    #         role_id = ",".join(map(str, sorted(roleid)))
    #         user = ModelUser.query.filter_by(username=user_name).one()
    #         user.role = role_id
    #         if is_single_transaction:
    #             db.session.commit()
    #     except sqlalchemy.exc.NoResultFound as e:
    #         db.session.rollback()
    #         msg = f"Can't find user with user_id {user_name}!"
    #         logging.error(msg)
    #         return {"message": msg, "error_message": str(e)}, 500
    #     except sqlalchemy.exc.MultipleResultsFound as e:
    #         db.session.rollback()
    #         msg = f"Multiple user found with user_id {user_name}!"
    #         logging.error(msg)
    #         return {"message": msg, "error_message": str(e)}, 500


class ModelUserSchema(ma.Schema):
    class Meta:
        fields = ('id', 'username', 'pwd', 'role', 'lastlogondate', 'lastmove', 'domain', 'token', 'location')


class ModelGroup(db.Model):
    __tablename__          = "groups"
    id                     = db.Column(db.Integer, primary_key=True)
    group_name             = db.Column(db.String(100), unique=True, nullable=False)
    roles                  = db.Column(db.String(50))

    def create_group(self, name, role_ids):
        new_group = ModelGroup(group_name=name, roles=role_ids)
        db.session.add(new_group)
        db.session.commit()
        return new_group

    def delete(self, group_name, is_single_transaction=True):
        try:
            group = ModelGroup.query.filter_by(group_name=group_name).one()
            db.session.delete(group)
            if is_single_transaction:
                db.session.commit()
        except sqlalchemy.exc.NoResultFound as e:
            db.session.rollback()
            msg = f"Can't find group {group_name}!"
            logging.error(msg)
            return {"message": msg, "error_message": str(e)}, 500
        except sqlalchemy.exc.MultipleResultsFound as e:
            db.session.rollback()
            msg = f"Multiple group found {group_name}!"
            logging.error(msg)
            return {"message": msg, "error_message": str(e)}, 500

    def put(self, group_name, roleid, is_single_transaction=True):
        try:
            role_id = ",".join(map(str, sorted(roleid)))
            group = ModelGroup.query.filter_by(group_name=group_name).one()
            group.roles = role_id
            if is_single_transaction:
                db.session.commit()
        except sqlalchemy.exc.NoResultFound as e:
            db.session.rollback()
            msg = f"Can't find group with group {group_name}!"
            logging.error(msg)
            return {"message": msg, "error_message": str(e)}, 500
        except sqlalchemy.exc.MultipleResultsFound as e:
            db.session.rollback()
            msg = f"Multiple group found with group {group_name}!"
            logging.error(msg)
            return {"message": msg, "error_message": str(e)}, 500


class ModelGroupSchema(ma.Schema):
    class Meta:
        fields = ('id', 'group_name', 'roles')


class ModelGroupRoleMapping(db.Model):
    __tablename__          = "group_role_mapping"
    id                     = db.Column(db.Integer, primary_key=True)
    group_id               = db.Column(db.Integer, nullable=False)
    role_id                = db.Column(db.Integer)


class ModelGroupRoleMappingSchema(ma.Schema):
    class Meta:
        fields = ('id', 'group_id', 'role_id')


class ModelRole(db.Model):
    __tablename__          = "role"
    id                     = db.Column(db.Integer, primary_key=True)
    name                   = db.Column(db.String(255), unique=True)
    role_dashboard         = db.Column(db.Integer)
    role_pm                = db.Column(db.Integer)
    role_ntx               = db.Column(db.Integer)
    role_sli               = db.Column(db.Integer)
    role_mkt               = db.Column(db.Integer)
    role_administration    = db.Column(db.Integer)
    role_lcm               = db.Column(db.Integer)
    create_date            = db.Column(DateTime(timezone=True), server_default=func.now())
    modify_date            = db.Column(DateTime(timezone=True), onupdate=func.now())
    description            = db.Column(db.String)

    @hybrid_method
    def delete_a_role(self, role_id):
        try:
            role = ModelRole.query.filter_by(id=role_id).scalar()
            ModelUserGroupRoleMapping.query.filter_by(role_id=role_id).delete()
            sub_role_names = ModelRole().sub_role_names
            for sub_role_name in sub_role_names:
                sub_role_id = getattr(role, sub_role_name)
                sub_role_model = model_mapping.get(sub_role_name)
                sub_role_model.query.filter_by(id=sub_role_id).delete()
            ModelRole.query.filter_by(id=role_id).delete()
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise flaskex.InternalServerError(f"Failed to delete role '{role_id}! Error: {e}")


    @hybrid_property
    def sub_role_names(self):
        return [_ for _ in ModelRole.__table__.columns.keys() if _.startswith("role_")]


class ModelRoleSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelRole
        load_instance = True


class ModelUserGroupRoleMapping(db.Model):
    __tablename__          = "user_group_role_mapping"
    id                     = db.Column(db.Integer, primary_key=True)
    user_id                = db.Column(db.Integer)
    user_name              = db.Column(db.String(50))
    group_id               = db.Column(db.Integer)
    group_name             = db.Column(db.String(50))
    role_id                = db.Column(db.Integer)
    role_name              = db.Column(db.String(255))
    from_group             = db.Column(db.Boolean)


    @hybrid_method
    def assign_role_for_user(self, user_id, role_ids, group_id=0):
        db.session.bulk_save_objects(
            [ModelUserGroupRoleMapping(user_id=user_id, group_id=group_id, role_id=role_id) for role_id in role_ids]
        )
        db.session.commit()

    @hybrid_method
    def unassign_role_from_user(self, user_id, role_ids):
        ModelUserGroupRoleMapping.query.filter(
            and_(ModelUserGroupRoleMapping.user_id == user_id, ModelUserGroupRoleMapping.role_id.in_(role_ids))
        ).delete()

    def delete_mappings_by_role_id(self, role_id, is_single_transaction=True):
        ModelUserGroupRoleMapping.query.filter_by(role_id=role_id).delete()
        if is_single_transaction:
            db.session.commit()

    def get_mapping_by_role_id(self, role_id):
        return ModelUserGroupRoleMapping.query.filter_by(role_id=role_id).one()


class ModelUserGroupRoleMappingSchema(ma.Schema):
    class Meta:
        fields = ('id', 'user_id', 'user_name', 'group_id', 'group_name', 'role_id', 'role_name', 'from_group')


class ModelSubRoleDashboard(db.Model):
    __tablename__          = "role_dashboard"
    id                     = db.Column(db.Integer, primary_key=True)
    view                   = db.Column(db.String(10), default="full")

    def __init__(self, **entries):
        # NOTE: Do not call superclass
        #       (which is otherwise a default behaviour).
        # super(User, self).__init__(**entries)

        self.__dict__.update(entries)


class ModelSubRoleDashboardSchema(ma.Schema):
    class Meta:
        fields = ('id', 'view')


class ModelSubRoleNutanix(db.Model):
    __tablename__          = "role_nutanix"
    id                     = db.Column(db.Integer, primary_key=True)
    view_pc                = db.Column(db.String(10), default="empty")
    view_pc_scope          = db.Column(db.String(8000))
    add_pc                 = db.Column(db.String(10), default="empty")
    add_pc_scope           = db.Column(db.String(8000))
    remove_pc              = db.Column(db.String(10), default="empty")
    remove_pc_scope        = db.Column(db.String(8000))
    view_pe                = db.Column(db.String(10), default="empty")
    view_pe_scope          = db.Column(db.String(8000))
    view_vm                = db.Column(db.String(10), default="empty")
    view_vm_scope          = db.Column(db.String(8000))
    view_ahv               = db.Column(db.String(10), default="empty")
    view_ahv_scope         = db.Column(db.String(8000))


class ModelSubRoleNutanixSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'view_pc', 'view_pc_scope', 'add_pc', 'add_pc_scope', 'remove_pc', 'remove_pc_scope', 'view_pe',
            'view_pe_scope', 'view_vm', 'view_vm_scope', 'view_ahv', 'view_ahv_scope')


class ModelSubRoleMarketplace(db.Model):
    __tablename__              = "role_marketplace"
    id                         = db.Column(db.Integer, primary_key=True)
    view_wl                    = db.Column(db.String(10), default="empty")
    view_wl_scope              = db.Column(db.String(8000))
    abort_wl                   = db.Column(db.String(10), default="empty")
    abort_wl_scope             = db.Column(db.String(8000))
    remove_wl                  = db.Column(db.String(10), default="empty")
    remove_wl_scope            = db.Column(db.String(8000))
    create_wl                  = db.Column(db.String(10), default="empty")
    create_wl_scope            = db.Column(db.String(8000))
    view_template              = db.Column(db.String(10), default="empty")
    view_template_scope        = db.Column(db.String(8000))
    create_template            = db.Column(db.String(10), default="empty")
    create_template_scope      = db.Column(db.String(8000))
    edit_template              = db.Column(db.String(10), default="empty")
    edit_template_scope        = db.Column(db.String(8000))
    remove_template            = db.Column(db.String(10), default="empty")
    remove_template_scope      = db.Column(db.String(8000))


class ModelSubRoleMarketplaceSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'view_wl', 'view_wl_scope', 'abort_wl', 'abort_wl_scope', 'remove_wl', 'remove_wl_scope',
            'create_wl', 'create_wl_scope', 'view_template', 'view_template_scope', 'create_template', 
            'create_template_scope', 'edit_template', 'edit_template_scope', 'remove_template', 'remove_template_scope'
        )


class ModelSubRolePM(db.Model):
    __tablename__          = "role_pm"
    id                     = db.Column(db.Integer, primary_key=True)
    view_ntx_pm            = db.Column(db.String(10), default="empty")
    view_ntx_pm_scope      = db.Column(db.String(8000))
    create_ntx_pm          = db.Column(db.String(10), default="empty")
    create_ntx_pm_scope    = db.Column(db.String(8000))
    abort_ntx_pm           = db.Column(db.String(10), default="empty")
    abort_ntx_pm_scope     = db.Column(db.String(8000))
    delete_ntx_pm          = db.Column(db.String(10), default="empty")
    delete_ntx_pm_scope    = db.Column(db.String(8000))
    view_sli_pm            = db.Column(db.String(10), default="empty")
    view_sli_pm_scope      = db.Column(db.String(8000))
    create_sli_pm          = db.Column(db.String(10), default="empty")
    create_sli_pm_scope    = db.Column(db.String(8000))
    abort_sli_pm           = db.Column(db.String(10), default="empty")
    abort_sli_pm_scope     = db.Column(db.String(8000))
    delete_sli_pm          = db.Column(db.String(10), default="empty")
    delete_sli_pm_scope    = db.Column(db.String(8000))


class ModelSubRolePMSchema(ma.Schema):
    class Meta:
        fields = (
            'id', 'view_ntx_pm', 'view_ntx_pm_scope', 'create_ntx_pm', 'create_ntx_pm_scope', 'abort_ntx_pm',
            'abort_ntx_pm_scope', 'delete_ntx_pm', 'delete_ntx_pm_scope', 'view_sli_pm', 'view_sli_pm_scope',
            'create_sli_pm', 'create_sli_pm_scope', 'abort_sli_pm', 'abort_sli_pm_scope', 'delete_sli_pm',
            'delete_sli_pm_scope'
        )


class ModelSubRoleSimplivity(db.Model):
    __tablename__          = "role_simplivity"
    id                     = db.Column(db.Integer, primary_key=True)
    view_vc                = db.Column(db.String(10), default="empty")
    view_vc_scope          = db.Column(db.String(8000))
    view_cluster           = db.Column(db.String(10), default="empty")
    view_cluster_scope     = db.Column(db.String(8000))
    view_vm                = db.Column(db.String(10), default="empty")
    view_vm_scope          = db.Column(db.String(8000))
    view_host              = db.Column(db.String(10), default="empty")
    view_host_scope        = db.Column(db.String(8000))


class ModelSubRoleSimplivitySchema(ma.Schema):
    class Meta:
        fields = ('id', 'view_vc', 'view_vc_scope', 'view_cluster', 'view_cluster_scope', 'view_vm', 'view_vm_scope',
                  'view_host', 'view_host_scope')


class ModelSubRoleAdministration(db.Model):
    __tablename__          = "role_administration"
    id                     = db.Column(db.Integer, primary_key=True)
    view_user              = db.Column(db.String(10), default="empty")
    view_user_scope        = db.Column(db.String(8000))
    view_role              = db.Column(db.String(10), default="empty")
    view_role_scope        = db.Column(db.String(8000))
    view_comment           = db.Column(db.String(10), default="empty")
    view_comment_scope     = db.Column(db.String(8000))
    edit_comment           = db.Column(db.String(10), default="empty")
    edit_comment_scope     = db.Column(db.String(8000))
    view_vault             = db.Column(db.String(10), default="empty")
    view_vault_scope       = db.Column(db.String(8000))



class ModelSubRoleAdministrationSchema(ma.SQLAlchemyAutoSchema):
    class Meta:
        model = ModelSubRoleAdministration
        load_instance = True


class ModelSubRoleLCM(db.Model):
    __tablename__           = "role_lcm"
    id                      = db.Column(db.Integer, primary_key=True)
    view_spp                = db.Column(db.String(10), default="empty")
    view_spp_scope          = db.Column(db.String(8000))
    view_aos                = db.Column(db.String(10), default="empty")
    view_aos_scope          = db.Column(db.String(8000))  
    view_move               = db.Column(db.String(10), default="empty")
    view_move_scope         = db.Column(db.String(8000))
    view_atm                = db.Column(db.String(10), default="empty")
    view_atm_scope          = db.Column(db.String(8000))
    view_slcm               = db.Column(db.String(10), default="empty")
    view_slcm_scope         = db.Column(db.String(8000))
    manage_slcm             = db.Column(db.String(10), default="empty")
    manage_slcm_scope       = db.Column(db.String(8000))


class ModelSubRoleLCMSchema(ma.Schema):
    class Meta:
        fields = ('id', 'view_spp', 'view_spp_scope', 'view_aos', 'view_aos_scope', 'view_move', 'view_move_scope', 'view_atm', 'view_atm_scope')


class ModelServiceAccount(db.Model):
    __tablename__ = 'dh_service_account'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(255), nullable=False)
    iv = db.Column(db.String(255))
    key = db.Column(db.String(255))
    crypted = db.Column(db.String(255))
    usage = db.Column(db.String(255))


class ModelServiceAccountSchema(ma.Schema):
    class Meta:
        fields = ('id', 'username', 'iv', 'key', 'crypted', 'usage')


# TODO: temporary solution, need improve
model_mapping = {
    "role_dashboard"      : ModelSubRoleDashboard,
    "role_pm"             : ModelSubRolePM,
    "role_ntx"            : ModelSubRoleNutanix,
    "role_sli"            : ModelSubRoleSimplivity,
    "role_mkt"            : ModelSubRoleMarketplace,
    "role_administration" : ModelSubRoleAdministration,
    "role_lcm"            : ModelSubRoleLCM
}
model_schma_mapping = {
    "role_dashboard"      : ModelSubRoleDashboardSchema,
    "role_pm"             : ModelSubRolePMSchema,
    "role_ntx"            : ModelSubRoleNutanixSchema,
    "role_sli"            : ModelSubRoleSimplivitySchema,
    "role_mkt"            : ModelSubRoleMarketplaceSchema,
    "role_administration" : ModelSubRoleAdministrationSchema,
    "role_lcm"            : ModelSubRoleLCMSchema
}
import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  // baseURL: "http://127.0.0.1:5000/api/v1", // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 60000 // request timeout
})
// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    if (store.getters.token) {
      config.headers['Authorization'] = "Bearer " + getToken()
    }
    return config
  },
  error => {
    // do something with request error
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    // const res = response.data
    // if the custom code is not 20000, it is judged as an error.
    return response
  },
  error => {
    // console.log('err' + error) // for debug
    if(error.response == undefined){
      MessageBox.alert(error.message)
    }
    let res = error.response
    if(res.status == 401){
      MessageBox.confirm('You have been logged out since you token was expired.', 'Confirm logout', {
        confirmButtonText: 'Re-Login',
        cancelButtonText: 'I dont care.',
        type: 'warning'
      }).then(() => {
        console.log('relogining')
        store.dispatch('user/resetToken')
        location.reload()
      }).catch(
        ()=>{
          console.log("dont do anything.")
        }
      )
    }


    // Message({
    //   message: error.message,
    //   type: 'error',
    //   duration: 5 * 1000
    // })
    return Promise.reject(error)
  }
)

export default service

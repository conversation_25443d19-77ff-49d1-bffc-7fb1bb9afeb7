<template>
    <div class="app-container">
      <div class="filter-container">
        <el-row :gutter="5" >
          <el-col :span="3" >
            <el-button class="filter-item"  type="primary" @click="handle_create" v-show="priv.role_mkt.create_template != 'empty'">
              Create
            </el-button>
          </el-col>
          <el-col :span="4" :offset="(priv.role_mkt.create_template != 'empty')?17:20" >
            <el-button style='float:right' class="filter-item" @click="handle_delete" type="danger" v-show="priv.role_mkt.remove_template != 'empty'">
              Delete
            </el-button>
          </el-col>
        </el-row>
      </div>
  
      <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="templatelist"
        border
        fit
        highlight-current-row
        style="width: 100%;"
        @sort-change="sortChange"
        @row-click="handle_row_click"
        class='template-table'
        ref='pctable'
      >
  
        <el-table-column label="ID" min-width="3%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Name" class-name="status-col" min-width="10%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Workload Type" min-width="5%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.workload_type }}</span>
          </template>
        </el-table-column>
        <el-table-column label="CPU" align="center" min-width="4%">
          <template slot-scope="{row}">
            <span>{{ row.cpu }}</span>
          </template>
        </el-table-column>
        <el-table-column label="CPU core" min-width="4%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.cpu_core }}</span>
          </template>
        </el-table-column>
        <el-table-column label="RAM" min-width="4%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.memory }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Disk" class-name="status-col" min-width="7%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.disk }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Image" class-name="status-col" min-width="7%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.image_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Boot Mode" min-width="6%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.boot_mode }}</span>
          </template>
        </el-table-column>
        <el-table-column  label="VlanID" min-width="5%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.vlan_id}}</span>
          </template>
        </el-table-column>
        <el-table-column  label="Naming convention" min-width="15%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.naming_convention}}</span>
          </template>
        </el-table-column>
      </el-table>
  
      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="get_template_list" />
  
      <el-dialog  id="modal" :visible.sync="dialogFormVisible" style="width:100%; margin-top:-6%;" :close-on-click-modal="false">

        <div style="width:100%;text-align: center;font-size:35px;">Create Template</div>
        <div style="border-top: 0.1px solid rgb(185, 182, 182);width:100%;height:0;margin-top:1%"></div>
        <el-form ref="template_form" :rules="rules" :model="new_template" label-position="right" label-width="150px" style="width: 100%;height:500px;margin-top:5%">
          <!-- tempalte name -->
          <el-form-item label="Name" prop="template_name">
            <el-input  v-model=new_template.template_name class="wl_template_label_body"  style="width:90%">
            </el-input>
          </el-form-item>

          <!--  -->
          <el-form-item label="OS" prop="template_os" >
            <el-radio v-model="new_template.workload_type" label="windows" class="wl_template_label_body">Windows</el-radio>
            <el-radio v-model="new_template.workload_type" label="linux" >Linux</el-radio>
            <el-radio v-model="new_template.workload_type" label="network" >Network appliance</el-radio>
          </el-form-item>

          <el-form-item label="Naming rule" prop="naming_convention" >
            <el-input  v-model=new_template.naming_convention class="wl_template_label_body" style="width:86%;float:left;">
            </el-input>
            <template >
              <el-popover placement="right" trigger="hover" title="FYI">
                <div>For RETSE012-NXC000<br>{bucode} => RET<br>{countrycode} => SE<br>{sitecode} => 012<br>{5_digit_sitecode} => 00012
                </div>
                <span slot="reference" style="width:4%;float:left;margin-left:2%"><svg-icon icon-class="information" class-name="card-panel-icon" /></span>
              </el-popover>
            </template>
          </el-form-item>

          <el-form-item  label="Image" prop="image_id" > 
            <el-select v-model="new_template.image_id" placeholder="Image" class="wl_template_label_body" :style="{'width':new_template.workload_type=='linux'?'65%':'90%'}" >
              <el-option v-for="item in imageOptions[new_template.workload_type]" :key="item.key" :label="'ID:'+item.key + ' Name:'+item.display_name" :value="item.key" />
            </el-select>

            <!-- we dont need 519 anymore -->
            <!-- <el-popover
              placement="top-start"
              title="Payload of ansible template 59"
              :hidden="new_template.workload_type!='linux'" 
              width="500"
              trigger="click">
              <el-button slot="reference" icon="el-icon-edit" style="margin-left:3%;width:25%"  @click="load_59_template">
                payload 59
              </el-button>
              <el-input
                    v-model="new_template.payload_59"
                    size="large"
                    :rows="21"
                    type="textarea"
                    placeholder="Please input"
                  />
            </el-popover> -->

            <el-popover
              key='hover'
              placement="top-start"
              title="Payload of ansible template 319"
              :hidden="new_template.workload_type!='linux'" 
              @hide="minify_319"
              width="500"
              trigger="click">
              <el-button slot="reference" class="" icon="el-icon-edit" style="margin-left:3%;width:25%" @click="load_319_template">
                payload 9423
              </el-button>
              <!-- this is actually 9423, but in the code it refers as 319, so 319 equels 9423 here. -->
              <el-input
                    v-model="new_template.payload_319"
                    size="large"
                    :rows="21"
                    type="textarea"
                    placeholder="Please input"
                  />
            </el-popover>

          </el-form-item>
          <div>
            <el-form-item label="CPU" prop="cpu" style="float:left;width:50%">
              <el-input  v-model=new_template.cpu class="wl_template_label_body" style="width:70%">
              </el-input>
            </el-form-item>
          
            <el-form-item label="CPU Core" prop="cpu_core"  style="float:left;width:50%">
              <el-input  v-model=new_template.cpu_core class="wl_template_label_body" style="width:70%">
              </el-input>
            </el-form-item>
          </div>

          <div>
            <el-form-item label="RAM" prop="ram" style="float:left;width:50%">
              <el-input  v-model=new_template.ram class="wl_template_label_body" style="width:70%">
              </el-input>
            </el-form-item>
          
            <el-form-item label="Disk" prop="disk"  style="float:left;width:50%">
              <el-input  v-model=new_template.disk class="wl_template_label_body" style="width:65%;float:left;">
              </el-input>
              <template >
              <el-popover placement="right" trigger="hover" title="FYI" >
                <div >Don't put the boot disk here<br>
                  If you want 35(C:\) 100(D:\) 200(E:\), just put 35,100,200<br>
                  Same for Linux and network appliance
                </div>
                <span slot="reference" style="width:6%;float:left;margin-left:4%"><svg-icon icon-class="information" class-name="card-panel-icon" /></span>
              </el-popover>
            </template>
            </el-form-item>
            <div style="clear:both"></div>
          </div>

          <div> 
            <el-form-item  label="Vlan" prop="vlan_id" style="float:left;width:100%;"> 
              <el-input v-model="new_template.vlan_id" class="wl_template_label_body" placeholder="Vlan"  style="float:left;width:28%"/>

              <el-checkbox v-model="new_template.register_dns" style="float:left;margin-left:15%"/>
              <div style="float:left;width:40%;;margin-left:2%"><span>Register DNS in IPAM</span></div>
            </el-form-item>
            <div style="clear:both"></div>
          </div>

          <el-form-item  label="Packages" prop="app_package" style="float:left;width:100%;">
              
            <el-select 
              class="wl_template_label_body"
              v-model="new_template.selected_app_package" 
              filterable 
              placeholder="Please select" 
              style="width: 90%" 
              multiple 
              >
              <el-option v-for="item in app_package_list" :key="item" :label="item" :value="item" style="font-size: large;"/>
            </el-select>

          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            Cancel
          </el-button>
          <el-button type="success"  @click="create_template" >
            Confirm
          </el-button>
        </div>
      </el-dialog>

    </div>
  </template>
  
  <script>
  import {GetTemplateList, GetWorkloadInfo, GetAppPackageList , CreateWorkloadTemplate, DeleteWorkloadTemplate } from '@/api/nutanix'
  import waves from '@/directive/waves' // waves directive
  import { parseTime } from '@/utils'
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination
  

  const peoptions =[
    { pc: 'SSP-EU-NTX.IKEA.COM', pe:['RETDE068-NXC000.ikea.com','RETSEHBG-NXC000.ikea.com','RETDE124-NXC000.ikea.com','RETFR134-NXC000.ikea.com'] },
    { pc: 'SSP-CHINA-NTX.IKEA.COM', pe:['RETCN856-NXC000.ikea.com','RETCNCHN-NXC000.ikea.com','RETCN644-NXC000.ikea.com','RETCNSOS-NXC000.ikea.com'] },
    { pc: 'SSP-APAC-NTX.IKEA.COM', pe:['RETKR373-NXC000.ikea.com','RETJP509-NXC000.ikea.com','RETKR522-NXC000.ikea.com','RETKRSO-NXC000.ikea.com'] },
    { pc: 'SSP-NA-NTX.IKEA.COM', pe:['RETUS100-NXC000.ikea.com','RETCA040-NXC000.ikea.com','RETUS209-NXC000.ikea.com','RETUS374-NXC000.ikea.com'] },
    { pc: 'SSP-RUSSIA-NTX.IKEA.COM', pe:['RETRU401','RETRU403','RETRU551','RETRU513'] },
    { pc: 'SSP-DT-NTX.IKEADT.COM', pe:['RETSEELM-NXC000.ikea.com'] },
    { pc: 'SSP-PPE-NTX.IKEA.COM', pe:['RETSE999-NXC000.ikea.com','RETCN888-NXC000.ikea.com'] },
  ]
  // arr to obj, such as { CN : "China", US : "USA" }

  
  const peTypeKeyValue = peoptions.reduce((acc, cur) => {
    acc[cur.pc] = cur.pe
    return acc
  }, {})
  export default {
    name: 'PrismTable',
    components: { Pagination },
    directives: { waves },
    filters: {
      LogFilter(log) {
        console
      },
    },
    data() {
      const validateTime =(rule, value, callback)=>{
        if(this.temp.datatimepickerdisabled){
          callback()
        }
        let currentdate = new Date()
        let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
        if (value < utctime){
          callback(new Error('Schedule date must be later then now.'))
        }else{
          let currnettime = utctime.getTime()
          let scheduletime = value.getTime()
          let timediff = scheduletime-currnettime
          if(timediff/1000/60 < 5){
            callback(new Error('Schedule date is too close from now.'))
          }else{
            callback()
          }
        }
        callback()
      }
      return {
        prismtier:[
          'Production',
          'PreProduction',
          'IKEADT',
          'IKEAD2'
        ],
        priv : this.$store.getters.all_privilege,
        new_template:{
          register_dns:true,
          cpu: "",
          cpu_core: "",
          vlan_id: "",
          template_name: "",
          workload_type: "windows",
          image_id: "",
          naming_convention: "",
          ram: "",
          disk: "",
          payload_319:'{"extra_var": {"ikea_hosts": [{"name": "@{vm_name}@"}]}}',
          payload_59:'{"extra_var": {"ikea_hosts": [{"name": "@{vm_name}@"}]}}',
          selected_app_package:[]
        },
        imageOptions:{},
        app_package_list:[],
        tableKey: 0,
        list: null,
        templatelist : null,
        prismlist: null,
        total: 0,
        listLoading: true,
        listQuery: {
          page: 1,
          limit: 20,
          cluster: '',
          prism: '',
          status: '',
          sort: '+id'
        },
        peTypeKeyValue,
        sortOptions: [{ label: 'ID Ascending', key: '+id' }, { label: 'ID Descending', key: '-id' }],
        // statusOptions: ['published', 'draft', 'deleted'],
        ShowCreationDate: false,
        temp: {
          id: '',
          fqdn: '',
          tier:'',
          timestamp: new Date(),
          cluster:'',
          prism: '',
          status: '',
          startnow: 1 ,
          datatimepickerdisabled:false,
          description: '',
          pmtype: 1,
          newsa:1,//flag for if use new service account or not, 1 means use new one, 0 means select from current list.
        },
        selectedrow:'',
        dialogFormVisible: false,
        dialogPvVisible: false,
        logdata: [],
        rules: {
          template_name: { required: true, message: 'Template name is required', trigger: 'change' },
          naming_convention: { required: true, message: 'Naming convention is required', trigger: 'change' },
          image_id: { required: true, message: 'Image is required', trigger: 'change' },
          cpu: { required: true, message: 'CPU is required', trigger: 'change' },
          cpu_core: { required: true, message: 'CPU Core is required', trigger: 'change' },
          ram: { required: true, message: 'Memory is required', trigger: 'change' },
          disk: { required: true, message: 'Disk is required', trigger: 'change' },
          vlan_id: { required: true, message: 'Vlan id is required', trigger: 'change' },
          // cluster: { required: true, message: 'cluster is required', trigger: 'change' },
          // timestamp: { type: 'date', required: true , trigger: 'change' , validator:validateTime}
        },
        downloadLoading: false,
        intervaljob:''
      }
    },
    created() {
      // this.get_template_list()
      this.get_workload_info()
      this.show_role()
    },
    methods: {      
      show_role(){
        console.log(this.priv)
        console.log(this.$store.getters)
      },
      get_template_list(){
        this.listLoading = true
        GetTemplateList(this.$store.getters.token).then(response => {
          this.templatelist = response.data
          this.total = response.data.length
          this.listLoading = false
        })
      },
      handleFilter() {
        this.listQuery.page = 1
      },
      sortChange(data) {
        const { prop, order } = data
        if (prop === 'id') {
          this.sortByID(order)
        }
      },
      sortByID(order) {
        if (order === 'ascending') {
          this.listQuery.sort = '+id'
        } else {
          this.listQuery.sort = '-id'
        }
        this.handleFilter()
      },
      resetTemp() {
        let localtime = new Date()
        let utctime =new Date( localtime.getTime() + 60*1000*localtime.getTimezoneOffset())
        this.temp = {
          id: undefined,
          timestamp: utctime,
          status: 'published',
          type: '',
          prism: '',
          startnow: "1" ,
          pmtype: "1" ,
          newsa: "1",
          datatimepickerdisabled: false
        }
      },
      handle_create() {
        this.resetTemp()
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['template_form'].clearValidate()
        })
      },
      handle_delete(){
        if(!this.selectedrow){ // if no row is selected
          this.$message({
              type: 'error',
              message: 'Select a template first.',
              duration:3000
            });
          return 
        }
        
        this.$confirm('Deleting template "'+ this.selectedrow.name +'", are you sure?',
             'Deleting template '+this.selectedrow.name, {
            confirmButtonText: 'YES',
            cancelButtonText: 'NO',
            type: 'danger'
          }).then(() => {
            let payload = {
              data:{
                template_ids:[this.selectedrow.id]
              },
              token: this.$store.getters.token
            }
            console.log(payload)
            DeleteWorkloadTemplate(payload).then((response)=>{
              console.log(response)
              this.$notify({
                    title: 'Success',
                    message: 'Template was deleted.',
                    type: 'success',
                    duration: 2000
                  })
              this.get_workload_info()
            })
            .catch((error)=>{
              this.$notify({
                    title: 'Failed',
                    message: error.response.data.message,
                    type: 'error',
                    duration: 2000
                  })
            })
          }).catch(()=>{

          });
      },
      handle_row_click(row,column,event){
        this.selectedrow = row
      },
      downloadLogFile(){
        if (!this.selectedrow.detaillogpath){
          this.$notify({
                title: 'Ooooops',
                message: 'No log yet!',
                type: 'info',
                duration: 2000
              })
        }
        let payload = {
          data:{  id:this.selectedrow.id,
                  filepath:this.selectedrow.detaillogpath},
          token: this.$store.getters.token
        }
        DownloadNTXPMLog(payload)
        .then((response)=>{
          const href = URL.createObjectURL(response.data);
          // create "a" HTML element with href to file & click
          const link = document.createElement('a');
          link.href = href;
          link.setAttribute('download', (payload.data.filepath.split("\\").at(-1)+'.log')); //or any other extension
          document.body.appendChild(link);
          link.click();
          // clean up "a" element & remove ObjectURL
          document.body.removeChild(link);
          URL.revokeObjectURL(href);
        })
      },
      handleFetchBriefLog(row) {
        this.selectedrow = row
        this.logdata = row.logs
        this.dialogPvVisible = true
      },
      formatJson(filterVal) {
        return this.list.map(v => filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        }))
      },
      getSortClass: function(key) {
        const sort = this.listQuery.sort
        return sort === `+${key}` ? 'ascending' : 'descending'
      },
      get_workload_info(){//get the information regarding this page, not single workload
        GetWorkloadInfo(this.$store.getters.token).then(response => {
          let data = response.data
          let image = data.image
          this.templatelist = data.template
          this.total = this.templatelist.length
          this.listLoading = false
          let windows = []; let linux= []; let network = []
          for(let im of image){
            if (im.os_type == 'windows'){
              windows.push({'key':im.id,'display_name':im.alias})
              continue
            }
            if (im.os_type == 'linux'){
              linux.push({'key':im.id,'display_name':im.alias})
            }
            if (im.os_type == 'network'){
              network.push({'key':im.id,'display_name':im.alias})
            }
          }
          this.imageOptions['windows'] = windows
          this.imageOptions['linux'] = linux
          this.imageOptions['network'] = network
        })
        GetAppPackageList(this.$store.getters.token).then(response => {
          this.app_package_list = response.data
        })
      },
      create_template(){

        this.$refs['template_form'].validate((valid)=>{
          //as the rules, validate the form, to see if there is anything not valid
          if(valid){
            let template = {
              name: this.new_template.template_name,
              naming_convention: this.new_template.naming_convention,
              workload_type: this.new_template.workload_type,
              cpu: parseInt(this.new_template.cpu),
              cpu_core: parseInt(this.new_template.cpu_core),
              memory: parseInt(this.new_template.ram),
              disk: (this.new_template.disk.split(",")).map((e)=>{return parseInt(e)}),
              image_id: parseInt(this.new_template.image_id),
              vlan_id: parseInt(this.new_template.vlan_id),
              app_package: this.new_template.selected_app_package,
              linux_payload: this.new_template.payload_319,
              update_dns: this.new_template.register_dns
            }

            let payload = {
              data : [template],
              token: this.$store.getters.token
            }
            
            CreateWorkloadTemplate(payload).then(
              response => {
                let result = response.data
                for (let _res of result){
                  if(_res.success){
                    this.$notify({
                      title: "Success",
                      message: _res['specs']['name']+ " has been created successfully.",
                      type: 'success',
                      duration: 5000
                    })
                  }else{
                    this.$notify({
                      title: "Error",
                      message: _res.message,
                      type: 'error',
                      duration: 5000
                    })
                  }
                }
              }
            )
            .catch((error)=>{
              this.$notify({
                  title: 'Error',
                  message: 'Failed to create a template'+ error,
                  type: 'error',
                  duration: 5000
                })
            })
            //modify string to int
          }
        })
      },
      load_319_template(){  
        let ugly_json = JSON.parse(this.new_template.payload_319)   
        let pretty = JSON.stringify(ugly_json, undefined, 4);
        this.new_template.payload_319 = pretty
      },
      minify_319(){
        try{
          this.new_template.payload_319=JSON.stringify(JSON.parse(this.new_template.payload_319))
        }
        catch{
          this.$message({
              type: 'error',
              message: "Hey, this 319 payload is not a json format."
            });  
        }
      },
      load_59_template(){  
        let ugly_json = JSON.parse(this.new_template.payload_59)   
        let pretty = JSON.stringify(ugly_json, undefined, 4);
        this.new_template.payload_59 = pretty
      },
      minify_59(){
        try{
          this.new_template.payload_59=JSON.stringify(JSON.parse(this.new_template.payload_59))
        }
        catch{
          this.$message({
              type: 'error',
              message: "Hey, this 59 payload is not a json format."
            });  
        }
      },
    },
    beforeDestroy(){
      clearInterval( this.intervaljob )
    }
  }
  </script>
  <style scoped>
   .template-table span{
       font-size: 17px
  }

  .form-label{
    color:gray;
    width:10%;
    float:left;
    margin-left:10%
  }

  .form-label_half_left{
    color:gray;
    width:20%;
    float:left;
    margin-left:20%;
  }

  .form-label_half_right{
    color:gray;
    width:20%;
    float:left;
    margin-left:5%
  }

  .form_item{
    margin-left:10%;
  }
  .wl_template_label_body{
    margin-left:30px
  }
  body, html{
   height:100%
  }
  </style>
<style lang="scss" >

    #modal{

      .el-dialog__header{
        padding:0px;
      }
      .el-dialog__body {
        padding: 10px 10px;
        width: 100%
      }
    }
    .el-form-item__error{
      margin-left:31px
    }
    .form-label_half_left ~ .el-form-item__error{
      margin-left:20%
    }
    .form-label_half_right ~ .el-form-item__error{
      margin-left:5%
    }
    .is-always-shadow {
      box-shadow: 1px 1px 3px 0.5px rgba(0, 0, 0, 0.1);
    } 
    .dh-card{
      border-radius: 4px;
      border: 1px solid #e6ebf5;
      background-color: #ffffff;
      overflow: hidden;
      color: #303133;
      transition: 0.3s;
      margin-left:3%;
      margin-bottom: 10px;
      width:96%;
    }

    .dh-card-header {
      border-radius: 4px;
      border: 1px solid #e6ebf5;
      background-color: #ffffff;
      overflow: hidden;
      color: #303133;
      transition: 0.3s;
      height:40px;
      width:100%;
      cursor:pointer;
    }
    .vm-name{
      width:92%;
      height:40px;
      float:left;
      font-size: large;
      text-align: center;
      padding:5px
    }
    .btn-groups{
      width:8%;
      height:40px;
      float:right;
    }
    .dh-btn{
      float:right;
      height:40px;
      border-radius: 0px;
    }
    .collapse-whole{
      float:left;
      margin-left:2%;
      width:100%
    }
    .collapse-left{
      float:left;
      margin-left:2%;
      width:40%
    }
    .collapse-right{
      float:right;
      margin-right:6%;
      width:50%
  }
   </style>
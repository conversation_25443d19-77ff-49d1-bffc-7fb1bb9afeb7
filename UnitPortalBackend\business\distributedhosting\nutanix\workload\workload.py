# installed module
import copy
import json
import os
import time
import re
import logging
import requests
import sqlalchemy
from flask import jsonify
from bs4 import BeautifulSoup
import werkzeug.exceptions as flaskex
from sqlalchemy import func, and_
from jinja2 import Template
# local file
from business.distributedhosting.nutanix.workload.sizing import Sizing
from models.database import db
import static.SETTINGS as SETTING
from base_path import application_path
from business.authentication.authentication import ServiceAccount
from business.authentication.tokenvalidation import PrivilegeValidation
from business.distributedhosting.nutanix.base_up_task import BaseUpTask
from business.distributedhosting.nutanix.base_up_task_property import BaseUpTaskProperty
from business.distributedhosting.nutanix.pe_components import StorageContainer
from business.distributedhosting.nutanix.task_status import TaskStatus
from business.distributedhosting.nutanix.workload.workload_exceptions import ImageUploadFailed, ImageUploadOverTime, WorkloadExisting, TowerJobFailed, \
    WorkloadException
from business.distributedhosting.nutanix.workload.app_package import AppPackage
from business.distributedhosting.nutanix.protection_domain import ProtectionDomain
from business.distributedhosting.nutanix.workload_network import WorkloadNetwork
from business.generic.ipam import Ipam
from business.generic.thors_hammer_api import ThorsHammerAPI
from business.generic.commonfunc import split_pe_into_parts, \
    convert_GiB_to_bytes, get_user_by_token, CommonHelper, ping3_with_retry
from business.generic.linux import Tower
from business.distributedhosting.nutanix.nutanix import PrismCentral, PrismElement, Prism_Element
from business.distributedhosting.nutanix.automation.automation import Automation
from models.ntx_models import ModelPrismCentral
from models.ntx_models_wh import ModelWarehousePrismCentral
from models.workload_models import ModelWorkloadTaskSchema, ModelWorkloadTask, ModelWorkloadTaskLogSchema, \
    ModelWorkloadTaskLog, ModelWorkloadTemplateSchema, ModelWorkloadTemplate, ModelWorkloadImageSchema, \
    ModelWorkloadImage
from static.WORKLOAD_SPEC import WorkloadSpec, TemplateSpec


class CreateWorkloadTask(BaseUpTask):
    LOG_DIR = SETTING.WORKLOAD_LOG_PATH
    LOG_TYPE = "NTX_WL"
    TASK_TYPE = "CREATE_WORKLOAD"

    def __init__(self, payload) -> None:
        super().__init__(ModelWorkloadTask, ModelWorkloadTaskSchema, ModelWorkloadTaskLog, ModelWorkloadTaskLogSchema, payload)
        self.vm_spec = {}
        try:
            pc_info = ModelPrismCentral.query.filter_by(fqdn=payload[WorkloadSpec.PC]).one()
            self.facility_type = "retail"
        except sqlalchemy.exc.NoResultFound:
            try:
                pc_info = ModelWarehousePrismCentral.query.filter_by(fqdn=payload[WorkloadSpec.PC]).one()
                self.facility_type = "warehouse"
            except sqlalchemy.exc.NoResultFound:
                raise Exception(f"Can't find PC '{payload[WorkloadSpec.PC]}' neither in Retail nor in Warehouse!")
        self.ntx_sa = ServiceAccount(usage=pc_info.service_account).get_service_account()
        self.domain = pc_info.domain

    @classmethod
    def get_workload_task(cls, getlog=False, page=None, limit=None):
        try:
            if getlog:  # get the task log create a log field in all tasks
                _res = []
                _taskschema = ModelWorkloadTaskSchema()
                max = db.session.query(
                    func.max(ModelWorkloadTaskLog.id).label("max_id"),
                    ModelWorkloadTaskLog.task_id.label("task_id")
                ).group_by(
                    ModelWorkloadTaskLog.task_id
                ).subquery()
                max_log = db.session.query(
                    ModelWorkloadTask.id.label("task_id"),
                    ModelWorkloadTaskLog.id.label("max_log_id"),
                    ModelWorkloadTaskLog.severity.label("log_severity"),
                    ModelWorkloadTaskLog.loginfo.label("log_info")
                ).join(
                    max,
                    and_(ModelWorkloadTaskLog.id == max.c.max_id)
                ).join(
                    ModelWorkloadTask,
                    and_(ModelWorkloadTask.id == ModelWorkloadTaskLog.task_id)
                ).filter(
                    ModelWorkloadTask.task_type == cls.TASK_TYPE
                ).subquery()
                query = db.session.query(
                    ModelWorkloadTask, max_log.c.log_severity, max_log.c.log_info
                ).filter(
                    ModelWorkloadTask.task_type == cls.TASK_TYPE
                ).outerjoin(
                    max_log,
                    and_(ModelWorkloadTask.id == max_log.c.task_id)
                )
                total = query.count()
                if page is not None and limit is not None:
                    query = query.order_by(ModelWorkloadTask.id.desc()).offset((page - 1) * limit).limit(limit)
                else:
                    query = query.order_by(ModelWorkloadTask.id.desc())
                query = query.all()
                for q in query:
                    _task = _taskschema.dump(q[0])
                    _task['latestlog'] = {
                        'severity': q[1] if q[1] else "info",
                        'loginfo': q[2] if q[2] else "No log yet."
                    }
                    _res.append(_task)
                result = _res
            else:
                base_query = ModelWorkloadTask().query.filter_by(task_type=cls.TASK_TYPE)
                total = base_query.count()
                if page is not None and limit is not None:
                    base_query = base_query.order_by(ModelWorkloadTask.id.desc()).offset((page - 1) * limit).limit(limit)
                else:
                    base_query = base_query.order_by(ModelWorkloadTask.id.desc())
                result = ModelWorkloadTaskSchema(many=True).dump(base_query.all())
            if page is not None and limit is not None:
                return jsonify({"data": result, "total": total})
            return jsonify(result)
        except Exception as e:
            logging.error(str(e))
            return False

    def run(self):
        def _init_payload_for_template():
            def _init_linux_payload():
                vm_fqdn = f"{self.task_info[WorkloadSpec.VM_NAME]}.{self.domain}"
                ansible_env = "PROD"
                match self.domain.lower():
                    case "ikead2.com":
                        ansible_env = "TEST"
                    case "ikeadt.com":
                        ansible_env = "DEV"
                self.task_info[WorkloadSpec.LINUX_PAYLOAD] = self.task_info[WorkloadSpec.LINUX_PAYLOAD] \
                    .replace('@{vm_fqdn}@', vm_fqdn) \
                    .replace('@{ansible_env}@', ansible_env)
            template_id = self.payload[WorkloadSpec.TEMPLATE_ID]
            logging.info(f"{user.username} is creating workloads with a template, template id: {template_id}.")
            template = WorkloadTemplate(template_id=template_id).get_template_by_id()
            for key, value in template.items():
                if key != TemplateSpec.ID and key != TemplateSpec.TEMPLATE_NAME:
                    self.task_info[key] = value
            if not self.task_info.get(WorkloadSpec.VM_NAME):  # For API triggered task.
                # e.g. {bucode}{countrycode}{sitecode}-NT1000
                # e.g. {bucode}{countrycode}{5_digit_sitecode}-NT1000
                naming_convention = self.task_info[TemplateSpec.NAMING_CONVENTION]
                digit_length = 5 if "{5_digit_sitecode}" in naming_convention else 0
                bu, country_code, bu_code = split_pe_into_parts(self.payload[WorkloadSpec.PE], digit_length)
                vm_name = naming_convention \
                    .replace('{bucode}', bu) \
                    .replace('{countrycode}', country_code) \
                    .replace('{sitecode}', bu_code) \
                    .replace('{5_digit_sitecode}', bu_code)
                self.task_info[WorkloadSpec.VM_NAME] = vm_name
            _init_linux_payload()
            # disk and app_package are saved as string in db, need to convert to list
            if isinstance(self.task_info[WorkloadSpec.DISK], str):
                self.task_info[WorkloadSpec.DISK] = self.task_info[WorkloadSpec.DISK].split(',')
            if isinstance(self.task_info[WorkloadSpec.PACKAGES], str):
                self.task_info[WorkloadSpec.PACKAGES] = self.task_info[WorkloadSpec.PACKAGES].split(',')

        def _init_payload_for_custom():
            logging.info(f"{user.username} is creating workloads with customized specs, specs: {self.payload}.")
            workload_type = self.task_info[WorkloadSpec.WORKLOAD_TYPE]
            if workload_type == "windows":
                self.task_info[WorkloadSpec.BOOT_MODE] = "SECURE_BOOT"
            elif workload_type == "linux" or workload_type == "network":
                self.task_info[WorkloadSpec.BOOT_MODE] = "LEGACY"

        user = get_user_by_token()
        for key, value in self.payload.items():
            self.task_info[key] = value
        self.task_info[WorkloadSpec.USE_TEMPLATE] = WorkloadSpec.TEMPLATE_ID in self.payload.keys()
        if self.task_info[WorkloadSpec.USE_TEMPLATE]:
            _init_payload_for_template()
        else:
            _init_payload_for_custom()
        self.vm_spec = copy.deepcopy(self.task_info)
        # disk and app_package need to convert to string, in order to save in db
        if isinstance(self.task_info[WorkloadSpec.DISK], list):
            self.task_info[WorkloadSpec.DISK] = ','.join(str(x) for x in self.task_info[WorkloadSpec.DISK])
        if isinstance(self.task_info[WorkloadSpec.PACKAGES], list):
            self.task_info[WorkloadSpec.PACKAGES] = ','.join(str(x) for x in self.task_info[WorkloadSpec.PACKAGES])
        self.task_duplicated_kwargs = {
            "task_type": self.TASK_TYPE, "name": self.vm_spec[WorkloadSpec.VM_NAME]
        }
        self.task_info[BaseUpTaskProperty.TASK_TYPE] = self.TASK_TYPE
        super().run()
        return True, ''

    def task_process(self):
        _pc = PrismCentral(pc=self.vm_spec[WorkloadSpec.PC], sa=self.ntx_sa, logger=self.logger)
        _pe = PrismElement(pe=self.vm_spec[WorkloadSpec.PE], sa=self.ntx_sa, logger=self.logger)
        self.rest_ipam = Ipam(sa=self.ntx_sa, logger=self.logger)
        self.check_vm_existence(_pe)
        self.check_sizing(_pe)
        self.check_ntx_network(_pe)
        self.get_wl_image(_pc, _pe)
        if self.vm_spec.get(WorkloadSpec.UPDATE_DNS):
            self._update_dns()
        else:
            self.ilg.write("Server is configured not to update DNS, skipping...")
        time.sleep(10)
        vm_uuid = self._create_vm_on_ntx_process(_pe)
        self.vm_spec[WorkloadSpec.VM_UUID] = vm_uuid
        self._post_create()
        if self.facility_type == "retail":
            self._add_vm_to_ntx_pd_process(vm_uuid)
        elif self.facility_type == "warehouse":
            # TODO: warehouse PD is in PC, not PE
            pass
        time.sleep(20)
        self._update_acp_scope(self.vm_spec[WorkloadSpec.WORKLOAD_TYPE])
        self._install_packages()

    def validate_rhelauto_image(self):
        self.logger.info("Validating the latest version of RHEL image")
        latest_rhel_auto = self.vm_spec['image_name']
        html_text = requests.get(self.vm_spec['image_path'], timeout=60).text
        html_soup = BeautifulSoup(html_text, "html.parser")
        a_contents = html_soup.find_all('a')
        for content in a_contents:
            # if "RHEL-" matches content.get_text():
            if re.match(re.compile(re.escape('RHEL-'), re.IGNORECASE), str.strip(content.get_text())):
                latest_rhel_auto = str.strip(content.get_text())
                self.logger.info(f"We have the latest _AUTO image name, is {latest_rhel_auto}")
        if self.vm_spec['image_name'] == latest_rhel_auto:
            self.logger.info("The current image is the latest version")
            return
        self.logger.info("The current one is mismatch with the latest, we need to update")
        kwargs = {
            'name': latest_rhel_auto
        }
        ModelWorkloadImage().update_image_by_id(int(self.vm_spec['image_id']), **kwargs)
        self.vm_spec['image_name'] = latest_rhel_auto

    def upload_wl_image_to_pe(self, _pe):
        self.logger.info(f"Prepare to upload image {self.vm_spec['image_alias']} to {_pe.pe}")
        res, container = _pe.get_container_by_name(name='SelfServiceContainer')  # Get the 'SelfServiceContainer' from PE, the container UUID is required for image upload action
        if not res:
            raise WorkloadException(f"The container 'SelfServiceContainer' does not exist in {_pe.pe}")
        self.logger.info(f"The container SelfServiceContainer is located, its UUID is {container['container_uuid']}")
        if '_AUTO' in self.vm_spec['image_alias']:  # We use differnt field to construct the image name, image_name-pe_name for RHELx_AUTO and image_alias-pe_name for others
            image_pe_name = f"{self.vm_spec['image_name']}-{_pe.pe}"
            image_url = f"{self.vm_spec['image_path']}{self.vm_spec['image_name']}"  # We checkout the image for RHEL_AUTO from the site provided by GLinux team
        else:
            pc = ModelPrismCentral.query.filter_by(fqdn=self.vm_spec['pc']).first()
            if not pc:
                pc = ModelWarehousePrismCentral.query.filter_by(fqdn=self.vm_spec[WorkloadSpec.PC]).first()
            if not pc.darksite:
                raise WorkloadException(f"Failed to get darksite of {self.vm_spec['pc']} in database")
            image_pe_name = f"{self.vm_spec['image_alias']}-{_pe.pe}"
            image_url = self.vm_spec['image_path'].replace("@@{{darksite}}@@", pc.darksite)  # We checkout the image for others form the darksite which is stored in the dh_ntx_pc table
        self.logger.info(f"Get the image {image_pe_name} from {image_url}")
        self.logger.info(f"Upload the image {image_pe_name} to {_pe.pe}")
        res, data = _pe.add_image(name=image_pe_name, annotation=image_pe_name,
                                  image_type="DISK_IMAGE", container_uuid=container['container_uuid'],
                                  image_url=image_url)  # Once the PE create a task for image uploading, it returns a task ID for track
        if not res:
            raise ImageUploadFailed(init=True, image_name=image_pe_name, original_err=data)
        self.logger.info(f"It's uploading the image, the task id is {data['taskUuid']}")
        res, msg = _pe.monitor_task_status(task_uuid=data['taskUuid'], retries=120, retry_interval=60)  # Check if the task completed
        if res == "succeeded":
            self.logger.info("Image upload is successfully completed")
            return
        if res == "failed":
            raise ImageUploadFailed(in_progress=True, image_name=image_pe_name, original_err=msg)
        raise ImageUploadOverTime(image_name=image_pe_name, completed_at=msg)  # If the task is not completed in 120 minutes, we raise an exception

    def get_wl_image(self, _pc, _pe):
        self.ilg.write("Check image", severity="title")
        self.ilg.write("Verifying workload image existence in DB and on cluster.")
        wl_image = ModelWorkloadImage().get_image_by_id(image_id=int(self.vm_spec['image_id']))  # load image from database by id, image{id, name, alias, path, md5, os_type}, and put attributes into the vm_spec
        self.vm_spec['image_name'] = wl_image.name
        self.vm_spec['image_path'] = wl_image.path
        self.vm_spec['image_alias'] = wl_image.alias
        self.logger.info("Checking if RHELx_AUTO image")
        if self.vm_spec.get('image_alias') and "_AUTO" in self.vm_spec['image_alias']:
            self.logger.info(f"Check the latest version from {self.vm_spec['image_path']} for _AUTO")
            self.validate_rhelauto_image()
            image_pe_name = f"{self.vm_spec['image_name']}-{_pe.pe}"  # RHEL-x-YYYYMMDDThhmmss-RETxxnnn-NXC00x
        else:
            image_pe_name = f"{self.vm_spec['image_alias']}-{_pe.pe}"
        count = 0
        while count < 5:
            self.logger.info(f"Validating image '{image_pe_name}' existence in PE '{_pe.pe}'...")  # When the image does not exist in PC, it may exists in PE, but yet migrated, or it does not exist in neither PC nor PE
            res, data = _pe.get_image_by_name(name=image_pe_name)
            if res:
                self.ilg.write("The image is found in PE")
                self.vm_spec['image_uuid'] = data['uuid']
                self.vm_spec[WorkloadSpec.IMAGE_VM_DISK_ID] = data['vm_disk_id']
                return
            self.ilg.write("Image doesn't exist on PE, will upload.")
            self.logger.info("Start to upload the image to PE...")
            self.upload_wl_image_to_pe(_pe=_pe)  # Upload image to PE
            count += 1  # Back to the start position of this WHILE loop, then we can get the image UUID from PC, and put it into the vm_spec
        raise WorkloadException("Failed to get workload image")

    def check_sizing(self, _pe):
        if self.vm_spec.get(WorkloadSpec.SKIP_SIZING):
            self.ilg.write("User forced to skip sizing, skipping...")
            return
        self.ilg.write("Sizing", severity="title")
        self.ilg.write("Sizing, check if cluster has enough capacity for this VM.")
        p_e = Prism_Element(sa=self.ntx_sa, fqdn=_pe.pe_fqdn, logger=self.logger)
        _sizing = Sizing(pe=p_e, logger=self.logger)
        _sizing.check_capacity(
            cpu=(self.vm_spec[WorkloadSpec.CPU] * self.vm_spec[WorkloadSpec.CPU_CORE]),
            memory=self.vm_spec[WorkloadSpec.MEMORY],
            disk=sum([int(_) for _ in self.vm_spec[WorkloadSpec.DISK]])
        )

    def check_vm_existence(self, _pe):
        """Check VM existence on NTX, AD/Ansible Inventory, Ipam"""
        self.ilg.write("Check VM existence", severity="title")
        self.ilg.write("Checking if vm already exists in the PE cluster.")
        vm_name = self.vm_spec[WorkloadSpec.VM_NAME]
        domain = _pe.pe_fqdn.split(".", maxsplit=1)[1]
        fqdn = f"{self.vm_spec[WorkloadSpec.VM_NAME]}.{domain}"

        # PE level check.
        res, mes = _pe.if_vm_exist(vm=vm_name)
        if not res:
            self.db_logger.write_pm_log(loginfo=mes)
            raise WorkloadExisting(vm_name, self.vm_spec[WorkloadSpec.PE])
        self.ilg.write(f"{vm_name} not exists in Cluster {self.vm_spec[WorkloadSpec.PE]}, move on...")

        # Directory level check, windows:AD/linux:AnsibleInventory
        self.ilg.write("Checking if vm already exists in the inventory AD/Tower.")
        if self.vm_spec[WorkloadSpec.WORKLOAD_TYPE] == "windows":
            if ThorsHammerAPI(logger=self.logger).is_computer_existing(vm_name):
                raise WorkloadExisting(vm_name, "AD")
            self.ilg.write(f"VM '{vm_name}' doesn't exist in AD, continue...")
        elif self.vm_spec[WorkloadSpec.WORKLOAD_TYPE] == "linux":
            inventory_id = 1073
            if Tower(logger=self.logger, db_logger=self.db_logger).is_host_existing_in_inventory(inventory_id, fqdn):
                raise WorkloadExisting(fqdn, f"Tower Inventory {inventory_id}")
            self.ilg.write(f"FQDN '{fqdn}' not exists in Tower Inventory, continue...")
        else:
            # Other workload types
            pass

        # DNS level check
        self.ilg.write("Checking if vm already exists in IPAM.")
        rest_ipam = Ipam(logger=self.logger)
        is_existing_in_ipam = rest_ipam.if_vmrecord_existed_ipam(fqdn)
        if is_existing_in_ipam:
            raise WorkloadExisting(vm_name, f"VM record '{vm_name}' already exists in IPAM!")
        self.ilg.write(f"'{self.vm_spec[WorkloadSpec.VM_NAME]}' not exists in IPAM, continue...")

    def check_ntx_network(self, _pe):
        self.ilg.write("Checking workload network on NTX.", severity="title")
        wl_network = WorkloadNetwork().get_network_by_vlan_id(vlan_id=int(self.vm_spec[WorkloadSpec.VLAN_ID]))
        self.vm_spec['vlan_name'] = wl_network['vlan_name']
        # check if vlan exists, otherwise, create a new network with required id and name convention
        network_pe_name = f"{_pe.pe.split('-')[0]}-{self.vm_spec[WorkloadSpec.VLAN_ID]}-{self.vm_spec['vlan_name']}"
        self.ilg.write(f"Checking PE network by VlanId={self.vm_spec[WorkloadSpec.VLAN_ID]}")

        count = 0
        while count < 5:
            data = _pe.get_network_by_vlan_id(vlan_id=int(self.vm_spec[WorkloadSpec.VLAN_ID]))
            if data:
                self.ilg.write(f"The network is found, the UUID is {data['uuid']}")
                self.vm_spec[WorkloadSpec.NETWORK_UUID] = data['uuid']
                break
            self.ilg.write("The network is NOT created, check if other tasks are running with the same network requirements.")
            parallel_tasks = ModelWorkloadTask.query.filter_by(
                vlan_id=int(self.vm_spec[WorkloadSpec.VLAN_ID])).filter_by(pe=self.vm_spec[WorkloadSpec.PE]).filter_by(
                status=TaskStatus.IN_PROGRESS)
            lowest_id = min([p_task.id for p_task in parallel_tasks])
            # if self has the lowest id, then create network
            # if other task has id less than self, then it will re-check after a short sleep
            # Along with the task with the lowest id running failed, the status changed to ERROR, we will find the another task has the lowest id, until the current task id is the lowest one
            if self.task.id == lowest_id:
                self.ilg.write(
                    f"The current task owns the lowest id, will create network with the name {network_pe_name}")
                _, data = _pe.add_network(name=network_pe_name, vlan_id=str(self.vm_spec[WorkloadSpec.VLAN_ID]))
                self.vm_spec[WorkloadSpec.NETWORK_UUID] = data[WorkloadSpec.NETWORK_UUID]
                break
            self.ilg.write(
                f"The task {lowest_id} started early, it will create network with the name {network_pe_name}, sleep 60s and check again.")
            time.sleep(60)
            count += 1
        if not self.vm_spec.get(WorkloadSpec.NETWORK_UUID):
            self.ilg.write(
                f"The network does not exist after waiting, create network with the name {network_pe_name} immediately.")
            _, data = _pe.add_network(name=network_pe_name, vlan_id=str(self.vm_spec[WorkloadSpec.VLAN_ID]))
            self.vm_spec[WorkloadSpec.NETWORK_UUID] = data['network_uuid']

    def _update_dns(self):
        self.logger.title("Update DNS")
        self.ilg.write("Start to assign IP to workload on IPAM...")
        _, pe_fqdn = CommonHelper.parse_pe_name_and_fqdn(self.vm_spec[WorkloadSpec.PE])
        domain = pe_fqdn.split(".", maxsplit=1)[1]
        fqdn = f"{self.vm_spec[WorkloadSpec.VM_NAME]}.{domain}"
        rest_ipam = Ipam(logger=self.logger)
        ip, ip_id = rest_ipam.assign_ip(
            vlan_id=self.vm_spec[WorkloadSpec.VLAN_ID],
            pe=self.vm_spec[WorkloadSpec.PE],
            fqdn=fqdn,
            subnet_ip=self.vm_spec.get(WorkloadSpec.SUBNET_IP),
            user_specified_ip=self.vm_spec.get(WorkloadSpec.USER_SPECIFIED_IP),
        )
        self.vm_spec[WorkloadSpec.IP] = ip
        self.vm_spec[WorkloadSpec.IP_ID] = ip_id
        self.set_task_property(WorkloadSpec.IP, ip)
        self.ilg.write(f"IP {ip} assigned to workload successfully. ip_id: {ip_id}")

    def _create_vm_by_pe_api(self, _pe):
        payload = self._generate_pe_create_vm_payload(_pe)
        task_uuid = _pe.create_vm(payload)
        if not _pe.is_task_succeeded(task_uuid):
            raise flaskex.BadGateway("VM creation task failed!")
        vm_uuid = _pe.get_task_detail(task_uuid)["entity_list"][0]["entity_id"]
        self.logger.info(f"Create VM by PE API succeeded! VM uuid: {vm_uuid}")
        return vm_uuid

    def _generate_pe_create_vm_payload(self, _pe):
        def _get_network_uuid():
            network = _pe.get_network_by_vlan_id(self.vm_spec[WorkloadSpec.VLAN_ID])
            if not network:
                raise flaskex.BadGateway(f"Can't find network with vlan_id {self.vm_spec[WorkloadSpec.VLAN_ID]} in pe!")
            return network['uuid']

        def _get_storage_container_uuid():
            containers = StorageContainer(self.vm_spec[WorkloadSpec.PE], self.ntx_sa, self.logger).list_storage_containers()
            for c in containers["entities"]:
                if c["name"] == "SelfServiceContainer":
                    return c["storage_container_uuid"]
            raise WorkloadException("Can't find storage container by name 'SelfServiceContainer'!")

        template_path = os.path.join(application_path, "static", "templates", "create_vm_by_pe_payload.jinja2")
        self.logger.info(f"template_path: {template_path}")
        with open(template_path) as f:
            t = Template(f.read())
        system_disk_size = int(self.vm_spec[WorkloadSpec.DISK][0])
        other_disks = self.vm_spec[WorkloadSpec.DISK][1:]
        user_data = self._generate_guest_customization_script(self.vm_spec) if self.vm_spec[WorkloadSpec.WORKLOAD_TYPE] == 'linux' else None
        render_params = {
            "workload_type": self.vm_spec[WorkloadSpec.WORKLOAD_TYPE],
            "vm_name": self.vm_spec[WorkloadSpec.VM_NAME],
            "memory_size_mib": self.vm_spec[WorkloadSpec.MEMORY] * 1024,  # convert GiB to MiB
            "num_cores_per_vcpu": self.vm_spec[WorkloadSpec.CPU_CORE],
            "num_vcpus": self.vm_spec[WorkloadSpec.CPU],
            "disks": [convert_GiB_to_bytes(int(disk)) for disk in other_disks],
            "system_disk_size": convert_GiB_to_bytes(system_disk_size),
            "network_uuid": _get_network_uuid(),
            "user_data": json.dumps(user_data),
            "storage_container_uuid": _get_storage_container_uuid(),
            "image_vm_disk_uuid": self.vm_spec[WorkloadSpec.IMAGE_VM_DISK_ID]
        }
        template = t.render(**render_params)
        return json.loads(template)

    def _generate_guest_customization_script(self, vm_spec):
        image_alias = vm_spec['image_alias']
        ip_id = vm_spec['ip_id']
        path = os.path.join("static", "OSCustomization")
        if image_alias == "RHEL9_AUTO" or image_alias == "RHEL8_AUTO":
            name = "LX-CloudInit-latest.yml"
        elif image_alias.startswith('RHEL8'):
            name = "LX-CloudInit-v2-RHEL8.yml"
        elif image_alias.startswith('RHEL9'):
            name = "LX-CloudInit-v1-RHEL9.yml"
        elif image_alias.startswith('RHEL7'):
            name = "LX-CloudInit-v1-RHEL7.yml"
        script_path = os.path.join(application_path, path, name)
        self.logger.info(f"script_path: {script_path}")
        with open(script_path) as f:
            script = f.read()
        ipam = Ipam(logger=self.logger)
        ip_class_parameters_dict = ipam.get_ip_class_parameters_dict(ip_id)
        ip_address_info = ipam.get_ip_address_info(ip_id)
        _, pe_fqdn = CommonHelper.parse_pe_name_and_fqdn(self.vm_spec[WorkloadSpec.PE])
        domain = pe_fqdn.split(".", maxsplit=1)[1]
        fqdn = f"{self.vm_spec[WorkloadSpec.VM_NAME].lower()}.{domain}"
        variables = {
            'VM_Name': vm_spec[WorkloadSpec.VM_NAME],
            'VM_Fqdn': fqdn,
            'VM_IP': vm_spec[WorkloadSpec.IP],
            'VM_SubnetMask': Ipam.calculate_subnet_mask(ip_address_info[0]["subnet_size"]),
            'VM_SubnetSize': Ipam.calculate_cidr(ip_address_info[0]["subnet_size"]),
            'VM_GW': ip_class_parameters_dict['gateway'],
            'VM_DnsDomain': ip_class_parameters_dict['domain'],
            'VM_DNS1': ip_class_parameters_dict['ikea_dns_server'],
            'VM_DNS2': ip_class_parameters_dict['ikea_dns_server_2'],
        }
        for k, v in variables.items():
            script = script.replace(f'@@{{{k}}}@@', v)
        self.logger.info(f"cloud init data: {script}")
        # in PE api payload, the cloud init data doesn't need to be base64 encoded, while in PC api it needs
        return script
        # return base64.b64encode(script.encode('utf-8')).decode()

    def _create_vm_on_ntx_process(self, _pe):
        self.db_logger.write_pm_log("Start to do the VM creating process on Nutanix.")
        workload_type = self.vm_spec[WorkloadSpec.WORKLOAD_TYPE]
        self.db_logger.write_pm_log(f"Workload type is {workload_type}")
        thors_hammer = ThorsHammerAPI(logger=self.logger)
        if workload_type == 'windows':
            self.db_logger.write_pm_log("Start to register machine in Thor's hammer...")
            thors_hammer.register_machine(self.vm_spec[WorkloadSpec.VM_NAME])
            prefix = self.vm_spec[WorkloadSpec.VM_NAME].split("-")[0]
            thors_hammer.make_profile(profiletype="TF", prefix=prefix)
            time.sleep(5)
            thors_hammer.add_host_profile(self.vm_spec[WorkloadSpec.VM_NAME], "Alias%20SET_INSTALL_TIME_FRAME")
        self.db_logger.write_pm_log("Start to create VM on Nutanix.")
        # For WIN servers, once the VM is created on NTX and powered on, it will be temporarily assigned a DHCP IP,
        # until thors_hammer.set_ip_address() in post_create()
        vm_uuid = self._create_vm_by_pe_api(_pe)
        self.db_logger.write_pm_log(f"VM created successfully on Nutanix. vm_uuid: {vm_uuid}")
        if workload_type == 'windows':
            self.db_logger.write_pm_log("Updating activation code for the machine in Thor's hammer...")
            thors_hammer.update_activation_code(self.vm_spec[WorkloadSpec.VM_NAME], vm_uuid)
        self.db_logger.write_pm_log("Power on VM...")
        _pe.set_vm_power_state(vm_uuid, 'ON')
        self.db_logger.write_pm_log("VM is power on now.")
        if workload_type == 'linux':
            self.db_logger.write_pm_log("Linux specific operations after VM power on...")
            if not self.vm_spec.get(WorkloadSpec.UPDATE_DNS):
                self.db_logger.write_pm_log("Sleep 30s after VM power on to get VM IP...")
                time.sleep(30)
                ntx_ip = _pe.get_vm_detail(vm_uuid, include_vm_nic_config=True)["vm_nics"][0]["ip_address"]
                self.vm_spec[WorkloadSpec.IP] = ntx_ip
                self.db_logger.write_pm_log(f"VM IP on Nutanix: {ntx_ip}")
            vm_ip = self.vm_spec[WorkloadSpec.IP]
            self.db_logger.write_pm_log(f"Testing if IP {vm_ip} is reachable...")
            if not ping3_with_retry(vm_ip, 400):
                raise WorkloadException(f'IP {vm_ip} is unreachable!')
            self.db_logger.write_pm_log("IP is reachable.")
            self.ilg.write("Sleep 5 minutes to ensure that DNS is correctly configured...")
            time.sleep(300)
            self.ilg.write("Woke up from sleep.")
        return vm_uuid

    def update_disks(self, pe, vm_uuid):
        vm_detail = pe.get_vm_detail(vm_uuid, include_vm_disk_config=True)
        pe.update_pe_disks(vm_detail, scsi_id=0, size_gb=self.vm_spec[WorkloadSpec.DISK[0]])

    def _launch_tower_workflow(self, template_id=9423):
        self.db_logger.write_pm_log(f"Start to launch tower template, id {template_id}.")
        tower = Tower(logger=self.logger, db_logger=self.db_logger)
        tower.get_tower_template(template_id)
        payload = json.loads(self.vm_spec[WorkloadSpec.LINUX_PAYLOAD])
        try:
            tower.execute_tower_template(template_id=template_id, payload=payload, retries=90)
        except TowerJobFailed as e:
            self.ilg.write(f"Tower job failed. Template id: {e.template_id}, job id: {e.job_id}")
            raise e
        self.db_logger.write_pm_log("Run tower template finished.")

    def _post_create(self):
        workload_type = self.vm_spec[WorkloadSpec.WORKLOAD_TYPE]
        if workload_type == 'windows':
            self.db_logger.write_pm_log("Start post creation stuff in Thor's hammer...")
            ThorsHammerAPI(logger=self.logger).post_create(self.vm_spec["name"], self.vm_spec["ip"])
        elif workload_type == 'linux':
            self.db_logger.write_pm_log("Start to launch tower templates")
            self._launch_tower_workflow()

    def _install_packages(self):
        self.logger.title("Install packages")
        if all([not _ for _ in self.vm_spec[WorkloadSpec.PACKAGES]]):
            self.ilg.write("App package not defined, skipping...")
            return
        self.ilg.write(f"Start to install packages: {self.vm_spec[WorkloadSpec.PACKAGES]}")
        AppPackage(self.vm_spec, self.ntx_sa, self.logger, self.db_logger, self.domain, self.facility_type).install_packages()
        self.ilg.write("Installation done.")

    def _add_vm_to_ntx_pd_process(self, vm_uuid):
        self.logger.title("Adding VM to PD")
        _pd = ProtectionDomain(pe=self.vm_spec[WorkloadSpec.PE], sa=self.ntx_sa, logger=self.logger)
        pe_cluster = self.vm_spec[WorkloadSpec.PE].split('.')[0]
        pd_name = f"{pe_cluster}-Gold_CCG"  # pd_name: {cluster_name}-Gold_CCG
        self.ilg.write(f"Adding VM to Protection domain {pd_name}")
        if not _pd.is_pd_existing(pd_name):
            self.ilg.write("PD doesn't exist on NTX, create a new one...")
            _pd.create_pd(pd_name)
        self.ilg.write("Adding schedules to PD...")
        _pd.add_schedules_to_pd(pd_name)
        self.ilg.write("Adding VM to PD...")
        _pd.add_vm_to_pd(vm_uuid, pd_name)

    def _update_acp_scope(self, vm_type):
        self.logger.title("Assign VM to role scope.")
        self.db_logger.write_log("Assign VM to role scope.")
        try:
            self.ilg.write(f"Updating ACP scpoe for {vm_type} vm type.")
            atm = Automation(pc=self.vm_spec[WorkloadSpec.PC], sa=self.ntx_sa, logger=self.logger)
            atm.dsc_acp(acp_scope=vm_type)
        except Exception as e:
            self.ilg.write(f"An error occurred when updting ACP, error {str(e)}. But it won't impact the vm provisioning.", severity="warning")

    @staticmethod
    def get_workload_task_logs(task_id):
        logs = ModelWorkloadTaskLog.query.filter_by(task_id=task_id).order_by(ModelWorkloadTaskLog.id.desc()).all()
        _schema = ModelWorkloadTaskLogSchema(many=True)
        return _schema.dump(logs)


class WorkloadTemplate():
    def __init__(self, token=None, template_id=None, logger=logging) -> None:
        self.template_id = template_id
        self.token = token
        self.logger = logger if logger else logging

    def get_template_list(self):
        try:
            _template_schema = ModelWorkloadTemplateSchema(many=True)
            _template_list = ModelWorkloadTemplate().query.all()
            return _template_schema.dump(_template_list)
        except Exception as e:
            logging.error(str(e))
            return False

    def get_template_by_id(self):
        try:
            _template_schema = ModelWorkloadTemplateSchema()
            _template = ModelWorkloadTemplate.query.filter_by(id=self.template_id).first()
            return _template_schema.dump(_template)
        except Exception as e:
            logging.error(str(e))
            return False

    @staticmethod
    def if_template_exist(template_id):
        try:
            template = ModelWorkloadTemplate.query.filter_by(id=template_id).first()
            return template, ''
        except Exception as e:
            return False, str(e)

    def create_workload_template(self, data):
        user = PrivilegeValidation.get_user_from_token()
        data['owner_id'] = user.id
        data[WorkloadSpec.DISK] = ','.join(str(x) for x in data[WorkloadSpec.DISK])
        if data.get(WorkloadSpec.PACKAGES):
            data[WorkloadSpec.PACKAGES] = ','.join(data[WorkloadSpec.PACKAGES])
        if data[WorkloadSpec.WORKLOAD_TYPE] == "windows":
            data[WorkloadSpec.BOOT_MODE] = "SECURE_BOOT"
        elif data[WorkloadSpec.WORKLOAD_TYPE] == "linux" or data[WorkloadSpec.WORKLOAD_TYPE] == "network":
            data[WorkloadSpec.BOOT_MODE] = "LEGACY"
        if not data.get(WorkloadSpec.IMAGE_NAME):
            image_name = ModelWorkloadImage.query.filter_by(id=data[WorkloadSpec.IMAGE_ID]).scalar().name
            data[WorkloadSpec.IMAGE_NAME] = image_name
        new_template = ModelWorkloadTemplate.create(data)
        return ModelWorkloadTemplateSchema().dump(new_template)

    def update_template(self, specs):
        user = PrivilegeValidation.get_user_from_token()
        logging.info(f"User {user.username} updating template id'{specs[WorkloadSpec.TEMPLATE_ID]}'")
        logging.info(f"New specs: {specs}")
        template = ModelWorkloadTemplate.update(specs)
        logging.critical("Workload template updated.")
        return template

    def delete_templates(self, template_ids):
        user = PrivilegeValidation.get_user_from_token()
        logging.info(f"User {user.username} deleting templates: {template_ids}")
        ModelWorkloadTemplate.bulk_delete_by_id(template_ids)
        logging.info("Workload template deleted.")


class WorkloadImage():
    def __init__(self) -> None:
        pass

    def get_image_list(self):
        try:
            _image_schema = ModelWorkloadImageSchema(many=True)
            _image_list = ModelWorkloadImage().query.all()
            return _image_schema.dump(_image_list)
        except Exception as e:
            logging.error(str(e))
            return None

    def get_image_by_id(self, image_id):
        try:
            image_list = self.get_image_list()
            for image in image_list:
                if image_id == image['id']:
                    return image
            return None
        except Exception as e:
            logging.error(str(e))
            return None

    def update_image_name(self, image_id, name):
        try:
            ModelWorkloadImage().update_image_name(id=image_id, name=name)
            return True
        except Exception as e:
            logging.error(str(e))
            return False

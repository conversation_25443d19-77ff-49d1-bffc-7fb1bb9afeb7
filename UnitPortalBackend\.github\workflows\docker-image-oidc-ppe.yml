name: Docker Build DHUP PPE

on: 
  workflow_dispatch:
  push:
    branches:
      - main

env:
  ARTIFACTORY_URL: artifactory.build.ingka.ikea.com
  ARTIFACTORY_REPO: distributedhostingcodecommunity-dhup-docker-dev-local
  IMAGE_NAME: dhup-backend

permissions:
  id-token: write
  contents: read

jobs:
  Build_and_push_image_to_Artifactory:
    runs-on: mgke-prod
    outputs:
      GIT_BRANCH: ${{ steps.meta.outputs.tags }}
      SHA_SHORT: ${{ steps.runtime-variables.outputs.SHA_SHORT }}
      UNIX_DATE: ${{ steps.runtime-variables.outputs.UNIX_DATE }}
      FULL_IMAGE_URI: ${{ steps.compiled-variable.outputs.FULL_IMAGE_URI }}
    steps:
      - name: Checkout local repository
        uses: actions/checkout@v4
        with:
          clean: 'true'
          fetch-tags: 'true'

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.ARTIFACTORY_URL }}/${{ env.ARTIFACTORY_REPO }}/${{ env.IMAGE_NAME }}

      - name: Setup JFrog CLI
        uses: jfrog/setup-jfrog-cli@v4
        id: login
        env:
          JF_URL: https://${{ env.ARTIFACTORY_URL }}
        with:
          oidc-provider-name: "ghes-prod"
          oidc-audience: "jfrog-github"

      - name: Login to Artifactory
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ARTIFACTORY_URL }}
          username: ${{ steps.login.outputs.oidc-user }}
          password: ${{ steps.login.outputs.oidc-token }}
          
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set runtime variables
        id: runtime-variables
        run: |
          echo "BUILD_DATE=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_OUTPUT
          echo "SHA_SHORT=$(git rev-parse --short "$GITHUB_SHA")" >> $GITHUB_OUTPUT
          echo "UNIX_DATE=$(date +'%s')" >> $GITHUB_OUTPUT

      - name: Set compiled variable
        id: compiled-variable
        run: |
          echo "FULL_IMAGE_URI=${{ steps.meta.outputs.tags }}-${{ steps.runtime-variables.outputs.SHA_SHORT }}-${{ steps.runtime-variables.outputs.UNIX_DATE }}" >> $GITHUB_OUTPUT

      - name: Push to artifactory
        uses: docker/build-push-action@v6
        with:
          push: true
          context: .
          tags: ${{ steps.meta.outputs.tags }}-${{ steps.runtime-variables.outputs.SHA_SHORT }}-${{ steps.runtime-variables.outputs.UNIX_DATE }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BUILD_TAG=${{ github.ref_name }}
            BUILD_DATE=${{ steps.runtime-variables.outputs.BUILD_DATE }}
            GIT_COMMIT_ID=${{ steps.runtime-variables.outputs.SHA_SHORT }}

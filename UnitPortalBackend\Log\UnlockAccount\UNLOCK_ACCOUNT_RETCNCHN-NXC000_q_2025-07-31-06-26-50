2025-07-31 14:26:51,127 INFO Start to run the task.
2025-07-31 14:26:51,165 INFO Starting to unlock account for PE: RETCNCHN-NXC000
2025-07-31 14:26:51,204 INFO Getting PE information
2025-07-31 14:26:51,313 INFO Found PE in retail: retcnchn-nxc000.ikea.com, PC: ssp-china-ntx.ikea.com
2025-07-31 14:26:51,351 INFO Executing account unlock operation
2025-07-31 14:26:51,392 INFO Connecting to Nutanix cluster...
2025-07-31 14:26:53,052 INFO Unlocking accounts...
2025-07-31 14:26:53,053 INFO Trying to SSH to the pe retcnchn-nxc000.
2025-07-31 14:26:53,063 INFO SSH connecting to retcnchn-nxc000.ikea.com, this is the '1' try.
2025-07-31 14:26:53,313 INFO SSH connected to retcnchn-nxc000.ikea.com.
2025-07-31 14:26:54,905 INFO SSH connecting to ssp-china-ntx.ikea.com, this is the '1' try.
2025-07-31 14:26:55,120 INFO SSH connected to ssp-china-ntx.ikea.com.
2025-07-31 14:26:55,227 INFO Unlocking PC account: 'admin'
2025-07-31 14:26:55,227 INFO Sending 'allssh sudo faillock --user admin --reset' to the server.
2025-07-31 14:27:00,228 INFO Successfully unlocked PC account: 'admin'
2025-07-31 14:27:00,228 INFO Unlocking PC account: 'nutanix'
2025-07-31 14:27:00,228 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.
2025-07-31 14:27:05,229 INFO Successfully unlocked PC account: 'nutanix'
2025-07-31 14:27:05,230 INFO Unlocking PC account: '1-click-nutanix'
2025-07-31 14:27:05,230 INFO Sending 'allssh sudo faillock --user 1-click-nutanix --reset' to the server.
2025-07-31 14:27:10,231 INFO Successfully unlocked PC account: '1-click-nutanix'
2025-07-31 14:27:11,217 INFO SSH connecting to retcnchn-nxc000.ikea.com, this is the '1' try.
2025-07-31 14:27:11,483 INFO SSH connected to retcnchn-nxc000.ikea.com.
2025-07-31 14:27:11,570 INFO Unlocking PE account: 'admin'
2025-07-31 14:27:11,571 INFO Sending 'allssh sudo faillock --user admin --reset' to the server.
2025-07-31 14:27:16,572 INFO Successfully unlocked PE account: 'admin'
2025-07-31 14:27:16,572 INFO Unlocking PE account: 'nutanix'
2025-07-31 14:27:16,572 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.
2025-07-31 14:27:21,574 INFO Successfully unlocked PE account: 'nutanix'
2025-07-31 14:27:21,575 INFO Unlocking PE account: '1-click-nutanix'
2025-07-31 14:27:21,575 INFO Sending 'allssh sudo faillock --user 1-click-nutanix --reset' to the server.
2025-07-31 14:27:26,575 INFO Successfully unlocked PE account: '1-click-nutanix'
2025-07-31 14:27:26,615 INFO Account unlocked successfully
2025-07-31 14:27:26,750 INFO Task is in 'Done' status.

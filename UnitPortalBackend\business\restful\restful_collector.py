import json
from flask_restful import Resource
from business.authentication.tokenvalidation import PrivilegeValidation
from flask import abort, request, Response
from .route import route


@route('/api/v1/ntx/automation/data_fetch')
class RestfulCollector(Resource):

    @PrivilegeValidation(privilege={"role_lcm": "view_atm"})
    def post(self):
        from collector.collector import CollectorRunner
        data = request.get_json(force=True)
        
        try:
            
            # Create a mapping from class names to class references dynamically
            collector_mapping = {cls.__name__: cls for cls in CollectorRunner.DEFAULT_COLLECTORS_RETAIL}
            empty = [False, False]  # if both are empty at the end, return 400
            
            # Retail collectors
            try:
                collectors_retail = [collector_mapping[name] for name in data['retail'] if name in collector_mapping]
            except KeyError:
                collectors_retail = []
                
            if not collectors_retail:
                collectors_retail = ['empty']  # Set to empty if no collectors found so if collectors_retail == True
                empty[0] = True  
            # Warehouse collectors
            try:
                collectors_wh = [collector_mapping[name] for name in data['warehouse'] if name in collector_mapping]
            except KeyError: 
                collectors_wh = []
               
            if not collectors_wh:
                collectors_wh = ['empty']
                empty[1] = True

            if empty[0] and empty[1]:
                abort(400, "No collectors found, check input. Payload should be like {'retail': ['collector1','collector2'] or [], 'warehouse': ['collector']}' or []")
            
            collector = CollectorRunner(collectors_retail = collectors_retail, collectors_wh = collectors_wh)
            # Perform collection
            collector.collect()

            return Response(json.dumps('Collector task complete'), status=200, mimetype='application/json')
        except Exception as e:
            abort(500, "Internal error for Collector task: " + str(e))

#installed modules
import logging
import sys

from flask_apispec import use_kwargs, doc
from flask_apispec.views import MethodResource

from business.distributedhosting.nutanix.delete_workload_task import CleanupFailedWorkloadTask
from business.distributedhosting.nutanix.workload.app_package import AppPackage
from static.WORKLOAD_SPEC import WorkloadSpec, TemplateSpec
from swagger.template_schema import CreateTemplateRequestSchema, DeleteTemplateRequestSchema, \
    UpdateTemplateRequestSchema
from swagger.workload_schema import CreateWorkloadRequestSchema, CleanupWorkloadRequestSchema, \
    DeleteWorkloadRequestSchema

sys.path.insert(0, '..')
sys.path.insert(0, '../..')
from flask_restful import Resource
from flask import abort, request, send_file, jsonify
#local files
from business.authentication.authentication import User
from business.authentication.tokenvalidation import admintokencheck, PrivilegeValidation
from business.distributedhosting.nutanix.workload.workload import WorkloadTemplate, CreateWorkloadTask, WorkloadImage
from business.generic.commonfunc import get_request_token
from business.distributedhosting.nutanix.delete_workload_task import DeleteWorkloadTask
from .route import route


@route('/api/v1/ntx/workload/template/list')
class RestfulWorkloadTemplateList(Resource):
    @PrivilegeValidation(privilege={"role_mkt": "view_template"})
    def get(self):
        try:
            # token = get_request_token()#{'Authorization':"bearer ****-*****-*****-****"}
            _template = WorkloadTemplate()
            if template_list := _template.get_template_list():
                return jsonify(template_list)
            raise Exception("Failed to get the template list.")
        except:
            abort(500, "Internal error")


@route('/api/v1/ntx/workload/task/list/<type>')
class RestfulWorkloadTaskList(Resource):
    @PrivilegeValidation(privilege={"role_mkt": "view_wl"})
    def get(self, type):
        try:
            page = request.args.get('page', type=int)
            limit = request.args.get('limit', type=int)
            if type.upper() == 'CREATE':
                _task = CreateWorkloadTask
            else:
                _task = DeleteWorkloadTask
            if task_list := _task.get_workload_task(getlog=True, page=page, limit=limit):
                return task_list
            raise Exception("Failed to get the workload list.")
        except:
            abort(500, "Internal error")


@route('/api/v1/ntx/workload/task/<type>')
class RestfulWorkloadTasks(Resource):
    # @PrivilegeValidation(privilege={"role_mkt": "view_wl"})
    def get(self, type):
        try:
            if type.upper() == 'CREATE':
                _task = CreateWorkloadTask
            else:
                _task = DeleteWorkloadTask
            if task_list := _task.get_workload_task(getlog=False):
                return task_list
            raise Exception("Failed to get the workload tasks.")
        except:
            abort(500, "Internal error")


@route('/api/v1/ntx/workload/task/log')
class RestfulWorkloadTaskLog(Resource):
    @PrivilegeValidation(privilege={"role_mkt": "view_wl"})
    def post(self):
        param = request.get_json(force=True)
        return CreateWorkloadTask.get_workload_task_logs(param["task_id"]), 200


@route('/api/v1/ntx/workload/info')
class RestfulGetWorkloadInfo(Resource):
    @PrivilegeValidation(privilege={"role_mkt": "view_wl"})
    def get(self):        
        try:
            response = {
                        "image": [],
                        "template": []
                        } 
            _image = WorkloadImage()
            _template = WorkloadTemplate()
            if image_list := _image.get_image_list():
                response["image"] = image_list
            if template_list := _template.get_template_list():
                response["template"] = template_list
            return jsonify(response)
        except:
            abort(500, "Internal error")


@route('/api/v1/ntx/workload')
class RestfulWorkload(MethodResource, Resource):
    # note: first @PrivilegeValidation, then @use_kwargs
    @PrivilegeValidation(privilege={"role_mkt": "create_wl"})
    @use_kwargs(CreateWorkloadRequestSchema(many=True), location='json')
    def post(self, *args, **kwargs):
        logging.info(f"args: {args}")
        logging.info(f"kwargs: {kwargs}")
        try:
            output = []
            if param := request.get_json(force=True):
                for vm in param:
                    res, mes = CreateWorkloadTask(vm).run()
                    if res:
                        output.append({
                            "name": vm.get('name'),
                            "template_id": vm.get(WorkloadSpec.TEMPLATE_ID),
                            "message": "Task has been created.",
                            "flag": True
                        })
                    else:
                        output.append({
                            "name": vm.get('name'),
                            "template_id": vm.get(WorkloadSpec.TEMPLATE_ID),
                            "message": f"Failed to create the task, error: {mes}.",
                            "flag": False
                        })
                return jsonify(output)
            raise Exception({'code': 400, 'message': 'Missing vm data.'}) #raise if pm details not given
        except Exception as e:
            if e.args:
                if type(e.args[0]) == dict:
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))

    @use_kwargs(DeleteWorkloadRequestSchema(many=True), location='json')
    @PrivilegeValidation(privilege={"role_mkt": "remove_wl"})
    def delete(self, *args, **kwargs):
        logging.info(f"args: {args}")
        logging.info(f"kwargs: {kwargs}")
        response = []
        param = request.get_json(force=True)
        for vm in param:
            try:
                DeleteWorkloadTask(vm).run()
                task_result = {
                    "vm_name": vm["name"],
                    "success": True,
                    "message": "Delete workload task has been created."
                }
                response.append(task_result)
            except Exception as e:
                task_result = {
                    "vm_name": vm["name"],
                    "success": False,
                    "message": f"Failed to create delete workload task. Reason: {str(e)}"
                }
                response.append(task_result)
        return response


#####Workload Template Operation#####
@route('/api/v1/ntx/workload/template')
class RestfulWorkloadTemplate(MethodResource, Resource):
    @doc(description="Create workload template", tags=['Marketplace'])
    @PrivilegeValidation(privilege={"role_mkt": "create_template"})
    @use_kwargs(CreateTemplateRequestSchema(many=True), location='json')
    def post(self, *args, **kwargs):
        """Create list of templates"""
        logging.info(f"args: {args}")
        logging.info(f"kwargs: {kwargs}")
        response = []
        params = request.get_json(force=True)
        for template_spec in params:
            try:
                new_template = WorkloadTemplate().create_workload_template(template_spec)
                response.append({
                    "success": True,
                    "template_id": new_template[TemplateSpec.ID],
                    "specs": new_template
                })
            except Exception as e:
                response.append({
                    "success": False,
                    "message": f"Failed to create workload template. Reason: {str(e)}"
                })
        return response

    @doc(description="Delete workload template", tags=['Marketplace'])
    @PrivilegeValidation(privilege={"role_mkt": "remove_template"})
    @use_kwargs(DeleteTemplateRequestSchema(), location='json')
    def delete(self, *args, **kwargs):
        """delete list of templates, by template id"""
        logging.info(f"args: {args}")
        logging.info(f"kwargs: {kwargs}")
        param = request.get_json(force=True)
        WorkloadTemplate().delete_templates(param['template_ids'])

    @doc(description="Update workload template", tags=['Marketplace'])
    @PrivilegeValidation(privilege={"role_mkt": "edit_template"})
    @use_kwargs(UpdateTemplateRequestSchema(), location='json')
    def put(self, *args, **kwargs):
        """update one template"""
        logging.info(f"args: {args}")
        logging.info(f"kwargs: {kwargs}")
        param = request.get_json(force=True)
        template = WorkloadTemplate().update_template(param)
        return {"success": True, "specs": template}


@route('/api/v1/ntx/workload/template/admin')
class RestfulGetWorkloadTemplate(Resource):
    @admintokencheck
    def get(self):        
        try:
            response = {
                        "template": []
                        } 
            _template = WorkloadTemplate()
            if template_list := _template.get_template_list():
                response["template"] = template_list
            return jsonify(response)
        except:
            abort(500, "Internal error")


@route('/api/v1/ntx/workload/template/admin/id')
class RestfulGetWorkloadTemplatebyid(Resource):
    @admintokencheck
    def post(self):        
        try:
            param = request.get_json(force=True)
            template_id = param['id']
            response = {
                        "template": []
                        } 
            _template = WorkloadTemplate(template_id)
            if template_list := _template.get_template_by_id():
                response["template"] = template_list
            return jsonify(response)
        except:
            abort(500, "Internal error")


@route('/api/v1/ntx/workload/supported_packages')
class RestfulSupportedpackages(Resource):
    def get(self):
        return AppPackage.supported_packages


@route('/api/v1/ntx/workload/log/download')
class RestfulDownloadWorkloadLog(Resource):  # need to optimize.
    def post(self):
        try:
            token = get_request_token()
            usr = User()  # get the user name , need to log it
            res, role = usr.get_role_by_token(token)
            if res:
                username = role.username  # noqa    # pylint: disable=W0612
            else:
                # if cann't get the username, then quit
                raise Exception({'code': 401, 'message': 'Username not found by the token.'})
            param = request.get_json(force=True)
            filepath = param['filepath']
            if not filepath:
                raise Exception({'code': 400, 'message': 'Cannot download the log file due to lack of file path.'})
            return send_file(filepath)
        except Exception as e:
            if e.args:
                if isinstance(e.args[0], dict):
                    if e.args[0].__contains__('code'):
                        abort(e.args[0]['code'], e.args[0]['message'])
            abort(500, str(e))


@route('/api/v1/ntx/workload/cleanup')
class RestfulCleanupFailedWorkload(MethodResource, Resource):
    @use_kwargs(CleanupWorkloadRequestSchema(many=True), location='json')
    @PrivilegeValidation(privilege={"role_mkt": "remove_wl"})
    def delete(self, *args, **kwargs):
        logging.info(f"args: {args}")
        logging.info(f"kwargs: {kwargs}")
        response = []
        param = request.get_json(force=True)
        for vm in param:
            try:
                CleanupFailedWorkloadTask(vm).run()
                task_result = {
                    "task_id": vm["task_id"],
                    "success": True,
                    "message": "Cleanup task has been created."
                }
                response.append(task_result)
            except Exception as e:
                task_result = {
                    "task_id": vm["task_id"],
                    "success": False,
                    "message": f"Failed to create cleanup task. Reason: {str(e)}"
                }
                response.append(task_result)
        return response

from models.database import db, ma
from marshmallow import fields


class ModelNTXPMTask(db.Model):
    __tablename__ = 'dh_retail_ntx_pm_task'
    id = db.Column(db.Integer, primary_key=True)
    prism = db.Column(db.String(50))
    cluster = db.Column(db.String(50))
    startdate = db.Column(db.String(50))
    pmtype = db.Column(db.String(50))
    status = db.Column(db.String(50))
    creater = db.Column(db.String(50))
    createdate = db.Column(db.String(50))
    createrinfo = db.Column(db.String(200))
    description = db.Column(db.String(255))
    pid = db.Column(db.Integer)
    detaillogpath = db.Column(db.String(255))
    logs = db.relationship('ModelNTXPMLog', backref='task', cascade="all, delete-orphan")
    # logs = db.relationship('ModelNTXPMLog', back_populates = 'ModelSLIPMTask', cascade="all, delete-orphan")


class ModelNTXPMTaskSchema(ma.Schema):
    class Meta:
        fields = ('id', 'prism', 'cluster', 'startdate', 'pmtype', 'status', 'creater', 'createdate', 'createrinfo',
                  'description', 'pid', 'detaillogpath')


class ModelNTXPMLog(db.Model):
    __tablename__ = 'dh_retail_ntx_pm_log'
    id = db.Column(db.Integer, primary_key=True)
    tasktype = db.Column(db.String(50))
    logdate = db.Column(db.String(50))
    severity = db.Column(db.String(50))
    loginfo = db.Column(db.String(255))
    task_id = db.Column(db.Integer, db.ForeignKey('dh_retail_ntx_pm_task.id'))
class ModelNTXPMLogSchema(ma.Schema):
    id = fields.Int()
    tasktype = fields.Str()
    logdate = fields.Str()
    severity = fields.Str()
    loginfo = fields.Str()
    task_id = fields.Int()

    class Meta:
        fields = ('id', 'tasktype', 'logdate', 'severity', 'loginfo', 'task_id')



##......................................................................................................................
##....SSSSSSSSSS....iiiii......................................lllll.iiiiiiiVVVV......VVVVVViiii....ttt.................
##...SSSSSSSSSSSS...iiiii......................................lllll.iiiii.iVVVV......VVVVVViiii...tttt.................
##..SSSSSSSSSSSSS...iiiii......................................lllll.iiiii.iVVVV......VVVVVViiii...tttt.................
##..SSSSSS.SSSSSSS.............................................lllll.......iVVVVV....VVVVVV........tttt.................
##..SSSSS....SSSSS..iiiii.mmmmmmmmmmm.mmmmmmm...ppppppppppp....lllll.iiiii..VVVVV....VVVVV.Viiii.ittttttttyyyy....yyyy..
##..SSSSSSS.........iiiii.mmmmmmmmmmmmmmmmmmmm..pppppppppppp...lllll.iiiii..VVVVV....VVVVV.Viiii.ittttttttyyyy...yyyyy..
##..SSSSSSSSSS......iiiii.mmmmmmmmmmmmmmmmmmmm..ppppppppppppp..lllll.iiiii..VVVVVV..VVVVVV.Viiii.ittttttttyyyy...yyyyy..
##...SSSSSSSSSSSS...iiiii.mmmmmmmmmmmmmmmmmmmm..ppppppppppppp..lllll.iiiii...VVVVV..VVVVV..Viiii...tttt...yyyyy..yyyyy..
##....SSSSSSSSSSSS..iiiii.mmmmmm..mmmmm..mmmmm..ppppp...ppppp..lllll.iiiii...VVVVV..VVVVV..Viiii...tttt...yyyyy.yyyyy...
##......SSSSSSSSSS..iiiii.mmmmm...mmmmm..mmmmm..ppppp...ppppp..lllll.iiiii...VVVVVVVVVVV...Viiii...tttt...yyyyy.yyyyy...
##..........SSSSSS..iiiii.mmmmm...mmmmm..mmmmm..ppppp....pppp..lllll.iiiii....VVVVVVVVVV...Viiii...tttt....yyyyyyyyyy...
##.SSSSS......SSSSS.iiiii.mmmmm...mmmmm..mmmmm..ppppp...ppppp..lllll.iiiii....VVVVVVVVVV...Viiii...tttt....yyyyyyyyy....
##.SSSSSS....SSSSSS.iiiii.mmmmm...mmmmm..mmmmm..ppppp...ppppp..lllll.iiiii....VVVVVVVVV....Viiii...tttt....yyyyyyyyy....
##..SSSSSSSSSSSSSS..iiiii.mmmmm...mmmmm..mmmmm..ppppppppppppp..lllll.iiiii.....VVVVVVVV....Viiii...ttttttt..yyyyyyy.....
##..SSSSSSSSSSSSSS..iiiii.mmmmm...mmmmm..mmmmm..ppppppppppppp..lllll.iiiii.....VVVVVVVV....Viiii...ttttttt..yyyyyyy.....
##...SSSSSSSSSSSS...iiiii.mmmmm...mmmmm..mmmmm..pppppppppppp...lllll.iiiii.....VVVVVVV.....Viiii...ttttttt...yyyyyy.....
##....SSSSSSSSSS....iiiii.mmmmm...mmmmm..mmmmm..ppppppppppp....lllll.iiiii......VVVVVV.....Viiii...ttttttt...yyyyy......
##..............................................ppppp........................................................yyyyy......
##..............................................ppppp.....................................................y.yyyyyy......
##..............................................ppppp.....................................................yyyyyyy.......
##..............................................ppppp.....................................................yyyyyyy.......
##..............................................ppppp.....................................................yyyyyy........
##......................................................................................................................


class ModelSLIPMTask(db.Model):
    __tablename__ = 'dh_retail_sli_pm_task'
    id = db.Column(db.Integer, primary_key=True)
    vcenter = db.Column(db.String(50))
    cluster = db.Column(db.String(50))
    startdate = db.Column(db.String(50))
    pmtype = db.Column(db.String(50))
    status = db.Column(db.String(50))
    creater = db.Column(db.String(50))
    createdate = db.Column(db.String(50))
    createrinfo = db.Column(db.String(200))
    description = db.Column(db.String(255))
    pid = db.Column(db.Integer)
    detaillogpath = db.Column(db.String(255))
    logs = db.relationship('ModelSLIPMLog', backref='slitask', cascade="all, delete-orphan")


class ModelSLIPMTaskSchema(ma.Schema):
    class Meta:
        fields = ('id', 'vcenter', 'cluster', 'startdate', 'pmtype', 'status', 'creater', 'createdate', 'createrinfo',
                  'description', 'pid', 'detaillogpath')


class ModelSLIPMLog(db.Model):
    __tablename__ = 'dh_retail_sli_pm_log'
    id = db.Column(db.Integer, primary_key=True)
    tasktype = db.Column(db.String(50))
    logdate = db.Column(db.String(50))
    severity = db.Column(db.String(50))
    loginfo = db.Column(db.String(255))
    task_id = db.Column(db.Integer, db.ForeignKey('dh_retail_sli_pm_task.id'))


class ModelSLIPMLogSchema(ma.Schema):
    class Meta:
        fields = ('id', 'tasktype', 'logdate', 'severity', 'loginfo', 'task_id')

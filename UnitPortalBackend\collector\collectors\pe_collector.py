import asyncio
import functools
import json
import logging
import time
from datetime import datetime
from typing import Any, Dict, Iterable, List, Optional, Tuple, Union
from uuid import uuid4

import requests

from business.distributedhosting.nutanix.nutanix import Prism_Element
from business.generic.commonfunc import split_pe_into_parts
from collector.collectors.base_collector import BaseCollector
from collector.collectors.modules.api_calls import APICallsPECollector, with_app_context
from collector.collectors.modules.db_operations import DatabaseOperations
from models.database import db
from models.ntx_models import ModelPrismElement
from models.ntx_models_wh import ModelWarehousePrismElement

# ----------------------
# Exception Classes
# ----------------------
GLOBAL_LOGGER_PE_FILE = None  # Used to get the correct logger for functions and classes outside of PECollector class. Is set in __init__ of PECollector class


class AuthenticationError(Exception):
    """Exception raised when authentication fails with Prism Element."""


class DataCollectionError(Exception):
    """Exception raised when data collection fails."""


# try / catch wrapper for functions
def try_catch_wrapper(func):
    def wrapper(*args, **kwargs):
        try:
            # if function is an async function, await it
            if asyncio.iscoroutinefunction(func):
                return asyncio.run(func(*args, **kwargs))
            return func(*args, **kwargs)

        except Exception as e:
            # print a better error message with what happened in function func and which line
            import traceback

            print(f"Error in {func.__name__}: {e}")
            traceback.print_exc()

            # if the function doesnt specify a return type, Error log and return None
            if func.__annotations__.get("return") is None:
                print(f"Error in {func.__name__}: No return type specified")
                return None

            # if the function has a boolean return type, return False
            if func.__annotations__.get("return") == bool:
                return False
            # otherwise return false value of the return type
            return func.__annotations__.get("return")()

    return wrapper


# ----------------------
# Utility Functions
# ----------------------


def bytes_to_gb(bytes_value: int) -> int:
    """Convert bytes to gigabytes, rounded to the nearest integer.

    Args:
        bytes_value: Size in bytes

    Returns:
        Size in gigabytes (rounded)
    """
    return int(round(bytes_value / (1024**3), 0))


def text_to_json(response: Union[requests.Response, str]) -> dict:
    """Load the response text into a JSON object.

    Args:
        response: Either a requests.Response object or a string containing JSON

    Returns:
        Parsed JSON as a dictionary, or empty dict if parsing fails

    Raises:
        RuntimeError: If response status code is not 200
    """
    logger = GLOBAL_LOGGER_PE_FILE if GLOBAL_LOGGER_PE_FILE else logging.getLogger(__name__)
    content = {}
    try:
        if isinstance(response, requests.Response):
            if response.status_code != 200:
                raise RuntimeError(f"Unexpected status code: {response.status_code}")
            text = response.text
        else:
            text = response
        content = json.loads(text)
    except Exception as e:
        logger.error("ERROR: failed to convert text to json format")
        logger.error(e)
    finally:
        return content


def _convert_cert_date(date_str):
    month_int = {
        "Jan": "01",
        "Feb": "02",
        "Mar": "03",
        "Apr": "04",
        "May": "05",
        "Jun": "06",
        "Jul": "07",
        "Aug": "08",
        "Sep": "09",
        "Oct": "10",
        "Nov": "11",
        "Dec": "12",
    }
    # Converts date from 'Mon Feb 17 17:02:08 PST 2025' to date object in '20250217' format
    # date_str = "Mon Feb 17 17:02:08 PST 2025"
    date_str_array = date_str.split(" ")
    # date_str_arry = ['Mon', 'Feb', '17', '17:02:08', 'PST', '2025']
    return datetime.strptime(f"{date_str_array[5]}{month_int[date_str_array[1]]}{date_str_array[2]}", "%Y%m%d")


# ----------------------
# Main Collector Class
# ----------------------


class PECollector(BaseCollector):
    """Collector for Prism Element data.

    This class is responsible for collecting data from Nutanix Prism Elements,
    including cluster information, host details, VM data, and more.

    The collection process follows these steps:
    1. Get all Prism Centrals (PCs)
    2. For each PC, get all Prism Elements (PEs) managed by the PC
    3. For each PE, collect detailed information
    """

    def __init__(self, sa=None, facility_type="retail") -> None:
        """Initialize the PECollector.

        Args:
            sa: Service account credentials (optional)
        """
        super().__init__(sa, facility_type)
        # self.facility_type from basecollector. used to name log file
        # self.logger from basecollector. Used to log to specific file
        global GLOBAL_LOGGER_PE_FILE
        GLOBAL_LOGGER_PE_FILE = self.logger
        self.api = APICallsPECollector(logger=self.logger)
        self.database_operations = DatabaseOperations(logger=self.logger)
        self.warehouse = False
        self.__reset__()
        # Access the data_cache directly for better code organization

    def __reset__(self) -> None:
        self.decommissioned_pes = []
        self.collect_result = {}
        self.central_pes = self.database_operations.get_central_pes()

    # ----------------------
    # Main Collection Workflow
    # ----------------------

    def collect(self, warehouse: bool = False) -> None:
        """Main collection method that initiates the data collection process.

        This is the entry point for the collection workflow:
        1. Get all Prism Centrals
        2. Collect data from all PEs managed by these PCs
        3. Update the cache with collected data
        """
        self.logger.title("Starting PECollector{}".format("Retail" if not warehouse else "Warehouse"))
        start_time = time.time()

        self._collect(warehouse)

        elapsed_time = time.time() - start_time
        self.__reset__()
        self.logger.info("PECollector completed in %.2f seconds", elapsed_time)

    def _collect(self, warehouse: bool = False) -> None:
        self.warehouse = warehouse
        pc_fqdn_list = self._get_prism_centrals()
        self._collect_from_prism_centrals(pc_fqdn_list)

        # Skip updating Decommissioned PEs
        for pe_fqdn in self.decommissioned_pes:
            self.collect_result.pop(pe_fqdn)

        for pe_fqdn in self.collect_result:
            self.database_operations.record_normalize(self.collect_result[pe_fqdn], ModelPrismElement)

        self.logger.info("Flashing data to database")

        if self.warehouse:
            self.database_operations.update_db(
                self.collect_result, ModelWarehousePrismElement, ModelWarehousePrismElement.fqdn, db.session
            )
            return
        self.database_operations.update_db(self.collect_result, ModelPrismElement, ModelPrismElement.fqdn, db.session)

    @with_app_context
    @try_catch_wrapper
    def _get_prism_centrals(self) -> List[Tuple[int, str]]:
        """Get the list of Prism Central FQDNs.

        Returns:
            List of Prism Central FQDNs
        """
        pcs = {}
        if self.warehouse:
            pcs = self.database_operations.get_dh_warehouse_ntx_pc()
        else:
            pcs = self.database_operations.get_dh_retail_ntx_pc()

        return [(pcs[fqdn]["id"], fqdn) for fqdn in pcs.keys()]

    @try_catch_wrapper
    def _collect_from_prism_centrals(self, pc_fqdn_list: Iterable[Tuple[int, str]]) -> None:
        """Collect data from all Prism Elements managed by the given Prism Centrals.

        Args:
            pc_fqdn_list: List of Prism Central FQDNs
        """
        # Step 1: Get all PEs from all PCs
        pe_urls = self._get_all_pe_urls(pc_fqdn_list)

        # Step 2: Collect data from each PE
        self._collect_from_prism_elements(pe_urls)

    @try_catch_wrapper
    async def _get_all_pe_urls(self, pc_fqdn_list: List[str]) -> List[str]:
        """Get URLs for all Prism Elements managed by the given Prism Centrals.

        Args:
            pc_fqdn_list: List of Prism Central FQDNs

        Returns:
            List of PE URLs in the format "pc_fqdn/pe_fqdn"
        """
        # Add tasks to collect cluster data from each PC
        task_name = "pc_" + str(uuid4())
        for _, pc_fqdn in pc_fqdn_list:
            self.processes.add_task(task_name, self.api.get_cluster_data, pc_fqdn)

        await self.processes.execute_asynchronous_tasks()

        # Process results to extract PE URLs
        pe_data = [x for x in self.processes.get_results()[task_name] if x is not False and x != []]
        pe_data = functools.reduce(lambda x, y: x + y, pe_data, [])
        pe_data = [pe for pe in pe_data if not 'PRISM_CENTRAL' in pe['status']['resources']['config']['service_list']]
        return self._extract_pe_urls(pe_data, pc_fqdn_list)

    @with_app_context
    @try_catch_wrapper
    def _extract_pe_urls(self, pe_data: List[dict], pc_fqdn_list: List[Tuple[int, str]]) -> List[str]:
        """Extract PE URLs from process results.

        Args:
            pc_fqdns: list of pc fqdn to collect

        Returns:
            List of PE URLs in the format "pc_fqdn/pe_fqdn"
        """
        # Get PEs from PC results
        pes_from_pc = [
            "/".join(
                [
                    str([id for id, fqdn in pc_fqdn_list if fqdn == pe["prism"]][0]),
                    pe["prism"].lower(),
                    pe["spec"]["name"].lower() + "." + ".".join(pe["prism"].split(".")[1:]),
                ]
            )
            for pe in pe_data
            if pe["spec"]["name"].lower() != "unnamed"
        ]

        # Get PEs from cache
        if self.warehouse:
            cached_pes = self.api.get_warehouse_pe_data()
        else:
            cached_pes = self.api.get_retail_pe_data()

        pes_from_db = [
            "/".join(
                [
                    str([id for id, fqdn in pc_fqdn_list if fqdn == pe["prism"]][0]),
                    pe["prism"].lower(),
                    pe["fqdn"].split(".")[0].lower() + "." + ".".join(pe["fqdn"].split(".")[1:]),
                ]
            )
            for pe in cached_pes
        ]

        # Combine and deduplicate
        return list(set(pes_from_pc + pes_from_db))

    @try_catch_wrapper
    async def _collect_from_prism_elements(self, pe_urls: List[str]) -> None:
        """Collect data from each Prism Element.

        Args:
            pe_urls: List of PE URLs in the format "pc_fqdn/pe_fqdn"
        """
        # Add tasks to collect data from each PE
        task_name = "pe_" + str(uuid4())
        for pe_url in pe_urls:
            self.processes.add_task(task_name, self.get_pe_data, pe_url)

        await self.processes.execute_asynchronous_tasks()
        _ = self.processes.get_results()[task_name]

    # ----------------------
    # PE Data Collection
    # ----------------------

    @try_catch_wrapper
    def get_pe_data(self, pe_url: str) -> None:
        """Collect data for a specific Prism Element.

        Args:
            pe_url: URL of the Prism Element in format "pc_id/pc_fqdn/pe_fqdn"

        Returns:
            None, the data is stored in self.collect_result
        """
        # Split the pe_url to get the pc_fqdn and pe_fqdn
        pc_id, pc_fqdn, pe_fqdn = pe_url.split("/")
        pe_fqdn = pe_fqdn.lower()
        if "nxp" in pe_fqdn or pe_fqdn.startswith("pc_"):
            return

        if pe_fqdn not in self.collect_result:
            self.collect_result[pe_fqdn] = {"fqdn": pe_fqdn, "prism": pc_fqdn, "pc_id": pc_id, "status": "Updating"}

        # Skip decommissioned PEs
        if self.collect_result.get(pe_fqdn, {}).get("status") == "Decommissioned":
            self.decommissioned_pes.append(pe_fqdn)
            return

        # Collect PE data
        return self._collect_pe_data(pe_fqdn, pc_fqdn)

    @try_catch_wrapper
    def _collect_pe_data(self, pe_fqdn: str, pc_fqdn: str) -> None:
        """Collect all data for a specific Prism Element.

        Args:
            pe_fqdn: FQDN of the Prism Element
            pc_fqdn: FQDN of the Prism Central managing this PE

        Returns:
            False if service account retrieval fails, None otherwise
        """
        # Step 1: Get basic PE info
        if not self._collect_pe_basic_info(pe_fqdn, pc_fqdn):
            return

        # Step 2: Initialize Prism Element client
        this_pe = self._initialize_pe_client(pe_fqdn, pc_fqdn)
        if not this_pe:
            return

        # Step 3: Collect detailed PE data
        self._collect_pe_detailed_data_with_status(this_pe, pe_fqdn)

        # Step 4: Clean up
        for key in self.collect_result:
            if self.collect_result[key].get("status") == "Updating":
                self.collect_result[key]["status"] = "Unreachable"

    @with_app_context
    @try_catch_wrapper
    def _collect_pe_detailed_data_with_status(self, this_pe: Prism_Element, pe_fqdn: str) -> None:
        """Collect detailed data for a Prism Element and update its status.

        Args:
            this_pe: Initialized Prism_Element object
            pe_fqdn: FQDN of the Prism Element
        """
        if self._collect_pe_detailed_data(this_pe, pe_fqdn):
            self.collect_result[pe_fqdn]["status"] = "Running"
        else:
            self.collect_result[pe_fqdn]["status"] = "Unreachable"
            raise Exception("Failed to get PE data for %s", pe_fqdn)

    @try_catch_wrapper
    def _collect_pe_basic_info(self, pe_fqdn: str, pc_fqdn: str) -> bool:
        """Collect basic information about a Prism Element.

        Args:
            pe_fqdn: FQDN of the Prism Element
            pc_fqdn: FQDN of the Prism Central

        Returns:
            True if successful, False otherwise
        """
        self.get_pe_basic_info(pe_fqdn, pc_fqdn, self.collect_result[pe_fqdn])
        return True

    @with_app_context
    @try_catch_wrapper
    def _initialize_pe_client(self, pe_fqdn: str, pc_fqdn: str) -> Optional[Prism_Element]:
        """Initialize a Prism Element client.

        Args:
            pe_fqdn: FQDN of the Prism Element
            pc_fqdn: FQDN of the Prism Central

        Returns:
            Initialized Prism_Element object or None if initialization fails
        """
        # Use the service account manager directly
        account = self.api.get_service_account(pe_fqdn, pc_fqdn, vault=None)
        if not account:
            return None

        return Prism_Element(
            fqdn=pe_fqdn,
            sa={"username": account["username"], "password": account["secret"]},
            logger=self.logger,
        )

    @try_catch_wrapper
    def _collect_pe_detailed_data(self, this_pe: Prism_Element, pe_fqdn: str) -> bool:
        """Collect all detailed data for a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            pe_fqdn: FQDN of the Prism Element

        Raises:
            Exception: If any data collection fails
        """
        # Collect different types of information
        if not self._collect_core_information(this_pe, pe_fqdn):
            raise Exception("Failed to get core information for %s", pe_fqdn)
        self._collect_additional_information(this_pe, pe_fqdn)
        return True

    @try_catch_wrapper
    def _collect_core_information(self, this_pe: Prism_Element, pe_fqdn: str) -> bool:
        """Collect core information about a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            pe_fqdn: FQDN of the Prism Element
        """
        if not self.get_cluster_info(this_pe, self.collect_result[pe_fqdn]):
            raise Exception("Failed to get cluster info for %s", pe_fqdn)
        self.get_hosts_info(this_pe, self.collect_result[pe_fqdn])
        self.get_config_info(this_pe, self.collect_result[pe_fqdn])
        return True

    @try_catch_wrapper
    def _collect_additional_information(self, this_pe: Prism_Element, pe_fqdn: str) -> None:
        """Collect additional information about a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            pe_fqdn: FQDN of the Prism Element
        """
        self.get_vm_info(this_pe, self.collect_result[pe_fqdn])
        self.get_license_info(this_pe, self.collect_result[pe_fqdn])
        self.get_ssl_info(this_pe, self.collect_result[pe_fqdn])
        self.get_lcm_info(this_pe, self.collect_result[pe_fqdn])
        if not self.warehouse:
            self.get_remote_site_info(this_pe, self.collect_result[pe_fqdn])
            self.get_remote_backup_info(this_pe, self.collect_result[pe_fqdn])

    # ----------------------
    # Data Collection Methods
    # ----------------------

    @try_catch_wrapper
    def get_timezone_info(self, pc_fqdn: str) -> str:
        pc_site = pc_fqdn.split("-")[1]
        if pc_site.startswith("eu"):
            return "UTC+02:00"
        if pc_site.startswith("apac"):
            return "UTC+08:00"
        if pc_site.startswith("china"):
            return "UTC+08:00"
        if pc_site.startswith("na"):
            return "UTC-05:00"
        if pc_site.startswith("russia"):
            return "UTC+04:00"
        if pc_site.startswith("ppe"):
            return "UTC+02:00"
        return "UTC+00:00"

    @try_catch_wrapper
    def get_pe_basic_info(
        self,
        pe_fqdn: str,
        pc_fqdn: str,
        entity_record: Dict[str, Any],
    ) -> None:
        """Extract and store basic information about a Prism Element.

        Args:
            pe_fqdn: FQDN of the Prism Element
            pc_fqdn: FQDN of the Prism Central
            entity_record: Dictionary to store the extracted information

        Raises:
            AssertionError: If PE name doesn't match expected pattern
        """
        with self.lock:
            pe_name = pe_fqdn.split(".")[0].upper()
            entity_record["status"] = "Updating"
            entity_record["prism"] = pc_fqdn
            entity_record["name"] = pe_name
            entity_record["fqdn"] = pe_fqdn.lower()
            # entity_record["bu_type"] = match.group("bu_type")
            entity_record["last_update"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            entity_record["is_central_pe"] = pe_fqdn in self.central_pes
            entity_record["bandwidth_runtime"] = 0.0
            entity_record["timezone"] = self.get_timezone_info(pc_fqdn)

            _, country_code, bu_code = split_pe_into_parts(pe_name)
            entity_record["country_code"] = country_code
            entity_record["site_code"] = bu_code

    @try_catch_wrapper
    def get_cluster_info(
        self,
        this_pe: Prism_Element,
        entity_record: Dict[str, Any],
    ) -> bool:
        """Collect and store cluster information from a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            entity_record: Dictionary to store the extracted information

        Raises:
            AuthenticationError: If authentication fails
            RuntimeError: If API request fails
        """
        pe_response = this_pe.rest.prism_get(version="v1", request_url="/cluster")
        if pe_response.status_code >= 400 and pe_response.status_code < 500:
            raise AuthenticationError(f"Authentication error for {this_pe.fqdn}: {pe_response.status_code}")
        if pe_response.status_code != 200:
            raise RuntimeError(f"Failed to get cluster info for {this_pe.fqdn}, return code: {pe_response.status_code}")
        pe_json = text_to_json(pe_response)
        with self.lock:
            entity_record["aos_version"] = pe_json["version"]
            entity_record["ncc_version"] = pe_json["nccVersion"]
            entity_record["uuid"] = pe_json["uuid"]
            entity_record["total_storage"] = bytes_to_gb(int(pe_json["usageStats"]["storage.capacity_bytes"]))
        return True

    @try_catch_wrapper
    def get_hosts_info(
        self,
        this_pe: Prism_Element,
        entity_record: Dict[str, Any],
    ) -> None:
        """Collect and store host information from a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            entity_record: Dictionary to store the extracted information
        """
        ret_host_list = this_pe.get_host()
        entity_record["node_number"] = 0

        if (
            not ret_host_list
            or "X-Ntnx-Env" in ret_host_list.headers.keys()
            and ret_host_list.headers["X-Ntnx-Env"] != "pe"
        ):
            self.logger.info("Host %s does not indentify itself as a PE", this_pe.fqdn)
            return
        hosts_info = text_to_json(ret_host_list)

        if isinstance(hosts_info, dict) and "entities" in hosts_info:
            ahv_version = hosts_info["entities"][0]["hypervisorFullName"]
        else:
            self.logger.error("ERROR: failed to get hosts info for %s", this_pe.fqdn)
            ahv_version = ""
            return
        with self.lock:
            entity_record["node_number"] = len(hosts_info["entities"])
            entity_record["ahv_version"] = ahv_version

    @try_catch_wrapper
    def get_remote_site_info(
        self,
        this_pe: Prism_Element,
        entity_record: Dict[str, Any],
    ) -> None:
        """Collect and store remote site information from a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            entity_record: Dictionary to store the extracted information
        """
        # get protection domain information
        pd_s = this_pe.get_protection_domains()
        # for the multi nodes PE, it has a protection domain named like "Gold_CCG"
        pd_gold_s = [
            pd
            for pd in pd_s.json()
            if "Gold_CCG" in pd["name"] and self.cache.pe_data.get(this_pe.fqdn, {}).get("name", "") in pd["name"]
        ]
        # for the single node PE, it has a protection domain named like "Silver_CCG"
        pd_silver_s = [
            pd
            for pd in pd_s.json()
            if "Silver_CCG" in pd["name"] and self.cache.pe_data.get(this_pe.fqdn, {}).get("name", "") in pd["name"]
        ]
        # if pd_gold_s, then get remote_site from pd_gold_s[0]['remoteSiteNames'][0], if pd_silver_s, then get it from pd_silver_s[0]['remoteSiteNames'][0]
        if len(pd_gold_s):
            remote_site_name = next(iter(pd_gold_s[0]["remoteSiteNames"]), "")
        elif len(pd_silver_s):
            remote_site_name = next(iter(pd_silver_s[0]["remoteSiteNames"]), "")
        else:
            self.logger.error("ERROR: cannot find any remote sites from protection domain")
            remote_site_name = ""
        # when the remote site name is like "RS_RETHU182-NXC000", we need to remove "RS_" from the name
        remote_site_runtime = remote_site_name.removeprefix("RS_")
        bandwidth_runtime = 0.0
        if not remote_site_name == "":
            remote_site_detail = this_pe.get_remote_site(remote_site_name=remote_site_name)
            bandwidth_runtime = (
                remote_site_detail.json().get("bandwidthPolicy", {}).get("defaultBandwidthLimit") / 1000000
                if remote_site_detail
                else 0
            )
        cached_remote_site_runtime = self.cache.pe_data.get(this_pe.fqdn, {}).get("remote_site_runtime", "")
        with self.lock:
            if remote_site_runtime != cached_remote_site_runtime:
                entity_record["remote_site_runtime"], entity_record["remote_site_last"] = (
                    remote_site_runtime,
                    cached_remote_site_runtime,
                )
            entity_record["bandwidth_runtime"] = bandwidth_runtime

    @try_catch_wrapper
    def get_remote_backup_info(
        self,
        this_pe: Prism_Element,
        entity_record: Dict[str, Any],
    ) -> None:
        """Collect and store remote backup information from a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            entity_record: Dictionary to store the extracted information
        """
        snap_s = this_pe.get_rs_dr_snapshots().json().get("entities")
        rs_snap_s = [
            snap
            for snap in snap_s
            if snap["protectionDomainName"].startswith(this_pe.fqdn.strip(this_pe.domain).strip(".").upper())
        ]
        with self.lock:
            entity_record["remote_backup_number"] = len(rs_snap_s)

    @try_catch_wrapper
    def get_config_info(
        self,
        this_pe: Prism_Element,
        entity_record: Dict[str, Any],
    ) -> None:
        """Collect and store configuration information from a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            entity_record: Dictionary to store the extracted information
        """
        response = this_pe.rest.prism_get(
            version="v1",
            request_url="/utils/entities?entityType=host&projection=memory_size_bytes%2Cnum_cpu_cores",
        )
        content_json = json.loads(response.text)
        with self.lock:
            entity_record["total_memory"], entity_record["total_cpu"] = bytes_to_gb(
                functools.reduce(
                    lambda x, y: int(x) + int(y),
                    [e["memory_size_bytes"] for e in content_json["entities"]],
                )
            ), functools.reduce(
                lambda x, y: int(x) + int(y),
                [e["num_cpu_cores"] for e in content_json["entities"]],
            )

    @try_catch_wrapper
    def get_lcm_info(
        self,
        this_pe: Prism_Element,
        entity_record: Dict[str, Any],
    ) -> None:
        """Collect and store LCM information from a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            entity_record: Dictionary to store the extracted information
        """
        lcm_config = this_pe.get_lcm_config()

        lcm_entities = this_pe.get_lcm_entities()
        cluster_entity = [
            entity
            for entity in lcm_entities
            if entity["entityClass"] == "Core Cluster" and entity["entityModel"] == "Foundation"
        ][0]
        spp_entities = [entity for entity in lcm_entities if entity["entityClass"] == "SPP"]

        with self.lock:
            entity_record["lcm_version"] = lcm_config["semantic_version"]
            entity_record["foundation_version"] = cluster_entity["version"]
            entity_record["spp_version"] = ", ".join(sorted(set(map(lambda x: x["version"], spp_entities))))

    @try_catch_wrapper
    def get_vm_info(
        self,
        this_pe: Prism_Element,
        entity_record: Dict[str, Any],
    ) -> None:
        """Collect and store VM information from a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            entity_record: Dictionary to store the extracted information
        """
        response = this_pe.get_vm()
        vm_data = text_to_json(response)
        with self.lock:
            entity_record["vm_number"] = len(
                [entity for entity in vm_data["entities"] if not entity["vmName"].endswith("CVM")]
            )
            entity_record["cvm_number"] = len(
                [entity for entity in vm_data["entities"] if entity["vmName"].endswith("CVM")]
            )

    @try_catch_wrapper
    def get_ssl_info(
        self,
        this_pe: Prism_Element,
        entity_record: Dict[str, Any],
    ) -> None:
        """Collect and store SSL certificate information from a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            entity_record: Dictionary to store the extracted information

        Raises:
            ValueError: If certificate or expiration date is not found
        """
        ssl_cert = this_pe.get_ssl_cert()
        expiry_date_str = ssl_cert.json().get("expiryDate", "")
        if not expiry_date_str or not isinstance(expiry_date_str, str):
            raise ValueError("No expiration date found")
        cert_expiry_date = _convert_cert_date(expiry_date_str)
        with self.lock:
            entity_record["cert_expiry_date"] = str(cert_expiry_date).split(" ")[0]

    @try_catch_wrapper
    def get_license_info(
        self,
        this_pe: Prism_Element,
        entity_record: Dict[str, Any],
    ) -> None:
        """Collect and store license information from a Prism Element.

        Args:
            this_pe: Initialized Prism_Element object
            entity_record: Dictionary to store the extracted information
        """
        license_response = this_pe.rest.prism_get(
            version="v1",
            request_url="/license?showAllClusters=true",
        )
        license_json = text_to_json(license_response)
        licenses = license_json["licenseInfoDTO"]["pcDetailsDTO"]["licenses"]
        with self.lock:
            entity_record["license_category"] = ", ".join(sorted(set(map(lambda x: x["category"], licenses))))
            entity_record["license_class"] = license_json["licenseInfoDTO"]["licenseConfigurationDetailsDTO"][
                "currentLicenseClass"
            ]
            for license_ in licenses:
                if license_["meter"] == "CORES":
                    entity_record["license_cores_capacity"] = str(int(float(license_["quantity"])))
                elif license_["meter"] == "FLASH":
                    entity_record["license_flash_capacity"] = license_["quantity"] + " Tib"
                elif license_["type"] == "FILE":
                    entity_record["license_hdd_capacity"] = " ".join(
                        [license_["quantity"], license_["meter"].capitalize()]
                    )
            entity_record["license_class"] = ", ".join(sorted(set(map(lambda x: x["type"], licenses))))

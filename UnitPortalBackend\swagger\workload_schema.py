import json
from json import <PERSON><PERSON>NDecodeError

import sqlalchemy
from flask import abort
from marshmallow import Schema, fields, validate, ValidationError, validates_schema, EXCLUDE
from ping3 import ping

from business.authentication.authentication import User<PERSON>ole, ServiceAccount
from business.distributedhosting.nutanix.task_status import TaskStatus
from business.distributedhosting.nutanix.workload.app_package import AppPackage
from business.generic.commonfunc import split_pe_into_parts, get_user_by_token
from business.generic.ipam import Ipam
from business.generic.ipam_exception import IpNotUnderVlan, IpOccupied
from models.ntx_models import ModelPrismCentral
from models.ntx_models_wh import ModelWarehousePrismCentral
from models.workload_models import ModelWorkloadImage, ModelWorkloadTemplate, ModelWorkloadTask, ModelWorkloadNetwork
from static.WORKLOAD_SPEC import WorkloadSpec


def validate_disk_sum(disk):
    if not sum(disk) <= 6 * 1024:
        raise ValidationError("Sum of disk should less than 6T")


class WorkloadSchema(Schema):
    name = fields.Str()
    workload_type = fields.Str(validate=validate.OneOf(["windows", "linux", "network"]))
    cpu = fields.Int(strict=True)
    cpu_core = fields.Int(strict=True)
    memory = fields.Int(strict=True)
    disk = fields.List(
        fields.Int(strict=True, validate=validate.Range(0, 2000)),
        validate=validate_disk_sum,
    )
    # Note: "windows" - "SECURE_BOOT", "linux"/"network"  - "LEGACY"
    # boot_mode = fields.Str(validate=validate.OneOf(["LEGACY", "SECURE_BOOT"]))        # parse from vm spec
    subnet_ip = fields.Str(allow_none=True)
    vlan_id = fields.Int(strict=True, allow_none=True)
    image_id = fields.Int(strict=True)
    app_package = fields.List(fields.Str(), allow_none=True)
    linux_payload = fields.Str(allow_none=True)

    update_dns = fields.Boolean(default=True)

    class Meta:
        unknown = EXCLUDE

    def validate_cpu(self, data):
        if data[WorkloadSpec.CPU] * data[WorkloadSpec.CPU_CORE] > 16:
            raise ValidationError("CPU sum (cpu * cpu_core) must be less than 16!")

    # @validates_schema
    # def validate_workload_type_and_boot_mode(self, data, **kwargs):
    #     if data.get(WorkloadSpec.TEMPLATE_ID):
    #         return
    #     workload_type = data[WorkloadSpec.WORKLOAD_TYPE]
    #     boot_mode = data[WorkloadSpec.BOOT_MODE]
    #     if not (workload_type == "windows" and boot_mode == "SECURE_BOOT") \
    #             and not (workload_type == "linux" and boot_mode == "LEGACY") \
    #             and not (workload_type == 'network' and boot_mode == "LEGACY"):
    #         raise ValidationError("Workload type and boot type doesn't match!")

    def validate_image_existence(self, data):
        """Validate if image exists in database, should be called before `validate_image_and_workload_type`"""
        image = ModelWorkloadImage().get_image_by_id(image_id=int(data[WorkloadSpec.IMAGE_ID]))
        if not image:
            raise ValidationError(f"Image id {data[WorkloadSpec.IMAGE_ID]} doesn't exist in database!")

    def validate_image_and_workload_type(self, data):
        image = ModelWorkloadImage().get_image_by_id(image_id=int(data[WorkloadSpec.IMAGE_ID]))
        image_os_type = image.os_type
        workload_type = data[WorkloadSpec.WORKLOAD_TYPE]
        if workload_type != image_os_type:
            raise ValidationError(f"Workload type ({workload_type}) and image os type ({image_os_type}) doesn't match!")

    def validate_supported_packages(self, data):
        supported_packages = AppPackage.supported_packages
        pkgs = data.get(WorkloadSpec.PACKAGES)
        unsupported_packages = [p for p in pkgs if p and p not in supported_packages]
        if unsupported_packages:
            raise ValidationError(f"Following packages installation are not supported yet: {unsupported_packages}")

    def validate_linux_payload_json(self, data):
        try:
            json.loads(data.get(WorkloadSpec.LINUX_PAYLOAD))
        except JSONDecodeError:
            raise ValidationError(f"'{WorkloadSpec.LINUX_PAYLOAD}' is not a valid json!")

    def validate_template_existence(self, data):
        template_id = data.get(WorkloadSpec.TEMPLATE_ID)
        try:
            ModelWorkloadTemplate().get_template_by_id(template_id)
        except (sqlalchemy.exc.NoResultFound, sqlalchemy.exc.MultipleResultsFound) as e:
            raise ValidationError(f"Search template id '{template_id}' in database failed! Error: {e}")

    def validate_vlan_id_existence_in_db(self, data):
        vlan_id = data.get(WorkloadSpec.VLAN_ID)
        try:
            ModelWorkloadNetwork().query.filter_by(vlan_id=vlan_id).one()
        except (sqlalchemy.exc.NoResultFound, sqlalchemy.exc.MultipleResultsFound):
            raise ValidationError(f"Can't find network with vlan_id {vlan_id} in database!! Please contact administrator.")


class CreateWorkloadRequestSchema(WorkloadSchema):
    template_id = fields.Int()
    pc = fields.Str(required=True)
    pe = fields.Str(required=True)
    # Note: "windows" - "SECURE_BOOT", "linux"/"network"  - "LEGACY"
    # boot_mode = fields.Str(validate=validate.OneOf(["LEGACY", "SECURE_BOOT"]))
    skip_sizing = fields.Boolean(default=False)
    user_specified_ip = fields.Str(allow_none=True, validate=validate.Regexp(
        r"^$|^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])$",
        error="Invalid IP address")
    )

    class Meta:
        unknown = EXCLUDE

    def validate_mandatory_inputs_for_custom(self, data):
        # if template_id is provided, use template to create
        # TODO: check if user has access to create vm by custom setting
        required_fields = [     # Exclude pc, pe
            WorkloadSpec.VM_NAME, WorkloadSpec.WORKLOAD_TYPE, WorkloadSpec.CPU, WorkloadSpec.CPU_CORE,
            WorkloadSpec.MEMORY, WorkloadSpec.DISK, WorkloadSpec.VLAN_ID,
            WorkloadSpec.IMAGE_ID
        ]
        if data.get(WorkloadSpec.WORKLOAD_TYPE) == "linux":
            required_fields.append(WorkloadSpec.LINUX_PAYLOAD)
        missing_fields = [field for field in required_fields if data.get(field) is None]
        if missing_fields:
            raise ValidationError(f"Following fields are required when template_id is not provided: {missing_fields}")

    def validate_subnet_ip(self, data, rest_ipam):
        try:
            rest_ipam.check_ip_is_under_vlan(data.get(WorkloadSpec.SUBNET_IP), data.get(WorkloadSpec.VLAN_ID))
        except IpNotUnderVlan as e:
            raise ValidationError(e.msg)

    def validate_user_specified_ip_availability(self, data, rest_ipam):
        user_specified_ip = data.get(WorkloadSpec.USER_SPECIFIED_IP)
        try:
            rest_ipam.check_ip_is_under_vlan(user_specified_ip, data.get(WorkloadSpec.VLAN_ID))
        except IpNotUnderVlan as e:
            raise ValidationError(e.msg)
        try:
            rest_ipam.check_ip_is_occupied(user_specified_ip)
        except IpOccupied as e:
            raise ValidationError(e.msg)
        if ping(user_specified_ip):
            raise ValidationError(f"IP {user_specified_ip} is pingable, please check if it's already in use.")

    def validate_vm_name_for_sapp(self, data):
        # For SAPP, vm name should convert to 5 digit
        if not data.get(WorkloadSpec.VM_NAME):
            return
        bu, country_code, bu_code = split_pe_into_parts(data[WorkloadSpec.PE], 5)
        expected_prefix = f"{bu}{country_code}{bu_code}"
        if not data[WorkloadSpec.VM_NAME].startswith(expected_prefix):
            raise ValidationError(f"For SAPP, VM name's prefix should be converted to {expected_prefix}!")

    def validate_rbac_template(self, data, user_privileges):
        """Check if user has access to create vm by template_id."""
        required_template_id = data.get(WorkloadSpec.TEMPLATE_ID)
        if user_privileges["role_mkt"]["create_wl"] == "full":
            return
        template_ids = user_privileges["role_mkt"]["create_wl_scope"].get("template_ids")      # Attention: "template_id_list" not "template_ids"
        if not template_ids or required_template_id not in template_ids:
            abort(403, f"You are not allowed to create vm by template '{required_template_id}'.")

    def validate_rbac_custom(self, data, user_privileges):
        """Check if user has access to create vm by custom setting."""
        if user_privileges["role_mkt"]["create_wl"] == "full":
            return
        custom_settings = user_privileges["role_mkt"]["create_wl_scope"].get("custom_settings")
        if not custom_settings:
            abort(403, "User is not allowed to create vm by custom setting.")
        if custom_settings == "all":
            return
        for vm_name_pattern in custom_settings:
            if vm_name_pattern in data[WorkloadSpec.VM_NAME]:
                return
        abort(403, f"User is not allowed to create vm with name {data.get(WorkloadSpec.VM_NAME)}. Allowed name pattern: {custom_settings}")

    def validate_skip_sizing_permission(self, user_privileges):
        if user_privileges["role_mkt"]["create_wl"] == "full":
            return
        if not user_privileges["role_mkt"]["create_wl_scope"].get("skip_sizing"):
            abort(403, "You are not allowed to skip sizing. ^_^")

    def _init_ntx_sa(self, data):
        try:
            pc_info = ModelPrismCentral.query.filter_by(fqdn=data[WorkloadSpec.PC]).one()
            self.facility_type = "retail"
        except sqlalchemy.exc.NoResultFound:
            try:
                pc_info = ModelWarehousePrismCentral.query.filter_by(fqdn=data[WorkloadSpec.PC]).one()
                self.facility_type = "warehouse"
            except sqlalchemy.exc.NoResultFound:
                raise Exception(f"Can't find PC '{data[WorkloadSpec.PC]}' neither in Retail nor in Warehouse!")
        return ServiceAccount(usage=pc_info.service_account).get_service_account()

    @validates_schema
    def validate(self, data, **kwargs):     # pylint: disable=unused-argument
        user = get_user_by_token()
        ntx_sa = self._init_ntx_sa(data)
        rest_ipam = Ipam(sa=ntx_sa)
        user_privileges = UserRole(user.id).get_all_privileges()
        if data.get(WorkloadSpec.SKIP_SIZING):
            self.validate_skip_sizing_permission(user_privileges)
        if data.get(WorkloadSpec.TEMPLATE_ID):
            self.validate_rbac_template(data, user_privileges)
            self.validate_template_existence(data)
        else:
            self.validate_rbac_custom(data, user_privileges)
            self.validate_cpu(data)
            self.validate_mandatory_inputs_for_custom(data)
            self.validate_image_existence(data)
            self.validate_image_and_workload_type(data)
            # self.validate_vlan_id_existence_in_db(data)
            if data.get(WorkloadSpec.SUBNET_IP):
                self.validate_subnet_ip(data, rest_ipam)
            if data.get(WorkloadSpec.USER_SPECIFIED_IP):
                self.validate_user_specified_ip_availability(data, rest_ipam)
            if data.get(WorkloadSpec.PACKAGES):
                self.validate_supported_packages(data)
                # if 'sapp_app' in data.get(WorkloadSpec.PACKAGES):
                #     self.validate_vm_name_for_sapp(data)
            if data.get(WorkloadSpec.LINUX_PAYLOAD):
                self.validate_linux_payload_json(data)


class CleanupWorkloadRequestSchema(Schema):
    task_id = fields.Int(required=True)
    force = fields.Boolean()

    @validates_schema
    def validate_task_id(self, data, **kwargs):     # pylint: disable=unused-argument
        task_id = data.get("task_id")
        task = ModelWorkloadTask.query.filter_by(id=task_id, task_type="CREATE_WORKLOAD").first()
        if not task:
            raise ValidationError(f"Can't find task to cleanup with id '{task_id}' in database!")
        if task.status in [TaskStatus.DONE, TaskStatus.CLEANUP_DONE]:
            raise ValidationError(
                f"Current task is in '{task.status}' status, unable to trigger CLEAN_UP action!")



class DeleteWorkloadRequestSchema(Schema):
    name = fields.Str(required=True)     # e.g. RETCN888-LX8888  (no ".ikea.com")
    pe = fields.Str(required=True)

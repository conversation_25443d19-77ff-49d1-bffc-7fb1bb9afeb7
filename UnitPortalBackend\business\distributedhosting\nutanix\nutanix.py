# installed module
import base64
import inspect
import json
import logging
import re
import traceback
import sys
import time
import functools
from typing import List
import requests
import datetime
import os
import paramiko
import sqlalchemy

from flask import jsonify
from jinja2 import Template
from sqlalchemy import and_, func
import werkzeug.exceptions as flaskex
from business.distributedhosting.facility_type import FacilityType
from business.distributedhosting.nutanix.task_status import SeamlessLcmPlannedPEStatus
# local file
import static.SETTINGS as SETTING
from base_path import application_path
from business.authentication.authentication import ServiceAccount, Vault
from business.generic.thors_hammer_api import ThorsH<PERSON>merAPI
from business.generic.ipam import Ipam
from business.generic.commonfunc import NutanixAPI, CommonRestCall, convert_GiB_to_bytes, SSHConnect, Redfish, \
    test_pinging, NutanixRest, CommonHelper
from models.atm_models import ModelNutanixSeamlessLcmPlannedPEs
from models.database import db
from business.distributedhosting.simplivity.simplivity import SimplivityvCenter
from business.distributedhosting.nutanix.protection_domain import ProtectionDomain
from models.ntx_models import ModelRetailNutanixHostSchema, ModelRetailNutanixHost, ModelPrismCentralSchema, \
    ModelPrismCentral, ModelPrismElementSchema, ModelPrismElement, ModelRetailNutanixVMSchema, ModelRetailNutanixVM, ModelNTXMOVELog, \
        ModelNTXMOVELogSchema, ModelRetailMOVETask, ModelRetailMOVETaskSchema
from models.sli_models import ModelSLIClusterSchema, ModelSLICluster
from models.ntx_models_wh import  ModelWHMOVETask, ModelWHNTXMOVELog, ModelWHNTXMOVELogSchema, ModelWHNTXMOVE, \
    ModelWHNTXMOVESchema, ModelWarehousePrismElement, ModelWarehousePrismElementSchema, ModelWarehousePrismCentral, \
    ModelWarehousePrismCentralSchema, ModelWarehouseVmwareVMs, ModelWarehouseNutanixVMSchema, ModelWarehouseNutanixVM, ModelWarehouseNutanixHostSchema, ModelWarehouseNutanixHost
from static.WORKLOAD_SPEC import WorkloadSpec
from io import StringIO
from business.generic.base_up_exception import SSHFailed


class Prism():
    def __init__(self, fqdn, sa=None, logger=logging, domain='ikea.com'):
        self.sa = sa if sa else ServiceAccount(usage="nutanix_pm").get_service_account()
        _pat = re.compile(r'(ikea|ikeadt|ikead2)\.com', re.IGNORECASE)  # match ikea.com / ikeadt.com ,ingore case
        try:
            self.domain = re.search(_pat, fqdn).group()
            self.fqdn = fqdn
        except Exception:
            self.domain = domain
            self.fqdn = f"{fqdn}.{domain}"
        self.endpoint = f"{self.fqdn}:9440"
        self.logger = logger if logger else logging
        self.rest = NutanixRest(fqdn=self.fqdn, username=self.sa['username'], password=self.sa['password'], logger=self.logger)

    def get_cluster(self):
        self.logger.info(f"Get cluster details from {self.fqdn}")
        return self.rest.prism_get(request_url="/cluster")

    def get_name_server(self):
        self.logger.info(f"Get the list of name servers from {self.fqdn}")
        return self.rest.prism_get(request_url="/cluster/name_servers")

    def get_ntp_server(self):
        self.logger.info(f"Get the list of NTP servers from {self.fqdn}")
        return self.rest.prism_get(request_url="/cluster/ntp_servers")

    def get_ssl_cert(self):
        self.logger.info(f"Get SSL certificate from {self.fqdn}")
        return self.rest.prism_get(request_url="/keys/pem")

    def upload_ssl_cert(self, keytype="RSA_2048", keyfile=None, certfile=None, chainfile=None):
        self.logger.info(f"Upload SSL certificate to {self.fqdn}")
        try:
            with open(keyfile, "rb") as key, open(certfile, "rb") as cert, open(chainfile, "rb") as chain:
                files = {
                    "keyType": keytype,
                    "key": key,
                    "cert": cert,
                    "caChain": chain
                }
                return self.rest.prism_post(request_url="/keys/pem/import", files=files)
        except FileNotFoundError as e:
            raise flaskex.BadRequest(f"Error occurred when reading file. Error message: {str(e)}")

    def get_host(self, uuid=None):
        if uuid:
            self.logger.info(f"Get an existing host from {self.fqdn}")
            data = self.rest.prism_get(request_url=f"/hosts/{uuid}")
        else:
            self.logger.info(f"Get a list of existing hosts from {self.fqdn}")
            data = self.rest.prism_get(request_url="/hosts")
        return data

    def get_subnet(self, uuid=None, filter=None):
        if uuid:
            self.logger.info(f"Get an existing subnet from {self.fqdn}")
            return self.rest.prism_get(version="v3", request_url=f"/subnets/{uuid}")
        self.logger.info(f"Get a list of existing subnets from {self.fqdn}")
        payload = {
            'kind': "subnet",
            'offset': 0,
            'length': 500,
            'sort_order': "ASCENDING",
            'sort_attribute': "name"
        }
        if filter:
            self.logger.info(f"Filter by {filter}")
            payload['filter'] = filter
        subnets = []
        page = 1
        while True:
            self.logger.info(f"Loading page {page}")
            data = self.rest.prism_post(version="v3", request_url="/subnets/list", payload=payload)
            self.logger.info(f"Got {len(data.json().get('entities'))} subnets in page {page}")
            subnets += data.json().get('entities')
            payload['offset'] += 500
            page += 1
            if len(data.json().get('entities')) < payload['length']:
                self.logger.info("It is the last page, break from pagination")
                break
        return subnets

    def get_image(self, uuid=None, filter=None):
        if uuid:
            self.logger.info(f"Get an existing image from {self.fqdn}")
            return self.rest.prism_get(version="v3", request_url=f"/images/{uuid}")
        self.logger.info(f"Get a list of existing images from {self.fqdn}")
        payload = {
            'kind': "image",
            'offset': 0,
            'length': 500,
            'sort_order': "ASCENDING",
            'sort_attribute': "name"
        }
        if filter:
            self.logger.info(f"Filter by {filter}")
            payload['filter'] = filter
        images = []
        page = 1
        while True:
            self.logger.info(f"Loading page {page}")
            data = self.rest.prism_post(version="v3", request_url="/images/list", payload=payload)
            self.logger.info(f"Got {len(data.json().get('entities'))} images in page {page}")
            images += data.json().get('entities')
            payload['offset'] += 500
            page += 1
            if len(data.json().get('entities')) < payload['length']:
                self.logger.info("It is the last page, break from pagination")
                break
        return images

    def get_task(self, uuid=None, filter=None):
        # An example of how to use filter is: filter="(operation_type==kLcmRootTask;status==kRunning)", it stands for a running LCM root task
        if uuid:
            self.logger.info(f"Get an existing task from {self.fqdn}")
            return self.rest.prism_get(version="v3", request_url=f"/tasks/{uuid}")
        self.logger.info(f"Get a list of existing tasks from {self.fqdn}")
        payload = {
            'kind': "task",
            'offset': 0,
            'length': 500,
        }
        if filter:
            self.logger.info(f"Filter by {filter}")
            payload['filter'] = filter
        tasks = []
        page = 1
        while True:
            self.logger.info(f"Loading page {page}")
            data = self.rest.prism_post(version="v3", request_url="/tasks/list", payload=payload)
            self.logger.info(f"Got {len(data.json().get('entities'))} tasks in page {page}")
            tasks += data.json().get('entities')
            payload['offset'] += 500
            page += 1
            if len(data.json().get('entities')) < payload['length']:
                self.logger.info("It is the last page, break from pagination")
                break
        return tasks

    def get_progress(self, uuid):
        self.logger.info(f"Get the progress of task {uuid} from {self.fqdn}")
        return self.rest.prism_get(request_url=f"/progress_monitors?filterCriteria=parent_task_uuid=={uuid}")

    def get_public_key(self, name=None):
        if name:
            self.logger.info("Get an existing public key")
            data = self.rest.prism_get(request_url=f"/cluster/public_keys/{name}")
        else:
            self.logger.info("Get a list of existing public keys")
            data = self.rest.prism_get(request_url="/cluster/public_keys")
        return data

    def get_vm(self, uuid=None):
        #get vms (cvm + user vms) through API v1
        if uuid:
            self.logger.info(f"Get an existing VM from {self.fqdn}")
            data = self.rest.prism_get(request_url=f"/vms/{uuid}")
        else:
            self.logger.info(f"Get a list of existing VMs from {self.fqdn}")
            data = self.rest.prism_get(request_url="/vms")
        return data

    def update_vm(self, uuid=None, payload=None):
        self.logger.info(f"Update the VM with UUID is {uuid} from {self.fqdn}")
        return self.rest.prism_put(request_url=f"/vms/{uuid}", payload=payload)

    def get_role(self, uuid=None, filter=None):
        if uuid:
            self.logger.info(f"Get a role from {self.fqdn}")
            return self.rest.prism_get(version="v3", request_url=f"/roles/{uuid}")
        self.logger.info("List the roles")
        payload = {
            'kind': "role",
            'offset': 0,
            'length': 500
        }
        if filter:
            self.logger.info(f"Filter by {filter}")
            payload['filter'] = filter
        roles = []
        page = 1
        while True:
            self.logger.info(f"Loading page {page}")
            data = self.rest.prism_post(version="v3", request_url="/roles/list", payload=payload)
            self.logger.info(f"Got {len(data.json().get('entities'))} roles in page {page}")
            roles += data.json().get('entities')
            payload['offset'] += 500
            page += 1
            if len(data.json().get('entities')) < payload['length']:
                self.logger.info("It is the last page, break from pagination")
                break
        return roles

    def get_acp(self, uuid=None, filter=None):
        if uuid:
            self.logger.info(f"Get an existing access control policy from {self.fqdn}")
            return self.rest.prism_get(version="v3", request_url=f"/access_control_policies/{uuid}")
        self.logger.info(f"Get a list of existing access control policies from {self.fqdn}")
        payload = {
            'kind': "access_control_policy",
            'offset': 0,
            'length': 500,
        }
        if filter:
            self.logger.info(f"Filter by {filter}")
            payload['filter'] = filter
        acps = []
        page = 1
        while True:
            self.logger.info(f"Loading page {page}")
            data = self.rest.prism_post(version="v3", request_url="/access_control_policies/list", payload=payload)
            self.logger.info(f"Got {len(data.json().get('entities'))} access control policies in page {page}")
            acps += data.json().get('entities')
            payload['offset'] += 500
            page += 1
            if len(data.json().get('entities')) < payload['length']:
                self.logger.info("It is the last page, break from pagination")
                break
        return acps

    def get_user_group(self, uuid=None, filter=None):
        if uuid:
            self.logger.info(f"Get an existing user group from {self.fqdn}")
            return self.rest.prism_get(version="v3", request_url=f"/user_groups/{uuid}")
        self.logger.info(f"Get a list of existing user groups from {self.fqdn}")
        payload = {
            'kind': "user_group",
            'offset': 0,
            'length': 500,
        }
        if filter:
            self.logger.info(f"Filter by {filter}")
            payload['filter'] = filter
        user_groups = []
        page = 1
        while True:
            self.logger.info(f"Loading page {page}")
            data = self.rest.prism_post(version="v3", request_url="/user_groups/list", payload=payload)
            self.logger.info(f"Got {len(data.json().get('entities'))} user groups in page {page}")
            user_groups += data.json().get('entities')
            payload['offset'] += 500
            page += 1
            if len(data.json().get('entities')) < payload['length']:
                self.logger.info("It is the last page, break from pagination")
                break
        return user_groups

    def create_acp(self, name, description, role, groups, vm_uuid_list=[]):
        self.logger.info(f"Create a new access control policy in {self.fqdn}")
        payload = {
            "spec" : {
                "name" : name,
                "description" : description,
                "resources" : {
                    "role_reference" : {
                        "kind" : "role",
                        "name" : role['name'],
                        "uuid" : role['uuid']
                    },
                    "filter_list" : {
                        "context_list" : [
                            {
                                "entity_filter_expression_list" : [
                                    {
                                        "operator" : "IN",
                                        "left_hand_side" : {"entity_type" : "ALL"},
                                        "right_hand_side" : {"collection" : "SELF_OWNED"}
                                    }
                                ],
                                "scope_filter_expression_list" : []
                            },
                            {
                                    "entity_filter_expression_list" : [
                                    {
                                        "operator" : "IN",
                                        "left_hand_side" : {"entity_type" : "vm"},
                                        "right_hand_side" : {"uuid_list" : [vm_uuid_list] if not isinstance(vm_uuid_list, list) else vm_uuid_list}
                                    }
                                ],
                                "scope_filter_expression_list" : []
                            }]
                    },
                    "user_group_reference_list" : [
                        {
                            "kind" : "user_group",
                            "name" : ug['name'],
                            "uuid" : ug['uuid']
                        } for ug in groups
                    ]
                }
            },
            "api_version" : "3.1.0",
            "metadata" : {
                "kind" : "access_control_policy",
                "spec_version" : 0
            }
        }
        return self.rest.prism_post(version="v3", request_url="/access_control_policies", payload=payload)

    def set_lcm_darksite(self, darksite_url=None, auto_inventory=False, auto_time="03:00"): #e.g. darksite_url=http://xxxx/release
        self.logger.info(f"Set LCM dark site in {self.fqdn}")
        hash_payload = {
            ".oid": "LifeCycleManager",
            ".method": "lcm_framework_rpc",
            ".kwargs": {
                "method_class": "LcmFramework",
                "method": "configure"
            }
        }
        if auto_inventory:
            hash_payload[".kwargs"]["args"] = [darksite_url, True, auto_time, None, None, None, None, True, None]
        else:
            hash_payload[".kwargs"]["args"] = [darksite_url, False, auto_time, None, None, None, None, True, None]
        return self.rest.genesis_post(hash_payload=hash_payload)

    def perform_inventory(self, darksite_url=None): #e.g. darksite_url=http://xxxx/release
        self.logger.info(f"Perform inventory on {self.fqdn}")
        hash_payload = {
            ".oid": "LifeCycleManager",
            ".method": "lcm_framework_rpc",
            ".kwargs": {
                "method_class": "LcmFramework",
                "method": "perform_inventory",
                "args": [darksite_url]
            }
        }
        data = self.rest.genesis_post(hash_payload=hash_payload)
        return json.loads(data.json().get('value')).get('.return')
        # return the task uuid
        # json.loads(data.json().get('value')).get('.return')

    def perform_inventory_v4(self):
        self.logger.info(f"Perform inventory on {self.fqdn}")
        data = self.rest.lcm_post(version="v4", request_url="/operations/$actions/performInventory", payload=None)
        return json.loads(data.text)['data']['extId']


    def get_lcm_config(self):
        self.logger.info(f"Get LCM configuration from {self.fqdn}")
        hash_payload = {
            ".oid": "LifeCycleManager",
            ".method": "lcm_framework_rpc",
            ".kwargs": {
                "method_class": "LcmFramework",
                "method": "get_config"
            }
        }
        data = self.rest.genesis_post(hash_payload=hash_payload)
        return json.loads(data.json().get('value')).get('.return')
        # json.loads(data.json().get('value')).get('.return')

    def get_lcm_entities(self, version="v4"):
        if version == "v1":
            self.logger.info(f"Get LCM entities from {self.fqdn} through v1 API")
            payload = {
                "offset": 0,
            }
            data = self.rest.lcm_post(version="v1", request_url="/resources/entities/list", payload=payload)
            return json.loads(data.json().get('entities'))
        self.logger.info(f"Get LCM entities from {self.fqdn} through v4 API")
        entities = []
        page = 1
        while True:
            self.logger.info(f"We're now loading page '{page}' from '{self.fqdn}'")
            data = self.rest.lcm_get(version="v4", request_url=f"/resources/entities?page={page - 1}&limit=100")
            self.logger.info(f"We found {len(data.json().get('data', []))} objects on page '{page}' from '{self.fqdn}'")
            entities += data.json().get('data', [])
            page += 1
            if len(data.json().get('data', [])) < 100:
                break
        return entities


    def perform_lcm_update(self, entities):
        self.logger.info(f"Perform LCM update on {self.fqdn}")
        payload = {
            "$reserved": {
                "$fqObjectType": "lcm.v4.r0.a1.common.UpdateSpec"
            },
            "$objectType": "lcm.v4.common.UpdateSpec",
            "$unknownFields": {},
            "entityUpdateSpecs": [
                {
                    "version": entity['version'],
                    "entityUuid": entity['entity_uuid'],
                    "$reserved": {
                        "$fqObjectType": "lcm.v4.r0.a1.common.EntityUpdateSpec"
                    },
                    "$objectType": "lcm.v4.common.EntityUpdateSpec",
                    "$unknownFields": {}
                }
                for entity in entities
            ]
        }
        return self.rest.lcm_post(version="v4", request_url="/operations/$actions/performUpdate", payload=payload)

    def get_lcm_software(self, filter_key="entity_model", filter_value="all"):
        # filter_key options: uuid, entity_model, version
        # filter_value options for entity_model: Flow Network Security PE, Foundation, Foundation Platforms, AHV hypervisor, AOS_CVE_PATCH, Licensing, Cluster Maintenance Utilities, Security AOS, FSM, AOS
        self.logger.info(f"Get LCM software from {self.fqdn}")
        payload = {
            'value': '{".oid":"LifeCycleManager",".method":"lcm_framework_rpc", \
                ".kwargs":{"method_class":"LcmFramework","method":"v3_group_api", \
                    "args":["{\\\"entity_type\\\":\\\"lcm_entity_v3\\\", \\\"group_member_count\\\":500,\
                        \\\"group_member_attributes\\\": [{\\\"attribute\\\":\\\"uuid\\\"},\
                        {\\\"attribute\\\":\\\"entity_model\\\"}, \
                        {\\\"attribute\\\":\\\"version\\\"}, \
                        {\\\"attribute\\\":\\\"location_id\\\"}], \
                        \\\"query_name\\\":\\\"lcm:EntityGroupModel\\\", \
                        \\\"filter_criteria\\\":\\\"entity_type==software\\\"}\"]}}'
        }
        data = self.rest.genesis_post(payload=payload)
        data = json.loads(data.json().get('value')).get('.return')
        if not data['group_results']:
            return None
        result = list()
        for _software in data['group_results'][0]['entity_results']:
            entity = {
                "entity_id": _software['entity_id']
            }
            for _data in _software['data']:
                if not _data['values']:
                    entity[_data['name']] = "N/A"
                else:
                    entity[_data['name']] = ",".join(_data['values'][0]['values'])
            result.append(entity)
        if filter_value == "all":
            return result
        result = [entity for entity in result if entity[filter_key] == filter_value]
        if result:
            return result
        return None

    def get_lcm_firmware(self, filter_key="entity_model", filter_value="all"):
        # filter_key options: entity_id, uuid, entity_model, version, location_id
        # filter_value options for entity_model: DX360 GEN10 PLUS
        self.logger.info(f"Get LCM firmware from {self.fqdn}")
        payload = {
            'value': '{".oid":"LifeCycleManager",".method":"lcm_framework_rpc", \
                ".kwargs":{"method_class":"LcmFramework","method":"v3_group_api", \
                    "args":["{\\\"entity_type\\\":\\\"lcm_entity_v3\\\",\\\"group_member_count\\\":500, \
                        \\\"group_member_attributes\\\":[{\\\"attribute\\\":\\\"uuid\\\"}, \
                        {\\\"attribute\\\":\\\"entity_model\\\"}, \
                        {\\\"attribute\\\":\\\"version\\\"}, \
                        {\\\"attribute\\\":\\\"location_id\\\"}], \
                        \\\"query_name\\\":\\\"lcm:EntityGroupModel\\\", \
                        \\\"filter_criteria\\\":\\\"entity_type==firmware\\\"}\"]}}'
        }
        data = self.rest.genesis_post(payload=payload)
        data = json.loads(data.json().get('value')).get('.return')
        if not data['group_results']:
            return None
        result = list()
        for _firmware in data['group_results'][0]['entity_results']:
            entity = {
                "entity_id": _firmware['entity_id']
            }
            for _data in _firmware['data']:
                if not _data['values']:
                    entity[_data['name']] = "N/A"
                else:
                    entity[_data['name']] = ",".join(_data['values'][0]['values'])
            result.append(entity)
        if filter_value == "all":
            return result
        result = [entity for entity in result if entity[filter_key] == filter_value]
        if result:
            return result
        return None

    def get_lcm_inventory(self, filter_key="entity_model", filter_value="all"):
        # filter_key options: uuid, entity_type, entity_model, version, entity_id, uuid, location_id
        # filter_value options for entity_type: software, firmware
        # fitler_value options for entity_model (when entity_type==software): Flow Network Security PE, Foundation,
        # Foundation Platforms, AHV hypervisor, AOS_CVE_PATCH, Licensing, Cluster Maintenance Utilities, Security AOS, FSM, AOS
        # filter_value options for entity_model (when entity_type==firmware): DX360 GEN10 PLUS
        self.logger.info(f"Get LCM inventory from {self.fqdn}")
        payload = {
            'value': '{".oid":"LifeCycleManager",".method":"lcm_framework_rpc", \
                ".kwargs":{"method_class":"LcmFramework","method":"v3_group_api", \
                    "args":["{\\\"entity_type\\\":\\\"lcm_entity_v3\\\",\\\"group_member_count\\\":500, \
                        \\\"group_member_attributes\\\":[{\\\"attribute\\\":\\\"uuid\\\"}, \
                        {\\\"attribute\\\":\\\"entity_type\\\"}, \
                        {\\\"attribute\\\":\\\"entity_model\\\"}, \
                        {\\\"attribute\\\":\\\"version\\\"}, \
                        {\\\"attribute\\\":\\\"location_id\\\"}], \
                        \\\"query_name\\\":\\\"lcm:EntityGroupModel\\\"}\"]}}'
        }
        data = self.rest.genesis_post(payload=payload)
        data = json.loads(data.json().get('value')).get('.return')
        if not data['group_results']:
            return None
        result = list()
        for entity_result in data['group_results'][0]['entity_results']:
            entity = {
                "entity_id": entity_result['entity_id']
            }
            for _data in entity_result['data']:
                if not _data['values']:
                    entity[_data['name']] = "N/A"
                else:
                    entity[_data['name']] = ",".join(_data['values'][0]['values'])
            result.append(entity)
        if filter_value == "all":
            return result
        result = [entity for entity in result if entity[filter_key] == filter_value]
        if result:
            return result
        return None

    def get_lcm_updates(self, filter_key="entity_model", filter_value="all"):
        # filter_key options: entity_id, uuid, entity_uuid, entity_class, status, version, entity_model
        # filter_value options for entity_class: Cluster Service, Core Cluster, Hypervisor
        # filter_value options for status: available, recomended, LTS
        # filter_value options for entity_model: Foundation Platforms, AOS, AHV hypervisor
        self.logger.info(f"Get LCM updates from {self.fqdn}")
        payload = {
            "entity_type": "lcm_available_version_v3",
            "group_member_count": 500,
            "query_name": "lcm:EntityGroupModel",
            "filter_criteria": "_master_cluster_uuid_==[no_val]",
            "group_member_attributes": [
                {"attribute": "entity_uuid"},
                {"attribute": "uuid"},
                {"attribute": "entity_class"},
                {"attribute": "status"},
                {"attribute": "version"},
                {"attribute": "entity_model"}
            ]
        }
        data = self.rest.prism_post(version="v3", request_url="/groups", payload=payload)
        if not data.json()['group_results']:
            return None
        result = list()
        for _comp in data.json()['group_results'][0]['entity_results']:
            entity = {
                "entity_id": _comp['entity_id']
            }
            for _data in _comp['data']:
                if not _data['values']:
                    entity[_data['name']] = "N/A"
                else:
                    entity[_data['name']] = ",".join(_data['values'][0]['values'])
            result.append(entity)
        if filter_value == "all":
            return result
        result = [entity for entity in result if entity[filter_key] == filter_value]
        if result:
            return result
        return None

    def get_lcm_progress(self, max_try=3):
        self.logger.info(f"Get LCM progress form {self.fqdn}")
        hash_payload = {
            ".oid": "LifeCycleManager",
            ".method": "lcm_framework_rpc",
            ".kwargs": {
                "method_class": "LcmFramework",
                "method": "is_lcm_operation_in_progress"
            }
        }
        data = self.rest.genesis_post(hash_payload=hash_payload)
        max_try -= 1
        if result := json.loads(data.json().get('value')).get('.return'):
            return result
        if max_try != 0:
            self.logger.warning(f"The return value {json.loads(data.json().get('value'))} is invalid, re-try in 1 minute")
            time.sleep(60)
            return self.get_lcm_progress(max_try=max_try)
        self.logger.info("Reached max try times, quit.")
        return None

    def get_remote_sites(self, proxy_cluster_uuid=None):
        self.logger.info(f"Get the list of remote sites form {self.fqdn}")
        if proxy_cluster_uuid:
            return self.rest.prism_get(version="v1", request_url=f"/remote_sites?proxy_cluster_uuid={proxy_cluster_uuid}")
        return self.rest.prism_get(version="v1", request_url="/remote_sites")

    def get_remote_site(self, remote_site_name=None, proxy_cluster_uuid=None):
        self.logger.info(f"Get the remote site {remote_site_name} from {self.fqdn}")
        if proxy_cluster_uuid:
            return self.rest.prism_get(version="v1", request_url=f"/remote_sites/{remote_site_name}?proxy_cluster_uuid={proxy_cluster_uuid}")
        return self.rest.prism_get(version="v1", request_url=f"/remote_sites/{remote_site_name}")

    def get_protection_domains(self, proxy_cluster_uuid=None):
        self.logger.info(f"Get the list of existing protection domains from {self.fqdn}")
        if proxy_cluster_uuid:
            return self.rest.prism_get(version="v1", request_url=f"/protection_domains?proxy_cluster_uuid={proxy_cluster_uuid}")
        return self.rest.prism_get(version="v1", request_url="/protection_domains")

    def get_rs_dr_snapshots(self, proxy_cluster_uuid=None):
        self.logger.info(f"Get the list of existing snapshots from {self.fqdn}")
        if proxy_cluster_uuid:
            return self.rest.prism_get(version="v1", request_url=f"/remote_sites/dr_snapshots?proxy_cluster_uuid={proxy_cluster_uuid}")
        return self.rest.prism_get(version="v1", request_url="/remote_sites/dr_snapshots")


class Prism_Central(Prism):     # pylint: disable=C0103
    def __init__(self, fqdn, sa=None, logger=logging, domain='ikea.com'):
        super().__init__(fqdn, sa, logger, domain)

    def get_clusters(self, uuid=None, filter=None):
        if uuid:
            self.logger.info(f"Get an existing cluster from {self.fqdn}")
            return self.rest.prism_get(version="v3", request_url=f"/clusters/{uuid}")
        self.logger.info(f"Get a list of existing clusters from {self.fqdn}")
        payload = {
            'kind': "cluster",
            'offset': 0,
            'length': 500,
            'sort_order': "ASCENDING",
            'sort_attribute': "name"
        }
        if filter:
            self.logger.info(f"Filter by {filter}")
            payload['filter'] = filter
        clusters = []
        page = 1
        while True:
            self.logger.info(f"Loaidng page {page}")
            data = self.rest.prism_post(version="v3", request_url="/clusters/list", payload=payload)
            self.logger.info(f"Got {len(data.json().get('entities'))} clusters in page {page}")
            clusters += data.json().get('entities')
            payload['offset'] += 500
            page += 1
            if len(data.json().get('entities')) < payload['length']:
                self.logger.info("It is the last page, break from pagination")
                break
        return clusters

    def get_host(self, uuid=None, filter=None):
        if uuid:
            self.logger.info(f"Get an existing host from {self.fqdn}")
            return self.rest.prism_get(version="v3", request_url=f"/hosts/{uuid}")
        self.logger.info(f"Get a list of existing hosts from {self.fqdn}")
        payload = {
            'kind': "host",
            'offset': 0,
            'length': 500,
            'sort_order': "ASCENDING",
            'sort_attribute': "name"
        }
        if filter:
            self.logger.info(f"Filter by {filter}")
            payload['filter'] = filter
        hosts = []
        data = self.rest.prism_post(version="v3", request_url="/hosts/list", payload=payload)
        self.logger.info(f"Got {len(data.json().get('entities'))} hosts")
        hosts += data.json().get('entities')
        return hosts

    def get_user_vm(self, uuid=None, filter=None):
        if uuid:
            self.logger.info(f"Get an existing VM from {self.fqdn}")
            return self.rest.prism_get(version="v3", request_url=f"/vms/{uuid}")
        self.logger.info(f"Get a list of existing VMs from {self.fqdn}")
        payload = {
            'kind': "vm",
            'offset': 0,
            'length': 500,
            'sort_order': "ASCENDING",
            'sort_attribute': "name"
        }
        if filter:
            self.logger.info(f"Filter by {filter}")
            payload['filter'] = filter
        vms = []
        page = 1
        while True:
            self.logger.info(f"Loading page {page}")
            data = self.rest.prism_post(version="v3", request_url="/vms/list", payload=payload)
            self.logger.info(f"Got {len(data.json().get('entities'))} VMs in page {page}")
            vms += data.json().get('entities')
            payload['offset'] += 500
            page += 1
            if len(data.json().get('entities')) < payload['length']:
                self.logger.info("It is the last page, break from pagination")
                break
        return vms

    def migrate_images_from_pe(self, image_uuids=[], pe_uuid=None):
        self.logger.info(f"Migrate images from PE({pe_uuid}) to {self.fqdn}")
        payload = {
            'cluster_reference': {
                'uuid': pe_uuid,
                "kind": "cluster",
                "name": "string"
            },
            "image_reference_list": []
        }
        if not len(image_uuids) == 0:
            for image_uuid in image_uuids:
                payload['image_reference_list'].append({'uuid': image_uuid})
        return self.rest.prism_post(version="v3", request_url="/images/migrate", payload=payload) # it returns a uuid of the task of image migration, data.json().get('task_uuid')

    def get_central_pe_id(self):
        return self.rest.prism_get(version="v3", request_url="/prism_central") # it returns 
  
        
class Prism_Element(Prism):     # pylint: disable=C0103
    def __init__(self, fqdn, sa=None, logger=logging, domain='ikea.com'):
        super().__init__(fqdn, sa, logger, domain)

    def get_user_vm(self, uuid=None):
        #get user vm through API v2
        if uuid:
            self.logger.info(f"Get an existing user VM from {self.fqdn}")
            data = self.rest.prism_get(version="v2", request_url=f"/vms/{uuid}")
        else:
            self.logger.info(f"Get a list of existing user VMs from {self.fqdn}")
            data = self.rest.prism_get(version="v2", request_url="/vms")
        return data

    def get_network(self, uuid=None):
        if uuid:
            self.logger.info(f"Get an existing network from {self.fqdn}")
            data = self.rest.prism_get(version="v2", request_url=f"/networks/{uuid}")
        else:
            self.logger.info(f"Get a list of existing networks from {self.fqdn}")
            data = self.rest.prism_get(version="v2", request_url="/networks")
        return data


class PrismCentral():
    def __init__(self, pc, sa=None, logger=logging, domain='ikea.com'):
        if not sa:
            _sa = ServiceAccount(usage="nutanix_pm")
            self.sa = _sa.get_service_account()
        else:
            self.sa = sa  # eg: {"username":'abc', "password":'secret'}
        self.pc = pc  # eg: ssp-eu-ntx.ikea.com
        self.endpoint = f'{self.pc}:9440'  # eg: ssp-eu-ntx.ikea.com:9440
        self.logger = logger if logger else logging
        _pat = re.compile(r'(ikea|ikeadt|ikead2)\.com', re.IGNORECASE)  # match ikea.com / ikeadt.com ,ingore case
        try:
            self.domain = re.search(_pat, self.pc).group()
        except Exception:
            self.domain = domain
        self.rest_pc = NutanixAPI(pc=self.pc, username=self.sa['username'], password=self.sa['password'],
                                  logger=self.logger)

    ######################################################################
    #                                                                    #
    #                       real time operation                          #
    #                                                                    #
    ######################################################################
    def get_cluster_list(self, filter=None):
        # it needs to return with a simple json, [{"name":"RETSE123-NXC000"},{"name":"RETSE124-NXC000"}]
        self.logger.info(f"Getting cluster list from {self.pc}.")
        json = {
            "kind": "cluster"
        }
        if filter:
            json['filter'] = filter
        try:
            res, data = self.rest_pc.call_pc_post(request_url='/clusters/list', payload=json)
            if res and data:
                cluster_list = [{"name": cluster["status"]['name'], "uuid": cluster['metadata']['uuid']} for cluster in
                                data["entities"]]  # type: ignore
                return True, cluster_list
            return False, "Can't get the cluster list.."
        except Exception as e:
            return False, e

    def get_host_list(self, filter=None):
        # it needs to return with a simple json, [{"name":"RETSE123-NXC000"},{"name":"RETSE124-NXC000"}]
        self.logger.info(f"Getting host list from {self.pc}.")
        payload = {
            "kind": "host"
        }
        if filter:
            payload["filter"] = filter
        try:
            res, data = self.rest_pc.call_pc_post(request_url='/hosts/list', payload=payload)
            if res and data:
                host_list = []
                self.logger.info("Iterating the host list to make our output nicer.")
                for host in data["entities"]:
                    try:
                        if "name" not in host["status"].keys():
                            self.logger.info("This host doesn't have a name, next.")
                            continue
                        host_list.append({"name": host["status"]["name"],
                                          "ip": host["status"]["resources"]["hypervisor"]["ip"],
                                          "cvm_ip": host["status"]["resources"]["controller_vm"]["ip"],
                                          "oob_ip": host["status"]["resources"]["ipmi"]["ip"],
                                          "cluster_uuid": host["status"]['cluster_reference']["uuid"]})
                    except Exception as e:
                        self.logger.error(f"An error occurred, when we iterating the host list, but we will continue, error: {str(e)}.")     # noqa
                return True, host_list
            return False, "Can't get the host list"
        except Exception as e:
            return False, str(e)


    def get_host_data_for_collector(self, filter=None, dict=False):
        # it needs to return with a simple json, [{"name":"RETSE123-NXC000"},{"name":"RETSE124-NXC000"}]
        self.logger.info(f"Getting host list from {self.pc}.")
        payload = {"kind": "host"}
        if filter:
            payload["filter"] = filter
        try:
            res, data = self.rest_pc.call_pc_post(
                request_url="/hosts/list", payload=payload
            )
            if res and data:
                self.logger.info(
                    "Iterating the host list to make our output in a nice list or dict."
                )
                if dict:
                    host_dict = {}
                else:
                    host_list = []
                import math

                for host in data["entities"]:
                    try:
                        if "name" not in host["status"].keys():
                            self.logger.info("This host doesn't have a name, next.")
                            continue
                        name = host["status"]["name"].upper()
                        uuid = host["metadata"]["uuid"]
                        pe_uuid = host["status"]["cluster_reference"]["uuid"]
                        disk_number = int(
                            len(
                                host["status"]["resources"]["host_disks_reference_list"]
                            )
                        )
                        sn = host["status"]["resources"]["serial_number"]
                        model = host["status"]["resources"]["block"]["block_model"]
                        memory_mib = host["status"]["resources"]["memory_capacity_mib"]
                        memory = (
                            str(math.ceil(memory_mib / 1024))
                        )  # Convert to GiB and concatenate took out GiB
                        cpu_core_number = int(
                            host["status"]["resources"]["num_cpu_cores"]
                        )
                        cpu_model = host["status"]["resources"]["cpu_model"]
                        ahv_ip = host["status"]["resources"]["hypervisor"]["ip"]
                        cvm_ip = host["status"]["resources"]["controller_vm"]["ip"]
                        ipmi_ip = host["status"]["resources"]["ipmi"]["ip"]

                        host_values = [
                            name,
                            sn, # new position for sn since it will be the new common ground amongst hosts
                            pe_uuid,
                            disk_number,
                            uuid,
                            model,
                            memory,
                            cpu_core_number,
                            cpu_model,
                            ahv_ip,
                            cvm_ip,
                            ipmi_ip,
                        ]
                        if dict:
                            host_dict[name] = host_values
                        else:
                            host_list.append(host_values)

                    except Exception as e:
                        self.logger.error(
                            f"An error occurred, when we iterating the host list, but we will continue, error: {str(e)}."
                        )  # noqa
                if dict:
                    return True, host_dict
                return True, host_list
            return False, []
        except Exception as e:
            return False, str(e)


    def get_host_list_in_pe(self, cluster):
        try:
            clu_res, cluster_list = self.get_cluster_list()
            if not clu_res:
                raise Exception("Failed to get the cluster list form PC, so can't get the host list either.")
            cluster = [clu for clu in cluster_list if
                       clu["name"] == cluster]  # [{"name":"SSSXXX-NXC000","uuid":"12123-132312-31231231-23123"}]
            if not cluster:
                raise Exception("The specific cluster in not in the cluster list.")
            _, host_list = self.get_host_list()
            hosts = [host for host in host_list if host["cluster_uuid"] == cluster[0]["uuid"]]
            if not hosts:
                raise Exception("We got an empty host list.....what happened??")
            return True, hosts
        except Exception as e:
            return False, str(e)

    def migrate_image_from_pe(self, pe_uuid):
        self.logger.info(f"Migrate image from PE with UUID is {pe_uuid} to {self.pc}.")
        payload = {
            "cluster_reference": {
                "uuid": pe_uuid,
                "kind": "cluster",
                "name": "string"
            },
            "image_reference_list": []
        }
        res, data = self.rest_pc.call_pc_post(request_url="/images/migrate", payload=payload)
        if not res:
            raise flaskex.BadGateway(f"Failed to migrate image from PE to PC! Detail: {data}")
        return data

    def get_image_list(self, filter=None):  # returns a list of image{name, uuid, image_type, image_state}
        self.logger.info(f"Getting image list from {self.pc}.")
        image_list = []
        page = 1
        payload = {
            "kind": "image",
            "offset": 0,
            "length": 500
        }
        if filter:
            payload["filter"] = filter
        try:
            while True:
                self.logger.info(f"Loading page {page}")
                _, data = self.rest_pc.call_pc_post(request_url="/images/list", payload=payload)
                self.logger.info(f"We get {len(data['entities'])} entities in page {page}")
                for image in data['entities']:
                    image_list.append({'name': image['status']['name'],
                                       'uuid': image['metadata']['uuid'],
                                       'image_state': image['status']['state']})
                if len(data['entities']) < payload['length']:
                    break
                payload['offset'] += payload['length']
                page += 1
            return True, image_list
        except Exception as e:
            return False, e

    def get_image_by_name(self, name):
        self.logger.info(f"Finding image '{name}' from {self.pc}")
        res, data = self.get_image_list(filter=f'name=={name}')
        if not res:
            raise flaskex.BadGateway(f"Failed to get image list! Reason: {data}")
        for image in data:
            if image["name"] == name:
                self.logger.info(f"The image {name} is found in {self.pc}")
                return image
        return None

    def get_role_list(self, length=500, offset=0, detail=False, filter=""):
        self.logger.info(f"Getting role list from {self.pc}. ")
        json = {
            "kind"   : "role",
            "length" : length,
            "offset" : offset,
            "filter" : filter
        }
        self.logger.info(f"Payload: {json}")
        try:
            res, data = self.rest_pc.call_pc_post(request_url='/roles/list', payload=json)
            if res and data:
                role_list = list()
                if detail:
                    _role_list = [{"name" : role["status"]['name'],
                                  "uuid" : role['metadata']['uuid'],
                                  "description" : role["status"]['description'],
                                  "resources" : role["status"]['resources']} for role in
                                data["entities"]]  # type: ignore
                else:
                    _role_list = [{"name" : role["status"]['name'],
                                  "uuid" : role['metadata']['uuid']} for role in
                                data["entities"]]  # type: ignore
                if data['metadata']['total_matches'] > (length + offset):
                    self.logger.info(f"There are {data['metadata']['total_matches']} records,but we can only fetch 500 at one time, keep fetching.")
                    res, result = self.get_user_group_list(length=500, offset=(length + offset), detail=detail)
                    role_list = result + _role_list
                else:
                    role_list = _role_list
                self.logger.info(f"Got {len(role_list)} role(s).")
                return True, role_list
            return False, "Can't get the role list.."
        except Exception as e:
            return False, str(e)

    def get_task_by_uuid(self, task_uuid):
        res, data = self.rest_pc.call_pc_get(f"/tasks/{task_uuid}")
        if not res:
            raise flaskex.BadGateway(
                f"Failed to {inspect.currentframe().f_code.co_name.replace('_', ' ')}! Reason: {data}")
        return data

    def get_acp_list(self, length=500, offset=0, detail=False, filter=""):
        # access control policy
        # which entities assigned to what role
        self.logger.info(f"Getting access control policy list from {self.pc}.")
        self.logger.info(f"length:{length},offset:{offset},detail:{detail},filter:{filter}")
        json = {
            "kind"   : "access_control_policy",
            "length" : length,
            "offset" : offset,
            "filter" : filter
        }
        try:
            res, data = self.rest_pc.call_pc_post(request_url='/access_control_policies/list', payload=json)
            if res and data:
                role_list = list()
                if data['metadata']['total_matches'] > (length + offset):
                    self.logger.info(f"There are {data['metadata']['total_matches']} records,but we can only fetch 500 at one time, keep fetching.")
                    res, _role_list = self.get_acp_list(length=500, offset=(length + offset), detail=detail)
                    role_list = data['entities'] + _role_list
                else:
                    return True, data['entities']
                return True, role_list
            return False, "Can't get the access_control_policies list.."
        except Exception as e:
            return False, str(e)

    def create_acp(self, name, description, role, groups, vm_uuid_list=[]):
        payload = {
            "spec" : {
                "name" : name,
                "description" : description,
                "resources" : {
                    "role_reference" : {
                        "kind" : "role",
                        "name" : role['name'],
                        "uuid" : role['uuid']
                    },
                    "filter_list" : {
                        "context_list" : [
                            {
                                "entity_filter_expression_list" : [
                                    {
                                        "operator" : "IN",
                                        "left_hand_side" : {"entity_type" : "ALL"},
                                        "right_hand_side" : {"collection" : "SELF_OWNED"}
                                    }
                                ],
                                "scope_filter_expression_list" : []
                            },
                            {
                                    "entity_filter_expression_list" : [
                                    {
                                        "operator" : "IN",
                                        "left_hand_side" : {"entity_type" : "vm"},
                                        "right_hand_side" : {"uuid_list" : [vm_uuid_list] if not isinstance(vm_uuid_list, list) else vm_uuid_list}
                                    }
                                ],
                                "scope_filter_expression_list" : []
                            }]
                    },
                    "user_group_reference_list" : [
                        {
                            "kind" : "user_group",
                            "name" : ug['name'],
                            "uuid" : ug['uuid']
                        } for ug in groups
                    ]
                }
            },
            "api_version" : "3.1.0",
            "metadata" : {
                "kind" : "access_control_policy",
                "spec_version" : 0
            }
        }
        self.logger.info(f"Creating ACP {name}.")
        res, _ = self.rest_pc.call_pc_post(request_url='/access_control_policies', payload=payload)
        return res

    def update_acp(self, name, description, role, groups, vm_uuid_list, acp):
        payload = {
            "spec" : {
                "name" : name,
                "description" : description,
                "resources" : {
                    "role_reference" : {
                        "kind" : "role",
                        "name" : role['name'],
                        "uuid" : role['uuid']
                    },
                    "filter_list" : {
                        "context_list" : [
                            {
                                "entity_filter_expression_list" : [
                                    {
                                        "operator" : "IN",
                                        "left_hand_side" : {"entity_type" : "ALL"},
                                        "right_hand_side" : {"collection" : "SELF_OWNED"}
                                    }
                                ],
                                "scope_filter_expression_list" : []
                            },
                            {
                                    "entity_filter_expression_list" : [
                                    {
                                        "operator" : "IN",
                                        "left_hand_side" : {"entity_type": "vm"},
                                        "right_hand_side" : {"uuid_list": [vm_uuid_list] if not isinstance(vm_uuid_list, list) else vm_uuid_list}
                                    }
                                ],
                                "scope_filter_expression_list" : []
                            }]
                    },
                    "user_group_reference_list" : [
                        {
                            "kind" : "user_group",
                            "name" : ug['name'],
                            "uuid" : ug['uuid']
                        } for ug in groups
                    ]
                }
            },
            "api_version" : "3.1.0",
            "metadata" : {
                "kind" : "access_control_policy",
                "spec_version" : acp['version'],
                "uuid" : acp['uuid']
            }
        }
        res, _ = self.rest_pc.call_pc_put(request_url=f"/access_control_policies/{acp['uuid']}", payload=payload)
        return res

    def get_user_group_list(self, length=500, offset=0, detail=False, filter=""):
        self.logger.info(f"Getting access control policy list from {self.pc}.")
        json = {
            "kind"   : "user_group",
            "length" : length,
            "offset" : offset,
            "filter" : filter
        }
        try:
            res, data = self.rest_pc.call_pc_post(request_url='/user_groups/list', payload=json)
            if res and data:
                ug_list = list()
                if data['metadata']['total_matches'] > (length + offset):
                    self.logger.info(f"There are {data['metadata']['total_matches']} records,but we can only fetch 500 at one time, keep fetching.")
                    res, _ug_list = self.get_user_group_list(length=500, offset=(length + offset), detail=detail)
                    ug_list = data['entities'] + _ug_list
                else:
                    ug_list = data['entities']
                self.logger.info(f"We got {len(ug_list)} user groups.")
                return True, ug_list
            return False, "Can't get the user group list.."
        except Exception as e:
            return False, str(e)

    def get_vm_list(self, length=500, offset=0, detail=False, filter=""):
        self.logger.info(f"Getting vm list from {self.pc}.")
        json = {
            "kind"   : "vm",
            "length" : length,
            "offset" : offset,
            "filter" : filter
        }
        try:
            res, data = self.rest_pc.call_pc_post(request_url='/vms/list', payload=json)
            if res and data:
                vm_list = list()
                if data['metadata']['total_matches'] > (length + offset):
                    self.logger.info(f"There are {data['metadata']['total_matches']} records, but we can only fetch 500 at one time, keep fetching.")
                    res, _vm_list = self.get_vm_list(length=500, offset=(length + offset), detail=detail)
                    vm_list = data['entities'] + _vm_list
                else:
                    vm_list = data['entities']
                self.logger.info(f"We got {len(vm_list)} vms.")
                return True, vm_list
            return False, "Can't get the vm list.."
        except Exception as e:
            return False, str(e)

    def get_vm_detail_by_uuid(self, uuid):
        self.logger.info(f"Start to get VM detail by uuid {uuid}...")
        res, data = self.rest_pc.call_pc_get(request_url=f'/vms/{uuid}')
        if not res:
            raise flaskex.InternalServerError("Failed to get VM detail!")
        return data

    def check_pe_reachable(self, pe):
        """ response example:
        {
            "entity_type": "cluster",
            "filtered_entity_count": 42,
            "filtered_group_count": 1,
            "group_results": [
                {
                    "entity_results": [
                        {
                            "data": [
                                {
                                    "data_type": "string",
                                    "name": "name",
                                    "values": [
                                        {
                                            "time": 1633886040498000,
                                            "values": [
                                                "retcnchn-nxp001"
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data_type": "bool",
                                    "name": "is_available",
                                    "values": [
                                        {
                                            "time": 1606820658701482,
                                            "values": [
                                                "true"
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data_type": "uint64",
                                    "name": "_created_timestamp_usecs_",
                                    "values": [
                                        {
                                            "time": 1606820637003394,
                                            "values": [
                                                "1606820637003394"
                                            ]
                                        }
                                    ]
                                }
                            ],
                            "entity_id": "5e644590-e520-4648-9ac0-4c973742d297"
                        }, ....
                    ]
                }
            ]
        }
        """
        data = self.get_pc_clusters_group(["name", "is_available"])
        results = data['group_results'][0]['entity_results']
        required_pe = pe.lower().split('.')[0]      # 'iiccn048-nxc001'
        for result in results:
            data_list = result['data']
            # assume the result is in order: 0 is name, 1 is is_available
            current_name = data_list[0]['values'][0]['values'][0]
            if required_pe == current_name.lower():
                status = data_list[1]['values'][0]['values'][0]
                return status == "true"
        raise flaskex.InternalServerError(f"Failed to get status for cluster '{pe}'!")

    def get_pc_clusters_group(self, attributes):
        payload = {
            "entity_type": "cluster",
            "filter_criteria": "include_pc==true",
            "group_member_attributes": [{"attribute": a} for a in attributes]
        }
        res, data = self.rest_pc.call_pc_post("/groups", payload=payload)
        if not res:
            raise flaskex.InternalServerError("Failed to call /groups!")
        return data

    def get_calm_apps(self, filter=""):
        payload = {
            "filter": filter,
            "kind": "app",
            "offset": 0,
            "length": 250,
        }
        res, data = self.rest_pc.call_pc_post("/apps/list", payload=payload)
        if not res:
            raise flaskex.BadGateway(f"Failed to call /apps/list! Response: {data}")
        return data

    def get_remote_sites(self, proxy_cluster_uuid, retry=10):
        self.logger.info(f"Pulling PE Remote Sites using PC Proxy, Cluster target '{proxy_cluster_uuid}'")
        res, data = self.rest_pc.call_pc_get(f"/remote_sites?proxyClusterUuid={proxy_cluster_uuid}", 1, retry=retry)
        if not res:
            raise flaskex.BadGateway(f"Failed to GET /remote_sites! Response: {data}")
        return data

    def get_remote_site_detail(self, proxy_cluster_uuid, site_name):
        self.logger.info(
            f"Getting PE Remote Sites using PC Proxy, Cluster target '{proxy_cluster_uuid}' with site name '{site_name}'")
        res, data = self.rest_pc.call_pc_get(f"/remote_sites/{site_name}?proxyClusterUuid={proxy_cluster_uuid}", 1)
        if not res:
            raise flaskex.BadGateway(f"Failed to GET /remote_sites/{site_name}! Response: {data}")
        return data

    def create_remote_site(
        self, proxy_cluster_uuid, remote_site_name, target_ip,
        target_port=2020, compression_enabled=True, ssh_enabled=False, proxy_enabled=False
    ):
        self.logger.info(
            f"Creating Remote Site : '{remote_site_name}' through PC Proxy, Cluster target '{proxy_cluster_uuid}'")
        payload = {
            "name": remote_site_name,
            "remoteIpPorts": {
                target_ip: target_port,
            },
            "proxyEnabled": proxy_enabled,
            "compressionEnabled": compression_enabled,
            "sshEnabled": ssh_enabled,
            "capabilities": ["BACKUP"],
        }
        res, data = self.rest_pc.call_pc_v1_post(f"/remote_sites?proxyClusterUuid={proxy_cluster_uuid}", payload=payload)
        if not res:
            raise flaskex.BadGateway(f"Failed to POST /remote_sites! Response: {data}")
        return data

    def delete_remote_site(self, proxy_cluster_uuid, rs_name):
        self.logger.info(f"Removing remote Site '{rs_name}'...")
        res, data = self.rest_pc.call_pc_delete(f"/remote_sites/{rs_name}?proxyClusterUuid={proxy_cluster_uuid}", 1)
        if not res:
            raise flaskex.BadGateway(f"Failed to DELETE /remote_sites/{rs_name}! Response: {data}")
        return data

    def get_prx_networks(self, proxy_cluster_uuid):
        self.logger.info(f"Getting networks for '{proxy_cluster_uuid}'...")
        res, data = self.rest_pc.call_pc_get(f"/networks?proxyClusterUuid={proxy_cluster_uuid}", api_version=2, retry=10)
        if not res:
            raise flaskex.BadGateway(f"Failed to GET /networks! Response: {data}")
        return data

    def get_prx_hosts(self, proxy_cluster_uuid):
        self.logger.info(f"Query PE Hosts through PC, using cluster: '{proxy_cluster_uuid}'")
        res, data = self.rest_pc.call_pc_get(f"/hosts?proxyClusterUuid={proxy_cluster_uuid}", api_version=1)
        if not res:
            raise flaskex.BadGateway(f"Failed to GET /hosts! Response: {data}")
        return data

    ######################################################################
    #                                                                    #
    #                          DB operation                              #
    #                                                                    #
    ######################################################################

    @staticmethod
    def _template_get_prism_list_from_db(
        model: ModelPrismCentral | ModelWarehousePrismCentral,
        schema: ModelPrismCentralSchema | ModelWarehousePrismCentralSchema,
    ) -> list:
        result = model.query.all()
        return schema(many=True).dump(result)

    @staticmethod
    def get_retail_prism_list_from_db() -> list:
        return PrismCentral._template_get_prism_list_from_db(ModelPrismCentral, ModelPrismCentralSchema)

    @staticmethod
    def get_warehouse_prism_list_from_db() -> list:
        return PrismCentral._template_get_prism_list_from_db(
            ModelWarehousePrismCentral, ModelWarehousePrismCentralSchema
        )

    @staticmethod
    def get_prism_list_from_db() -> list:
        return PrismCentral.get_retail_prism_list_from_db() + PrismCentral.get_warehouse_prism_list_from_db()

    @staticmethod
    def get_prism_by_id_from_db(id):
        prism = ModelPrismCentral.query.filter_by(id=id).first()
        pcschema = ModelPrismCentralSchema()
        result = pcschema.dump(prism)
        return result

    @staticmethod
    def get_prism_by_name_from_db(fqdn):
        prism = ModelPrismCentral.query.filter_by(fqdn=fqdn).first()
        if prism:
            pcschema = ModelPrismCentralSchema()
        else:
            prism = ModelWarehousePrismCentral.query.filter_by(fqdn=fqdn).first()
            pcschema = ModelWarehousePrismCentralSchema()
        result = pcschema.dump(prism)
        return result

    def update_lcm_info_to_db(self):
        commonrest = CommonRestCall(self.sa['username'], self.sa['password'])
        pc = ModelPrismCentral.query.filter_by(name=self.pc).first()
        pcschema = ModelPrismCentralSchema()
        config = commonrest.call_restapi(url=f"https://{self.endpoint}{SETTING.API_URL['lcm_config']}",
                                         method="GET")  # get the lcm config info (lcm version , darksite )
        entities = commonrest.call_restapi(url=f"https://{self.endpoint}{SETTING.API_URL['lcm_entity']}",
                                           method="GET")  # get the lcm entities info , like calm,epsilon version...
        if config.status_code == 200 and entities.status_code == 200 and pc:
            config, entities = config.json(), entities.json()
            pc.lcm = config["data"]["semanticVersion"]
            pc.darksite = config["data"]["url"]
            pc.epsilon = next(
                (x["requestVersion"] for x in entities["data"] if x["entityModel"] == SETTING.LCM["epsilon"]), None)
            pc.calm = next(
                (x["requestVersion"] for x in entities["data"] if x["entityModel"] == SETTING.LCM["calm"]), None)
            pc.cmu = next(
                (x["requestVersion"] for x in entities["data"] if x["entityModel"] == SETTING.LCM["cmu"]), None)
            pc.flowsecurity = next(
                (x["requestVersion"] for x in entities["data"] if x["entityModel"] == SETTING.LCM["flowsecurity"]),
                None
            )
            pc.licensing = next(
                (x["requestVersion"] for x in entities["data"] if x["entityModel"] == SETTING.LCM["licensing"]), None)
            pc.ncc = next(
                (x["requestVersion"] for x in entities["data"] if x["entityModel"] == SETTING.LCM["ncc"]), None
            )
            pc.pc = next((x["requestVersion"] for x in entities["data"] if x["entityModel"] == SETTING.LCM["pc"]), None)
            db.session.commit()
            return pcschema.dump(pc)
        return  # log it

    def add_prism_to_db(self, data):
        try:
            task = ModelPrismCentral(**data)
            db.session.add(task)
            db.session.commit()
            return True, "PC added."
        except Exception as e:
            return False, str(e)

    def _generate_guest_customization_script(self, vm_spec):
        image_alias = vm_spec['image_alias']
        ip_id = vm_spec['ip_id']
        # path = "\\static\\OSCustomization"
        path = os.path.join("static", "OSCustomization")
        if image_alias == "RHEL9_AUTO" or image_alias == "RHEL8_AUTO":
            name = "LX-CloudInit-latest.yml"
        elif image_alias.startswith('RHEL8'):
            name = "LX-CloudInit-v2-RHEL8.yml"
        elif image_alias.startswith('RHEL9'):
            name = "LX-CloudInit-v1-RHEL9.yml"
        elif image_alias.startswith('RHEL7'):
            name = "LX-CloudInit-v1-RHEL7.yml"
        # script_path = f"{application_path}\\{path}\\{name}"
        script_path = os.path.join(application_path, path, name)
        self.logger.info(f"script_path: {script_path}")
        with open(script_path) as f:
            script = f.read()
        ipam = Ipam()
        ip_class_parameters_dict = ipam.get_ip_class_parameters_dict(ip_id)
        ip_address_info = ipam.get_ip_address_info(ip_id)
        variables = {
            'VM_Name': vm_spec['name'],
            'VM_Fqdn': f"{vm_spec['name'].lower()}.ikea.com",
            'VM_IP': vm_spec['ip'],
            'VM_SubnetMask': Ipam.calculate_subnet_mask(ip_address_info[0]["subnet_size"]),
            'VM_SubnetSize': Ipam.calculate_cidr(ip_address_info[0]["subnet_size"]),
            'VM_GW': ip_class_parameters_dict['gateway'],
            'VM_DnsDomain': ip_class_parameters_dict['domain'],
            'VM_DNS1': ip_class_parameters_dict['ikea_dns_server'],
            'VM_DNS2': ip_class_parameters_dict['ikea_dns_server_2'],
        }
        for k, v in variables.items():
            script = script.replace(f'@@{{{k}}}@@', v)
        self.logger.info(f"cloud init data: {script}")
        return base64.b64encode(script.encode('utf-8')).decode()

    def _generate_create_vm_payload(self, vm_spec):
        template_path = os.path.join(application_path, "static", "templates", "create_vm_payload.jinja2")
        self.logger.info(f"template_path: {template_path}")
        with open(template_path) as f:
            t = Template(f.read())
        network = PrismElement(vm_spec['pe']).get_network_by_vlan_id(vm_spec['vlan_id'])
        if not network:
            raise flaskex.BadGateway(f"Can't find network with vlan_id {vm_spec['vlan_id']} in pe!")
        network_uuid = network['uuid']
        system_disk_size = int(vm_spec[WorkloadSpec.DISK][0])
        other_disks = vm_spec["disk"][1:]
        render_params = {
            "workload_type": vm_spec[WorkloadSpec.WORKLOAD_TYPE],
            "vm_name": vm_spec[WorkloadSpec.VM_NAME],
            "memory_size_mib": vm_spec[WorkloadSpec.MEMORY] * 1024,    # convert GiB to MiB
            "num_vcpus_per_socket": vm_spec[WorkloadSpec.CPU_CORE],
            "num_sockets": vm_spec[WorkloadSpec.CPU],
            "image_uuid": vm_spec["image_uuid"],
            "boot_type": vm_spec[WorkloadSpec.BOOT_MODE],
            "disks": [convert_GiB_to_bytes(int(disk)) for disk in other_disks],
            "system_disk_size": convert_GiB_to_bytes(system_disk_size),
            "cluster_uuid": NutanixOperation.get_pe_by_fqdn_from_db(fqdn=vm_spec["pe"]).uuid,
            "network_uuid": network_uuid,
            "user_data": self._generate_guest_customization_script(vm_spec) if vm_spec['workload_type'] == 'linux' else None        # noqa
        }
        template = t.render(**render_params)
        return json.loads(template)

    def create_vm_on_ntx(self, vm_spec):
        task_uuid = self.create_vm_by_pc_api(vm_spec)
        if not self.is_task_succeeded(task_uuid):
            raise flaskex.BadGateway("VM creation task failed!")
        vm_uuid = self.get_vm_uuid(task_uuid)
        self.logger.info(f"Create VM succeeded! VM uuid: {vm_uuid}")
        return vm_uuid

    def create_vm_by_pc_api(self, vm_spec):
        payload = self._generate_create_vm_payload(vm_spec)
        self.logger.info(f"create vm payload: {payload}")
        res, data = self.rest_pc.call_pc_post(request_url='/vms', payload=payload)
        if not res:
            err = data.get("message_list")
            raise flaskex.InternalServerError(f"Create VM API sent failed! Error: '{err}'")
        task_uuid = data["status"]["execution_context"]["task_uuid"]
        self.logger.info(f"Task created, task_uuid: {task_uuid}")
        return task_uuid

    def is_task_succeeded(self, task_uuid, retries=5, retry_interval=60):
        self.logger.info(f"Querying task status... Task uuid: {task_uuid}")
        for _ in range(retries):
            res, data = self.rest_pc.call_pc_get(f"/tasks/{task_uuid}")
            if not res:
                self.logger.warning(f"Failed to get task status in present loop. Reason: {data}")
                continue
            status = data["status"]
            self.logger.info(f"Task status: {status}")
            if status.lower() == "succeeded":
                return True
            if status.lower() == "failed":
                return False
            self.logger.info(f"Task is not ended, sleep {retry_interval}s to retry...")
            time.sleep(retry_interval)
        return False

    def get_vm_uuid(self, task_uuid):
        _, data = self.rest_pc.call_pc_get(f"/tasks/{task_uuid}")
        return data["entity_reference_list"][0]["uuid"]

    def delete_vm_on_ntx(self, vm_uuid):
        self.logger.info("Start to delete VM on Nutanix...")
        res, data = self.rest_pc.call_pc_delete(f"/vms/{vm_uuid}")
        if not res:
            raise flaskex.InternalServerError(f"Failed to delete VM on Nutanix! Detail: {data}")
        task_uuid = data["status"]["execution_context"]["task_uuid"]
        self.logger.info(f"Task created, task_uuid: {task_uuid}")
        return task_uuid

    def get_ssl_cert(self):
        self.logger.info(f"Get SSL certificate from {self.pc}.")
        res, data = self.rest_pc.call_pc_get(request_url="/keys/pem", api_version=1)
        if res and data:
            self.logger.info("Got the certificate")
            return True, data
        self.logger.error(f"Failed to get the certificate from {self.pc}. Error message: {data}")
        return False, data

    def upload_ssl_cert(self, keytype="RSA_2048", keyfile=None, certfile=None, chainfile=None):
        self.logger.info(f"Uploading SSL certificate to {self.pc}.")
        try:
            with open(keyfile, "rb") as key, open(certfile, "rb") as cert, open(chainfile, "rb") as chain:
                files = {
                    "keyType": keytype,
                    "key": key,
                    "cert": cert,
                    "caChain": chain
                }
                res, _ = self.rest_pc.call_pc_v1_post(request_url="/keys/pem/import", files=files)
            if not res:
                self.logger.error(f"Failed to upload the certificate, reason: {res}")
                return False
            self.logger.info("Successfully upload the certificate")
            return True
        except Exception:
            return False

    def get_cluster_detail(self):
        self.logger.info("Getting cluster details.")
        res, data = self.rest_pc.call_pc_get(request_url="/cluster", api_version=1)
        if res:
            return data
        raise Exception(f"Failed to get cluster detail, error:{data}")

    @staticmethod
    def _template_get_pc_tier_relationship(model: ModelPrismCentral | ModelWarehousePrismCentral) -> dict:
        relationship = dict()
        for _pc in model.query.all():
            relationship[_pc.fqdn.upper()] = _pc.tier.upper()
        return relationship

    @staticmethod
    def get_retail_pc_tier_relationship() -> dict:
        return PrismCentral._template_get_pc_tier_relationship(ModelPrismCentral)

    @staticmethod
    def get_warehouse_pc_tier_relationship() -> dict:
        return PrismCentral._template_get_pc_tier_relationship(ModelWarehousePrismCentral)

    @staticmethod
    def get_pc_tier_relationship() -> dict:
        return PrismCentral.get_retail_pc_tier_relationship() | PrismCentral.get_warehouse_pc_tier_relationship()


class PrismElement:
    def __init__(self, pe, sa=None, logger=None, timeout=30, is_central_pe=False, collector = False, pe_fqdn = False) -> None:
        if is_central_pe:
            self.pe, self.pe_fqdn = pe, pe

        if pe_fqdn:
            self.pe, self.pe_fqdn = pe, pe_fqdn

        else:
            self.pe, self.pe_fqdn = CommonHelper.parse_pe_name_and_fqdn(pe)

        self.logger = logger if logger else logging
        self.sa = sa
        if not sa:
            _sa = ServiceAccount(usage="nutanix_pm")
            self.sa = _sa.get_service_account()

        if collector:  # Replacing functionality in CommonHelper called in NutanixAPI constructor
            self.rest_pe = NutanixAPI(
                pe=self.pe_fqdn,
                username=self.sa['username'],
                password=self.sa['password'],
                logger=self.logger,
                timeout=timeout,
                collector = True,
                pe_fqdn=self.pe_fqdn
            )
        else:
            self.rest_pe = NutanixAPI(
                pe=self.pe_fqdn,
                username=self.sa["username"],
                password=self.sa["password"],
                logger=self.logger,
                timeout=timeout,
            )


    def add_network(self, name, vlan_id, vs_name=None):
        self.logger.info(f"Creating network {name} with vlan {vlan_id} in {self.pe}")
        payload = {
            'name': name,
            'vlan_id': vlan_id
        }
        if vs_name:
            payload['vswitch_name'] = vs_name
        res, data = self.rest_pe.call_pe_v2_post("/networks", payload=payload)
        if not res:
            self.logger.error(f"Failed to create network {name}! Reason: {data['message']}")
            raise Exception(f"Failed to create network {name}! Reason: {data['message']}")
        return res, data

    def get_network_list(self, details=False):
        self.logger.info(f"Getting network list from {self.pe}")
        res, data = self.rest_pe.call_pe_get(request_url='/networks',
                                             api_version=2)  # calling API version2 to get network list from PE
        if not res:
            raise flaskex.BadGateway(f"Failed to get network list! Reason: {data}")
        self.logger.info(f"Got the network list from {self.pe}.")
        network_list = []
        # get and assemble eache network object, and push them to the network_list array
        for network in data['entities']:
            try:
                network_entity = {'uuid': network.get('uuid'), 'name': network.get('name'), 'vlan_id': network.get('vlan_id'),
                                  'vswitch_uuid': network.get('virtual_switch_uuid'),
                                  'vswitch_name': network.get('vswitch_name')}
                if details:
                    network_entity['detail'] = network
                network_list.append(network_entity)
            except Exception as e:
                raise flaskex.InternalServerError(f"Got error when adding {network['name']} to the return list.{str(e)}")
        return network_list

    def get_container_list(self, details=False):  # get the container list, use nutanix V1 api.
        res, data = self.rest_pe.call_pe_get(request_url='/containers')
        self.logger.info(f"Getting container list from {self.pe}")
        if res and data:
            self.logger.info(f"Got the container list from {self.pe}.")
            container_list = []
            for container in data['entities']:
                try:
                    container_entity = {'id': container['id'], 'container_uuid': container['containerUuid'],
                                        'name': container['name'], 'cluster_uuid': container['clusterUuid']}
                    if details:
                        container_entity['detail'] = container
                    container_list.append(container_entity)
                except Exception:
                    self.logger.error(f'Got error when adding {container["name"]} to the return list.')
            return True, container_list
        self.logger.error(f"Failed to get the container list from {self.pe}. Error message:{data}")
        return False, data

    def get_container_by_name(self, name):
        res, data = self.get_container_list()
        if not res:
            raise Exception(f"Failed to get container list! Reason: {data}")
        for container in data:
            if container["name"] == name:
                self.logger.info(f"The container {name} is found")
                return True, container
        return False, f"The container {name} does not exist in {self.pe}"

    def get_image_list(self, details=False):  # get the image list, use nutanix V2 api.
        res, data = self.rest_pe.call_pe_get(request_url='/images', api_version=2)
        self.logger.info(f"Getting image list from {self.pe}")
        if res and data:
            self.logger.info(f"Got the image list from {self.pe}.")
            image_list = []
            for image in data['entities']:
                try:
                    image_entity = {'uuid': image['uuid'], 'name': image['name'], 'image_type': image['image_type'],
                                    'image_state': image['image_state'], 'vm_disk_id': image['vm_disk_id']}
                    if details:
                        image_entity['detail'] = image
                    image_list.append(image_entity)
                except Exception:
                    self.logger.error(f"Got error when adding {image['name']} to the return list.")
            return True, image_list
        self.logger.error(f"Failed to get the image list from {self.pe}. Error message:{data}")
        return False, data

    def add_image(self, name, annotation, image_type, container_uuid, image_url):
        self.logger.info(f"Uploading image {name} to {self.pe}")
        payload = {
            'name': name,
            'annotation': annotation,
            'imageType': image_type,
            'imageImportSpec': {
                'containerUuid': container_uuid,
                'url': image_url
            }
        }
        res, data = self.rest_pe.call_pe_v0_8_post("/images", payload=payload)
        if res and data:
            return True, data
        self.logger.error(f"Failed to upload image {name}! Reason: {data}")
        return False, data

    def get_vm_list(self, details=False, filter_string=None):  # get the vm list , use nutanix V1 api.
        request_url = "/vms"
        if filter_string:
            request_url += f"?{filter_string}"
        self.logger.info(f"Getting VM list from {self.pe}.")
        res, data = self.rest_pe.call_pe_get(request_url=request_url)
        if res and data:
            self.logger.info(f"Got the VM list from {self.pe}.")
            vm_list = []
            if details:
                # vm_list = [{'name':vm['vmName'], 'state':vm['powerState'], 'is_controller_vm':vm['controllerVm'], 'uuid':vm['uuid'], 'detail':vm} for vm in data['entities']]     # noqa
                # not using a easy way , in case nutanix API return some shit.
                for vm in data['entities']:  # type:  ignore
                    try:
                        vm_list.append(
                            {'name': vm['vmName'], 'state': vm['powerState'], 'is_controller_vm': vm['controllerVm'],
                             'uuid': vm['uuid'], 'detail': vm})  # type:  ignore
                    except Exception:
                        self.logger.error(f'Got error when adding {vm["vmName"]} to the return list.')  # type:  ignore
            else:
                for vm in data['entities']:  # type:  ignore
                    try:
                        vm_list.append(
                            {'name': vm['vmName'], 'state': vm['powerState'], 'is_controller_vm': vm['controllerVm'],
                             'uuid': vm['uuid']})  # type:  ignore
                    except Exception:
                        self.logger.error(f'Got error when adding {vm["vmName"]} to the return list.')
                # vm_list = [{'name':vm['vmName'], 'state':vm['powerState'], 'is_controller_vm':vm['controllerVm'], 'uuid':vm['uuid']} for vm in data['entities']]       # noqa
                # not using a easy way , in case nutanix API return some shit.
            return True, vm_list
        self.logger.error(f"Failed to get the VM list from {self.pe}. Error message:{data}")
        return False, data

    def get_host_list(self, pc = None, details=False, tried_fix=False):  # get the host list , use nutanix V1 api.
        res, data = self.rest_pe.call_pe_get(request_url='/hosts')
        self.logger.info(f"Getting host list from {self.pe}.")
        if res and data:
            self.logger.info(f"Got the host list from {self.pe}.")
            host_list = []
            if details:
                for host in data['entities']:  # type:  ignore
                    try:
                        host_list.append({'name': host['name'], 'ip': host['hypervisorAddress'], 'state': host['state'],
                                          'cvm_ip': host['controllerVmBackplaneIp'], 'oob_ip': host['ipmiAddress'],
                                          'detail': host})  # type:  ignore
                    except Exception:
                        self.logger.error(f'Got error when adding {host["name"]} to the return list.')  # type:  ignore
            else:
                for host in data['entities']:  # type:  ignore
                    try:
                        host_list.append({'name': host['name'], 'ip': host['hypervisorAddress'],
                                          'cvm_ip': host['controllerVmBackplaneIp'], 'oob_ip': host['ipmiAddress'],
                                          'state': host['state']})  # type:  ignore
                    except Exception:
                        self.logger.error(f'Got error when adding {host["name"]} to the return list.')  # type:  ignore
            tried_fix = True
            return True, host_list
        if not tried_fix:
            from business.distributedhosting.nutanix.automation.automation import Automation
            self._prism = PrismCentral(pc=pc, logger=self.logger)
            self.tier = self._prism.get_prism_by_name_from_db(fqdn=pc)['tier']
            # self.logger.info(f"the service account name is {self.sa['username']}")
            self.vault = Vault(tier=self.tier)
            res, ssh_pass = self.vault.get_secret(f"{self.pe.upper()}/Site_Pe_Admin")
            sa = {"username": "admin", "password": ssh_pass["secret"]}
            atm = Automation(pc=pc, pe=self.pe_fqdn, sa = sa, logger=self.logger)
            res, _ = atm.reauth_pe_ad()
            if not res:
                raise Exception(f"Failed to re-auth AD for {self.pe}")
            self.logger.info("Good, re-auth works, let's see if we can get the updates this time.")
            return self.get_host_list(self, details=False, tried_fix = True)
        self.logger.error(f"Failed to get the host list from {self.pe}. Error message:{data}")
        return False, data


    def get_host_nics_dict(self, host_uuid = None, retries = 5):     #Single host works for bot wh and reatil

        self.logger.info(f"Getting host nics list from {self.pe} host_uuid: {host_uuid}.")

        res, data = self.rest_pe.call_pe_get(request_url=f'/hosts/{host_uuid}/host_nics', retries = retries)

        if res and data:
            self.logger.info(
                f"Got the hosts nics list from {self.pe} host_uuid: {host_uuid}."
            )
            self.logger.info("putting the data in a nice dict.")
            host_nic_dict = {}

            count = 0
            for nic in data:
                try:
                    link_speed_gbps = nic["linkSpeedInKbps"] / 1000000 # took out "GiB"
                except:
                    link_speed_gbps = None

                try:
                    host_nic_dict["nic" + str(count) + "_uuid"] = nic["uuid"]
                except:
                    host_nic_dict["nic" + str(count) + "_uuid"] = "NA"
                try:
                    host_nic_dict["nic" + str(count) + "_mac"] = nic["macAddress"]
                except:
                    host_nic_dict["nic" + str(count) + "_mac"] = "NA"
                try:
                    host_nic_dict["nic" + str(count) + "_speed"] = link_speed_gbps
                except:
                    host_nic_dict["nic" + str(count) + "_speed"] = "NA"
                try:
                    host_nic_dict["nic" + str(count) + "_mtu"] = nic["mtuInBytes"]
                except:
                    host_nic_dict["nic" + str(count) + "_mtu"] = "NA"
                try:
                    host_nic_dict["nic" + str(count) + "_sw_device"] = nic[
                        "switchDeviceId"
                    ]
                except:
                    host_nic_dict["nic" + str(count) + "_sw_device"] = "NA"
                try:
                    host_nic_dict["nic" + str(count) + "_sw_port"] = nic["switchPortId"]
                except:
                    host_nic_dict["nic" + str(count) + "_sw_port"] = "NA"
                try:
                    host_nic_dict["nic" + str(count) + "_sw_vendor"] = nic[
                        "switchVendorInfo"
                    ]
                except:
                    host_nic_dict["nic" + str(count) + "_sw_vendor"] = "NA"
                try:
                    host_nic_dict["nic" + str(count) + "_sw_vlan"] = nic["switchVlanId"]
                except:
                    host_nic_dict["nic" + str(count) + "_sw_vlan"] = "NA"
                count += 1

            return True, host_nic_dict

        self.logger.error(
            f"Failed to get the host nics list from {self.pe}. Reason is : {data}"
        )
        return False, data


    def set_vm_power_state(self, vm_uuid, transition, vm_name=None):
        # transition valid value: ON OFF POWERCYCLE RESET PAUSE SUSPEND RESUME SAVE ACPI_SHUTDOWN ACPI_REBOOT
        # upper/lower case are both valid
        self.logger.info(f"Start to set VM power state to '{transition}', VM uuid: {vm_uuid}, VM name: {vm_name}.")
        res, data = self.rest_pe.call_pe_v2_post(f'/vms/{vm_uuid}/set_power_state', payload={"transition": transition})
        if not res:
            raise Exception(f"Failed to power on/off VM! Reason: {data}")
        task_uuid = data["task_uuid"]
        if not self.is_task_succeeded(task_uuid):
            raise Exception("VM power state change task failed!")
        self.logger.info(f"Successfully set VM power state to '{transition}'.")

    def is_task_succeeded(self, task_uuid, retries=5, retry_interval=60):
        for i in range(retries):
            self.logger.info(f"Get task status attempting {i+1}/{retries}...")
            res, data = self.rest_pe.call_pe_get(f'/tasks/{task_uuid}', api_version=2)
            if not res:
                self.logger.warning(f"Failed to get task staus in present loop. Reason: {data}")
                continue
            status = data["progress_status"]
            self.logger.info(f"Task status: {status}")
            if status.lower() == "succeeded":
                return True
            if status.lower() == "failed":
                self.logger.error(f"Task failed! Error detail: {data['meta_response']['error_detail']}")
                return False
            self.logger.info(f"Task is not ended, sleep {retry_interval}s to retry... Task percentage: {data.get('percentage_complete')}")
            time.sleep(retry_interval)
        return False

    def monitor_task_status(self, task_uuid, retries=5, retry_interval=60):
        for i in range(retries):
            self.logger.info(f"Get task status attempting {i+1}/{retries}...")
            res, data = self.rest_pe.call_pe_get(f'/tasks/{task_uuid}', api_version=2)
            if not res:
                self.logger.warning(f"Failed to get task staus in present loop. Reason: {data}")
                continue
            status = data["progress_status"]
            self.logger.info(f"Task status: {status}")
            if status.lower() == "succeeded":
                return status.lower(), "succeeded"
            if status.lower() == "failed":
                return status.lower(), f"{data['meta_response']['error_detail']}"
            self.logger.info(f"Task is not ended, sleep {retry_interval}s to retry... Task percentage: {data.get('percentage_complete')}")
            time.sleep(retry_interval)
        self.logger.info(f"Task is not ended after {retries} times retry, now we stop monitoring")
        return "not_ended", f"{data.get('percentage_complete')}"


    def get_ssl_cert(self):
        self.logger.info(f"Get SSL certificate from {self.pe}.")
        res, data = self.rest_pe.call_pe_get(request_url="/keys/pem")
        if res and data:
            self.logger.info("Got the certificate")
            return True, data
        self.logger.error(f"Failed to get the certificate from {self.pe}. Error message: {data}")
        return False, data

    def upload_ssl_cert(self, keytype="RSA_2048", keyfile=None, certfile=None, chainfile=None):
        self.logger.info(f"Uploading SSL certificate to {self.pe}.")
        try:
            with open(keyfile, "rb") as key, open(certfile, "rb") as cert, open(chainfile, "rb") as chain:
                files = {
                    "keyType": keytype,
                    "key": key,
                    "cert": cert,
                    "caChain": chain
                }
                res, _ = self.rest_pe.call_pe_post(request_url="/keys/pem/import", files=files)
            if not res:
                self.logger.error(f"Failed to upload the certificate, reason: {res}")
                return False
            self.logger.info("Successfully upload the certificate")
            return True
        except Exception:
            return False

    def shutdown_host_via_ssh(self, hostip, username, password, hostname=None):
        self.logger.title(f"Powering off AHV: {hostname if hostname else hostip}.")
        try:
            username = username if username else self.sa['username']
            password = password if password else self.sa['password']
            _conn = SSHConnect(host=hostip, username=username, password=password, logger=self.logger)
            res, _ = _conn.connect()
            if not res:
                raise Exception("Failed to connect to AHV.")
            _conn.invoke_shell()
            _conn.send_command("shutdown -h now")
            return True
        except Exception as e:
            self.logger.error(str(e))
            return False

    def shutdown_cluster(self, cvm, username=None, password=None):
        self.logger.title("Shutting down the cluster.")
        try:
            username = username if username else self.sa['username']
            password = password if password else self.sa['password']
            _conn = SSHConnect(host=cvm, username=username, password=password, logger=self.logger)
            self.logger.info("SSH connecting to the CVM.")
            res, ssh = _conn.connect()
            # if res == False, ssh will be the error message
            if not res:
                raise Exception(ssh)

            _conn.invoke_shell()  # invoke a shell in the SSHConnect instance
            _conn.send_command("cluster stop")
            time.sleep(4)
            res, output = _conn.recv()
            if res:
                rep = re.compile(re.escape('I agree'), re.IGNORECASE)
                if not re.search(rep, output):
                    self.logger.error("We didn't get the correct output from 'cluster stop', abort.")
                    raise Exception(
                        f"We didn't get the correct output from 'cluster stop', the output is:{output}, abort.")
            else:
                self.logger.error("We didn't get the correct output from 'cluster stop', abort.")
                raise Exception(f"We didn't get the correct output from 'cluster stop', the output is:{output}, abort.")
            _conn.send_command("I agree")
            time.sleep(4)
            res, output = _conn.recv()
            if res:
                rep = re.compile(re.escape('Operation failed'), re.IGNORECASE)
                if re.search(rep, output):
                    self.logger.error("Operation failed on 'cluster stop', abort.")
                    raise Exception("Operation failed on 'cluster stop', abort.")
            else:
                raise Exception("We didn't get the correct output from 'cluster stop', abort.")
            return True, "Cluster is stopping."
        except Exception as e:
            return False, f"Failed to shutdown the cluster, error: {str(e)}"

    def start_cluster(self, cvm, username=None, password=None):
        self.logger.title("Starting up the cluster.")
        try:
            username = username if username else self.sa['username']
            password = password if password else self.sa['password']
            _conn = SSHConnect(host=cvm, username=username, password=password, logger=self.logger)
            self.logger.info("SSH connecting to the CVM.")
            res, ssh = _conn.connect()
            # if res == False, ssh will be the error message
            if not res:
                raise Exception(ssh)

            _conn.invoke_shell()  # invoke a shell in the SSHConnect instance
            _conn.send_command("cluster start")
            return True, "Cluster is starting up."
        except Exception as e:
            return False, f"Failed to shutdown the cluster, error: {str(e)}"

    def get_cluster_status(self, cvm, pe=None, username=None, password=None):
        self.logger.info("Getting the cluster status.")
        try:
            pe = pe if pe else self.pe
            username = username if username else self.sa['username']
            password = password if password else self.sa['password']
            _conn = SSHConnect(host=cvm, username=username, password=password, logger=self.logger)
            _, _ = _conn.connect()
            # if res == False, ssh will be the error message
            _conn.invoke_shell()  # invoke a shell in the SSHConnect instance
            _conn.send_command('cluster status')
            time.sleep(10)
            _, output = _conn.recv()
            rep_start = re.compile(re.escape('The state of the cluster: start'), re.IGNORECASE)
            rep_stop = re.compile(re.escape('The state of the cluster: stop'), re.IGNORECASE)
            if re.search(rep_start, output):
                return True, "On"
            if re.search(rep_stop, output):
                return True, "Off"
            return True, "Unknown"
        except Exception as e:
            self.logger.error(str(e))
            return False, str(e)

    def get_cluster_detail(self):
        self.logger.info("Getting cluster details.")
        res, data = self.rest_pe.call_pe_get("/cluster", api_version=2)
        if res:
            return data
        raise flaskex.BadGateway(f"Failed to get cluster detail, error:{data}")

    def get_aos_version(self):
        clu = self.get_cluster_detail()
        if version := clu.get("version"):
            return version
        raise Exception("Failed to get AOS version.")

    def check_host_power_state_via_redfish(self, oobip, username, password):
        self.logger.title(f"Checking the power state of {oobip}.")
        try:
            _ilo = Redfish(ip=oobip, username=username, password=password)
            res, state = _ilo.get_ilo_state()
            if not res:  # need to check if ip is pinging, if not, then let's say it's powered off.
                self.logger.error(f"We have error when querying ilo state, error:{state}")
                return False, state
            if state['PowerState'] == "Off":
                self.logger.info("It's off.")
                return True, "Off"
            if state['PowerState'] == "On":
                self.logger.info("It's on.")
                return True, "On"
            self.logger.info(f"It's {state['PowerState']}, but we consider it's off :D.")
            return False, "Off"
        except Exception as e:
            self.logger.error(f"We have error when querying ilo state, error:{state}")
            return False, f"An error occurred when checking the power state: {str(e)}"

    def shutdown_host_via_redfish(self, oobip, username, password):
        self.logger.info(f"Shutting down AHV on {oobip} api now.")
        try:
            _ilo = Redfish(ip=oobip, username=username, password=password)
            res, state = _ilo.change_power_state(state="ForceOff")
            if not res:  # need to check if ip is pinging, if not, then let's say it's powered off.
                return False, state
            return True, 1
        except Exception as e:
            return False, str(e)

    def power_on_host_via_redfish(self, oobip, username, password):
        self.logger.info(f"Powering on AHV on {oobip} api now.")
        try:
            _ilo = Redfish(ip=oobip, username=username, password=password)
            res, state = _ilo.change_power_state(state="On")
            if not res:  # need to check if ip is pinging, if not, then let's say it's powered off.
                return False, state
            return True, 1
        except Exception as e:
            return False, str(e)

    def check_cvm_connection(self, pe_info, retry=120):
        state = [{"name": cvm["name"], "ip": cvm["cvm_ip"], "state": "Off"} for cvm in pe_info]
        try:
            i = 0
            while i < retry and state:
                for cvm in state:
                    self.logger.info(f"Checking {cvm['name']} cvm ip: {cvm['ip']}.")
                    if test_pinging(cvm["ip"]):
                        self.logger.info(f"Good, {cvm['name']} cvm is online.")
                        cvm["state"] = "On"
                state = [_state for _state in state if _state["state"] == "Off"]
                i += 1
                if state and i and i < retry:
                    self.logger.info(f"Not all cvm are online, will retry the {i} time in 10 seconds.")
                    time.sleep(SETTING.PM["QUERY_CVM_INTERVAL_TIME"])

            if state:
                self.logger.error(
                    f"We've been waiting for a long time, but {','.join([_s['name'] for _s in state])} CVM not online, abort.")          # noqa
                return False, f"Warning, {','.join([_s['name'] for _s in state])} CVM not online, please manually check."     # noqa
            self.logger.info("All cvm we got are online now, continue.")
            return True, "All good."
        except Exception as e:
            self.logger.error("An error occurred when checking cvm state.")
            return False, str(e)

    def check_ipmi_connection(self, pe_info, retry=5):
        self.logger.title("Checking IPMI connection state.")
        state = [{"name": ipmi["name"], "ip": ipmi["oob_ip"], "state": "Off"} for ipmi in pe_info]
        try:
            i = 0
            while i < retry and state:
                for ipmi in state:
                    self.logger.info(f"Checking {ipmi['name']} ipmi ip: {ipmi['ip']}.")
                    if test_pinging(ipmi["ip"]):
                        self.logger.info(f"Good, {ipmi['name']} ipmi is online.")
                        ipmi["state"] = "On"
                state = [_state for _state in state if _state["state"] == "Off"]
                i += 1
                if state and i and i < retry:
                    self.logger.info(f"Not all ipmi are online, will retry the {i} time in 3 minutes.")
                    time.sleep(SETTING.PM["QUERY_OOB_INTERVAL_TIME"])

            if state:
                self.logger.error(f"{','.join([_s['name'] for _s in state])} ILO/IPMI not online, abort.")
                return False, f"Warning, {','.join([_s['name'] for _s in state])} ILO/IPMI not online, please kindly ask LIT to power that on."       # noqa
            self.logger.info("All oob we got are online now, continue.")
            return True, "All good."
        except Exception as e:
            self.logger.error("An error occurred when checking ipmi state.")
            return False, str(e)

    def get_lcm_info(self):
        value = {
            ".oid": "LifeCycleManager",
            ".method": "lcm_framework_rpc",
            ".kwargs": {
                "method_class": "LcmFramework",
                "method": "get_config"
            }
        }
        payload = {
            "value": json.dumps(value)
        }
        res, message = self.rest_pe.call_pe_post(request_url="/genesis", payload=payload)
        return res, message

    def get_task_status(self, task_uuid):
        self.logger.info(f"Start to get the status of task {task_uuid} .")
        res, data = self.rest_pe.call_pe_get(
            request_url=f'/progress_monitors?filterCriteria=parent_task_uuid=={task_uuid}')
        if not res:
            raise Exception(f"Failed to get task status! Reason: {data}")
        return data

    def get_network_uuid(self, name):
        self.logger.info(f"Start to get network uuid by network name {name}...")
        res, data = self.rest_pe.call_pe_get(request_url='/networks', api_version=2)
        if not res:
            raise Exception(f"Failed to get networks list! Reason: {data}")
        for e in data["entities"]:
            if e.get("name") == name:
                self.logger.info(f'Network uuid: {e.get("uuid")}')
                return e.get("uuid")
        raise Exception(f"Can't find network with name '{name}'!")

    def if_vm_exist(self, vm):
        self.logger.info(f"Checking if {vm} existed in PE.")
        vm_name = vm.split(".")[0].lower()
        res, vms_list = self.get_vm_list(filter_string=f"sortCriteria=vm_name&searchString={vm}")
        if not res:
            raise flaskex.BadGateway(f"Failed to get the VM list ..... Original error: {vms_list}")
        if [_vm for _vm in vms_list if vm_name == _vm["name"].lower()]:
            self.logger.info(f"{vm} exists in {self.pe}.")
            return False, f"{vm} exists in {self.pe} "
        self.logger.info(f"{vm} doesn't exist in {self.pe}.")
        return True, f"{vm} doesn't exist in {self.pe} "

    def get_network_by_vlan_id(self, vlan_id):
        data = self.get_network_list()
        for network in data:
            if int(network["vlan_id"]) == int(vlan_id):
                self.logger.info(f"Vlan {vlan_id} is found")
                return network
        return False

    def get_image_by_name(self, name):
        self.logger.info(f"Start to find the image {name} from {self.pe}")
        res, data = self.get_image_list()
        if not res:
            raise Exception(f"Failed to get image list! Reason: {data}")
        for image in data:
            if image["name"] == name:
                self.logger.info(f"The image {name} is found in {self.pe}")
                return True, image
        return False, f"The image {name} does not exist in {self.pe}"

    def get_tolerance_status(self):
        res, data = self.rest_pe.call_pe_get(request_url='/cluster/domain_fault_tolerance_status')
        self.logger.info(f"Getting tolerance status from {self.pe}")
        if res and data:
            self.logger.info(f"Got the tolerance status from {self.pe}.")
            return True, data
        self.logger.error(f"Failed to get the tolerance status from {self.pe}. Error message:{data}")
        return False, data

    def upgrade_aos(self, version):
        _payload = {
            ".oid" : "ClusterManager",
            ".method" : "cluster_upgrade",
            ".kwargs" : {
              "nos_version" : version,
              "manual_upgrade" : False,
              "ignore_preupgrade_tests" : False,
              "skip_upgrade" : False
            }
        }
        payload = {"value" : json.dumps(_payload)}
        res, mes = self.rest_pe.call_pe_post(request_url="/genesis", payload=payload)
        if not res:
            self.logger.error(mes)
            raise Exception(mes)
        self.logger.info(mes)
        return True

    def get_task_progress(self):
        pass

    def reset_ilo_via_hostssh(self, facility_type):
        cvm_user, cvm_pass = PrismElement.get_pe_vault_secret(self.pe_fqdn, facility_type=facility_type)
        if not(cvm_user and cvm_pass):
            raise Exception('Either pe nutanix account or password is empty.')
        _ssh = SSHConnect(host=self.pe_fqdn, username=cvm_user, password=cvm_pass)
        _ssh.connect()
        if not _ssh.invoke_shell():
            raise Exception('Failed to invoke shell.')
        command = "hostssh 'ipmitool bmc reset cold'"
        _ssh.send_command(command)
        time.sleep(10)
        _output = _ssh.receive_output()
        if not re.search(r"Sent cold reset command to MC", _output):
            raise Exception('The output seems be weird, is not what we expected...')
        _ssh.ssh.close()
        return _output

    def stop_foundation_via_allssh(self, facility_type):
        cvm_user, cvm_pass = PrismElement.get_pe_vault_secret(self.pe_fqdn, facility_type=facility_type)
        if not(cvm_user and cvm_pass):
            raise Exception('Either pe nutanix account or password is empty.')
        _ssh = SSHConnect(host=self.pe_fqdn, username=cvm_user, password=cvm_pass)
        _ssh.connect()
        if not _ssh.invoke_shell():
            raise Exception('Failed to invoke shell.')
        command = 'allssh "genesis restart;genesis stop foundation"'
        _ssh.send_command(command)
        time.sleep(300)
        _output = _ssh.receive_output()
        _ssh.ssh.close()
        return _output

    def disable_lcm_pem(self, facility_type):
        cvm_user, cvm_pass = PrismElement.get_pe_vault_secret(self.pe_fqdn, facility_type=facility_type)
        if not(cvm_user and cvm_pass):
            raise Exception('Either pe nutanix account or password is empty.')
        _ssh = SSHConnect(host=self.pe_fqdn, username=cvm_user, password=cvm_pass)
        _ssh.connect()
        if not _ssh.invoke_shell():
            raise Exception('Failed to invoke shell.')
        command = 'configure_lcm --disable_pem'
        _ssh.send_command(command)
        time.sleep(20)
        self.logger.info(_ssh.receive_output())
        command = "configure_lcm -p | grep env_params"
        _ssh.send_command(command)
        time.sleep(10)
        _output = _ssh.receive_output()
        _ssh.ssh.close()
        if(re.search(r"'pem':\s+False", _output)):
            self.logger.info("Good, pem is false")
            return True
        self.logger.warning("Emm...looks like pem is not false. here is the output.")
        self.logger.warning(_output)
        return False

    def create_vm(self, payload):
        res, data = self.rest_pe.call_pe_v2_post(request_url='/vms', payload=payload)
        if not res:
            raise flaskex.InternalServerError(f"Create VM API sent failed! Detail: {data}.")
        task_uuid = data["task_uuid"]
        self.logger.info(f"Task created, task_uuid: {task_uuid}")
        return task_uuid

    def update_vm(self, vm_uuid, payload):
        res, data = self.rest_pe.call_pe_put(request_url=f'/vms/{vm_uuid}', payload=payload, api_version = 2)
        if not res:
            raise flaskex.InternalServerError('update failed!')
        task_uuid = data["task_uuid"]
        return task_uuid

    def delete_vm(self, vm_uuid):
        res, data = self.rest_pe.call_pe_delete(request_url=f'/vms/{vm_uuid}')
        if not res:
            raise flaskex.InternalServerError(f"Delete VM API sent failed! Detail: {data}.")
        task_uuid = data["task_uuid"]
        self.logger.info(f"Task created, task_uuid: {task_uuid}")
        return task_uuid

    def get_vm_detail(self, vm_uuid, include_vm_nic_config=False):
        url = f"/vms/{vm_uuid}"
        if include_vm_nic_config:
            url += "?include_vm_nic_config=true"
        res, data = self.rest_pe.call_pe_get(url, api_version=2)
        if not res:
            raise flaskex.BadGateway(f"Failed to get vm detail! Reason: {data}")
        return data

    def get_task_detail(self, task_uuid):
        res, data = self.rest_pe.call_pe_get(f"/tasks/{task_uuid}", api_version=2)
        if not res:
            raise flaskex.BadGateway(f"Failed to get task status! Detail: {data}")
        return data

    def get_remote_site_dr_snapshot(self):
        res, data = self.rest_pe.call_pe_get("/remote_sites/dr_snapshots")
        if res and data:
            return res, data
        self.logger.error("Failed to get snapshots of the remote sites.")
        return False, data

    ######################################################################
    #                                                                    #
    #                          DB operation                              #
    #                                                                    #
    ######################################################################
    @staticmethod
    def get_ahv_list_from_db(page=None, limit=None):
        ahvschema = ModelRetailNutanixHostSchema(many=True)
        ahv_list = ModelRetailNutanixHost.query.all()
        reresult = ahvschema.dump(ahv_list)
        whahvschema = ModelWarehouseNutanixHostSchema(many=True)
        whahv_list = ModelWarehouseNutanixHost.query.all()
        whresult = whahvschema.dump(whahv_list)
        result = reresult + whresult
        total = len(result)
        if page is not None and limit is not None:
            start = (page - 1) * limit
            end = start + limit
            result = result[start:end]
            return jsonify({"data": result, "total": total})
        return jsonify(result)

    @staticmethod
    def get_pc_pe_correspondence_from_db(country=None):
        pe_list = ModelPrismElement.query.order_by('prism').all() + ModelWarehousePrismElement.query.order_by('prism').all()
        from itertools import groupby
        from operator import attrgetter
        pe_list = groupby(pe_list, attrgetter('prism'))
        if country:
            correspondence = []
            retail_pelist_orderpc = ModelPrismElement.query.order_by('prism').all()
            rettail_pelist_bypc = groupby(retail_pelist_orderpc, attrgetter('prism'))
            pclist = [key for key, _ in rettail_pelist_bypc]
            subpequery = db.session.query(
                func.lower(ModelNutanixSeamlessLcmPlannedPEs.pe_fqdn)
            ).filter(
                ModelNutanixSeamlessLcmPlannedPEs.status != SeamlessLcmPlannedPEStatus.CANCELED,
                ModelNutanixSeamlessLcmPlannedPEs.status != SeamlessLcmPlannedPEStatus.ERROR
            ).subquery()
            for pc in pclist:
                filteredpe_list = db.session.query(ModelPrismElement).filter(
                    ~func.lower(ModelPrismElement.fqdn).in_(subpequery),
                    ModelPrismElement.prism == pc,
                    ModelPrismElement.status != 'Decommissioned',
                    ModelPrismElement.node_number > 1).order_by('country_code').all()
                if not filteredpe_list:
                    continue
                retail_pelist = groupby(filteredpe_list, attrgetter('country_code'))
                correspondence.append({
                    "pc": pc,
                    "pe": [{
                        "country": k.upper(),
                        "name": [
                            {
                                "fqdn": i.fqdn.upper(),
                                "aos_version": i.aos_version,
                            } for i in v
                        ],
                    } for k, v in retail_pelist]
                })
        else:
            correspondence = [{"pc": key.upper(), "pe": [v.fqdn.upper() for v in value]} for key, value in pe_list]
        return jsonify(correspondence)

    def get_cvmlist_singlepe_from_db(self):
        _res = []
        pe_cvm = ModelRetailNutanixVM.query.filter_by(pe_name=self.pe.upper()).filter(ModelRetailNutanixVM.name.contains('-CVM')).all()
        pe_vm_schema = ModelRetailNutanixVMSchema()
        vault = Vault(tier="PRODUCTION")
        _, sa_ahv_root = vault.get_secret(f"{self.pe.upper()}/Site_Ahv_Root")
        _, sa_pe_nutanix = vault.get_secret(f"{self.pe.upper()}/Site_Pe_Nutanix")

        for cvm in pe_cvm:
            _cvm = pe_vm_schema.dump(cvm)
            _cvm['ahv_root']   = sa_ahv_root["secret"]
            _cvm['pe_nutanix'] = sa_pe_nutanix["secret"]
            _res.append(_cvm)
        return jsonify(_res)
    
    @staticmethod
    def get_pe_vault_secret(pe, facility_type="retail"):
        if facility_type.lower() == "warehouse":
            pc = ModelWarehousePrismElement.get_prism_by_pe_name(pe)
            tier = ModelWarehousePrismCentral.query.filter_by(fqdn=pc).one().tier
            vault = Vault(tier=tier)
        else:
            pc = ModelPrismElement.get_prism_by_pe_name(pe)
            tier = ModelPrismCentral.query.filter_by(fqdn=pc).one().tier
            vault = Vault(tier=tier)
        _, secret_info = vault.get_secret(f"{pe.split('.')[0].upper()}/{SETTING.PE_VAULT_LABEL['cvm_nutanix']}")
        return secret_info["username"], secret_info["secret"]

    @staticmethod
    def _get_from_db(model, schema, **filter):
        if not filter:
            result = model.query.all()
        else:
            if filter.keys() - model.__table__.columns.keys():
                raise Exception(f"Invalid filter keys: {filter.keys() - model.__table__.columns.keys()}")
            result = model.query.filter_by(**filter).all()
        result_schema = schema(many=True)
        return result_schema.dump(result)

    @staticmethod
    def get_retail_pe_list_from_db(**filter) -> List:
        return PrismElement._get_from_db(ModelPrismElement, ModelPrismElementSchema, **filter)

    @staticmethod
    def get_wh_pe_list_from_db(**filter) -> List:
        return PrismElement._get_from_db(ModelWarehousePrismElement, ModelWarehousePrismElementSchema, **filter)

    @staticmethod
    def get_pe_list_from_db(page=None, limit=None, **filter):
        retail_pe_list = PrismElement.get_retail_pe_list_from_db(**filter)
        warehouse_pe_list = PrismElement.get_wh_pe_list_from_db(**filter)
        combined_pe_list = retail_pe_list + warehouse_pe_list
        total = len(combined_pe_list)
        if page and limit:
            start = (page - 1) * limit
            end = start + limit
            combined_pe_list = combined_pe_list[start:end]
            return {"data": combined_pe_list, "total": total}
        return combined_pe_list
    
    @staticmethod
    def get_retail_vm_list_from_db(**filter):
        return PrismElement._get_from_db(ModelRetailNutanixVM, ModelRetailNutanixVMSchema, **filter)

    @staticmethod
    def get_wh_vm_list_from_db(**filter):
        return PrismElement._get_from_db(ModelWarehouseNutanixVM, ModelWarehouseNutanixVMSchema, **filter)

    @staticmethod
    def get_vm_list_from_db(**filter):
        return PrismElement.get_retail_vm_list_from_db(**filter) + PrismElement.get_wh_vm_list_from_db(**filter)


class NutanixOperation():
    def __init__(self, pc, pe, facility_type="retail", logger=logging):
        self.facility_type = facility_type
        if self.facility_type == "retail":
            self.model_pe = ModelPrismElement
            self.model_pc = ModelPrismCentral
        elif self.facility_type == "warehouse":
            self.model_pe = ModelWarehousePrismElement
            self.model_pc = ModelWarehousePrismCentral
        self.pc = pc.lower()  # something like ssp-eu-ntx.ikea.com
        rep = re.compile(r'\.(ikea|ikeadt|ikead2)\.com', re.IGNORECASE)
        if re.search(rep, pe):  # pe is like an fqdn  :"RETCN888-NXC000.ikea.com"
            self.pe_fqdn = pe
            self.pe = rep.sub("", self.pe_fqdn)
        else:  # pe is not like an fqdn: "RETCN888-NXC000"
            self.pe = pe
            self.pe_fqdn = self.model_pe.query.filter(and_(self.model_pe.name.like('%'+pe+'%'), self.model_pe.prism == self.pc)).first().fqdn
        self.logger = logger
        # self.logger.info(f"Getting service account for usage:(nutanix_pm)")
        pc_info = self.model_pc.query.filter_by(fqdn=self.pc).first()
        self.tier = pc_info.tier
        if self.tier == "WAREHOUSE":
            self.svc_tag = "production"
        elif self.tier == "WAREHOUSEDT":
            self.svc_tag = "ikeadt"
        else:
            self.svc_tag = self.tier
        self.sa = ServiceAccount(usage=f"nutanix_opt_{self.svc_tag.lower()}").get_service_account()
        self._prism = PrismCentral(self.pc, self.sa, self.logger)

    def if_cluster_exists(self):
        self.logger.info(f"Checking if cluster '{self.pe}' exists in {self.pc}.")
        if not (self.sa['username'] and self.sa['password']):
            self.logger.error("Failed to start the PM due to lack of service account or password.")
            return False, "cannnot find service account or the password."
        self.logger.info("Getting the cluster list from PC.")
        res, data = self._prism.get_cluster_list()
        # (True, [{'name': 'retse999-nxp001'}, {'name': 'RETCN888-NXC000'}, {'name': 'RETSE999-NXC000'}]
        existing_flag = False  # return this to show , if the cluster is existing
        if res and isinstance(data, list) and data:  # if data is not list, or data is null, return false
            matched_cluster = []
            self.logger.info(
                f"Got the cluster list from PC, searching the target cluster {self.pe} in the list."
            )
            for cluster in data:
                try:
                    # if cluster['name'] == self.cluster:
                    if re.search(f"{self.pe}", cluster['name'], re.IGNORECASE):
                        self.logger.info(f"{self.pe} exists in {self.pc}, continue.")
                        matched_cluster.append(cluster['name'])
                        existing_flag = True
                except Exception:
                    pass
            return existing_flag, (matched_cluster if existing_flag else "Didn't find the cluster in the list.")
        self.logger.error(data)
        self.logger.error("Could not get the cluster list from PC.")
        return False, 'Erorr, could not get the cluster list from PC.'

    def get_pe_info(self, cluster=None, pc=None, sa=None, frompc=False):    # pylint: disable=W0613
        # get cvm/ahv/oob ip/fqdn/credential
        try:
            if not sa:
                sa = self.sa
            if not frompc:
                _pe = PrismElement(pe=self.pe_fqdn, sa=sa, logger=self.logger)
                # get the host list first, including ahv/cvm/ipmi ip address
                res, host_list = _pe.get_host_list(pc=self.pc)
            else:
                _pc = PrismCentral(pc=self.pc, sa=sa, logger=self.logger)
                res, host_list = _pc.get_host_list_in_pe(
                    cluster=self.pe.upper())  # need to pass the pe name with out domain in captical case
            if res:
                self.logger.info("Got the host list.")
                if self.tier:
                    # if tier was specified
                    self.logger.info(f"Getting vault from {self.tier}.")
                    _vault = Vault(tier=self.tier)
                else:
                    self.logger.info("Getting vault from dist-host-retail/dhprod.")
                    _vault = Vault()
                # loop the host list , add vault of cvm/oob/ahv
                for key, item in SETTING.PE_VAULT_LABEL.items():
                    self.logger.info(f"Getting {item}.")
                    res, secret = _vault.get_secret(f"{self.pe.upper()}/{item}")
                    if res:
                        self.logger.info(f"Got {item}.")
                        for _h in host_list:
                            _h[key] = {"username": secret["username"], "password": secret["secret"]}  # type: ignore
                    else:
                        self.logger.error(f"Failed to get {item}, quiting now.")
                        raise Exception(f"Failed to get {item}.")
                return True, host_list
            self.logger.error("Failed to get the host list, quiting now.")
            raise Exception(f'Failed to get the host list from {self.pe}.')
        except Exception as e:
            self.logger.error(str(e))
            return False, str(e)

    def get_pe_info_by_fqdn(self):
        pass

    @staticmethod
    def get_pe_by_fqdn_from_db(fqdn, return_facility_type=False):
        facility_type = None
        try:
            pe = ModelPrismElement.query.filter_by(fqdn=fqdn).one()
            facility_type = FacilityType.RETAIL
        except sqlalchemy.exc.NoResultFound:
            pe = ModelWarehousePrismElement.query.filter_by(fqdn=fqdn).one()
            facility_type = FacilityType.WAREHOUSE
        if return_facility_type:
            return pe, facility_type
        return pe
    
    @staticmethod
    def get_pe_by_name_from_db(name, return_facility_type=False):
        facility_type = None
        try:
            pe = ModelPrismElement.query.filter_by(name=name).one()
            facility_type = FacilityType.RETAIL
        except sqlalchemy.exc.NoResultFound:
            pe = ModelWarehousePrismElement.query.filter_by(name=name).one()
            facility_type = FacilityType.WAREHOUSE
        if return_facility_type:
            return pe, facility_type
        return pe
    
    @staticmethod
    def get_host_by_uuid_from_db(uuid, return_facility_type=False):
        facility_type = None
        try:
            host = ModelRetailNutanixHost.query.filter_by(uuid=uuid).one()
            facility_type = FacilityType.RETAIL
        except sqlalchemy.exc.NoResultFound:
            host = ModelWarehouseNutanixHost.query.filter_by(uuid=uuid).one()
            facility_type = FacilityType.WAREHOUSE
        if return_facility_type:
            return host, facility_type
        return host
    ##########################################################


class NutanixCLI():
    def __init__(self, pe, logger, pc=None, ntx_sa=None, vault=None):
        rep = re.compile(r'\.(ikea|ikeadt|ikead2)\.com', re.IGNORECASE)
        if re.search(rep, pe):  # pe is like an fqdn  :"RETCN888-NXC000.ikea.com"
            self.pe_fqdn = pe
            self.pe = rep.sub("", self.pe_fqdn)
        else:  # pe is not like an fqdn: "RETCN888-NXC000"
            self.pe = pe
            self.pe_fqdn = f"{self.pe}.ikea.com"
        self.logger = logger
        # self.logger.info(f"the service account name is {self.sa['username']}")
        if vault:
            self.vault = vault
        else:
            self.pc = pc.lower()  # something like ssp-eu-ntx.ikea.com
            # self.logger.info(f"Getting service account for usage:(nutanix_pm)")
            self._prism = PrismCentral(pc=self.pc, sa=ntx_sa, logger=self.logger)
            self.tier = self._prism.get_prism_by_name_from_db(fqdn=self.pc)['tier']
            self.vault = Vault(tier=self.tier)
        res, nutanix_account = self.vault.get_secret(f"{self.pe.upper()}/Site_Pe_Nutanix")
        if not res:
            raise Exception(nutanix_account)
        self.site_nutanix_account = nutanix_account
        # res, ssh_private_key = self.vault.get_secret(f"{self.pe.upper()}/Site_Gw_Priv_Key")
        # if not res:
        #     raise Exception(ssh_private_key)
        # self.site_ssh_private_key = ssh_private_key
        _res, key_string = self.vault.get_secret(f"{self.pe.upper()}/Site_Gw_Priv_Key")
        public_key = None
        if isinstance(key_string, dict) and key_string.get('secret', '').startswith('-----BEGIN RSA PRIVATE KEY-----'):
            public_key = paramiko.RSAKey(file_obj=StringIO(key_string['secret']))
        self.ssh = SSHConnect(host=pe, username=self.site_nutanix_account['username'], password=self.site_nutanix_account['secret'], public_key=public_key, logger=logger)

    def secure_connect(func):
        @functools.wraps(func)
        def sec(self, *args, **kwargs):
            self.logger.info(f"Trying to SSH to the pe {self.pe}.")
            res, mes = self.ssh.connect()
            if not res:
                res, _ = self.ssh.connect_sshkey()
                if not res:
                    raise flaskex.InternalServerError(f"Failed to connect to {self.pe}. error:{mes}.")
            return func(self, *args, **kwargs)
        return sec

    @secure_connect
    def get_ncli_host(self):
        if ncli_host := self.ssh.exec_command("/home/<USER>/prism/cli/ncli host list --json=pretty"):
            return json.loads(ncli_host)
        raise Exception("Failed to get ncli host list.")

    @secure_connect
    def get_acli_host(self):
        if acli_host := self.ssh.exec_command("/usr/local/nutanix/bin/acli -o json host.list"):
            return json.loads(acli_host)
        raise Exception("Failed to get acli host list.")

    @secure_connect
    def get_ncli_software_status(self, version, software="NOS"):
        if acli_host := self.ssh.exec_command(f"/home/<USER>/prism/cli/ncli software list software-type={software} name={version}"):
            return acli_host.decode() if isinstance(acli_host, bytes) else acli_host
        raise Exception("Failed to get the software version.")

    @secure_connect
    def clean_folder(self, path):
        if isinstance(path, str):
            path = [path]
        elif isinstance(path, list):
            pass
        else:
            raise Exception("Path is neither string or list, undoable.")
        for _p in path:
            self.logger.info(f"Cleaning {_p}.")
            self.ssh.exec_command(f"sudo rm {_p}")

    @secure_connect
    def download_file(self, path, url, log_func=None):
        #log_func is the function I use to write DB log, can be None if no need to write DB log
        try:
            command = f"cd {path};wget --continue --tries=100 --retry-connrefused --read-timeout=20 --waitretry=30 {url}"
            if self.ssh.invoke_shell() and self.ssh.send_command(command):
                time.sleep(10) # let the download start
            else:
                self.logger.error("Failed invoke the ssh shell or send the command.")
                raise Exception("Failed invoke the ssh shell or send the command, download didn't start.")
            while True:
                res, output = self.ssh.recv(write_log=False)
                if res and output:
                    output_list = output.split('\r')
                    percentage_list = [_p for _p in output_list if re.search("\d*%", _p)]
                    if log_func and percentage_list:
                        log_func(percentage_list[-1])
                    # for _o in output_list:
                    #     if _s := re.search("\d*%", _o):
                    #         log_func(_o)
                    time.sleep(30)
                else:
                    return
        except Exception:
            self.logger(str(repr(traceback.format_exception(sys.exception()))))
            return False

    @secure_connect
    def upload_ncli_software(self, binary, meta, software="NOS"):
        command = "/home/<USER>/prism/cli/ncli software upload "
        command += f"software-type={software} file-path=/home/<USER>/tmp/{binary} meta-file-path=/home/<USER>/tmp/{meta}"
        if self.ssh.invoke_shell() and self.ssh.send_command(command):
            time.sleep(10) # let the upload start
        else:
            self.logger.error("Failed invoke the ssh shell or send the command.")
            raise Exception("Failed invoke the ssh shell or send the command, upload didn't start.")
        for i in range(10):
            #we wait for 5 minutes
            res, output = self.ssh.recv()
            if res and output:
                if re.search("completed", output):
                    return True
                if re.search("Error", output):
                    raise Exception(output)
            i += 1
            time.sleep(30)
        return False

    @secure_connect
    def get_acli_vm_group(self, group_name):
        self.logger.info(f"Fetching ACI VM Group by name: '{group_name}'")
        if output := self.ssh.exec_command("/usr/local/nutanix/bin/acli -o json vm_group.list"):
            group = json.loads(output)["data"]
        else:
            raise flaskex.InternalServerError("Failed to get acli vm group!")
        # output:
        # {
        #   "status": 0,
        #   "data": [
        #       {"name": "LIP", "uuid": "726ebd57-d1e9-4fb5-a3f5-c5c603b53a3b"},
        #       {"name": "PaloAlto", "uuid": "8fa85674-9292-46fe-8031-957ecc8676f8"}
        #   ],
        #   "error": null
        # }

        filtered_groups = [g for g in group if g.get("name") == group_name]
        group_count = len(filtered_groups)
        if group_count > 1:
            self.logger.warning(
                f"{group_count} groups detected with name '{group_name}'. Returning the first one in the array.")
            selected_group = filtered_groups[0]
        elif group_count == 1:
            self.logger.info(f"1 Group found with name '{group_name}'.")
            selected_group = filtered_groups[0]
        else:
            self.logger.info(f"No group(s) found with name '{group_name}'.")
            selected_group = None
        return selected_group

    @secure_connect
    def new_acli_vm_group(self, group_name):
        self.ssh.exec_command(f"/usr/local/nutanix/bin/acli -o json vm_group.create {group_name}")

    @secure_connect
    def set_acli_group_affinity(self, group_uuid):
        self.logger.info(f"Configuring ACLI VM Group: '{group_uuid}' for Anti Affinity!")
        self.ssh.exec_command(f"/usr/local/nutanix/bin/acli -o json vm_group.antiaffinity_set {group_uuid}")

    @secure_connect
    def add_acli_vm_to_group(self, group, vm_name):
        self.logger.info(f"Adding '{vm_name}' via ACI towards VM Group: '{group.get('name')} UUID: {group.get('uuid')}'")
        self.ssh.exec_command(f"/usr/local/nutanix/bin/acli -o json vm_group.add_vms {group.get('uuid')} vm_list={vm_name}")

    @secure_connect
    def unlock_account(self, prism_name, pc_model_class, pe_model_class):
        accounts = ['admin', 'nutanix', '1-click-nutanix']
        pe_prism_info = pe_model_class.query.filter_by(name=prism_name).first()
        pc_prism_info = pc_model_class.query.filter_by(fqdn=self.pc).first()
        
        if not pe_prism_info or not pc_prism_info:
            raise Exception(f"Cannot find PE or PC with name: {prism_name}")
        
        vault = Vault(tier=pc_prism_info.tier)
        
        def unlock_accounts(host, username, password, key_string, account_type="PE"):
            public_key = paramiko.RSAKey(file_obj=StringIO(key_string[1]['secret']))
            ssh = SSHConnect(
                host=host,
                username=username,
                password=password,
                public_key=public_key,
                logger=self.logger
            )
            res, _ = ssh.connect()
            if not res:
                res_sshkey, _ = ssh.connect_sshkey()
                if not res_sshkey:
                    raise SSHFailed(host)
            ssh.invoke_shell()
            for account in accounts:
                self.logger.info(f"Unlocking {account_type} account: '{account}'")
                ssh.send_command(f"allssh sudo faillock --user {account} --reset")
                time.sleep(5)
                self.logger.info(f"Successfully unlocked {account_type} account: '{account}'")
        
        # Unlock PC accounts if PE is central
        if pe_prism_info.is_central_pe:
            central_pe = pc_prism_info.central_pe_fqdn.split('.')[0]
            _res, pc_nutanix_account = vault.get_secret(f"{central_pe.upper()}/Site_Pc_Nutanix")
            pc_key_string = vault.get_secret(f"{central_pe.upper()}/Site_Gw_Priv_Key")
            unlock_accounts(
                host=pc_prism_info.fqdn,
                username=pc_nutanix_account['username'],
                password=pc_nutanix_account['secret'],
                key_string=pc_key_string,
                account_type="PC"
            )
        
        # Unlock PE accounts
        _res, pe_nutanix_account = vault.get_secret(f"{prism_name.upper()}/Site_Pe_Nutanix")
        pe_key_string = vault.get_secret(f"{prism_name.upper()}/Site_Gw_Priv_Key")
        unlock_accounts(
            host=pe_prism_info.fqdn,
            username=pe_nutanix_account['username'],
            password=pe_nutanix_account['secret'],
            key_string=pe_key_string,
            account_type="PE"
        )


class AllPrism():
    def __init__(self) -> None:
        pass
    
    @classmethod
    def get_vm_list(cls, page=None, limit=None):
        vmschema = ModelRetailNutanixVMSchema(many=True)
        vm_list = ModelRetailNutanixVM.query.filter_by(status="Available").all()
        result = vmschema.dump(vm_list)
        # add pc field...
        peschema = ModelPrismElementSchema(many=True)
        pe_list = ModelPrismElement.query.all()
        pe = peschema.dump(pe_list)
        whvmschema = ModelWarehouseNutanixVMSchema(many=True)
        whvm_list = ModelWarehouseNutanixVM.query.all()
        whresult = whvmschema.dump(whvm_list)
        whahvschema = ModelWarehousePrismElementSchema(many=True)
        whahv_list = ModelWarehousePrismElement.query.filter_by(status='Running').all()
        whpe = whahvschema.dump(whahv_list)
        cor = {}
        for _pe in pe:
            cor[_pe["name"]] = _pe["prism"]
        for _pe in whpe:
            cor[_pe["name"]] = _pe["prism"]
        def temp(x):
            if 'pe_name' in x.keys():
                if x['pe_name'] in cor.keys():
                    return {**{"prism": cor[x['pe_name']]}, **x}
                return {**{"prism": "N/A"}, **x}
            return {**{"prism": "N/A"}, **x}
    
        all_result = list(map(temp, result)) + list(map(temp, whresult))
        total = len(all_result)
        if page is not None and limit is not None:
            start = (page - 1) * limit
            end = start + limit
            all_result = all_result[start:end]
            return jsonify({"data": all_result, "total": total})
        return jsonify(all_result)


class NtxHost:
    def get_all_host_info_from_db(self):
        return ModelRetailNutanixHostSchema(many=True).dump(ModelRetailNutanixHost.query.all())


class VM_Check(object):     # pylint: disable=C0103
    def __init__(self, pe, sa=None, logger=None, vm=None):
        self.server_name = vm
        self.logger = logger
        self.sa = sa
        if not self.sa:
            self.sa = ServiceAccount(usage="nutanix_pm").get_service_account()
        self.pe, self.pe_fqdn = CommonHelper.parse_pe_name_and_fqdn(pe)
        self.rest_pe = NutanixAPI(
            pe=self.pe,
            username=self.sa["username"],
            password=self.sa["password"],
            logger=self.logger,
        )
        self.thors_hammer = ThorsHammerAPI(logger=self.logger)

    def get_nic_uuid(self):
        try:
            res, data = self.rest_pe.call_pe_get(request_url="/vms")
            vm_name = self.server_name.split(".")[0].lower()
            if res and data:
                target_vm = [_vm for _vm in data["entities"] if vm_name == _vm["vmName"].lower()]
                if target_vm:
                    target_vm_uuid = target_vm[0]["uuid"]
                    res, data = self.rest_pe.call_pe_get(
                        request_url=f"/vms/{target_vm_uuid}/nics", api_version=2
                    )
                    if res and data:
                        # return True, data["entities"][0]["nic_uuid"], target_vm_uuid
                        return True, data["entities"], target_vm_uuid
                    return False, f'Failed to get the Nic_uuid.{"nic_uuid": "N/A"}'
                return False, f'Failed to get the Target Vm.{"target_vm": "N/A"}'
            return False, f'Failed to get the VM list.{"target_vm": "N/A"}'
        except Exception as e:
            self.logger.error(f"Failed to get the VM uuid for {self.server_name}. Error: {e}")
            return False, f"Failed to get the VM uuid for {self.server_name}. Error: {e}"

    def set_limitedvlan_tonic(self, mac_address, vlanlist=None, pe=None, facility_type="retail"):
        try:
            model_pe = ModelPrismElement
            model_pc = ModelPrismCentral
            if facility_type == "warehouse":
                model_pe = ModelWarehousePrismElement
                model_pc = ModelWarehousePrismCentral
            pe = pe if pe else self.pe
            pc = model_pe.query.filter_by(fqdn=self.pe_fqdn).one().prism
            tier = model_pc.query.filter_by(fqdn=pc).one().tier
            vm_name = self.server_name.split(".")[0].upper()
            _, secret_info = Vault(tier).get_secret(
                f"{pe.split('.')[0].upper()}/{SETTING.PE_VAULT_LABEL['cvm_nutanix']}")
            username, password = secret_info["username"], secret_info["secret"]
            _res, key_string = Vault(tier).get_secret(f"{self.pe.upper()}/Site_Gw_Priv_Key")
            public_key = None
            if isinstance(key_string, dict) and key_string.get('secret', '').startswith('-----BEGIN RSA PRIVATE KEY-----'):
                public_key = paramiko.RSAKey(file_obj=StringIO(key_string['secret']))
            _conn = SSHConnect(
                host=pe.lower(),
                username=username,
                password=password,
                public_key=public_key,
                logger=self.logger
            )
            res, _ = _conn.connect()
            # if res == False, ssh will be the error message
            if not res:
                res_sshkey, _ = _conn.connect_sshkey()
                if not res_sshkey:
                    raise flaskex.InternalServerError(f"Failed to connect to {self.pe}!")
            _conn.invoke_shell()  # invoke a shell in the SSHConnect instance
            if not vlanlist:
                _conn.send_command(
                    f"acli vm.nic_update {vm_name} {mac_address} vlan_mode=kTrunked update_vlan_trunk_info=true"
                )
                time.sleep(10)
            else:
                _conn.send_command(
                    f"acli vm.nic_update {vm_name} {mac_address} trunked_networks={vlanlist} vlan_mode=kTrunked update_vlan_trunk_info=true"
                )
                time.sleep(10)
            res, output = _conn.recv()
            rep_complete = re.compile(re.escape(" complete"), re.IGNORECASE)
            if re.search(rep_complete, output):
                self.logger.info("Completed update nic mode to trunked")
                return True, "Completed"
        except Exception as e:
            self.logger.error(str(e))
            return False, str(e)

    def add_nic_vm(self, vm_uuid, vlanids):
        _pe = PrismElement(self.pe, sa=self.sa, logger=self.logger)
        self.logger.info(f"Let's power off {self.server_name} first")
        _pe.set_vm_power_state(vm_uuid, "off", self.server_name)
        time.sleep(10)
        for vlanid in vlanids:
            data = _pe.get_network_by_vlan_id(vlan_id=vlanid)
            if data:
                self.logger.info(f"The network is found, the UUID is {data['uuid']}")
                network_uuid = data['uuid']
            else:
                network_pe_name = self.pe+"-"+str(vlanid)+"-Servers"
                self.logger.info(f"The network does not exist, try to create with the name {self.server_name}")
                res, data = _pe.add_network(name=network_pe_name, vlan_id=vlanid)
                if not res:
                    raise Exception(f"Failed to create the network {network_pe_name},error:{data}")
                network_uuid = data['network_uuid']
            payload = {
                    "spec_list": [
                        {
                            "network_uuid": network_uuid,
                            "is_connected": "true",
                            "vlan_id": vlanid
                        }
                    ]
                }
            self.logger.info(f"start add {vlanid} for {self.server_name}!")
            res, data = _pe.rest_pe.call_pe_v2_post(request_url=f'/vms/{vm_uuid}/nics', payload=payload)
            if not res:
                raise flaskex.InternalServerError('update failed!')
            task_uuid = data["task_uuid"]
            res = _pe.is_task_succeeded(task_uuid=task_uuid, retries=1)
            if not res:
                raise flaskex.InternalServerError("update failed")
        self.logger.info(f"Great, nic added. Let's start VM {self.server_name} ")
        _pe.set_vm_power_state(vm_uuid, "on", self.server_name)

    def set_nic_trunked(self, trunk_id, facility_type, pe=None, ):
        try:
            pe = pe if pe else self.pe
            self.logger.info("Start to get network uuid by vlanid...")
            res, data = self.rest_pe.call_pe_get(request_url="/networks", api_version=2)
            if not res:
                raise Exception(f"Failed to get networks list! Reason: {data}")
            fwvlan_uuid = [
                e.get("uuid") for e in data["entities"] if e.get('vlan_id') == trunk_id
            ]
            if not fwvlan_uuid:
                raise Exception(f"No matched FW vlan on cluster {pe}")
            res, targetnic_list, _ = self.get_nic_uuid()
            fwnic_mac = None
            for uuid in fwvlan_uuid:
                for e in targetnic_list:
                    if uuid == e["network_uuid"]:
                        fwnic_mac = e.get("mac_address")
                        break
            if not fwnic_mac:
                raise Exception(f"No matched FW NIC on VM {self.server_name}")
            rep_trunk = self.set_limitedvlan_tonic(mac_address=fwnic_mac, facility_type=facility_type)
            if not rep_trunk:
                raise Exception(f"Failed to set the trunk on NIC {self.server_name}")
            return True, f"Successed set the trunk on NIC {self.server_name}"
        except Exception as e:
            self.logger.error(str(e))
            return False, str(e)

    # def set_nic_trunked(self):
    #     res, targetnic_uuid, target_vm_uuid = self.get_nic_uuid()
    #     if not res:
    #         raise Exception(f"Failed to get the uuid for {self.server_name}.")
    #     payload = {"nic_spec": {"vlan_mode": "Trunked"}}
    #     updateres, data = self.rest_pe.call_pe_v2_put(
    #         request_url=f"/vms/{target_vm_uuid}/nics{targetnic_uuid}", payload=payload
    #     )
    #     if updateres:
    #         return True, f"update the '{self.server_name}' successfully !"
    #     else:
    #         return False, f"Failed to update the '{self.server_name}' !"

    def add_profiles_tovm(self, profiles_name: list, server_name):
        # profile_name
        try:
            res = [
                self.thors_hammer.add_host_profile(
                    self, server_name, profile_name=profile_name
                )
                for profile_name in profiles_name
            ]
            if res:
                return True, res.split(",")
        except Exception:
            return False, f'Failed to delete machine. Error: {res.json()["Error"]}'

    def new_singleprofile(self, profile_name, prefix, profile_description, profile_type="Alias"):
        full_profilename = profile_type + f"%20{profile_name}"
        res = self.thors_hammer.new_profile(prefix, full_profilename, profile_description)
        if not res.json()["Success"]:
            raise Exception(f'Failed to delete machine. Error: {res.json()["Error"]}')
        return True, f"Successed to Create the profile {profile_name}"

    def new_standardedprofile(self, prefix, profiletype):
        res = self.thors_hammer.make_profile(prefix, profiletype)
        if not res.json()["Success"]:
            raise Exception(f'Failed to new {profiletype} profile. Error: {res.json()["Error"]}')
        return True, f"Succeeded to new {profiletype} profile"

    def create_affinity_policy(self, server_name: list, pe=None, username=None, password=None):
        try:
            pe = pe if pe else self.pe
            username = username if username else self.sa["username"]
            password = password if password else self.sa["password"]
            affinity_groupname = server_name[0] + "_antiaffinity"
            _conn = SSHConnect(
                host=pe, username=username, password=password, logger=self.logger
            )
            res, _ = _conn.connect()
            # if res == False, ssh will be the error message
            if not res:
                raise Exception(f"Failed to connect {pe} with SSH.")
            _conn.invoke_shell()  # invoke a shell in the SSHConnect instance
            _conn.send_command(f"acli vm_group.create {affinity_groupname}")
            time.sleep(10)
            _conn.send_command(
                f"acli vm_group.add_vms group-name vm_list={server_name}"
            )
            time.sleep(10)
            _conn.send_command(f"acli vm_group.antiaffinity_set {affinity_groupname}")
            res, output = _conn.recv()
            rep_complete = re.compile(
                re.escape(f"{affinity_groupname}: complete"), re.IGNORECASE
            )
            if re.search(rep_complete, output):
                return True, "Completed"
            return True, "Completed"
        except Exception as e:
            self.logger.error(str(e))
            return False, str(e)

    def get_targetvm_uuid(self):
        try:
            res, data = self.rest_pe.call_pe_get(request_url="/vms")
            vm_name = self.server_name.split(".")[0].lower()
            if res and data:
                target_vm = [_vm for _vm in data["entities"] if vm_name == _vm["vmName"].lower()]
                if target_vm:
                    target_vm_uuid = target_vm[0]["uuid"]
                    return True, target_vm_uuid,
                return False, 'Failed to get the Target Vm.{"target_vm": "N/A"}'
            return False, 'Failed to get the VM list.{"target_vm": "N/A"}'
        except Exception as e:
            self.logger.error(f"Failed to get the VM uuid for {self.server_name}. Error: {e}")
            return False, f"Failed to get the VM uuid for {self.server_name}. Error: {e}"

    def add_vm_category(self, pc, category_name, category_value):
        try:
            target_vm_uuid = self.get_targetvm_uuid()
            rest_pc = NutanixAPI(pc=pc, username=self.sa["username"], password=self.sa["password"], logger=self.logger)
            payload = {
                "metadata": {
                    "kind": "vm",
                    "categories": {
                        f"{category_name}": f"{category_value}"
                    },
                    "categories_mapping": {
                        f"{category_name}" : [f"{category_value}"]
                    }
                },
                "spec": {
                    # "name":f"{self.server_name}",
                    "resources": {
                        "hardware_clock_timezone": "CET",
                        "power_state": "ON"
                    }
                }
            }
            res, _ = rest_pc.call_pc_post(request_url=f"/vms/{target_vm_uuid}", payload=payload)
            if res:
                return True, f"Successed add the {self.server_name} into category {category_name}:{category_value}"
            return False, f"Failed to add the {self.server_name} into category."
        except Exception as e:
            self.logger.error(f"Failed to get the VM uuid for {self.server_name}. Error: {e}")
            return False, f"Failed to add the {self.server_name} into category. Error: {e}"

    def create_orupdate_category(self, pc, category_name):
        try:
            rest_pc = NutanixAPI(pc=pc, username=self.sa["username"], password=self.sa["password"], logger=self.logger)
            res, data = rest_pc.call_pc_v3_put(request_url="/categories/{category_name}")
            if res and data:
                return True, f"Successed to create the category : {category_name}"
            return False, f'Failed to create the category : {category_name}'
        except Exception as e:
            self.logger.error(f"Failed to create the category {category_name}. Error: {e}")
            return False, f"Failed to create the category : {category_name}. Error: {e}"

    def create_orupdate_categoryvalue(self, pc, category_name, category_value):
        try:
            rest_pc = NutanixAPI(pc=pc, username=self.sa["username"], password=self.sa["password"], logger=self.logger)
            res, data = rest_pc.call_pc_v3_put(request_url="/categories/{category_name}/{category_value}")
            if res and data:
                return True, f"Successed to create the category : {category_name}:{category_value}"
            return False, f'Failed to create the category : {category_name}:{category_value}'
        except Exception as e:
            self.logger.error(f"Failed to create the category {category_name}:{category_value}. Error: {e}")
            return False, f"Failed to create the category : {category_name}:{category_value}. Error: {e}"


class Ntx_Move(object):     # pylint: disable=C0103
    def __init__(self, pe=None, sa=None, sli_sa=None, optoken=None, move_winsa=None, move_lxsa=None, logger=logging) -> None:
        _sa = sa if sa else ServiceAccount(usage="nutanix_pm").get_service_account()
        self._sli_sa = sli_sa if sli_sa else ServiceAccount(usage="simplivity_pm").get_service_account()
        self.rest = CommonRestCall(username=_sa['username'], password=_sa['password'])
        self.optoken = optoken if optoken else ServiceAccount(usage="1ptoken").get_service_account()
        _wh_sa = ServiceAccount(usage="wh_vcenter")
        self.whsa = _wh_sa.get_service_account()
        # _move_winsa = ServiceAccount(usage="ntx_move_win")
        self.move_winsa = move_winsa if move_winsa else ServiceAccount(usage="ntx_move_win").get_service_account()
        self.move_lxsa = move_lxsa if move_lxsa else ServiceAccount(usage="ntx_move_lx").get_service_account()
        self.move_sa = move_winsa if move_winsa else ServiceAccount(usage="ntx_move").get_service_account()
        self.sa = _sa
        self.logger = logger
        if pe:
            rep = re.compile(r'\.(ikea|ikeadt|ikead2)\.com', re.IGNORECASE)
            if re.search(rep, pe):  # pe is like an fqdn  :"RETCN888-NXC000.ikea.com"
                self.pe_fqdn = pe
                self.pe = rep.sub("", self.pe_fqdn)
            else:  # pe is not like an fqdn: "RETCN888-NXC000"
                self.pe = pe
                self.pe_fqdn = f"{self.pe}.ikea.com"
            self.rest_pe = NutanixAPI(
                pe=self.pe,
                username=self.sa["username"],
                password=self.sa["password"],
                logger=self.logger,
            )

    def login_move(self, move_username, move_password, move_ip):
        loginpayload = {
            "Spec": {
                "UserName": move_username,
                "Password": move_password
            }
        }
        url = f"https://{move_ip}/move/v2/users/login"
        self.logger.info("Trying to log on the move application...")
        res = self.rest.call_restapi(url, method="POST", payload=loginpayload)
        if not res:
            self.logger.error("Failed to log on the move application.")
            raise Exception('Failed to log on the MOVE.')
        self.logger.info("Successed to log on the move application.")
        return True, res.json()["Status"]["Token"]

    def create_provider(self, move_token, provider_type, move_ip, vc=None): #"Type": "ESXI","Type": "AOS",
        headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        url = f"https://{move_ip}/move/v2/providers"
        if provider_type == "ESXI":
            self.logger.info("Trying to get the FED password from 1 Password.")
            if vc == "itseelm-bb4441.ikea.com":
                fed_username = self.whsa['username']
                fed_upassword = self.whsa['password']
            else:
                fed_rest = self.get_slifed_pw(vc)
                fed_username = [item["value"] for item in fed_rest["fields"] if item["id"] == "username"][0]
                fed_upassword = [item["value"] for item in fed_rest["fields"] if item["id"] == "password"][0]
            self.logger.info("Successed to get the FED password from 1 Password.")
            provider_name = (self.pe).split("-")[0]
            esxi_payload = {
                "Spec": {
                    "ESXAccessInfo": {
                    "IPorFQDN": vc,
                    "Username": fed_username,
                    "Password": fed_upassword
                    },
                    "Type": provider_type,
                    "Name": provider_name
                }
            }
            self.logger.info("Trying to create ESXI provider.")
            res = self.call_moveapi(url, method="POST", payload=esxi_payload, headers=headers)
        else:
            self.logger.info("Trying to get the Admin password from Vault.")
            rep = re.compile(re.escape("ds"), re.IGNORECASE)
            if self.pe_fqdn == "RETCN888-NXC000.IKEA.COM":
                _vault = Vault(tier="PREPRODUCTION")
            elif re.search(rep, self.pe_fqdn):
                _vault = Vault(tier="WAREHOUSE")
            else:
                _vault = Vault(tier="PRODUCTION")
            res, secret = _vault.get_secret(f"{self.pe.upper()}/Site_Pe_Admin")
            if not res:
                self.logger.error("Failed to get the Admin password from Vault.")
                raise Exception("Failed to get Site_Pe_Admin.")
            aos_payload = {
                "Spec": {
                    "AOSAccessInfo": {
                    "IPorFQDN": self.pe_fqdn,
                    "Username": "admin",
                    "Password": secret["secret"]
                    },
                    "Type": provider_type,
                    "Name": self.pe
                }
            }
            self.logger.info("Trying to create AOS provider.")
            res = self.call_moveapi(url, method="POST", payload=aos_payload, headers=headers)
        if not res:
            self.logger.error(f"Failed to create {provider_type} provider.")
            raise Exception(f'Failed to create {provider_type} provider.')
        self.logger.info(f"Successed to create {provider_type} provider.")
        return True, f"Successed to create {provider_type} provider on Move", res.json()

    def update_provider(self, move_token, move_ip, aospro_uuid):
        headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        url = f"https://{move_ip}/move/v2/providers/{aospro_uuid}"
        self.logger.info("Trying to get the Admin password from Vault.")
        if self.pe_fqdn == "RETCN888-NXC000.IKEA.COM":
            _vault = Vault(tier="PREPRODUCTION")
        else:
            _vault = Vault(tier="PRODUCTION")
        res, secret = _vault.get_secret(f"{self.pe.upper()}/Site_Pe_Admin")
        if not res:
            self.logger.error("Failed to get the Admin password from Vault.")
            raise Exception("Failed to get Site_Pe_Admin.")
        aos_payload = {
            "Spec": {
                "AOSAccessInfo": {
                "IPorFQDN": self.pe_fqdn,
                "Username": "admin",
                "Password": str(secret["secret"])
                },
                "Type": "AOS",
                "Name": self.pe
            },
            "UUID": aospro_uuid
        }
        self.logger.info("Trying to update AOS provider.")
        res = self.call_moveapi(url, method="PUT", payload=aos_payload, headers=headers)
        if not res:
            return False, "Failed to update AOS provider."
        return True, "Successed to update AOS provider."

    def validate_provider(self, move_token, move_ip, aospro_uuid):
        headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        url = f"https://{move_ip}/move/v2/providers/{aospro_uuid}/validate"
        self.logger.info(f"Trying to valiate the Admin password for AOS provider: {aospro_uuid}.")
        res = requests.request(url=url, method="POST", headers=headers, verify=False)
        if res.status_code == 200:
            return True, "Successed to update AOS provider.", res
        return False, f"{res.json()['Message']}.", res

    def create_aos_provider(self, move_token, aos_provider_name, move_ip):
        aos_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        aos_payload = {
            "Spec": {
                "ESXAccessInfo": {
                "IPorFQDN": "vc-cnfed01.ikea.com",
                "Username": self._sa['username'],
                "Password": self._sa['password']
                },
                "Type": "AOS",
                "Name": aos_provider_name
            }
        }
        url = f"https://{move_ip}/move/v2/providers"
        res = self.call_moveapi(url, method="POST", payload=aos_payload, headers=aos_headers)
        if not res:
            raise Exception('Failed to create AOS provider.')
        # return res.json()["Status"]["Token"]
        return True, "Successed to create AOS provider"

    def list_providers(self, move_token, move_ip):
        providers_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        providers_body = {
            "properties": {
                "RefreshInventory": "true",
                "EntityType": "VM"
            }
        }
        self.logger.info("Trying to list providers.")
        url = f"https://{move_ip}/move/v2/providers/list"
        res = self.call_moveapi(url, method="POST", payload=providers_body, headers=providers_headers)
        if not res:
            self.logger.error("Failed to list providers.")
            raise Exception('Failed to list providers.')
        self.logger.info("Successed to list providers.")
        return True, "Successed to list providers", res.json()

    def get_single_provider(self, move_token, provider_uuid, move_ip):
        s_provider_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        url = f"https://{move_ip}/move/v2/providers/{provider_uuid}"
        self.logger.info("Trying to get a provider.")
        res = self.call_moveapi(url, method="GET", headers=s_provider_headers)
        if not res:
            self.logger.error("Failed to get a provider.")
            raise Exception('Failed to get provider.')
        self.logger.info("Successed to get a provider.")
        return True, f"Successed to get provider {res.json()['Spec']['Name']}", res.json()

    def fetch_provider_vminfos(self, move_token, provider_uuid, move_ip):
        source_vm_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        source_vm_payload = {
            "Spec": {
                "RefreshInventory": "true",
                "EntityType": "VM"
            }
        }
        self.logger.info("Trying to fetch provider details.")
        url = f"https://{move_ip}/move/v2/providers/{provider_uuid}/workloads/list"
        res = self.call_moveapi(url, method="POST", payload=source_vm_payload, headers=source_vm_headers)
        if not res:
            self.logger.error("Failed to fetch provider details.")
            raise Exception('Failed to get provider.')
        self.logger.info("Successed to fetch provider details.")
        return True, f"Successed to get Source VMs details for {provider_uuid}", res.json()

    def create_migration_plan(self, move_token, vm_uuid: list, plan_name, move_ip):
        # vlan104_pattern = re.compile("104-Servers")
        res, _, provider_data = self.list_providers(move_token, move_ip)
        if not res:
            raise Exception('Failed to list providers.')
        self.logger.info("Generating the data...")
        esxi_provider_uuid = [esxi_privoidor["Spec"]["UUID"] for esxi_privoidor in provider_data["Entities"] if esxi_privoidor["Spec"]["Type"] == "VMWARE_ESXI_VCENTER"][0]
        aos_provider_uuid = [aos_privoidor["Spec"]["UUID"] for aos_privoidor in provider_data["Entities"] if aos_privoidor["Spec"]["Type"] == "AOS_AHV_PE"][0]
        aos_containers = [aos_containers["Spec"]["AOSProperties"]["Clusters"][0]["Containers"]for aos_containers in provider_data["Entities"] if aos_containers["Spec"]["Type"] == "AOS_AHV_PE"]
        aos_container_uuid = [aos_container["UUID"]for aos_container in aos_containers[0] if aos_container["Name"] == "SelfServiceContainer"][0]
        # aos_networks = [aos_network["Spec"]["AOSProperties"]["Clusters"][0]["Networks"] for aos_network in provider_data["Entities"] if aos_network["Spec"]["Type"]=="AOS_AHV_PE"]
        # aos_network_uuid = [aos_network["UUID"] for aos_network in aos_networks[0] if vlan104_pattern.search(aos_network["Name"])][0]
        aos_cluster_uuid = [aos_cluster["Spec"]["AOSProperties"]["Clusters"][0]["UUID"] for aos_cluster in provider_data["Entities"] if aos_cluster["Spec"]["Type"] == "AOS_AHV_PE"][0]
        # source_provider = self.fetch_provider_vminfos(move_token, esxi_provider_uuid, move_ip)
        # [item for item in source_provider["Entities"] if item["VMName"]==]
        vm_reference = [
                    {
                        "VMReference": {
                            "UUID": vmuuid['VMUuid']
                        },
                        "GuestPrepMode": "auto",
                        "RetainMacAddress": True,
                        "updatedSkipIPRetention": False,
                        "UninstallGuestTools": True,
                        "updatedUninstallGuestTools": False,
                        "SkipCdrom": True,
                        # "EnableMemoryOvercommit": "false",
                        "VMPriority": "Medium",
                        # "CategoriesMapping": {},
                        "RetainUserData": False,
                        "CreatePublicIpAddress": False,
                        "VMCustomizeType": "static",
                        "PowerOffForpRDMtovRDMConversion": True,
                        "AllowUVMOps": True
                    }
                for vmuuid in vm_uuid]
        network_setting = [
            {
                "SourceNetworkID": sn_id['Network_Name'],
                "TargetNetworkID": sn_id['target_network_uuid']
            }
            for sn_id in vm_uuid
        ]
        new_plan_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        new_plan_payload = {
            "Spec": {
                "TargetInfo": {
                "ProviderUUID": aos_provider_uuid,
                "AOSProviderAttrs": {
                    "ClusterUUID": aos_cluster_uuid,
                    "ContainerUUID": aos_container_uuid
                }
                },
                "Workload": {
                "Type": "VM",
                "VMs": vm_reference
                },
                "Name": plan_name,
                "Settings": {
                    "Bandwidth": 1,
                    # "Schedule": {
                    #     "ScheduleAtEpochSec": 1701332237000000000
                    # },
                    "GuestPrepMode": "auto",
                    "SkipIPRetentionUI": False,
                    "SkipUninstallGuestToolsUI": False
                },
                "SourceInfo": {
                "ProviderUUID": esxi_provider_uuid
                },
                "NetworkMappings": network_setting
            }
        }
        self.logger.info("Trying to create the new migration plan  ...")
        url = f"https://{move_ip}/move/v2/plans"
        res = self.call_moveapi(url, method="POST", payload=new_plan_payload, headers=new_plan_headers)
        if not res:
            self.logger.error("Failed to create the new migration plan  ...")
            raise Exception('Failed to Create Migration Plan.')
        self.logger.info("Successed to create the new migration plan  ...")
        return True, f"Successed to Create Migration Plan : {res.json()['Spec']['Name']}", res.json()

    def check_plan_readiness(self, move_token, migration_plan_uuid, move_ip):
        readiness_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        self.logger.info("Trying to check the migration plan readiness ...")
        url = f"https://{move_ip}/move/v2/plans/{migration_plan_uuid}/readiness"
        res = self.call_moveapi(url, method="POST", headers=readiness_headers)
        if not res:
            self.logger.error("Failed to check the migration plan readiness ...")
            raise Exception('Failed to check the migration plan readiness.')
        if res.json()['Status']["Failed"]:
            self.logger.info("The migration plan readiness is failed ...")
            return False, "Migration plan is not ready to start, please check"
        self.logger.info("The migration plan readiness is ready ...")
        return True, "Migration plan is ready to start. Continuing..."

    def prepare_source_environment(self, move_token, migration_plan_uuid, vm_uuid: list, move_ip, gst_username, gst_password):
        vm_reference = [
                {
                    "UUID": vmuuid['VMUuid']
                }
                for vmuuid in vm_uuid]
        prepare_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        prepare_payload = {
            "Spec": {
                "CommonCredentials": {
                "LinuxUserName": self.move_lxsa["username"],
                "LinuxPassword": self.move_lxsa["password"],
                "WindowsUserName": gst_username,
                "WindowsPassword": gst_password
                },
                "VMs": vm_reference,
                "GuestPrepMode": "auto",
                "RunInBackground": True
            }
        }
        self.logger.info("Trying to prepare the migration plan...")
        url = f"https://{move_ip}/move/v2/plans/{migration_plan_uuid}/prepare"
        res = self.call_moveapi(url, method="POST", payload=prepare_payload, headers=prepare_headers)
        if not res:
            self.logger.error("Failed to complete the source VMs preparation.")
            raise Exception('Failed to complete the source VMs preparation.')
        self.logger.info("Successed to complete the source VMs preparation")
        return True, "Successed to complete the source VMs preparation"

    def start_migration_plan(self, move_token, migration_plan_uuid, move_ip):
        url = f"https://{move_ip}/move/v2/plans/{migration_plan_uuid}/start"
        start_mp_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        start_mp_payload = {
            "Spec": {
                "Time": 0
            }
        }
        self.logger.info("Trying to start the migration plan...")
        res = self.call_moveapi(url, method="POST", payload=start_mp_payload, headers=start_mp_headers)
        if not res:
            self.logger.error("Failed to start the migration plan...")
            raise Exception('Failed to create AOS provider.')
        self.logger.info("Successed to start the migration plan...")
        return True, "Successed to start the migration plan"

    def list_workloads(self, move_token, migration_plan_uuid, move_ip):
        list_wl_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        self.logger.info("Trying to list the workloads.")
        url = f"https://{move_ip}/move/v2/plans/{migration_plan_uuid}/workloads/list"
        res = self.call_moveapi(url, method="POST", headers=list_wl_headers)
        if not res:
            self.logger.error("Failed to list the workloads.")
            raise Exception('Failed to list the Workloads.')
        self.logger.info("Successed to list the workloads.")
        return True, "Successed to list the Workloads", res.json()

    def get_single_workload(self, move_token, workload_uuid, migration_plan_uuid, move_ip):  #get the ProgressPercentage/StateString for an workload
        get_swl_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        self.logger.info("Trying to get a workload.")
        url = f"https://{move_ip}/move/v2/plans/{migration_plan_uuid}/workloads/{workload_uuid}"
        res = self.call_moveapi(url, method="GET", headers=get_swl_headers)
        if not res:
            self.logger.error("Failed to get a workload.")
            raise Exception('Failed to create AOS provider.')
        self.logger.info("Successed to get a workload.")
        return True, "Successed to create AOS provider", res.json()

    def perform_singleworkload_action(self, move_token, workload_uuid, migration_plan_uuid, action_type, move_ip) :
        migrtion_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        migrtion_payload = {
            "Spec": {
                "Action": action_type
            }
        }
        self.logger.info("Trying to start the cutover task.")
        url = f"https://{move_ip}/move/v2/plans/{migration_plan_uuid}/workloads/{workload_uuid}/action"
        res = self.call_moveapi(url, method="POST", payload=migrtion_payload, headers=migrtion_headers)
        if not res:
            self.logger.error("Failed to start the cutover task.")
            raise Exception('Failed to start the cutover task.')
        self.logger.info("Successed to start the cutover task.")
        return True, "Successed to start the cutover task"

    def perform_mulworkload_action(self, move_token, workload_uuid, migration_plan_uuid, action_type, move_ip) :
        # {
        #     "Spec": {
        #         "Action": "cutover",
        #         "WorkloadReferences": [
        #             {
        #                 "PlanReference": "dc664bb2-2469-4484-92d4-bd94ce685ba5",
        #                 "WorkloadReference": "c130496a-880c-50c6-9350-49fd5b9d91a4"
        #             },
        #             {
        #                 "PlanReference": "dc664bb2-2469-4484-92d4-bd94ce685ba5",
        #                 "WorkloadReference": "098a6a04-0496-501a-8ec7-e594a1934676"
        #             }
        #         ]
        #     }
        # }
        workload_references = [
            {
                "PlanReference": migration_plan_uuid,
                "WorkloadReference": workloaduuid
            } for workloaduuid in workload_uuid
        ]
        migrtion_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        migrtion_payload = {
            "Spec": {
                "Action": action_type,
                "WorkloadReferences": workload_references
            }
        }
        self.logger.info("Trying to start the cutover task.")
        url = f"https://{move_ip}/move/v2/plans/workloads/action"
        res = self.call_moveapi(url, method="POST", payload=migrtion_payload, headers=migrtion_headers)
        if not res:
            self.logger.error("Failed to start the cutover task.")
            raise Exception('Failed to start the cutover task.')
        self.logger.info("Successed to start the cutover task.")
        return True, "Successed to start the cutover task"

    def get_vm_moveinfos(self, vm_list: list):
        try:
            self.logger.info("Trying to generate the VMs details.")
            res, data = self.rest_pe.call_pe_get(request_url="/vms")
            vmlist = [vm_name.split(".")[0].lower() for vm_name in vm_list]
            if res and data:
                self.logger.info("Successed to generate the VMs details.")
                _movevms = [[{"vmname": _vm['vmName'], "vm_uuid": _vm['uuid']} for _vm in data["entities"] if vm_name == _vm["vmName"].lower()] for vm_name in vmlist]
                if _movevms:
                    return True, _movevms
                self.logger.info("Failed to generate the VMs details.")
                return False, 'Failed to get the Target Vm.{"target_vm": "N/A"}'
            self.logger.info("Failed to get the VM list.")
            return False, 'Failed to get the VM list.{"target_vm": "N/A"}'
        except Exception as e:
            self.logger.error(f"Failed to get the VM details. Error: {e}")
            return False, f"Failed to get the VM details . Error: {e}"

    def call_moveapi(self, url, method="GET", params=None, headers=None, files=None, payload=None, is_json=True, retry=5):
        i = 1
        while i < retry:
            try:
                self.logger.info(f"Calling restapi, URL: {url}")
                if (headers is not None) and (payload is not None):
                    if is_json:  # this will send data as json
                        res = requests.request(method, url=url, headers=headers, params=params, json=payload,
                                               verify=False)
                    else:
                        res = requests.request(method, url=url, headers=headers, params=params, data=payload,
                                               verify=False)
                elif payload:
                    if is_json:
                        res = requests.request(method, url=url, params=params, json=payload, verify=False)
                elif files:
                    res = requests.request(method, url=url, files=files, headers=headers, verify=False)
                elif headers:
                    res = requests.request(method, url=url, headers=headers, verify=False)
                else:
                    res = requests.request(method, url=url, auth=(self.sa["username"], self.sa["password"]), verify=False)
                if res.ok:
                    return res
                i += 1
                self.logger.warning(f"Call api failed, going to do the {i} retry...")
                self.logger.info(f"Response content: {res.content}")
            except Exception:
                i += 1
                self.logger.warning(f"Call api failed, going to do the {i} retry...")
        self.logger.error(f"Call api still failed after {i} retries...")
        raise flaskex.BadRequest(f"Out of retry times when calling {url}")

    def list_migration_plans(self, move_token, move_ip):
        providers_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        providers_body = {
            "EntityType": "VM"
        }
        self.logger.info("Trying to list the migration plans.")
        url = f"https://{move_ip}/move/v2/plans/list"
        res = self.call_moveapi(url, method="POST", payload=providers_body, headers=providers_headers)
        if not res:
            self.logger.info("Failed o list the migration plans.")
            raise Exception('Failed to list the migration plans.')
        self.logger.info("Successed o list the migration plans.")
        # return res.json()["Entities"]["MetaData"]["Name"]
        return True, "Successed to list providers", res.json()

    def get_clusterslist_move(self):
        # clusterschema = ModelSLIClusterSchema(many=True)
        logsschema, _logschema = ModelNTXMOVELogSchema(many=True), ModelNTXMOVELogSchema()
        _, _movetaskschema = ModelRetailMOVETaskSchema(many=True), ModelRetailMOVETaskSchema()
        cluster_list = ModelSLICluster.query.all()
        _res = []
        _clusterschema = ModelSLIClusterSchema()
        _movelog_schema = ModelNTXMOVELogSchema()
        for cluster in cluster_list:
            _cluster = _clusterschema.dump(cluster)
            if ModelRetailMOVETask.query.filter_by(cluster=_cluster['name']).first():
                latest_task = (ModelRetailMOVETask.query.filter_by(cluster=_cluster['name']).order_by(ModelRetailMOVETask.id.desc())).first()
                task_id = latest_task.id
                detail_log_path = latest_task.detaillogpath
                if ModelNTXMOVELog.query.filter_by(taskid=task_id).first():
                    logs = ModelNTXMOVELog.query.filter_by(taskid=task_id).order_by(ModelNTXMOVELog.id.desc()).all()
                    _cluster['logs'] = logsschema.dump(logs)
                _cluster['latest_taskid'] = task_id
                _cluster['detail_log_path'] = detail_log_path
            _cluster['pe'] = _cluster['name']+"-NXC000.IKEA.COM"  # type: ignore
            _res.append(_cluster)
        return jsonify(_res)

    def get_whclusterslist_move(self):
        cluster_list = ModelWHNTXMOVE.query.order_by(ModelWHNTXMOVE.cluster).all()
        _res = []
        _clusterschema = ModelWHNTXMOVESchema()
        for cluster in cluster_list:
            _cluster = _clusterschema.dump(cluster)
            if ModelWarehousePrismElement.query.filter(ModelWarehousePrismElement.name.contains(_cluster['cluster'])).all():
                latest_task = (ModelWHMOVETask.query.filter_by(cluster=_cluster['cluster'].lower()).order_by(ModelWHMOVETask.id.desc())).first()
                _cluster['latest_taskid'] = latest_task.id if latest_task else "Null"
                _cluster['detail_log_path'] = latest_task.detaillogpath if latest_task else "Null"
                _cluster['pe_a'] = _cluster['cluster']+"-NXC000.IKEA.COM"  # type: ignore
                _cluster['pe_b'] = _cluster['cluster']+"-NXC001.IKEA.COM"  # type: ignore
                _cluster['status'] = {'a': 'a', 'b': 'b'}
            _res.append(_cluster)
        return jsonify(_res)

    def get_move_brief_log(self):
        logsschema = ModelWHNTXMOVELogSchema(many=True)
        latest_task = (ModelWHMOVETask.query.filter_by(pe=self.pe_fqdn).order_by(ModelWHMOVETask.id.desc())).first()
        if latest_task:
            task_id = latest_task.id
            logs = ModelWHNTXMOVELog.query.filter_by(taskid=task_id).order_by(ModelWHNTXMOVELog.id.desc()).all()
            return logsschema.dump(logs) if logs else []
        return []

    def get_tasks_move(self):
        movetasks = ModelRetailMOVETask.query.all()
        _movetaskschema = ModelRetailMOVETaskSchema(many=True)
        tasklist = _movetaskschema.dump(movetasks)
        return jsonify(tasklist)


    def get_sli_nt_vms(self, vc, cluster_name):
        vc = SimplivityvCenter(vc=vc, username=self._sli_sa['username'], password=self._sli_sa['password'])
        vc.connect()
        self.logger.info(f"Trying to list NT servers in {vc}.")
        nt_vms = vc.get_nt_vms(clustername=cluster_name)
        self.logger.info(f"Trying to list NT servers in {nt_vms}.")
        return nt_vms

    def get_wh_vms(self, cluster_name):
        vms_db = ModelWarehouseVmwareVMs.query.filter(ModelWarehouseVmwareVMs.name.contains(cluster_name)).all()
        vms = [vm.name for vm in vms_db]
        self.logger.info(f"Trying to list NT servers in {vms}.")
        return vms

    def get_sli_networ_portgroups(self, vc, cluster_name):
        vc = SimplivityvCenter(vc=vc, username=self._sli_sa['username'], password=self._sli_sa['password'])
        vc.connect()
        _pat = re.compile(cluster_name)
        self.logger.info("Trying to list NT servers.")
        clus = vc.get_cluster()
        portgroup_host0 = [clu.host[0].config.network.portgroup for clu in clus if re.search(_pat, clu.name)]
        self.logger.info(f"Trying to list NT servers in {cluster_name}.")
        return portgroup_host0

    def new_movevm(self, move_name, network_uuid, vmdisk_uuid):
        move_payload = {
            "name": move_name,
            "memory_mb": 16384,
            "num_vcpus": 8,
            "description": "MOVE 5.4.1",
            "num_cores_per_vcpu": 1,
            "timezone": "UTC",
            "machine_type": "PC",
            "vm_disks": [
                {
                    "is_cdrom": "false",
                    "disk_address": {
                        "device_bus": "scsi",
                        "device_index": 0
                    },
                    "vm_disk_clone": {
                        "disk_address": {
                            "vmdisk_uuid": vmdisk_uuid
                        },
                        "minimum_size": 53687091200
                    }
                }
            ],
            "vm_nics": [
                {
                    "network_uuid": network_uuid,
                    "is_connected": "true"
                }
            ]
        }
        self.logger.info(f"Trying to deploy the NTX MOVE {move_name}.")
        res, data = self.rest_pe.call_pe_v2_post("/vms", payload=move_payload)
        if not res:
            self.logger.info(f"Failed to deploy the NTX MOVE {move_name}.")
            raise Exception(f"Failed to create the new MOVE vm {move_name}")
        self.logger.info(f"Successed to deploy the NTX MOVE {move_name}.")
        return data

    def new_movevm_wh(self, move_name, network_uuid, vmdisk_uuid):
        move_payload = {
            "name": move_name,
            "memory_mb": 4096,
            "num_vcpus": 2,
            "description": "MOVE 5.0.0",
            "num_cores_per_vcpu": 1,
            "timezone": "UTC",
            "machine_type": "PC",
            "vm_disks": [
                {
                    "is_cdrom": "false",
                    "disk_address": {
                        "device_bus": "scsi",
                        "device_index": 0
                    },
                    "vm_disk_clone": {
                        "disk_address": {
                            "vmdisk_uuid": vmdisk_uuid
                        },
                        "minimum_size": 53687091200
                    }
                }
            ],
            "vm_nics": [
                {
                    "network_uuid": network_uuid,
                    "is_connected": "true"
                }
            ]
        }
        self.logger.info(f"Trying to deploy the NTX MOVE {move_name}.")
        res, data = self.rest_pe.call_pe_v3_post("/vms", payload=move_payload)
        if not res:
            self.logger.info(f"Failed to deploy the NTX MOVE {move_name}.")
            raise Exception(f"Failed to create the new MOVE vm {move_name}")
        self.logger.info(f"Successed to deploy the NTX MOVE {move_name}.")
        return data

    def get_exiting_task(self, task_uuid):
        self.logger.info(f"Trying to get the task {task_uuid}.")
        res, data = self.rest_pe.call_pe_v3_get(f"/tasks/{task_uuid}")
        if not res:
            self.logger.error("Failed to get the task {task_uuid}.")
            raise Exception("Failed to verify the task status.")
        if data["status"] == "SUCCEEDED":
            self.logger.error(f"Successed to complete the task {task_uuid}.")
            return data["entity_reference_list"][0]["uuid"]

    def change_mov_password(self, move_token, move_ip):
        url = f"https://{move_ip}/move/v2/configure"
        headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        payload = {
            "Spec": {
                "EulaAccepted": True,
                "NewPassword": self.move_sa['password'],
                "SetNewPassword": True,
                "TelemetryOn": True
            }
        }
        self.logger.info("Trying to change the application password.")
        res = self.call_moveapi(url, method="POST", payload=payload, headers=headers)
        if not res:
            self.logger.error("Failed to change the application password.")
            raise Exception('Failed to change the password.')
        self.logger.info("Successed to change the application password.")
        return True, "Successed to change the password"

    def get_slice_id(self):
        op_headers = {
            "Authorization": 'Bearer %s' % self.optoken['password'],
            "Content-Type": "application/json",
            "Accept": "application/json"
        },
        self.logger.info("Trying to get the CE id in OnePassword.")
        op_url = "https://secrets-automation.ocp.ikea.com/v1/vaults"
        res = requests.request(method="GET", url=op_url, headers=op_headers[0], verify=False)
        if not res:
            raise Exception('Failed to create AOS provider.')
        return True, "Successed to create AOS provider", res.json()

    def get_slifed_id(self, slice_id):
        # ce_rest = self.get_slice_id()
        # slice_id = [item["id"] for item in ce_rest[0] if item["name"]=="SimpliVity_CE"]
        fed_url = f"https://secrets-automation.ocp.ikea.com/v1/vaults/{slice_id[0]}/items?"
        fed_headers = {
            "Authorization": 'Bearer %s' % self.optoken['password'],
            "Content-Type": "application/json",
            "Accept": "application/json"
        },
        res = requests.request(method="GET", url=fed_url, headers=fed_headers[0], verify=False)
        # res = self.call_moveapi(url=fed_url, method="GET", headers=fed_headers)
        if not res:
            raise Exception('Failed to create AOS provider.')
        # return res.json()["Status"]["Token"]
        return True, "Successed to create AOS provider", res.json()

    def get_slifed_pw(self, vc):
        vault_headers = {
            "Authorization": 'Bearer %s' % self.optoken['password'],
            "Content-Type": "application/json",
            "Accept": "application/json"
        },
        _, _, ce_data = self.get_slice_id()
        slice_id = [item["id"] for item in ce_data if item["name"] == "SimpliVity_CE"]
        fed_title = (vc.split(".")[0]).split("-")[1].upper() + " - admin"
        _, _, fed_data = self.get_slifed_id(slice_id)
        vault_id = [item["id"] for item in fed_data if item["title"] == fed_title]
        vault_url = f"https://secrets-automation.ocp.ikea.com/v1/vaults/{slice_id[0]}/items/{vault_id[0]}"
        res = requests.request(method="GET", url=vault_url, headers=vault_headers[0], verify=False)
        if not res:
            raise Exception('Failed to create AOS provider.')
        return res.json()

    def get_move_ip(self, move_name):
        payload = {
            "kind": "vm",
            "length": 150
        }
        res, data = self.rest_pe.call_pe_v3_post(request_url="/vms/list", payload=payload )
        if not res:
            self.logger.error("Failed to get the vm list.")
            raise Exception("Failed to verify the vm list.")
        move_ip = [item["status"]["resources"]["nic_list"][0]["ip_endpoint_list"][0]["ip"] for item in data["entities"] if item["status"]["name"] == move_name]
        if not move_ip:
            raise Exception("Failed to get the MOVE IP.")
        return True, move_ip[0]

    def check_vmlist_connection(self):
        self.logger.info("Trying to get the Admin password from Vault.")
        rep = re.compile(re.escape("ds"), re.IGNORECASE)
        if self.pe_fqdn == "RETCN888-NXC000.IKEA.COM":
            _vault = Vault(tier="PREPRODUCTION")
        elif re.search(rep, self.pe_fqdn):
            _vault = Vault(tier="WAREHOUSE")
        else:
            _vault = Vault(tier="PRODUCTION")
        res, secret = _vault.get_secret(f"{self.pe.upper()}/Site_Pe_Admin")
        if not res:
            self.logger.error("Failed to get the Admin password from Vault.")
            raise Exception("Failed to get Site_Pe_Admin.")
        self.logger.info("Successed to get the Admin password from Vault.")
        payload = {
            "kind": "vm",
            "length": 150
        }
        url = f"https://{self.pe_fqdn}:9440/api/nutanix/v3/vms/list"
        self.logger.info("Trying to log on the move application...")
        c = CommonRestCall(username='admin', password=secret["secret"])
        vmres = c.call_restapi(url, method="POST", payload=payload)
        self.logger.info(vmres.status_code)
        # res, data = self.rest_pe.call_pe_v3_post(request_url="/vms/list",payload=payload )
        if vmres.status_code == 401:
            return False
        return True

    def start_cutover(self, move_token, move_ip):
        providers_headers = {
            "Authorization": move_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        providers_body = {
            "EntityType": "VM"
        }
        self.logger.info("Trying to list the migration plans.")
        url = f"https://{move_ip}/move/v2/plans/ff80acf8-8153-48a5-b735-d2aaa77d05da/workloads/dd5f2dff-b93a-55c5-99b3-52861241b59f/action"
        res = self.call_moveapi(url, method="POST", payload=providers_body, headers=providers_headers)
        if not res:
            self.logger.info("Failed o list the migration plans.")
            raise Exception('Failed to list the migration plans.')
        self.logger.info("Successed o list the migration plans.")
        # return res.json()["Entities"]["MetaData"]["Name"]
        return True, "Successed to list providers", res.json()

    def if_cuttask_succeeded(self, move_token, move_ip, retries=5, retry_interval=60):
        for i in range(retries):
            self.logger.info(f"Get task status attempting {i+1}/{retries}...")
            res, _, data = self.list_migration_plans(move_token, move_ip)
            if not res:
                self.logger.warning(f"Failed to get task staus in present loop. Reason: {data}")
                continue
            cutover_task_status = [migration_plan["MetaData"]["StateString"] for migration_plan in data["Entities"] ]
            if not cutover_task_status:
                self.logger.info("Failed o list the migration plans.")
                raise Exception('Failed to list the migration plans.')
            # ntmigration_plan_name = (self.pe.upper()).split('-')[0]+"_NT_MOVE"
            # lxmigration_plan_name = (self.pe.upper()).split('-')[0]+"_LX_MOVE"
            # # migration_plan_name = "RETCN888_NT_MOVE"
            # print(ntmigration_plan_name)
            # nt_cutover_task = [migration_plan["MetaData"]["StateString"] for migration_plan in data["Entities"] if migration_plan["MetaData"]["Name"]== ntmigration_plan_name]
            # lx_cutover_task = [migration_plan["MetaData"]["StateString"] for migration_plan in data["Entities"] if migration_plan["MetaData"]["Name"]== lxmigration_plan_name]
            # if not nt_cutover_task:
            #     self.logger.info(f"Failed o list the migration plans.")
            #     raise Exception(f'Failed to list the migration plans.')
            # # status = nt_cutover_task["MetaData"]["StateString"]
            # cutover_task=nt_cutover_task+lx_cutover_task
            self.logger.info(f"Task status: {cutover_task_status}")
            if "MigrationPlanStateInProgress" not in  cutover_task_status:
            # if status == "MigrationPlanStateCompleted":
                return True
            if "Failed" in cutover_task_status:
                self.logger.error(f"Task failed! Error detail: {data['meta_response']['error_detail']}")
                return False
            self.logger.info(f"Task is not ended, sleep {retry_interval}s to retry... Task status: {cutover_task_status[0]}")
            time.sleep(retry_interval)
        return False

    def if_newvm_on(self, move_token, move_ip, retries=5, retry_interval=60):
        for i in range(retries):
            self.logger.info(f"Get task status attempting {i+1}/{retries}...")
            res, _, data = self.list_migration_plans(move_token, move_ip)
            if not res:
                self.logger.warning(f"Failed to get task staus in present loop. Reason: {data}")
                continue
            migration_plan_name = (self.pe.upper()).split('-')[0]+"_NT_MOVE"
            nt_cutover_task = [migration_plan["MetaData"]["Name"] for migration_plan in data["Entities"] if migration_plan["MetaData"]["Name"] == migration_plan_name]
            if not nt_cutover_task:
                self.logger.info("Failed o list the migration plans.")
                raise Exception('Failed to list the migration plans.')
            status = nt_cutover_task["MetaData"]["StateString"]
            self.logger.info(f"Task status: {status}")
            if status == "MigrationPlanStateCompleted":
                return True
            if status.lower() == "failed":
                self.logger.error(f"Task failed! Error detail: {data['meta_response']['error_detail']}")
                return False
            self.logger.info(f"Task is not ended, sleep {retry_interval}s to retry... Task percentage: {data.get('percentage_complete')}")
            time.sleep(retry_interval)
        return False

    def rename_slivms(self, vc, cluster_name, vm_list):
        try:
            vc = SimplivityvCenter(vc=vc, username=self._sli_sa['username'], password=self._sli_sa['password'])
            vc.connect()
            self.logger.info(f"Trying to list NT servers in {vc}.")
            vms = vc.get_vm(clustername=cluster_name, include_template=False)
            renamed_vms = [vm for vm in vms if vm.name in vm_list]
            for revm in renamed_vms:
                if revm.runtime.powerState != "poweredOff":
                    revm.PowerOff()
                    time.sleep(30)
                self.logger.info(f"Trying to rename VM {revm.name}.")
                new_vm_name = f'{revm.name}_MOVE_TO_DPC_{datetime.datetime.utcnow().strftime("%Y%m%d")}'
                revm.Rename(new_vm_name)
            self.logger.info("Completed the servers rename task.")
            return True, 'Completed the servers rename task.'
        except Exception as e:
            return False, str(e)

    def is_pd_existing(self, name):
        self.logger.info(f"Start to check if protection domain [{name}] exists...")
        url = f"https://{self.pe_fqdn}:9440/PrismGateway/services/rest/v2.0/protection_domains/{name}"
        res = self.call_moveapi(url=url, method="GET")
        if not res:
            self.logger.info(f"Protection domain [{name}] doesn't exist!")
            return False
        self.logger.info(f"Protection domain [{name}] exists!")
        return True

    def _add_vm_to_ntx_pd_process(self, vm_uuid):
        _pd = ProtectionDomain(pe=self.pe, logger=self.logger)
        # pe_cluster = self.vm_spec[WorkloadSpec.PE].split('.')[0]
        pd_name = f"{self.pe}-Gold_CCG"      # pd_name: {cluster_name}-Gold_CCG
        self.logger.info(f"Adding VM to Protection domain {pd_name}")
        if not self.is_pd_existing(pd_name):
            self.logger.info("PD doesn't exist on NTX, create a new one...")
            _pd.create_pd(pd_name)
        self.logger.info("Adding schedules to PD.")
        _pd.add_schedules_to_pd(pd_name)
        self.logger.info("Adding VM to PD.")
        _pd.add_vm_to_pd(vm_uuid, pd_name)
        self.logger.info("PD task done.")

    def if_plan_existing(self, move_token, move_ip):
        migration_plan_name = (self.pe.upper()).split('-')[0]+"_NT_MOVE"
        _, _, plan_list = self.list_migration_plans(move_token, move_ip)
        # migration_plan_name = "RETCN888_NT_MOVE"
        migration_plans_name = [migration_plan_name, migration_plan_name + "_01", migration_plan_name + "_02"]
        for plan in migration_plans_name:
            if_plan_name_existing = [migration_plan for migration_plan in plan_list["Entities"] if migration_plan["MetaData"]["Name"] == plan]
            if not if_plan_name_existing:
                migration_plan_name = plan
                break
        return migration_plan_name

    def if_ntplan_existing(self, move_token, move_ip):
        migration_plan_name = (self.pe.upper()).split('-')[0]+"_NT_MOVE"
        _, _, plan_list = self.list_migration_plans(move_token, move_ip)
        # migration_plan_name = "RETCN888_NT_MOVE"
        if not plan_list["Entities"]:
            return False, 'Failed to list migration plans.'
        if_plan_name_existing = [migration_plan for migration_plan in plan_list["Entities"] if migration_plan["MetaData"]["Name"] == migration_plan_name]
        if not if_plan_name_existing:
            return False, 'NT_MOVE plan not existed. '
        return True, if_plan_name_existing[0]["MetaData"]["UUID"]

    def update_acp(self):
        try:
            from business.distributedhosting.nutanix.automation.automation import Automation
            self.logger.info("Filtering the pc info in the DB.")
            _task = ModelPrismElement.query.filter_by(fqdn=(self.pe_fqdn).lower()).first()
            pc = _task.prism
            self.logger.info(f"Updating ACP scpoe for vm type on {pc}.")
            atm = Automation(pc=pc)
            atm.dsc_acp()
            self.logger.info("Succeed to updated ACP scpoe for vm type on {pc}.")
        except Exception as e:
            self.logger.warning(f"An error occurred when updting ACP, error {str(e)}")

    def stop_pbrservice_status(self, host, username=None, password=None):
        try:
            if not (username and password):
                username = self.move_lxsa["username"]
                password = self.move_lxsa["password"]
            _conn = SSHConnect(host=host, username=username, password=password, logger=self.logger)
            res, _ = _conn.connect()
            if not res:
                raise Exception(f"Failed to connect {host} with SSH.")
            _conn.invoke_shell()  # invoke a shell in the SSHConnect instance
            _conn.send_command("sudo systemctl stop pbr-client")
            time.sleep(4)
            _conn.send_command(f"{password}")
            time.sleep(10)
            _conn.send_command("sudo systemctl status pbr-client")
            time.sleep(4)
            _conn.send_command(f"{password}")
            time.sleep(4)
            res, output = _conn.recv()
            rep_complete = re.compile(
                re.escape("active (running)"), re.IGNORECASE)
            if re.search(rep_complete, output):
                return False, "PBR service is still running."
            return True, "PBR service is stopped successfully."
        except Exception:
            self.logger.info('Failed to exec the command "sudo systemctl stop pbr-client".')
            return False, 'Failed to exec the command "sudo systemctl stop pbr-client".'

    def start_pbrservice_status(self, host, username=None, password=None):
        try:
            if not (username and password):
                username = self.move_lxsa["username"]
                password = self.move_lxsa["password"]
            _conn = SSHConnect(host=host, username=username, password=password, logger=self.logger)
            res, _ = _conn.connect()
            if not res:
                raise Exception(f"Failed to connect {host} with SSH.")
            _conn.invoke_shell()  # invoke a shell in the SSHConnect instance
            _conn.send_command("sudo systemctl status pbr-client")
            time.sleep(4)
            _conn.send_command(f"{password}")
            time.sleep(4)
            res, output = _conn.recv()
            rep_complete = re.compile(
                re.escape("active (running)"), re.IGNORECASE)
            if not re.search(rep_complete, output):
                _conn.send_command("sudo systemctl status pbr-client")
                time.sleep(4)
                _conn.send_command(f"{password}")
                time.sleep(10)
                res, output = _conn.recv()
                if re.search(rep_complete, output):
                    return True, "PBR service is started successfully."
                return False, "PBR service is still stopped."
            return True, "PBR service has been running."
        except Exception:
            self.logger.info('Failed to exec the command "sudo systemctl start pbr-client".')
            return False, 'Failed to exec the command "sudo systemctl start pbr-client".'

    def if_whvm_exist(self, vm):
        self.logger.info(f"Checking if {vm} existed in PE.")
        vm_name = vm.split(".")[0].lower()
        res, vms_list = self.get_vm_list()
        if not res:
            raise Exception("Failed to get the VM list .....")
        if [_vm for _vm in vms_list if vm_name == _vm["name"].lower()]:
            self.logger.info(f"{vm} exists in {self.pe}.")
            return False, f"{vm} exists in {self.pe} "
        self.logger.info(f"{vm} doesn't exist in {self.pe}.")
        return True, f"{vm} doesn't exist in {self.pe} "

    def get_whvm_list(self, details=False):  # get the vm list , use nutanix V1 api.
        res, data = self.rest_pe.call_pe_get(request_url='/vms')
        self.logger.info(f"Getting VM list from {self.pe}.")
        if res and data:
            self.logger.info(f"Got the VM list from {self.pe}.")
            vm_list = []
            if details:
                # vm_list = [{'name':vm['vmName'], 'state':vm['powerState'], 'is_controller_vm':vm['controllerVm'], 'uuid':vm['uuid'], 'detail':vm} for vm in data['entities']]     # noqa
                # not using a easy way , in case nutanix API return some shit.
                for vm in data['entities']:  # type:  ignore
                    try:
                        vm_list.append(
                            {'name': vm['vmName'], 'state': vm['powerState'], 'is_controller_vm': vm['controllerVm'],
                             'uuid': vm['uuid'], 'detail': vm})  # type:  ignore
                    except Exception:
                        self.logger.error(f'Got error when adding {vm["vmName"]} to the return list.')  # type:  ignore
            else:
                for vm in data['entities']:  # type:  ignore
                    try:
                        vm_list.append(
                            {'name': vm['vmName'], 'state': vm['powerState'], 'is_controller_vm': vm['controllerVm'],
                             'uuid': vm['uuid']})  # type:  ignore
                    except Exception:
                        self.logger.error(f'Got error when adding {vm["vmName"]} to the return list.')
                # vm_list = [{'name':vm['vmName'], 'state':vm['powerState'], 'is_controller_vm':vm['controllerVm'], 'uuid':vm['uuid']} for vm in data['entities']]       # noqa
                # not using a easy way , in case nutanix API return some shit.
            return True, vm_list
        self.logger.error(f"Failed to get the VM list from {self.pe}. Error message:{data}")
        return False, data

    def upload_vddk(self, move_token, move_ip):
        vddk_path = os.path.join(application_path, "static", "VMware-vix-disklib-7.0.3-19513565.x86_64.tar.gz")
        vddk_headers = {
            "Authorization": move_token,
            # "Content-Type": "multipart/form-data",
            "Accept": "application/json"
        }
        vddk_files = {
            "VDDK2": open(vddk_path, 'rb')
        }
        self.logger.info("Trying to upload VDDK.")
        url = f"https://{move_ip}/move/v2/vddk/upload"
        res = self.call_moveapi(url, method="POST", files=vddk_files, headers=vddk_headers)
        if not res:
            self.logger.info("Failed to upload VDDK.")
            raise Exception('Failed to upload VDDK.')
        self.logger.info("Successfully uploaded VDDK.")
        return True, "Successfully uploaded VDDK", res.json()

    def get_vddk_status(self, move_token, move_ip):
        vddk_headers = {
            "Authorization": move_token,
            # "Content-Type": "multipart/form-data",
            "Accept": "application/json"
        }
        self.logger.info("Trying to get VDDK status.")
        url = f"https://{move_ip}/move/v2/vddk"
        res = self.call_moveapi(url, method="GET", headers=vddk_headers)
        upload_status = [us["Uploaded"] for us in res.json()["Status"] if us["ParamKey"] == "VDDK2"]
        if not upload_status[0]:
            return False, " VDDK status is not uploaded."
        self.logger.info("Successfully got VDDK status.")
        return True, "VDDK status is uploaded."

from marshmallow import Schema, fields, validates_schema, ValidationError, EXCLUDE

from business.authentication.authentication import ServiceAccount, LdapAdmin
from models.auth_models import ModelRole
from swagger.rbac_role_schema import PrivilegeSchema


class LoginResponseSchema(Schema):
    roles = fields.List(fields.Str())
    # roles = fields.Str(default='Success')
    token = fields.Str()
    name = fields.Str()
    privilege = fields.Nested(PrivilegeSchema, description="user Privileges")


class CreateUserRequestSchema(Schema):
    name = fields.Str(required=True)
    role_ids = fields.List(fields.Int(), default=[6], required=True)       # TODO: default role id should be 'only_view'
    is_local_user = fields.Boolean(default=False)
    password = fields.Str()

    @validates_schema
    def validate(self, data, **kwargs):
        if not data.get("is_local_user"):
            # If not a local user, validate if the user exists in AD
            sa = ServiceAccount(usage="nutanix_pm").get_service_account()
            ldap = LdapAdmin(sa["username"], sa["password"])
            username = data.get('name')
            if not ldap.is_user_existing_in_ad(username):
                raise ValidationError(f"User '{username}' doesn't exist in AD!")
        elif not data.get("password"):
            raise ValidationError("A password is required when creating local user!")
        # TODO: validate role existence?

class DeleteUserRequestSchema(Schema):
    id = fields.Int(required=True)


class CreateGroupRequestSchema(Schema):
    name = fields.Str(required=True)
    role_ids = fields.List(fields.Int(), default=[6], required=True)

    @validates_schema
    def validate(self, data, **kwargs):
        group_name = data.get("group_name")
        sa = ServiceAccount(usage="nutanix_pm").get_service_account()
        ldap = LdapAdmin(sa["username"], sa["password"])
        if not ldap.is_group_existing_in_ad(group_name):
            raise ValidationError(f"Group '{group_name}' doesn't exist in AD!")


class UpdateUserRoleRequestSchema(Schema):
    user_id = fields.Int(required=True)
    role_ids = fields.List(fields.Int(), required=True)

    def validate_role_not_empty(self, role_ids):
        if not role_ids:
            raise ValidationError("Can't set user role to empty!")

    def validate_new_roles_existence(self, role_ids):
        # validates if the new role_ids exists in db
        search_result = ModelRole.query.filter(ModelRole.id.in_(role_ids)).all()
        role_ids_in_db = [_.id for _ in search_result]
        wrong = [_ for _ in role_ids if _ not in role_ids_in_db]
        if wrong:
            raise ValidationError(f"Following role id doesn't exist! {wrong}")

    @validates_schema
    def validate(self, data, **kwargs):
        role_ids = data.get("role_ids")
        self.validate_role_not_empty(role_ids)
        self.validate_new_roles_existence(role_ids)

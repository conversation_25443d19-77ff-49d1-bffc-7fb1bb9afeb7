import logging
import os
import re
import uuid
import socket
from datetime import datetime
try:
    import secrets
except ImportError:
    secrets = None
import tornado.websocket

from uuid import uuid4
from tornado.ioloop import I<PERSON>oop
from tornado.iostream import _ERRNO_CONNRESET
from tornado.util import errno_from_exception


BUF_SIZE = 32 * 1024
clients = {}  # {ip: {id: worker}}
LOG_DIR = os.path.join(os.path.dirname(__file__), 'log')
os.makedirs(LOG_DIR, exist_ok=True)

class CommonLogger(logging.Logger):
        super(logging.Logger)

        def title(self, msg):
            if len(msg) < 98:
                self.info("*" * 100)
                self.info("*" + " " * 98 + "*")
                space_before = (100 - 2 - len(msg)) // 2
                space_behind = 100 - 2 - space_before - len(msg)
                self.info("*" + " " * space_before + msg + " " * space_behind + "*")
                self.info("*" + " " * 98 + "*")
                self.info("*" * 100)
            else:
                self.info("*" * 100)
                self.info(msg)
                self.info("*" * 100)

def clear_worker(worker, clients):
    ip = worker.src_addr[0]
    workers = clients.get(ip)
    assert worker.id in workers
    workers.pop(worker.id)

    if not workers:
        clients.pop(ip)
        if not clients:
            clients.clear()


def recycle_worker(worker):
    if worker.handler:
        return
    # logging.warning('Recycling worker {}'.format(worker.id))
    worker.close(reason='worker recycled')


class Worker(object):
    def __init__(self, loop, ssh, chan, dst_addr):
        self.loop = loop
        self.ssh = ssh
        self.chan = chan
        self.dst_addr = dst_addr
        self.fd = chan.fileno()
        self.id = self.gen_id()
        self.data_to_dst = []
        self.handler = None
        self.mode = IOLoop.READ
        self.closed = False
        # 创建新的日志文件
        self.hostname = dst_addr[0]  # 假设 dst_addr[0] 是 hostname 或 IP
        self.pfile_path = os.path.dirname(os.path.abspath(__file__))
        self.log_file = os.path.join(LOG_DIR, f"nutanix@{self.hostname}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        print(f"Log file created: {self.log_file}")
        self.setup_logging()
        self.logger = self.setup_common_logger(str(uuid.uuid4()), self.log_file)

    def __call__(self, fd, events):
        if events & IOLoop.READ:
            self.on_read()
        if events & IOLoop.WRITE:
            self.on_write()
        if events & IOLoop.ERROR:
            self.close(reason='error event occurred')

    @classmethod
    def gen_id(cls):
        return secrets.token_urlsafe(nbytes=32) if secrets else uuid4().hex

    def set_handler(self, handler):
        if not self.handler:
            self.handler = handler

    def update_handler(self, mode):
        if self.mode != mode:
            self.loop.update_handler(self.fd, mode)
            self.mode = mode
        if mode == IOLoop.WRITE:
            self.loop.call_later(0.1, self, self.fd, IOLoop.WRITE)

    def setup_logging(self):
        os.makedirs(LOG_DIR, exist_ok=True)
        logging.debug(f"Log directory ensured: {LOG_DIR}")
        with open(self.log_file, 'w+') as file:
            file.write("This is a log file.\n")
            print(f"Log file '{self.log_file}' created successfully.")
        # os.makedirs(LOG_DIR, exist_ok=True)
        # fp = open(self.log_file, 'w+')        # pylint: disable=R1732
        # fp.write(f'nutanix@{self.hostname}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')  # 清空文件内容
        # fp.flush()  # 确保文件内容被写入
        # fp.close()
        # 配置 Logger
        # logging.basicConfig(
        #     filename='src\webssh\log\nutanix@RETDE069-NXC000_20250425_195749.log',
        #     level=logging.DEBUG,
        #     format='%(asctime)s - %(levelname)s - %(message)s'
        # )
        # log_dir = os.path.join('src', 'webssh', 'log')
        # log_file = os.path.join(log_dir, 'nutanix@RETDE069-NXC000_20250425_195749.log')
        # log_file = "src/webssh/log/app.log"
        logging.basicConfig(
            level=logging.DEBUG,  # 设置日志输出等级
            format='%(asctime)s - %(levelname)s - %(message)s',  # 日志格式
            handlers=[
                logging.FileHandler(self.log_file),  # 将日志输出到文件
                logging.StreamHandler()  # 同时输出到控制台
            ]
        )
    def setup_common_logger(self, name, log_file=None, level=logging.INFO):
        # create new loggers
        formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
        logger = CommonLogger(name)
        logger.setLevel(level)
        if not log_file:
            ch = logging.StreamHandler()
            ch.setLevel(logging.DEBUG)
            ch.setFormatter(formatter)
        else:
            ch = logging.FileHandler(log_file, 'a')
            ch.setFormatter(formatter)
        logger.addHandler(ch)
        return logger
    
    def on_read(self):
        self.logger.info("Checking if the task existence...")
        # self.logger.info('worker {} on read'.format(self.id))
        logging.debug('worker {} on read'.format(self.id))
        try:
            data = self.chan.recv(BUF_SIZE)
            # self.logger.info(data.decode('utf-8'))
        except (OSError, IOError) as e:
            logging.error(e)
            self.logger.error(e)
            if self.chan.closed or errno_from_exception(e) in _ERRNO_CONNRESET:
                # self.logger.info('chan error on reading')
                self.close(reason='chan error on reading')
        else:
            logging.debug('{!r} from {}:{}'.format(data, *self.dst_addr))
            original_string = data.decode('utf-8', 'ignore')

            cleaned_string = re.sub(r'\x1b\[[0-?9;]*m', '', original_string)

            cleaned_string = cleaned_string.replace('\r\n', '\n').strip()
            self.logger.info('{!r}'.format(cleaned_string))
            if not data:
                # self.logger.info('Session timeout')
                self.close(reason='Session timeout')
                return
            # self.logger.info('{!r}')
            logging.debug('{!r}'.format(data.decode('utf-8'), *self.handler.src_addr))
            try:
                self.handler.write_message(data, binary=True)
                # self.logger.info('WebSocket closed')
            except tornado.websocket.WebSocketClosedError:
                # self.logger.info('WebSocket closed')
                self.close(reason='websocket closed')

    def on_write(self):
        logging.debug('worker {} on write'.format(self.id))
        if not self.data_to_dst:
            return

        data = ''.join(self.data_to_dst)
        # self.logger.info(data)
        logging.debug('{!r} to {}:{}'.format(data, *self.dst_addr))

        try:
            sent = self.chan.send(data)
        except (OSError, IOError) as e:
            logging.error(e)
            if self.chan.closed or errno_from_exception(e) in _ERRNO_CONNRESET:
                self.close(reason='chan error on writing')
            else:
                self.update_handler(IOLoop.WRITE)
        else:
            self.data_to_dst = []
            data = data[sent:]
            if data:
                self.data_to_dst.append(data)
                self.update_handler(IOLoop.WRITE)
            else:
                self.update_handler(IOLoop.READ)
            # self.logger.info(self.data_to_dst)

    def close(self, reason=None):
        if self.closed:
            return
        self.closed = True

        logging.info(
            'Closing worker {} with reason: {}'.format(self.id, reason)
        )
        if self.handler:
            self.loop.remove_handler(self.fd)
            self.handler.close(reason=reason)
        self.chan.close()
        self.ssh.close()
        logging.info('Connection to {}:{} lost'.format(*self.dst_addr))

        clear_worker(self, clients)
        logging.debug(clients)

2025-08-05 10:34:52,882 INFO Start to run the task.
2025-08-05 10:35:00,384 INFO Checking Maintenance Mode via v2.0 API (/hosts)
2025-08-05 10:35:00,384 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/hosts, method: GET, headers: None
2025-08-05 10:35:00,384 INFO params: None
2025-08-05 10:35:00,384 INFO User: 1-click-nutanix
2025-08-05 10:35:00,384 INFO payload: None
2025-08-05 10:35:00,384 INFO files: None
2025-08-05 10:35:00,384 INFO timeout: 30
2025-08-05 10:35:01,983 INFO API Check: All good, no hosts or CVMs are in maintenance mode.
2025-08-05 10:35:01,985 INFO Checking CVM status
2025-08-05 10:35:02,513 INFO Trying to SSH to the RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 10:35:02,514 INFO First try with username/password.
2025-08-05 10:35:02,514 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 10:35:05,106 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 10:35:11,215 INFO Sending 'cluster status |grep -v UP' to the server.
2025-08-05 10:35:27,218 INFO CVM IP:*********** Status:Up
2025-08-05 10:35:27,218 INFO CVM IP:*********** Status:Up
2025-08-05 10:35:27,218 INFO CVM IP:*********** Status:Up
2025-08-05 10:35:27,218 INFO Great, all CVM status are Up
2025-08-05 10:35:27,249 INFO Calling restapi, URL: https://retseelm-nxc000.ikead2.com:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 10:35:27,249 INFO params: None
2025-08-05 10:35:27,249 INFO User: <EMAIL>
2025-08-05 10:35:27,249 INFO payload: None
2025-08-05 10:35:27,249 INFO files: None
2025-08-05 10:35:27,249 INFO timeout: 30
2025-08-05 10:35:29,256 INFO Getting host list from retseelm-nxc000.
2025-08-05 10:35:29,256 INFO Got the host list from retseelm-nxc000.
2025-08-05 10:35:29,256 INFO Got the host list.
2025-08-05 10:35:29,256 INFO Getting vault from IKEAD2.
2025-08-05 10:35:30,349 INFO Getting Site_Pe_Nutanix.
2025-08-05 10:35:30,905 INFO Got Site_Pe_Nutanix.
2025-08-05 10:35:30,905 INFO Getting Site_Pe_Admin.
2025-08-05 10:35:31,486 INFO Got Site_Pe_Admin.
2025-08-05 10:35:31,487 INFO Getting Site_Oob.
2025-08-05 10:35:32,366 INFO Got Site_Oob.
2025-08-05 10:35:32,366 INFO Getting Site_Ahv_Nutanix.
2025-08-05 10:35:33,116 INFO Got Site_Ahv_Nutanix.
2025-08-05 10:35:33,116 INFO Getting Site_Ahv_Root.
2025-08-05 10:35:33,615 INFO Got Site_Ahv_Root.
2025-08-05 10:35:33,615 INFO Getting Site_Gw_Priv_Key.
2025-08-05 10:35:34,082 INFO Got Site_Gw_Priv_Key.
2025-08-05 10:35:34,082 INFO Getting Site_Gw_Pub_Key.
2025-08-05 10:35:34,650 INFO Got Site_Gw_Pub_Key.
2025-08-05 10:35:34,650 INFO Getting Site_Pe_Svc.
2025-08-05 10:35:35,176 INFO Got Site_Pe_Svc.
2025-08-05 10:35:35,196 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-05 10:35:35,245 INFO Connecting to CVM *********** for AHV password updates
2025-08-05 10:35:35,245 INFO SSH connecting to ***********, this is the '1' try.
2025-08-05 10:35:37,281 INFO SSH connected to *********** with SSHKEY.
2025-08-05 10:35:38,449 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 10:35:43,459 INFO Start reset AHV user nutanix password from CVM ***********
2025-08-05 10:35:43,459 INFO unlocking nutanix account
2025-08-05 10:35:43,459 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 10:35:54,475 INFO Sending '*' to the server.
2025-08-05 10:36:09,989 INFO AHV User nutanix Password Update Success
2025-08-05 10:36:10,002 INFO Start reset AHV user root password from CVM ***********
2025-08-05 10:36:10,018 INFO unlocking root account
2025-08-05 10:36:10,019 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 10:36:20,521 INFO Sending '*' to the server.
2025-08-05 10:36:36,033 INFO AHV User root Password Update Success
2025-08-05 10:36:36,045 INFO Connecting to CVM *********** for AHV password updates
2025-08-05 10:36:36,045 INFO SSH connecting to ***********, this is the '1' try.
2025-08-05 10:36:38,028 INFO SSH connected to *********** with SSHKEY.
2025-08-05 10:36:39,116 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 10:36:44,180 INFO Start reset AHV user nutanix password from CVM ***********
2025-08-05 10:36:44,207 INFO unlocking nutanix account
2025-08-05 10:36:44,207 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 10:36:55,208 INFO Sending '*' to the server.
2025-08-05 10:37:10,738 INFO AHV User nutanix Password Update Success
2025-08-05 10:37:10,749 INFO Start reset AHV user root password from CVM ***********
2025-08-05 10:37:10,759 INFO unlocking root account
2025-08-05 10:37:10,759 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 10:37:21,261 INFO Sending '*' to the server.
2025-08-05 10:37:36,777 INFO AHV User root Password Update Success
2025-08-05 10:37:36,787 INFO Connecting to CVM *********** for AHV password updates
2025-08-05 10:37:36,788 INFO SSH connecting to ***********, this is the '1' try.
2025-08-05 10:37:38,797 INFO SSH connected to *********** with SSHKEY.
2025-08-05 10:37:39,904 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 10:37:44,924 INFO Start reset AHV user nutanix password from CVM ***********
2025-08-05 10:37:44,941 INFO unlocking nutanix account
2025-08-05 10:37:44,941 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 10:37:55,943 INFO Sending '*' to the server.
2025-08-05 10:38:11,459 INFO AHV User nutanix Password Update Success
2025-08-05 10:38:11,471 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Ahv_Nutanix
2025-08-05 10:38:12,425 INFO Saving token completed.
2025-08-05 10:38:12,435 INFO Start reset AHV user root password from CVM ***********
2025-08-05 10:38:12,445 INFO unlocking root account
2025-08-05 10:38:12,445 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 10:38:22,946 INFO Sending '*' to the server.
2025-08-05 10:38:38,459 INFO AHV User root Password Update Success
2025-08-05 10:38:38,469 INFO Saving token to Vault... Username: root, label: RETSEELM-NXC000/Site_Ahv_Root
2025-08-05 10:38:39,590 INFO Saving token completed.
2025-08-05 10:38:39,602 INFO Starting to reset iLO password for user 'administrator' across all hosts.
2025-08-05 10:38:39,617 INFO Updating host RETSEELM-NX7001 (*************)
2025-08-05 10:38:39,626 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-05 10:38:39,627 INFO Finding account URI for user 'administrator'.
2025-08-05 10:38:39,627 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 10:38:39,627 INFO params: None
2025-08-05 10:38:39,627 INFO User: administrator
2025-08-05 10:38:39,627 INFO payload: None
2025-08-05 10:38:39,627 INFO files: None
2025-08-05 10:38:39,627 INFO timeout: None
2025-08-05 10:38:40,799 INFO Got the response with OK
2025-08-05 10:38:40,800 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 10:38:40,800 INFO params: None
2025-08-05 10:38:40,800 INFO User: administrator
2025-08-05 10:38:40,800 INFO payload: None
2025-08-05 10:38:40,800 INFO files: None
2025-08-05 10:38:40,801 INFO timeout: None
2025-08-05 10:38:42,018 INFO Got the response with OK
2025-08-05 10:38:42,019 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 10:38:42,019 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 10:38:42,020 INFO params: None
2025-08-05 10:38:42,020 INFO User: administrator
2025-08-05 10:38:42,020 INFO payload: {'Password': '*****'}
2025-08-05 10:38:42,020 INFO files: None
2025-08-05 10:38:42,020 INFO timeout: None
2025-08-05 10:38:43,332 INFO ILO object updated successfully
2025-08-05 10:38:43,344 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-05 10:38:43,361 INFO Updating host RETSEELM-NX7002 (*************)
2025-08-05 10:38:43,372 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-05 10:38:43,372 INFO Finding account URI for user 'administrator'.
2025-08-05 10:38:43,373 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 10:38:43,373 INFO params: None
2025-08-05 10:38:43,373 INFO User: administrator
2025-08-05 10:38:43,373 INFO payload: None
2025-08-05 10:38:43,373 INFO files: None
2025-08-05 10:38:43,373 INFO timeout: None
2025-08-05 10:38:44,583 INFO Got the response with OK
2025-08-05 10:38:44,584 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 10:38:44,584 INFO params: None
2025-08-05 10:38:44,584 INFO User: administrator
2025-08-05 10:38:44,584 INFO payload: None
2025-08-05 10:38:44,584 INFO files: None
2025-08-05 10:38:44,584 INFO timeout: None
2025-08-05 10:38:46,011 INFO Got the response with OK
2025-08-05 10:38:46,012 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 10:38:46,012 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 10:38:46,012 INFO params: None
2025-08-05 10:38:46,012 INFO User: administrator
2025-08-05 10:38:46,013 INFO payload: {'Password': '*****'}
2025-08-05 10:38:46,013 INFO files: None
2025-08-05 10:38:46,013 INFO timeout: None
2025-08-05 10:38:47,815 INFO ILO object updated successfully
2025-08-05 10:38:47,831 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-05 10:38:47,843 INFO Updating host RETSEELM-NX7003 (*************)
2025-08-05 10:38:47,852 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-05 10:38:47,852 INFO Finding account URI for user 'administrator'.
2025-08-05 10:38:47,852 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 10:38:47,852 INFO params: None
2025-08-05 10:38:47,852 INFO User: administrator
2025-08-05 10:38:47,852 INFO payload: None
2025-08-05 10:38:47,852 INFO files: None
2025-08-05 10:38:47,852 INFO timeout: None
2025-08-05 10:38:49,039 INFO Got the response with OK
2025-08-05 10:38:49,040 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 10:38:49,040 INFO params: None
2025-08-05 10:38:49,040 INFO User: administrator
2025-08-05 10:38:49,040 INFO payload: None
2025-08-05 10:38:49,040 INFO files: None
2025-08-05 10:38:49,040 INFO timeout: None
2025-08-05 10:38:50,231 INFO Got the response with OK
2025-08-05 10:38:50,232 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 10:38:50,232 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 10:38:50,232 INFO params: None
2025-08-05 10:38:50,232 INFO User: administrator
2025-08-05 10:38:50,232 INFO payload: {'Password': '*****'}
2025-08-05 10:38:50,233 INFO files: None
2025-08-05 10:38:50,233 INFO timeout: None
2025-08-05 10:38:51,583 INFO ILO object updated successfully
2025-08-05 10:38:51,615 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-05 10:38:51,710 INFO Successfully updated password for 'administrator' on all hosts. Saving to Vault.
2025-08-05 10:38:51,727 INFO Saving token to Vault... Username: administrator, label: RETSEELM-NXC000/Site_Oob
2025-08-05 10:38:52,390 INFO Saving token completed.
2025-08-05 10:38:52,424 INFO Start reset CVM Nutanix password
2025-08-05 10:38:52,487 INFO Attempting to reset password for user 'nutanix' on cluster associated with CVM RETSEELM-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-05 10:38:52,487 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-05 10:38:55,080 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-05 10:38:55,092 INFO Unlocking user 'nutanix' on all CVMs.
2025-08-05 10:38:55,094 INFO SSH Executing 'allssh sudo faillock --user nutanix --reset'.
2025-08-05 10:38:55,919 INFO Waiting for 10 seconds for the execution.
2025-08-05 10:39:05,920 INFO stdout: b''
2025-08-05 10:39:05,945 INFO Changing password for 'nutanix' on single node RETSEELM-NXC000.ikead2.com.
2025-08-05 10:39:05,945 INFO SSH Executing '*****************************************************'.
2025-08-05 10:39:06,480 INFO Waiting for 5 seconds for the execution.
2025-08-05 10:39:11,481 INFO stdout: b'Changing password for user nutanix.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 10:39:11,493 INFO Password change command sent. Verifying new password for 'nutanix' with a new SSH connection.
2025-08-05 10:39:16,494 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-05 10:39:19,082 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-05 10:39:19,098 INFO Successfully reset and verified new password for user 'nutanix'.
2025-08-05 10:39:19,108 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Pe_Nutanix
2025-08-05 10:39:19,738 INFO Saving token completed.
2025-08-05 10:39:20,219 INFO taking a 30S extra powernap
2025-08-05 10:39:50,286 INFO Start reset admin password
2025-08-05 10:39:50,316 INFO Attempting to reset password for user 'admin' on cluster associated with CVM RETSEELM-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-05 10:39:50,316 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-05 10:39:52,831 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-05 10:39:52,849 INFO Unlocking user 'admin' on all CVMs.
2025-08-05 10:39:52,849 INFO SSH Executing 'allssh sudo faillock --user admin --reset'.
2025-08-05 10:39:53,707 INFO Waiting for 10 seconds for the execution.
2025-08-05 10:40:03,708 INFO stdout: b''
2025-08-05 10:40:03,724 INFO Changing password for 'admin' on single node RETSEELM-NXC000.ikead2.com.
2025-08-05 10:40:03,724 INFO SSH Executing '***************************************************'.
2025-08-05 10:40:04,245 INFO Waiting for 5 seconds for the execution.
2025-08-05 10:40:09,245 INFO stdout: b'Changing password for user admin.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 10:40:09,259 INFO Password change command sent. Verifying new password for 'admin' with a new SSH connection.
2025-08-05 10:40:14,260 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-05 10:40:16,844 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-05 10:40:16,858 INFO Successfully reset and verified new password for user 'admin'.
2025-08-05 10:40:16,870 INFO Saving token to Vault... Username: admin, label: RETSEELM-NXC000/Site_Pe_Admin
2025-08-05 10:40:17,557 INFO Saving token completed.
2025-08-05 10:43:08,762 INFO This is a central PE, start to reset PCVM password...
2025-08-05 10:43:08,762 INFO Resetting password for Site_Pc_Nutanix...
2025-08-05 10:43:09,753 INFO taking a 30S extra powernap
2025-08-05 10:43:39,767 INFO Start reset PCVM Site_Pc_Nutanix password
2025-08-05 10:43:43,479 INFO Attempting to reset password for user 'nutanix' on cluster associated with CVM 10.62.234.12, authenticating as 'nutanix'.
2025-08-05 10:43:43,480 INFO SSH connecting to 10.62.234.12, this is the '1' try.
2025-08-05 10:43:46,012 INFO SSH connected to 10.62.234.12.
2025-08-05 10:43:46,022 INFO Unlocking user 'nutanix' on all CVMs.
2025-08-05 10:43:46,023 INFO SSH Executing 'allssh sudo faillock --user nutanix --reset'.
2025-08-05 10:43:46,854 INFO Waiting for 10 seconds for the execution.
2025-08-05 10:43:56,855 INFO stdout: b''
2025-08-05 10:43:56,872 INFO Changing password for 'nutanix' on single node 10.62.234.12.
2025-08-05 10:43:56,872 INFO SSH Executing '*****************************************************'.
2025-08-05 10:43:57,456 INFO Waiting for 5 seconds for the execution.
2025-08-05 10:44:02,457 INFO stdout: b'Changing password for user nutanix.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 10:44:02,471 INFO Password change command sent. Verifying new password for 'nutanix' with a new SSH connection.
2025-08-05 10:44:07,472 INFO SSH connecting to 10.62.234.12, this is the '1' try.
2025-08-05 10:44:10,043 INFO SSH connected to 10.62.234.12.
2025-08-05 10:44:10,064 INFO Successfully reset and verified new password for user 'nutanix'.
2025-08-05 10:44:10,812 INFO Resetting password for Site_Pc_Admin...
2025-08-05 10:44:11,854 INFO taking a 30S extra powernap
2025-08-05 10:44:41,868 INFO Start reset PCVM Site_Pc_Admin password
2025-08-05 10:44:41,880 INFO Attempting to reset password for user 'admin' on cluster associated with CVM 10.62.234.12, authenticating as 'nutanix'.
2025-08-05 10:44:41,881 INFO SSH connecting to 10.62.234.12, this is the '1' try.
2025-08-05 10:44:44,478 INFO SSH connected to 10.62.234.12.
2025-08-05 10:44:44,494 INFO Unlocking user 'admin' on all CVMs.
2025-08-05 10:44:44,495 INFO SSH Executing 'allssh sudo faillock --user admin --reset'.
2025-08-05 10:44:45,341 INFO Waiting for 10 seconds for the execution.
2025-08-05 10:44:55,342 INFO stdout: b''
2025-08-05 10:44:55,380 INFO Changing password for 'admin' on single node 10.62.234.12.
2025-08-05 10:44:55,382 INFO SSH Executing '***************************************************'.
2025-08-05 10:44:55,924 INFO Waiting for 5 seconds for the execution.
2025-08-05 10:45:00,924 INFO stdout: b'Changing password for user admin.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 10:45:00,936 INFO Password change command sent. Verifying new password for 'admin' with a new SSH connection.
2025-08-05 10:45:05,937 INFO SSH connecting to 10.62.234.12, this is the '1' try.
2025-08-05 10:45:08,475 INFO SSH connected to 10.62.234.12.
2025-08-05 10:45:08,491 INFO Successfully reset and verified new password for user 'admin'.
2025-08-05 10:45:09,116 INFO Start reset 1-click-nutanix password for PE RETSEELM-NXC000
2025-08-05 10:45:09,700 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-08-05 10:45:09,701 INFO params: None
2025-08-05 10:45:09,701 INFO User: admin
2025-08-05 10:45:09,702 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-08-05 10:45:09,702 INFO files: None
2025-08-05 10:45:09,702 INFO timeout: None
2025-08-05 10:45:11,918 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-08-05 10:45:11,918 INFO params: None
2025-08-05 10:45:11,919 INFO User: admin
2025-08-05 10:45:11,919 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-08-05 10:45:11,920 INFO files: None
2025-08-05 10:45:11,920 INFO timeout: None
2025-08-05 10:45:13,624 INFO This is centrol PE, Start reset 1-click-nutanix password for Pc ssp-dhd2-ntx.ikead2.com
2025-08-05 10:45:14,390 INFO Saving token to Vault... Username: 1-click-nutanix, label: RETSEELM-NXC000/Site_Pe_Svc
2025-08-05 10:45:15,011 INFO Saving token completed.
2025-08-05 10:45:15,021 INFO ****************************************************************************************************
2025-08-05 10:45:15,022 INFO *                                                                                                  *
2025-08-05 10:45:15,022 INFO *                                          Renew SSH key                                           *
2025-08-05 10:45:15,022 INFO *                                                                                                  *
2025-08-05 10:45:15,022 INFO ****************************************************************************************************
2025-08-05 10:45:16,205 INFO Check if ssh_key exist
2025-08-05 10:45:16,206 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: GET, headers: None
2025-08-05 10:45:16,206 INFO params: None
2025-08-05 10:45:16,206 INFO User: admin
2025-08-05 10:45:16,206 INFO payload: None
2025-08-05 10:45:16,206 INFO files: None
2025-08-05 10:45:16,206 INFO timeout: None
2025-08-05 10:45:17,913 INFO SSH key exist, we need replace it.
2025-08-05 10:45:17,913 INFO Deleting public key...
2025-08-05 10:45:17,914 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys/Gateway, method: DELETE, headers: None
2025-08-05 10:45:17,914 INFO params: None
2025-08-05 10:45:17,914 INFO User: admin
2025-08-05 10:45:17,914 INFO payload: None
2025-08-05 10:45:17,914 INFO files: None
2025-08-05 10:45:17,914 INFO timeout: None
2025-08-05 10:45:19,696 INFO Delete public key finished.
2025-08-05 10:45:19,696 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: GET, headers: None
2025-08-05 10:45:19,697 INFO params: None
2025-08-05 10:45:19,697 INFO User: admin
2025-08-05 10:45:19,698 INFO payload: None
2025-08-05 10:45:19,698 INFO files: None
2025-08-05 10:45:19,698 INFO timeout: None
2025-08-05 10:45:21,249 INFO SSH key exist, we need replace it.
2025-08-05 10:45:21,249 INFO Deleting public key...
2025-08-05 10:45:21,250 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys/Gateway, method: DELETE, headers: None
2025-08-05 10:45:21,250 INFO params: None
2025-08-05 10:45:21,250 INFO User: admin
2025-08-05 10:45:21,250 INFO payload: None
2025-08-05 10:45:21,251 INFO files: None
2025-08-05 10:45:21,251 INFO timeout: None
2025-08-05 10:45:22,852 INFO Delete public key finished.
2025-08-05 10:45:22,868 INFO Generating ssh_key
2025-08-05 10:45:22,869 INFO Generating SSH key: ssh-keygen -t rsa -b 2048 -f c:\Dev\UnitPortalBackend\tmp\sshkey\RETSEELM-NXC000_2025-08-05-02-45-16\prvkey -q -N "" -m PEM
2025-08-05 10:45:23,103 INFO Key pair generated: c:\Dev\UnitPortalBackend\tmp\sshkey\RETSEELM-NXC000_2025-08-05-02-45-16\prvkey
2025-08-05 10:45:23,120 INFO Installing public key...
2025-08-05 10:45:23,120 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: POST, headers: None
2025-08-05 10:45:23,120 INFO params: None
2025-08-05 10:45:23,120 INFO User: admin
2025-08-05 10:45:23,120 INFO payload: {'name': 'Gateway', 'key': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDEGKAfkjhpD2gCEtqhgUSvw6PBAuhGgvRoDVDnlCGCGY2RwPcodKYrSStoMizmfZubuL/33UqsyCtEeI5Xb0e07hKr4vYWD0c/6XllcDyoJWeS0S/+sJg5kfNDIm9G7SuDL2P73jX23S9q71tucdhNSPrPIQuEzz0YtbTjdP87kbTSsH1DwJ6GruqJO9pPlkHnFbYrdWj2S+UQVarwZuknZsy/SPxHQfNqJ6bcFCgLXAqDGMfLuDDqE5S2Rx+mwzCoZDnNHOdwJ5U26Z7/m8epohWnem2lVaBjf15DVaTOjs2KUc2s1/jxZbec55eykcHy3m8EdWkJaAYtuCkJ4oul ikea\\hunhe@ITCNSHG-NB0436'}
2025-08-05 10:45:23,120 INFO files: None
2025-08-05 10:45:23,120 INFO timeout: None
2025-08-05 10:45:24,782 INFO Install public key finished.
2025-08-05 10:45:24,782 INFO Installing public key...
2025-08-05 10:45:24,783 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: POST, headers: None
2025-08-05 10:45:24,783 INFO params: None
2025-08-05 10:45:24,783 INFO User: admin
2025-08-05 10:45:24,783 INFO payload: {'name': 'Gateway', 'key': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDEGKAfkjhpD2gCEtqhgUSvw6PBAuhGgvRoDVDnlCGCGY2RwPcodKYrSStoMizmfZubuL/33UqsyCtEeI5Xb0e07hKr4vYWD0c/6XllcDyoJWeS0S/+sJg5kfNDIm9G7SuDL2P73jX23S9q71tucdhNSPrPIQuEzz0YtbTjdP87kbTSsH1DwJ6GruqJO9pPlkHnFbYrdWj2S+UQVarwZuknZsy/SPxHQfNqJ6bcFCgLXAqDGMfLuDDqE5S2Rx+mwzCoZDnNHOdwJ5U26Z7/m8epohWnem2lVaBjf15DVaTOjs2KUc2s1/jxZbec55eykcHy3m8EdWkJaAYtuCkJ4oul ikea\\hunhe@ITCNSHG-NB0436'}
2025-08-05 10:45:24,784 INFO files: None
2025-08-05 10:45:24,784 INFO timeout: None
2025-08-05 10:45:27,124 INFO Install public key finished.
2025-08-05 10:45:27,135 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Gw_Priv_Key
2025-08-05 10:45:27,790 INFO Saving token completed.
2025-08-05 10:45:27,799 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Gw_Pub_Key
2025-08-05 10:45:28,453 INFO Saving token completed.
2025-08-05 10:45:28,501 INFO Task is in 'Done' status.

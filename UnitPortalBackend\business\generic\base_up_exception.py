from business.distributedhosting.nutanix.task_status import TaskStatus


class BaseUpException(Exception):
    def __init__(self, msg, status=TaskStatus.ERROR):
        super().__init__(self)
        self.msg = msg
        self.status = status

    def __str__(self):
        return self.msg


class DatabaseError(BaseUpException):
    def __init__(self, msg):
        super().__init__(msg)


class DoneWithError(BaseUpException):
    def __init__(self):
        super().__init__("The task is done, but with some error occurred.", TaskStatus.DONE_WITH_ERROR)


class SSHFailed(BaseUpException):
    def __init__(self, destination):
        super().__init__(f"SSH to {destination} failed!")


class TaskSkipped(BaseUpException):
    def __init__(self, msg="The task is skipped."):
        super().__init__(msg, TaskStatus.SKIPPED)


class VaultException(BaseUpException):
    def __init__(self, msg):
        super().__init__(msg)


class VaultLoginFailed(VaultException):
    def __init__(self):
        super().__init__("Vault login failed!")


class VaultGetSecretFailed(VaultException):
    def __init__(self, label):
        super().__init__(f"Failed to get secret data of {label}!")


class BenchmarkGetFailed(BaseUpException):
    def __init__(self, bmk_id):
        super().__init__(f"Cannot find benchmark by id={bmk_id}!")


class GetPeFailed(BaseUpException):
    def __init__(self, pe_name):
        super().__init__(f"PE with name '{pe_name}' not found in any model")
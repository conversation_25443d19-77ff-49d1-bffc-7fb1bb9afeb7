import win32serviceutil
import win32service
import win32event
import servicemanager
import logging
from src.webssh.main import main

class UnitPortalWebSSHService(win32serviceutil.ServiceFramework):
    _svc_name_ = "UnitPortalWebSSHService"
    _svc_display_name_ = "Unit Portal Web SSH Service" 
    _svc_description_ = "This service runs UnitPortalWebSSHService"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.stop_event = win32event.CreateEvent(None, 0, 0, None)
        self.running = True

        logging.basicConfig(
            filename='D:\\UP-Shuai_WS\\UnitPortalFrontend\\src\\webssh\\log\\app.log',
            level=logging.DEBUG,
            format='%(asctime)s %(levelname)s %(message)s'
        )

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.stop_event)
        self.running = False

    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                               servicemanager.PYS_SERVICE_STARTED,
                               (self._svc_name_, ''))
        try:
            main()  # 启动你的主函数
            # while self.running:
            #     # 这里是你要运行的脚本的逻辑
            #     logging.info("Service is running...")
            #     time.sleep(25)  # 模拟一个正在运行的服务
        except Exception as e:
            logging.error(f"Service failed to run main(): {e}")
            self.SvcStop()


if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(UnitPortalWebSSHService)
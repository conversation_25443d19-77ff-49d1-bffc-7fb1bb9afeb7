import asyncio
from business.authentication.authentication import ServiceAccount
from collector.collectors.modules.db_operations import DatabaseOperations
from collector.collectors.modules.api_calls import APICallsPCCollector
from collector.collectors.modules.multi_job import AsynchronousProcessing
from collector.collectors.base_collector import BaseCollector
from models.ntx_models import ModelPrismCentral
from models.ntx_models_wh import ModelWarehousePrismCentral
from models.database import db
import time
from datetime import datetime, timezone



class PCCollector(BaseCollector):

    def __init__(self, sa=None, facility_type = "retail") -> None:
        super().__init__(sa, facility_type)
        # self.facility_type from basecollector. used to name log file
        # self.logger from basecollector. Used to log to specific file
        self.database_operations = DatabaseOperations(logger=self.logger)
        
    async def _async_method_api_for_pc_get_central_site_using_sa(self):
        api = APICallsPCCollector(logger = self.logger)
        processes = AsynchronousProcessing(thread = True)
        
        for pc in self.pc_fqdn:
            sa_col = self.pc_fqdn[pc]["service_account"]
            
            try:
                sa = ServiceAccount(sa_col).get_service_account()
                processes.add_task(pc, api.get_central_site_using_sa, pc, sa)
            except Exception as e:
                self.logger.error(f"error for {pc} could not get service account!!: {e}")
                continue
            
        await processes.execute_asynchronous_tasks()
        api_results = processes.get_results()
        return api_results
    
    async def _async_method_api_for_pc(self):
        api = APICallsPCCollector(logger = self.logger)
        processes = AsynchronousProcessing(thread = True)
        
        for pc in self.pc_fqdn:
            processes.add_task("PC_LCM", api.get_lcm_information, pc) 
            processes.add_task("PC_cluster", api.get_cluster_info1, pc)
            processes.add_task("PC_cluster2", api.get_cluster_info2, pc)
            #processes.add_task("PC_cluster3", api.get_cluster_info3, pc) # not used. For now we use sa in other method
            processes.add_task("PC_cert", api.get_ssl_certificates, pc)
            
        await processes.execute_asynchronous_tasks()
        
        api_results = processes.get_results()
        
        return api_results
        

    def collect(self, warehouse = False):
        self.warehouse = warehouse
        self.logger.title("PCCollector{}".format("Retail" if not warehouse else "Warehouse"))
        self.logger.info("Starting PCCollector for {}".format("Retail" if not warehouse else "Warehouse"))
        start_time = time.time()
        
        if warehouse:
            pc_fqdn = self.database_operations.get_dh_warehouse_ntx_pc
        else:
            pc_fqdn = self.database_operations.get_dh_retail_ntx_pc
        self.pc_fqdn = pc_fqdn()
        if not self.pc_fqdn:
            self.logger.error("No data from db is collected for Host collector! PC data is empty.")

        self.logger.info("Start with getting central_pe using service account and update to db") # Temp solution for central_pe problem
        api_result_central_site = asyncio.run(self._async_method_api_for_pc_get_central_site_using_sa())
        pc_new_values_list_for_central_site = [self._columns_default(pc) for pc in self.pc_fqdn]
        self._db_update_central_site(pc_new_values_list_for_central_site, api_result_central_site)

        api_results = asyncio.run(self._async_method_api_for_pc())
        pc_fetched_lcm_dict = {k: v for d in api_results["PC_LCM"] for k, v in d.items()} # contains a dict of both config anf entity
        self.pc_fqdn = pc_fqdn() # refresh after the update of central_pe
        pc_new_values_list = [self._columns_default(pc) for pc in self.pc_fqdn]
        self._add_table_id(pc_new_values_list)
        self._add_lcm_related_values(pc_new_values_list, pc_fetched_lcm_dict)
        self._add_cluster_related_values(pc_new_values_list, api_results)
        self._add_cert_and_update_date(pc_new_values_list, api_results)
        pc_new_values_dict = {item["fqdn"]: item for item in pc_new_values_list}
        if warehouse:
            self.database_operations.update_db(pc_new_values_dict, ModelWarehousePrismCentral, ModelWarehousePrismCentral.fqdn, db.session)
        else:
            self.database_operations.update_db(pc_new_values_dict, ModelPrismCentral, ModelPrismCentral.fqdn, db.session)
        self.logger.info("PCCollector for {} completed in {:.2f} seconds".format("Retail" if not warehouse else "Warehouse", time.time() - start_time))
   
    def _db_update_central_site(self, pc_new_list, api_result_central_site):
        update_central_site_dict = {}
        for pc_new in pc_new_list:
            try:
                fqdn = pc_new["fqdn"]
                pc_domain = self.pc_fqdn[fqdn]["domain"]
                if not pc_domain:
                    self.logger.error(f" central site skipped and not updated since we could not collect it and need the current information for vault usage, lets hope its the right info still!: {fqdn}")
                    continue
                pc_new = {}
                pc_new["id"] = self.pc_fqdn[fqdn]["id"]
                central_pe = api_result_central_site[fqdn][0]
                pc_new["central_pe_fqdn"] = f"{central_pe}.{pc_domain}".lower()
                update_central_site_dict[fqdn] = pc_new
            except Exception as e:
                self.logger.error(f" central site skipped and not updated since we could not collect it and need the current information for vault usage, lets hope its the right info still!: {fqdn}: {e}")
                continue
        self.logger.info("Updating central site in db")
        if self.warehouse:
            self.database_operations.update_db(update_central_site_dict, ModelWarehousePrismCentral, ModelWarehousePrismCentral.fqdn, db.session)     
            return
        self.database_operations.update_db(update_central_site_dict, ModelPrismCentral, ModelPrismCentral.fqdn, db.session)

    def _add_table_id(self, pc_new_list):
        for pc_new in pc_new_list:
            try:
                pc_new["id"] = self.pc_fqdn[pc_new["fqdn"]]["id"]
            except Exception as e:
                self.logger.error(f"error for column id {pc_new['fqdn']}:  {e}")
                continue

    def _add_lcm_related_values(self, pc_new_list, pc_fetched_lcm_dict):
        
        for pc_new in pc_new_list:
            if not pc_fetched_lcm_dict[pc_new["fqdn"]]["lcm_config"]:
                self.logger.error(f"error for lcm_config {pc_new['fqdn']}:  {pc_fetched_lcm_dict[pc_new['fqdn']]['lcm_config']}")
                continue
            try:
                lcm_config = pc_fetched_lcm_dict[pc_new["fqdn"]]["lcm_config"]
                try:
                    pc_new["lcm_version"] = lcm_config.get("semantic_version", lcm_config.get("version"))

                except Exception as e:
                    self.logger.error(f"error for lcm version {pc_new['fqdn']}: {e}")
            except Exception as e:
                self.logger.error(f"error for lcm_config related values {pc_new['fqdn']}: {e}")

            if not pc_fetched_lcm_dict[pc_new["fqdn"]]["lcm_entity"]:
                self.logger.error(f"error for lcm_entity {pc_new['fqdn']}:  {pc_fetched_lcm_dict[pc_new['fqdn']]['lcm_entity']}")
                continue
            try:
                lcm_entity = pc_fetched_lcm_dict[pc_new["fqdn"]]["lcm_entity"]
                try:
                    pc_new["licensing_version"] = next((item.get("version") for item in lcm_entity if item.get("entityModel") == "Licensing"))
                except Exception as e:
                    self.logger.error(f"error for licensing version {pc_new['fqdn']}:  {e}")
                #try:
                    #pc_new["ncc_version"] = next((item.get("version") for item in lcm_entity if item.get("entityModel") == "NCC"))
                #except Exception as e:
                    #self.logger.error(f"error for ncc version {pc_new['fqdn']}: {e}")
                try:
                    pc_new["pc_version"] = next((item.get("version") for item in lcm_entity if item.get("entityModel") == "PC"))
                except Exception as e:
                    self.logger.error(f"error for pc version {pc_new['fqdn']}: {e}")
            except Exception as e:
                self.logger.error(f"error for lcm_entity related values {pc_new['fqdn']}: {e}")

    def _add_cluster_related_values(self, pc_new_list, api_results):
        pc_cluster = {item[0]: item[1] for item in api_results["PC_cluster"]}
        pc_cluster2 = {item[0]: item[1] for item in api_results["PC_cluster2"]}
        #pc_cluster3 = {item[0]: item[1] for item in api_results["PC_cluster3"]} # used with SA instead

        for pc_new in pc_new_list:
            fqdn = pc_new["fqdn"]

            if fqdn in pc_cluster and pc_cluster[fqdn]:
                try:
                    pc_new["uuid"] = pc_cluster[fqdn]["uuid"]
                except Exception as e:
                    self.logger.error(f"error for uuid {fqdn}: {e}")
                try:
                    pc_new["ip"] = pc_cluster[fqdn]["clusterExternalIPAddress"]
                except Exception as e:
                    self.logger.error(f"error for ip {fqdn}: {e}")
                try:
                    pc_new["pc_version"] = pc_cluster[fqdn]["version"]
                except Exception as e:
                    self.logger.error(f"error for pc version {fqdn}: {e}")
                try:
                    pc_new["ncc_version"] = pc_cluster[fqdn]["nccVersion"]

                except Exception as e:
                    self.logger.error(f"error for ncc version {fqdn}: {e}")

            if fqdn in pc_cluster2 and pc_cluster2[fqdn]:
                try:
                    pc_new["cluster_number"] = len(pc_cluster2[fqdn])
                except Exception as e:
                    self.logger.error(f"error for cluster number in {fqdn}: {e}")

            #if fqdn in pc_cluster3 and pc_cluster3[fqdn]:
              #  try:
                    #domain = self.pc_fqdn[fqdn]["domain"]
                   # pc_new["central_pe_fqdn"] = f'{pc_cluster3[fqdn]["spec"]["name"]}.{domain}'.lower()
               # except Exception as e:
                   # self.logger.error(f"error for {fqdn}: ", e)

    def _add_cert_and_update_date(self, pc_new_list, api_results):
        pc_cert = {item[0]: item[1] for item in api_results["PC_cert"]}
        for pc_new in pc_new_list:
            fqdn = pc_new["fqdn"]
            if fqdn in pc_cert and pc_cert[fqdn]:
                try:
                    cert_expiry_date = pc_cert[fqdn][1]["expiryDate"]
                    cert_expiry_date_conv = self._convert_cert_data(cert_expiry_date)
                    pc_new["cert_expiry_date"] = cert_expiry_date_conv
                except Exception as e:
                    self.logger.error(f"error for cert_expiry_date for {fqdn}: {e}")
            pc_new["last_update"] = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")


    def _convert_cert_data(self, cert_expiry_date):
        month_number = {
            "Jan": "01",
            "Feb": "02",
            "Mar": "03",
            "Apr": "04",
            "May": "05",
            "Jun": "06",
            "Jul": "07",
            "Aug": "08",
            "Sep": "09",
            "Oct": "10",
            "Nov": "11",
            "Dec": "12"
            }
        date_split = cert_expiry_date.split(" ")
        month = month_number[date_split[1]]
        return str(datetime.strptime(f"{date_split[5]}{month}{date_split[2]}", '%Y%m%d')).split(" ")[0]
    
    def _columns_default(self, pc):
        pc = {
            "id": None,
            "fqdn": pc.lower(),
            "lcm_version": None,
            "licensing_version": None,
            "ncc_version": None,
            "pc_version": None,
            "uuid": None,
            "cluster_number": None,
            "ip": None,
            #"central_pe_fqdn": None, managed separately
            "cert_expiry_date": None,
            "last_update": None,
        }
        return pc
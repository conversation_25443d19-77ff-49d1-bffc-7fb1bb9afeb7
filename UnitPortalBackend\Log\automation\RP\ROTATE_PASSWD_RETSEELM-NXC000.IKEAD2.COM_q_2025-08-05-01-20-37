2025-08-05 09:20:37,238 INFO Start to run the task.
2025-08-05 09:20:41,160 INFO Checking Maintenance Mode
2025-08-05 09:20:41,160 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-08-05 09:20:41,160 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:20:44,066 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 09:20:44,066 INFO SSH Executing '/home/<USER>/prism/cli/ncli host list --json=pretty'.
2025-08-05 09:20:44,899 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:20:49,901 INFO stdout: b'{\n  "data" : [ {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::4",\n    "uuid" : "8a276a5a-9cd0-4702-8ed9-5699d07c192e",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH967RD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::60",\n        "diskUuid" : "ba87f2bb-2824-44e3-8240-75c32c2c4e80",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH967RD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH99N2D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::51",\n        "diskUuid" : "35ea81b9-7cfb-4924-9b45-f07a4cd6fc3f",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N2D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH7B71D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::53",\n        "diskUuid" : "65a27c83-885e-4672-be27-05971598814b",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7B71D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9726D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::61",\n        "diskUuid" : "f4390867-7871-494b-8fd2-65dfa19de512",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9726D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH9B3ED",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::58",\n        "diskUuid" : "9979a224-9d9e-42bc-a282-b1af4e2f9acd",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B3ED",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9421D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::52",\n        "diskUuid" : "01e76618-04df-4259-a761-7e0bab50887b",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9421D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH8DKKD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::56",\n        "diskUuid" : "e31d9325-c534-4c9e-a67e-65081ebff024",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8DKKD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98J3D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::57",\n        "diskUuid" : "c61322d0-febf-45eb-8293-8bb50ef57e95",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98J3D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307893",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::55",\n        "diskUuid" : "d55987bc-a972-43e0-8c49-380c1fed8ff0",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307893",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307888",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::54",\n        "diskUuid" : "514be4f9-c6dc-4f16-805f-8372704d5f1d",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307888",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7001",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8S",\n    "blockSerial" : "CZ20240J8S",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753432724206906,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "454545",\n      "controller_num_iops" : "163",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "267466",\n      "controller_num_write_io" : "1630",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "1306497",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "324",\n      "controller_total_io_size_kbytes" : "15644",\n      "content_cache_hit_ppm" : "1000000",\n      "controller_num_io" : "1630",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "87",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "1",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "801",\n      "num_io" : "11",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "1564",\n      "hypervisor_num_received_bytes" : "4457349928607",\n      "hypervisor_timespan_usecs" : "29954673",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "31",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "181",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "1564",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "3281390424254",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "3",\n      "hypervisor_memory_usage_ppm" : "409960",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "163",\n      "total_io_time_usecs" : "1993",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "5",\n      "write_io_bandwidth_kBps" : "9",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "6",\n      "total_io_size_kbytes" : "127",\n      "io_bandwidth_kBps" : "12",\n      "content_cache_physical_memory_usage_bytes" : "3149611776",\n      "controller_timespan_usecs" : "10000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-397670632",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "545454",\n      "controller_avg_write_io_latency_usecs" : "801",\n      "content_cache_logical_memory_usage_bytes" : "2751941144"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "8904691712",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "1322599874560",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97238525408872",\n      "storage_tier.ssd.usage_bytes" : "1007249940480",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91527057217128",\n      "storage.usage_bytes" : "1016154632192",\n      "storage_tier.ssd.free_bytes" : "5711468191744"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::5",\n    "uuid" : "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH9453D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::63",\n        "diskUuid" : "55b9bef0-e64e-4b83-b56d-07424ac5a4fa",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9453D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH9B0JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::69",\n        "diskUuid" : "87299dda-79ad-4648-a18d-867837bdb061",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B0JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH947JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::66",\n        "diskUuid" : "21accc04-9438-4b1e-a735-121e6651e2c1",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH947JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9B2GD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::68",\n        "diskUuid" : "8fcec93c-f3a5-4a88-a18f-ad5a75a3d36e",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B2GD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH95WYD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::64",\n        "diskUuid" : "190548f4-f43c-40fd-8e0d-c805d972bf31",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH95WYD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9984D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::67",\n        "diskUuid" : "f73b0d65-6966-4e81-92fb-7815e0986aea",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9984D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH99N1D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::70",\n        "diskUuid" : "ebe3c979-d24f-46fb-9f2d-06b675e7f957",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N1D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98WLD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::65",\n        "diskUuid" : "3b10236d-18ef-48d1-b777-8f9f6b64ced8",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98WLD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N200095",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::72",\n        "diskUuid" : "4ce56fa0-31fa-45aa-9740-39f6561ab0e2",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N200095",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307878",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::71",\n        "diskUuid" : "064c52a5-ac8a-4f7f-ae98-967f225ddb32",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307878",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7002",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********30",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********30"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8R",\n    "blockSerial" : "CZ20240J8R",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753430337664248,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "2",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "4",\n      "read_io_ppm" : "710526",\n      "controller_num_iops" : "334",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "34915",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "280161",\n      "controller_num_write_io" : "6596",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "4555661",\n      "controller_total_read_io_size_kbytes" : "688",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "13165",\n      "content_cache_num_lookups" : "472",\n      "controller_total_io_size_kbytes" : "64772",\n      "content_cache_hit_ppm" : "1000000",\n      "controller_num_io" : "6684",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "87",\n      "num_write_iops" : "1",\n      "controller_num_random_io" : "0",\n      "num_iops" : "3",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "681",\n      "num_io" : "38",\n      "controller_num_read_io" : "88",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "34",\n      "controller_io_bandwidth_kBps" : "3238",\n      "hypervisor_num_received_bytes" : "4681455823379",\n      "hypervisor_timespan_usecs" : "30161073",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "396",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "142",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "3204",\n      "controller_write_io_ppm" : "986834",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "3385336702177",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "39",\n      "hypervisor_memory_usage_ppm" : "410090",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "329",\n      "total_io_time_usecs" : "5398",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "7",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "27",\n      "write_io_bandwidth_kBps" : "49",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "396",\n      "num_write_io" : "11",\n      "total_io_size_kbytes" : "886",\n      "io_bandwidth_kBps" : "88",\n      "content_cache_physical_memory_usage_bytes" : "2829458364",\n      "controller_timespan_usecs" : "20000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-359765932",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "289473",\n      "controller_avg_write_io_latency_usecs" : "685",\n      "content_cache_logical_memory_usage_bytes" : "2469692432"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "8882331648",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "1324495831040",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97237598490216",\n      "storage_tier.ssd.usage_bytes" : "1008199219200",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91527079577192",\n      "storage.usage_bytes" : "1017081550848",\n      "storage_tier.ssd.free_bytes" : "5710518913024"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::6",\n    "uuid" : "34ff0abd-9dee-4e7d-9481-4716d64569b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH96JDD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::41",\n        "diskUuid" : "2c172a78-3626-4861-bfff-98d6d79fef49",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH96JDD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH995TD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::43",\n        "diskUuid" : "94de93d2-5e8f-44d7-85d7-c414177670a7",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH995TD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH991DD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::46",\n        "diskUuid" : "4d3bffc9-fa0d-4ff3-bd95-436cf68ab516",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH991DD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9825D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::45",\n        "diskUuid" : "ea324991-7bb3-49a9-84f6-7c30e1c938a5",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9825D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH8XYHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::48",\n        "diskUuid" : "5f14642e-c45b-4efc-b5ee-95c532192c22",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8XYHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH99MHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::44",\n        "diskUuid" : "5758e233-4edc-4045-8446-0625e9ba05c0",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99MHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH9ADHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::47",\n        "diskUuid" : "dbeb0a0e-ff11-4b98-a2b7-a45bad3407fd",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9ADHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH7XGHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::42",\n        "diskUuid" : "08dba0c0-cd98-4d75-a131-a60323ba43b5",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7XGHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307864",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::50",\n        "diskUuid" : "39e3bde6-2802-4ad2-825c-a0d61ae9fb35",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307864",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307881",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::49",\n        "diskUuid" : "a032c48d-3866-4fd6-88af-1f6a25fe5ac3",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307881",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7003",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********31",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********31"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8Q",\n    "blockSerial" : "CZ20240J8Q",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "ncli_manual",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 4,\n    "bootTimeInUsecs" : 1753435095319593,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "18",\n      "read_io_ppm" : "0",\n      "controller_num_iops" : "136",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "10759",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "201233",\n      "controller_num_write_io" : "1178",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "887902",\n      "controller_total_read_io_size_kbytes" : "1472",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "135095",\n      "content_cache_num_lookups" : "895",\n      "controller_total_io_size_kbytes" : "11324",\n      "content_cache_hit_ppm" : "994413",\n      "controller_num_io" : "1362",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "97",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "651",\n      "num_io" : "2",\n      "controller_num_read_io" : "184",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "147",\n      "controller_io_bandwidth_kBps" : "1132",\n      "hypervisor_num_received_bytes" : "2808020829519",\n      "hypervisor_timespan_usecs" : "30036290",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "4",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "198",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "985",\n      "controller_write_io_ppm" : "864904",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "4982676903636",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "0",\n      "hypervisor_memory_usage_ppm" : "511591",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "117",\n      "total_io_time_usecs" : "397",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "8",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "0",\n      "write_io_bandwidth_kBps" : "6",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "8",\n      "controller_avg_read_io_latency_usecs" : "58",\n      "num_write_io" : "2",\n      "total_io_size_kbytes" : "64",\n      "io_bandwidth_kBps" : "6",\n      "content_cache_physical_memory_usage_bytes" : "6279657516",\n      "controller_timespan_usecs" : "10000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-160328388",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "1000000",\n      "controller_avg_write_io_latency_usecs" : "744",\n      "content_cache_logical_memory_usage_bytes" : "6119329128"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "1684717568",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "94144479232",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "98219539652200",\n      "storage_tier.ssd.usage_bytes" : "33455671296",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91534277191272",\n      "storage.usage_bytes" : "35140388864",\n      "storage_tier.ssd.free_bytes" : "6685262460928"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  } ],\n  "status" : 0\n}\n'
2025-08-05 09:20:49,920 INFO All good, no hosts are set as NCLI maintenance inside this cluster.
2025-08-05 09:20:49,920 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-08-05 09:20:49,920 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:20:52,453 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 09:20:52,453 INFO SSH Executing '/usr/local/nutanix/bin/acli -o json host.list'.
2025-08-05 09:20:53,347 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:20:58,348 INFO stdout: b'{"data": [{"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "8a276a5a-9cd0-4702-8ed9-5699d07c192e", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "34ff0abd-9dee-4e7d-9481-4716d64569b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}], "error": null, "status": 0}\n'
2025-08-05 09:20:58,348 INFO This seems a very old AOS version...
2025-08-05 09:20:58,348 INFO This seems a very old AOS version...
2025-08-05 09:20:58,348 INFO This seems a very old AOS version...
2025-08-05 09:20:58,371 INFO All good, no hosts are set as ACLI maintenance inside this cluster.
2025-08-05 09:20:58,391 INFO Checking CVM status
2025-08-05 09:20:58,863 INFO Trying to SSH to the RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 09:20:58,863 INFO First try with username/password.
2025-08-05 09:20:58,863 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:21:01,458 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 09:21:07,604 INFO Sending 'cluster status |grep -v UP' to the server.
2025-08-05 09:21:23,606 INFO CVM IP:*********** Status:Up
2025-08-05 09:21:23,608 INFO CVM IP:*********** Status:Up
2025-08-05 09:21:23,608 INFO CVM IP:*********** Status:Up
2025-08-05 09:21:23,608 INFO Great, all CVM status are Up
2025-08-05 09:21:23,658 INFO Calling restapi, URL: https://retseelm-nxc000.ikead2.com:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:21:23,658 INFO params: None
2025-08-05 09:21:23,658 INFO User: <EMAIL>
2025-08-05 09:21:23,658 INFO payload: None
2025-08-05 09:21:23,658 INFO files: None
2025-08-05 09:21:23,658 INFO timeout: 30
2025-08-05 09:21:25,606 INFO Getting host list from retseelm-nxc000.
2025-08-05 09:21:25,606 INFO Got the host list from retseelm-nxc000.
2025-08-05 09:21:25,606 INFO Got the host list.
2025-08-05 09:21:25,606 INFO Getting vault from IKEAD2.
2025-08-05 09:21:26,207 INFO Getting Site_Pe_Nutanix.
2025-08-05 09:21:26,691 INFO Got Site_Pe_Nutanix.
2025-08-05 09:21:26,691 INFO Getting Site_Pe_Admin.
2025-08-05 09:21:27,154 INFO Got Site_Pe_Admin.
2025-08-05 09:21:27,154 INFO Getting Site_Oob.
2025-08-05 09:21:27,667 INFO Got Site_Oob.
2025-08-05 09:21:27,667 INFO Getting Site_Ahv_Nutanix.
2025-08-05 09:21:28,137 INFO Got Site_Ahv_Nutanix.
2025-08-05 09:21:28,137 INFO Getting Site_Ahv_Root.
2025-08-05 09:21:28,602 INFO Got Site_Ahv_Root.
2025-08-05 09:21:28,602 INFO Getting Site_Gw_Priv_Key.
2025-08-05 09:21:29,092 INFO Got Site_Gw_Priv_Key.
2025-08-05 09:21:29,092 INFO Getting Site_Gw_Pub_Key.
2025-08-05 09:21:29,730 INFO Got Site_Gw_Pub_Key.
2025-08-05 09:21:29,730 INFO Getting Site_Pe_Svc.
2025-08-05 09:21:30,408 INFO Got Site_Pe_Svc.
2025-08-05 09:21:30,412 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-05 09:21:30,492 INFO Connecting to CVM *********** for AHV password updates
2025-08-05 09:21:30,492 INFO SSH connecting to ***********, this is the '1' try.
2025-08-05 09:21:32,453 INFO SSH connected to *********** with SSHKEY.
2025-08-05 09:21:33,586 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 09:21:38,610 INFO Start reset AHV user nutanix password from CVM ***********
2025-08-05 09:21:38,625 INFO unlocking nutanix account
2025-08-05 09:21:38,625 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 09:21:49,628 INFO Sending '*' to the server.
2025-08-05 09:22:05,129 INFO AHV User nutanix Password Update Success
2025-08-05 09:22:05,145 INFO Start reset AHV user root password from CVM ***********
2025-08-05 09:22:05,159 INFO unlocking root account
2025-08-05 09:22:05,159 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 09:22:15,664 INFO Sending '*' to the server.
2025-08-05 09:22:31,170 INFO AHV User root Password Update Success
2025-08-05 09:22:31,191 INFO Connecting to CVM *********** for AHV password updates
2025-08-05 09:22:31,201 INFO SSH connecting to ***********, this is the '1' try.
2025-08-05 09:22:33,172 INFO SSH connected to *********** with SSHKEY.
2025-08-05 09:22:34,340 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 09:22:39,361 INFO Start reset AHV user nutanix password from CVM ***********
2025-08-05 09:22:39,372 INFO unlocking nutanix account
2025-08-05 09:22:39,372 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 09:22:50,374 INFO Sending '*' to the server.
2025-08-05 09:23:05,909 INFO AHV User nutanix Password Update Success
2025-08-05 09:23:05,947 INFO Start reset AHV user root password from CVM ***********
2025-08-05 09:23:05,976 INFO unlocking root account
2025-08-05 09:23:05,976 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 09:23:16,479 INFO Sending '*' to the server.
2025-08-05 09:23:32,005 INFO AHV User root Password Update Success
2025-08-05 09:23:32,093 INFO Connecting to CVM *********** for AHV password updates
2025-08-05 09:23:32,094 INFO SSH connecting to ***********, this is the '1' try.
2025-08-05 09:23:34,178 INFO SSH connected to *********** with SSHKEY.
2025-08-05 09:23:35,343 INFO Sending 'ssh root@192.168.5.1' to the server.
2025-08-05 09:23:40,360 INFO Start reset AHV user nutanix password from CVM ***********
2025-08-05 09:23:40,378 INFO unlocking nutanix account
2025-08-05 09:23:40,378 INFO Sending 'sudo faillock --user nutanix --reset' to the server.
2025-08-05 09:23:51,385 INFO Sending '*' to the server.
2025-08-05 09:24:06,891 INFO AHV User nutanix Password Update Success
2025-08-05 09:24:06,916 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Ahv_Nutanix
2025-08-05 09:24:07,680 INFO Saving token completed.
2025-08-05 09:24:07,697 INFO Start reset AHV user root password from CVM ***********
2025-08-05 09:24:07,709 INFO unlocking root account
2025-08-05 09:24:07,709 INFO Sending 'sudo faillock --user root --reset' to the server.
2025-08-05 09:24:18,211 INFO Sending '*' to the server.
2025-08-05 09:24:33,719 INFO AHV User root Password Update Success
2025-08-05 09:24:33,719 INFO Saving token to Vault... Username: root, label: RETSEELM-NXC000/Site_Ahv_Root
2025-08-05 09:24:34,471 INFO Saving token completed.
2025-08-05 09:24:34,485 INFO Starting to reset iLO password for user 'administrator' across all hosts.
2025-08-05 09:24:34,495 INFO Updating host RETSEELM-NX7001 (*************)
2025-08-05 09:24:34,510 INFO Connecting to Redfish API on ************* to reset password for user 'administrator'.
2025-08-05 09:24:34,511 INFO Finding account URI for user 'administrator'.
2025-08-05 09:24:34,511 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 09:24:34,511 INFO params: None
2025-08-05 09:24:34,512 INFO User: administrator
2025-08-05 09:24:34,512 INFO payload: None
2025-08-05 09:24:34,512 INFO files: None
2025-08-05 09:24:34,512 INFO timeout: None
2025-08-05 09:24:35,696 INFO Got the response with OK
2025-08-05 09:24:35,701 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 09:24:35,701 INFO params: None
2025-08-05 09:24:35,701 INFO User: administrator
2025-08-05 09:24:35,701 INFO payload: None
2025-08-05 09:24:35,701 INFO files: None
2025-08-05 09:24:35,701 INFO timeout: None
2025-08-05 09:24:36,921 INFO Got the response with OK
2025-08-05 09:24:36,923 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 09:24:36,924 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 09:24:36,924 INFO params: None
2025-08-05 09:24:36,925 INFO User: administrator
2025-08-05 09:24:36,925 INFO payload: {'Password': '*****'}
2025-08-05 09:24:36,926 INFO files: None
2025-08-05 09:24:36,926 INFO timeout: None
2025-08-05 09:24:38,203 INFO ILO object updated successfully
2025-08-05 09:24:38,223 INFO Successfully updated iLO password for user 'administrator' on *************.
2025-08-05 09:24:38,237 INFO Updating host RETSEELM-NX7002 (***********30)
2025-08-05 09:24:38,258 INFO Connecting to Redfish API on ***********30 to reset password for user 'administrator'.
2025-08-05 09:24:38,258 INFO Finding account URI for user 'administrator'.
2025-08-05 09:24:38,258 INFO Calling restapi, URL: https://***********30/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 09:24:38,258 INFO params: None
2025-08-05 09:24:38,258 INFO User: administrator
2025-08-05 09:24:38,258 INFO payload: None
2025-08-05 09:24:38,258 INFO files: None
2025-08-05 09:24:38,258 INFO timeout: None
2025-08-05 09:24:39,458 INFO Got the response with OK
2025-08-05 09:24:39,458 INFO Calling restapi, URL: https://***********30/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 09:24:39,458 INFO params: None
2025-08-05 09:24:39,458 INFO User: administrator
2025-08-05 09:24:39,458 INFO payload: None
2025-08-05 09:24:39,458 INFO files: None
2025-08-05 09:24:39,458 INFO timeout: None
2025-08-05 09:24:40,631 INFO Got the response with OK
2025-08-05 09:24:40,631 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 09:24:40,631 INFO Calling restapi, URL: https://***********30/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 09:24:40,642 INFO params: None
2025-08-05 09:24:40,642 INFO User: administrator
2025-08-05 09:24:40,642 INFO payload: {'Password': '*****'}
2025-08-05 09:24:40,642 INFO files: None
2025-08-05 09:24:40,642 INFO timeout: None
2025-08-05 09:24:41,867 INFO ILO object updated successfully
2025-08-05 09:24:41,908 INFO Successfully updated iLO password for user 'administrator' on ***********30.
2025-08-05 09:24:41,918 INFO Updating host RETSEELM-NX7003 (***********31)
2025-08-05 09:24:41,928 INFO Connecting to Redfish API on ***********31 to reset password for user 'administrator'.
2025-08-05 09:24:41,928 INFO Finding account URI for user 'administrator'.
2025-08-05 09:24:41,928 INFO Calling restapi, URL: https://***********31/redfish/v1/AccountService/Accounts/, method: GET, headers: None
2025-08-05 09:24:41,928 INFO params: None
2025-08-05 09:24:41,928 INFO User: administrator
2025-08-05 09:24:41,928 INFO payload: None
2025-08-05 09:24:41,928 INFO files: None
2025-08-05 09:24:41,928 INFO timeout: None
2025-08-05 09:24:43,207 INFO Got the response with OK
2025-08-05 09:24:43,209 INFO Calling restapi, URL: https://***********31/redfish/v1/AccountService/Accounts/1, method: GET, headers: None
2025-08-05 09:24:43,209 INFO params: None
2025-08-05 09:24:43,209 INFO User: administrator
2025-08-05 09:24:43,209 INFO payload: None
2025-08-05 09:24:43,209 INFO files: None
2025-08-05 09:24:43,211 INFO timeout: None
2025-08-05 09:24:44,375 INFO Got the response with OK
2025-08-05 09:24:44,378 INFO Sending PATCH request to AccountService/Accounts/1 to update the password.
2025-08-05 09:24:44,378 INFO Calling restapi, URL: https://***********31/redfish/v1/AccountService/Accounts/1, method: PATCH, headers: None
2025-08-05 09:24:44,378 INFO params: None
2025-08-05 09:24:44,378 INFO User: administrator
2025-08-05 09:24:44,379 INFO payload: {'Password': '*****'}
2025-08-05 09:24:44,379 INFO files: None
2025-08-05 09:24:44,379 INFO timeout: None
2025-08-05 09:24:45,700 INFO ILO object updated successfully
2025-08-05 09:24:45,712 INFO Successfully updated iLO password for user 'administrator' on ***********31.
2025-08-05 09:24:45,723 INFO Successfully updated password for 'administrator' on all hosts. Saving to Vault.
2025-08-05 09:24:45,730 INFO Saving token to Vault... Username: administrator, label: RETSEELM-NXC000/Site_Oob
2025-08-05 09:24:46,414 INFO Saving token completed.
2025-08-05 09:24:46,424 INFO Start reset CVM Nutanix password
2025-08-05 09:24:46,441 INFO Attempting to reset password for user 'nutanix' on cluster associated with CVM RETSEELM-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-05 09:24:46,441 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-05 09:24:49,005 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-05 09:24:49,005 INFO Unlocking user 'nutanix' on all CVMs.
2025-08-05 09:24:49,005 INFO SSH Executing 'allssh sudo faillock --user nutanix --reset'.
2025-08-05 09:24:49,852 INFO Waiting for 10 seconds for the execution.
2025-08-05 09:24:59,852 INFO stdout: b''
2025-08-05 09:24:59,864 INFO Changing password for 'nutanix' on single node RETSEELM-NXC000.ikead2.com.
2025-08-05 09:24:59,864 INFO SSH Executing '*****************************************************'.
2025-08-05 09:25:00,384 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:25:05,389 INFO stdout: b'Changing password for user nutanix.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 09:25:05,411 INFO Password change command sent. Verifying new password for 'nutanix' with a new SSH connection.
2025-08-05 09:25:10,414 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-05 09:25:13,015 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-05 09:25:13,083 INFO Successfully reset and verified new password for user 'nutanix'.
2025-08-05 09:25:13,094 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Pe_Nutanix
2025-08-05 09:25:13,896 INFO Saving token completed.
2025-08-05 09:25:14,475 INFO taking a 30S extra powernap
2025-08-05 09:25:44,482 INFO Start reset admin password
2025-08-05 09:25:44,504 INFO Attempting to reset password for user 'admin' on cluster associated with CVM RETSEELM-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-05 09:25:44,504 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-05 09:25:47,077 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-05 09:25:47,092 INFO Unlocking user 'admin' on all CVMs.
2025-08-05 09:25:47,092 INFO SSH Executing 'allssh sudo faillock --user admin --reset'.
2025-08-05 09:25:47,935 INFO Waiting for 10 seconds for the execution.
2025-08-05 09:25:57,940 INFO stdout: b''
2025-08-05 09:25:57,951 INFO Changing password for 'admin' on single node RETSEELM-NXC000.ikead2.com.
2025-08-05 09:25:57,952 INFO SSH Executing '***************************************************'.
2025-08-05 09:25:58,478 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:26:03,482 INFO stdout: b'Changing password for user admin.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 09:26:03,498 INFO Password change command sent. Verifying new password for 'admin' with a new SSH connection.
2025-08-05 09:26:08,501 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-05 09:26:11,080 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-05 09:26:11,100 INFO Successfully reset and verified new password for user 'admin'.
2025-08-05 09:26:11,112 INFO Saving token to Vault... Username: admin, label: RETSEELM-NXC000/Site_Pe_Admin
2025-08-05 09:26:11,811 INFO Saving token completed.
2025-08-05 09:28:09,598 INFO This is a central PE, start to reset PCVM password...
2025-08-05 09:28:09,599 INFO Resetting password for Site_Pc_Nutanix...
2025-08-05 09:28:10,563 INFO taking a 30S extra powernap
2025-08-05 09:28:40,746 INFO Start reset PCVM Site_Pc_Nutanix password
2025-08-05 09:28:49,780 INFO Attempting to reset password for user 'nutanix' on cluster associated with CVM ***********2, authenticating as 'nutanix'.
2025-08-05 09:28:49,780 INFO SSH connecting to ***********2, this is the '1' try.
2025-08-05 09:28:52,859 INFO SSH connected to ***********2.
2025-08-05 09:28:52,878 INFO Unlocking user 'nutanix' on all CVMs.
2025-08-05 09:28:52,878 INFO SSH Executing 'allssh sudo faillock --user nutanix --reset'.
2025-08-05 09:28:53,721 INFO Waiting for 10 seconds for the execution.
2025-08-05 09:29:03,723 INFO stdout: b''
2025-08-05 09:29:03,730 INFO Changing password for 'nutanix' on single node ***********2.
2025-08-05 09:29:03,730 INFO SSH Executing '*****************************************************'.
2025-08-05 09:29:04,261 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:29:09,262 INFO stdout: b'Changing password for user nutanix.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 09:29:09,283 INFO Password change command sent. Verifying new password for 'nutanix' with a new SSH connection.
2025-08-05 09:29:14,284 INFO SSH connecting to ***********2, this is the '1' try.
2025-08-05 09:29:16,830 INFO SSH connected to ***********2.
2025-08-05 09:29:16,842 INFO Successfully reset and verified new password for user 'nutanix'.
2025-08-05 09:29:17,458 INFO Resetting password for Site_Pc_Admin...
2025-08-05 09:29:18,515 INFO taking a 30S extra powernap
2025-08-05 09:29:48,527 INFO Start reset PCVM Site_Pc_Admin password
2025-08-05 09:29:57,526 INFO Attempting to reset password for user 'admin' on cluster associated with CVM ***********2, authenticating as 'nutanix'.
2025-08-05 09:29:57,526 INFO SSH connecting to ***********2, this is the '1' try.
2025-08-05 09:30:00,111 INFO SSH connected to ***********2.
2025-08-05 09:30:00,127 INFO Unlocking user 'admin' on all CVMs.
2025-08-05 09:30:00,127 INFO SSH Executing 'allssh sudo faillock --user admin --reset'.
2025-08-05 09:30:00,955 INFO Waiting for 10 seconds for the execution.
2025-08-05 09:30:10,964 INFO stdout: b''
2025-08-05 09:30:10,979 INFO Changing password for 'admin' on single node ***********2.
2025-08-05 09:30:10,980 INFO SSH Executing '***************************************************'.
2025-08-05 09:30:11,521 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:30:16,522 INFO stdout: b'Changing password for user admin.\npasswd: all authentication tokens updated successfully.\n'
2025-08-05 09:30:16,533 INFO Password change command sent. Verifying new password for 'admin' with a new SSH connection.
2025-08-05 09:30:21,537 INFO SSH connecting to ***********2, this is the '1' try.
2025-08-05 09:30:24,196 INFO SSH connected to ***********2.
2025-08-05 09:30:24,212 INFO Successfully reset and verified new password for user 'admin'.
2025-08-05 09:30:24,814 INFO Start reset 1-click-nutanix password for PE RETSEELM-NXC000
2025-08-05 09:30:25,332 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-08-05 09:30:25,332 INFO params: None
2025-08-05 09:30:25,332 INFO User: admin
2025-08-05 09:30:25,332 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-08-05 09:30:25,332 INFO files: None
2025-08-05 09:30:25,332 INFO timeout: None
2025-08-05 09:30:27,608 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-08-05 09:30:27,608 INFO params: None
2025-08-05 09:30:27,608 INFO User: admin
2025-08-05 09:30:27,608 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-08-05 09:30:27,608 INFO files: None
2025-08-05 09:30:27,608 INFO timeout: None
2025-08-05 09:30:30,271 INFO This is centrol PE, Start reset 1-click-nutanix password for Pc ssp-dhd2-ntx.ikead2.com
2025-08-05 09:30:31,120 INFO Saving token to Vault... Username: 1-click-nutanix, label: RETSEELM-NXC000/Site_Pe_Svc
2025-08-05 09:30:32,231 INFO Saving token completed.
2025-08-05 09:30:32,242 INFO ****************************************************************************************************
2025-08-05 09:30:32,242 INFO *                                                                                                  *
2025-08-05 09:30:32,242 INFO *                                          Renew SSH key                                           *
2025-08-05 09:30:32,242 INFO *                                                                                                  *
2025-08-05 09:30:32,242 INFO ****************************************************************************************************
2025-08-05 09:30:33,757 INFO Check if ssh_key exist
2025-08-05 09:30:33,757 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: GET, headers: None
2025-08-05 09:30:33,758 INFO params: None
2025-08-05 09:30:33,758 INFO User: admin
2025-08-05 09:30:33,758 INFO payload: None
2025-08-05 09:30:33,759 INFO files: None
2025-08-05 09:30:33,759 INFO timeout: None
2025-08-05 09:30:35,688 INFO SSH key exist, we need replace it.
2025-08-05 09:30:35,688 INFO Deleting public key...
2025-08-05 09:30:35,688 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys/Gateway, method: DELETE, headers: None
2025-08-05 09:30:35,688 INFO params: None
2025-08-05 09:30:35,688 INFO User: admin
2025-08-05 09:30:35,688 INFO payload: None
2025-08-05 09:30:35,688 INFO files: None
2025-08-05 09:30:35,688 INFO timeout: None
2025-08-05 09:30:37,305 INFO Delete public key finished.
2025-08-05 09:30:37,305 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: GET, headers: None
2025-08-05 09:30:37,305 INFO params: None
2025-08-05 09:30:37,305 INFO User: admin
2025-08-05 09:30:37,305 INFO payload: None
2025-08-05 09:30:37,305 INFO files: None
2025-08-05 09:30:37,305 INFO timeout: None
2025-08-05 09:30:38,776 INFO SSH key exist, we need replace it.
2025-08-05 09:30:38,776 INFO Deleting public key...
2025-08-05 09:30:38,777 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys/Gateway, method: DELETE, headers: None
2025-08-05 09:30:38,777 INFO params: None
2025-08-05 09:30:38,777 INFO User: admin
2025-08-05 09:30:38,777 INFO payload: None
2025-08-05 09:30:38,777 INFO files: None
2025-08-05 09:30:38,777 INFO timeout: None
2025-08-05 09:30:40,447 INFO Delete public key finished.
2025-08-05 09:30:40,458 INFO Generating ssh_key
2025-08-05 09:30:40,459 INFO Generating SSH key: ssh-keygen -t rsa -b 2048 -f c:\Dev\UnitPortalBackend\tmp\sshkey\RETSEELM-NXC000_2025-08-05-01-30-33\prvkey -q -N "" -m PEM
2025-08-05 09:30:40,866 INFO Key pair generated: c:\Dev\UnitPortalBackend\tmp\sshkey\RETSEELM-NXC000_2025-08-05-01-30-33\prvkey
2025-08-05 09:30:40,873 INFO Installing public key...
2025-08-05 09:30:40,873 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: POST, headers: None
2025-08-05 09:30:40,873 INFO params: None
2025-08-05 09:30:40,873 INFO User: admin
2025-08-05 09:30:40,873 INFO payload: {'name': 'Gateway', 'key': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDGTBXGDur0buTZv0J6zygGy9Ow8rKYQ5pKJLXV28csz20tXTNNVDbL+dvy6JAn+nqRG3ZKsCql0/Tj0uaToGuZOyU+pK3r+D/B+JsR1Fu5kTRJnKfV13J+2XgYPG8SK5AC8LXsNmE1KO+wkClgVkSuraR475qBo7HliRfny5l6KETJiTpd4NS0gTVAJ2rlXLx41rwksSeieO9SQRAijlg3i6XM7RSjjB7JyRkMQGAoNkNRaU42rrg/OQ3vo9uA0Qt1gyTZDo6u5XB19VIzDrLC7+EnoD833fB539Ewp4kCtjXk9+5N4My732GT1lEy/yoCL2hIM8XxZExtNZRXAV6L ikea\\hunhe@ITCNSHG-NB0436'}
2025-08-05 09:30:40,873 INFO files: None
2025-08-05 09:30:40,873 INFO timeout: None
2025-08-05 09:30:42,718 INFO Install public key finished.
2025-08-05 09:30:42,724 INFO Installing public key...
2025-08-05 09:30:42,724 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/cluster/public_keys, method: POST, headers: None
2025-08-05 09:30:42,724 INFO params: None
2025-08-05 09:30:42,724 INFO User: admin
2025-08-05 09:30:42,724 INFO payload: {'name': 'Gateway', 'key': 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDGTBXGDur0buTZv0J6zygGy9Ow8rKYQ5pKJLXV28csz20tXTNNVDbL+dvy6JAn+nqRG3ZKsCql0/Tj0uaToGuZOyU+pK3r+D/B+JsR1Fu5kTRJnKfV13J+2XgYPG8SK5AC8LXsNmE1KO+wkClgVkSuraR475qBo7HliRfny5l6KETJiTpd4NS0gTVAJ2rlXLx41rwksSeieO9SQRAijlg3i6XM7RSjjB7JyRkMQGAoNkNRaU42rrg/OQ3vo9uA0Qt1gyTZDo6u5XB19VIzDrLC7+EnoD833fB539Ewp4kCtjXk9+5N4My732GT1lEy/yoCL2hIM8XxZExtNZRXAV6L ikea\\hunhe@ITCNSHG-NB0436'}
2025-08-05 09:30:42,724 INFO files: None
2025-08-05 09:30:42,724 INFO timeout: None
2025-08-05 09:30:44,910 INFO Install public key finished.
2025-08-05 09:30:44,927 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Gw_Priv_Key
2025-08-05 09:30:45,697 INFO Saving token completed.
2025-08-05 09:30:45,711 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Gw_Pub_Key
2025-08-05 09:30:46,356 INFO Saving token completed.
2025-08-05 09:30:46,554 INFO Task is in 'Done' status.

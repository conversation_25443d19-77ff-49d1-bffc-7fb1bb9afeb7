2025-08-01 10:59:32,616 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-01 10:59:32,617 INFO Getting the cluster list from PC.
2025-08-01 10:59:32,617 INFO Getting cluster list from ssp-dhd2-ntx.ikead2.com.
2025-08-01 10:59:32,617 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/api/nutanix/v3/clusters/list, method: POST, headers: None
2025-08-01 10:59:32,617 INFO params: None
2025-08-01 10:59:32,617 INFO User: <EMAIL>
2025-08-01 10:59:32,617 INFO payload: {'kind': 'cluster'}
2025-08-01 10:59:32,626 INFO files: None
2025-08-01 10:59:32,628 INFO timeout: None
2025-08-01 10:59:34,391 WARNING Response content: b'{"message": "ldap: failed to fetch groups or OUs for user CN=L-MTEAUTO-A-RETSEELM,OU=ServiceAccounts,OU=ELM,OU=SE,OU=RET,OU=HBG,DC=ikead2,DC=com : ldap: failed to query groups for user CN=L-MTEAUTO-A-RETSEELM,OU=ServiceAccounts,OU=ELM,OU=SE,OU=RET,OU=HBG,DC=ikead2,DC=com : failed to connect: LDAP Result Code 200 "Network Error": read tcp 10.100.0.44:57002->10.62.176.31:636: read: connection reset by peer"}'
2025-08-01 10:59:34,391 WARNING API response is not ok, going to do the 2 retry...
2025-08-01 10:59:35,496 WARNING Response content: b'{"message": "ldap: failed to fetch groups or OUs for user CN=L-MTEAUTO-A-RETSEELM,OU=ServiceAccounts,OU=ELM,OU=SE,OU=RET,OU=HBG,DC=ikead2,DC=com : ldap: failed to query groups for user CN=L-MTEAUTO-A-RETSEELM,OU=ServiceAccounts,OU=ELM,OU=SE,OU=RET,OU=HBG,DC=ikead2,DC=com : failed to connect: LDAP Result Code 200 "Network Error": read tcp 10.100.2.45:43224->10.62.176.31:636: read: connection reset by peer"}'
2025-08-01 10:59:35,496 WARNING API response is not ok, going to do the 3 retry...
2025-08-01 10:59:36,644 WARNING Response content: b'{"message": "failed to connect: LDAP Result Code 200 "Network Error": read tcp 10.100.1.61:54344->10.62.176.31:636: read: connection reset by peer"}'
2025-08-01 10:59:36,644 WARNING API response is not ok, going to do the 4 retry...
2025-08-01 10:59:37,760 WARNING Response content: b'{"message": "failed to connect: LDAP Result Code 200 "Network Error": read tcp 10.100.0.44:57018->10.62.176.31:636: read: connection reset by peer"}'
2025-08-01 10:59:37,760 WARNING API response is not ok, going to do the 5 retry...
2025-08-01 10:59:38,832 WARNING Response content: b'{"message": "failed to connect: LDAP Result Code 200 "Network Error": read tcp 10.100.2.45:43228->10.62.176.31:636: read: connection reset by peer"}'
2025-08-01 10:59:38,832 ERROR Can't get the cluster list..
2025-08-01 10:59:38,832 ERROR Could not get the cluster list from PC.

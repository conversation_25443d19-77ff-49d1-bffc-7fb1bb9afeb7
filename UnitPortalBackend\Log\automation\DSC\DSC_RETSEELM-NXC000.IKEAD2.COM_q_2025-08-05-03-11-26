2025-08-05 11:11:26,384 INFO Start to run the task.
2025-08-05 11:11:26,455 INFO ****************************************************************************************************
2025-08-05 11:11:26,456 INFO *                                                                                                  *
2025-08-05 11:11:26,456 INFO *                                  Configuring MaintenanceMode...                                  *
2025-08-05 11:11:26,457 INFO *                                                                                                  *
2025-08-05 11:11:26,467 INFO ****************************************************************************************************
2025-08-05 11:11:30,816 INFO Checking Maintenance Mode via v2.0 API (/hosts)
2025-08-05 11:11:30,818 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/hosts, method: GET, headers: None
2025-08-05 11:11:30,818 INFO params: None
2025-08-05 11:11:30,818 INFO User: 1-click-nutanix
2025-08-05 11:11:30,818 INFO payload: None
2025-08-05 11:11:30,818 INFO files: None
2025-08-05 11:11:30,818 INFO timeout: 30
2025-08-05 11:11:32,140 INFO API Check: All good, no hosts or CVMs are in maintenance mode.
2025-08-05 11:11:32,161 INFO Checking CVM status
2025-08-05 11:11:33,234 INFO Trying to SSH to the RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 11:11:33,234 INFO First try with username/password.
2025-08-05 11:11:33,235 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 11:11:35,779 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 11:11:41,899 INFO Sending 'cluster status |grep -v UP' to the server.
2025-08-05 11:11:57,902 INFO CVM IP:*********** Status:Up
2025-08-05 11:11:57,902 INFO CVM IP:*********** Status:Up
2025-08-05 11:11:57,902 INFO CVM IP:*********** Status:Up
2025-08-05 11:11:57,903 INFO Great, all CVM status are Up
2025-08-05 11:11:57,913 INFO MaintenanceMode: Done
2025-08-05 11:11:57,923 INFO ****************************************************************************************************
2025-08-05 11:11:57,924 INFO *                                                                                                  *
2025-08-05 11:11:57,924 INFO *                                    Configuring IpamHealth...                                     *
2025-08-05 11:11:57,924 INFO *                                                                                                  *
2025-08-05 11:11:57,924 INFO ****************************************************************************************************
2025-08-05 11:12:00,304 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 11:12:00,304 INFO params: None
2025-08-05 11:12:00,305 INFO User: <EMAIL>
2025-08-05 11:12:00,305 INFO payload: None
2025-08-05 11:12:00,305 INFO files: None
2025-08-05 11:12:00,305 INFO timeout: 30
2025-08-05 11:12:01,704 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=parent_subnet_id='81005', method: GET, headers: None
2025-08-05 11:12:01,704 INFO params: None
2025-08-05 11:12:01,704 INFO User: <EMAIL>
2025-08-05 11:12:01,705 INFO payload: None
2025-08-05 11:12:01,705 INFO files: None
2025-08-05 11:12:01,705 INFO timeout: 30
2025-08-05 11:12:02,782 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91749', method: GET, headers: None
2025-08-05 11:12:02,782 INFO params: None
2025-08-05 11:12:02,782 INFO User: <EMAIL>
2025-08-05 11:12:02,783 INFO payload: None
2025-08-05 11:12:02,783 INFO files: None
2025-08-05 11:12:02,783 INFO timeout: 30
2025-08-05 11:12:03,856 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 11:12:03,856 INFO params: None
2025-08-05 11:12:03,856 INFO User: <EMAIL>
2025-08-05 11:12:03,856 INFO payload: None
2025-08-05 11:12:03,858 INFO files: None
2025-08-05 11:12:03,858 INFO timeout: 30
2025-08-05 11:12:04,886 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 11:12:04,886 INFO params: None
2025-08-05 11:12:04,886 INFO User: <EMAIL>
2025-08-05 11:12:04,886 INFO payload: None
2025-08-05 11:12:04,886 INFO files: None
2025-08-05 11:12:04,886 INFO timeout: 30
2025-08-05 11:12:06,058 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 11:12:06,059 INFO params: None
2025-08-05 11:12:06,059 INFO User: <EMAIL>
2025-08-05 11:12:06,059 INFO payload: None
2025-08-05 11:12:06,059 INFO files: None
2025-08-05 11:12:06,059 INFO timeout: 30
2025-08-05 11:12:07,106 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 11:12:07,107 INFO params: None
2025-08-05 11:12:07,107 INFO User: <EMAIL>
2025-08-05 11:12:07,107 INFO payload: None
2025-08-05 11:12:07,107 INFO files: None
2025-08-05 11:12:07,107 INFO timeout: 30
2025-08-05 11:12:08,330 INFO Checking IPAM Health
2025-08-05 11:12:08,330 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 11:12:08,330 INFO params: None
2025-08-05 11:12:08,330 INFO User: 1-click-nutanix
2025-08-05 11:12:08,331 INFO payload: None
2025-08-05 11:12:08,331 INFO files: None
2025-08-05 11:12:08,331 INFO timeout: 30
2025-08-05 11:12:09,563 INFO Getting host list from RETSEELM-NXC000.
2025-08-05 11:12:09,564 INFO Got the host list from RETSEELM-NXC000.
2025-08-05 11:12:09,577 INFO Node records on IPAM and NTX match.
2025-08-05 11:12:09,592 INFO Checking IPAM integration...
2025-08-05 11:12:09,608 INFO Let's check '9' IP records, DNS update status
2025-08-05 11:12:09,608 INFO Pulling Ipam record with IP *************
2025-08-05 11:12:09,608 INFO This record is already enabled.
2025-08-05 11:12:09,608 INFO Pulling Ipam record with IP *************
2025-08-05 11:12:09,608 INFO This record is already enabled.
2025-08-05 11:12:09,609 INFO Pulling Ipam record with IP ***********31
2025-08-05 11:12:09,609 INFO This record is already enabled.
2025-08-05 11:12:09,609 INFO Pulling Ipam record with IP ***********
2025-08-05 11:12:09,609 INFO This record is already enabled.
2025-08-05 11:12:09,609 INFO Pulling Ipam record with IP ***********
2025-08-05 11:12:09,609 INFO This record is already enabled.
2025-08-05 11:12:09,609 INFO Pulling Ipam record with IP ***********
2025-08-05 11:12:09,609 INFO This record is already enabled.
2025-08-05 11:12:09,609 INFO Pulling Ipam record with IP ***********
2025-08-05 11:12:09,610 INFO This record is already enabled.
2025-08-05 11:12:09,610 INFO Pulling Ipam record with IP ***********
2025-08-05 11:12:09,610 INFO This record is already enabled.
2025-08-05 11:12:09,610 INFO Pulling Ipam record with IP ***********
2025-08-05 11:12:09,610 INFO This record is already enabled.
2025-08-05 11:12:09,628 INFO Records are all enabled.
2025-08-05 11:12:09,646 INFO IpamHealth: Done
2025-08-05 11:12:09,658 INFO ****************************************************************************************************
2025-08-05 11:12:09,658 INFO *                                                                                                  *
2025-08-05 11:12:09,658 INFO *                                      Configuring CvmRam...                                       *
2025-08-05 11:12:09,658 INFO *                                                                                                  *
2025-08-05 11:12:09,658 INFO ****************************************************************************************************
2025-08-05 11:12:12,087 INFO Desired State 'cvm_ram' has been disabled inside this site profile.
2025-08-05 11:12:12,087 INFO Checking CVM RAM Desired state...
2025-08-05 11:12:12,088 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/genesis, method: POST, headers: None
2025-08-05 11:12:12,088 INFO params: None
2025-08-05 11:12:12,088 INFO User: 1-click-nutanix
2025-08-05 11:12:12,088 INFO payload: {'value': '{".oid": "ClusterManager", ".method": "get_cluster_cvm_params_map", ".kwargs": {}}'}
2025-08-05 11:12:12,088 INFO files: None
2025-08-05 11:12:12,088 INFO timeout: 30
2025-08-05 11:12:13,462 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 11:12:13,462 INFO params: None
2025-08-05 11:12:13,462 INFO User: <EMAIL>
2025-08-05 11:12:13,462 INFO payload: None
2025-08-05 11:12:13,462 INFO files: None
2025-08-05 11:12:13,462 INFO timeout: 30
2025-08-05 11:12:14,483 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 11:12:14,483 INFO params: None
2025-08-05 11:12:14,483 INFO User: <EMAIL>
2025-08-05 11:12:14,483 INFO payload: None
2025-08-05 11:12:14,483 INFO files: None
2025-08-05 11:12:14,483 INFO timeout: 30
2025-08-05 11:12:15,633 INFO CVM RAM Desired state already met.
2025-08-05 11:12:15,644 INFO CvmRam: Done
2025-08-05 11:12:15,655 INFO ****************************************************************************************************
2025-08-05 11:12:15,656 INFO *                                                                                                  *
2025-08-05 11:12:15,656 INFO *                                      Configuring VmRbac...                                       *
2025-08-05 11:12:15,656 INFO *                                                                                                  *
2025-08-05 11:12:15,656 INFO ****************************************************************************************************
2025-08-05 11:12:17,932 INFO Desired State 'role_vm_rbac' has been disabled inside this site profile.
2025-08-05 11:12:17,944 INFO Renewing access control policies now.
2025-08-05 11:12:17,944 INFO Renewing all the linux,windows,network vms.
2025-08-05 11:12:17,946 INFO Getting access control policy list from SSP-DHD2-NTX.ikead2.com.
2025-08-05 11:12:17,946 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/api/nutanix/v3/user_groups/list, method: POST, headers: None
2025-08-05 11:12:17,946 INFO params: None
2025-08-05 11:12:17,946 INFO User: 1-click-nutanix
2025-08-05 11:12:17,946 INFO payload: {'kind': 'user_group', 'length': 500, 'offset': 0, 'filter': ''}
2025-08-05 11:12:17,946 INFO files: None
2025-08-05 11:12:17,946 INFO timeout: None
2025-08-05 11:12:19,901 INFO We got 1 user groups.
2025-08-05 11:12:19,902 INFO Getting vm list from SSP-DHD2-NTX.ikead2.com.
2025-08-05 11:12:19,902 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/api/nutanix/v3/vms/list, method: POST, headers: None
2025-08-05 11:12:19,902 INFO params: None
2025-08-05 11:12:19,902 INFO User: 1-click-nutanix
2025-08-05 11:12:19,902 INFO payload: {'kind': 'vm', 'length': 500, 'offset': 0, 'filter': ''}
2025-08-05 11:12:19,902 INFO files: None
2025-08-05 11:12:19,902 INFO timeout: None
2025-08-05 11:12:21,607 INFO We got 24 vms.
2025-08-05 11:12:21,608 INFO Getting role list from SSP-DHD2-NTX.ikead2.com. 
2025-08-05 11:12:21,608 INFO Payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Linux-Operator'}
2025-08-05 11:12:21,608 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/api/nutanix/v3/roles/list, method: POST, headers: None
2025-08-05 11:12:21,608 INFO params: None
2025-08-05 11:12:21,608 INFO User: 1-click-nutanix
2025-08-05 11:12:21,608 INFO payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Linux-Operator'}
2025-08-05 11:12:21,608 INFO files: None
2025-08-05 11:12:21,608 INFO timeout: None
2025-08-05 11:12:22,889 INFO Got 0 role(s).
2025-08-05 11:12:22,889 ERROR Can't get the role list or role list is empty, we will skip this ACP.
2025-08-05 11:12:22,889 INFO Getting role list from SSP-DHD2-NTX.ikead2.com. 
2025-08-05 11:12:22,889 INFO Payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Windows-Operator'}
2025-08-05 11:12:22,889 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/api/nutanix/v3/roles/list, method: POST, headers: None
2025-08-05 11:12:22,889 INFO params: None
2025-08-05 11:12:22,889 INFO User: 1-click-nutanix
2025-08-05 11:12:22,889 INFO payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Windows-Operator'}
2025-08-05 11:12:22,889 INFO files: None
2025-08-05 11:12:22,889 INFO timeout: None
2025-08-05 11:12:24,155 INFO Got 0 role(s).
2025-08-05 11:12:24,155 ERROR Can't get the role list or role list is empty, we will skip this ACP.
2025-08-05 11:12:24,155 INFO Getting role list from SSP-DHD2-NTX.ikead2.com. 
2025-08-05 11:12:24,155 INFO Payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Networking-Operator'}
2025-08-05 11:12:24,155 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/api/nutanix/v3/roles/list, method: POST, headers: None
2025-08-05 11:12:24,155 INFO params: None
2025-08-05 11:12:24,155 INFO User: 1-click-nutanix
2025-08-05 11:12:24,156 INFO payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Networking-Operator'}
2025-08-05 11:12:24,156 INFO files: None
2025-08-05 11:12:24,156 INFO timeout: None
2025-08-05 11:12:25,423 INFO Got 0 role(s).
2025-08-05 11:12:25,424 ERROR Can't get the role list or role list is empty, we will skip this ACP.
2025-08-05 11:12:25,490 INFO VmRbac: Done
2025-08-05 11:12:25,505 INFO ****************************************************************************************************
2025-08-05 11:12:25,506 INFO *                                                                                                  *
2025-08-05 11:12:25,506 INFO *                                   Configuring StorageConfig...                                   *
2025-08-05 11:12:25,506 INFO *                                                                                                  *
2025-08-05 11:12:25,506 INFO ****************************************************************************************************
2025-08-05 11:12:27,767 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/storage_containers, method: GET, headers: None
2025-08-05 11:12:27,767 INFO params: None
2025-08-05 11:12:27,767 INFO User: 1-click-nutanix
2025-08-05 11:12:27,767 INFO payload: None
2025-08-05 11:12:27,767 INFO files: None
2025-08-05 11:12:27,767 INFO timeout: 30
2025-08-05 11:12:29,040 INFO Checking vdisks...
2025-08-05 11:12:29,053 INFO State of the default container is already as desired.
2025-08-05 11:12:29,065 INFO Configuring storage containers...
2025-08-05 11:12:29,076 INFO StorageConfig: Done
2025-08-05 11:12:29,091 INFO ****************************************************************************************************
2025-08-05 11:12:29,091 INFO *                                                                                                  *
2025-08-05 11:12:29,091 INFO *                                       Configuring Smtp...                                        *
2025-08-05 11:12:29,091 INFO *                                                                                                  *
2025-08-05 11:12:29,091 INFO ****************************************************************************************************
2025-08-05 11:12:31,952 INFO Desired State 'smtp_config' has been disabled inside this site profile.
2025-08-05 11:12:31,953 INFO Configuring SMTP Settings
2025-08-05 11:12:31,953 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/smtp, method: PUT, headers: None
2025-08-05 11:12:31,953 INFO params: None
2025-08-05 11:12:31,953 INFO User: 1-click-nutanix
2025-08-05 11:12:31,953 INFO payload: {'address': 'smtp-gw.ikea.com', 'port': 25, 'username': None, 'password': None, 'secureMode': 'NONE', 'fromEmailAddress': '<EMAIL>', 'emailStatus': {'status': 'UNKNOWN', 'message': None}}
2025-08-05 11:12:31,953 INFO files: None
2025-08-05 11:12:31,953 INFO timeout: 30
2025-08-05 11:12:33,452 INFO Configuring SMTP Alert Settings
2025-08-05 11:12:33,452 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/alerts/configuration, method: PUT, headers: None
2025-08-05 11:12:33,452 INFO params: None
2025-08-05 11:12:33,452 INFO User: 1-click-nutanix
2025-08-05 11:12:33,452 INFO payload: {'emailContactList': ['<EMAIL>'], 'enable': False, 'enableDefaultNutanixEmail': True, 'skipEmptyAlertEmailDigest': True, 'defaultNutanixEmail': '<EMAIL>', 'smtpserver': {'address': 'smtp-gw.ikea.com', 'port': 25, 'username': None, 'password': None, 'secureMode': 'NONE', 'fromEmailAddress': '<EMAIL>', 'emailStatus': {'status': 'UNKNOWN', 'message': None}}, 'tunnelDetails': {'httpProxy': None, 'serviceCenter': None, 'connectionStatus': {'lastCheckedTimeStampUsecs': 0, 'status': 'UNKNOWN', 'message': None}, 'transportStatus': {'status': 'UNKNOWN', 'message': None}}, 'emailConfigRules': None, 'emailTemplate': {'subjectPrefix': None, 'bodySuffix': None}}
2025-08-05 11:12:33,452 INFO files: None
2025-08-05 11:12:33,452 INFO timeout: 30
2025-08-05 11:12:34,844 INFO PC specific process not implemented yet.
2025-08-05 11:12:34,854 INFO Smtp: Done
2025-08-05 11:12:34,865 INFO ****************************************************************************************************
2025-08-05 11:12:34,865 INFO *                                                                                                  *
2025-08-05 11:12:34,866 INFO *                                      Configuring DnsNtp...                                       *
2025-08-05 11:12:34,866 INFO *                                                                                                  *
2025-08-05 11:12:34,866 INFO ****************************************************************************************************
2025-08-05 11:12:37,166 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 11:12:37,166 INFO params: None
2025-08-05 11:12:37,166 INFO User: <EMAIL>
2025-08-05 11:12:37,166 INFO payload: None
2025-08-05 11:12:37,166 INFO files: None
2025-08-05 11:12:37,166 INFO timeout: 30
2025-08-05 11:12:38,146 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=subnet_id%3D%2791747%27, method: GET, headers: None
2025-08-05 11:12:38,146 INFO params: None
2025-08-05 11:12:38,146 INFO User: <EMAIL>
2025-08-05 11:12:38,147 INFO payload: None
2025-08-05 11:12:38,147 INFO files: None
2025-08-05 11:12:38,147 INFO timeout: 30
2025-08-05 11:12:39,121 INFO AHV / CVM Subnet: '91747' finding the rest.
2025-08-05 11:12:39,121 INFO Desired State 'dns_ntp_config' has been disabled inside this site profile.
2025-08-05 11:12:39,141 INFO Configuring NTP...
2025-08-05 11:12:39,141 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/cluster/ntp_servers, method: GET, headers: None
2025-08-05 11:12:39,141 INFO params: None
2025-08-05 11:12:39,141 INFO User: 1-click-nutanix
2025-08-05 11:12:39,141 INFO payload: None
2025-08-05 11:12:39,141 INFO files: None
2025-08-05 11:12:39,142 INFO timeout: 30
2025-08-05 11:12:40,501 INFO Target NTP servers: ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com', 'ntp1-na.ikea.com', 'ntp1-ap.ikea.com', 'ntp1-cn.ikea.com']
2025-08-05 11:12:40,501 INFO Current NTP servers: ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com', 'ntp1-na.ikea.com', 'ntp1-ap.ikea.com', 'ntp1-cn.ikea.com']
2025-08-05 11:12:40,512 INFO NTP is already as desired.
2025-08-05 11:12:40,522 INFO Configuring DNS...
2025-08-05 11:12:40,522 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/cluster/name_servers, method: GET, headers: None
2025-08-05 11:12:40,522 INFO params: None
2025-08-05 11:12:40,522 INFO User: 1-click-nutanix
2025-08-05 11:12:40,522 INFO payload: None
2025-08-05 11:12:40,523 INFO files: None
2025-08-05 11:12:40,523 INFO timeout: 30
2025-08-05 11:12:41,825 INFO Target DNS servers: ['10.59.253.2', '10.59.67.9']
2025-08-05 11:12:41,825 INFO Current DNS servers: ['10.59.253.2', '10.59.67.9']
2025-08-05 11:12:41,881 INFO DNS is already as desired.
2025-08-05 11:12:41,898 INFO Repeating for Central PE
2025-08-05 11:12:41,910 INFO Configuring NTP...
2025-08-05 11:12:41,910 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/cluster/ntp_servers, method: GET, headers: None
2025-08-05 11:12:41,910 INFO params: None
2025-08-05 11:12:41,910 INFO User: 1-click-nutanix
2025-08-05 11:12:41,910 INFO payload: None
2025-08-05 11:12:41,911 INFO files: None
2025-08-05 11:12:41,911 INFO timeout: 30
2025-08-05 11:12:43,262 INFO Target NTP servers: ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com', 'ntp1-na.ikea.com', 'ntp1-ap.ikea.com', 'ntp1-cn.ikea.com']
2025-08-05 11:12:43,262 INFO Current NTP servers: ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com', 'ntp1-na.ikea.com', 'ntp1-ap.ikea.com', 'ntp1-cn.ikea.com']
2025-08-05 11:12:43,280 INFO NTP is already as desired.
2025-08-05 11:12:43,301 INFO Configuring DNS...
2025-08-05 11:12:43,301 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/cluster/name_servers, method: GET, headers: None
2025-08-05 11:12:43,301 INFO params: None
2025-08-05 11:12:43,302 INFO User: 1-click-nutanix
2025-08-05 11:12:43,302 INFO payload: None
2025-08-05 11:12:43,302 INFO files: None
2025-08-05 11:12:43,302 INFO timeout: 30
2025-08-05 11:12:44,589 INFO Target DNS servers: ['10.59.253.2', '10.59.67.9']
2025-08-05 11:12:44,589 INFO Current DNS servers: ['10.59.253.2', '10.59.67.9']
2025-08-05 11:12:44,600 INFO DNS is already as desired.
2025-08-05 11:12:44,612 INFO DnsNtp: Done
2025-08-05 11:12:44,626 INFO ****************************************************************************************************
2025-08-05 11:12:44,626 INFO *                                                                                                  *
2025-08-05 11:12:44,627 INFO *                                        Configuring Ha...                                         *
2025-08-05 11:12:44,627 INFO *                                                                                                  *
2025-08-05 11:12:44,627 INFO ****************************************************************************************************
2025-08-05 11:12:46,910 INFO Desired State 'ha_reservation' has been disabled inside this site profile.
2025-08-05 11:12:46,911 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 11:12:46,911 INFO params: None
2025-08-05 11:12:46,911 INFO User: 1-click-nutanix
2025-08-05 11:12:46,911 INFO payload: None
2025-08-05 11:12:46,911 INFO files: None
2025-08-05 11:12:46,911 INFO timeout: 30
2025-08-05 11:12:48,235 INFO Getting host list from RETSEELM-NXC000.
2025-08-05 11:12:48,235 INFO Got the host list from RETSEELM-NXC000.
2025-08-05 11:12:48,236 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/ha, method: GET, headers: None
2025-08-05 11:12:48,236 INFO params: None
2025-08-05 11:12:48,236 INFO User: 1-click-nutanix
2025-08-05 11:12:48,236 INFO payload: None
2025-08-05 11:12:48,236 INFO files: None
2025-08-05 11:12:48,236 INFO timeout: 30
2025-08-05 11:12:49,468 INFO HA Reservation is already in desired state.
2025-08-05 11:12:49,482 INFO Ha: Done
2025-08-05 11:12:49,493 INFO ****************************************************************************************************
2025-08-05 11:12:49,494 INFO *                                                                                                  *
2025-08-05 11:12:49,494 INFO *                                    Configuring AuthConfig...                                     *
2025-08-05 11:12:49,494 INFO *                                                                                                  *
2025-08-05 11:12:49,494 INFO ****************************************************************************************************
2025-08-05 11:12:52,590 INFO Configuring LDAP...
2025-08-05 11:12:52,590 INFO Desired auth config name: ikead2
2025-08-05 11:12:52,590 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 11:12:52,590 INFO params: None
2025-08-05 11:12:52,591 INFO User: 1-click-nutanix
2025-08-05 11:12:52,591 INFO payload: None
2025-08-05 11:12:52,591 INFO files: None
2025-08-05 11:12:52,591 INFO timeout: 30
2025-08-05 11:12:53,864 INFO PE is already Joined towards 'ikead2.com'.
2025-08-05 11:12:53,880 INFO Username is already set towards '<EMAIL>'
2025-08-05 11:12:53,893 INFO Configuring the Role Mappings for 'ikead2'
2025-08-05 11:12:53,893 INFO Getting Groups that need to be bound.
2025-08-05 11:12:53,893 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 11:12:53,893 INFO params: None
2025-08-05 11:12:53,893 INFO User: 1-click-nutanix
2025-08-05 11:12:53,893 INFO payload: None
2025-08-05 11:12:53,893 INFO files: None
2025-08-05 11:12:53,893 INFO timeout: 30
2025-08-05 11:12:55,090 INFO We are setting up RoleMappings using group: '['NXAdmin']'
2025-08-05 11:12:55,091 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/authconfig/directories/ikead2/role_mappings, method: GET, headers: None
2025-08-05 11:12:55,091 INFO params: None
2025-08-05 11:12:55,091 INFO User: 1-click-nutanix
2025-08-05 11:12:55,091 INFO payload: None
2025-08-05 11:12:55,091 INFO files: None
2025-08-05 11:12:55,091 INFO timeout: 30
2025-08-05 11:12:56,299 INFO Role ROLE_USER_ADMIN with entity type GROUP is already in desired state.
2025-08-05 11:12:56,300 INFO Role ROLE_CLUSTER_ADMIN with entity type GROUP is already in desired state.
2025-08-05 11:12:56,300 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 11:12:56,300 INFO params: None
2025-08-05 11:12:56,300 INFO User: 1-click-nutanix
2025-08-05 11:12:56,300 INFO payload: None
2025-08-05 11:12:56,300 INFO files: None
2025-08-05 11:12:56,300 INFO timeout: 30
2025-08-05 11:12:57,519 INFO Repeating for Central PE
2025-08-05 11:12:57,529 INFO Configuring LDAP...
2025-08-05 11:12:57,529 INFO Desired auth config name: ikead2
2025-08-05 11:12:57,529 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 11:12:57,529 INFO params: None
2025-08-05 11:12:57,530 INFO User: 1-click-nutanix
2025-08-05 11:12:57,530 INFO payload: None
2025-08-05 11:12:57,530 INFO files: None
2025-08-05 11:12:57,530 INFO timeout: 30
2025-08-05 11:12:58,732 INFO PC is already Joined towards 'ikead2.com'.
2025-08-05 11:12:58,743 INFO Username is already set towards '<EMAIL>'
2025-08-05 11:12:58,755 INFO Configuring the Role Mappings for 'ikead2'
2025-08-05 11:12:58,755 INFO Getting Groups that need to be bound.
2025-08-05 11:12:58,755 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 11:12:58,755 INFO params: None
2025-08-05 11:12:58,755 INFO User: 1-click-nutanix
2025-08-05 11:12:58,756 INFO payload: None
2025-08-05 11:12:58,756 INFO files: None
2025-08-05 11:12:58,756 INFO timeout: 30
2025-08-05 11:12:59,974 INFO We are setting up RoleMappings using group: '['NXAdmin']'
2025-08-05 11:12:59,975 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/PrismGateway/services/rest/v1/authconfig/directories/IKEAD2/role_mappings, method: GET, headers: None
2025-08-05 11:12:59,975 INFO params: None
2025-08-05 11:12:59,975 INFO User: 1-click-nutanix
2025-08-05 11:12:59,975 INFO payload: None
2025-08-05 11:12:59,975 INFO files: None
2025-08-05 11:12:59,975 INFO timeout: 30
2025-08-05 11:13:01,312 INFO Role ROLE_USER_ADMIN with entity type GROUP doesn't exist, creating...
2025-08-05 11:13:01,312 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/PrismGateway/services/rest/v1/authconfig/directories/IKEAD2/role_mappings?&entityType=GROUP&role=ROLE_USER_ADMIN, method: POST, headers: None
2025-08-05 11:13:01,312 INFO params: None
2025-08-05 11:13:01,312 INFO User: 1-click-nutanix
2025-08-05 11:13:01,312 INFO payload: {'directoryName': 'IKEAD2', 'role': 'ROLE_USER_ADMIN', 'entityType': 'GROUP', 'entityValues': ['NXAdmin']}
2025-08-05 11:13:01,313 INFO files: None
2025-08-05 11:13:01,313 INFO timeout: 30
2025-08-05 11:13:02,509 WARNING Response content: b'{"message":"Access is denied"}'
2025-08-05 11:13:02,509 WARNING API response is not ok, going to do the 2 retry...
2025-08-05 11:13:03,690 WARNING Response content: b'{"message":"Access is denied"}'
2025-08-05 11:13:03,691 WARNING API response is not ok, going to do the 3 retry...
2025-08-05 11:13:04,828 WARNING Response content: b'{"message":"Access is denied"}'
2025-08-05 11:13:04,829 WARNING API response is not ok, going to do the 4 retry...
2025-08-05 11:13:06,003 WARNING Response content: b'{"message":"Access is denied"}'
2025-08-05 11:13:06,004 WARNING API response is not ok, going to do the 5 retry...
2025-08-05 11:13:07,179 WARNING Response content: b'{"message":"Access is denied"}'
2025-08-05 11:13:07,248 ERROR ['Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 91, in task_process\n    c(self.pe, self.logger, self.db_logger, self.facility_type).configure()\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 1019, in configure\n    self.configure_role_mapping(px_mode="PC")\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 1191, in configure_role_mapping\n    self.rest_ac.add_role_mapping(ac_name, desired_entity_values, desired_role)\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\pe_components.py", line 311, in add_role_mapping\n    raise flaskex.BadGateway(f"Failed to add role mappings! Reason: {data}")\n', 'werkzeug.exceptions.BadGateway: 502 Bad Gateway: Failed to add role mappings! Reason: 502 Bad Gateway: Out of retry times when calling https://SSP-DHD2-NTX.ikead2.com:9440/PrismGateway/services/rest/v1/authconfig/directories/IKEAD2/role_mappings?&entityType=GROUP&role=ROLE_USER_ADMIN. Response: b\'{"message":"Access is denied"}\'\n']
2025-08-05 11:13:07,264 WARNING Failed on current step, but will continue the other steps...
2025-08-05 11:13:07,304 INFO ****************************************************************************************************
2025-08-05 11:13:07,304 INFO *                                                                                                  *
2025-08-05 11:13:07,304 INFO *                                    Configuring AhvHostname...                                    *
2025-08-05 11:13:07,304 INFO *                                                                                                  *
2025-08-05 11:13:07,304 INFO ****************************************************************************************************
2025-08-05 11:13:09,755 INFO Configure AHV Hostnames
2025-08-05 11:13:09,755 INFO Desired State 'ahv_host_names' has been disabled inside this site profile.
2025-08-05 11:13:09,756 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 11:13:09,756 INFO params: None
2025-08-05 11:13:09,756 INFO User: 1-click-nutanix
2025-08-05 11:13:09,756 INFO payload: None
2025-08-05 11:13:09,756 INFO files: None
2025-08-05 11:13:09,756 INFO timeout: 30
2025-08-05 11:13:11,024 INFO Getting host list from RETSEELM-NXC000.
2025-08-05 11:13:11,024 INFO Got the host list from RETSEELM-NXC000.
2025-08-05 11:13:11,025 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 11:13:11,025 INFO params: None
2025-08-05 11:13:11,025 INFO User: <EMAIL>
2025-08-05 11:13:11,025 INFO payload: None
2025-08-05 11:13:11,025 INFO files: None
2025-08-05 11:13:11,025 INFO timeout: 30
2025-08-05 11:13:11,994 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 11:13:11,995 INFO params: None
2025-08-05 11:13:11,995 INFO User: <EMAIL>
2025-08-05 11:13:11,995 INFO payload: None
2025-08-05 11:13:11,995 INFO files: None
2025-08-05 11:13:11,995 INFO timeout: 30
2025-08-05 11:13:13,087 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 11:13:13,087 INFO params: None
2025-08-05 11:13:13,087 INFO User: <EMAIL>
2025-08-05 11:13:13,087 INFO payload: None
2025-08-05 11:13:13,087 INFO files: None
2025-08-05 11:13:13,087 INFO timeout: 30
2025-08-05 11:13:14,068 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 11:13:14,068 INFO params: None
2025-08-05 11:13:14,068 INFO User: <EMAIL>
2025-08-05 11:13:14,068 INFO payload: None
2025-08-05 11:13:14,068 INFO files: None
2025-08-05 11:13:14,068 INFO timeout: 30
2025-08-05 11:13:15,149 INFO Current node is good, skipping...
2025-08-05 11:13:15,149 INFO Current node is good, skipping...
2025-08-05 11:13:15,149 INFO Current node is good, skipping...
2025-08-05 11:13:15,160 INFO AhvHostname: Done
2025-08-05 11:13:15,178 INFO ****************************************************************************************************
2025-08-05 11:13:15,178 INFO *                                                                                                  *
2025-08-05 11:13:15,179 INFO *                                    Configuring CvmHostname...                                    *
2025-08-05 11:13:15,179 INFO *                                                                                                  *
2025-08-05 11:13:15,179 INFO ****************************************************************************************************
2025-08-05 11:13:18,248 INFO Configure CVM Hostnames
2025-08-05 11:13:18,248 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 11:13:18,248 INFO params: None
2025-08-05 11:13:18,248 INFO User: 1-click-nutanix
2025-08-05 11:13:18,249 INFO payload: None
2025-08-05 11:13:18,249 INFO files: None
2025-08-05 11:13:18,249 INFO timeout: 30
2025-08-05 11:13:19,488 INFO Getting host list from RETSEELM-NXC000.
2025-08-05 11:13:19,488 INFO Got the host list from RETSEELM-NXC000.
2025-08-05 11:13:19,489 INFO Desired State 'cvm_host_names' has been disabled inside this site profile.
2025-08-05 11:13:19,489 INFO Getting VM list from RETSEELM-NXC000.
2025-08-05 11:13:19,490 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-05 11:13:19,490 INFO params: None
2025-08-05 11:13:19,490 INFO User: 1-click-nutanix
2025-08-05 11:13:19,490 INFO payload: None
2025-08-05 11:13:19,490 INFO files: None
2025-08-05 11:13:19,490 INFO timeout: 30
2025-08-05 11:13:20,778 INFO Got the VM list from RETSEELM-NXC000.
2025-08-05 11:13:20,779 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 11:13:20,779 INFO params: None
2025-08-05 11:13:20,779 INFO User: <EMAIL>
2025-08-05 11:13:20,779 INFO payload: None
2025-08-05 11:13:20,779 INFO files: None
2025-08-05 11:13:20,779 INFO timeout: 30
2025-08-05 11:13:21,759 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 11:13:21,759 INFO params: None
2025-08-05 11:13:21,760 INFO User: <EMAIL>
2025-08-05 11:13:21,760 INFO payload: None
2025-08-05 11:13:21,760 INFO files: None
2025-08-05 11:13:21,760 INFO timeout: 30
2025-08-05 11:13:22,882 INFO Working with 'RETSEELM-NX7001CVM'
2025-08-05 11:13:22,882 INFO The name to set to NTX cvm: 'NTNX-RETSEELM-NX7001-CVM'
2025-08-05 11:13:22,882 INFO Current node name on NTX is equal to IPAM, skipping...
2025-08-05 11:13:22,883 INFO Working with 'RETSEELM-NX7002CVM'
2025-08-05 11:13:22,883 INFO The name to set to NTX cvm: 'NTNX-RETSEELM-NX7002-CVM'
2025-08-05 11:13:22,883 INFO Current node name on NTX is equal to IPAM, skipping...
2025-08-05 11:13:22,883 INFO Working with 'RETSEELM-NX7003CVM'
2025-08-05 11:13:22,883 INFO The name to set to NTX cvm: 'NTNX-RETSEELM-NX7003-CVM'
2025-08-05 11:13:22,883 INFO Current node name on NTX is equal to IPAM, skipping...
2025-08-05 11:13:22,897 INFO CvmHostname: Done
2025-08-05 11:13:22,921 INFO ****************************************************************************************************
2025-08-05 11:13:22,922 INFO *                                                                                                  *
2025-08-05 11:13:22,922 INFO *                                        Configuring Oob...                                        *
2025-08-05 11:13:22,922 INFO *                                                                                                  *
2025-08-05 11:13:22,922 INFO ****************************************************************************************************
2025-08-05 11:13:29,409 INFO oneview_scope is DPC_EU_D2, myscope is /rest/scopes/dc4d1e10-ba24-44e9-97cc-3f45c31f7ff1
2025-08-05 11:13:42,730 INFO Desired State 'oob_health' has been disabled inside this site profile.
2025-08-05 11:13:42,730 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 11:13:42,730 INFO params: None
2025-08-05 11:13:42,730 INFO User: 1-click-nutanix
2025-08-05 11:13:42,731 INFO payload: None
2025-08-05 11:13:42,731 INFO files: None
2025-08-05 11:13:42,731 INFO timeout: 30
2025-08-05 11:13:44,001 INFO Getting host list from RETSEELM-NXC000.
2025-08-05 11:13:44,001 INFO Got the host list from RETSEELM-NXC000.
2025-08-05 11:13:44,002 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NX7001oob.ikead2.com', method: GET, headers: None
2025-08-05 11:13:44,002 INFO params: None
2025-08-05 11:13:44,002 INFO User: <EMAIL>
2025-08-05 11:13:44,002 INFO payload: None
2025-08-05 11:13:44,002 INFO files: None
2025-08-05 11:13:44,002 INFO timeout: 30
2025-08-05 11:13:45,000 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=subnet_id%3D%2791749%27, method: GET, headers: None
2025-08-05 11:13:45,001 INFO params: None
2025-08-05 11:13:45,001 INFO User: <EMAIL>
2025-08-05 11:13:45,001 INFO payload: None
2025-08-05 11:13:45,001 INFO files: None
2025-08-05 11:13:45,001 INFO timeout: 30
2025-08-05 11:13:46,665 INFO ****************************************************************************************************
2025-08-05 11:13:46,666 INFO *                                                                                                  *
2025-08-05 11:13:46,666 INFO *                                 Checking OOB for RETSEELM-NX7001                                 *
2025-08-05 11:13:46,666 INFO *                                                                                                  *
2025-08-05 11:13:46,666 INFO ****************************************************************************************************
2025-08-05 11:13:46,667 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NX7001oob.ikead2.com', method: GET, headers: None
2025-08-05 11:13:46,667 INFO params: None
2025-08-05 11:13:46,667 INFO User: <EMAIL>
2025-08-05 11:13:46,667 INFO payload: None
2025-08-05 11:13:46,667 INFO files: None
2025-08-05 11:13:46,667 INFO timeout: 30
2025-08-05 11:13:47,621 INFO SSH connecting to ***********, this is the '1' try.
2025-08-05 11:13:53,756 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 11:13:53,757 INFO SSH connecting to ***********, this is the '2' try.
2025-08-05 11:13:59,961 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 11:13:59,961 INFO SSH connecting to ***********, this is the '3' try.
2025-08-05 11:14:04,343 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 11:14:04,343 INFO SSH connecting to ***********, this is the '4' try.
2025-08-05 11:14:10,551 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 11:14:10,551 INFO SSH connecting to ***********, this is the '5' try.
2025-08-05 11:14:16,746 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 11:14:16,748 ERROR We've retried for 5 times, still not connected, aborting.
2025-08-05 11:14:16,748 ERROR Failed to connect to ***********
2025-08-05 11:14:16,748 INFO OOB for RETSEELM-NX7001 is reachable.
2025-08-05 11:14:16,748 INFO Checking iLO status...
2025-08-05 11:14:18,039 INFO ILO status is good.
2025-08-05 11:14:18,040 INFO Checking ILO name and DNS name for RETSEELM-NX7001
2025-08-05 11:14:18,040 INFO Setting server name and DNS name for RETSEELM-NX7001OOB
2025-08-05 11:14:18,040 INFO Retrieving RedFish Network Stack '*************'
2025-08-05 11:14:18,040 INFO Calling restapi, URL: https://*************/redfish/v1/SessionService/Sessions, method: POST, headers: {'Content-Type': 'application/json;charset=UTF-8'}
2025-08-05 11:14:18,040 INFO params: None
2025-08-05 11:14:18,040 INFO User: administrator
2025-08-05 11:14:18,040 INFO payload: {'UserName': 'administrator', 'Password': '*****'}
2025-08-05 11:14:18,040 INFO files: None
2025-08-05 11:14:18,040 INFO timeout: 5
2025-08-05 11:14:20,373 INFO Calling restapi, URL: https://*************/redfish/v1/Systems/1/, method: GET, headers: {'X-Auth-Token': '1f0c3d6a954293e9ad14008a1e8a258b'}
2025-08-05 11:14:20,374 INFO params: None
2025-08-05 11:14:20,374 INFO User: administrator
2025-08-05 11:14:20,374 INFO payload: None
2025-08-05 11:14:20,374 INFO files: None
2025-08-05 11:14:20,374 INFO timeout: None
2025-08-05 11:14:21,692 INFO Get ILO HostName successfull.
2025-08-05 11:14:21,694 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/EthernetInterfaces/1/, method: GET, headers: None
2025-08-05 11:14:21,695 INFO params: None
2025-08-05 11:14:21,695 INFO User: administrator
2025-08-05 11:14:21,695 INFO payload: None
2025-08-05 11:14:21,696 INFO files: None
2025-08-05 11:14:21,696 INFO timeout: None
2025-08-05 11:14:22,833 INFO Got the response with OK
2025-08-05 11:14:22,833 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 11:14:22,833 INFO params: None
2025-08-05 11:14:22,833 INFO User: administrator
2025-08-05 11:14:22,833 INFO payload: None
2025-08-05 11:14:22,833 INFO files: None
2025-08-05 11:14:22,833 INFO timeout: None
2025-08-05 11:14:24,059 INFO Got the response with OK
2025-08-05 11:14:24,061 INFO Group Already exists.
2025-08-05 11:14:24,603 INFO LDAP Binding already in desired state.
2025-08-05 11:14:24,604 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 11:14:24,604 INFO params: None
2025-08-05 11:14:24,604 INFO User: administrator
2025-08-05 11:14:24,605 INFO payload: None
2025-08-05 11:14:24,605 INFO files: None
2025-08-05 11:14:24,606 INFO timeout: None
2025-08-05 11:14:25,882 INFO Got the response with OK
2025-08-05 11:14:25,884 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Roles/dirgroup3a7c6167d73b780ee5fe83de, method: GET, headers: None
2025-08-05 11:14:25,884 INFO params: None
2025-08-05 11:14:25,884 INFO User: administrator
2025-08-05 11:14:25,884 INFO payload: None
2025-08-05 11:14:25,885 INFO files: None
2025-08-05 11:14:25,885 INFO timeout: None
2025-08-05 11:14:27,848 INFO Got the response with OK
2025-08-05 11:14:27,850 INFO Privileges are already correct.
2025-08-05 11:14:27,850 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/EthernetInterfaces/1, method: GET, headers: None
2025-08-05 11:14:27,850 INFO params: None
2025-08-05 11:14:27,850 INFO User: administrator
2025-08-05 11:14:27,850 INFO payload: None
2025-08-05 11:14:27,850 INFO files: None
2025-08-05 11:14:27,850 INFO timeout: None
2025-08-05 11:14:29,138 INFO Got the response with OK
2025-08-05 11:14:29,139 INFO DNS is already correct.
2025-08-05 11:14:29,139 INFO Checking NTP configuration...
2025-08-05 11:14:29,139 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/DateTime, method: GET, headers: None
2025-08-05 11:14:29,139 INFO params: None
2025-08-05 11:14:29,139 INFO User: administrator
2025-08-05 11:14:29,139 INFO payload: None
2025-08-05 11:14:29,139 INFO files: None
2025-08-05 11:14:29,139 INFO timeout: None
2025-08-05 11:14:30,583 INFO Got the response with OK
2025-08-05 11:14:30,585 INFO Current DateTime configuration: {'@odata.context': '/redfish/v1/$metadata#HpeiLODateTime.HpeiLODateTime', '@odata.etag': 'W/"1BF57873"', '@odata.id': '/redfish/v1/Managers/1/DateTime', '@odata.type': '#HpeiLODateTime.v2_0_0.HpeiLODateTime', 'Id': 'DateTime', 'ConfigurationSettings': 'Current', 'DateTime': '2025-08-05T03:14:30Z', 'Links': {'EthernetNICs': {'@odata.id': '/redfish/v1/Managers/1/EthernetInterfaces'}}, 'NTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'Name': 'iLO Date and Time Settings', 'PropagateTimeToHost': False, 'StaticNTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'TimeZone': {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, 'TimeZoneList': [{'Index': 0, 'Name': 'International Date Line West', 'UtcOffset': '-12:00', 'Value': 'GMT+12:00'}, {'Index': 1, 'Name': 'Midway Island, Samoa', 'UtcOffset': '-11:00', 'Value': 'SST+11:00'}, {'Index': 2, 'Name': 'Hawaii', 'UtcOffset': '-10:00', 'Value': 'HST+10:00'}, {'Index': 3, 'Name': 'Marquesas', 'UtcOffset': '-09:30', 'Value': 'MART+9:30'}, {'Index': 4, 'Name': 'Alaska', 'UtcOffset': '-09:00', 'Value': 'AKST+9:00AKDT+08:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 5, 'Name': 'Pacific Time(US & Canada), Tijuana, Portland', 'UtcOffset': '-08:00', 'Value': 'PST+8:00PDT+07:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 6, 'Name': 'Arizona, Chihuahua, La Paz, Mazatlan, Mountain Time (US & Canad', 'UtcOffset': '-07:00', 'Value': 'MST+7:00MDT+06:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 7, 'Name': 'Central America, Central Time(US & Canada)', 'UtcOffset': '-06:00', 'Value': 'CST+6:00CDT+05:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 8, 'Name': 'Bogota, Lima, Quito, Eastern Time(US & Canada)', 'UtcOffset': '-05:00', 'Value': 'EST+5:00EDT+04:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 9, 'Name': 'Caracas, Georgetown', 'UtcOffset': '-04:00', 'Value': 'VET+4:00'}, {'Index': 10, 'Name': 'Atlantic Time(Canada), Santiago', 'UtcOffset': '-04:00', 'Value': 'AST+4:00ADT+03:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 11, 'Name': 'Newfoundland', 'UtcOffset': '-03:30', 'Value': 'NST+3:30NDT+02:30:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 12, 'Name': 'Brasilia, Buenos Aires, Greenland', 'UtcOffset': '-03:00', 'Value': 'ART+3:00'}, {'Index': 13, 'Name': 'Mid-Atlantic', 'UtcOffset': '-02:00', 'Value': 'GST+2:00'}, {'Index': 14, 'Name': 'Azores, Cape Verde Is.', 'UtcOffset': '-01:00', 'Value': 'CVT+1:00'}, {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, {'Index': 16, 'Name': 'Dublin, London', 'UtcOffset': '+00:00', 'Value': 'WET-0WEST-1,M3.5.0/01:00:00,M10.5.0/02:00:00'}, {'Index': 17, 'Name': 'Amsterdam, Berlin, Bern, Rome, Paris, West Central Africa', 'UtcOffset': '+01:00', 'Value': 'CET-1:00CEST-02:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 18, 'Name': 'Athens, Bucharest, Cairo, Jerusalem', 'UtcOffset': '+02:00', 'Value': 'EET-2:00EEST-03:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 19, 'Name': 'Baghdad, Kuwait, Riyadh, Moscow, Istanbul, Nairobi', 'UtcOffset': '+03:00', 'Value': 'AST-3:00'}, {'Index': 20, 'Name': 'Tehran', 'UtcOffset': '+03:30', 'Value': 'IRST-3:30IRDT-04:30:00,80/00:00:00,264/00:00:00'}, {'Index': 21, 'Name': 'Abu Dhabi, Muscat, Baku, Tbilisi, Yerevan', 'UtcOffset': '+04:00', 'Value': 'GST-4:00'}, {'Index': 22, 'Name': 'Kabul', 'UtcOffset': '+04:30', 'Value': 'AFT-4:30'}, {'Index': 23, 'Name': 'Ekaterinburg, Islamabad, Karachi, Tashkent', 'UtcOffset': '+05:00', 'Value': 'YEKT-5:00'}, {'Index': 24, 'Name': 'Chennai, Kolkata, Mumbai, New Delhi', 'UtcOffset': '+05:30', 'Value': 'IST-5:30'}, {'Index': 25, 'Name': 'Kathmandu', 'UtcOffset': '+05:45', 'Value': 'NPT-5:45'}, {'Index': 26, 'Name': 'Almaty, Dhaka, Sri Jayawardenepura', 'UtcOffset': '+06:00', 'Value': 'ALMT-6:00'}, {'Index': 27, 'Name': 'Rangoon', 'UtcOffset': '+06:30', 'Value': 'MMT-6:30'}, {'Index': 28, 'Name': 'Bangkok, Hanio, Jakarta, Novosibirsk, Astana, Krasnoyarsk', 'UtcOffset': '+07:00', 'Value': 'ICT-7:00'}, {'Index': 29, 'Name': 'Beijing, Chongqing, Hong Kong, Urumqi, Taipei, Perth', 'UtcOffset': '+08:00', 'Value': 'CST-8:00'}, {'Index': 30, 'Name': 'Eucla', 'UtcOffset': '+08:45', 'Value': 'ACWST-08:45'}, {'Index': 31, 'Name': 'Osaka, Sapporo, Tokyo, Seoul, Yakutsk', 'UtcOffset': '+09:00', 'Value': 'JST-9:00'}, {'Index': 32, 'Name': 'Adelaide, Darwin', 'UtcOffset': '+09:30', 'Value': 'ACST-9:30ACDT-10:30:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 33, 'Name': 'Canberra, Melbourne, Sydney, Guam, Hobart, Vladivostok', 'UtcOffset': '+10:00', 'Value': 'AEST-10:00AEDT-11:00:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 34, 'Name': 'Lord Howe', 'UtcOffset': '+10:30', 'Value': 'LHST-10:30LHDT11:00'}, {'Index': 35, 'Name': 'Chatham', 'UtcOffset': '+10:45', 'Value': 'CHAST-10:45CHADT-11:45'}, {'Index': 36, 'Name': 'Magadan, Solomon Is., New Caledonia', 'UtcOffset': '+11:00', 'Value': 'MAGT-11:00'}, {'Index': 37, 'Name': 'Auckland, Wellington, Fiji, Kamchatka, Marshall Is.', 'UtcOffset': '+12:00', 'Value': 'NZST-12:00NZDT-13:00:00,M9.5.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 38, 'Name': "Nuku'alofa", 'UtcOffset': '+13:00', 'Value': 'TKT-13:00'}, {'Index': 39, 'Name': 'Line Islands', 'UtcOffset': '+14:00', 'Value': 'LINT-14:00'}, {'Index': 40, 'Name': 'Unspecified Time Zone', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}]}
2025-08-05 11:14:30,585 INFO NTP configuration is already in desired state
2025-08-05 11:14:30,585 INFO No configuration changes made, skipping ILO reset.
2025-08-05 11:14:30,585 INFO Oneview part
2025-08-05 11:14:30,585 INFO Check if the OOB is in oneview
2025-08-05 11:14:31,655 INFO OOB is in oneview and state is normal
2025-08-05 11:14:31,677 INFO OOB configuration is done for RETSEELM-NX7001.
2025-08-05 11:14:31,688 INFO ****************************************************************************************************
2025-08-05 11:14:31,689 INFO *                                                                                                  *
2025-08-05 11:14:31,689 INFO *                                 Checking OOB for RETSEELM-NX7002                                 *
2025-08-05 11:14:31,689 INFO *                                                                                                  *
2025-08-05 11:14:31,689 INFO ****************************************************************************************************
2025-08-05 11:14:31,689 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NX7002oob.ikead2.com', method: GET, headers: None
2025-08-05 11:14:31,689 INFO params: None
2025-08-05 11:14:31,689 INFO User: <EMAIL>
2025-08-05 11:14:31,689 INFO payload: None
2025-08-05 11:14:31,689 INFO files: None
2025-08-05 11:14:31,690 INFO timeout: 30
2025-08-05 11:14:32,706 INFO SSH connecting to ***********, this is the '1' try.

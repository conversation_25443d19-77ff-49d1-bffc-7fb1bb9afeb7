function Get-GstAccountFromPam() {
    param(
        [switch] $Display,
        [int]    $MaxTry = 3
    )
    $SafeGuardModule = "safeguard-ps"
    $PAM             = "pam.ikea.com"
    try {
        if (Get-Module -Name $SafeGuardModule) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The module '$SafeGuardModule' loaded"
        } elseif (Get-Module -Name $SafeGuardModule -ListAvailable) {
            Import-Module -Name $SafeGuardModule -Force
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The module '$SafeGuardModule' imported"
        } else {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Install the module '$SafeGuardModule'"
            Install-Module -Name $SafeGuardModule -Repository PSGallery -Scope CurrentUser -Force
        }
    } catch {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurred when importing the module '$SafeGuardModule'. Cause: $_"
    }

    do {
        $MaxTry --
        $Exception = 0
        try {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Try to log into the Safeguard appliance '$Pam'"
            $Connection = Connect-Safeguard -Appliance $PAM -Browser -Verbose
        } catch {
            $Exception = 1
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Login failed. Cause: $_"
        }
        if (!$Exception) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We got the token"
            break
        }
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Now we sleep 10s, we can close the browser, then re-try"
        Start-Sleep 10
    } until (!$MaxTry)
    if ($Exception) {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Out of max try '$MaxTry' to obtain token, quit"
        return $null
    }
    $Account         = Get-SafeguardRequestableAccount | Where-Object {$_.AccessRequestType -eq "Password" -and $_.AccountName -like "GST*"} | Select-Object -First 1
    $CheckoutRequest = Get-SafeguardMyRequest
    $RequestId       = ""
    $AccountAsset    = "IKEA"
    if ($Account.AccountName -eq $CheckoutRequest.AccountName) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "A request '$($CheckoutRequest.Id)' exists, checkout the password from it"
        $RequestId = $CheckoutRequest.Id
    } else {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Try to create a new request"
        try {
            $NewRequest = New-SafeguardAccessRequest -Appliance $PAM -Insecure -AccountToUse $Account.AccountId -AssetToUse $AccountAsset -AccessRequestType "Password" -ReasonComment "Scripted Password Checkout" -RequestedDurationHours 10
            $RequestId  = $NewRequest.Id
            Start-Sleep 10
        } catch {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to create access request. Cause: $_"
            return $null
        }
    }
    try {
        if ($PWord = Get-SafeguardAccessRequestPassword -Appliance $Pam -Insecure -RequestId $RequestId) {
            Set-Clipboard -Value $PWord
            $Result = [PSCustomObject]@{
                "Password" = "Available in your clipboard"
            }
            if ($Display) {
                $Result.Password = $PWord
            }
            return  $Result
        }
    } catch {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "Failed to create access request. Cause: $_"
        return $null
    }
}
function Get-SecretForSiaB() {
    param(
        [string] [ValidateSet("PROD", "PPE", "DT", "D2")]                                                        $Tier = "PROD",
        [string] [Parameter(ParameterSetName = 'Cluster')]                                                       $Site,
        [string] [ValidateSet("NXC000", "NXC001", "NXC002", "NXC003")] [Parameter(ParameterSetName = 'Cluster')] $NXC = "NXC000",
        [string] [Parameter(ParameterSetName = 'Prism')]                                                         $Prism,
        [string] [ValidateSet("Site_Ahv_Nutanix", "Site_Ahv_Root", "Site_Gw_Svc", "Site_Gw_Priv_Key", "Site_Gw_Pub_Key",
                              "Site_Move", "Site_Oob", "Site_Pc_Admin", "Site_Pc_Nutanix", 
                              "Site_Pc_Svc", "Site_Pe_Admin", "Site_Pe_Nutanix", 
                              "Site_Pe_Svc", "Site_Witness_Admin", "Site_Witness_Nutanix")]                      $Secret = "Site_Pe_Nutanix",
        [switch]                                                                                                 $Display
    )
    if ($Prism) {
        $Prism = $Prism.ToUpper()
        #$Cluster = $Prism.Split("-")[0]
        $Site  = $Prism.Split("-")[0]
        $NXC   = $Prism.Split("-")[1]
    }
    if (!$PSBoundParameters.ContainsKey('Tier')) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are using the default tier: $Tier"
    }
    if (!$PSBoundParameters.ContainsKey('Secret')) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are quering the default secret: $Secret"
    }
    #Load vault configuration
    $VaultVars  = $(Read-Var).Vault
    $Endpoint   = $VaultVars.Endpoint

    #Assemble the full namespace to query result from vault
    $Namespace  = $($VaultVars.Retail.MasterNamespace + "/" + ([string]$VaultVars.Retail.$Tier.NameSpace).Trim())
    $Token      = $VaultVars.Retail.Token
    $Engine     = ([string]$VaultVars.Retail.$Tier.Engine).Trim()
    $Site       = $Site.ToUpper()
    $SecretPath = $Site + "-" + $NXC + "/" + $Secret

    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Query from the vault under the namespace $($Namespace)"
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Retrieve the result from the path $($SecretPath)"
    
    $Headers    = @{
        "X-Vault-Namespace" = "$($Namespace)"
        "X-Vault-Token"     = "$($Token)"
    }
    $RequestURI = "v1/$($Engine)/data/$($SecretPath)"
    $Result     = Invoke-Vault-API -Endpoint $Endpoint `
                                   -RequestURI $RequestURI `
                                   -Headers $Headers `
                                   -Method GET
    
    if($Result){
        $KV = [pscustomobject]@{
            'Prism'    = $($Site + "-" + $NXC)
            'Secret'   = $Secret
            'Username' = (($Result.data).data).Username
        }
        Set-Clipboard -Value $((($Result.data).data).Secret)
        switch ($Display){
            $true {
                $KV | Add-Member -MemberType NoteProperty -Name 'Password' -Value $((($Result.data).data).Secret)
            }
            $false {
                $KV | Add-Member -MemberType NoteProperty -Name 'Password' -Value 'Only Available In Your Clipboard'
            }
        }
        return $KV
    }
    return $null
}
function Get-SecretForWiaB() {
    <#
    .SYNOPSIS
        Get password from Warehouse Vault for WiaB clusters.

    .DESCRIPTION
        Get password from Warehouse Vault for WiaB clusters.

    .PARAMETER Prism
        Prism Element name, same as the cluster name in Vault.
    
    .PARAMETER Tier
        Tier for different environment.

    .PARAMETER Site
        Site of cluster.

    .PARAMETER NXC
        NXC number of cluster.

    .PARAMETER Secret
        Different password of Nutanix cluster/host.
    
    .NOTES
        Version:        1.0.1
        Author:         <JAWAN36>
        Creation Date:  <2024-05-20>
        Purpose/Change: Initial script development
    
    .EXAMPLE
        Get-SecretForWiaB -Prism RETCN856-NXC000
        Get-SecretForWiaB -Site RETCN856

        Description
        -----------
        Get the default password of account "Site_pe_nutanix" for cluster RETCN856-NXC000.

    .EXAMPLE
        Get-SecretForWiaB -Prism RETCN856-NXC000 -Secret Site_Oob

        Description
        -----------
        Get the default password of account "Administrator" for host iLO on cluster RETCN856-NXC000.

    .EXAMPLE
        Get-SecretForWiaB -Prism RETCN888-NXC000 -Tier PPE

        Description
        -----------
        Get the default password of account "nutanix" for cluster RETCN888-NXC000 in PPE environment.
    #>
    param(
        [string] [ValidateSet("PROD", "PPE", "DT")]                                          $Tier = "PROD",
        [string] [Parameter(ParameterSetName = 'Cluster')]                                   $Site,
        [string] [ValidateSet("NXC000", "NXC001")] [Parameter(ParameterSetName = 'Cluster')] $NXC = "NXC000",
        [string] [Parameter(ParameterSetName = 'Prism')]                                     $Prism,
        [string] [ValidateSet("Site_Ahv_Nutanix", "Site_Ahv_Root", "Site_Gw_Svc", "Site_Gw_Priv_Key", "Site_Gw_Pub_Key",
                              "Site_Move", "Site_Oob", "Site_Pc_Admin", "Site_Pc_Nutanix", 
                              "Site_Pc_Svc", "Site_Pe_Admin", "Site_Pe_Nutanix", 
                              "Site_Pe_Svc", "Site_Witness_Admin", "Site_Witness_Nutanix")]  $Secret = "Site_Pe_Nutanix",
        [switch]                                                                             $Display
    )
    if ($Prism) {
        $Prism = $Prism.ToUpper()
        $Site  = $Prism.Split("-")[0]
        $NXC   = $Prism.Split("-")[1]
    }
    if (!$PSBoundParameters.ContainsKey('Tier')) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are using the default tier: $Tier"
    }
    if (!$PSBoundParameters.ContainsKey('Secret')) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are quering the default secret: $Secret"
    }
    #Load vault configuration
    $VaultVars  = $(Read-Var).Vault
    $Endpoint   = $VaultVars.Endpoint

    #Assemble the full namespace to query result from vault
    $Namespace  = $($VaultVars.WareHouse.MasterNamespace + "/" + ([string]$VaultVars.WareHouse.$Tier.NameSpace).Trim())
    $Token      = $VaultVars.WareHouse.Token
    $Engine     = ([string]$VaultVars.WareHouse.$Tier.Engine).Trim()
    $Site       = $Site.ToUpper()
    $SecretPath = $Site + "-" + $NXC + "/" + $Secret

    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Query from the vault under the namespace $($Namespace)"
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Retrieve the result from the path $($SecretPath)"
    $Headers    = @{
        "X-Vault-Namespace" = "$($Namespace)"
        "X-Vault-Token"     = "$($Token)"
    }
    $RequestURI = "v1/$($Engine)/data/$($SecretPath)"
    $Result     = Invoke-Vault-API -Endpoint $Endpoint `
                                   -RequestURI $RequestURI `
                                   -Headers $Headers `
                                   -Method GET
    if($Result){
        $KV = [pscustomobject]@{
            'Prism'    = $($Site + "-" + $NXC)
            'Secret'   = $Secret
            'Username' = (($Result.data).data).Username
        }
        Set-Clipboard -Value $((($Result.data).data).Secret)
        switch ($Display){
            $true {
                $KV | Add-Member -MemberType NoteProperty -Name 'Password' -Value $((($Result.data).data).Secret)
            }
            $false {
                $KV | Add-Member -MemberType NoteProperty -Name 'Password' -Value 'Only Available In Your Clipboard'
            }
        }
        return $KV
    }
    return $null
}
function Get-SecretForWtp() {
    param(
        [string] [ValidateSet("PROD", "PPE", "DT")]                                           $Tier = "PROD",
        [string] [ValidateSet("Nutanix", "ESXI")]                                             $Solution = "ESXI",
        [string] [parameter(Mandatory=$true)]                                                 $Cluster,
        [string] [ValidateSet("Site_Ahv_Nutanix", "Site_Ahv_Root", 
                              "Site_Gw_Svc", "Site_Oob", "Site_Pe_Admin", "Site_Pe_Nutanix")] $Secret = "Site_Pe_Nutanix",
        [switch]                                                                              $Display
    )
    if (!$PSBoundParameters.ContainsKey('Tier')) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are using the default tier: $Tier"
    }
    if (!$PSBoundParameters.ContainsKey('Solution')) {
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are using the default solution: $Solution"
    }
    $VaultVars  = $(Read-Var).Vault
    $Endpoint   = $VaultVars.Endpoint
    $Namespace  = $($VaultVars.Warehouse.MasterNamespace + "/" + ([string]$VaultVars.WareHouse.$Tier.NameSpace).Trim())
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Query from the vault under the namespace $($Namespace)"
    #First call to vault API and gather the access token
    $Token      = $VaultVars.WareHouse.Token
    $Engine     = ([string]$VaultVars.WareHouse.$Tier.Engine_ESXi).Trim()
    $SecretPath = ([string]$VaultVars.Warehouse.$Tier.$Solution).Trim() + "/" + $Cluster
    if("Nutanix" -eq $Solution){
        if (!$PSBoundParameters.ContainsKey('Secret')) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We are quering the default secret: $Secret"
        }
        $SecretPath += "/" + $Secret
    }
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Retrieve the result from the path $($SecretPath)"
    $Headers = @{
        "X-Vault-Namespace" = "$($Namespace)"
        "X-Vault-Token"     = "$($Token)"
    }
    $RequestURI = "v1/$($Engine)/data/$($SecretPath)"
    $Result     = Invoke-Vault-API -Endpoint $Endpoint `
                                   -RequestURI $RequestURI `
                                   -Headers $Headers `
                                   -Method GET
    if($Result) {
        $KV = [pscustomobject]@{
            'Cluster' = $Cluster
        }
        Switch($Solution) {
            "Nutanix" {
                $KV | Add-Member -NotePropertyName 'Username' -NotePropertyValue $Result.data.data.PSObject.Properties.Name
                $KV | Add-Member -NotePropertyName 'Secret' -NotePropertyValue $Secret
                Set-Clipboard -Value $Result.data.data.PSObject.Properties.Value
                Switch($Display){
                    $true {
                        $KV | Add-Member -NotePropertyName 'Password' -NotePropertyValue $Result.data.data.PSObject.Properties.Value
                    }
                    $false {
                        $KV | Add-Member -NotePropertyName 'Password' -NotePropertyValue 'Only Available in your clipboard'
                    }
                }
            }
            "ESXI" {
                $KV | Add-Member -NotePropertyName Username -NotePropertyValue $Result.data.data.Username
                Set-Clipboard -Value $Result.data.data.password
                Switch($Display){
                    $true {
                        $KV | Add-Member -NotePropertyName 'Password' -NotePropertyValue $Result.data.data.password
                    }
                    $false {
                        $KV | Add-Member -NotePropertyName 'Password' -NotePropertyValue 'Only Available in your clipboard'
                    }
                }
            }
        }
        return $KV
    }
    return $null
}
function Get-VaultSecret() {
    <#
    .SYNOPSIS
    The function returns the password for Nutanix.
    
    .DESCRIPTION
    The function returns passwords for SiaB and WiaB in production environment.
    It also provides a menu to select different passwords in Vault.
    The password will be pasted in clipboard by default.
    
    .PARAMETER Prism
    Mandatory parameter, it accept full cluster name or just site name.
    If you use just site name, it will add -NXC000 as default cluster name.

    .PARAMETER Tier
    This is now set with "PROD" as default.
    We can add D2/DT/PPE in future if needed.
    
    .EXAMPLE
    Get-VaultSecret

    Get-VaultSecret retcn856

    Get-VaultSecret retcn856-nxc000

    Get-VaultSecret -Prism retcn856

    Get-VaultSecret -Prism retcn856-nxc000

    .NOTES
    Selection list for Vault passwords (frequently used)
    ╔═════════════════════════╗
    ║      Vault Options      ║
    ╟─────────────────────────╢
    ║  1. Site_Pe_Nutanix     ║
    ║  2. Site_Pe_Admin       ║
    ║  3. Site_Ahv_Nutanix    ║
    ║  4. Site_Ahv_Root       ║
    ║  5. Site_Oob            ║
    ║  6. Site_Pe_Svc         ║
    ║  7. Quit                ║
    ╚═════════════════════════╝

    #2024-10-28 Add capability for D2/DT/PPE environment
    
    #>
    param(
        [string] [Parameter(Mandatory=$true)] $Prism
    )

    # Convert Prism to standard Prism object
    $Obj    = ConvertTo-StandardPrism -Name $Prism
    $Prism  = $Obj.PrismName
    $Domain = $Obj.Domain
    $Tier   = $Obj.Tier
    $Fqdn   = $Obj.Fqdn

    # validate the prism fqdn
    if (Resolve-DnsName $Fqdn) {
        Write-ConsoleLog -Level INFO -Message "$Fqdn is valid with DNS" # resolve DNS to check if cluster exist
    } else {
        Write-ConsoleLog -Level ERROR -Message "$Fqdn can't be resolved, please make sure you have correct name"
        break
    }

    # Get PC clusters
    $Vars = Read-Var
    $SiabPc = $Vars.Infrastructure.Retail.Nutanix.PrismCentral | Select-Object -ExpandProperty Cluster
    $WiabPc = $Vars.Infrastructure.Warehouse.Nutanix.PrismCentral | Select-Object -ExpandProperty Cluster
    $PcList = $SiabPc + $WiabPc

    # Check if cluster is a PC cluster
    if ($Prism -in $PcList) {
        # Get selection for prism central secrets
        do {
            Get-Menu -Title "Vault Options" -Selections "Site_Pe_Nutanix",
                "Site_Pe_Admin",
                "Site_Pe_Svc",   
                "Site_Ahv_Nutanix",
                "Site_Ahv_Root",
                "Site_Oob",
                "Site_Pc_Admin",
                "Site_Pc_Nutanix",
                "Site_Pc_Svc",
                "Site_Witness_Admin",
                "Site_Witness_Nutanix"
            $Selection = Read-Host "Please enter your selection (Q to quit)"
            if ($Selection -eq "Q") { return }
        } while (
            $Selection -notin (1..11)
        )

        # Switch selection to secret
        switch ($Selection) {
            1 { $Secret = "Site_Pe_Nutanix" }
            2 { $Secret = "Site_Pe_Admin" }
            3 { $Secret = "Site_Pe_Svc" }
            4 { $Secret = "Site_Ahv_Nutanix" }
            5 { $Secret = "Site_Ahv_Root" }
            6 { $Secret = "Site_Oob" }
            7 { $Secret = "Site_Pc_Admin" }
            8 { $Secret = "Site_Pc_Nutanix" }
            9 { $Secret = "Site_Pc_Svc" }
            10 { $Secret = "Site_Witness_Admin" }
            11 { $Secret = "Site_Witness_Nutanix" }
        }
        Write-ConsoleLog -Level INFO -Message "Start to query password for $Secret"

        # Assemble the full namespace to query result from vault for Retail&Warehouse
        if ($Prism -like "DS*" -or $Prism -like "MOD*") {
            Get-SecretForWiaB -Prism $Prism -Tier $Tier -Secret $Secret
        } else {
            Get-SecretForSiaB -Prism $Prism -Tier $Tier -Secret $Secret
        }
    } else {
        # Get selection for prism element secrets
        do {
            Get-Menu -Title "Vault Options" -Selections "Site_Pe_Nutanix",
                "Site_Pe_Admin",
                "Site_Pe_Svc",    
                "Site_Ahv_Nutanix",
                "Site_Ahv_Root",
                "Site_Oob"
            $Selection = Read-Host "Please enter your selection (Q to quit)"
            if ($Selection -eq "Q") { return }
        } while (
            $Selection -notin (1..6)
        )

        # Switch selection to secret
        switch ($Selection) {
            1 { $Secret = "Site_Pe_Nutanix" }
            2 { $Secret = "Site_Pe_Admin" }
            3 { $Secret = "Site_Pe_Svc" }
            4 { $Secret = "Site_Ahv_Nutanix" }
            5 { $Secret = "Site_Ahv_Root" }
            6 { $Secret = "Site_Oob" }
        }
        Write-ConsoleLog -Level INFO -Message "Start to query password for $Secret"

        # Assemble the full namespace to query result from vault for Retail&Warehouse
        if ($Prism -like "DS*" -or $Prism -like "MOD*") {
            Get-SecretForWiaB -Prism $Prism -Tier $Tier -Secret $Secret
        } else {
            Get-SecretForSiaB -Prism $Prism -Tier $Tier -Secret $Secret
        }
    }
}
function Test-AdCredential() {
    <#
    .SYNOPSIS
    Test AD credential.
    
    .DESCRIPTION
    Test AD credential.
    
    .PARAMETER Username
    Parameter for AD username to test.
    It can be "jawan36" or "<EMAIL>".
    
    .PARAMETER Password
    Parameter for password to test.
    
    .PARAMETER Tier
    Parameter for D2/DT/PPE/PROD, it will switch to different domain.
    
    .EXAMPLE
    Test-AdCredential -Username "gstjawan36" -Password "xxxxxxxx"

    .EXAMPLE
    Test-AdCredential -Username "jawan36" -Password "xxxxxxxx" -Tier DT
    
    .NOTES
    General notes
    #>
    [CmdletBinding()]
    [OutputType([System.Boolean])]
    param (
        [Parameter(Mandatory = $true)][ValidateNotNullOrEmpty()][string]                $Username,
        [Parameter(Mandatory = $true)][ValidateNotNullOrEmpty()][string]                $Password,
        [Parameter(Mandatory = $false)][ValidateSet("D2", "DT", "PPE", "PROD")][string] $Tier = "PROD"
    )

    switch ($Tier) {
        "Prod" { $Domain = "ikea.com" }
        "PPE"  { $Domain = "ikea.com" }
        "DT"   { $Domain = "ikeadt.com" }
        "D2"   { $Domain = "ikead2.com" }
    }

    try {
        Add-Type -AssemblyName System.DirectoryServices.AccountManagement
        $Context = New-Object System.DirectoryServices.AccountManagement.PrincipalContext([System.DirectoryServices.AccountManagement.ContextType]::Domain, $Domain)
        $IsValid = $context.ValidateCredentials($Username, $Password)
        return $IsValid
    } catch {
        Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "Failed to test user credentials. The error was: $_"
        return $false
    }
}
function Test-VaultToken() {
    <#
    .SYNOPSIS
    This function will be used to test Vault token validity.
    
    .DESCRIPTION
    This function will be used to test Vault token validity.
    If the token is not valid, it will return false.
    If the token is valid, it will return the token information.
    
    .PARAMETER Token
    Mandatory parameter, the Vault token.
    
    .EXAMPLE
    Test-VaultToken -Token $WhToken

    --------------------Output--------------------
    IssueTime  : 2024-11-18T02:45:33.562900384Z
    ExpireTime : 2024-12-20T02:45:33.562890616Z
    CreateTTL  : 2764800
    TTL        : 676641
    Namespace  : digital-warehouse
    
    .EXAMPLE
    Test-VaultToken -Token $WhToken

    --------------------Output--------------------
    2024-12-12T14:48:17 [ERROR] Test-VaultToken There is an error: Response status code does not indicate success: 403 (Forbidden).
    False

    .NOTES
    General notes
    #>
    param (
        [Parameter(Mandatory = $true)][string] $Token
    )
    if (!$Token) {
        $Token = Read-Host "Please input vault token" -MaskInput
    }
    $Endpoint = (Read-Var).Vault.Endpoint
    try {
        $Reply = Invoke-RestMethod -Method Get -Uri "https://$Endpoint/v1/auth/token/lookup-self" -Headers @{"X-Vault-Token" = $Token}
    } catch {
        Write-ConsoleLog -Level ERROR -FunctionName (Get-FunctionName) -Message "There is an error: $($_.Exception.Message)"
        return $false
    }

    if ($Reply) {
        <# Action to perform if the condition is true #>
        $Result = [PSCustomObject]@{
            "IssueTime"  = $Reply.data.issue_time
            "ExpireTime" = $Reply.data.expire_time            
            "CreateTTL"  = $Reply.data.creation_ttl
            "TTL"        = $Reply.data.ttl
            "Namespace"  = ($Reply.data.display_name -split "-auth-")[0]
        }
    }

    return $Result
}
function Update-VaultToken() {
    param(
        [ValidateSet("Retail", "Warehouse")] [string] $Domain
    )
    # $RegistryModulePath = "HKCU:\GDHAssist\"
    # $VaultTokenProp     = @{
    #         'Retail'    = 'VaultTokenRetail'
    #         'Warehouse' = 'VaultTokenWarehouse'
    # }
    try {
        # if(!(Test-Path -Path $RegistryModulePath)){
        #     New-Item -Path $RegistryModulePath -Force
        # }
        if (!($Domain)) {
            # New-ItemProperty -Path $RegistryModulePath `
            #                  -Name $VaultTokenProp.Retail `
            #                  -Type String `
            #                  -Value (ConvertFrom-SecureString -SecureString $VaultTokenRetail) `
            #                  -Force
            # New-ItemProperty -Path $RegistryModulePath `
            #                  -Name $VaultTokenProp.Warehouse `
            #                  -Type String `
            #                  -Value (ConvertFrom-SecureString -SecureString $VaultTokenWarehouse) `
            #                  -Force
            if ($IsWindows) {
                $VaultTokenRetail    = Read-Host "[PROMPT]Vault token for Retail" -AsSecureString
                $VaultTokenWarehouse = Read-Host "[PROMPT]Vault token for Warehouse" -AsSecureString
                $Global:Secrets.VaultToken.Retail = (ConvertFrom-SecureString -SecureString $VaultTokenRetail)
                $Global:Secrets.VaultToken.Warehouse = (ConvertFrom-SecureString -SecureString $VaultTokenWarehouse)
            } else {
                $Global:Secrets.VaultToken.Retail = Read-Host "[PROMPT]Vault token for Retail"
                $Global:Secrets.VaultToken.Warehouse = Read-Host "[PROMPT]Vault token for Warehouse"
            }
        } else {
            if ($IsWindows) {
                $VaultToken = Read-Host "[PROMPT]Vault token for $Domain" -AsSecureString
                $Global:Secrets.VaultToken.$Domain = (ConvertFrom-SecureString -SecureString $VaultToken)
            } else {
                $VaultToken = Read-Host "[PROMPT]Vault token for $Domain"
                $Global:Secrets.VaultToken.$Domain = $VaultToken
            }
        }
        Set-Content -Path $Global:ScrtJsonPath -Value ($Global:Secrets | ConvertTo-Json -Depth 10)
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The Vault token has been updated"
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "To update the Vault token, please run the command Update-VaultToken (-Domain)"
    } catch {
        Write-ConsoleLog -Level ERROR -FunctionName $(Get-FunctionName) -Message $_
        return $null
    }
    
}
function Update-GstAccount() {
    <#
    .SYNOPSIS
    This function is used to update the GST account in the registry.
    
    .DESCRIPTION
    This function is used to update the GST account (or admin account 
    for D2/DT) in the registry.
    
    .PARAMETER Username
    Parameter description
    
    .PARAMETER Password
    Parameter description
    
    .PARAMETER Tier
    Default tier is PROD. It can be D2, DT, PPE, PROD.
    
    .EXAMPLE
    Update-GstAccount -Username "<EMAIL>" -Password "xxxxxxxx"

    .EXAMPLE
    Update-GstAccount -Username "<EMAIL>" -Password "xxxxxxxx" -Tier DT
    
    .NOTES
    General notes
    #>
    param(
        [string] $Username,
        [string] $Password,
        [ValidateSet("D2", "DT", "PPE", "PROD")][string] $Tier = "PROD"
    )
    # $RegistryModulePath = "HKCU:\GDHAssist\"

    # switch ($Tier) {
    #     "D2"    { $GstUsernameProp = "GstD2Username"; $GstPwdProp = "GstD2Password" ; $Example = "<EMAIL>"}
    #     "DT"    { $GstUsernameProp = "GstDTUsername"; $GstPwdProp = "GstDTPassword"; $Example = "<EMAIL>"}
    #     "PPE"   { $GstUsernameProp = "GstUsername"; $GstPwdProp = "GstPassword"; $Example = "<EMAIL>" }
    #     Default { $GstUsernameProp = "GstUsername"; $GstPwdProp = "GstPassword"; $Example = "<EMAIL>" }
    # }
    switch ($Tier) {
        "D2"    {$ScrtTier = "D2";      $Example = "<EMAIL>"}
        "DT"    {$ScrtTier = "DT";      $Example = "<EMAIL>"}
        "PPE"   {$ScrtTier = "Default"; $Example = "<EMAIL>"}
        Default {$ScrtTier = "Default"; $Example = "<EMAIL>"}
    }
    try{
        if(-not $Password){
            if ($IsWindows) {
                $GSTCredential = Get-Credential -Title "[PROMPT]Input your $Tier GST account credential" -Message "Example: $Example"
                $Global:Secrets.GstAccount.$ScrtTier.Username = $GSTCredential.Username
                $Global:Secrets.GstAccount.$ScrtTier.Encrypted = (ConvertFrom-SecureString -SecureString $GSTCredential.Password)
            } else {
                Write-Host "[PROMPT] Input your $Tier GST account credential"
                Write-Host "Example: $Example"
                $Global:Secrets.GstAccount.$ScrtTier.Username  = Read-Host "User"
                $Global:Secrets.GstAccount.$ScrtTier.Encrypted = Read-Host "Password for user $($Global:Secrets.GstAccount.$ScrtTier.Username)"
            }
        } else {
            if ($IsWindows) {
                $SecPassword = ConvertTo-SecureString $Password -AsPlainText -Force
                $Encrypted = ConvertFrom-SecureString -SecureString $SecPassword
                $Global:Secrets.GstAccount.$ScrtTier.Username = $Username
                $Global:Secrets.GstAccount.$ScrtTier.Encrypted = $Encrypted
            } else {
                $Global:Secrets.GstAccount.$ScrtTier.Username = $Username
                $Global:Secrets.GstAccount.$ScrtTier.Encrypted = $Password
            }
        }
        Set-Content -Path $Global:ScrtJsonPath -Value ($Global:Secrets | ConvertTo-Json -Depth 10)
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The $Tier GST account is updated"
        Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "To update the GST account, please run the command Update-GstAccount -Tier" 
    } catch {
        Write-Host $_ -ForegroundColor Yellow
    }
}
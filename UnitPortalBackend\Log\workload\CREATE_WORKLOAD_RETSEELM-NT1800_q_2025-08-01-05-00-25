2025-08-01 13:00:25,883 INFO Start to run the task.
2025-08-01 13:00:25,898 INFO ****************************************************************************************************
2025-08-01 13:00:25,898 INFO *                                                                                                  *
2025-08-01 13:00:25,898 INFO *                                        Check VM existence                                        *
2025-08-01 13:00:25,898 INFO *                                                                                                  *
2025-08-01 13:00:25,912 INFO ****************************************************************************************************
2025-08-01 13:00:25,919 INFO Checking if vm already exists in the PE cluster.
2025-08-01 13:00:25,919 INFO Checking if RETSEELM-NT1800 existed in PE.
2025-08-01 13:00:25,919 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 13:00:25,919 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms?sortCriteria=vm_name&searchString=RETSEELM-NT1800, method: GET, headers: None
2025-08-01 13:00:25,919 INFO params: None
2025-08-01 13:00:25,919 INFO User: <EMAIL>
2025-08-01 13:00:25,919 INFO payload: None
2025-08-01 13:00:25,919 INFO files: None
2025-08-01 13:00:25,919 INFO timeout: 30
2025-08-01 13:00:28,132 INFO Got the VM list from RETSEELM-NXC000.
2025-08-01 13:00:28,133 INFO RETSEELM-NT1800 doesn't exist in RETSEELM-NXC000.
2025-08-01 13:00:28,146 INFO RETSEELM-NT1800 not exists in Cluster RETSEELM-NXC000.IKEAD2.COM, move on...
2025-08-01 13:00:28,157 INFO Checking if vm already exists in the inventory AD/Tower.
2025-08-01 13:00:28,180 INFO Calling restapi, URL: https://thorshammereu.ikea.com/api/runmethod/robo/get-computer/prefix=RETSEELM-NT1800&client=yes, method: GET, headers: None
2025-08-01 13:00:28,180 INFO params: None
2025-08-01 13:00:28,181 INFO User: L-LTTHOR-A-ITSEELM
2025-08-01 13:00:28,181 INFO payload: None
2025-08-01 13:00:28,181 INFO files: None
2025-08-01 13:00:28,181 INFO timeout: None
2025-08-01 13:00:30,194 INFO VM 'RETSEELM-NT1800' doesn't exist in AD, continue...
2025-08-01 13:00:30,206 INFO Checking if vm already exists in IPAM.
2025-08-01 13:00:30,206 INFO Start to check if RETSEELM-NT1800.IKEAD2.COM existed in IPAM...
2025-08-01 13:00:30,206 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NT1800.IKEAD2.COM', method: GET, headers: None
2025-08-01 13:00:30,220 INFO params: None
2025-08-01 13:00:30,220 INFO User: <EMAIL>
2025-08-01 13:00:30,220 INFO payload: None
2025-08-01 13:00:30,220 INFO files: None
2025-08-01 13:00:30,220 INFO timeout: 30
2025-08-01 13:00:31,561 INFO 'RETSEELM-NT1800' not exists in IPAM, continue...
2025-08-01 13:00:31,573 INFO ****************************************************************************************************
2025-08-01 13:00:31,573 INFO *                                                                                                  *
2025-08-01 13:00:31,573 INFO *                                              Sizing                                              *
2025-08-01 13:00:31,573 INFO *                                                                                                  *
2025-08-01 13:00:31,573 INFO ****************************************************************************************************
2025-08-01 13:00:31,587 INFO Sizing, check if cluster has enough capacity for this VM.
2025-08-01 13:00:31,588 INFO Get a list of existing hosts from RETSEELM-NXC000.IKEAD2.COM
2025-08-01 13:00:31,588 INFO Calling /hosts through v1 API using GET method
2025-08-01 13:00:31,588 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 13:00:31,588 INFO params: None
2025-08-01 13:00:31,588 INFO User: <EMAIL>
2025-08-01 13:00:31,588 INFO payload: None
2025-08-01 13:00:31,588 INFO files: None
2025-08-01 13:00:31,589 INFO timeout: None
2025-08-01 13:00:33,401 INFO Get cluster details from RETSEELM-NXC000.IKEAD2.COM
2025-08-01 13:00:33,401 INFO Calling /cluster through v1 API using GET method
2025-08-01 13:00:33,401 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster, method: GET, headers: None
2025-08-01 13:00:33,401 INFO params: None
2025-08-01 13:00:33,401 INFO User: <EMAIL>
2025-08-01 13:00:33,401 INFO payload: None
2025-08-01 13:00:33,401 INFO files: None
2025-08-01 13:00:33,401 INFO timeout: None
2025-08-01 13:00:35,351 INFO Get a list of existing user VMs from RETSEELM-NXC000.IKEAD2.COM
2025-08-01 13:00:35,352 INFO Calling /vms through v2 API using GET method
2025-08-01 13:00:35,352 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/vms, method: GET, headers: None
2025-08-01 13:00:35,352 INFO params: None
2025-08-01 13:00:35,352 INFO User: <EMAIL>
2025-08-01 13:00:35,353 INFO payload: None
2025-08-01 13:00:35,353 INFO files: None
2025-08-01 13:00:35,353 INFO timeout: None
2025-08-01 13:00:37,170 INFO The cluster is a 3 node(s) cluster
2025-08-01 13:00:37,181 INFO Fetching capacity from node RETSEELM-NX7001
2025-08-01 13:00:37,181 INFO Storage of node RETSEELM-NX7001 is 44.68 TiB
2025-08-01 13:00:37,181 INFO Number of cores on node RETSEELM-NX7001 is 20
2025-08-01 13:00:37,183 INFO Memory install on node RETSEELM-NX7001 is 377.08 GiB
2025-08-01 13:00:37,183 INFO Fetching capacity from node RETSEELM-NX7002
2025-08-01 13:00:37,183 INFO Storage of node RETSEELM-NX7002 is 44.68 TiB
2025-08-01 13:00:37,183 INFO Number of cores on node RETSEELM-NX7002 is 20
2025-08-01 13:00:37,184 INFO Memory install on node RETSEELM-NX7002 is 377.08 GiB
2025-08-01 13:00:37,184 INFO Fetching capacity from node RETSEELM-NX7003
2025-08-01 13:00:37,184 INFO Storage of node RETSEELM-NX7003 is 44.68 TiB
2025-08-01 13:00:37,185 INFO Number of cores on node RETSEELM-NX7003 is 20
2025-08-01 13:00:37,185 INFO Memory install on node RETSEELM-NX7003 is 345.58 GiB
2025-08-01 13:00:37,186 INFO Number of nodes in this cluster is 3
2025-08-01 13:00:37,186 INFO Total storage capacity on this cluster is 134.04 TiB
2025-08-01 13:00:37,186 INFO total number of CPU cores on cluster is 60
2025-08-01 13:00:37,187 INFO Total memory capacity on this cluster is 1099.74 GiB
2025-08-01 13:00:37,187 INFO Resilient storage capacity on this cluster is 84.************** TiB
2025-08-01 13:00:37,187 INFO Number of resilient physical CPU cores is 40
2025-08-01 13:00:37,187 INFO Number of resilient physical CPU cores accounting CVMs is 34
2025-08-01 13:00:37,187 INFO Number of resilient virtual CPU cores (assuming 1:4 ratio) is 136
2025-08-01 13:00:37,187 INFO Resilient memory capacity on this cluster is 722.************* GiB
2025-08-01 13:00:37,187 INFO Resilient memory capacity accounting CVMs on this cluster is 658.************* GiB
2025-08-01 13:00:37,187 INFO Utilized storage of cluster is 0.97 TiB
2025-08-01 13:00:37,187 INFO There are 6 VMs on this cluster
2025-08-01 13:00:37,187 INFO Number of virtual cores used by 6 VMs that are powered on is 66
2025-08-01 13:00:37,187 INFO Memory used by 6 VMs that are powered on is 264.0 GiB
2025-08-01 13:00:37,187 INFO Available storage for new VM provisioning is 83.************** TiB
2025-08-01 13:00:37,187 INFO Available vCPU cores for new VM provisioning is 70
2025-08-01 13:00:37,187 INFO Available memory for new VM provisioning is 394.************* GiB
2025-08-01 13:00:37,205 INFO ****************************************************************************************************
2025-08-01 13:00:37,205 INFO *                                                                                                  *
2025-08-01 13:00:37,205 INFO *                                Checking workload network on NTX.                                 *
2025-08-01 13:00:37,205 INFO *                                                                                                  *
2025-08-01 13:00:37,205 INFO ****************************************************************************************************
2025-08-01 13:00:37,249 INFO Checking PE network by VlanId=793
2025-08-01 13:00:37,249 INFO Getting network list from RETSEELM-NXC000
2025-08-01 13:00:37,250 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/networks, method: GET, headers: None
2025-08-01 13:00:37,250 INFO params: None
2025-08-01 13:00:37,250 INFO User: <EMAIL>
2025-08-01 13:00:37,250 INFO payload: None
2025-08-01 13:00:37,250 INFO files: None
2025-08-01 13:00:37,251 INFO timeout: 30
2025-08-01 13:00:39,096 INFO Got the network list from RETSEELM-NXC000.
2025-08-01 13:00:39,103 INFO Vlan 793 is found
2025-08-01 13:00:39,119 INFO The network is found, the UUID is 9531e569-3bec-4d92-8581-6209bea747db
2025-08-01 13:00:39,137 INFO ****************************************************************************************************
2025-08-01 13:00:39,137 INFO *                                                                                                  *
2025-08-01 13:00:39,137 INFO *                                           Check image                                            *
2025-08-01 13:00:39,137 INFO *                                                                                                  *
2025-08-01 13:00:39,137 INFO ****************************************************************************************************
2025-08-01 13:00:43,223 INFO Verifying workload image existence in DB and on cluster.
2025-08-01 13:00:44,672 INFO Checking if RHELx_AUTO image
2025-08-01 13:00:47,322 INFO Validating image 'ICC_2k16_v0015-RETSEELM-NXC000' existence in PE 'RETSEELM-NXC000'...
2025-08-01 13:00:47,588 INFO Start to find the image ICC_2k16_v0015-RETSEELM-NXC000 from RETSEELM-NXC000
2025-08-01 13:00:47,588 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/images, method: GET, headers: None
2025-08-01 13:00:47,588 INFO params: None
2025-08-01 13:00:47,588 INFO User: <EMAIL>
2025-08-01 13:00:47,588 INFO payload: None
2025-08-01 13:00:47,588 INFO files: None
2025-08-01 13:00:47,588 INFO timeout: 30
2025-08-01 13:00:49,533 INFO Getting image list from RETSEELM-NXC000
2025-08-01 13:00:49,533 INFO Got the image list from RETSEELM-NXC000.
2025-08-01 13:00:49,533 INFO The image ICC_2k16_v0015-RETSEELM-NXC000 is found in RETSEELM-NXC000
2025-08-01 13:00:50,362 INFO The image is found in PE
2025-08-01 13:03:32,336 INFO Start to cleanup VM...
2025-08-01 13:03:32,413 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='retseelm-nt1800.ikead2.com', method: GET, headers: None
2025-08-01 13:03:32,413 INFO params: None
2025-08-01 13:03:32,413 INFO User: <EMAIL>
2025-08-01 13:03:32,413 INFO payload: None
2025-08-01 13:03:32,413 INFO files: None
2025-08-01 13:03:32,413 INFO timeout: 30
2025-08-01 13:03:33,497 WARNING Can't find fqdn retseelm-nt1800.ikead2.com on Ipam, is it already gone?
2025-08-01 13:03:33,498 INFO Getting VM list from RETSEELM-NXC000.
2025-08-01 13:03:33,498 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms?sortCriteria=vm_name&searchString=RETSEELM-NT1800, method: GET, headers: None
2025-08-01 13:03:33,498 INFO params: None
2025-08-01 13:03:33,498 INFO User: admin
2025-08-01 13:03:33,498 INFO payload: None
2025-08-01 13:03:33,498 INFO files: None
2025-08-01 13:03:33,498 INFO timeout: 30
2025-08-01 13:03:35,301 INFO Got the VM list from RETSEELM-NXC000.
2025-08-01 13:03:35,310 INFO Start to check if protection domain [RETSEELM-NXC000-Gold_CCG] exists...
2025-08-01 13:03:35,311 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/protection_domains/RETSEELM-NXC000-Gold_CCG, method: GET, headers: None
2025-08-01 13:03:35,311 INFO params: None
2025-08-01 13:03:35,312 INFO User: admin
2025-08-01 13:03:35,312 INFO payload: None
2025-08-01 13:03:35,312 INFO files: None
2025-08-01 13:03:35,312 INFO timeout: None
2025-08-01 13:03:37,130 WARNING Response content: b'{"message":"Protection domain \'RETSEELM-NXC000-Gold_CCG\' does not exist","detailed_message":"Protection domain \'RETSEELM-NXC000-Gold_CCG\' does not exist","error_code":{"code":1103,"help_url":"http://my.nutanix.com"}}'
2025-08-01 13:03:37,131 WARNING API response is not ok, going to do the 2 retry...
2025-08-01 13:03:38,874 WARNING Response content: b'{"message":"Protection domain \'RETSEELM-NXC000-Gold_CCG\' does not exist","detailed_message":"Protection domain \'RETSEELM-NXC000-Gold_CCG\' does not exist","error_code":{"code":1103,"help_url":"http://my.nutanix.com"}}'
2025-08-01 13:03:38,874 WARNING API response is not ok, going to do the 3 retry...
2025-08-01 13:03:40,342 WARNING Response content: b'{"message":"Protection domain \'RETSEELM-NXC000-Gold_CCG\' does not exist","detailed_message":"Protection domain \'RETSEELM-NXC000-Gold_CCG\' does not exist","error_code":{"code":1103,"help_url":"http://my.nutanix.com"}}'
2025-08-01 13:03:40,342 WARNING API response is not ok, going to do the 4 retry...
2025-08-01 13:03:41,773 WARNING Response content: b'{"message":"Protection domain \'RETSEELM-NXC000-Gold_CCG\' does not exist","detailed_message":"Protection domain \'RETSEELM-NXC000-Gold_CCG\' does not exist","error_code":{"code":1103,"help_url":"http://my.nutanix.com"}}'
2025-08-01 13:03:41,773 WARNING API response is not ok, going to do the 5 retry...
2025-08-01 13:03:43,253 WARNING Response content: b'{"message":"Protection domain \'RETSEELM-NXC000-Gold_CCG\' does not exist","detailed_message":"Protection domain \'RETSEELM-NXC000-Gold_CCG\' does not exist","error_code":{"code":1103,"help_url":"http://my.nutanix.com"}}'
2025-08-01 13:03:43,278 WARNING PD doesn't exist, skipping...
2025-08-01 13:03:43,290 WARNING Can't find vm matching name RETSEELM-NT1800 on Nutanix, skipping...
2025-08-01 13:03:43,305 INFO Checking template 9275 existence...
2025-08-01 13:03:45,101 INFO Good, we found the template: 9275 in tower.
2025-08-01 13:03:46,470 WARNING Tower inventory object retseelm-nt1800.ikead2.com doesn't exist! Continue...
2025-08-01 13:03:46,493 INFO Start to delete machine RETSEELM-NT1800 in Thor's Hammer...
2025-08-01 13:03:46,493 INFO Calling restapi, URL: https://thorshammereu.ikea.com/api/runmethod/robo/remove-computer/computerName=RETSEELM-NT1800, method: GET, headers: None
2025-08-01 13:03:46,493 INFO params: None
2025-08-01 13:03:46,493 INFO User: L-LTTHOR-A-ITSEELM
2025-08-01 13:03:46,493 INFO payload: None
2025-08-01 13:03:46,493 INFO files: None
2025-08-01 13:03:46,493 INFO timeout: None
2025-08-01 13:03:47,912 INFO Succeeded to delete machine!
2025-08-01 13:03:47,928 INFO Delete computer on ThorsHammer succeed.
2025-08-01 13:03:47,956 INFO VM clean up successfully.

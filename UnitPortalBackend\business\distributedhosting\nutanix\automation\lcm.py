# installed module
import logging
import time
import multiprocessing
import datetime
import re
import json
import uuid
import sys
from flask import Flask
import traceback

from sqlalchemy.exc import NoResultFound, MultipleResultsFound

# local file
from business.authentication.authentication import ServiceAccount, Vault
from business.distributedhosting.nutanix.automation.automation_exception import LCMTimeIsNotGood
from business.distributedhosting.nutanix.maintenance import Maintenance
from business.distributedhosting.nutanix.pe_components import RestEulas
from business.generic.base_up_exception import BaseUpException
from business.generic.commonfunc import SSHConnect, Redfish
from collector.collectors.modules.api_calls import with_app_context
from models.ntx_models_wh import ModelWarehousePrismCentral, ModelWarehousePrismElement
logging.getLogger('paramiko').setLevel(logging.INFO)
# paramiko log is too annoying
from business.loggings.loggings import DBLogging
from business.distributedhosting.nutanix.nutanix import PrismElement, Prism_Element
from models.atm_models import ModelRetailNutanixAutomationSPPLCMTask, ModelRetailNutanixAutomationSPPLCMTaskSchema, \
    ModelRetailNutanixAutomationAOSLCMTask, ModelRetailNutanixAutomationAOSLCMTaskSchema, \
    ModelRetailNutanixAutomationAOSLCMTaskLog, ModelNutanixSeamlessLcmPlannedPEs
from models.auth_models import ModelUser
from models.ntx_models import ModelPrismElement, ModelPrismCentral
from models.models import db
from business.distributedhosting.nutanix.automation.automation import Automation
import static.SETTINGS as SETTING
from business.generic.commonfunc import DBConfig
from business.generic.commonfunc import create_file, setup_common_logger, NutanixAPI
from business.distributedhosting.nutanix.task_common import TaskCommon
from business.distributedhosting.nutanix.task_status import TaskStatus, LcmTaskStatus
import werkzeug.exceptions as flaskex
from business.benchmark.benchmark import Benchmark
from packaging import version


class LCM():
    """
    create_task -> start_subprocess_for_spp_upgrade -> upgrade_spp
    scan_spp_task -> check_task_status -> check_single_task
    """
    def __init__(self, pc=None, pe=None, sa=None, token=None, facility_type=None, slcm_plan_id=None) -> None:
        self.facility_type = facility_type
        self.pc = pc.lower() if pc else None
        self.pe = pe
        self.token = token
        self.task = dict()
        self.is_lcm_version_good = False
        self.logger = None
        if sa:
            self.sa = sa
        else:
            _sa = ServiceAccount(usage="nutanix_pm")
            self.sa = _sa.get_service_account()
        self.slcm_plan_id = slcm_plan_id
    
    def init_bmk(self, pe=None, facility_type=None):
        self.facility_type = facility_type if facility_type else self.facility_type
        if self.facility_type == "retail":
            self.model_pe = ModelPrismElement
            self.model_pc = ModelPrismCentral
        elif self.facility_type == "warehouse":
            self.model_pe = ModelWarehousePrismElement
            self.model_pc = ModelWarehousePrismCentral
        self.pe = pe if pe else self.pe

        # Ensure session is active
        session = db.session()
        try:
            self.cluster_info = session.query(self.model_pe).filter_by(fqdn=self.pe).one()
            self.benchmark = Benchmark().get_bmk_by_id(bmk_id=self.cluster_info.bmk_id)
            self.pe_name = self.cluster_info.name
            usage = session.query(self.model_pc).filter_by(fqdn=self.cluster_info.prism).one().service_account
            _sa = ServiceAccount(usage)
            self.sa = _sa.get_service_account()
            self.vault = Vault(self.benchmark['tier'], namespace = f"{self.benchmark['vault']['master_namespace']}/{self.benchmark['vault']['tier_namespace']}", usage=self.benchmark['vault']['service_account'])
            self.spp_lowest_version = self.benchmark['lcm_version']['spp_lowest_version']
            self.spp_step_version = self.benchmark['lcm_version']['spp_step_version']
            self.spp_base_version = self.benchmark['lcm_version']['spp_base_version']
            self.target_spp_version = self.benchmark['lcm_version']['spp_latest_version']
        finally:
            session.close()

    def if_good_timing(self, allowed_start=21, allowed_end=5):
        time_diff_dict = {
            'ssp-na-ntx.ikea.com': -4,
            'ssp-china-ntx.ikea.com': 8,
            'ssp-apac-ntx.ikea.com': 9,
            'ssp-eu-ntx.ikea.com': 2,
            'ssp-dt-ntx.ikeadt.com': 2,
            'ssp-ru-ntx.ikea.com': 3
        }
        if self.pc in time_diff_dict.keys():
            _time_diff = time_diff_dict[self.pc]
        else:
            _time_diff = 2
        current_hour = (datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours = _time_diff)).hour
        return (current_hour <= allowed_end or current_hour >= allowed_start)

    def create_task(self, task_id=None):
        self.init_bmk()
        try:
            task = dict()
            user = ModelUser.query.filter_by(token=self.token).first()
            if not user:
                raise Exception("Cann't find a user by this token, try re-login maybe ?")
            
            if not self.if_good_timing():
                raise Exception("We are only allowed to perform spp upgrade \
                                during 21:00-4:00 local time, sir.")
            
            ongoing_tasks = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(
                pc=self.pc).filter_by(status=TaskStatus.IN_PROGRESS).all()
            if len(ongoing_tasks) >= 10:
                return False, 'There are already 10 tasks running in parallel in this PC, \
                                please wait for at least 1 of them to be done.'
            if not task_id:
                # we need to create a task first.
                task["creater"] = user.username
                task["pe"] = self.pe
                task["pc"] = self.pc
                task["status"] = "Not Started"
                task["create_date"] = datetime.datetime.utcnow().strftime("%Y-%m-%d,%H:%M:%S")
                task["target_version"] = self.target_spp_version
                task["facility_type"] = self.facility_type
                task["slcm_plan_id"] = self.slcm_plan_id
                _lcm_task = ModelRetailNutanixAutomationSPPLCMTask(**task)
                db.session.add(_lcm_task)
                db.session.commit()

            else:
                # task is already there, we detected a finished middle task, and proceed.
                pass
            _lcm_task_schema = ModelRetailNutanixAutomationSPPLCMTaskSchema()
            self.task = _lcm_task_schema.dump(_lcm_task)
            self.start_subprocess_for_spp_upgrade()
            return True, ''

        except Exception as e:
            logging.error(str(e))
            return False, str(e)

    def start_subprocess_for_spp_upgrade(self):
        logging.info(f"Starting spp upgrade process in {self.pe}, target version {self.target_spp_version}.")
        self.logger = ''
        p = multiprocessing.Process(target=self.upgrade_spp)
        p.start()
        _task = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=self.task['id']).first()
        _task.pid = p.pid
        db.session.commit()

    def upgrade_spp(self):
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)  # db is in models.py
        app.app_context().push()  # context definition
        def _write_log(lg, log_info, severity='info'):
            # got pissed off that everytime I need to write log in 2 place...
            if severity not in ['info', 'warning', 'error']:
                severity = 'info'
            lg.write_lcm_log(log_info, logseverity=severity)
            _logger = getattr(self.logger, severity)
            _logger(log_info)

        _task = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=self.task["id"]).first()
        pe, pc, target_version = self.task['pe'], self.task['pc'], self.task['target_version']

        if re.match(TaskStatus.IN_PROGRESS, _task.status, re.I):
            # match the task status with "inprogress", jump out if it's already in progress, just in case.
            logging.info(f"SPP upgrading task :{self.task['id']} already started.")
            return
        _task.status = "Prechecking"
        db.session.commit()  # change the task status to inprogress
        self._update_slcm_planned_pe(_task.slcm_plan_id, _task.pe, LcmTaskStatus.PRE_CHECKING)

        lg = DBLogging(taskid=self.task["id"], logtype="SPP")
        lg.write_lcm_log("Starting to create local log file.")
        # check if foundation version meets the requirement
        try:
            if local_log_path := create_file(filepath=SETTING.SPP_LCM_LOG_PATH,
                                             filename=f'{pc}_{pe}_{datetime.datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}'):
                logging.info(f"Local log file created, path: {local_log_path}.")
                lg.write_lcm_log(loginfo=f'Local log file created, path: {local_log_path}.')
                self.logger = setup_common_logger(str(uuid.uuid4()), local_log_path)
                # for function/class using, logging into the specific file
                _task.detail_log_path = local_log_path
                db.session.commit()
            else:
                raise Exception('Failed to create local log file, workload deployment will be ended now.')
            lg.write_lcm_log("Local log file created.")


            self.logger.title(f"SPP upgrade started, PE:{pe}, target version:{target_version}")

            _write_log(lg, "Getting available SPP updates.")
            plan = self.get_spp_upgrade_plan()
            _write_log(lg, f"We detected {len(plan)} hosts, \
                       {len([_plan for _plan in plan if _plan['need_upgrade']])} need to be upgrade.")
            if not len([_plan for _plan in plan if _plan['need_upgrade']]):
                self.update_lcm_version_info_to_pe()
                lg.write_lcm_log("Dude, all SPP are good, why did you start this task, too early to drink !!")
                _task = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=self.task["id"]).first()
                _task.status = "Done"
                db.session.commit()
                self._update_slcm_planned_pe(_task.slcm_plan_id, _task.pe, TaskStatus.DONE)
                return
            
            self.logger.title("SPP pre upgrading")
            

            _write_log(lg, "SPP pre-upgrading.")
            _write_log(lg, "1.Reset ilo 2.Stop foundation 3. disable pem")
            self.pre_upgrade()


            _write_log(lg, "Getting PE lcm version info.")
            
            lcm_loop = 0 
            while lcm_loop < 5 :
                lcm_info = self.get_pe_lcm_info()  # response is a json if successfull done
                if lcm_info.get(".return"):
                    break
                else:
                    self.logger.info(f"Getting lcm info failed, lcm info :{lcm_info}")
                    time.sleep(20)
                    lcm_loop += 1
            lcm_upgrade_flag = False
            if lcm_version := lcm_info['.return']['semantic_version']:
                self.model_pe.query.filter_by(fqdn=self.pe).first().lcm_version = lcm_version
                db.session.commit()
                if version.parse(lcm_version) >= version.parse(self.benchmark['lcm_version']['lcm_version']):
                    self.is_lcm_version_good = True
                    _write_log(lg, f"LCM version:{lcm_version} is greater or equals the required version.")
                else:
                    _write_log(lg, f"LCM version:{lcm_version} is less than the required version, \
                               let's check again after performing inventory.")
                    lcm_upgrade_flag = True
            else:
                message = "Failed to get LCM version of this PE."
                _write_log(lg, message, 'error')
                raise Exception(message)

            _write_log(lg, "Getting PE lcm status.")
            lcm_status = self.get_lcm_progress(logger=self.logger)
            _write_log(lg, f"LCM progress status is '{lcm_status}' .")
            lcm_status = self.handle_lcm_status(lg, lcm_status, _task)
            if lcm_status is False:  # SPP Upgrade in progress
                return
            _write_log(lg, "LCM is not progressing with anything, let's perform inventory now.")
            if version.parse(self.cluster_info.aos_version) >= version.parse("6.8"):
                task_uuid = self.p_e.perform_inventory_v4()
            else:
                task_uuid = self.perform_pe_inventory()
            _write_log(lg, f"Inventory has been launched, task uuid :{task_uuid}.")
            _pe = PrismElement(pe=pe, logger=self.logger, sa=self.sa)
            continue_flag = False
            for _i in range(30 + (30 if lcm_status == "Inventory" else 0)):
                # if lcm is already doing one invenroty, then we wait for more time
                _write_log(lg, "Taking a 2-minutes power nap now.")
                time.sleep(120)  # wait 2 minutes , then get the lcm status
                _write_log(lg, "Time up, getting pe lcm task status.")
                try:
                    lcm_status = self.get_lcm_progress(logger=self.logger)
                except Exception:
                    if lcm_upgrade_flag:
                        _write_log(lg, "We have an issue querying lcm status, but it's probably because of we are upgrading lcm component.")
                        _write_log(lg, "Let's wait for some more time.")
                    else:
                        raise Exception("We have an issue querying lcm status, please try again later.")
                if lcm_status:  # if it's None, mean lcm is idle
                    _write_log(lg, "LCM is still not finished.")
                    continue

                _write_log(lg, "LCM is not progressing with anything, let's check the task.")
                task_status = _pe.get_task_status(task_uuid)

                if tasks := task_status['entities']:
                    if [_subtask for _subtask in tasks if _subtask['status'] != 'succeeded']:
                        _write_log(lg, ';'.join([f"task_name:{_subtask['operation']}, \
                                                 task_status:{_subtask['status']}" for _subtask in tasks]))
                        _write_log(lg, "There is still tasks not finished, \
                                   but LCM is over?? weird, let's wait for some more time.")
                    else:
                        continue_flag = True
                        _write_log(lg, "All tasks are succeeded, good, let's proceed.")
                        break
                else:
                    _write_log(lg, f"Empty task??? task uuid :{task_uuid}, game over now.")
                    raise Exception("Cannot get the task details, but lcm is idle, task id : {task_uuid}")

            if not continue_flag:
                raise Exception("60 minutes have passed; LCM is still ongoing or having issue. We are not allowed to continue, but you can start another round after 30 minutes.")

            if not self.is_lcm_version_good:  # check lcm version again after inventory
                lcm_info = self.get_pe_lcm_info()   # response is a json if successfull done
                if lcm_version := lcm_info['.return']['semantic_version']:
                    self.model_pe.query.filter_by(fqdn=self.pe).first().lcm_version = lcm_version
                    db.session.commit()
                    if version.parse(lcm_version) >= version.parse(self.benchmark['lcm_version']['lcm_version']):
                        self.is_lcm_version_good = True
                        _write_log(lg, f"LCM version:{lcm_version} is greater or equals the required version, \
                                   great, inventory worked!!")
                    else:
                        _write_log(lg, f"LCM version:{lcm_version} is less than the required version even after \
                                   performing the inventory, weird, break here.", 'error')
                else:
                    message = "Failed to get LCM version of this PE."
                    _write_log(lg, message, 'error')
                    raise Exception(message)

            # check foundation version now

            #####################################################################
            #                                                                   #
            #                  Check foundation version                         #
            #                                                                   #
            #####################################################################
            _write_log(lg, "Getting PE foundation version.")
            foundation_flag = False
            if fnd := self.get_lcm_component_version(component="Foundation"):
                _write_log(lg, f"PE foundation version is {fnd[0]['version']}")
                self.model_pe.query.filter_by(fqdn=self.pe).first().foundation_version = fnd[0]['version']
                db.session.commit()

                if version.parse(fnd[0]['version']) < version.parse(self.benchmark['lcm_version']['foundation_version']):
                    foundation_flag = True
                    _write_log(lg, f"We need to upgrade foundation to {self.benchmark['lcm_version']['foundation_version']}.")
                else:
                    _write_log(lg, "Very good, current foundation version is good for SPP upgrade, let's roll.")
            else:
                message = "Failed to get foundation version of this PE."
                _write_log(lg, message, 'error')
                raise Exception(message)
            #####################################################################
            #                                                                   #
            #                  Upgrade foundation version                       #
            #                                                                   #
            #####################################################################
            if foundation_flag:
                _write_log(lg, "Getting available new foundation versions.")
                if available_updates := self.get_pe_avaliable_updates(component="Foundation"):
                    _write_log(lg, "Got the available foundatino versions, \
                               {','.join([update['version'] for update in available_updates])}")
                    if matched_version := [update for update in available_updates
                                           if update['version'] == self.benchmark['lcm_version']['foundation_version']]:
                        _write_log(lg, "Cool, we got one perfect match, performing upgrade now.")
                        res, data = self.perform_upgrade(matched_version)
                    else:
                        message = "Emmm, there is no perfect match version."
                        _write_log(lg, message, 'error')
                        raise Exception(message)
                else:
                    message = "There is no available foundation versions, task over."
                    _write_log(lg, message, 'error')
                    raise Exception(message)
                if not res:
                    message = f"Failed to perform foundation upgrade, error :{data}."
                    _write_log(lg, message, 'error')
                    raise Exception(message)
                task_uuid = data['data']['extId']
                _write_log(lg, f"Foundation upgrade started, task uuid:{task_uuid}.")
                continue_flag = False
                _pe = PrismElement(pe=pe, sa=self.sa, logger=self.logger)
                for _i in range(10):  # wait for at most 20 minutes, foundation upgrade is a quick one.
                    _write_log(lg, "Taking a 2-minutes power nap now.")
                    time.sleep(120)  # wait 2 minutes , then get the lcm status
                    _write_log(lg, "Time up, getting pe lcm task status.")
                    lcm_status = self.get_lcm_progress(logger=self.logger)
                    if lcm_status:  # if it's None, mean lcm is idle
                        _write_log(lg, "LCM is still not finished.")
                        continue
                    _write_log(lg, "LCM is not progressing with anything, let's check the task.")
                    task_status = _pe.get_task_status(task_uuid)
                    if tasks := task_status['entities']:
                        if [_subtask for _subtask in tasks if _subtask['status'] != 'succeeded']:
                            _write_log(lg, ';'.join([f"task_name:{_subtask['operation']}, \
                                                     task_status:{_subtask['status']}" for _subtask in tasks]))
                            _write_log(lg, "There is still tasks not finished, \
                                       but LCM is over?? weird, let's wait for some more time.")
                        else:
                            continue_flag = True
                            _write_log(lg, "All tasks are succeeded, good, let's proceed.")
                            break
                    else:
                        _write_log(lg, f"Empty task??? task uuid :{task_uuid}, game over now.")
                        raise Exception("Cannot get the task details, but lcm is idle, task id : {task_uuid}")
                if not continue_flag:
                    raise Exception("20 minutes has passed, foundation upgrade still not finished, \
                                    We are not allowed to continue.")
                # if we got here, meaning the foundation upgrade task was successfully done.
                self.model_pe.query.filter_by(
                    fqdn=self.pe).first().foundation_version = self.benchmark['lcm_version']['foundation_version']
                db.session.commit()
                _write_log(lg, "LCM/Foundation are ready, let's upgrade SPP!.")
            #####################################################################
            #                                                                   #
            #                           SPP Upgrade                             #
            #                                                                   #
            #####################################################################
            self.logger.title("SPP Upgrade")
            _write_log(lg, "Getting available SPP updates.")
            if avaliable_updates := self.get_pe_avaliable_updates(component='SPP', match_key='entity_class'):
                _write_log(lg, "Got the available SPP updates.")
                entity_list = list()
                _write_log(lg, "Going through and match host_list and available_list")
                _write_log(lg, "Need to generate new plan... yeah, nutanix is this funny.")
                plan = self.get_spp_upgrade_plan()
                for _p in [_plan for _plan in plan if _plan['need_upgrade']]:
                    match_flag = False
                    _write_log(lg, f"Matching available update for {_p['name']}")
                    for _upd in avaliable_updates:
                        if _upd['entity_uuid'] == _p['entity_uuid'] and _upd['version'] == _p['next_version']:
                            match_flag = True
                            entity_list.append(_upd)
                    if not match_flag:
                        message = f"There is no matched available SPP version for {_p['name']}...please manualy check."
                        _write_log(lg, message, 'error')
                        raise Exception(message)

                res, data = self.perform_upgrade(entity_list)
                if not res:
                    message = f"Failed to perform SPP upgrade, error :{data}."
                    _write_log(lg, message, 'error')
                    raise Exception(message)
                _write_log(lg, "Good, LCM is proceeding now.")
                task_uuid = data['data']['extId']
                _task = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=self.task["id"]).first()
                _task.ntx_task_id = task_uuid
                _task.status = "spp upgrading"
                db.session.commit()
                self._update_slcm_planned_pe(_task.slcm_plan_id, _task.pe, LcmTaskStatus.SPP_UPGRADING)
            else:
                _write_log(lg, "We have a plan, but we didn't find any available updates in LCM.", "error")
                _task = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=self.task["id"]).first()
                _task.ntx_task_id = task_uuid
                _task.status = "Error"
                db.session.commit()
                self._update_slcm_planned_pe(_task.slcm_plan_id, _task.pe, TaskStatus.ERROR)

        except Exception as e:
            max_length = len(str(e)) if len(str(e)) < 255 else 255
            _write_log(lg, f'{str(e)[0:max_length]}', 'error')
            _task = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=self.task["id"]).first()
            _task.status = "Error"
            db.session.commit()
            self._update_slcm_planned_pe(_task.slcm_plan_id, _task.pe, TaskStatus.ERROR)

    def pre_upgrade(self):
        #1. reset ilo
        #2. execute "configure_lcm --disable_pem", and use the command "configure_lcm -p | grep pem" to confirm that value is "False".
        #3. allssh "genesis restart;genesis stop foundation"

        _pe = PrismElement(pe=self.pe, sa=self.sa, logger=self.logger)

        #1
        self.logger.info("reset all ilo.")
        if _pe.reset_ilo_via_hostssh(facility_type=self.facility_type):
            self.logger.info("take a 2 minutes power nap.")
        else:
            raise "Resetting ilo failed."
        time.sleep(120)

        #2
        self.logger.info("disable_pem")
        if not _pe.disable_lcm_pem(facility_type=self.facility_type):
            raise "Pem is not disabled, please manually disable it first."

        #3
        self.logger.info("restart genesis, stop foundation")
        _pe.stop_foundation_via_allssh(facility_type=self.facility_type)


    def perform_pe_inventory(self):
        if not self.pe or not self.pc:
            self.logger.error("PC or PE not spicified.")
            return
        self.logger.info("Getting pc information from db.")
        if pc := self.model_pc.query.filter_by(fqdn=self.pc).first():
            self.logger.info("Got the pc.")
            if darksite := pc.darksite:
                self.logger.info(f"Got the darksite: {darksite}.")
            else:
                self.logger.error("Darksite is empty...")
                return
        else:
            self.logger.error(f"Cannot find the PC:{self.pc} in the DB....")
            return
        self.logger.info(f"Launching inventory towards {self.pe}.")
        value = {".oid": "LifeCycleManager",
                 ".method": "lcm_framework_rpc",
                 ".kwargs": {
                     "method_class": "LcmFramework",
                     "method": "perform_inventory",
                     "args": [
                         f"http://{darksite}/release"
                     ]}}
        payload = {
            "value": json.dumps(value)
        }
        _ntx_api = NutanixAPI(pe=self.pe, username=self.sa['username'],
                              password=self.sa['password'], logger=self.logger)
        res, message = _ntx_api.call_pe_post(request_url="/genesis", payload=payload)
        if res:
            return json.loads(message['value'])['.return']
        raise Exception("Failed to perform inventory.")

    def get_pe_lcm_info(self):
        value = {".oid": "LifeCycleManager",
                 ".method": "lcm_framework_rpc",
                 ".kwargs": {
                     "method_class": "LcmFramework",
                     "method": "get_config"}}
        payload = {
            "value": json.dumps(value)
        }
        _ntx_api = NutanixAPI(pe=self.pe, username=self.sa['username'],
                              password=self.sa['password'], logger=self.logger)
        res, message = _ntx_api.call_pe_post(request_url="/genesis", payload=payload)
        if res:
            return json.loads(message['value'])
        raise Exception(f"Failed to get PE LCM version info. Error Message : {message}")

    def get_pe_avaliable_updates(self, component='all', match_key='entity_model', tried_fix=False):
        payload = {"entity_type": "lcm_available_version_v3",
                   "group_member_count": 500,
                   "query_name": "lcm:EntityGroupModel",
                   "filter_criteria": "_master_cluster_uuid_==[no_val]",
                   "group_member_attributes": [
                       {"attribute": "uuid"},
                       {"attribute": "entity_uuid"},
                       {"attribute": "entity_class"},
                       {"attribute": "status"},
                       {"attribute": "version"},
                       {"attribute": "entity_model"}]}
        _ntx_api = NutanixAPI(pe=self.pe, username=self.sa['username'],
                              password=self.sa['password'], logger=self.logger)
        res, status = _ntx_api.call_pe_v3_post(request_url="/groups", payload=payload)
        self.logger.info(f"Status is {res}, result is {status}")
        if res:
            result = list()
            if not status['group_results']:
                return None
            for _comp in status['group_results'][0]['entity_results']:
                entity = {
                    "entity_id": _comp['entity_id']
                }
                for _data in _comp['data']:
                    if not _data['values']:
                        entity[_data['name']] = "N/A"
                    else:
                        entity[_data['name']] = ",".join(_data['values'][0]['values'])
                result.append(entity)
            if component == 'all':
                return result
            
            res = [entity for entity in result if entity[match_key] == component]
            return res if res else None
        if not tried_fix:
            self.logger.warning("Ooooops, failed to get available updates, let's try to renew \
                                the authentication with AD, and see if it will fix it.")
            atm = Automation(pc=self.pc, pe=self.pe, logger=self.logger)
            res, _mes = atm.reauth_pe_ad()
            if not res:
                raise Exception(f"Failed to re-auth AD for {self.pe}")
            self.logger.info("Good, re-auth works, let's see if we can get the updates this time.")
            return self.get_pe_avaliable_updates(component = component, match_key = match_key, tried_fix=True)
        raise Exception("Failed to get available updates even after we tried re-auth, need manual fix.")

    def handle_lcm_status(self, lg, lcm_status, _task):
        if isinstance(lcm_status, str) and "LCM Framework is starting up" in lcm_status:
            max_wait_times = 3
            wait_count = 0
            self._write_log(lg, "LCM Framework is starting up. Will wait up to 15 minutes...")
            while wait_count < max_wait_times:
                time.sleep(300)
                wait_count += 1
                self._write_log(lg, f"Check {wait_count}/3: Waiting for LCM Framework to start...")
                lcm_status = self.get_lcm_progress(logger=self.logger)
                if not isinstance(lcm_status, str) or "LCM Framework is starting up" not in lcm_status:
                    self._write_log(lg, "LCM Framework is started, proceeding with other operations.")
                    break
            if isinstance(lcm_status, str) and "LCM Framework is starting up" in lcm_status:
                self._write_log(lg, "LCM Framework is still starting up after 15 minutes. Please check the cluster manually and retry the task after LCM is ready.", "warning")
                raise Exception("LCM Framework startup timeout after 15 minutes")

        # Autoupdate Start
        if lcm_status == "Autoupdate":
            max_autoupdate_checks = 5
            check_count = 0
            while check_count < max_autoupdate_checks:
                self._write_log(lg, f"LCM is in Autoupdate status, waiting for 2 minutes before next check (attempt {check_count + 1}/{max_autoupdate_checks})...")
                time.sleep(120)  # Wait for 2 minutes
                new_status = self.get_lcm_progress(logger=self.logger)
                if new_status != "Autoupdate":
                    self._write_log(lg, f"LCM status changed from Autoupdate to {new_status}, proceeding...")
                    lcm_status = new_status
                    break
                check_count += 1
            if check_count >= max_autoupdate_checks:
                self._write_log(lg, "LCM stayed in Autoupdate status for too long (10 minutes), aborting the process.")
                raise Exception("LCM stayed in Autoupdate status for too long")
            
        # Inventory Start
        if lcm_status == "Inventory":
            max_wait_seconds = 3600
            wait_interval = 300
            waited = 0
            self._write_log(lg, "LCM is running Inventory. Waiting for it to finish...")
            while lcm_status == "Inventory" and waited < max_wait_seconds:
                time.sleep(wait_interval)
                waited += wait_interval
                lcm_status = self.get_lcm_progress(logger=self.logger)
                self._write_log(lg, f"Checked LCM status after {waited} seconds: {lcm_status}")
            if lcm_status == "Inventory":
                self._write_log(lg, "LCM Inventory has been running for over 60 minutes.")
                raise Exception("LCM Inventory timeout: still running after 60 minutes.")
        # Upgrade
        elif lcm_status and lcm_status != "Inventory":
            self._write_log(lg, "There is another LCM in progress, task over.")
            _task.status = "spp upgrading"
            db.session.commit()
            self._update_slcm_planned_pe(_task.slcm_plan_id, _task.pe, LcmTaskStatus.SPP_UPGRADING)
            return False
        return lcm_status

    def get_lcm_progress(self, logger=logging, retry=3):
        payload = {
            'value': '{".oid":"LifeCycleManager",".method":"lcm_framework_rpc", \
            ".kwargs":{"method_class":"LcmFramework","method":"is_lcm_operation_in_progress"}}'
        }
        _ntx_api = NutanixAPI(pe=self.pe, username=self.sa['username'],
                              password=self.sa['password'], logger=self.logger)
        res, message = _ntx_api.call_pe_post(request_url="/genesis", payload=payload)
        if res:
            try:
                value = json.loads(message['value'])
                status = value.get('.return') or value.get('.error')
            except Exception:
                logger.warning(f"The return value of this API is {json.loads(message['value'])}, weird, sleep 1 min and then retry.")
                if retry != 0:
                    time.sleep(60)
                    status = self.get_lcm_progress(logger=logger, retry=retry-1)
            return status
        raise Exception("Failed to get the lcm progress.")

    def get_lcm_component_version(self, component='all', match_key='entity_model'):
        payload = {
            'value': '{".oid":"LifeCycleManager",".method":"lcm_framework_rpc", \
                ".kwargs":{"method_class":"LcmFramework","method":"v3_group_api", \
                    "args":["{\\\"entity_type\\\":\\\"lcm_entity_v3\\\", \
                        \\\"group_member_count\\\":500,\\\"group_member_attributes\\\": \
                        [{\\\"attribute\\\":\\\"id\\\"},{\\\"attribute\\\":\\\"entity_model\\\"}, \
                        {\\\"attribute\\\":\\\"version\\\"}],\\\"query_name\\\":\\\"lcm:EntityGroupModel\\\", \
                            \\\"filter_criteria\\\":\\\"entity_type==software\\\"}\"]}}'
        }
        _ntx_api = NutanixAPI(pe=self.pe, username=self.sa['username'],
                              password=self.sa['password'], logger=self.logger)
        res, message = _ntx_api.call_pe_post(request_url="/genesis", payload=payload)
        if res:
            status = json.loads(message['value'])['.return']
            result = list()
            for _comp in status['group_results'][0]['entity_results']:
                entity = {
                    "entity_id": _comp['entity_id']
                }
                for _data in _comp['data']:
                    if not _data['values']:
                        entity[_data['name']] = "N/A"
                    else:
                        entity[_data['name']] = ",".join(_data['values'][0]['values'])
                result.append(entity)
            if component == 'all':
                return result
            res = [entity for entity in result if entity[match_key] == component]
            if res:
                return res
            return None
        raise Exception("Failed to get the lcm component version.")

    def get_firmware_version(self, component='all', match_key='entity_model'):
        payload = {
            'value': '{".oid":"LifeCycleManager",".method":"lcm_framework_rpc", \
                ".kwargs":{"method_class":"LcmFramework","method":"v3_group_api", \
                    "args":["{\\\"entity_type\\\":\\\"lcm_entity_v2\\\",\\\"group_member_count\\\":500, \
                        \\\"group_member_attributes\\\":[{\\\"attribute\\\":\\\"uuid\\\"}, \
                        {\\\"attribute\\\":\\\"entity_model\\\"}, \
                        {\\\"attribute\\\":\\\"version\\\"}, \
                        {\\\"attribute\\\":\\\"location_id\\\"}], \
                        \\\"query_name\\\":\\\"lcm:EntityGroupModel\\\", \
                        \\\"filter_criteria\\\":\\\"entity_type==firmware\\\"}\"]}}'
        }
        _ntx_api = NutanixAPI(pe=self.pe, username=self.sa['username'],
                              password=self.sa['password'], logger=self.logger)
        res, message = _ntx_api.call_pe_post(request_url="/genesis", payload=payload)
        if res:
            status = json.loads(message['value'])['.return']
            result = list()
            for _comp in status['group_results'][0]['entity_results']:
                entity = {
                    "entity_id": _comp['entity_id']
                }
                for _data in _comp['data']:
                    if not _data['values']:
                        entity[_data['name']] = "N/A"
                    else:
                        entity[_data['name']] = ",".join(_data['values'][0]['values'])
                result.append(entity)
            if component == 'all':
                return result
            res = [entity for entity in result if entity[match_key] == component]
            return res if res else None
        raise Exception("Failed to get the firmware version.")

    def perform_upgrade(self, entities):
        payload = {
            "$reserved": {
                "$fqObjectType": "lcm.v4.r0.a1.common.EntityUpdateSpecs"
            },
            "entityUpdateSpecs": [
                {
                    "version": entity['version'],
                    "entityUuid": entity['entity_uuid'],
                    "$reserved": {
                        "$fqObjectType": "lcm.v4.r0.a1.common.EntityUpdateSpec"
                    },
                    "$objectType": "lcm.v4.common.EntityUpdateSpecs"
                }
                for entity in entities
            ]
        }
        _ntx_api = NutanixAPI(pe=self.pe, username=self.sa['username'],
                              password=self.sa['password'], logger=self.logger)
        res, message = _ntx_api.call_pe_v4_post(request_url="/operations/$actions/performUpdate", payload=payload)
        return res, message

    def get_spp_upgrade_plan(self):
        self.logger.info("Making SPP upgrade plan.")
        _pe = PrismElement(pe=self.pe, sa=self.sa, logger=self.logger)
        self.logger.info("Getting current firmware version.")
        self.p_e = Prism_Element(fqdn=self.pe, sa=self.sa, logger=self.logger)
        spp_verion = self.p_e.get_lcm_inventory(filter_key="entity_type", filter_value="firmware")
        # spp_verion = self.get_firmware_version()
        self.logger.info("Getting host list of PE.")
        _res, pe_hosts = _pe.get_host_list(details=True)
        if len(pe_hosts) != len(spp_verion):
            raise Exception(f"We detected {len(pe_hosts)} host, \
                            but {len(spp_verion)} SPP info, something must be wrong, task over...")
        if len(pe_hosts) == 1:
            raise Exception("Not allowed to perform this on a single-node cluster, task over...")
        result = list()
        for _host in pe_hosts:
            self.logger.info(f"Dealing with {_host['name']} .")
            _res = dict()
            _res['name'], _res['host_uuid'] = _host['name'], _host['detail']['uuid']
            _host_spp = [_version for _version in spp_verion
                         if _host['detail']['uuid'] == _version['location_id'].split(":")[1]]
            # _version is like {'entity_id': 'd30f08f2-39f2-4ef4-a03a-5e0cb3875baf',
            # 'uuid': 'd30f08f2-39f2-4ef4-a03a-5e0cb3875baf', 'entity_model': 'DX360 GEN10 PLUS',
            # 'version': '202 04.0', 'location_id': 'node:cca0c92b-4a06-4fbf-8637-3d1da842994f'}
            if not _host_spp:
                self.logger.info(f'There is no SPP info matches this host, do we have a ghost host??? Hostname:{_host["name"]}, Host ip:{_host["ip"]}')
                _res['status'] = False
                _res['description'] = "There is no SPP info matches this host"
                _res['need_upgrade'] = False

                continue
            if version.parse(_host_spp[0]['version']) >= version.parse(self.spp_lowest_version):
                _res['spp_version'], _res['need_upgrade'] = _host_spp[0]['version'], False
                self.logger.info("Good, this host is already above the bearable SPP version, skip this one.")
            else:
                self.logger.info("OK, this host needs SPP upgrades, let's check which version it should go to.")
                if version.parse(_host_spp[0]['version']) < version.parse(self.spp_base_version):
                    self.logger.info(f"The SPP of this host is lower then base version {self.spp_base_version},  it needs to be upgrade to {self.spp_step_version} first.")
                    _res['spp_version'] = _host_spp[0]['version']
                    _res['need_upgrade'] = True
                    _res['next_version'] = self.spp_step_version
                    _res['entity_uuid'] = _host_spp[0]['entity_id']
                else:
                    self.logger.info(f"The SPP of this host is greater then base version {self.spp_base_version}, we can directly go to the target version.")
                    _res['spp_version'] = _host_spp[0]['version']
                    _res['need_upgrade'] = True
                    _res['next_version'] = self.target_spp_version
                    _res['entity_uuid'] = _host_spp[0]['entity_id']
            result.append(_res)
        return result

    def update_lcm_version_info_to_pe(self):
        firm = self.p_e.get_lcm_inventory(filter_key="entity_type", filter_value="firmware")
        firm_version = ','.join(set([_firm['version'] for _firm in firm]))
        lcm = self.get_pe_lcm_info()
        lcm_version = lcm['.return']['semantic_version']
        fnd = self.get_lcm_component_version(component="Foundation")
        fnd_version = fnd[0]['version']
        _pe = self.model_pe.query.filter_by(fqdn=self.pe.upper()).first()
        self.logger.info(f"Updating {self.pe} LCM versions to DataBase.")
        _pe.foundation_version, _pe.lcm_version, _pe.spp_version = fnd_version, lcm_version, firm_version
        db.session.commit()

    def check_task_status(self):
        _spp_tasks = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(status='spp upgrading').all()
        logging.info(f"Detected {len(_spp_tasks)} SPP tasks, start to spawn sub process.")
        for _task in _spp_tasks:
            self.logger = ''
            p = multiprocessing.Process(target=self.check_single_task, args=[_task.id])
            p.start()
            print(f'starting {p.pid} for spp task check.')

    def check_single_task(self, task_id):
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)  # db is in models.py
        app.app_context().push()  # context definition
        if task := ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=task_id).first():
            try:
                self.init_bmk(pe=task.pe, facility_type=task.facility_type)
                self.pe = task.pe
                self.pc = task.pc
                self.task = task.__dict__
                self.target_spp_version = task.target_version
                lg = DBLogging(taskid=task.id, logtype="SPP")
                self.lg = lg
                self.logger = setup_common_logger(str(uuid.uuid4()), task.detail_log_path)
                def _write_log(lg, log_info, severity='info'):
                    # got pissed off that everytime I need to write log in 2 place...
                    if severity not in ['info', 'warning', 'error']:
                        severity = 'info'
                    lg.write_lcm_log(log_info, logseverity=severity)
                    _logger = getattr(self.logger, severity)
                    _logger(log_info)
                _pe = PrismElement(pe=self.pe, sa=self.sa, logger=self.logger)
                _write_log(lg, "Checking SPP upgrade status.")
                task_status = _pe.get_task_status(task.ntx_task_id)
                repair_flag = False #determine whether we need to repair, depends on the task status
                if tasks := task_status['entities']:
                    _write_log(lg, '  ;  '.join([f"task_name:{_subtask['operation']}, \
                            task_status:{_subtask['status']}" for _subtask in tasks]))
                    #print task details including sub-task to log
                    if [_subtask for _subtask in tasks if re.match("failed|aborted", _subtask['status'], re.I)]:
                        # if there is at least 1 failed/aborted, then we need to repair
                        _write_log(lg, "There is at least 1 failed/aborted task, let's try to repair pe.")
                        repair_flag = True
                        # if self.repair_spp():
                        #     _write_log(lg, f"Good, all nodes are in healthy state, let's try to upgrade spp again now.")
                        # else:
                        #     raise("Failed to repair. please manually do it and launch again.")
                        #try to repair
                    elif [_subtask for _subtask in tasks if (not re.match("succeeded", _subtask['status'], re.I))]:
                        # if there is no failed/aborted, and all matched succeeded, then we go ahead
                        _write_log(lg, "There is still tasks running, let's wait for some more time.")
                        return
                    
                    else:
                        # there is at least 1 task that is not succeeded/failed/aborted, meaning it's running, let's wait.
                        _write_log(lg, "All tasks are succeeded, good, let's proceed.")
                else:
                    _write_log(lg, f"Empty task??? task uuid :{task.ntx_task_id}, game over now.")
                    raise Exception(f"Cannot get the task details, task id : {task.ntx_task_id}")
                if repair_flag:
                    if self.repair_spp(task.id):
                        _write_log(lg, "Good, pe has been repaired, let's continue with the upgrade.")
                        self.change_task_status(task_id=task_id, status="spp upgrading")
                        self._update_slcm_planned_pe(task.slcm_plan_id, task.pe, LcmTaskStatus.SPP_UPGRADING)
                    else:
                        raise Exception("Failed to repair pe, need to manual check, sorry.")

                plan = self.get_spp_upgrade_plan()
                _write_log(lg, f"We detected {len(plan)} hosts, \
                           {len([_plan for _plan in plan if _plan['need_upgrade']])} need to be upgrade.")
                if not len([_plan for _plan in plan if _plan['need_upgrade']]):
                    # if all nodes are upgraded to the target version
                    lg.write_lcm_log("Perfect, all nodes meet the spp version, task successfully done!")
                    # update all the versions...
                    self.update_lcm_version_info_to_pe()
                    _task = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=task_id).first()
                    _task.status = "Done"
                    db.session.commit()
                    self._update_slcm_planned_pe(_task.slcm_plan_id, _task.pe, TaskStatus.DONE)
                    return
                # if not all nodes are upgraded to the target version
                _write_log(lg, "Perform inventory first.")
                _write_log(lg, "Getting PE lcm status.")
                lcm_status = self.get_lcm_progress()
                _write_log(lg, f"LCM progress status is '{lcm_status}' .")
                if lcm_status and lcm_status != "Inventory":
                    _write_log(lg, "There is another LCM in progress, we will come back later.")
                    return
                _write_log(lg, "LCM is not progressing with anything, let's perform inventory now.")
                if version.parse(self.cluster_info.aos_version) >= version.parse("6.8"):
                    task_uuid = self.p_e.perform_inventory_v4()
                else:
                    task_uuid = self.perform_pe_inventory()
                _write_log(lg, f"Inventory has been launched, task uuid :{task_uuid}.")
                for _i in range(30 + (30 if lcm_status == "Inventory" else 0)):
                    # if lcm is already doing one invenroty, then we wait for more time
                    _write_log(lg, "Taking a 2-minutes power nap now.")
                    time.sleep(120)  # wait 2 minutes , then get the lcm status
                    _write_log(lg, "Time up, getting pe lcm task status.")
                    lcm_status = self.get_lcm_progress(logger=self.logger)
                    if lcm_status:  # if it's None, mean lcm is idle
                        _write_log(lg, "LCM is still not finished.")
                        continue
                    _write_log(lg, "LCM is not progressing with anything, let's check the task.")
                    task_status = _pe.get_task_status(task_uuid)
                    if tasks := task_status['entities']:
                        if [_subtask for _subtask in tasks if _subtask['status'] != 'succeeded']:
                            _write_log(lg, ';'.join([f"task_name:{_subtask['operation']}, \
                                                        task_status:{_subtask['status']}" for _subtask in tasks]))
                            _write_log(lg, "There is still tasks not finished, \
                                        but LCM is over?? weird, let's wait for some more time.")
                        else:
                            _write_log(lg, "All tasks are succeeded, good, let's proceed.")
                            break
                    else:
                        _write_log(lg, f"Empty task??? task uuid :{task_uuid}, game over now.")
                        raise Exception("Cannot get the task details, but lcm is idle, task id : {task_uuid}")
                if [_subtask for _subtask in tasks if _subtask['status'] != 'succeeded']:
                    raise Exception("There is still tasks not finished, \
                                        but we've been waiting for quite a while, task over.")
                entity_list = list()
                _write_log(lg, "Getting available updates.")
                avaliable_updates = self.get_pe_avaliable_updates(component='SPP', match_key='entity_class')
                if not avaliable_updates:
                    raise Exception("No available updates found, please check darksite files.")
                for _p in [_plan for _plan in plan if _plan['need_upgrade']]:
                    match_flag = False
                    _write_log(lg, f"Matching available update for {_p['name']}")
                    for _upd in avaliable_updates:
                        if _upd['entity_uuid'] == _p['entity_uuid'] and _upd['version'] == _p['next_version']:
                            match_flag = True
                            entity_list.append(_upd)
                    if not match_flag:
                        message = f"There is no matched available SPP version for {_p['name']}...please manualy check."
                        _write_log(lg, message, 'error')
                        raise Exception(message)
                res, data = self.perform_upgrade(entity_list)
                if not res:
                    message = f"Failed to perform SPP upgrade, error :{data}."
                    _write_log(lg, message, 'error')
                    raise Exception(message)
                
                task_uuid = data['data']['extId']
                _task = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=task_id).first()
                _task.ntx_task_id = task_uuid
                _task.status = "spp upgrading"
                db.session.commit()
                self._update_slcm_planned_pe(_task.slcm_plan_id, _task.pe, LcmTaskStatus.SPP_UPGRADING)
                try:
                    _write_log(lg, str(self.task))
                    _write_log(lg, str(self.task["id"]))
                except Exception as e:
                    _write_log(lg, str(e))
            except Exception as e:
                _task = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=task_id).first()
                lg = DBLogging(taskid=task.id, logtype="SPP")
                lg.write_lcm_log(logseverity='error', loginfo=str(e))
                self.logger.error(str(e))
                _task.status = "Error"
                db.session.commit()
                self._update_slcm_planned_pe(_task.slcm_plan_id, _task.pe, TaskStatus.ERROR)

    def repair_spp(self, task_id):
        self.change_task_status(task_id=task_id, status="Repairing")
        self._update_slcm_planned_pe(self.slcm_plan_id, self.pe, LcmTaskStatus.REPAIRING)
        self._write_log(self.lg, "Start to repair.")
        self._write_log(self.lg, "Checking if there is cvm/ahv offline.")
        _pe = PrismElement(pe=self.pe, sa=self.sa)
        res, host_list = _pe.get_host_list()
        print(host_list)
        if not res:
            self._write_log(self.lg, log_info="Failed to get host list", severity="error")
            return False
        _vault = Vault(tier=self.benchmark['tier'])
        res, _pe_nutanix = _vault.get_secret(f"{_pe.pe.upper()}/Site_Pe_Nutanix")
        res, _pe_ilo = _vault.get_secret(f"{_pe.pe.upper()}/Site_Oob")
        if not res:
            self._write_log(self.lg, log_info="Failed to get nutanix account and password.", severity="error")
        phoenix_nodes, cvm_nodes, oob_nodes, ahv_nodes = [], [], [], []
        for _host in host_list:
            _ssh = SSHConnect(host=_host['cvm_ip'], username=_pe_nutanix['username'], password=_pe_nutanix['secret'])
            if _ssh.connect()[0]:
                self.logger.info(f"This cvm {_host['cvm_ip']} is online.")
                cvm_nodes.append(_host['cvm_ip'])
            else:
                self.logger.info(f"This cvm {_host['cvm_ip']} is not online, let check if we can use phoenix password to login.")
                phoenix_nodes.append(_host['cvm_ip'])
                oob_nodes.append(_host['oob_ip'])
                ahv_nodes.append(_host['ip'])
            _ssh.ssh.close()
        # let filter out some bad situation first
        if len(host_list) == len(phoenix_nodes):
            self._write_log(self.lg, log_info="All cvms are not accessable by 'nutanix' account? Either password is not correct or we are fugged.", severity="error")
            return False
        if len(phoenix_nodes) > 1:
            self._write_log(self.lg, log_info="More than 1 cvm is not accessable by 'nutanix' account, this is not something our automation can handle, sorry.", severity="error")
            return False
        # OK let's deal with it now
        if phoenix_nodes and len(phoenix_nodes) == 1:
            # self._write_log(self.lg, log_info="Looks like we got 1 cvm not online, let's see if it's in phoenix.", severity="info")
            self.exit_phoenix_mode(phoenix_nodes[0], oob_nodes[0], _pe_nutanix, _pe_ilo)
            self._write_log(self.lg, log_info="Host in normal mode, start exit maintenance")
            self.exit_maintenance_mode(ahv_nodes[0])
            return True
        return True

    def exit_phoenix_mode(self, _host, oob_ip,  _pe_nutanix, _pe_ilo):
        count = 0
        while count < 4:
            mode = self.check_host_mode(_host, _pe_nutanix,)
            if not mode:
                self.force_reboot_host(oob_ip, _pe_ilo)
                mode = self.check_host_mode(_host, _pe_nutanix,)
            if mode == "normal":
                return
            if mode == "phoenix":
                _ssh = SSHConnect(host=_host, username= 'root', password= "nutanix/4u")
                self._write_log(self.lg, log_info="good, it's respongding to phonnix password, try use command reboot host to AHV from phoenix.")
                _res, ssh = _ssh.connect()
                try:
                    self._write_log(self.lg, log_info="Trying command: python /phoenix/reboot_to_host.py")
                    ssh.exec_command("python /phoenix/reboot_to_host.py")
                    # Wait some time to see if reboot succeeded
                    time.sleep(30)
                    # Check if still connectable, if not, reboot might have started
                    if not self.check_host_mode(_host, _pe_nutanix) == "phoenix":
                        self._write_log(self.lg, log_info="Command appears to have worked, host no longer in phoenix mode")
                    else:
                        # Host still in phoenix mode, try alternative command
                        self._write_log(self.lg, log_info="Host still in phoenix mode, trying alternative command")
                        try:
                            ssh = SSHConnect(host=_host, username= 'root', password= "nutanix/4u")
                            _res, ssh = ssh.connect()
                            if _res:
                                ssh.exec_command("sh /phoenix/reboot_to_host.sh")
                                self._write_log(self.lg, log_info="Executed alternative reboot command")
                        except Exception as e2:
                            self._write_log(self.lg, log_info=f"Alternative command failed: {str(e2)}", severity="warning")
                except Exception as e:
                    self._write_log(self.lg, log_info=f"Exception with first command: {str(e)}, trying alternative", severity="warning")
                    # Try alternative command
                    try:
                        ssh = SSHConnect(host=_host, username= 'root', password= "nutanix/4u")
                        _res, ssh = ssh.connect()
                        if _res:
                            ssh.exec_command("sh /phoenix/reboot_to_host.sh")
                            self._write_log(self.lg, log_info="Executed alternative reboot command")
                    except Exception as e2:
                        self._write_log(self.lg, log_info=f"Both commands failed: {str(e2)}", severity="warning")
                
                self._write_log(self.lg, log_info="let's wait server reboot.")
                reboot_result = self.is_phoenix_reboot_succeeded(_host, _pe_nutanix, oob_ip = oob_ip, _pe_ilo = _pe_ilo)
                if reboot_result:
                    return
                mode = self.check_host_mode(_host, _pe_nutanix,)
                if mode == "normal":
                    return
                count += 1
                ssh.close()
        raise flaskex.InternalServerError("After 3 retries, Still can not bring Host to normal mode, Please check with Admin")


    def is_phoenix_reboot_succeeded(self, _host, _pe_nutanix, oob_ip, _pe_ilo):
        count = 1
        while count < 3:
            time.sleep(100)
            self._write_log(self.lg, log_info=f"This is {count} time.")
            mode = self.check_host_mode(_host, _pe_nutanix)
            if not mode:
                self.force_reboot_host(oob_ip, _pe_ilo)
            if mode == "normal":
                return True
            count += 1
        self._write_log(self.lg, log_info="Sorry, check reboot status failed, please contact Admin", severity="error")
        return False

    def force_reboot_host(self, oob_ip, _pe_ilo):
        _ilo = Redfish(ip=oob_ip, username='administrator', password=_pe_ilo['secret'])
        self._write_log(self.lg, log_info="welcom to redfish environment, now we need some times to reboot host, please wait!")
        self.logger.info("let's wait server reboot")
        _ilo.change_power_state(state="GracefulRestart")
        self._write_log(self.lg, log_info="Server will booting,you can login to ILO and check.")
        time.sleep(300)
        
    def check_host_mode(self, _host, _pe_nutanix):
        _ssh = SSHConnect(host=_host, username= 'root', password= "nutanix/4u")
        if _ssh.connect()[0]:
            self._write_log(self.lg, log_info="Server in phoenix mode")
            _ssh.ssh.close()
            return "phoenix"
        _ssh = SSHConnect(host=_host, username=_pe_nutanix['username'], password=_pe_nutanix['secret'])
        if _ssh.connect()[0]:
            self._write_log(self.lg, log_info="Server in normal mode")
            _ssh.ssh.close()
            return "normal"
        self._write_log(self.lg, log_info="Failed to determine server mode after force reboot", severity="warning")
        self.logger.warning("Failed to determine server mode after force reboot")
        return False
    
    def exit_maintenance_mode(self, _host):
        mt = Maintenance(pe=self.pe, sa=self.sa, logger=self.logger)
        mt.Ncli_host_maintenance("exit", _host)
        mt.Acli_host_maintenance("exit", _host)
        _pe = PrismElement(pe=self.pe, sa=self.sa, logger=self.logger)
        _pe.stop_foundation_via_allssh(facility_type=self.facility_type)


    def change_task_status(self, task_id, status):
        _task = ModelRetailNutanixAutomationSPPLCMTask.query.filter_by(id=task_id).first()
        _task.status = status
        db.session.commit()

    def _write_log(self, lg, log_info, severity='info'):
        #lg is DBLogging instance
        # got pissed off that everytime I need to write log in 2 place...
        if severity not in ['info', 'warning', 'error']:
            severity = 'info'
        lg.write_lcm_log(log_info, logseverity=severity)
        _logger = getattr(self.logger, severity)
        _logger(log_info)

    @staticmethod
    def need_upgrade(current_version, target_version):
        """
        This function is used to check if the cluster needs LCM(SPP/AOS) upgrade.
        return True as long as there is one host needs to be upgraded,
        otherwise return False
        But current_version has a lot different kinds of value like:
        NA, "", Undefine, "2023.***********", "2023.***********,2023.***********", "2023.*********** 2023.***********" etc...
        """
        if not current_version:
            return True
        if current_version in ["NA", "Undefine", "Undefined"]:
            return True
        if re.search("\w\s?,\s?\w", current_version.strip()): #"2023.***********,2023.***********"
            for _version in current_version.split(","):
                if LCM.need_upgrade(_version.strip(), target_version.strip()):
                    return True
        if re.search("\w\s+\w", current_version.strip()): # "2023.*********** 2023.***********"
            for _version in current_version.strip().split():
                if LCM.need_upgrade(_version.strip(), target_version.strip()):
                    return True
        return current_version < target_version

    def _update_slcm_planned_pe(self, slcm_plan_id, pe_fqdn, status):
        """
        Update ModelNutanixSeamlessLcmPlannedPEs
        """
        if not slcm_plan_id:
            return
        self.logger.info("This task is triggered by Seamless LCM plan, updating SLCM tables...")
        try:
            planned_one = db.session.query(ModelNutanixSeamlessLcmPlannedPEs).filter_by(
                plan_id=slcm_plan_id, pe_fqdn=pe_fqdn).one()
        except NoResultFound:
            self.logger.error(f"Cannot find the planned PE {pe_fqdn} in SLCM plan {slcm_plan_id}, skipping update.")
            return
        except MultipleResultsFound:
            self.logger.error(f"Multiple planned PEs found for {pe_fqdn} in SLCM plan {slcm_plan_id}, cannot update.")
            return
        planned_one.status = status
        # planned_one.planned_date = None
        # planned_one.task_completed_at = datetime.datetime.now()
        db.session.commit()
        self.logger.info(f"Updated finished. Planned PE status updated to: {status}.")


class AosLCM(LCM):
    """
    run -> start_task -> initiate_upgrade
    monitor -> start_monitor -> monitor_upgrade
    """
    def __init__(self, pc=None, pe=None, facility_type=None, sa=None, slcm_plan_id=None):
        self.facility_type = facility_type
        self.pc = pc
        self.pe = pe
        self.sa = sa if sa else ServiceAccount(usage="nutanix_pm").get_service_account()
        self.logger = None
        self.tc = self.init_task()
        self.task = None
        self.is_task = False
        self.slcm_plan_id = slcm_plan_id

    def init_bmk(self, pe=None, facility_type=None):
        self.facility_type = facility_type if facility_type else self.facility_type
        if self.facility_type == "retail":
            self.model_pe = ModelPrismElement
            self.model_pc = ModelPrismCentral
        elif self.facility_type == "warehouse":
            self.model_pe = ModelWarehousePrismElement
            self.model_pc = ModelWarehousePrismCentral
        self.pe = pe if pe else self.pe
        cluster_info = self.model_pe.query.filter_by(fqdn=self.pe).one()
        self.benchmark = Benchmark().get_bmk_by_id(bmk_id=cluster_info.bmk_id)
        self.pe_name = cluster_info.name
        self.vault = Vault(self.benchmark['tier'], namespace = f"{self.benchmark['vault']['master_namespace']}/{self.benchmark['vault']['tier_namespace']}", usage=self.benchmark['vault']['service_account'])
        _res, data = self.vault.get_secret(f"{self.pe_name.upper()}/Site_Pe_Admin")
        self.pe_sa = {"username": data['username'],  "password": data['secret']}
        self.target_aos_version = self.benchmark['lcm_version']['aos_version']
        self.target_ahv_version = self.benchmark['lcm_version']['ahv_version']
        
    def init_task(self):
        tc = TaskCommon(
            task_model=ModelRetailNutanixAutomationAOSLCMTask,
            task_model_schema=ModelRetailNutanixAutomationAOSLCMTaskSchema,
        )
        return tc

    @with_app_context
    def run(self, param):
        # Check if there is a running task for AOS upgrading exists in database 'dh_retail_ntx_automation_aos_lcm_task', filter by PE fqdn and task stauts including 'Not Started', 'Prechecking', 'Upgrading'
        # If task exists in in 'dh_retail_ntx_automation_aos_lcm_task', raise the exception
        # Otherwise, create a new task in database 'dh_retail_ntx_automation_aos_lcm_task' and return task_id stands for 'id'
        # Then, it creates a sub process to start the task
        self.tc.check_task_existence(status_list=[TaskStatus.NOT_STARTED, TaskStatus.IN_PROGRESS, TaskStatus.PRECHECKING], pe=self.pe)
        self.init_bmk()
        param['target_aos_version'] = self.target_aos_version
        param['target_ahv_version'] = self.target_ahv_version
        self.task = self.tc.create_task_in_db(**param)
        try:
            # It raises exception when anything wrong with the sub process creating, and then it will change the task status to 'Error'
            self.tc.spawn_subprocess(target_function=self.start_task, task_id=self.task.id, init_app=False, check_process_amount=False)
        except Exception:
            error_info = str(repr(traceback.format_exception(sys.exception())))
            logging.error(error_info)
            self.task.status = TaskStatus.ERROR
            db.session.commit()
            self._update_slcm_planned_pe(self.task.slcm_plan_id, self.task.pe, TaskStatus.ERROR)
    
    def start_task(self):
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)
        app.app_context().push()
        self.init_bmk()
        if task := ModelRetailNutanixAutomationAOSLCMTask.query.filter_by(id=self.task.id).first():
            try:
                self.task = task
                self.setup_loggers()
                self.is_task = True
                self._write_log(log_info=f"In subprocess task of 'Initiate AOS/AHV Upgrading', task_id: {self.task.id}")
                self.task.status = TaskStatus.PRECHECKING
                db.session.commit()
                self._update_slcm_planned_pe(self.task.slcm_plan_id, self.task.pe, LcmTaskStatus.PRE_CHECKING)
                # receive the ntx_task_id, and task status from the sub process, when it returns DONE, the ntx_task_id is None
                self.task.ntx_task_id, self.task.status = self.initiate_upgrade()
                db.session.commit()
                self._update_slcm_planned_pe(self.task.slcm_plan_id, self.task.pe, TaskStatus.IN_PROGRESS)
            except BaseUpException as e:
                self._write_log(f"Task failed. Detail: {e}", log_severity="error")
                self.task.status = TaskStatus.ERROR
                db.session.commit()
                self._update_slcm_planned_pe(self.task.slcm_plan_id, self.task.pe, TaskStatus.ERROR)
    
    def monitor(self):
        kwargs = {
            'status': TaskStatus.IN_PROGRESS
        }
        tasks = self.tc.get_exist_tasks(**kwargs)
        for task in tasks:
            try:
                # It raises exception when anything wrong with the sub process creating, and then it will change the task status to 'Error'
                kwargs = {
                    'task_id': task.id
                }
                self.tc.spawn_subprocess(target_function=self.start_monitor, task_id=task.id, kwargs=kwargs, init_app=False, check_process_amount=False)
            except Exception:
                error_info = str(repr(traceback.format_exception(sys.exception())))
                logging.error(error_info)
                self.task.status = TaskStatus.ERROR
                db.session.commit()
                self._update_slcm_planned_pe(self.task.slcm_plan_id, self.task.pe, TaskStatus.ERROR)

    def start_monitor(self, task_id):
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
        db.init_app(app)
        app.app_context().push()
        if task := ModelRetailNutanixAutomationAOSLCMTask.query.filter_by(id=task_id).first():
            try:
                self.init_bmk(pe=task.pe, facility_type=task.facility_type)
                self.pc = task.pc
                self.pe = task.pe
                self.target_ahv_version = task.target_ahv_version
                self.target_aos_version = task.target_aos_version
                self.task = task
                self.setup_loggers(local_log_path=task.detail_log_path)
                self.is_task = True
                self._write_log(log_info=f"In subprocess task of 'Monitor AOS/AHV Upgrading', task_id: {self.task.id}")
                self._write_log(log_info="Start to do AOS/AHV Upgrade Monitoring", log_severity="title")
                task_status = self.monitor_upgrade()
                self.task.status = task_status
                db.session.commit()
                self._update_slcm_planned_pe(self.task.slcm_plan_id, self.task.pe, task_status)

                if task_status == TaskStatus.DONE:
                    self._write_log(log_info="AOS LCM Task is completed. Updating EULA...")
                    self.configure_eula()

            except Exception:
                error_info = str(repr(traceback.format_exception(sys.exception())))
                self._write_log(log_info=error_info, log_severity="error")
                self.task.status = TaskStatus.ERROR
                db.session.commit()
                self._update_slcm_planned_pe(self.task.slcm_plan_id, self.task.pe, TaskStatus.ERROR)

    def monitor_upgrade(self):
        self.p_e = Prism_Element(fqdn=self.pe, logger=self.logger, sa=self.pe_sa)

        # Check if all tasks are completed
        task = self.p_e.get_progress(uuid=self.task.ntx_task_id).json()
        if sub_tasks := task.get('entities'):
            if [sub for sub in sub_tasks if sub['status'] != 'succeeded']:
                # When the tasks are failed
                if [sub for sub in sub_tasks if sub['status'] == 'failed']:
                    self._write_log(log_info=';'.join([f"task_name:{sub['operation']}, task_status:{sub['status']}" for sub in sub_tasks]))
                    self._write_log(log_info="There are sub tasks are failed, task over...")
                    return TaskStatus.ERROR
                # When the tasks are aborted
                if [sub for sub in sub_tasks if sub['status'] == 'aborted']:
                    self._write_log(log_info=';'.join([f"task_name:{sub['operation']}, task_status:{sub['status']}" for sub in sub_tasks]))
                    self._write_log(log_info="There are sub tasks aborted, task over...")
                    return TaskStatus.ABORT
                self._write_log(log_info=';'.join([f"task_name:{sub['operation']}, task_status:{sub['status']}" for sub in sub_tasks]))
                # When tasks are still runing, confirm if time is up
                create_date = datetime.datetime.strptime(self.task.create_date, "%Y-%m-%d,%H:%M:%S")
                now_date = datetime.datetime.utcnow()
                if (abs(now_date - create_date).total_seconds()) > self.benchmark['lcm_activity']['aos_upgrade_timeout_sec']:
                    self._write_log(log_info="Time is up, task over...")
                    return TaskStatus.ERROR
                self._write_log(log_info="There are sub tasks not finished yet, Wait...")
                return TaskStatus.IN_PROGRESS
            # When all tasks are successfully finished
            self._write_log(log_info="All sub tasks succeeded, let's proceed.")
        else:
            raise flaskex.InternalServerError(f"Cannot get the task details, but lcm is idle, task id: {self.task.ntx_task_id}")
        
        # Check if all objects are upgraded to the target version, since all tasks are successfully finished
        plan = self.get_software_upgrade_plan()
        self._write_log(log_info=f"We've detected {len(plan)} objects in upgrade plan, {len([p for p in plan if p['need_upgrade']])} objects need to be upgrade.")
        # When all nodes meet the target version
        if not len([p for p in plan if p['need_upgrade']]):
            self.update_software_version_info_to_pe()
            self._write_log(log_info="Perfect, all nodes meet the target version.")
            return TaskStatus.DONE
        # When tasks are successfully finished, but not all nodes are upgraded to the target version, we post warning status
        return TaskStatus.WARN


    def setup_loggers(self, local_log_path=None):
        lg = DBLogging(logdir=SETTING.AOS_LCM_LOG_PATH, taskid=self.task.id, logtype="AOS", log_model=ModelRetailNutanixAutomationAOSLCMTaskLog)
        self.lg = lg
        if not local_log_path:
            local_log_path = create_file(filepath=SETTING.AOS_LCM_LOG_PATH, filename=f"{self.pc}_{self.pe}_{datetime.datetime.utcnow().strftime('%Y-%m-%d-%H-%M-%S')}")
            self.task.detail_log_path = local_log_path
            db.session.commit()
            self.lg.write_lcm_log(loginfo=f"Log file created on: {local_log_path}.")
        else:
            self.lg.write_lcm_log(loginfo=f"We've detected local log file: {local_log_path}")
        self.logger = setup_common_logger(str(uuid.uuid4()), local_log_path)
        
    
    def _write_log(self, log_info, log_severity=None):
        if log_severity not in ["info", "warning", "error", "title"]:
            log_severity = "info"
        if log_severity == "info":
            self.logger.info(log_info)
        if log_severity == "warning":
            self.logger.warning(log_info)
        if log_severity == "error":
            self.logger.error(log_info)
        if log_severity == "title":
            self.logger.title(log_info)
        
        if self.is_task:
            self.lg.write_lcm_log(loginfo=log_info, logseverity=log_severity)


    def compare_ahv_versions(self, current_version, target_version):
        """
        Compare AHV version numbers, handling both old and new format versions.
        
        Args:
            current_version (str): Current AHV version
            target_version (str): Target AHV version
        
        Returns:
            bool: True if upgrade is needed, False otherwise
        """
        # Extract the numeric parts
        current_digits = self.get_ahv_version_digits(current_version)
        target_digits = self.get_ahv_version_digits(target_version)
        
        # Check if we're comparing mixed formats (new vs old)
        # New format: 10.0, 10.0.1, 10.0.2.1 (first part is usually 1-2 digits)
        # Old format: 20230302.102001 (first part is 8 digits, date-based YYYYMMDD)
        current_is_new_format = bool(re.match(r"^\d{1,2}\.\d+(\.\d+)*$", current_digits))
        target_is_new_format = bool(re.match(r"^\d{1,2}\.\d+(\.\d+)*$", target_digits))
        
        # If one is new format and one is old format, new format is always newer
        if current_is_new_format and not target_is_new_format:
            return False  # No upgrade needed
        if not current_is_new_format and target_is_new_format:
            return True   # Upgrade needed
        # Compare versions of the same format
        return version.parse(current_digits) < version.parse(target_digits)


    def get_ahv_version_digits(self, ahv_version):
        """
        Extract the numeric version from the AHV version string.
        For example:
        - 'el8.nutanix.20230302.102001' -> '20230302.102001'
        - '10.0.2.1' -> '10.0.2.1'
        """
        try:
            # Check if it's a new format version number (like "10.0", "10.0.1", "10.0.2.1")
            if re.match(r"^\d+\.\d+(\.\d+)*$", ahv_version):
                return ahv_version
            # Process old format version numbers (like "2023.***********" or "el8.nutanix.20230302.102001")
            parts = [part for part in ahv_version.split(".") if part.isdigit() or part.replace(".", "").isdigit()]
            return ".".join(parts)
        except Exception as e:
            self.logger.error(f"Failed to parse AHV version: {ahv_version}. Error: {str(e)}")
            raise ValueError(f"Invalid AHV version format: {ahv_version}")

    def get_darksite_url(self):
        if not self.pc or not self.pe:
            raise flaskex.InternalServerError("Failed to get dark site due to PC or PE is empty, quit!")
        self._write_log(log_info="Getting pc information from db.")
        if pc := self.model_pc.query.filter_by(fqdn=self.pc).first():
            self._write_log(log_info="Got the PC.")
            if darksite := pc.darksite:
                self._write_log(log_info=f"Got the dark site: {darksite}.")
            else:
                raise flaskex.InternalServerError("Dark site is empty in database, quit!")
            self._write_log(f"We've got the dark site, start to assemble the dark site url for {self.pe}")
            systems_data = self.benchmark["systems"]["dark_site_json"]
            darksite_url = f"http://{json.loads(systems_data)['endpoint']}/release"
            if darksite_url := SETTING.DARK_SITE["URL"]:
                return darksite_url.replace("darksite", darksite)
            raise flaskex.InternalServerError("The dark site URL is missing in the SETTING, quit!")
        raise flaskex.InternalServerError("Cannot find the PC in database, quit!")

    def validate_lcm_version(self):
        self._write_log(log_info=f"Getting LCM config from {self.pe}.")
        lcm_config = self.p_e.get_lcm_config()
        if lcm_version := lcm_config['semantic_version']:
            if version.parse(lcm_version) >= version.parse(self.benchmark['lcm_version']['lcm_version']):
                self._write_log(log_info=f"LCM version: {lcm_version} is good.")
                return True
            self._write_log(log_info=f"LCM version: {lcm_version} is lower than required.")
            return False
        raise flaskex.InternalServerError(f"Cannot get LCM version from {self.pe}, quit!")
    
    def update_software_version_info_to_pe(self):
        lcm_softwares = self.p_e.get_lcm_software()
        ahv_hypervisor = [software for software in lcm_softwares if software['entity_model'] == "AHV hypervisor"][0]
        ahv_version = "Nutanix " + self.get_ahv_version_digits(ahv_hypervisor['version'])
        aos = [software for software in lcm_softwares if software['entity_model'] == "AOS"][0]
        aov_version = aos['version']
        _pe = self.model_pe.query.filter_by(fqdn=self.pe).first()
        self.logger.info(f"Updating {self.pe} software versions.")
        _pe.ahv_version, _pe.aos_version = ahv_version, aov_version
        db.session.commit()
    
    
    def initiate_upgrade(self):
        self._write_log(log_info="Pre-check for AOS/AHV upgrade", log_severity="title")
        self._write_log(log_info=f"Task status is {self.task.status} in current")
        self.p_e = Prism_Element(fqdn=self.pe, logger=self.logger, sa=self.pe_sa)
        # Confirm if it's a good time to start
        if not self.if_good_timing():
            raise LCMTimeIsNotGood()
        # Check if it does not reach the task number limit
        kwargs = {
            'status': TaskStatus.IN_PROGRESS
        }
        tasks = self.tc.get_exist_tasks(**kwargs)
        if len(tasks) >= self.benchmark['lcm_activity']['aos_upgrade_tasks_limit']:
            raise flaskex.InternalServerError(f"The task amount reach the limition of {self.benchmark['lcm_activity']['aos_upgrade_tasks_limit']}, abort.")

        # Get upgrade plan
        plan = self.get_software_upgrade_plan()
        self._write_log(log_info=f"We've detected {len(plan)} objects in upgrade plan, {len([p for p in plan if p['need_upgrade']])} objects need to be upgrade.")
        if not len([p for p in plan if p['need_upgrade']]):
            self.update_software_version_info_to_pe()
            self._write_log(log_info="Dude, all softwares are good, why did you start this task, too early to drink !!")
            return "", TaskStatus.DONE
        
        # Validate LCM version
        is_lcm_version_good = self.validate_lcm_version()
        if not is_lcm_version_good:
            self._write_log("We will check again after performing inventory.")
        
        self._write_log(log_info="Start to do LCM inventory", log_severity="title")
        # Validate LCM progress and perform inventory
        self._write_log(log_info=f"Getting LCM status for {self.pe}.")
        # lcm_status: 'Inventory' when LCM is performing inventory
        lcm_status = self.p_e.get_lcm_progress(max_try=1)
        if not lcm_status:
            self._write_log(log_info="LCM is not progressing with anything, let's perform inventory now.")
            systems_data = self.benchmark["systems"]["dark_site_json"]
            dark_site_url = f"http://{json.loads(systems_data)['endpoint']}/release"
            task_uuid = self.p_e.perform_inventory(darksite_url=dark_site_url)
            # task_uuid = self.p_e.perform_inventory_v4() # TODO: change to v4 after lcm upgrade to LCM3.X
            self._write_log(log_info=f"Inventory has been launched, task uuid :{task_uuid}.")
        elif lcm_status != "Inventory":
            raise flaskex.InternalServerError(f"There is another LCM activity alive, it is {lcm_status}, abort.")
        else:
            self._write_log(log_info="LCM is perform inventory")
            lcm_task = self.p_e.get_task(filter="(operation_type==kLcmRootTask;status==kRunning)")
            if len(lcm_task) != 1:
                raise flaskex.InternalServerError("Multi LCM inventory tasks created, weired, abort.")
            lcm_task = lcm_task[0]
            task_uuid = lcm_task['uuid']
            self._write_log(log_info=f"We've got running Inventory task, task uuid :{task_uuid}.")
        
        # Monitoring status of inventory task
        continue_flag = False
        for _i in range(10 + (10 if lcm_status == "Inventory" else 0)):
            # if lcm is already doing one invenroty, then we wait for more time
            self._write_log(log_info="Taking a 2-minutes power nap now.")
            time.sleep(120)  # wait 2 minutes , then get the lcm status
            self._write_log(log_info="Time up, getting pe lcm task status.")
            lcm_status = self.p_e.get_lcm_progress(max_try=1)
            if lcm_status:  # if it's None, mean lcm is idle
                self._write_log(log_info="LCM is still not finished.")
                continue
            self._write_log(log_info="LCM is not progressing with anything, let's check the task.")
            task = self.p_e.get_progress(uuid=task_uuid).json()
            if sub_tasks := task.get('entities'):
                if [sub for sub in sub_tasks if sub['status'] != 'succeeded']:
                    self._write_log(log_info=';'.join([f"task_name:{sub['operation']}, task_status:{sub['status']}" for sub in sub_tasks]))
                    self._write_log(log_info="There are sub tasks not finished yet, but LCM is over?? Wait...")
                else:
                    continue_flag = True
                    self._write_log(log_info="All sub tasks for performing inventory are succeeded, let's proceed.")
                    break
            else:
                raise flaskex.InternalServerError(f"Cannot get the task details, but lcm is idle, task id: {task_uuid}")
        if not continue_flag:
            raise flaskex.InternalServerError(f"{40 if lcm_status == 'Inventory' else 20} minutes passed, LCM still having issue, abort.")
        
        if not is_lcm_version_good:  # check lcm version again after inventory
            self._write_log(log_info="We need to re-check LCM version if it's good.")
            if is_lcm_version_good := self.validate_lcm_version():
                self._write_log(log_info="Performing inventory worked, let's proceed.")
            else:
                raise flaskex.InternalServerError("LCM version is not good for proceed.")
        
        # Getting available updates
        self._write_log(log_info="Getting available AOS/AHV updates.")
        if lcm_updates := self.p_e.get_lcm_updates():
            ahv_updates = [update for update in lcm_updates if update.get("entity_model") == "AHV hypervisor"]
            aos_updates = [update for update in lcm_updates if update.get("entity_model") == "AOS"]
        else:
            raise flaskex.InternalServerError("No updates available.")
        # if not ahv_updates or not aos_updates:
        #     raise flaskex.InternalServerError(f"AOS and AHV updates are not both available, abort.")
        
        # Validate available updates
        available_updates = ahv_updates + aos_updates
        if not available_updates:
            raise flaskex.InternalServerError("We could not find any avalable updates, manually check please, abort.")
        self._write_log("We've got avaialbe updates.")
        entities = list()
        self._write_log(log_info="Going through and match object list and avaiable updates.")
        self._write_log(log_info="Re-generate the update plan.")
        plan = self.get_software_upgrade_plan()
        for obj in [p for p in plan if p['need_upgrade']]:
            match_flag = False
            self._write_log(log_info=f"Matching available update for {obj['name']}")
            if obj['entity_model'] == "AOS":
                for update in available_updates:
                    if update['entity_uuid'] == obj['entity_uuid'] and update['version'] == obj['next_version']:
                        match_flag = True
                        entities.append(update)
                        continue
                if not match_flag:
                    raise flaskex.InternalServerError(f"There is no matched available AOS version for {obj['name']}, please manualy check, abort.")
            if obj['entity_model'] == "AHV hypervisor":
                for update in available_updates:
                    if update['entity_uuid'] == obj['entity_uuid'] and self.get_ahv_version_digits(update['version']) == obj['next_version']:
                        match_flag = True
                        entities.append(update)
                        continue
                if not match_flag:
                    raise flaskex.InternalServerError(f"There is no matched available AHV version for {obj['name']}, please manually check, abort.")
        # AOS/AHV upgrade
        self._write_log(log_info="Everything is ready, start to upgrade AOS/AHV")
        self._write_log(log_info="Start to do AOS/AHV Upgrading", log_severity="title")
        result = self.p_e.perform_lcm_update(entities=entities)
        if ntx_task_id := result.json().get('data').get('extId'):
            self._write_log(f"We've successfully initiated AOS/AHV upgrade, task uuid: {ntx_task_id}, it may takes a while to complete, please check the status later, task over.")
            return ntx_task_id, TaskStatus.IN_PROGRESS
        raise flaskex.InternalServerError("Failed to get the task of AOS/AHV upgrade, please manually check, task over with errors.")

    def get_software_upgrade_plan(self):
        self._write_log(log_info=f"Making software upgrade plan for {self.pe}")
        self._write_log(log_info=f"Getting host list from {self.pe}.")
        nodes = self.p_e.get_host().json().get('entities')
        # {'uuid': 'bf573971-c83b-49f7-8ca2-2f8c6fe6d9ca', 'name': 'RETCN888-NX7002', 'hypervisorAddress': '*************'...}
        # We cannot do this update on a single-node cluster
        if len(nodes) == 1:
            raise flaskex.InternalServerError("It's not allowed to perform this upgrade on a single-node cluster, quit.")
        self._write_log(log_info=f"Getting current software version of {self.pe}")
        # lcm_softwares = self.p_e.get_lcm_software()
        self._write_log(log_info="Trying to fetch LCM inventory data directly.")
        lcm_softwares = self.p_e.get_lcm_inventory(filter_key="entity_type", filter_value="software")
        if not lcm_softwares:
            self._write_log(log_info=f"No inventory data available. Performing inventory on {self.pe}")
            task_uuid = self.p_e.perform_inventory_v4()
            self._write_log(log_info=f"Inventory has been launched, task uuid :{task_uuid}.")
            while True:
                lcm_status = self.p_e.get_lcm_progress(max_try=1)
                if lcm_status and lcm_status.lower() == "inventory":
                    self._write_log(log_info="LCM is still performing inventory. Current status: {}".format(lcm_status))
                    time.sleep(120)
                    continue
                if not lcm_status:
                    self._write_log(log_info="LCM inventory completed. Fetching LCM inventory data.")
                    lcm_softwares = self.p_e.get_lcm_inventory(filter_key="entity_type", filter_value="software")
                    break
        self._write_log(log_info=f"Filtering out provisioning AOS/AHV for {self.pe}")
        ahv_hypervisors = [software for software in lcm_softwares if software['entity_model'] == "AHV hypervisor"]
        # {'entity_id': 'cf15cd33-1c61-44b0-95ea-7f6608f6cf49', 'uuid': 'cf15cd33-1c61-44b0-95ea-7f6608f6cf49', 'entity_model': 'AHV hypervisor', 'version': 'el7.nutanix.20201105.2298', 'location_id': 'node:7ad13d12-32cf-4edb-b963-c36df3ae8ff4', '_created_timestamp_usecs_': '1710408346832603'}
        aos = [software for software in lcm_softwares if software['entity_model'] == "AOS"][0]
        # [{'entity_id': '79d9614f-1afb-4665-9e34-d95f4520a475', 'uuid': '79d9614f-1afb-4665-9e34-d95f4520a475', 'entity_model': 'AOS', 'version': '6.5.3.6', 'location_id': 'cluster:0005e268-8f79-9e46-4411-6805cadad640', '_created_timestamp_usecs_': '1710408359363971'}]
        if len(nodes) != len(ahv_hypervisors):
            raise flaskex.InternalServerError(f"We have {len(ahv_hypervisors)} AHV versions, but detected {len(nodes)} nodes available, not matched, quit.")
        result = list()
        for node in nodes:
            if node_ahv := [software for software in ahv_hypervisors if node['uuid'] == software['location_id'].split(":")[1]]:
                # if  len(node_ahv) != 1:
                #     # To avoid the situation of multiple AHV versions on one node, we just skip this node
                #     self._write_log(log_info=f"There is no AHV version exact matched with the host:{node['name']}, ip:{node['hypervisorAddress']}")
                #     continue
                ahv = node_ahv[0]
                ahv['version'] = self.get_ahv_version_digits(ahv['version'])
                res = {
                    'location': "Host",
                    'name': node['name'], 
                    'entity_model': "AHV hypervisor", 
                    'entity_uuid': ahv['entity_id'], 
                    'version': ahv['version'], 
                    'next_version': self.target_ahv_version.lower().strip(), 
                    'need_upgrade': True
                }
                if not self.compare_ahv_versions(res['version'], res['next_version']):
                    res['need_upgrade'] = False
                    self._write_log(log_info=f"Good, this host {node['name']} applied with {ahv['version']} is already at or above target AHV version {self.target_ahv_version}, no need to upgrade.")
                else:
                    self._write_log(log_info=f"This host {node['name']} applied with {ahv['version']} is below target AHV version {self.target_ahv_version}, upgrade needed.")
                result.append(res)
            else:
                self._write_log(log_info=f"Failed to get the AHV version of the host:{node['name']}, ip:{node['hypervisorAddress']}")
                continue
        if aos:
            res = {
                'location': "Cluster",
                'name': self.pe, 
                'entity_model': "AOS", 
                'entity_uuid': aos['entity_id'], 
                'version': aos['version'].lower().strip(),
                'next_version': self.target_aos_version.lower().strip(),
                'need_upgrade': True
            }
            if not version.parse(res['version']) < version.parse(res['next_version']):
                res['need_upgrade'] = False
                self._write_log(log_info=f"Good, the provisioning AOS version {res['version']} is already at or above target AOS version {res['next_version']}, no need to upgrade.")
            else:
                self._write_log(log_info=f"The provisioning AOS version {res['version']} does not match target AOS version {res['next_version']}, upgrade needed.")
            result.append(res)
        self._write_log(log_info=f"We've generated the plan, is: {json.dumps(result)}")
        return result

    def configure_eula(self):
        self._write_log("Configure User License Agreement Acceptance...")
        rest = RestEulas(self.pe, self.pe_sa, self.logger)
        eulas = rest.list_eulas()["entities"]
        # if len(eulas) >= 2:
        #     raise flaskex.InternalServerError("There can be only '1' EULA setting!")
        for eula in eulas:
            if eula.get('enabled') and eula.get('userDetailsList') and eula['userDetailsList'][0].get("username") == self.benchmark['eula']['name']:
                self._write_log("Already completed Eula Settings.")
                return
        rest.accept_eulas(username=self.benchmark['eula']['name'], company_name=self.benchmark['eula']['company'], job_title=self.benchmark['eula']['role'])

2025-08-05 09:32:11,979 INFO Start to run the task.
2025-08-05 09:32:11,991 INFO ****************************************************************************************************
2025-08-05 09:32:11,992 INFO *                                                                                                  *
2025-08-05 09:32:11,992 INFO *                                  Configuring MaintenanceMode...                                  *
2025-08-05 09:32:11,992 INFO *                                                                                                  *
2025-08-05 09:32:11,995 INFO ****************************************************************************************************
2025-08-05 09:32:15,710 INFO Checking Maintenance Mode
2025-08-05 09:32:15,710 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-08-05 09:32:15,710 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:32:18,277 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 09:32:18,277 INFO SSH Executing '/home/<USER>/prism/cli/ncli host list --json=pretty'.
2025-08-05 09:32:19,119 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:32:24,122 INFO stdout: b'{\n  "data" : [ {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::4",\n    "uuid" : "8a276a5a-9cd0-4702-8ed9-5699d07c192e",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH967RD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::60",\n        "diskUuid" : "ba87f2bb-2824-44e3-8240-75c32c2c4e80",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH967RD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH99N2D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::51",\n        "diskUuid" : "35ea81b9-7cfb-4924-9b45-f07a4cd6fc3f",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N2D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH7B71D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::53",\n        "diskUuid" : "65a27c83-885e-4672-be27-05971598814b",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7B71D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9726D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::61",\n        "diskUuid" : "f4390867-7871-494b-8fd2-65dfa19de512",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9726D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH9B3ED",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::58",\n        "diskUuid" : "9979a224-9d9e-42bc-a282-b1af4e2f9acd",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B3ED",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9421D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::52",\n        "diskUuid" : "01e76618-04df-4259-a761-7e0bab50887b",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9421D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH8DKKD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::56",\n        "diskUuid" : "e31d9325-c534-4c9e-a67e-65081ebff024",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8DKKD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98J3D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::57",\n        "diskUuid" : "c61322d0-febf-45eb-8293-8bb50ef57e95",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98J3D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307893",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::55",\n        "diskUuid" : "d55987bc-a972-43e0-8c49-380c1fed8ff0",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307893",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307888",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::54",\n        "diskUuid" : "514be4f9-c6dc-4f16-805f-8372704d5f1d",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307888",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7001",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8S",\n    "blockSerial" : "CZ20240J8S",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753432724206906,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "1",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "387096",\n      "controller_num_iops" : "98",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "283662",\n      "controller_num_write_io" : "983",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "1506952",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "1297",\n      "controller_total_io_size_kbytes" : "8248",\n      "content_cache_hit_ppm" : "680801",\n      "controller_num_io" : "983",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "87",\n      "num_write_iops" : "1",\n      "controller_num_random_io" : "0",\n      "num_iops" : "3",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1533",\n      "num_io" : "31",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "824",\n      "hypervisor_num_received_bytes" : "4460758868519",\n      "hypervisor_timespan_usecs" : "29998591",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "102",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "144",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "824",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "3283097297341",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "10",\n      "hypervisor_memory_usage_ppm" : "409960",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "98",\n      "total_io_time_usecs" : "4484",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "12",\n      "write_io_bandwidth_kBps" : "35",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "8",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "19",\n      "total_io_size_kbytes" : "458",\n      "io_bandwidth_kBps" : "45",\n      "content_cache_physical_memory_usage_bytes" : "3148363344",\n      "controller_timespan_usecs" : "10000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-397736160",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "612903",\n      "controller_avg_write_io_latency_usecs" : "1533",\n      "content_cache_logical_memory_usage_bytes" : "2750627184"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "8901804032",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "1322719313920",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97238399202920",\n      "storage_tier.ssd.usage_bytes" : "1007379034112",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91527060104808",\n      "storage.usage_bytes" : "1016280838144",\n      "storage_tier.ssd.free_bytes" : "5711339098112"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::5",\n    "uuid" : "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH9453D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::63",\n        "diskUuid" : "55b9bef0-e64e-4b83-b56d-07424ac5a4fa",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9453D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH9B0JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::69",\n        "diskUuid" : "87299dda-79ad-4648-a18d-867837bdb061",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B0JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH947JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::66",\n        "diskUuid" : "21accc04-9438-4b1e-a735-121e6651e2c1",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH947JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9B2GD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::68",\n        "diskUuid" : "8fcec93c-f3a5-4a88-a18f-ad5a75a3d36e",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B2GD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH95WYD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::64",\n        "diskUuid" : "190548f4-f43c-40fd-8e0d-c805d972bf31",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH95WYD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9984D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::67",\n        "diskUuid" : "f73b0d65-6966-4e81-92fb-7815e0986aea",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9984D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH99N1D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::70",\n        "diskUuid" : "ebe3c979-d24f-46fb-9f2d-06b675e7f957",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N1D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98WLD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::65",\n        "diskUuid" : "3b10236d-18ef-48d1-b777-8f9f6b64ced8",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98WLD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N200095",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::72",\n        "diskUuid" : "4ce56fa0-31fa-45aa-9740-39f6561ab0e2",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N200095",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307878",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::71",\n        "diskUuid" : "064c52a5-ac8a-4f7f-ae98-967f225ddb32",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307878",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7002",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********30",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********30"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8R",\n    "blockSerial" : "CZ20240J8R",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753430337664248,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "411764",\n      "controller_num_iops" : "95",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "396",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "249453",\n      "controller_num_write_io" : "1914",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "2047463",\n      "controller_total_read_io_size_kbytes" : "4",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "522",\n      "content_cache_num_lookups" : "1421",\n      "controller_total_io_size_kbytes" : "17836",\n      "content_cache_hit_ppm" : "706544",\n      "controller_num_io" : "1915",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "87",\n      "num_write_iops" : "1",\n      "controller_num_random_io" : "0",\n      "num_iops" : "1",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1069",\n      "num_io" : "17",\n      "controller_num_read_io" : "1",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "891",\n      "hypervisor_num_received_bytes" : "4684614587680",\n      "hypervisor_timespan_usecs" : "29977482",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "60",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "117",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "891",\n      "controller_write_io_ppm" : "999477",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "3387213043327",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "6",\n      "hypervisor_memory_usage_ppm" : "410090",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "95",\n      "total_io_time_usecs" : "1990",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "4",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "7",\n      "write_io_bandwidth_kBps" : "13",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "396",\n      "num_write_io" : "10",\n      "total_io_size_kbytes" : "190",\n      "io_bandwidth_kBps" : "19",\n      "content_cache_physical_memory_usage_bytes" : "2842812260",\n      "controller_timespan_usecs" : "20000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-361093980",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "588235",\n      "controller_avg_write_io_latency_usecs" : "1069",\n      "content_cache_logical_memory_usage_bytes" : "2481718280"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "8872005632",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "1324563005440",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97237516756584",\n      "storage_tier.ssd.usage_bytes" : "1008291278848",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91527089903208",\n      "storage.usage_bytes" : "1017163284480",\n      "storage_tier.ssd.free_bytes" : "5710426853376"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::6",\n    "uuid" : "34ff0abd-9dee-4e7d-9481-4716d64569b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH96JDD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::41",\n        "diskUuid" : "2c172a78-3626-4861-bfff-98d6d79fef49",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH96JDD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH995TD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::43",\n        "diskUuid" : "94de93d2-5e8f-44d7-85d7-c414177670a7",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH995TD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH991DD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::46",\n        "diskUuid" : "4d3bffc9-fa0d-4ff3-bd95-436cf68ab516",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH991DD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9825D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::45",\n        "diskUuid" : "ea324991-7bb3-49a9-84f6-7c30e1c938a5",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9825D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH8XYHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::48",\n        "diskUuid" : "5f14642e-c45b-4efc-b5ee-95c532192c22",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8XYHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH99MHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::44",\n        "diskUuid" : "5758e233-4edc-4045-8446-0625e9ba05c0",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99MHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH9ADHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::47",\n        "diskUuid" : "dbeb0a0e-ff11-4b98-a2b7-a45bad3407fd",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9ADHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH7XGHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::42",\n        "diskUuid" : "08dba0c0-cd98-4d75-a131-a60323ba43b5",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7XGHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307864",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::50",\n        "diskUuid" : "39e3bde6-2802-4ad2-825c-a0d61ae9fb35",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307864",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307881",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::49",\n        "diskUuid" : "a032c48d-3866-4fd6-88af-1f6a25fe5ac3",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307881",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7003",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********31",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********31"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8Q",\n    "blockSerial" : "CZ20240J8Q",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "ncli_manual",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 4,\n    "bootTimeInUsecs" : 1753435095319593,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "10000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "333333",\n      "controller_num_iops" : "67",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "217807",\n      "controller_num_write_io" : "671",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "795426",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "1358",\n      "controller_total_io_size_kbytes" : "6660",\n      "content_cache_hit_ppm" : "696612",\n      "controller_num_io" : "671",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "97",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1185",\n      "num_io" : "6",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "666",\n      "hypervisor_num_received_bytes" : "2809605479931",\n      "hypervisor_timespan_usecs" : "29918615",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "15",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "1100",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "666",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "4987296041807",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "1",\n      "hypervisor_memory_usage_ppm" : "511591",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "67",\n      "total_io_time_usecs" : "6603",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "2",\n      "write_io_bandwidth_kBps" : "5",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "4",\n      "total_io_size_kbytes" : "73",\n      "io_bandwidth_kBps" : "7",\n      "content_cache_physical_memory_usage_bytes" : "6303380872",\n      "controller_timespan_usecs" : "10000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-161186232",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "666666",\n      "controller_avg_write_io_latency_usecs" : "1185",\n      "content_cache_logical_memory_usage_bytes" : "6142194640"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "1682046976",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "94411309056",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "98219494090344",\n      "storage_tier.ssd.usage_bytes" : "33503903744",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91534279861864",\n      "storage.usage_bytes" : "35185950720",\n      "storage_tier.ssd.free_bytes" : "6685214228480"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  } ],\n  "status" : 0\n}\n'
2025-08-05 09:32:24,134 INFO All good, no hosts are set as NCLI maintenance inside this cluster.
2025-08-05 09:32:24,134 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-08-05 09:32:24,134 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:32:26,770 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 09:32:26,771 INFO SSH Executing '/usr/local/nutanix/bin/acli -o json host.list'.
2025-08-05 09:32:27,663 INFO Waiting for 5 seconds for the execution.
2025-08-05 09:32:32,666 INFO stdout: b'{"data": [{"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "8a276a5a-9cd0-4702-8ed9-5699d07c192e", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "34ff0abd-9dee-4e7d-9481-4716d64569b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}], "error": null, "status": 0}\n'
2025-08-05 09:32:32,667 INFO This seems a very old AOS version...
2025-08-05 09:32:32,667 INFO This seems a very old AOS version...
2025-08-05 09:32:32,667 INFO This seems a very old AOS version...
2025-08-05 09:32:32,680 INFO All good, no hosts are set as ACLI maintenance inside this cluster.
2025-08-05 09:32:32,691 INFO Checking CVM status
2025-08-05 09:32:33,311 INFO Trying to SSH to the RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 09:32:33,311 INFO First try with username/password.
2025-08-05 09:32:33,311 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:32:36,393 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 09:32:42,521 INFO Sending 'cluster status |grep -v UP' to the server.
2025-08-05 09:32:58,524 INFO CVM IP:*********** Status:Up
2025-08-05 09:32:58,524 INFO CVM IP:*********** Status:Up
2025-08-05 09:32:58,524 INFO CVM IP:*********** Status:Up
2025-08-05 09:32:58,524 INFO Great, all CVM status are Up
2025-08-05 09:32:58,563 INFO MaintenanceMode: Done
2025-08-05 09:32:58,594 INFO ****************************************************************************************************
2025-08-05 09:32:58,594 INFO *                                                                                                  *
2025-08-05 09:32:58,594 INFO *                                    Configuring IpamHealth...                                     *
2025-08-05 09:32:58,594 INFO *                                                                                                  *
2025-08-05 09:32:58,594 INFO ****************************************************************************************************
2025-08-05 09:33:00,935 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:33:00,936 INFO params: None
2025-08-05 09:33:00,936 INFO User: <EMAIL>
2025-08-05 09:33:00,936 INFO payload: None
2025-08-05 09:33:00,936 INFO files: None
2025-08-05 09:33:00,936 INFO timeout: 30
2025-08-05 09:33:02,383 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=parent_subnet_id='81005', method: GET, headers: None
2025-08-05 09:33:02,383 INFO params: None
2025-08-05 09:33:02,383 INFO User: <EMAIL>
2025-08-05 09:33:02,383 INFO payload: None
2025-08-05 09:33:02,383 INFO files: None
2025-08-05 09:33:02,384 INFO timeout: 30
2025-08-05 09:33:03,894 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91749', method: GET, headers: None
2025-08-05 09:33:03,901 INFO params: None
2025-08-05 09:33:03,901 INFO User: <EMAIL>
2025-08-05 09:33:03,901 INFO payload: None
2025-08-05 09:33:03,901 INFO files: None
2025-08-05 09:33:03,901 INFO timeout: 30
2025-08-05 09:33:05,303 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:33:05,303 INFO params: None
2025-08-05 09:33:05,303 INFO User: <EMAIL>
2025-08-05 09:33:05,303 INFO payload: None
2025-08-05 09:33:05,303 INFO files: None
2025-08-05 09:33:05,305 INFO timeout: 30
2025-08-05 09:33:06,278 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 09:33:06,278 INFO params: None
2025-08-05 09:33:06,278 INFO User: <EMAIL>
2025-08-05 09:33:06,278 INFO payload: None
2025-08-05 09:33:06,278 INFO files: None
2025-08-05 09:33:06,278 INFO timeout: 30
2025-08-05 09:33:08,891 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:33:08,891 INFO params: None
2025-08-05 09:33:08,891 INFO User: <EMAIL>
2025-08-05 09:33:08,891 INFO payload: None
2025-08-05 09:33:08,891 INFO files: None
2025-08-05 09:33:08,892 INFO timeout: 30
2025-08-05 09:33:09,912 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 09:33:09,912 INFO params: None
2025-08-05 09:33:09,912 INFO User: <EMAIL>
2025-08-05 09:33:09,912 INFO payload: None
2025-08-05 09:33:09,912 INFO files: None
2025-08-05 09:33:09,912 INFO timeout: 30
2025-08-05 09:33:11,099 INFO Checking IPAM Health
2025-08-05 09:33:11,099 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:33:11,099 INFO params: None
2025-08-05 09:33:11,099 INFO User: 1-click-nutanix
2025-08-05 09:33:11,099 INFO payload: None
2025-08-05 09:33:11,099 INFO files: None
2025-08-05 09:33:11,099 INFO timeout: 30
2025-08-05 09:33:12,319 INFO Getting host list from RETSEELM-NXC000.
2025-08-05 09:33:12,319 INFO Got the host list from RETSEELM-NXC000.
2025-08-05 09:33:12,338 INFO Node records on IPAM and NTX match.
2025-08-05 09:33:12,348 INFO Checking IPAM integration...
2025-08-05 09:33:12,364 INFO Let's check '9' IP records, DNS update status
2025-08-05 09:33:12,364 INFO Pulling Ipam record with IP *************
2025-08-05 09:33:12,364 INFO This record is already enabled.
2025-08-05 09:33:12,364 INFO Pulling Ipam record with IP ***********30
2025-08-05 09:33:12,364 INFO This record is already enabled.
2025-08-05 09:33:12,364 INFO Pulling Ipam record with IP ***********31
2025-08-05 09:33:12,364 INFO This record is already enabled.
2025-08-05 09:33:12,365 INFO Pulling Ipam record with IP ***********
2025-08-05 09:33:12,365 INFO This record is already enabled.
2025-08-05 09:33:12,365 INFO Pulling Ipam record with IP ***********
2025-08-05 09:33:12,365 INFO This record is already enabled.
2025-08-05 09:33:12,365 INFO Pulling Ipam record with IP ***********
2025-08-05 09:33:12,365 INFO This record is already enabled.
2025-08-05 09:33:12,365 INFO Pulling Ipam record with IP ***********
2025-08-05 09:33:12,365 INFO This record is already enabled.
2025-08-05 09:33:12,365 INFO Pulling Ipam record with IP ***********
2025-08-05 09:33:12,365 INFO This record is already enabled.
2025-08-05 09:33:12,365 INFO Pulling Ipam record with IP ***********
2025-08-05 09:33:12,365 INFO This record is already enabled.
2025-08-05 09:33:12,377 INFO Records are all enabled.
2025-08-05 09:33:12,386 INFO IpamHealth: Done
2025-08-05 09:33:12,394 INFO ****************************************************************************************************
2025-08-05 09:33:12,394 INFO *                                                                                                  *
2025-08-05 09:33:12,394 INFO *                                      Configuring CvmRam...                                       *
2025-08-05 09:33:12,395 INFO *                                                                                                  *
2025-08-05 09:33:12,395 INFO ****************************************************************************************************
2025-08-05 09:33:14,840 INFO Desired State 'cvm_ram' has been disabled inside this site profile.
2025-08-05 09:33:14,841 INFO Checking CVM RAM Desired state...
2025-08-05 09:33:14,841 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/genesis, method: POST, headers: None
2025-08-05 09:33:14,841 INFO params: None
2025-08-05 09:33:14,842 INFO User: 1-click-nutanix
2025-08-05 09:33:14,842 INFO payload: {'value': '{".oid": "ClusterManager", ".method": "get_cluster_cvm_params_map", ".kwargs": {}}'}
2025-08-05 09:33:14,842 INFO files: None
2025-08-05 09:33:14,842 INFO timeout: 30
2025-08-05 09:33:16,129 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:33:16,129 INFO params: None
2025-08-05 09:33:16,129 INFO User: <EMAIL>
2025-08-05 09:33:16,129 INFO payload: None
2025-08-05 09:33:16,130 INFO files: None
2025-08-05 09:33:16,130 INFO timeout: 30
2025-08-05 09:33:17,129 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 09:33:17,129 INFO params: None
2025-08-05 09:33:17,129 INFO User: <EMAIL>
2025-08-05 09:33:17,129 INFO payload: None
2025-08-05 09:33:17,129 INFO files: None
2025-08-05 09:33:17,129 INFO timeout: 30
2025-08-05 09:33:18,262 INFO CVM RAM Desired state already met.
2025-08-05 09:33:18,279 INFO CvmRam: Done
2025-08-05 09:33:18,293 INFO ****************************************************************************************************
2025-08-05 09:33:18,294 INFO *                                                                                                  *
2025-08-05 09:33:18,294 INFO *                                      Configuring VmRbac...                                       *
2025-08-05 09:33:18,294 INFO *                                                                                                  *
2025-08-05 09:33:18,294 INFO ****************************************************************************************************
2025-08-05 09:33:20,544 INFO Desired State 'role_vm_rbac' has been disabled inside this site profile.
2025-08-05 09:33:20,563 INFO Renewing access control policies now.
2025-08-05 09:33:20,563 INFO Renewing all the linux,windows,network vms.
2025-08-05 09:33:20,563 INFO Getting access control policy list from SSP-DHD2-NTX.ikead2.com.
2025-08-05 09:33:20,563 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/api/nutanix/v3/user_groups/list, method: POST, headers: None
2025-08-05 09:33:20,563 INFO params: None
2025-08-05 09:33:20,563 INFO User: 1-click-nutanix
2025-08-05 09:33:20,563 INFO payload: {'kind': 'user_group', 'length': 500, 'offset': 0, 'filter': ''}
2025-08-05 09:33:20,563 INFO files: None
2025-08-05 09:33:20,563 INFO timeout: None
2025-08-05 09:33:21,946 INFO We got 1 user groups.
2025-08-05 09:33:21,946 INFO Getting vm list from SSP-DHD2-NTX.ikead2.com.
2025-08-05 09:33:21,946 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/api/nutanix/v3/vms/list, method: POST, headers: None
2025-08-05 09:33:21,946 INFO params: None
2025-08-05 09:33:21,946 INFO User: 1-click-nutanix
2025-08-05 09:33:21,946 INFO payload: {'kind': 'vm', 'length': 500, 'offset': 0, 'filter': ''}
2025-08-05 09:33:21,957 INFO files: None
2025-08-05 09:33:21,957 INFO timeout: None
2025-08-05 09:33:23,613 INFO We got 24 vms.
2025-08-05 09:33:23,613 INFO Getting role list from SSP-DHD2-NTX.ikead2.com. 
2025-08-05 09:33:23,613 INFO Payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Linux-Operator'}
2025-08-05 09:33:23,613 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/api/nutanix/v3/roles/list, method: POST, headers: None
2025-08-05 09:33:23,613 INFO params: None
2025-08-05 09:33:23,613 INFO User: 1-click-nutanix
2025-08-05 09:33:23,613 INFO payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Linux-Operator'}
2025-08-05 09:33:23,613 INFO files: None
2025-08-05 09:33:23,613 INFO timeout: None
2025-08-05 09:33:24,887 INFO Got 0 role(s).
2025-08-05 09:33:24,887 ERROR Can't get the role list or role list is empty, we will skip this ACP.
2025-08-05 09:33:24,887 INFO Getting role list from SSP-DHD2-NTX.ikead2.com. 
2025-08-05 09:33:24,887 INFO Payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Windows-Operator'}
2025-08-05 09:33:24,887 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/api/nutanix/v3/roles/list, method: POST, headers: None
2025-08-05 09:33:24,887 INFO params: None
2025-08-05 09:33:24,887 INFO User: 1-click-nutanix
2025-08-05 09:33:24,888 INFO payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Windows-Operator'}
2025-08-05 09:33:24,888 INFO files: None
2025-08-05 09:33:24,889 INFO timeout: None
2025-08-05 09:33:26,308 INFO Got 0 role(s).
2025-08-05 09:33:26,309 ERROR Can't get the role list or role list is empty, we will skip this ACP.
2025-08-05 09:33:26,309 INFO Getting role list from SSP-DHD2-NTX.ikead2.com. 
2025-08-05 09:33:26,309 INFO Payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Networking-Operator'}
2025-08-05 09:33:26,309 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/api/nutanix/v3/roles/list, method: POST, headers: None
2025-08-05 09:33:26,309 INFO params: None
2025-08-05 09:33:26,309 INFO User: 1-click-nutanix
2025-08-05 09:33:26,310 INFO payload: {'kind': 'role', 'length': 500, 'offset': 0, 'filter': 'name==IKEA-Networking-Operator'}
2025-08-05 09:33:26,310 INFO files: None
2025-08-05 09:33:26,310 INFO timeout: None
2025-08-05 09:33:27,541 INFO Got 0 role(s).
2025-08-05 09:33:27,541 ERROR Can't get the role list or role list is empty, we will skip this ACP.
2025-08-05 09:33:27,548 INFO VmRbac: Done
2025-08-05 09:33:27,563 INFO ****************************************************************************************************
2025-08-05 09:33:27,563 INFO *                                                                                                  *
2025-08-05 09:33:27,563 INFO *                                   Configuring StorageConfig...                                   *
2025-08-05 09:33:27,563 INFO *                                                                                                  *
2025-08-05 09:33:27,563 INFO ****************************************************************************************************
2025-08-05 09:33:30,141 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/storage_containers, method: GET, headers: None
2025-08-05 09:33:30,141 INFO params: None
2025-08-05 09:33:30,141 INFO User: 1-click-nutanix
2025-08-05 09:33:30,141 INFO payload: None
2025-08-05 09:33:30,141 INFO files: None
2025-08-05 09:33:30,141 INFO timeout: 30
2025-08-05 09:33:31,389 INFO Checking vdisks...
2025-08-05 09:33:31,455 INFO State of the default container is already as desired.
2025-08-05 09:33:31,988 INFO Configuring storage containers...
2025-08-05 09:33:32,023 INFO StorageConfig: Done
2025-08-05 09:33:32,086 INFO ****************************************************************************************************
2025-08-05 09:33:32,086 INFO *                                                                                                  *
2025-08-05 09:33:32,086 INFO *                                       Configuring Smtp...                                        *
2025-08-05 09:33:32,086 INFO *                                                                                                  *
2025-08-05 09:33:32,086 INFO ****************************************************************************************************
2025-08-05 09:33:34,941 INFO Desired State 'smtp_config' has been disabled inside this site profile.
2025-08-05 09:33:34,941 INFO Configuring SMTP Settings
2025-08-05 09:33:34,941 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/cluster/smtp, method: PUT, headers: None
2025-08-05 09:33:34,941 INFO params: None
2025-08-05 09:33:34,941 INFO User: 1-click-nutanix
2025-08-05 09:33:34,941 INFO payload: {'address': 'smtp-gw.ikea.com', 'port': 25, 'username': None, 'password': None, 'secureMode': 'NONE', 'fromEmailAddress': '<EMAIL>', 'emailStatus': {'status': 'UNKNOWN', 'message': None}}
2025-08-05 09:33:34,941 INFO files: None
2025-08-05 09:33:34,941 INFO timeout: 30
2025-08-05 09:33:36,502 INFO Configuring SMTP Alert Settings
2025-08-05 09:33:36,502 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/alerts/configuration, method: PUT, headers: None
2025-08-05 09:33:36,502 INFO params: None
2025-08-05 09:33:36,502 INFO User: 1-click-nutanix
2025-08-05 09:33:36,502 INFO payload: {'emailContactList': ['<EMAIL>'], 'enable': False, 'enableDefaultNutanixEmail': True, 'skipEmptyAlertEmailDigest': True, 'defaultNutanixEmail': '<EMAIL>', 'smtpserver': {'address': 'smtp-gw.ikea.com', 'port': 25, 'username': None, 'password': None, 'secureMode': 'NONE', 'fromEmailAddress': '<EMAIL>', 'emailStatus': {'status': 'UNKNOWN', 'message': None}}, 'tunnelDetails': {'httpProxy': None, 'serviceCenter': None, 'connectionStatus': {'lastCheckedTimeStampUsecs': 0, 'status': 'UNKNOWN', 'message': None}, 'transportStatus': {'status': 'UNKNOWN', 'message': None}}, 'emailConfigRules': None, 'emailTemplate': {'subjectPrefix': None, 'bodySuffix': None}}
2025-08-05 09:33:36,502 INFO files: None
2025-08-05 09:33:36,502 INFO timeout: 30
2025-08-05 09:33:38,043 INFO PC specific process not implemented yet.
2025-08-05 09:33:38,098 INFO Smtp: Done
2025-08-05 09:33:38,113 INFO ****************************************************************************************************
2025-08-05 09:33:38,114 INFO *                                                                                                  *
2025-08-05 09:33:38,114 INFO *                                      Configuring DnsNtp...                                       *
2025-08-05 09:33:38,114 INFO *                                                                                                  *
2025-08-05 09:33:38,114 INFO ****************************************************************************************************
2025-08-05 09:33:40,583 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:33:40,583 INFO params: None
2025-08-05 09:33:40,583 INFO User: <EMAIL>
2025-08-05 09:33:40,583 INFO payload: None
2025-08-05 09:33:40,583 INFO files: None
2025-08-05 09:33:40,583 INFO timeout: 30
2025-08-05 09:33:41,599 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=subnet_id%3D%2791747%27, method: GET, headers: None
2025-08-05 09:33:41,599 INFO params: None
2025-08-05 09:33:41,599 INFO User: <EMAIL>
2025-08-05 09:33:41,599 INFO payload: None
2025-08-05 09:33:41,599 INFO files: None
2025-08-05 09:33:41,599 INFO timeout: 30
2025-08-05 09:33:42,600 INFO AHV / CVM Subnet: '91747' finding the rest.
2025-08-05 09:33:42,600 INFO Desired State 'dns_ntp_config' has been disabled inside this site profile.
2025-08-05 09:33:42,615 INFO Configuring NTP...
2025-08-05 09:33:42,616 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/cluster/ntp_servers, method: GET, headers: None
2025-08-05 09:33:42,616 INFO params: None
2025-08-05 09:33:42,616 INFO User: 1-click-nutanix
2025-08-05 09:33:42,616 INFO payload: None
2025-08-05 09:33:42,616 INFO files: None
2025-08-05 09:33:42,616 INFO timeout: 30
2025-08-05 09:33:43,960 INFO Target NTP servers: ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com', 'ntp1-na.ikea.com', 'ntp1-ap.ikea.com', 'ntp1-cn.ikea.com']
2025-08-05 09:33:43,960 INFO Current NTP servers: ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com', 'ntp1-na.ikea.com', 'ntp1-ap.ikea.com', 'ntp1-cn.ikea.com']
2025-08-05 09:33:43,972 INFO NTP is already as desired.
2025-08-05 09:33:43,995 INFO Configuring DNS...
2025-08-05 09:33:43,995 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/cluster/name_servers, method: GET, headers: None
2025-08-05 09:33:43,995 INFO params: None
2025-08-05 09:33:43,995 INFO User: 1-click-nutanix
2025-08-05 09:33:43,995 INFO payload: None
2025-08-05 09:33:43,995 INFO files: None
2025-08-05 09:33:43,995 INFO timeout: 30
2025-08-05 09:33:45,343 INFO Target DNS servers: ['10.59.253.2', '10.59.67.9']
2025-08-05 09:33:45,343 INFO Current DNS servers: ['10.59.253.2', '10.59.67.9']
2025-08-05 09:33:45,362 INFO DNS is already as desired.
2025-08-05 09:33:45,375 INFO Repeating for Central PE
2025-08-05 09:33:45,399 INFO Configuring NTP...
2025-08-05 09:33:45,400 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/cluster/ntp_servers, method: GET, headers: None
2025-08-05 09:33:45,400 INFO params: None
2025-08-05 09:33:45,400 INFO User: 1-click-nutanix
2025-08-05 09:33:45,400 INFO payload: None
2025-08-05 09:33:45,400 INFO files: None
2025-08-05 09:33:45,400 INFO timeout: 30
2025-08-05 09:33:46,706 INFO Target NTP servers: ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com', 'ntp1-na.ikea.com', 'ntp1-ap.ikea.com', 'ntp1-cn.ikea.com']
2025-08-05 09:33:46,706 INFO Current NTP servers: ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com', 'ntp1-na.ikea.com', 'ntp1-ap.ikea.com', 'ntp1-cn.ikea.com']
2025-08-05 09:33:46,719 INFO NTP is already as desired.
2025-08-05 09:33:46,724 INFO Configuring DNS...
2025-08-05 09:33:46,724 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/cluster/name_servers, method: GET, headers: None
2025-08-05 09:33:46,724 INFO params: None
2025-08-05 09:33:46,724 INFO User: 1-click-nutanix
2025-08-05 09:33:46,724 INFO payload: None
2025-08-05 09:33:46,724 INFO files: None
2025-08-05 09:33:46,724 INFO timeout: 30
2025-08-05 09:33:48,033 INFO Target DNS servers: ['10.59.253.2', '10.59.67.9']
2025-08-05 09:33:48,033 INFO Current DNS servers: ['10.59.253.2', '10.59.67.9']
2025-08-05 09:33:48,099 INFO DNS is already as desired.
2025-08-05 09:33:48,108 INFO DnsNtp: Done
2025-08-05 09:33:48,109 INFO ****************************************************************************************************
2025-08-05 09:33:48,109 INFO *                                                                                                  *
2025-08-05 09:33:48,109 INFO *                                        Configuring Ha...                                         *
2025-08-05 09:33:48,109 INFO *                                                                                                  *
2025-08-05 09:33:48,109 INFO ****************************************************************************************************
2025-08-05 09:33:50,563 INFO Desired State 'ha_reservation' has been disabled inside this site profile.
2025-08-05 09:33:50,563 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:33:50,563 INFO params: None
2025-08-05 09:33:50,563 INFO User: 1-click-nutanix
2025-08-05 09:33:50,563 INFO payload: None
2025-08-05 09:33:50,563 INFO files: None
2025-08-05 09:33:50,563 INFO timeout: 30
2025-08-05 09:33:51,780 INFO Getting host list from RETSEELM-NXC000.
2025-08-05 09:33:51,780 INFO Got the host list from RETSEELM-NXC000.
2025-08-05 09:33:51,780 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/ha, method: GET, headers: None
2025-08-05 09:33:51,780 INFO params: None
2025-08-05 09:33:51,780 INFO User: 1-click-nutanix
2025-08-05 09:33:51,780 INFO payload: None
2025-08-05 09:33:51,780 INFO files: None
2025-08-05 09:33:51,780 INFO timeout: 30
2025-08-05 09:33:53,121 INFO HA Reservation is already in desired state.
2025-08-05 09:33:53,126 INFO Ha: Done
2025-08-05 09:33:53,141 INFO ****************************************************************************************************
2025-08-05 09:33:53,141 INFO *                                                                                                  *
2025-08-05 09:33:53,141 INFO *                                    Configuring AuthConfig...                                     *
2025-08-05 09:33:53,141 INFO *                                                                                                  *
2025-08-05 09:33:53,141 INFO ****************************************************************************************************
2025-08-05 09:33:56,134 INFO Configuring LDAP...
2025-08-05 09:33:56,135 INFO Desired auth config name: ikead2
2025-08-05 09:33:56,135 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 09:33:56,135 INFO params: None
2025-08-05 09:33:56,135 INFO User: 1-click-nutanix
2025-08-05 09:33:56,135 INFO payload: None
2025-08-05 09:33:56,135 INFO files: None
2025-08-05 09:33:56,135 INFO timeout: 30
2025-08-05 09:33:57,370 INFO PE is already Joined towards 'ikead2.com'.
2025-08-05 09:33:57,380 INFO Username is already set towards '<EMAIL>'
2025-08-05 09:33:57,390 INFO Configuring the Role Mappings for 'ikead2'
2025-08-05 09:33:57,390 INFO Getting Groups that need to be bound.
2025-08-05 09:33:57,390 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 09:33:57,390 INFO params: None
2025-08-05 09:33:57,390 INFO User: 1-click-nutanix
2025-08-05 09:33:57,390 INFO payload: None
2025-08-05 09:33:57,390 INFO files: None
2025-08-05 09:33:57,390 INFO timeout: 30
2025-08-05 09:33:58,674 INFO We are setting up RoleMappings using group: '['NXAdmin']'
2025-08-05 09:33:58,674 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/authconfig/directories/ikead2/role_mappings, method: GET, headers: None
2025-08-05 09:33:58,675 INFO params: None
2025-08-05 09:33:58,675 INFO User: 1-click-nutanix
2025-08-05 09:33:58,675 INFO payload: None
2025-08-05 09:33:58,675 INFO files: None
2025-08-05 09:33:58,675 INFO timeout: 30
2025-08-05 09:33:59,871 INFO Role ROLE_USER_ADMIN with entity type GROUP is already in desired state.
2025-08-05 09:33:59,871 INFO Role ROLE_CLUSTER_ADMIN with entity type GROUP is already in desired state.
2025-08-05 09:33:59,871 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 09:33:59,871 INFO params: None
2025-08-05 09:33:59,871 INFO User: 1-click-nutanix
2025-08-05 09:33:59,871 INFO payload: None
2025-08-05 09:33:59,871 INFO files: None
2025-08-05 09:33:59,872 INFO timeout: 30
2025-08-05 09:34:01,144 INFO Repeating for Central PE
2025-08-05 09:34:01,153 INFO Configuring LDAP...
2025-08-05 09:34:01,153 INFO Desired auth config name: ikead2
2025-08-05 09:34:01,153 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 09:34:01,153 INFO params: None
2025-08-05 09:34:01,153 INFO User: 1-click-nutanix
2025-08-05 09:34:01,153 INFO payload: None
2025-08-05 09:34:01,153 INFO files: None
2025-08-05 09:34:01,153 INFO timeout: 30
2025-08-05 09:34:02,613 INFO PC is already Joined towards 'ikead2.com'.
2025-08-05 09:34:02,625 INFO Username is already set towards '<EMAIL>'
2025-08-05 09:34:02,639 INFO Configuring the Role Mappings for 'ikead2'
2025-08-05 09:34:02,639 INFO Getting Groups that need to be bound.
2025-08-05 09:34:02,639 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/PrismGateway/services/rest/v2.0/authconfig, method: GET, headers: None
2025-08-05 09:34:02,639 INFO params: None
2025-08-05 09:34:02,639 INFO User: 1-click-nutanix
2025-08-05 09:34:02,639 INFO payload: None
2025-08-05 09:34:02,639 INFO files: None
2025-08-05 09:34:02,639 INFO timeout: 30
2025-08-05 09:34:04,092 INFO We are setting up RoleMappings using group: '['NXAdmin']'
2025-08-05 09:34:04,092 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/PrismGateway/services/rest/v1/authconfig/directories/IKEAD2/role_mappings, method: GET, headers: None
2025-08-05 09:34:04,092 INFO params: None
2025-08-05 09:34:04,092 INFO User: 1-click-nutanix
2025-08-05 09:34:04,092 INFO payload: None
2025-08-05 09:34:04,092 INFO files: None
2025-08-05 09:34:04,092 INFO timeout: 30
2025-08-05 09:34:05,389 INFO Role ROLE_USER_ADMIN with entity type GROUP doesn't exist, creating...
2025-08-05 09:34:05,389 INFO Calling restapi, URL: https://SSP-DHD2-NTX.ikead2.com:9440/PrismGateway/services/rest/v1/authconfig/directories/IKEAD2/role_mappings?&entityType=GROUP&role=ROLE_USER_ADMIN, method: POST, headers: None
2025-08-05 09:34:05,389 INFO params: None
2025-08-05 09:34:05,389 INFO User: 1-click-nutanix
2025-08-05 09:34:05,389 INFO payload: {'directoryName': 'IKEAD2', 'role': 'ROLE_USER_ADMIN', 'entityType': 'GROUP', 'entityValues': ['NXAdmin']}
2025-08-05 09:34:05,389 INFO files: None
2025-08-05 09:34:05,389 INFO timeout: 30
2025-08-05 09:34:06,560 WARNING Response content: b'{"message":"Access is denied"}'
2025-08-05 09:34:06,560 WARNING API response is not ok, going to do the 2 retry...
2025-08-05 09:34:07,717 WARNING Response content: b'{"message":"Access is denied"}'
2025-08-05 09:34:07,717 WARNING API response is not ok, going to do the 3 retry...
2025-08-05 09:34:08,889 WARNING Response content: b'{"message":"Access is denied"}'
2025-08-05 09:34:08,889 WARNING API response is not ok, going to do the 4 retry...
2025-08-05 09:34:10,097 WARNING Response content: b'{"message":"Access is denied"}'
2025-08-05 09:34:10,097 WARNING API response is not ok, going to do the 5 retry...
2025-08-05 09:34:11,258 WARNING Response content: b'{"message":"Access is denied"}'
2025-08-05 09:34:11,276 ERROR ['Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 91, in task_process\n    c(self.pe, self.logger, self.db_logger, self.facility_type).configure()\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 1019, in configure\n    self.configure_role_mapping(px_mode="PC")\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 1191, in configure_role_mapping\n    self.rest_ac.add_role_mapping(ac_name, desired_entity_values, desired_role)\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\pe_components.py", line 311, in add_role_mapping\n    raise flaskex.BadGateway(f"Failed to add role mappings! Reason: {data}")\n', 'werkzeug.exceptions.BadGateway: 502 Bad Gateway: Failed to add role mappings! Reason: 502 Bad Gateway: Out of retry times when calling https://SSP-DHD2-NTX.ikead2.com:9440/PrismGateway/services/rest/v1/authconfig/directories/IKEAD2/role_mappings?&entityType=GROUP&role=ROLE_USER_ADMIN. Response: b\'{"message":"Access is denied"}\'\n']
2025-08-05 09:34:11,286 WARNING Failed on current step, but will continue the other steps...
2025-08-05 09:34:11,288 INFO ****************************************************************************************************
2025-08-05 09:34:11,288 INFO *                                                                                                  *
2025-08-05 09:34:11,288 INFO *                                    Configuring AhvHostname...                                    *
2025-08-05 09:34:11,288 INFO *                                                                                                  *
2025-08-05 09:34:11,288 INFO ****************************************************************************************************
2025-08-05 09:34:13,879 INFO Configure AHV Hostnames
2025-08-05 09:34:13,879 INFO Desired State 'ahv_host_names' has been disabled inside this site profile.
2025-08-05 09:34:13,879 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:34:13,879 INFO params: None
2025-08-05 09:34:13,879 INFO User: 1-click-nutanix
2025-08-05 09:34:13,879 INFO payload: None
2025-08-05 09:34:13,879 INFO files: None
2025-08-05 09:34:13,880 INFO timeout: 30
2025-08-05 09:34:15,134 INFO Getting host list from RETSEELM-NXC000.
2025-08-05 09:34:15,134 INFO Got the host list from RETSEELM-NXC000.
2025-08-05 09:34:15,134 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:34:15,135 INFO params: None
2025-08-05 09:34:15,135 INFO User: <EMAIL>
2025-08-05 09:34:15,135 INFO payload: None
2025-08-05 09:34:15,135 INFO files: None
2025-08-05 09:34:15,135 INFO timeout: 30
2025-08-05 09:34:16,524 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 09:34:16,525 INFO params: None
2025-08-05 09:34:16,525 INFO User: <EMAIL>
2025-08-05 09:34:16,525 INFO payload: None
2025-08-05 09:34:16,525 INFO files: None
2025-08-05 09:34:16,525 INFO timeout: 30
2025-08-05 09:34:17,630 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:34:17,630 INFO params: None
2025-08-05 09:34:17,630 INFO User: <EMAIL>
2025-08-05 09:34:17,630 INFO payload: None
2025-08-05 09:34:17,630 INFO files: None
2025-08-05 09:34:17,631 INFO timeout: 30
2025-08-05 09:34:18,588 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 09:34:18,588 INFO params: None
2025-08-05 09:34:18,588 INFO User: <EMAIL>
2025-08-05 09:34:18,588 INFO payload: None
2025-08-05 09:34:18,588 INFO files: None
2025-08-05 09:34:18,588 INFO timeout: 30
2025-08-05 09:34:19,710 INFO Current node is good, skipping...
2025-08-05 09:34:19,710 INFO Current node is good, skipping...
2025-08-05 09:34:19,711 INFO Current node is good, skipping...
2025-08-05 09:34:19,724 INFO AhvHostname: Done
2025-08-05 09:34:19,735 INFO ****************************************************************************************************
2025-08-05 09:34:19,736 INFO *                                                                                                  *
2025-08-05 09:34:19,736 INFO *                                    Configuring CvmHostname...                                    *
2025-08-05 09:34:19,736 INFO *                                                                                                  *
2025-08-05 09:34:19,736 INFO ****************************************************************************************************
2025-08-05 09:34:23,269 INFO Configure CVM Hostnames
2025-08-05 09:34:23,270 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:34:23,270 INFO params: None
2025-08-05 09:34:23,271 INFO User: 1-click-nutanix
2025-08-05 09:34:23,271 INFO payload: None
2025-08-05 09:34:23,271 INFO files: None
2025-08-05 09:34:23,271 INFO timeout: 30
2025-08-05 09:34:24,524 INFO Getting host list from RETSEELM-NXC000.
2025-08-05 09:34:24,524 INFO Got the host list from RETSEELM-NXC000.
2025-08-05 09:34:24,524 INFO Desired State 'cvm_host_names' has been disabled inside this site profile.
2025-08-05 09:34:24,525 INFO Getting VM list from RETSEELM-NXC000.
2025-08-05 09:34:24,525 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/vms, method: GET, headers: None
2025-08-05 09:34:24,525 INFO params: None
2025-08-05 09:34:24,525 INFO User: 1-click-nutanix
2025-08-05 09:34:24,525 INFO payload: None
2025-08-05 09:34:24,525 INFO files: None
2025-08-05 09:34:24,525 INFO timeout: 30
2025-08-05 09:34:25,864 INFO Got the VM list from RETSEELM-NXC000.
2025-08-05 09:34:25,864 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NXC000.IKEAD2.COM', method: GET, headers: None
2025-08-05 09:34:25,864 INFO params: None
2025-08-05 09:34:25,864 INFO User: <EMAIL>
2025-08-05 09:34:25,864 INFO payload: None
2025-08-05 09:34:25,865 INFO files: None
2025-08-05 09:34:25,865 INFO timeout: 30
2025-08-05 09:34:26,847 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=subnet_id='91747', method: GET, headers: None
2025-08-05 09:34:26,848 INFO params: None
2025-08-05 09:34:26,848 INFO User: <EMAIL>
2025-08-05 09:34:26,848 INFO payload: None
2025-08-05 09:34:26,849 INFO files: None
2025-08-05 09:34:26,849 INFO timeout: 30
2025-08-05 09:34:27,903 INFO Working with 'RETSEELM-NX7001CVM'
2025-08-05 09:34:27,904 INFO The name to set to NTX cvm: 'NTNX-RETSEELM-NX7001-CVM'
2025-08-05 09:34:27,904 INFO Current node name on NTX is equal to IPAM, skipping...
2025-08-05 09:34:27,904 INFO Working with 'RETSEELM-NX7002CVM'
2025-08-05 09:34:27,904 INFO The name to set to NTX cvm: 'NTNX-RETSEELM-NX7002-CVM'
2025-08-05 09:34:27,904 INFO Current node name on NTX is equal to IPAM, skipping...
2025-08-05 09:34:27,904 INFO Working with 'RETSEELM-NX7003CVM'
2025-08-05 09:34:27,904 INFO The name to set to NTX cvm: 'NTNX-RETSEELM-NX7003-CVM'
2025-08-05 09:34:27,905 INFO Current node name on NTX is equal to IPAM, skipping...
2025-08-05 09:34:27,926 INFO CvmHostname: Done
2025-08-05 09:34:27,941 INFO ****************************************************************************************************
2025-08-05 09:34:27,941 INFO *                                                                                                  *
2025-08-05 09:34:27,941 INFO *                                        Configuring Oob...                                        *
2025-08-05 09:34:27,941 INFO *                                                                                                  *
2025-08-05 09:34:27,941 INFO ****************************************************************************************************
2025-08-05 09:34:35,717 INFO oneview_scope is DPC_EU_D2, myscope is /rest/scopes/dc4d1e10-ba24-44e9-97cc-3f45c31f7ff1
2025-08-05 09:34:43,373 INFO Desired State 'oob_health' has been disabled inside this site profile.
2025-08-05 09:34:43,373 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-05 09:34:43,373 INFO params: None
2025-08-05 09:34:43,373 INFO User: 1-click-nutanix
2025-08-05 09:34:43,374 INFO payload: None
2025-08-05 09:34:43,374 INFO files: None
2025-08-05 09:34:43,374 INFO timeout: 30
2025-08-05 09:34:45,184 INFO Getting host list from RETSEELM-NXC000.
2025-08-05 09:34:45,184 INFO Got the host list from RETSEELM-NXC000.
2025-08-05 09:34:45,184 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NX7001oob.ikead2.com', method: GET, headers: None
2025-08-05 09:34:45,184 INFO params: None
2025-08-05 09:34:45,184 INFO User: <EMAIL>
2025-08-05 09:34:45,184 INFO payload: None
2025-08-05 09:34:45,185 INFO files: None
2025-08-05 09:34:45,185 INFO timeout: 30
2025-08-05 09:34:46,211 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_block_subnet_list?WHERE=subnet_id%3D%2791749%27, method: GET, headers: None
2025-08-05 09:34:46,211 INFO params: None
2025-08-05 09:34:46,212 INFO User: <EMAIL>
2025-08-05 09:34:46,212 INFO payload: None
2025-08-05 09:34:46,212 INFO files: None
2025-08-05 09:34:46,212 INFO timeout: 30
2025-08-05 09:34:47,176 INFO ****************************************************************************************************
2025-08-05 09:34:47,176 INFO *                                                                                                  *
2025-08-05 09:34:47,177 INFO *                                 Checking OOB for RETSEELM-NX7001                                 *
2025-08-05 09:34:47,177 INFO *                                                                                                  *
2025-08-05 09:34:47,177 INFO ****************************************************************************************************
2025-08-05 09:34:47,177 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NX7001oob.ikead2.com', method: GET, headers: None
2025-08-05 09:34:47,177 INFO params: None
2025-08-05 09:34:47,177 INFO User: <EMAIL>
2025-08-05 09:34:47,177 INFO payload: None
2025-08-05 09:34:47,177 INFO files: None
2025-08-05 09:34:47,177 INFO timeout: 30
2025-08-05 09:34:48,275 INFO SSH connecting to ***********, this is the '1' try.
2025-08-05 09:34:54,509 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:34:54,509 INFO SSH connecting to ***********, this is the '2' try.
2025-08-05 09:35:00,701 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:35:00,701 INFO SSH connecting to ***********, this is the '3' try.
2025-08-05 09:35:06,896 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:35:06,896 INFO SSH connecting to ***********, this is the '4' try.
2025-08-05 09:35:13,105 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:35:13,106 INFO SSH connecting to ***********, this is the '5' try.
2025-08-05 09:35:19,274 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:35:19,275 ERROR We've retried for 5 times, still not connected, aborting.
2025-08-05 09:35:19,275 ERROR Failed to connect to ***********
2025-08-05 09:35:19,275 INFO OOB for RETSEELM-NX7001 is reachable.
2025-08-05 09:35:19,276 INFO Checking iLO status...
2025-08-05 09:35:20,583 INFO ILO status is good.
2025-08-05 09:35:20,584 INFO Checking ILO name and DNS name for RETSEELM-NX7001
2025-08-05 09:35:20,584 INFO Setting server name and DNS name for RETSEELM-NX7001OOB
2025-08-05 09:35:20,584 INFO Retrieving RedFish Network Stack '*************'
2025-08-05 09:35:20,584 INFO Calling restapi, URL: https://*************/redfish/v1/SessionService/Sessions, method: POST, headers: {'Content-Type': 'application/json;charset=UTF-8'}
2025-08-05 09:35:20,584 INFO params: None
2025-08-05 09:35:20,585 INFO User: administrator
2025-08-05 09:35:20,585 INFO payload: {'UserName': 'administrator', 'Password': '*****'}
2025-08-05 09:35:20,585 INFO files: None
2025-08-05 09:35:20,585 INFO timeout: 5
2025-08-05 09:35:22,990 INFO Calling restapi, URL: https://*************/redfish/v1/Systems/1/, method: GET, headers: {'X-Auth-Token': '75f44d87465db8ec96569baa4944db35'}
2025-08-05 09:35:22,991 INFO params: None
2025-08-05 09:35:22,991 INFO User: administrator
2025-08-05 09:35:22,991 INFO payload: None
2025-08-05 09:35:22,992 INFO files: None
2025-08-05 09:35:22,992 INFO timeout: None
2025-08-05 09:35:24,362 INFO Get ILO HostName successfull.
2025-08-05 09:35:24,363 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/EthernetInterfaces/1/, method: GET, headers: None
2025-08-05 09:35:24,363 INFO params: None
2025-08-05 09:35:24,364 INFO User: administrator
2025-08-05 09:35:24,364 INFO payload: None
2025-08-05 09:35:24,364 INFO files: None
2025-08-05 09:35:24,364 INFO timeout: None
2025-08-05 09:35:25,542 INFO Got the response with OK
2025-08-05 09:35:25,543 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:35:25,543 INFO params: None
2025-08-05 09:35:25,543 INFO User: administrator
2025-08-05 09:35:25,543 INFO payload: None
2025-08-05 09:35:25,544 INFO files: None
2025-08-05 09:35:25,544 INFO timeout: None
2025-08-05 09:35:26,760 INFO Got the response with OK
2025-08-05 09:35:26,763 INFO Group Already exists.
2025-08-05 09:35:27,335 INFO LDAP Binding already in desired state.
2025-08-05 09:35:27,336 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:35:27,336 INFO params: None
2025-08-05 09:35:27,337 INFO User: administrator
2025-08-05 09:35:27,337 INFO payload: None
2025-08-05 09:35:27,337 INFO files: None
2025-08-05 09:35:27,338 INFO timeout: None
2025-08-05 09:35:28,651 INFO Got the response with OK
2025-08-05 09:35:28,652 INFO Calling restapi, URL: https://*************/redfish/v1/AccountService/Roles/dirgroup3a7c6167d73b780ee5fe83de, method: GET, headers: None
2025-08-05 09:35:28,652 INFO params: None
2025-08-05 09:35:28,652 INFO User: administrator
2025-08-05 09:35:28,653 INFO payload: None
2025-08-05 09:35:28,653 INFO files: None
2025-08-05 09:35:28,653 INFO timeout: None
2025-08-05 09:35:29,862 INFO Got the response with OK
2025-08-05 09:35:29,864 INFO Privileges are already correct.
2025-08-05 09:35:29,864 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/EthernetInterfaces/1, method: GET, headers: None
2025-08-05 09:35:29,864 INFO params: None
2025-08-05 09:35:29,864 INFO User: administrator
2025-08-05 09:35:29,864 INFO payload: None
2025-08-05 09:35:29,864 INFO files: None
2025-08-05 09:35:29,864 INFO timeout: None
2025-08-05 09:35:30,991 INFO Got the response with OK
2025-08-05 09:35:30,992 INFO DNS is already correct.
2025-08-05 09:35:30,993 INFO Checking NTP configuration...
2025-08-05 09:35:30,993 INFO Calling restapi, URL: https://*************/redfish/v1/Managers/1/DateTime, method: GET, headers: None
2025-08-05 09:35:30,993 INFO params: None
2025-08-05 09:35:30,993 INFO User: administrator
2025-08-05 09:35:30,993 INFO payload: None
2025-08-05 09:35:30,993 INFO files: None
2025-08-05 09:35:30,993 INFO timeout: None
2025-08-05 09:35:32,289 INFO Got the response with OK
2025-08-05 09:35:32,290 INFO Current DateTime configuration: {'@odata.context': '/redfish/v1/$metadata#HpeiLODateTime.HpeiLODateTime', '@odata.etag': 'W/"1BF57873"', '@odata.id': '/redfish/v1/Managers/1/DateTime', '@odata.type': '#HpeiLODateTime.v2_0_0.HpeiLODateTime', 'Id': 'DateTime', 'ConfigurationSettings': 'Current', 'DateTime': '2025-08-05T01:35:31Z', 'Links': {'EthernetNICs': {'@odata.id': '/redfish/v1/Managers/1/EthernetInterfaces'}}, 'NTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'Name': 'iLO Date and Time Settings', 'PropagateTimeToHost': False, 'StaticNTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'TimeZone': {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, 'TimeZoneList': [{'Index': 0, 'Name': 'International Date Line West', 'UtcOffset': '-12:00', 'Value': 'GMT+12:00'}, {'Index': 1, 'Name': 'Midway Island, Samoa', 'UtcOffset': '-11:00', 'Value': 'SST+11:00'}, {'Index': 2, 'Name': 'Hawaii', 'UtcOffset': '-10:00', 'Value': 'HST+10:00'}, {'Index': 3, 'Name': 'Marquesas', 'UtcOffset': '-09:30', 'Value': 'MART+9:30'}, {'Index': 4, 'Name': 'Alaska', 'UtcOffset': '-09:00', 'Value': 'AKST+9:00AKDT+08:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 5, 'Name': 'Pacific Time(US & Canada), Tijuana, Portland', 'UtcOffset': '-08:00', 'Value': 'PST+8:00PDT+07:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 6, 'Name': 'Arizona, Chihuahua, La Paz, Mazatlan, Mountain Time (US & Canad', 'UtcOffset': '-07:00', 'Value': 'MST+7:00MDT+06:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 7, 'Name': 'Central America, Central Time(US & Canada)', 'UtcOffset': '-06:00', 'Value': 'CST+6:00CDT+05:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 8, 'Name': 'Bogota, Lima, Quito, Eastern Time(US & Canada)', 'UtcOffset': '-05:00', 'Value': 'EST+5:00EDT+04:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 9, 'Name': 'Caracas, Georgetown', 'UtcOffset': '-04:00', 'Value': 'VET+4:00'}, {'Index': 10, 'Name': 'Atlantic Time(Canada), Santiago', 'UtcOffset': '-04:00', 'Value': 'AST+4:00ADT+03:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 11, 'Name': 'Newfoundland', 'UtcOffset': '-03:30', 'Value': 'NST+3:30NDT+02:30:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 12, 'Name': 'Brasilia, Buenos Aires, Greenland', 'UtcOffset': '-03:00', 'Value': 'ART+3:00'}, {'Index': 13, 'Name': 'Mid-Atlantic', 'UtcOffset': '-02:00', 'Value': 'GST+2:00'}, {'Index': 14, 'Name': 'Azores, Cape Verde Is.', 'UtcOffset': '-01:00', 'Value': 'CVT+1:00'}, {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, {'Index': 16, 'Name': 'Dublin, London', 'UtcOffset': '+00:00', 'Value': 'WET-0WEST-1,M3.5.0/01:00:00,M10.5.0/02:00:00'}, {'Index': 17, 'Name': 'Amsterdam, Berlin, Bern, Rome, Paris, West Central Africa', 'UtcOffset': '+01:00', 'Value': 'CET-1:00CEST-02:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 18, 'Name': 'Athens, Bucharest, Cairo, Jerusalem', 'UtcOffset': '+02:00', 'Value': 'EET-2:00EEST-03:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 19, 'Name': 'Baghdad, Kuwait, Riyadh, Moscow, Istanbul, Nairobi', 'UtcOffset': '+03:00', 'Value': 'AST-3:00'}, {'Index': 20, 'Name': 'Tehran', 'UtcOffset': '+03:30', 'Value': 'IRST-3:30IRDT-04:30:00,80/00:00:00,264/00:00:00'}, {'Index': 21, 'Name': 'Abu Dhabi, Muscat, Baku, Tbilisi, Yerevan', 'UtcOffset': '+04:00', 'Value': 'GST-4:00'}, {'Index': 22, 'Name': 'Kabul', 'UtcOffset': '+04:30', 'Value': 'AFT-4:30'}, {'Index': 23, 'Name': 'Ekaterinburg, Islamabad, Karachi, Tashkent', 'UtcOffset': '+05:00', 'Value': 'YEKT-5:00'}, {'Index': 24, 'Name': 'Chennai, Kolkata, Mumbai, New Delhi', 'UtcOffset': '+05:30', 'Value': 'IST-5:30'}, {'Index': 25, 'Name': 'Kathmandu', 'UtcOffset': '+05:45', 'Value': 'NPT-5:45'}, {'Index': 26, 'Name': 'Almaty, Dhaka, Sri Jayawardenepura', 'UtcOffset': '+06:00', 'Value': 'ALMT-6:00'}, {'Index': 27, 'Name': 'Rangoon', 'UtcOffset': '+06:30', 'Value': 'MMT-6:30'}, {'Index': 28, 'Name': 'Bangkok, Hanio, Jakarta, Novosibirsk, Astana, Krasnoyarsk', 'UtcOffset': '+07:00', 'Value': 'ICT-7:00'}, {'Index': 29, 'Name': 'Beijing, Chongqing, Hong Kong, Urumqi, Taipei, Perth', 'UtcOffset': '+08:00', 'Value': 'CST-8:00'}, {'Index': 30, 'Name': 'Eucla', 'UtcOffset': '+08:45', 'Value': 'ACWST-08:45'}, {'Index': 31, 'Name': 'Osaka, Sapporo, Tokyo, Seoul, Yakutsk', 'UtcOffset': '+09:00', 'Value': 'JST-9:00'}, {'Index': 32, 'Name': 'Adelaide, Darwin', 'UtcOffset': '+09:30', 'Value': 'ACST-9:30ACDT-10:30:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 33, 'Name': 'Canberra, Melbourne, Sydney, Guam, Hobart, Vladivostok', 'UtcOffset': '+10:00', 'Value': 'AEST-10:00AEDT-11:00:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 34, 'Name': 'Lord Howe', 'UtcOffset': '+10:30', 'Value': 'LHST-10:30LHDT11:00'}, {'Index': 35, 'Name': 'Chatham', 'UtcOffset': '+10:45', 'Value': 'CHAST-10:45CHADT-11:45'}, {'Index': 36, 'Name': 'Magadan, Solomon Is., New Caledonia', 'UtcOffset': '+11:00', 'Value': 'MAGT-11:00'}, {'Index': 37, 'Name': 'Auckland, Wellington, Fiji, Kamchatka, Marshall Is.', 'UtcOffset': '+12:00', 'Value': 'NZST-12:00NZDT-13:00:00,M9.5.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 38, 'Name': "Nuku'alofa", 'UtcOffset': '+13:00', 'Value': 'TKT-13:00'}, {'Index': 39, 'Name': 'Line Islands', 'UtcOffset': '+14:00', 'Value': 'LINT-14:00'}, {'Index': 40, 'Name': 'Unspecified Time Zone', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}]}
2025-08-05 09:35:32,291 INFO NTP configuration is already in desired state
2025-08-05 09:35:32,291 INFO No configuration changes made, skipping ILO reset.
2025-08-05 09:35:32,291 INFO Oneview part
2025-08-05 09:35:32,291 INFO Check if the OOB is in oneview
2025-08-05 09:35:33,375 INFO OOB is in oneview and state is normal
2025-08-05 09:35:33,400 INFO OOB configuration is done for RETSEELM-NX7001.
2025-08-05 09:35:33,413 INFO ****************************************************************************************************
2025-08-05 09:35:33,413 INFO *                                                                                                  *
2025-08-05 09:35:33,414 INFO *                                 Checking OOB for RETSEELM-NX7002                                 *
2025-08-05 09:35:33,414 INFO *                                                                                                  *
2025-08-05 09:35:33,414 INFO ****************************************************************************************************
2025-08-05 09:35:33,414 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NX7002oob.ikead2.com', method: GET, headers: None
2025-08-05 09:35:33,414 INFO params: None
2025-08-05 09:35:33,414 INFO User: <EMAIL>
2025-08-05 09:35:33,414 INFO payload: None
2025-08-05 09:35:33,414 INFO files: None
2025-08-05 09:35:33,414 INFO timeout: 30
2025-08-05 09:35:34,831 INFO SSH connecting to ***********, this is the '1' try.
2025-08-05 09:35:41,215 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:35:41,215 INFO SSH connecting to ***********, this is the '2' try.
2025-08-05 09:35:47,572 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:35:47,572 INFO SSH connecting to ***********, this is the '3' try.
2025-08-05 09:35:53,862 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:35:53,862 INFO SSH connecting to ***********, this is the '4' try.
2025-08-05 09:36:00,226 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:36:00,226 INFO SSH connecting to ***********, this is the '5' try.
2025-08-05 09:36:06,554 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-08-05 09:36:06,554 ERROR We've retried for 5 times, still not connected, aborting.
2025-08-05 09:36:06,554 ERROR Failed to connect to ***********
2025-08-05 09:36:06,555 INFO OOB for RETSEELM-NX7002 is reachable.
2025-08-05 09:36:06,555 INFO Checking iLO status...
2025-08-05 09:36:07,880 INFO ILO status is good.
2025-08-05 09:36:07,880 INFO Checking ILO name and DNS name for RETSEELM-NX7002
2025-08-05 09:36:07,881 INFO Setting server name and DNS name for RETSEELM-NX7002OOB
2025-08-05 09:36:07,881 INFO Retrieving RedFish Network Stack '***********30'
2025-08-05 09:36:07,881 INFO Calling restapi, URL: https://***********30/redfish/v1/SessionService/Sessions, method: POST, headers: {'Content-Type': 'application/json;charset=UTF-8'}
2025-08-05 09:36:07,881 INFO params: None
2025-08-05 09:36:07,882 INFO User: administrator
2025-08-05 09:36:07,882 INFO payload: {'UserName': 'administrator', 'Password': '*****'}
2025-08-05 09:36:07,882 INFO files: None
2025-08-05 09:36:07,882 INFO timeout: 5
2025-08-05 09:36:10,234 INFO Calling restapi, URL: https://***********30/redfish/v1/Systems/1/, method: GET, headers: {'X-Auth-Token': '1a9cb9cccd0c9b6ce7f53045ae712b1a'}
2025-08-05 09:36:10,235 INFO params: None
2025-08-05 09:36:10,235 INFO User: administrator
2025-08-05 09:36:10,235 INFO payload: None
2025-08-05 09:36:10,235 INFO files: None
2025-08-05 09:36:10,235 INFO timeout: None
2025-08-05 09:36:11,508 INFO Get ILO HostName successfull.
2025-08-05 09:36:11,509 INFO Calling restapi, URL: https://***********30/redfish/v1/Managers/1/EthernetInterfaces/1/, method: GET, headers: None
2025-08-05 09:36:11,510 INFO params: None
2025-08-05 09:36:11,510 INFO User: administrator
2025-08-05 09:36:11,510 INFO payload: None
2025-08-05 09:36:11,510 INFO files: None
2025-08-05 09:36:11,510 INFO timeout: None
2025-08-05 09:36:12,651 INFO Got the response with OK
2025-08-05 09:36:12,653 INFO Calling restapi, URL: https://***********30/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:36:12,653 INFO params: None
2025-08-05 09:36:12,653 INFO User: administrator
2025-08-05 09:36:12,653 INFO payload: None
2025-08-05 09:36:12,653 INFO files: None
2025-08-05 09:36:12,653 INFO timeout: None
2025-08-05 09:36:13,852 INFO Got the response with OK
2025-08-05 09:36:13,854 INFO Group Already exists.
2025-08-05 09:36:14,414 INFO LDAP Binding already in desired state.
2025-08-05 09:36:14,414 INFO Calling restapi, URL: https://***********30/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:36:14,415 INFO params: None
2025-08-05 09:36:14,415 INFO User: administrator
2025-08-05 09:36:14,415 INFO payload: None
2025-08-05 09:36:14,415 INFO files: None
2025-08-05 09:36:14,415 INFO timeout: None
2025-08-05 09:36:16,563 INFO Got the response with OK
2025-08-05 09:36:16,565 INFO Calling restapi, URL: https://***********30/redfish/v1/AccountService/Roles/dirgroup3a7c6167d73b780ee5fe83de, method: GET, headers: None
2025-08-05 09:36:16,565 INFO params: None
2025-08-05 09:36:16,565 INFO User: administrator
2025-08-05 09:36:16,565 INFO payload: None
2025-08-05 09:36:16,565 INFO files: None
2025-08-05 09:36:16,565 INFO timeout: None
2025-08-05 09:36:18,101 INFO Got the response with OK
2025-08-05 09:36:18,103 INFO Privileges are already correct.
2025-08-05 09:36:18,103 INFO Calling restapi, URL: https://***********30/redfish/v1/Managers/1/EthernetInterfaces/1, method: GET, headers: None
2025-08-05 09:36:18,103 INFO params: None
2025-08-05 09:36:18,103 INFO User: administrator
2025-08-05 09:36:18,104 INFO payload: None
2025-08-05 09:36:18,104 INFO files: None
2025-08-05 09:36:18,104 INFO timeout: None
2025-08-05 09:36:19,542 INFO Got the response with OK
2025-08-05 09:36:19,543 INFO DNS is already correct.
2025-08-05 09:36:19,543 INFO Checking NTP configuration...
2025-08-05 09:36:19,543 INFO Calling restapi, URL: https://***********30/redfish/v1/Managers/1/DateTime, method: GET, headers: None
2025-08-05 09:36:19,543 INFO params: None
2025-08-05 09:36:19,543 INFO User: administrator
2025-08-05 09:36:19,544 INFO payload: None
2025-08-05 09:36:19,544 INFO files: None
2025-08-05 09:36:19,544 INFO timeout: None
2025-08-05 09:36:20,828 INFO Got the response with OK
2025-08-05 09:36:20,831 INFO Current DateTime configuration: {'@odata.context': '/redfish/v1/$metadata#HpeiLODateTime.HpeiLODateTime', '@odata.etag': 'W/"1BF57873"', '@odata.id': '/redfish/v1/Managers/1/DateTime', '@odata.type': '#HpeiLODateTime.v2_0_0.HpeiLODateTime', 'Id': 'DateTime', 'ConfigurationSettings': 'Current', 'DateTime': '2025-08-05T01:36:20Z', 'Links': {'EthernetNICs': {'@odata.id': '/redfish/v1/Managers/1/EthernetInterfaces'}}, 'NTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'Name': 'iLO Date and Time Settings', 'PropagateTimeToHost': False, 'StaticNTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'TimeZone': {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, 'TimeZoneList': [{'Index': 0, 'Name': 'International Date Line West', 'UtcOffset': '-12:00', 'Value': 'GMT+12:00'}, {'Index': 1, 'Name': 'Midway Island, Samoa', 'UtcOffset': '-11:00', 'Value': 'SST+11:00'}, {'Index': 2, 'Name': 'Hawaii', 'UtcOffset': '-10:00', 'Value': 'HST+10:00'}, {'Index': 3, 'Name': 'Marquesas', 'UtcOffset': '-09:30', 'Value': 'MART+9:30'}, {'Index': 4, 'Name': 'Alaska', 'UtcOffset': '-09:00', 'Value': 'AKST+9:00AKDT+08:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 5, 'Name': 'Pacific Time(US & Canada), Tijuana, Portland', 'UtcOffset': '-08:00', 'Value': 'PST+8:00PDT+07:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 6, 'Name': 'Arizona, Chihuahua, La Paz, Mazatlan, Mountain Time (US & Canad', 'UtcOffset': '-07:00', 'Value': 'MST+7:00MDT+06:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 7, 'Name': 'Central America, Central Time(US & Canada)', 'UtcOffset': '-06:00', 'Value': 'CST+6:00CDT+05:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 8, 'Name': 'Bogota, Lima, Quito, Eastern Time(US & Canada)', 'UtcOffset': '-05:00', 'Value': 'EST+5:00EDT+04:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 9, 'Name': 'Caracas, Georgetown', 'UtcOffset': '-04:00', 'Value': 'VET+4:00'}, {'Index': 10, 'Name': 'Atlantic Time(Canada), Santiago', 'UtcOffset': '-04:00', 'Value': 'AST+4:00ADT+03:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 11, 'Name': 'Newfoundland', 'UtcOffset': '-03:30', 'Value': 'NST+3:30NDT+02:30:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 12, 'Name': 'Brasilia, Buenos Aires, Greenland', 'UtcOffset': '-03:00', 'Value': 'ART+3:00'}, {'Index': 13, 'Name': 'Mid-Atlantic', 'UtcOffset': '-02:00', 'Value': 'GST+2:00'}, {'Index': 14, 'Name': 'Azores, Cape Verde Is.', 'UtcOffset': '-01:00', 'Value': 'CVT+1:00'}, {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, {'Index': 16, 'Name': 'Dublin, London', 'UtcOffset': '+00:00', 'Value': 'WET-0WEST-1,M3.5.0/01:00:00,M10.5.0/02:00:00'}, {'Index': 17, 'Name': 'Amsterdam, Berlin, Bern, Rome, Paris, West Central Africa', 'UtcOffset': '+01:00', 'Value': 'CET-1:00CEST-02:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 18, 'Name': 'Athens, Bucharest, Cairo, Jerusalem', 'UtcOffset': '+02:00', 'Value': 'EET-2:00EEST-03:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 19, 'Name': 'Baghdad, Kuwait, Riyadh, Moscow, Istanbul, Nairobi', 'UtcOffset': '+03:00', 'Value': 'AST-3:00'}, {'Index': 20, 'Name': 'Tehran', 'UtcOffset': '+03:30', 'Value': 'IRST-3:30IRDT-04:30:00,80/00:00:00,264/00:00:00'}, {'Index': 21, 'Name': 'Abu Dhabi, Muscat, Baku, Tbilisi, Yerevan', 'UtcOffset': '+04:00', 'Value': 'GST-4:00'}, {'Index': 22, 'Name': 'Kabul', 'UtcOffset': '+04:30', 'Value': 'AFT-4:30'}, {'Index': 23, 'Name': 'Ekaterinburg, Islamabad, Karachi, Tashkent', 'UtcOffset': '+05:00', 'Value': 'YEKT-5:00'}, {'Index': 24, 'Name': 'Chennai, Kolkata, Mumbai, New Delhi', 'UtcOffset': '+05:30', 'Value': 'IST-5:30'}, {'Index': 25, 'Name': 'Kathmandu', 'UtcOffset': '+05:45', 'Value': 'NPT-5:45'}, {'Index': 26, 'Name': 'Almaty, Dhaka, Sri Jayawardenepura', 'UtcOffset': '+06:00', 'Value': 'ALMT-6:00'}, {'Index': 27, 'Name': 'Rangoon', 'UtcOffset': '+06:30', 'Value': 'MMT-6:30'}, {'Index': 28, 'Name': 'Bangkok, Hanio, Jakarta, Novosibirsk, Astana, Krasnoyarsk', 'UtcOffset': '+07:00', 'Value': 'ICT-7:00'}, {'Index': 29, 'Name': 'Beijing, Chongqing, Hong Kong, Urumqi, Taipei, Perth', 'UtcOffset': '+08:00', 'Value': 'CST-8:00'}, {'Index': 30, 'Name': 'Eucla', 'UtcOffset': '+08:45', 'Value': 'ACWST-08:45'}, {'Index': 31, 'Name': 'Osaka, Sapporo, Tokyo, Seoul, Yakutsk', 'UtcOffset': '+09:00', 'Value': 'JST-9:00'}, {'Index': 32, 'Name': 'Adelaide, Darwin', 'UtcOffset': '+09:30', 'Value': 'ACST-9:30ACDT-10:30:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 33, 'Name': 'Canberra, Melbourne, Sydney, Guam, Hobart, Vladivostok', 'UtcOffset': '+10:00', 'Value': 'AEST-10:00AEDT-11:00:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 34, 'Name': 'Lord Howe', 'UtcOffset': '+10:30', 'Value': 'LHST-10:30LHDT11:00'}, {'Index': 35, 'Name': 'Chatham', 'UtcOffset': '+10:45', 'Value': 'CHAST-10:45CHADT-11:45'}, {'Index': 36, 'Name': 'Magadan, Solomon Is., New Caledonia', 'UtcOffset': '+11:00', 'Value': 'MAGT-11:00'}, {'Index': 37, 'Name': 'Auckland, Wellington, Fiji, Kamchatka, Marshall Is.', 'UtcOffset': '+12:00', 'Value': 'NZST-12:00NZDT-13:00:00,M9.5.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 38, 'Name': "Nuku'alofa", 'UtcOffset': '+13:00', 'Value': 'TKT-13:00'}, {'Index': 39, 'Name': 'Line Islands', 'UtcOffset': '+14:00', 'Value': 'LINT-14:00'}, {'Index': 40, 'Name': 'Unspecified Time Zone', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}]}
2025-08-05 09:36:20,831 INFO NTP configuration is already in desired state
2025-08-05 09:36:20,831 INFO No configuration changes made, skipping ILO reset.
2025-08-05 09:36:20,831 INFO Oneview part
2025-08-05 09:36:20,833 INFO Check if the OOB is in oneview
2025-08-05 09:36:21,916 INFO OOB is in oneview and state is normal
2025-08-05 09:36:21,933 INFO OOB configuration is done for RETSEELM-NX7002.
2025-08-05 09:36:21,952 INFO ****************************************************************************************************
2025-08-05 09:36:21,952 INFO *                                                                                                  *
2025-08-05 09:36:21,952 INFO *                                 Checking OOB for RETSEELM-NX7003                                 *
2025-08-05 09:36:21,953 INFO *                                                                                                  *
2025-08-05 09:36:21,953 INFO ****************************************************************************************************
2025-08-05 09:36:21,953 INFO Calling restapi, URL: https://IPAM.IKEA.COM/rest/ip_address_list?WHERE=name='RETSEELM-NX7003oob.ikead2.com', method: GET, headers: None
2025-08-05 09:36:21,953 INFO params: None
2025-08-05 09:36:21,953 INFO User: <EMAIL>
2025-08-05 09:36:21,953 INFO payload: None
2025-08-05 09:36:21,953 INFO files: None
2025-08-05 09:36:21,954 INFO timeout: 30
2025-08-05 09:36:22,996 INFO SSH connecting to ***********, this is the '1' try.
2025-08-05 09:36:25,828 INFO SSH connected to ***********.
2025-08-05 09:36:26,960 INFO Sending 'sudo ipmitool lan print | grep "IP Address " | grep -v "Source"
' to the server.
2025-08-05 09:36:29,464 INFO OOB for RETSEELM-NX7003 is reachable.
2025-08-05 09:36:29,464 INFO Checking iLO status...
2025-08-05 09:36:30,756 INFO ILO status is good.
2025-08-05 09:36:30,756 INFO Checking ILO name and DNS name for RETSEELM-NX7003
2025-08-05 09:36:30,756 INFO Setting server name and DNS name for RETSEELM-NX7003OOB
2025-08-05 09:36:30,757 INFO Retrieving RedFish Network Stack '***********31'
2025-08-05 09:36:30,757 INFO Calling restapi, URL: https://***********31/redfish/v1/SessionService/Sessions, method: POST, headers: {'Content-Type': 'application/json;charset=UTF-8'}
2025-08-05 09:36:30,757 INFO params: None
2025-08-05 09:36:30,757 INFO User: administrator
2025-08-05 09:36:30,757 INFO payload: {'UserName': 'administrator', 'Password': '*****'}
2025-08-05 09:36:30,757 INFO files: None
2025-08-05 09:36:30,757 INFO timeout: 5
2025-08-05 09:36:33,093 INFO Calling restapi, URL: https://***********31/redfish/v1/Systems/1/, method: GET, headers: {'X-Auth-Token': 'f96a07d19f9b4adb16149b216de20ada'}
2025-08-05 09:36:33,093 INFO params: None
2025-08-05 09:36:33,094 INFO User: administrator
2025-08-05 09:36:33,094 INFO payload: None
2025-08-05 09:36:33,094 INFO files: None
2025-08-05 09:36:33,094 INFO timeout: None
2025-08-05 09:36:34,379 INFO Get ILO HostName successfull.
2025-08-05 09:36:34,380 INFO Calling restapi, URL: https://***********31/redfish/v1/Managers/1/EthernetInterfaces/1/, method: GET, headers: None
2025-08-05 09:36:34,380 INFO params: None
2025-08-05 09:36:34,380 INFO User: administrator
2025-08-05 09:36:34,380 INFO payload: None
2025-08-05 09:36:34,380 INFO files: None
2025-08-05 09:36:34,380 INFO timeout: None
2025-08-05 09:36:35,499 INFO Got the response with OK
2025-08-05 09:36:35,502 INFO Calling restapi, URL: https://***********31/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:36:35,502 INFO params: None
2025-08-05 09:36:35,502 INFO User: administrator
2025-08-05 09:36:35,502 INFO payload: None
2025-08-05 09:36:35,503 INFO files: None
2025-08-05 09:36:35,503 INFO timeout: None
2025-08-05 09:36:36,650 INFO Got the response with OK
2025-08-05 09:36:36,652 INFO Group Already exists.
2025-08-05 09:36:37,226 INFO LDAP Binding already in desired state.
2025-08-05 09:36:37,226 INFO Calling restapi, URL: https://***********31/redfish/v1/AccountService, method: GET, headers: None
2025-08-05 09:36:37,227 INFO params: None
2025-08-05 09:36:37,227 INFO User: administrator
2025-08-05 09:36:37,227 INFO payload: None
2025-08-05 09:36:37,227 INFO files: None
2025-08-05 09:36:37,227 INFO timeout: None
2025-08-05 09:36:38,374 INFO Got the response with OK
2025-08-05 09:36:38,376 INFO Calling restapi, URL: https://***********31/redfish/v1/AccountService/Roles/dirgroup3a7c6167d73b780ee5fe83de, method: GET, headers: None
2025-08-05 09:36:38,376 INFO params: None
2025-08-05 09:36:38,376 INFO User: administrator
2025-08-05 09:36:38,376 INFO payload: None
2025-08-05 09:36:38,376 INFO files: None
2025-08-05 09:36:38,376 INFO timeout: None
2025-08-05 09:36:40,565 INFO Got the response with OK
2025-08-05 09:36:40,566 INFO Privileges are already correct.
2025-08-05 09:36:40,567 INFO Calling restapi, URL: https://***********31/redfish/v1/Managers/1/EthernetInterfaces/1, method: GET, headers: None
2025-08-05 09:36:40,567 INFO params: None
2025-08-05 09:36:40,567 INFO User: administrator
2025-08-05 09:36:40,567 INFO payload: None
2025-08-05 09:36:40,567 INFO files: None
2025-08-05 09:36:40,567 INFO timeout: None
2025-08-05 09:36:42,033 INFO Got the response with OK
2025-08-05 09:36:42,033 INFO DNS is already correct.
2025-08-05 09:36:42,034 INFO Checking NTP configuration...
2025-08-05 09:36:42,034 INFO Calling restapi, URL: https://***********31/redfish/v1/Managers/1/DateTime, method: GET, headers: None
2025-08-05 09:36:42,034 INFO params: None
2025-08-05 09:36:42,034 INFO User: administrator
2025-08-05 09:36:42,034 INFO payload: None
2025-08-05 09:36:42,034 INFO files: None
2025-08-05 09:36:42,034 INFO timeout: None
2025-08-05 09:36:43,517 INFO Got the response with OK
2025-08-05 09:36:43,518 INFO Current DateTime configuration: {'@odata.context': '/redfish/v1/$metadata#HpeiLODateTime.HpeiLODateTime', '@odata.etag': 'W/"1BF57873"', '@odata.id': '/redfish/v1/Managers/1/DateTime', '@odata.type': '#HpeiLODateTime.v2_0_0.HpeiLODateTime', 'Id': 'DateTime', 'ConfigurationSettings': 'Current', 'DateTime': '2025-08-05T01:36:43Z', 'Links': {'EthernetNICs': {'@odata.id': '/redfish/v1/Managers/1/EthernetInterfaces'}}, 'NTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'Name': 'iLO Date and Time Settings', 'PropagateTimeToHost': False, 'StaticNTPServers': ['ntp1-eu.ikea.com', 'ntp2-eu.ikea.com'], 'TimeZone': {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, 'TimeZoneList': [{'Index': 0, 'Name': 'International Date Line West', 'UtcOffset': '-12:00', 'Value': 'GMT+12:00'}, {'Index': 1, 'Name': 'Midway Island, Samoa', 'UtcOffset': '-11:00', 'Value': 'SST+11:00'}, {'Index': 2, 'Name': 'Hawaii', 'UtcOffset': '-10:00', 'Value': 'HST+10:00'}, {'Index': 3, 'Name': 'Marquesas', 'UtcOffset': '-09:30', 'Value': 'MART+9:30'}, {'Index': 4, 'Name': 'Alaska', 'UtcOffset': '-09:00', 'Value': 'AKST+9:00AKDT+08:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 5, 'Name': 'Pacific Time(US & Canada), Tijuana, Portland', 'UtcOffset': '-08:00', 'Value': 'PST+8:00PDT+07:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 6, 'Name': 'Arizona, Chihuahua, La Paz, Mazatlan, Mountain Time (US & Canad', 'UtcOffset': '-07:00', 'Value': 'MST+7:00MDT+06:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 7, 'Name': 'Central America, Central Time(US & Canada)', 'UtcOffset': '-06:00', 'Value': 'CST+6:00CDT+05:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 8, 'Name': 'Bogota, Lima, Quito, Eastern Time(US & Canada)', 'UtcOffset': '-05:00', 'Value': 'EST+5:00EDT+04:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 9, 'Name': 'Caracas, Georgetown', 'UtcOffset': '-04:00', 'Value': 'VET+4:00'}, {'Index': 10, 'Name': 'Atlantic Time(Canada), Santiago', 'UtcOffset': '-04:00', 'Value': 'AST+4:00ADT+03:00:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 11, 'Name': 'Newfoundland', 'UtcOffset': '-03:30', 'Value': 'NST+3:30NDT+02:30:00,M3.2.0/02:00:00,M11.1.0/02:00:00'}, {'Index': 12, 'Name': 'Brasilia, Buenos Aires, Greenland', 'UtcOffset': '-03:00', 'Value': 'ART+3:00'}, {'Index': 13, 'Name': 'Mid-Atlantic', 'UtcOffset': '-02:00', 'Value': 'GST+2:00'}, {'Index': 14, 'Name': 'Azores, Cape Verde Is.', 'UtcOffset': '-01:00', 'Value': 'CVT+1:00'}, {'Index': 15, 'Name': 'Greenwich Mean Time, Casablanca, Monrovia', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}, {'Index': 16, 'Name': 'Dublin, London', 'UtcOffset': '+00:00', 'Value': 'WET-0WEST-1,M3.5.0/01:00:00,M10.5.0/02:00:00'}, {'Index': 17, 'Name': 'Amsterdam, Berlin, Bern, Rome, Paris, West Central Africa', 'UtcOffset': '+01:00', 'Value': 'CET-1:00CEST-02:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 18, 'Name': 'Athens, Bucharest, Cairo, Jerusalem', 'UtcOffset': '+02:00', 'Value': 'EET-2:00EEST-03:00:00,M3.5.0/01:00:00,M10.5.0/01:00:00'}, {'Index': 19, 'Name': 'Baghdad, Kuwait, Riyadh, Moscow, Istanbul, Nairobi', 'UtcOffset': '+03:00', 'Value': 'AST-3:00'}, {'Index': 20, 'Name': 'Tehran', 'UtcOffset': '+03:30', 'Value': 'IRST-3:30IRDT-04:30:00,80/00:00:00,264/00:00:00'}, {'Index': 21, 'Name': 'Abu Dhabi, Muscat, Baku, Tbilisi, Yerevan', 'UtcOffset': '+04:00', 'Value': 'GST-4:00'}, {'Index': 22, 'Name': 'Kabul', 'UtcOffset': '+04:30', 'Value': 'AFT-4:30'}, {'Index': 23, 'Name': 'Ekaterinburg, Islamabad, Karachi, Tashkent', 'UtcOffset': '+05:00', 'Value': 'YEKT-5:00'}, {'Index': 24, 'Name': 'Chennai, Kolkata, Mumbai, New Delhi', 'UtcOffset': '+05:30', 'Value': 'IST-5:30'}, {'Index': 25, 'Name': 'Kathmandu', 'UtcOffset': '+05:45', 'Value': 'NPT-5:45'}, {'Index': 26, 'Name': 'Almaty, Dhaka, Sri Jayawardenepura', 'UtcOffset': '+06:00', 'Value': 'ALMT-6:00'}, {'Index': 27, 'Name': 'Rangoon', 'UtcOffset': '+06:30', 'Value': 'MMT-6:30'}, {'Index': 28, 'Name': 'Bangkok, Hanio, Jakarta, Novosibirsk, Astana, Krasnoyarsk', 'UtcOffset': '+07:00', 'Value': 'ICT-7:00'}, {'Index': 29, 'Name': 'Beijing, Chongqing, Hong Kong, Urumqi, Taipei, Perth', 'UtcOffset': '+08:00', 'Value': 'CST-8:00'}, {'Index': 30, 'Name': 'Eucla', 'UtcOffset': '+08:45', 'Value': 'ACWST-08:45'}, {'Index': 31, 'Name': 'Osaka, Sapporo, Tokyo, Seoul, Yakutsk', 'UtcOffset': '+09:00', 'Value': 'JST-9:00'}, {'Index': 32, 'Name': 'Adelaide, Darwin', 'UtcOffset': '+09:30', 'Value': 'ACST-9:30ACDT-10:30:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 33, 'Name': 'Canberra, Melbourne, Sydney, Guam, Hobart, Vladivostok', 'UtcOffset': '+10:00', 'Value': 'AEST-10:00AEDT-11:00:00,M10.1.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 34, 'Name': 'Lord Howe', 'UtcOffset': '+10:30', 'Value': 'LHST-10:30LHDT11:00'}, {'Index': 35, 'Name': 'Chatham', 'UtcOffset': '+10:45', 'Value': 'CHAST-10:45CHADT-11:45'}, {'Index': 36, 'Name': 'Magadan, Solomon Is., New Caledonia', 'UtcOffset': '+11:00', 'Value': 'MAGT-11:00'}, {'Index': 37, 'Name': 'Auckland, Wellington, Fiji, Kamchatka, Marshall Is.', 'UtcOffset': '+12:00', 'Value': 'NZST-12:00NZDT-13:00:00,M9.5.0/02:00:00,M4.1.0/02:00:00'}, {'Index': 38, 'Name': "Nuku'alofa", 'UtcOffset': '+13:00', 'Value': 'TKT-13:00'}, {'Index': 39, 'Name': 'Line Islands', 'UtcOffset': '+14:00', 'Value': 'LINT-14:00'}, {'Index': 40, 'Name': 'Unspecified Time Zone', 'UtcOffset': '+00:00', 'Value': 'GMT-0'}]}
2025-08-05 09:36:43,519 INFO NTP configuration is already in desired state
2025-08-05 09:36:43,519 INFO No configuration changes made, skipping ILO reset.
2025-08-05 09:36:43,519 INFO Oneview part
2025-08-05 09:36:43,519 INFO Check if the OOB is in oneview
2025-08-05 09:36:44,631 INFO OOB is in oneview and state is normal
2025-08-05 09:36:44,644 INFO OOB configuration is done for RETSEELM-NX7003.
2025-08-05 09:36:44,673 INFO Oob: Done
2025-08-05 09:36:44,691 INFO ****************************************************************************************************
2025-08-05 09:36:44,691 INFO *                                                                                                  *
2025-08-05 09:36:44,692 INFO *                                       Configuring Pulse...                                       *
2025-08-05 09:36:44,692 INFO *                                                                                                  *
2025-08-05 09:36:44,692 INFO ****************************************************************************************************
2025-08-05 09:36:47,137 INFO Desired State 'pulse' has been disabled inside this site profile.
2025-08-05 09:36:47,151 INFO Configure User License Agreement Acceptance...
2025-08-05 09:36:47,151 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/eulas, method: GET, headers: None
2025-08-05 09:36:47,151 INFO params: None
2025-08-05 09:36:47,151 INFO User: 1-click-nutanix
2025-08-05 09:36:47,151 INFO payload: None
2025-08-05 09:36:47,152 INFO files: None
2025-08-05 09:36:47,152 INFO timeout: 30
2025-08-05 09:36:48,677 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/eulas/accept, method: POST, headers: None
2025-08-05 09:36:48,677 INFO params: None
2025-08-05 09:36:48,677 INFO User: 1-click-nutanix
2025-08-05 09:36:48,678 INFO payload: {'username': 'Emil Nilsson', 'companyName': 'IKEA', 'jobTitle': 'Digital Technology Engineer'}
2025-08-05 09:36:48,678 INFO files: None
2025-08-05 09:36:48,678 INFO timeout: 30
2025-08-05 09:36:49,957 INFO Configuring Pulse...
2025-08-05 09:36:49,958 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/pulse, method: PUT, headers: None
2025-08-05 09:36:49,958 INFO params: None
2025-08-05 09:36:49,958 INFO User: 1-click-nutanix
2025-08-05 09:36:49,958 INFO payload: {'enable': True, 'enableDefaultNutanixEmail': 'false', 'isPulsePromptNeeded': 'false'}
2025-08-05 09:36:49,958 INFO files: None
2025-08-05 09:36:49,958 INFO timeout: 30
2025-08-05 09:36:51,409 INFO Configuring Remote Diagnostics...
2025-08-05 09:36:51,409 INFO Disabling remote diagnostics for RETSEELM-NXC000.IKEAD2.COM...
2025-08-05 09:36:51,975 INFO Trying to SSH to the RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 09:36:51,975 INFO First try with username/password.
2025-08-05 09:36:51,975 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-05 09:36:54,502 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-05 09:36:58,613 INFO Sending '/home/<USER>/ncc/bin/nusights/set_remote_diagnostics_status --enable=false' to the server.
2025-08-05 09:36:58,613 INFO Checking result
2025-08-05 09:36:58,613 INFO Sending '/usr/local/nutanix/cluster/bin/zkcat /appliance/logical/nusights/collectors/kCommand/override_config' to the server.
2025-08-05 09:37:00,615 INFO Receiving the output .
2025-08-05 09:37:00,615 INFO Received the output: #SSH OUTPUT START#.
2025-08-05 09:37:00,616 INFO 

Nutanix Controller VM (CVM) is a virtual storage appliance.



Alteration of the CVM (unless advised by Nutanix Technical Support or

Support Portal Documentation) is unsupported and may result in loss

of User VMs or other data residing on the cluster.



Unsupported alterations may include (but are not limited to):



- Configuration changes / removal of files.

- Installation of third-party software/scripts not approved by Nutanix.

- Installation or upgrade of software packages from non-Nutanix

  sources (using yum, rpm, or similar).



** Notice: SSH will no longer be available in upcoming releases.      **  

** Nutanix Support may access the bash shell on an exceptional basis. **

Last login: Tue Aug  5 03:36:36 CEST 2025 from *********** on ssh

Last login: Tue Aug  5 03:36:54 2025 from **************


nutanix@NTNX-CZ20240J8S-A-CVM:***********:~$ /home/<USER>/ncc/bin/nusights/set_r
remote_diagnostics_status --enable=false

RCC status has been changed successfully, rcc enabled = false

nutanix@NTNX-CZ20240J8S-A-CVM:***********:~$ /usr/local/nutanix/cluster/bin/zkcat
t /appliance/logical/nusights/collectors/kCommand/override_config

{"rcc_enabled":false,"rcc_status_details":""}nutanix@NTNX-CZ20240J8S-A-CVM:***********:~$ 
2025-08-05 09:37:00,616 INFO #SSH OUTPUT END#
2025-08-05 09:37:00,629 INFO Remote diagnostics has been disabled.
2025-08-05 09:37:00,639 INFO Configuring Remote Diagnostics...
2025-08-05 09:37:00,639 INFO Disabling remote diagnostics for ************...
2025-08-05 09:37:01,136 INFO Trying to SSH to the ************.
2025-08-05 09:37:01,136 INFO First try with username/password.
2025-08-05 09:37:01,137 INFO SSH connecting to ************, this is the '1' try.
2025-08-05 09:37:22,179 WARNING Connection failed due to Connection time out, please verify if the host is online., retry.
2025-08-05 09:37:22,179 ERROR We've retried for 1 times, still not connected, aborting.
2025-08-05 09:37:22,179 INFO Failed to connect with username/password, try with key.
2025-08-05 09:37:22,180 INFO SSH connecting to ************, this is the '1' try.
2025-08-05 09:37:43,226 WARNING Connection failed due to Connection time out, please verify if the host is online., retry.
2025-08-05 09:37:43,226 ERROR We've retried for 1 times, still not connected, aborting.
2025-08-05 09:37:43,241 ERROR ['Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 91, in task_process\n    c(self.pe, self.logger, self.db_logger, self.facility_type).configure()\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 1477, in configure\n    self.configure_remote_diagnostics(self.pc_ip, vault_secret_name=f"{self.pe.split(\'.\')[0].upper()}/{self.benchmark[\'vault\'][\'central_labels\'][\'site_pc_nutanix\']}")\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 1504, in configure_remote_diagnostics\n    ssh_connect.connect_by_pwd_or_key()\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\generic\\commonfunc.py", line 721, in connect_by_pwd_or_key\n    raise SSHFailed(self.host)\n', 'business.generic.base_up_exception.SSHFailed: SSH to ************ failed!\n']
2025-08-05 09:37:43,253 WARNING Failed on current step, but will continue the other steps...
2025-08-05 09:37:43,265 INFO ****************************************************************************************************
2025-08-05 09:37:43,265 INFO *                                                                                                  *
2025-08-05 09:37:43,265 INFO *                                Configuring SyncBackupSettings...                                 *
2025-08-05 09:37:43,265 INFO *                                                                                                  *
2025-08-05 09:37:43,266 INFO ****************************************************************************************************
2025-08-05 09:37:46,109 ERROR ['Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 91, in task_process\n    c(self.pe, self.logger, self.db_logger, self.facility_type).configure()\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 1537, in __init__\n    self.rs_target_site_sa = self._init_target_site_sa()\n                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\distributedhosting\\nutanix\\automation\\desired_state_config.py", line 1544, in _init_target_site_sa\n    raise VaultGetSecretFailed(pe_svc_label)\n', 'business.generic.base_up_exception.VaultGetSecretFailed: Failed to get secret data of None/Site_Pe_Svc!\n']
2025-08-05 09:37:46,121 WARNING Failed on current step, but will continue the other steps...
2025-08-05 09:37:46,155 ERROR Task failed. Detail: The task is done, but with some error occurred.

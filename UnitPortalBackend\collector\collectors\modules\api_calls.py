# Author: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
# Date: 2025-01
# Description: This module provides classes for API calls to Nutanix Prism Central and Prism Element, including data collection and authentication management in a concurrent environment.

import functools
import asyncio
from typing import List
from business.distributedhosting.nutanix.nutanix import (
    Prism_Central,
    PrismElement,
    PrismCentral,
    Prism,
)
from business.generic.commonfunc import OneView
from business.authentication.authentication import Vault
from collector.collectors.modules.db_operations import DatabaseOperations
from models.ntx_models import ModelPrismCentral
from models.ntx_models_wh import ModelWarehousePrismCentral
from business.authentication.authentication import  Vault
from flask import Flask, current_app
import threading
import logging
from models.database import db
from business.generic.commonfunc import DBConfig

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
app.config['SQLALCHEMY_TRACK_MODIFACATIONS'] = False
db.init_app(app)


def with_app_context(func):
    """Decorator to ensure a function runs within a Flask application context.

    This decorator checks if there's already an application context active.
    If not, it creates one for the duration of the function call.

    Args:
        func: The function to decorate

    Returns:
        Decorated function that runs within an application context
    """

    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        # Check if we're already in an application context
        if current_app:
            # Already in an application context, just call the function
            return await func(*args, **kwargs)

        # Create an application context
        with app.app_context():
            return await func(*args, **kwargs)

    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        # Check if we're already in an application context
        if current_app:
            # Already in an application context, just call the function
            return func(*args, **kwargs)

        # Create an application context
        with app.app_context():
            return func(*args, **kwargs)

    if asyncio.iscoroutinefunction(func):
        return async_wrapper

    return sync_wrapper


class Singleton:
    """Base class for implementing the Singleton pattern."""

    _instance = None

    def __new__(cls, *args, **kwargs): # args and kwargs not used, but needed for logger
        if cls._instance is None:
            cls._instance = super(Singleton, cls).__new__(cls)
        # Suppress pylint warning for unused args and kwargs
        _ = args
        _ = kwargs
        return cls._instance


class ServiceAccountManager(Singleton):
    """Manages service account credentials and vault interactions.

    This class is responsible for retrieving, caching, and managing service account
    credentials from the vault. It implements thread-safe access to credentials.
    """
    def __init__(self, logger = None):
        self.svc_lock = threading.Lock() 
        self.vault_instance_lock = threading.Lock() 
        self.condition_svc = threading.Condition(self.svc_lock) 
        self.logger = logger if logger else logging
        if not hasattr(self, "initialized"):
            self.__reset__()
        


    def __reset__(self):
        self.pc_fqdn_central_cite_dict = DatabaseOperations.get_central_pes()
        self.pc_tier_relation = PrismCentral.get_pc_tier_relationship()
        self.vault_instance = {}
        self.vault_pe_authentication = {}
        self.vault_pc_authentication = {}
        self.vault_fetching_status = {}
        self.failed_vault = []  

        # Statistics
        self.initialized = True
        self.reused = 0
        self.new = 0
        self.total = 0
        self.failed = 0
    
    def get_svc_account(self, pe_fqdn=False, pc_fqdn=False, vault=None):
        pe_fqdn = pe_fqdn.lower()
        pc_fqdn_upper = pc_fqdn.upper()

        # vault has to be an instance of Class Vault
        self.total += 1
        vault_information = (
            self.vault_pe_authentication
            if not pe_fqdn in self.pc_fqdn_central_cite_dict.keys()
            else self.vault_pc_authentication
        )

        with self.condition_svc:  # To coordinate threads that need to wait for certain conditions to be met before proceeding.
            # Check if the account is already cached
            if pe_fqdn in vault_information:
                account = vault_information[pe_fqdn]
                self.reused += 1
                return account

            # If another thread is already fetching the account, wait for it to complete
            if pe_fqdn in self.vault_fetching_status:
                while pe_fqdn in self.vault_fetching_status:
                    self.condition_svc.wait()
                account = vault_information.get(pe_fqdn)

                if account:
                    self.reused += 1
                    return account

            # The account wasn't stored. Now mark the account as being fetched.
            self.vault_fetching_status[pe_fqdn] = True

        try: # added so we can set a finally block and release the lock
            tier = self.pc_tier_relation.get(pc_fqdn_upper, None)
            with self.vault_instance_lock:
                if not tier:
                    self.logger.error(f"This pe {pe_fqdn} entry has no 'tier', please check the db data.")
                if tier.upper() in self.vault_instance:
                    vault = self.vault_instance[tier.upper()]
                else:
                    vault = Vault(tier=tier, thread_safe = True)
                    self.vault_instance[tier.upper()] = vault # must reuse vault instance since its thread safe, in vault init we use db which can not handle many threads

            # Fetch the account from the vault (I/O operation)
            pe_name = pe_fqdn.split(".")[0].upper()
            if pe_fqdn in self.pc_fqdn_central_cite_dict.keys():
                res, account = vault.get_secret(f"{pe_name}/Site_Pc_Svc")
            else:
                res, account = vault.get_secret(f"{pe_name}/Site_Pe_Svc")
            if not (res and account):
                self.failed += 1
                failed = f"PE: {pe_name}. reason: {account}"
                self.failed_vault.append(failed)
                self.logger.error(f"Failed to get 1-click-nutanix account for this PE: {pe_name} from vault. Reason: {account}")
                return False

            # Cache the account and update statistics
            with self.condition_svc:
                if pe_fqdn in self.pc_fqdn_central_cite_dict.keys():
                    self.vault_pc_authentication[pe_fqdn] = account
                else:
                    self.vault_pe_authentication[pe_fqdn] = account
                self.new += 1
                del self.vault_fetching_status[pe_fqdn]
                self.condition_svc.notify_all()

            return account
        except Exception as e:
            self.failed += 1
            failed = f"PE: {pe_name}. reason: {account}"
            self.failed_vault.append(failed)
            self.logger.error(f"Exception occurred while fetching account for {pe_fqdn}: {e}")
            self.failed += 1
            return False
        finally: # added so that a thread is always released from the lock
            with self.condition_svc:
                if pe_fqdn in self.vault_fetching_status:
                    del self.vault_fetching_status[pe_fqdn]
                self.condition_svc.notify_all()

    def get_stats(self):
        return("Total: ", self.total, " Reused: ", self.reused, " New: ", self.new, " Failed: ", self.failed)
    
    def get_failed_vault(self):
        return self.failed_vault
    
    def reset(self):
        self.__reset__()



class MetaCache(Singleton):
    def __init__(self, logger = logging):
        self.logger = logger if logger else logging
        self.__reset__()

    def __reset__(self):
        self.pc_data = PrismCentral.get_prism_list_from_db()
        self.central_pe_info = {
            pc["fqdn"]: {"fqdn": pc["central_pe_fqdn"], "prism": pc["fqdn"]} for pc in self.pc_data
        }
        
        self.retail_pe_data_database = DatabaseOperations.get_dh_retail_ntx_pe() 
        self.warehouse_pe_data_database = DatabaseOperations.get_dh_warehouse_ntx_pe() 
        pe_data_database = DatabaseOperations.get_dh_ntx_pe() #for both wh and retail 
        self.pe_data = {pe["fqdn"]: pe for pe in pe_data_database} # Idea is that host script and the other collectors below host(not PC and PE) can use this data. Can be modified. 
        self.nutanix_fetching_status = {}

    def clear_cache_data(self):
        self.__reset__()

    def get_column_id(self, model: "SQLAlchemy.Model", column_name: str) -> int:
        return [c.name for c in model.__table__.columns].index(column_name)


class APICallsCollector:
    def __init__(self, logger = None):
        self.logger = logger if logger else logging
        self.cache = MetaCache()
        self.svc_account_manager = ServiceAccountManager(logger=self.logger)
    
    @with_app_context
    def get_svc_account(self, pc_fqdn, pe_fqdn=None) -> dict:
        if not pe_fqdn:
            try:
                central_pe = self.cache.central_pe_info[pc_fqdn]
                pe_fqdn = central_pe['fqdn']
            except Exception as e:
                self.logger.error(f"Failed to get central pe for {pc_fqdn} because of {e}")
                raise Exception(f"Central PE not found for {pc_fqdn}") from e
        account = self.svc_account_manager.get_svc_account(pe_fqdn, pc_fqdn)
        return account


class APICallsPCCollector(APICallsCollector):
    def __init__(self, logger = None):
        super().__init__(logger = logger)

    @with_app_context
    def get_central_site_using_sa(self, pc, sa):
        current_pc = Prism_Central(
                fqdn=pc,
                sa=sa,
                logger = self.logger
            )
        try:
            central_data = current_pc.get_central_pe_id().json()
            central_pe_id = central_data["resources"]["pc_vm_list"][0]["cluster_reference"]["uuid"]
            cluster_data = current_pc.get_clusters(uuid = central_pe_id).json()
            return cluster_data["spec"]["name"]
        except Exception as e:
            self.logger.error(f"Failed to get central site for {pc} because of {e}")
            return False
    
    @with_app_context
    def get_lcm_information(self, pc, retry = 1):
        try:
            central_pe = self.cache.central_pe_info[pc]
        except Exception as e:
            self.logger.error(f"Failed to get central pe for {pc} because of {e}")
            return {pc: {"lcm_config": False, "lcm_entity": False}}
        try:
            account = self.get_svc_account(central_pe['prism'], central_pe['fqdn'])
            if not account:
                return {pc: {"lcm_config": False, "lcm_entity": False}}
        except Exception as e:
            self.logger.error(f"Failed to get account for {pc} because of {e}")
            return {pc: {"lcm_config": False, "lcm_entity": False}}
        try:
            current_pc = Prism_Central(
                fqdn=pc,
                sa={"username": account["username"], "password": account["secret"]},
                logger = self.logger
            )
        except Exception as e:
            self.logger.error(f"Failed to get Prism Central instance for {pc} because of {e}")
            return {pc: {"lcm_config": False, "lcm_entity": False}}
        
        lcm_version = None
        try:
            lcm_config = current_pc.get_lcm_config()
            lcm_version = lcm_config.get("semantic_version", lcm_config.get("version", None))
        except Exception as e:
            self.logger.error(f"Failed to get LCM config for {pc} because of {e}")
            return {pc: {"lcm_config": False, "lcm_entity": False}}
        lcm_api_version = "v4"
        if lcm_version == "2.4.5.2":
            lcm_api_version = "v1"
        try:
            lcm_entity = current_pc.get_lcm_entities(version= lcm_api_version)
        except Exception as e:
            self.logger.error(f"Failed to get LCM entities for {pc} because of {e}")
            return {pc: {"lcm_config": lcm_config, "lcm_entity": False}}
        if not lcm_config or not lcm_entity:
            if retry > 0:
                return self.get_lcm_information(pc, retry=retry - 1)
            
            self.logger.error(f"Failed to get LCM information for {pc} after retries")
            return {pc: {"lcm_config": False, "lcm_entity": False}}
        
        pc_lcm = {pc: {"lcm_config": lcm_config, "lcm_entity": lcm_entity}}
        return pc_lcm
        
    @with_app_context
    def get_cluster_info1(self, pc, retry = 1):
        try:
            central_pe = self.cache.central_pe_info[pc]
        except Exception as e:
            self.logger.error(f"Failed to get central pe for {pc} because of {e}")
            return False, []
        try:
            account = self.get_svc_account(central_pe['prism'], central_pe['fqdn'])
            if not account:
                return False, []
            current_pc = Prism(
                fqdn=pc,
                sa={"username": account["username"], "password": account["secret"]},
                logger = self.logger
            )
        except Exception as e:
            self.logger.error(f"Failed to get account for {pc} because of {e}")
            return False, []

        try:
            cluster_data = current_pc.get_cluster()
            cluster_data = cluster_data.json()
        except Exception as e:
            self.logger.error(f"Failed to get cluster data for {pc} because of {e}")
            return False, []

        if not cluster_data:
            if retry > 0:
                return self.get_cluster_info1(pc, retry=retry - 1)
            return False, []
        
        return pc, cluster_data
        
    @with_app_context
    def get_cluster_info2(self, pc, retry = 1):
        try:
            central_pe = self.cache.central_pe_info[pc]
        except Exception as e:
            self.logger.error(f"Failed to get central pe for {pc} because of {e}")
            return False, []
        
        try:
            account = self.get_svc_account(central_pe['prism'], central_pe['fqdn'])
            if not account:
                return False, []
            current_pc = Prism_Central(
                fqdn=pc,
                sa={"username": account["username"], "password": account["secret"]},
                logger = self.logger
            )
        except Exception as e:
            self.logger.error(f"Failed to get account for {pc} because of {e}")
            return False, []

        try:
            cluster_data = current_pc.get_clusters()
        except Exception as e:
            self.logger.error(f"Failed to get cluster data for {pc} because of {e}")
            return False, []

        if not cluster_data:
            if retry > 0:
                return self.get_cluster_info2(pc, retry=retry - 1)
            return False, []
        
        return pc, cluster_data
    
    @with_app_context   
    def get_cluster_info3(self, pc, retry = 1):
        try:
            central_pe = self.cache.central_pe_info[pc]
        except Exception as e:
            self.logger.error(f"Failed to get central pe for {pc} because of {e}")
            return False, []
        
        try:
            account = self.get_svc_account(central_pe['prism'], central_pe['fqdn'])
            if not account:
                return False, []
            current_pc = Prism_Central(
                fqdn=pc,
                sa={"username": account["username"], "password": account["secret"]},
                logger = self.logger
            )
        except Exception as e:
            self.logger.error(f"Failed to get account for {pc} because of {e}")
            return False, []

        try:
            central_data = current_pc.get_central_pe_id().json()
            central_pe_id = central_data["resources"]["pc_vm_list"][0]["cluster_reference"]["uuid"]
            cluster_data = current_pc.get_clusters(uuid = central_pe_id).json()
            
        except Exception as e:
            self.logger.error(f"Failed to get cluster data for {pc} because of {e}")
            return False, []

        if not cluster_data:
            if retry > 0:
                return self.get_cluster_info3(pc, retry=retry - 1)
            return False, []
        
        return pc, cluster_data
        
    @with_app_context
    def get_ssl_certificates(self, pc, retry = 1):
        try:
            central_pe = self.cache.central_pe_info[pc]
        except Exception as e:
            self.logger.error(f"Failed to get central pe for {pc} because of {e}")
            return False, []
        
        try:
            account = self.svc_account_manager.get_svc_account(central_pe['fqdn'], central_pe['prism'])
            if not account:
                return False, []
            current_pc = PrismCentral(
                pc=pc,
                sa={"username": account["username"], "password": account["secret"]},
                logger = self.logger
            )
        except Exception as e:
            self.logger.error(f"Failed to get account for {pc} because of {e}")
            return False, []

        try:
            ssl_certificates = current_pc.get_ssl_cert()
        except Exception as e:
            self.logger.error(f"Failed to get ssl certificates for {pc} because of {e}")
            return False, []

        if not ssl_certificates:
            if retry > 0:
                return self.get_ssl_certificates(pc, retry=retry - 1)
            return False, []
        
        return pc, ssl_certificates
        

class APICallsPECollector(APICallsCollector):
    def __init__(self, logger = None):
        super().__init__(logger = logger)

    @with_app_context
    def get_cluster_data(self, pc, retry=1) -> List[dict]:
        try:
            account = self.get_svc_account(pc)
            if not account:
                return False
            current_pc = Prism_Central(
                fqdn=pc,
                sa={"username": account["username"], "password": account["secret"]},
                logger = self.logger
            )
        except Exception as e:
            self.logger.error(f"Failed to get account for {pc} because of {e}")
            return False

        try:
            cluster_data = current_pc.get_clusters()
        except Exception as e:
            self.logger.error(f"Failed to get cluster data for {pc} because of {e}")
            return False

        if not cluster_data:
            if retry > 0:
                self.get_cluster_data(pc, retry=retry - 1)
            else:
                return False
        cluster_data = [{**cluster, "prism": pc} for cluster in cluster_data]
        return cluster_data

    def get_retail_pe_data(self):
        """Get cached Prism Element data."""
        return self.cache.retail_pe_data_database

    def get_warehouse_pe_data(self):
        """Get cached Prism Element data."""
        return self.cache.warehouse_pe_data_database

    def get_service_account(self, pe_fqdn, pc_fqdn, vault=None):
        """Get service account for a Prism Element."""
        return self.svc_account_manager.get_svc_account(pe_fqdn, pc_fqdn, vault)


class APICallsHostCollector(APICallsCollector):
    # class variables
    def __init__(self, logger = None):
        super().__init__(logger = logger)
        #self.logger = logger if logger else logging
        #self.cache = MetaCache()
        #self.svc_account_manager = ServiceAccountManager(logger=self.logger)
        self.nic_lock = threading.Lock()
        self.condition_nic = threading.Condition(self.nic_lock)

    @with_app_context
    def get_nic_data(self, pe_fqdn, pe, host_uuid):

        try:
            pe = self.cache.pe_data[pe_fqdn] 
        except KeyError as e:
            self.logger.error( 
                f"Failed to get pe for host with uuid:{host_uuid} because of keyerror: {e}. PE should exist or be Running in pe database before running host script." 
            )
            return {host_uuid: False}
        except Exception as e:
            self.logger.error(f"Failed to get pe for {pe} because of {e}")
            return {host_uuid: False}
        if pe == "NA" or host_uuid == "NA":
            return {host_uuid: False}


        try:

            account = self.get_svc_account(pe["prism"], pe_fqdn)

            if not account:
                return {host_uuid: False}
        except Exception as e:
            self.logger.error(
                f"Failed to get account for {pe['name']} because of {e}"
            )
            return {host_uuid: False}
        prism_element = PrismElement(
            pe=pe["name"],
            sa={"username": account["username"], "password": account["secret"]},
            logger=self.logger,
            collector=True,
            pe_fqdn=pe_fqdn,
            timeout=15,
        )

        with self.condition_nic:  # make one call to a server/pe at a time
            pe_name = pe["name"]
            # Check if another thread is already fetching data for this PE
            if pe_name in self.cache.nutanix_fetching_status:
                while pe_name in self.cache.nutanix_fetching_status:
                    self.condition_nic.wait()  # the lock is released so other threads can join 1 of the 2 "with self.condition_nic"
                    # and makes the thread wait until notify_all() is called and it checks the condition again

        self.cache.nutanix_fetching_status[pe_name] = True
        try:
            state, host_nics_dict = prism_element.get_host_nics_dict(
                host_uuid=host_uuid, retries=1
            )
        finally:
            with self.condition_nic:
                del self.cache.nutanix_fetching_status[pe_name]
                self.condition_nic.notify_all()
        host_dict = {host_uuid: host_nics_dict}
        if not state:
            return {host_uuid: state}
        return host_dict


    @with_app_context
    def get_host_data(self, pc, retry=1):
        try:
            account = self.get_svc_account(pc)

            if not account:
                return False
            current_pc = PrismCentral(
                pc=pc,
                sa={"username": account["username"], "password": account["secret"]},
                logger=self.logger
            )
        except Exception as e:
            self.logger.error(f"Failed to get account for: {pc} because of: {e}")
            return False

        success, temp_pc_list = current_pc.get_host_data_for_collector()

        if not success:
            if retry > 0:
                self.get_host_data(pc, retry=retry - 1)
            else:
                return False
        return temp_pc_list


    @with_app_context
    def get_oneview_data(self, pc, retry=1, warehouse = False):
        oneview_url_with_less_bytes = False # if url needs to be changed to one that returns less bytes
        label = "A_Central-Production/HpeOneview"
        pc_model = ModelPrismCentral
        facility_type = "retail"
        if warehouse:
            pc_model = ModelWarehousePrismCentral
            facility_type = "warehouse"

        tier = pc_model.query.filter_by(fqdn=pc).first().tier
        try:
            vault = Vault(tier=tier, thread_safe = True)
        except:
            self.logger.error(f"Failed to get vault instance for {pc} because of: {e}")
            return False
        try:
            oneview_instance = OneView(pc=pc, pe=None, label=label, vault=vault, facility_type = facility_type, logger = self.logger, retry = retry)
        except Exception as e:
            self.logger.error(f"Failed to get oneview instance for {pc} because of: {e}")
            return False
        # fails a lot
        try:
            oneview_data = oneview_instance.get_oneview_servers_hardware_collector(
                start=0, fixed_amount_returns = 200, oneview_url_with_less_bytes = oneview_url_with_less_bytes
            )
        except Exception as e:
            self.logger.error(f"oneview for {pc} failed because of: {e}")
            if retry == 0:
                return False
            oneview_data = self.get_oneview_data(pc, retry=retry - 1)
            self.logger.info(f"Retrying collecting for: {pc}")
        return oneview_data


    def get_missing_oneview_data(self, pc, label=False, retry=1): # Not done and if needed!
        if not label:
            return False
        tier = ModelPrismCentral.query.filter_by(fqdn=pc).first().tier

        vault = Vault(tier=tier, thread_safe = True)
        oneview = OneView(pc=pc, pe=None, label=label, vault=vault, logger=self.logger)

        try:
            oneview_data = oneview.get_device_data_for_server_from_oneview_by_uuid(label)
        except Exception as e:
            self.logger.error(f"oneview for {pc} failed because of: {e}")
            if retry == 0:
                return False
            oneview_data = self.get_oneview_data(pc, retry=retry - 1)
            self.logger.info(f"Retrying collecting for: {pc}")
        return oneview_data


class APICallsVMCollector(APICallsCollector):
    def __init__(self, logger = None):
        super().__init__(logger = logger)

# Author: TiAke2
# Date: 2024-11
# Description: This module provides classes for concurrent, asynchronous, and parallel processing in Python.
# It includes the ConcurrentProcessing class for threading, the AsynchronousProcessing class for async tasks and also threading, and the ParallelProcessing class for multiprocessing.


import threading
import queue
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
import asyncio


'''Combining these classes should be done carefully. As of now parallel and async would work best together in theory.
Keep in mind that you may need to add lock etc to your methods to ensure that they are thread safe. But safety comes at a cost of efficiency...'''

# Threading: Imagine you have a kitchen with multiple stations. Each station can handle a different task independently.
#  You assign each cook to a station: one for eggs, one for toast, and one for cleaning. Each station operates concurrently,
#  but you need to ensure they share resources like the stove and utensils without conflict.
#  This setup maximizes efficiency but requires careful coordination to avoid issues.

#
#
#  Not quite done. use AsynchronousProcessing instead, which can have threads


class ConcurrentProcessing:
    def __init__(self, max_threads = 100):

        self.thread_pool = queue.Queue(maxsize=max_threads)
        self.threads = []
        self.results = queue.Queue()
        self.result_list = []
        self.errors = []
        #self.lock = threading.Lock()
        self.variable_lock = {} # Idea is to ensure that only one variable with the same name is active at a time to ensure stability

        #self.success_thread_counter = threading.Lock()
        self.success_thread_count = 0
        #self.total_thread_count_lock = threading.Lock()
        self.total_thread_count_value = 0

    def add_task(self, task, *args, **kwargs):

        thread = threading.Thread(target=self.worker, args=(task, *args), kwargs=kwargs)
        self.threads.append(thread)

    def start_tasks(self):

        for thread in self.threads:
            self.thread_pool.put(1)

            thread.start()
        for thread in self.threads:
            thread.join()

            self.thread_pool.get()


        while not self.results.empty():
            result = self.results.get()
            if isinstance(result, list):
                self.result_list.extend(result)

        return self.result_list

    def worker(self, task, *args, **kwargs):
        try:
            result = task(*args, **kwargs)
            if result:
                self.results.put(result)
        except Exception as e:
            self.errors.append(e)
        finally:
            print("done")
            #self.thread_pool.task_done()

# Asynchronous, single-threaded: You start cooking the eggs and set a timer. While the eggs are cooking, you start the toast and set another timer.
# While both are cooking, you clean the kitchen. When the timers go off, you take the eggs off the heat and the toast out of the toaster and serve them.
# You handle multiple tasks, but only one at a time.
# Remember to create a method in your script with the keyword async. to isolate the async function. And when you use it use keyword asyncio.run("method_name")
# can use threads and should if I/O is blocking


class AsynchronousProcessing:
    def __init__(self, max_jobs = 100, thread = False, use_wait = False, wait_time = 15):
        """
        Initialize the AsynchronousProcessing class.
        :param max_jobs: Maximum number of concurrent jobs.
        :param thread: Whether to use threading for blocking I/O tasks.
        :param use_wait: Whether to wait between batches of tasks. The batch size becomes the max_jobs size
        :param wait_time: Time to wait between batches of tasks.
        """
        self.thread = thread
        self.max_jobs = max_jobs
        self.use_wait = use_wait
        self.wait_time = wait_time
        if self.thread:
            self.thread_pool_executor = ThreadPoolExecutor(max_jobs)
        self.result = {}
        self.tasks = []


    async def _asynchronous_task(self, task_name, task_method, *args, **kwargs):

        if asyncio.iscoroutinefunction(task_method):
            if not self.thread:  # If the task method is a coroutine, await its result
                # To define a method as a coroutine in Python, you need to use the async def syntax. This makes the method an asynchronous coroutine, which can be awaited using the await keyword.
                result = await task_method(*args, **kwargs)
            else:
                def sync_wrapper(task_method, *args, **kwargs):
                    loop = asyncio.new_event_loop()
                    try:
                        coro = task_method(*args, **kwargs)
                        asyncio.set_event_loop(loop)
                        return loop.run_until_complete(coro)
                    finally:
                        loop.close()
                loop = asyncio.get_running_loop()
                result = await loop.run_in_executor(
                    self.thread_pool_executor,
                    lambda: sync_wrapper(task_method, *args, **kwargs)
                )
        else:
            if self.thread:
                loop = asyncio.get_running_loop()
                result = await loop.run_in_executor(
                    self.thread_pool_executor,
                    lambda: task_method(*args, **kwargs)
                )
            else:
                # Otherwise, run the task method synchronously
                result = task_method(*args, **kwargs)

        # Store the result in the result dictionary
        if task_name in self.result:
            self.result[task_name].append(result)
        else:
            self.result[task_name] = [result]


    async def execute_asynchronous_tasks(self): # Use this to run the tasks

        self.wait = 0
        if self.use_wait:
            tasks = []
            for task_name, task_method, *args, kwargs in self.tasks:
                tasks.append(self._asynchronous_task(task_name, task_method, *args, **kwargs))
                if (self.max_jobs // len(tasks)+1) == 1:
                    # Execute the current batch of tasks and wait
                    await asyncio.gather(*tasks)
                    self.wait += 1
                    tasks = []
                    await asyncio.sleep(self.wait_time)
            if tasks:
                await asyncio.gather(*tasks)
        else:
            tasks = []
            for task_name, task_method, *args, kwargs in self.tasks:
                tasks.append(self._asynchronous_task(task_name, task_method, *args, **kwargs))
            await asyncio.gather(*tasks)



    def add_task(self, task_name, task_method, *args, **kwargs): # Use this to add tasks

        self.tasks.append((task_name, task_method, *args, kwargs))

    # Use this to get the results

    def get_results(self):
        """
        Get the results of all executed tasks.
        :return: Dictionary of task results.
        """
        return self.result


# You hire two more cooks, each with their own separate kitchen. One cook handles the eggs in one kitchen, and the other cook handles the toast in another kitchen.
# You, as the main cook, oversee both kitchens. Since each cook has their own kitchen, they don't need to coordinate with each other about shared resources like stoves or utensils.
# This setup allows both tasks to be done simultaneously without interference, but it requires more resources to maintain two separate kitchens.
# You need to manage the overall process and ensure that both kitchens are running smoothly and efficiently
class ParallelProcessing:
    def __init__(self):

        self.executor = concurrent.futures.ProcessPoolExecutor()
        self.result = {}
        #self.tasks = []  #Uncomment if you want to keep track of tasks separately

    def add_process(self, task_name, task_method, *args, **kwargs):
        """
        Add a process to the executor.
        :param task_name: Name of the task.
        :param task_method: Method to execute the task.
        :param args: Arguments for the task method.
        :param kwargs: Keyword arguments for the task method.
        """
        #self.tasks.append(task_name)  # Uncomment if you want to keep track of tasks separately
        future = self.executor.submit(task_method, *args, **kwargs)
        self.result[task_name] = future


    def execute(self):
        from multiprocessing import freeze_support
        freeze_support()

        results = [future.result() for future in concurrent.futures.as_completed(self.result.values())]

        return results


    def extra_work(self, task_name, callback_function): # callback for extra work if one process is done.

        if task_name in self.result:
            process = self.result[task_name]
            try:
                result = process.result()

                if callback_function:
                    callback_result = callback_function(result)
            except:
                callback_result = None

        else:
            print(f"Process {task_name} not found.")
        return callback_result


    def get_result(self):
        concurrent.futures.wait(self.result.values())
        results = {}
        for task_name, future in self.result.items():
            try:
                results[task_name] = future.result()
            except Exception:
                results[task_name] = False
        return results

    def shutdown(self):
        self.executor.shutdown(wait = True)

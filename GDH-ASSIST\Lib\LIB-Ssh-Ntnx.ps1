function Ssh-Prism-UpdateVmMemory(){
    param(
        [string] $Fqdn,
        [string] $Vm,
        [int]    $MemoryInGb,
        [string] $Username,
        [string] $PWord
    )
    try {
        $SshSession = New-SSHSession -ComputerName $Fqdn `
                                     -Credential $(New-Object System.Management.Automation.PSCredential ($Username, $($Pword | ConvertTo-SecureString -AsPlainText -Force))) `
                                     -ConnectionTimeout 999 `
                                     -WarningAction:0 `
                                     -AcceptKey:$true `
                                     -Force
        if ($SshSession) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're updating the VM $Vm, "
            $Execute = Invoke-SSHCommand -SSHSession $SshSession `
                                         -Command "/usr/local/nutanix/bin/acli vm.update $Vm memory=$($MemoryInGb)G"
            $SshSession.Disconnect()
        }else {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "It's unable to update due to the session does not exist"
            return $null
        }
    }catch {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message $_
        return $null
    }
    $Execute.output
}
function Ssh-Prism-StartVm(){
    param(
        [string] $Fqdn,
        [string] $Username,
        [string] $PWord
    )
    try {
        $SshSession = New-SSHSession -ComputerName $Fqdn `
                                     -Credential $(New-Object System.Management.Automation.PSCredential ($Username, $($Pword | ConvertTo-SecureString -AsPlainText -Force))) `
                                     -ConnectionTimeout 999 `
                                     -WarningAction:0 `
                                     -AcceptKey:$true `
                                     -Force
        if ($SshSession) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're starting the VMs"
            $Execute = Invoke-SSHCommand -SSHSession $SshSession `
                                         -Command 'for i in `/usr/local/nutanix/bin/acli vm.list power_state=off | awk ''{print $1}'' | grep -v NTNX` ; do /usr/local/nutanix/bin/acli vm.on $i; done'
            $SshSession.Disconnect()
            #$Command = 'for i in `/usr/local/nutanix/bin/acli vm.list power_state=off | awk ''{print $1}'' | grep -v NTNX` ; do /usr/local/nutanix/bin/acli vm.on $i; done'
            #$Stream  = $SshSession.Session.CreateShellStream("dumb", 0, 0, 0, 0, 1000)
            #$Hide    = Invoke-SSHStreamShellCommand -ShellStream $stream -Command $Command
        }else {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "It's unable to start VMs due to the session does not exist"
            return $null
        }
    }catch {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message $_
        return $null
    }
    $Execute.output
    #$Hide
}
function Ssh-Prism-ResetPcTrust(){
    param(
        [string] $Fqdn,
        [string] $Username,
        [string] $PWord,
        [string] $PeAdminPass
    )
    try {
        $SshSession = New-SSHSession -ComputerName $Fqdn `
                                     -Credential $(New-Object System.Management.Automation.PSCredential ($Username, $($Pword | ConvertTo-SecureString -AsPlainText -Force))) `
                                     -ConnectionTimeout 999 `
                                     -WarningAction:0 `
                                     -AcceptKey:$true `
                                     -Force
        if ($SshSession) {
            Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "We're reseting the PE-PC connection"
            $Stream  = $SshSession.Session.CreateShellStream("dumb", 0, 0, 0, 0, 1000)
            $Commands = @(
                "/usr/local/nutanix/bin/nuclei -username admin -password '$($PeAdminPass)' -server localhost remote_connection.reset_pe_pc_remoteconnection"
                "allssh genesis stop aplos aplos_engine insights_server insights_data_transfer prism hera; cluster start"
            )
            $Commands | ForEach-Object {
                Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending command '$_'"
                Invoke-SSHStreamShellCommand -ShellStream $stream -Command $_
                # $Execute = Invoke-SSHCommand -SSHSession $SshSession -Command $_
                # $Execute.output
                Start-Sleep 5
            }
            $SshSession.Disconnect()
        }else {
            Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message "It's unable to reset trust due to the session does not exist"
            return $null
        }
    }catch {
        Write-ConsoleLog -Level WARN -FunctionName $(Get-FunctionName) -Message $_
        return $null
    }
}
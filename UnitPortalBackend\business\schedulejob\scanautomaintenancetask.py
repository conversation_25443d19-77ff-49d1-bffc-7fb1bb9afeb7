from flask import Flask
import logging
from business.generic.commonfunc import DBConfig
from models.models import db
from business.distributedhosting.nutanix.automation.auto_maintenance import AutoMaintenance


def scan_automaintenancetask_stuck_tasks():
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    db.init_app(app)
    logging.info("Scaning Auto Maintenance tasks...") 
    with app.app_context():
        AutoMaintenance.scan_stuck_tasks()
        db.session.remove()
        db.engine.dispose()
        db.session.close()
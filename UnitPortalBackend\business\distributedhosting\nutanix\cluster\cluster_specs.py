class ClusterSpec:
    # Required for all stages
    PC_FQDN = 'pc_fqdn'
    PE_NAME = 'pe_name'
    STAGE = 'stage'
    STEP = 'step'
    # optional
    BENCHMARK_ID = 'benchmark_id'
    TASK_ID = 'task_id'
    STAGE_STATUS = 'stage_status'
    RESUME = 'resume'
    AHV_SUBNET = 'ahv_subnet'
    OOB_VLAN_ID = 'oob_vlan_id'
    SCAN_OOB = 'scan_oob'
    SELECTED_OOB = 'selected_oob'
    AHV_CVM_SUBNET_ID = 'ahv_cvm_subnet_id'
    OOB_SUBNET_ID = 'oob_subnet_id'
    PE_IP = 'pe_ip'
    DATA_SERVICE_IP = 'data_service_ip'
    AHV_CVM_OOB_MAPPING = 'ahv_cvm_oob_mapping'
    # e.g.
    # ahv_cvm_oob_mapping = [
    #   {
    #     "dhcp": {
    #       "ahv": "*************",
    #       "cvm": "*************",
    #       "oob": "*************"
    #     },
    #     "static": {
    #       "ahv": {
    #         "hostname": "retd2886-nx7001",
    #         "ip": "************"
    #       },
    #       "cvm": {
    #         "hostname": "retd2886-nx7001cvm",
    #         "ip": "************"
    #       },
    #       "oob": {
    #         "hostname": "retd2886-nx7001oob",
    #         "ip": "*************"
    #       }
    #     }
    #   },
    # ...
    # ]
    CLUSTER_UUID = "cluster_uuid"
    JOB_STORE = "job_store_new_cluster"
    FACILITY_TYPE = 'facility_type'


class ClusterSpecWh(ClusterSpec):
    IS_METRO_TASK = 'is_metro_task'
    ROOM_TYPE = 'room_type'     # A/B
    METRO_TASK_ID = 'metro_task_id'
    PE_NAME_A = 'pe_name_a'
    PE_NAME_B = 'pe_name_b'

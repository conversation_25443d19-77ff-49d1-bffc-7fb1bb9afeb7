<template>
    <div class="app-container">
      <div class="filter-container">
        <el-row :gutter="5" >
          <el-col :span="3">
            <el-button class="filter-item"  type="primary" @click="handle_create">
                Create a new role
            </el-button>
          </el-col>          
          <el-col :span="2" :offset="18" style="padding-right:2%">
            <el-button style='float:right' class="filter-item"  type="warning" @click="handle_edit">
              Edit
            </el-button>
          </el-col>
          <el-col :span="1">
            <el-button style='float:right' class="filter-item"  type="danger" @click="handle_delete">
              Delete
            </el-button>
          </el-col>

      </el-row>
      </div>

      <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="role_list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
        @sort-change="sort_change"
        @row-click="handle_row_click"
        class='role-table'
        ref='roletable'
      >
  
        <el-table-column label="ID" min-width="5" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Name" min-width="10%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Description" min-width="14%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.description }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Dashboard " align="center" min-width="5%">
          <template slot-scope="{row}">
            <span>{{ row.role_dashboard }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Marketplace" min-width="6%" align="center" >
          <template slot-scope="{row}">
            <span>{{ row.role_mkt }}</span>
          </template>
        </el-table-column>
        <el-table-column label="PM" class-name="status-col" min-width="6%" align="center" >
          <template slot-scope="{row}">
            <span>
              {{ row.role_pm }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Nutanix" class-name="status-col" min-width="6%" align="center" >
          <template slot-scope="{row}">
            <span>
              {{ row.role_ntx }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="SimpliVity" min-width="6%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.role_sli }}</span>
          </template>
        </el-table-column>
        <el-table-column  label="Administraton" min-width="6%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.role_administration}}</span>
          </template>
        </el-table-column>
        <el-table-column  label="LCM" min-width="6%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.role_lcm}}</span>
          </template>
        </el-table-column>
        <el-table-column  label="User Assigned" min-width="6%" align="center">
          <template slot-scope="{row}">
            <span>{{ row.assigned_user}}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="get_role_list" />
  
      <el-dialog :title="'Create a new role'" :visible.sync="dialogFormVisible" top='2%' width="80%">
        <el-form ref="dataForm" :rules="rules" :model="create_new_role" label-position="left" label-width="90px" style="width: 90%; margin-left:50px;">
          <el-form-item label="Name" prop="name" >
            <el-input  v-model=create_new_role.name style="width:100%;border-bottom: 1px solid black;">
  
            </el-input>
          </el-form-item>
          <el-form-item label="Description" prop="description" >
            <el-input  v-model=create_new_role.description style="width:100%;border-bottom: 1px solid black;">
  
            </el-input>
          </el-form-item>
          <el-form-item label="Select privileges"  label-width="190px" >
          </el-form-item>
          <div class="role_div">
            <div class="role_panel">
              <div class="role_panel_header">
                <el-button  class="filter-item role_panel_header_button"  @click="expand_all('treeRef')">
                  Expand All
                </el-button>
                <el-button class="filter-item role_panel_header_button" @click="collapse_all('treeRef')">
                  Collapse All
                </el-button>
                <el-button class="filter-item role_panel_header_button" @click="select_all">
                  Select All
                </el-button>
                <el-button class="filter-item role_panel_header_button" @click="unselect_all">
                  Unselect All
                </el-button>
              </div>
              <div class="role_panel_tree" style="margin-top:1%">
                <el-tree 
                  ref="treeRef"
                  :data="data_tree" 
                  node-key="id"
                  :props="defaultProps" 
                  show-checkbox 
                  :default-checked-keys="[1]"
                  highlight-current
                  @check="click_tree_checkbox">
                  <template #default="{ node, data }">
                    <span @click="change_scope_view(node)" class="custom-tree-node">
                      <span >{{ node.label }}</span>
                    </span>
                  </template>
                </el-tree>
              </div>

            </div>

            <div class="role_scope">
                <div class="role_scope_header" >
                  <template v-for="key of Object.keys(pm_ntx_scope)">
                    <div v-if="view_scope[key]">
                      <label class="role_scope_header_label">
                        Show :
                      </label>
                      <el-select v-model="pm_ntx_scope[key].element.scope_select" @change="change_ntx_checkbox_visibility(key)" style="width:15%;margin:4px 0 0 8px">
                        <el-option key="all" label="All" value="all" />
                        <el-option key="selected" label="Selected" value="selected" />
                        <el-option key="unselected" label="Unselected" value="unselected" />
                      </el-select>
                      <el-checkbox style="margin-left:20px" v-model="pm_ntx_scope[key].checkallpc" @change="check_all_pe(key)">Check all</el-checkbox>
                      <el-checkbox style="margin-left:20px" v-if="key=='abort_ntx_pm'">Only self-created</el-checkbox>
                      <div style="float:right;width:40%;margin-right:1%;">
                    <el-input v-model="fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large" style="width:64%;margin-right:3%;margin-top:3px"/>
                    <el-button  class="filter-item"  type="primary"  @click="filter_checkbox(key)">
                      Search
                    </el-button>
                      </div>
                    </div>
                  </template>

                  <template>
                    <div v-if="view_scope.create_wl" style="padding-top:10px">
                      <!-- page for edit the access for a user to use vm template -->
                      <div>
                        <!-- <label class="role_scope_header_label">
                          Show :
                        </label> -->
                        <el-checkbox style="margin-left:20px" v-model="wl_scope.check_all"  @change="check_all_wl_template()">Check all templates</el-checkbox>
                      </div>
                    </div>
                  </template>

                </div>

                <div class="role_scope_body" >
                  <div v-if="view_scope.cluster">
                    cluster select scope
                  </div>
                  <template v-for="ele,key of pm_ntx_scope">
                    <div v-if="view_scope[key]" style="height:100%">
                        <el-tabs tab-position="right"  class="filter-tabs" v-model="ele.element.pc_tab">
                          <el-tab-pane v-for="pair in ele.checkbox"  :name="pair.pc" style="height:100%;">

                            <span slot="label"><el-checkbox style="margin-right:6px" :key="pair.pc" :ref="pair.pc" v-model="pair.checkall" @change="select_all_pe_in_pc(key,pair.pc)" :indeterminate="pair.indeterminate"></el-checkbox>{{ pair.pc }}</span>
                            <div style="overflow-y:auto;height:490px;">
                              <!-- <el-button style="width:30%;margin-bottom: 20px;" @click = "check_all_visible_ntx_pe(key,true)" >
                                check all
                              </el-button>
                              <el-button style="width:30%" @click = "check_all_visible_ntx_pe(key,false)" >
                                uncheck all
                              </el-button> -->
                              <el-checkbox 
                                style="width:40%" 
                                @change   = "include_future_pe(pair,key)"
                                v-model = "pair.include_future_pe"
                              >
                              Including all the new PEs in the future
                              </el-checkbox>
                              <el-checkbox-group v-model="pair.check_group" >
                              <el-checkbox 
                                style="width:40%" 
                                v-for     = "_pe in (pair.pe)" 
                                :key      = "_pe.name" 
                                :label    = "_pe.name" 
                                v-if      = "_pe.show"
                                @change   = "change_checkbox(key)"
                                :disabled = "_pe.disabled"
                              >
                              {{_pe.name}}
                              </el-checkbox>
                            </el-checkbox-group>
                            </div>
                          </el-tab-pane>
                        </el-tabs>
                    </div>
                  </template>
                  <div v-if="view_scope.all">
                    <div style="margin-left:20px;font-size: large;">
                      No need to select any scope, just YES or NO, thank you Sir / Ma'am.
                    </div>
                  </div>
                  <div v-if="view_scope.create_wl" >
                    <!-- page for edit the access for a user to use vm template -->
                    <div style="margin-left:20px;font-size: large;height:27vh;overflow-y:auto;">
                      <el-checkbox-group v-model="wl_scope.check_group">
                        <el-checkbox 
                          style="width:42%;" 
                          v-for     = "_template in wl_scope.template_list" 
                          :key      = "_template['id']" 
                          :label    = "_template['id']" 
                          @change   = "change_wl_template_checkbox(_template)"
                        >
                        {{_template.name}}
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>

                    <!-- page for edit the access for a user to create customized vm -->
                    <div style="margin-left:20px;font-size: large;height:6vh;">
                      User will only be allowed to create a VM with the name matches below regular expression:
                      <el-input  v-model=wl_scope.custom_regex style="width:90%;border: 1px solid black;" placeholder="input the regex here" @change="update_wl_create_leaf">
                      </el-input>

                    </div>

                    <div style="margin-left:20px;font-size: large;height:8vh;">
                      <el-checkbox
                       v-model="wl_scope.skip_sizing"
                       @change   = "change_wl_template_skip_sizing_checkbox()"
                      >
                        User will only be allowed to skip the sizing check during VM provsioning
                      </el-checkbox>

                    </div>

                  </div>
                </div>
                <div style="clear: both;"></div>
            </div>
          </div>  
          <div style="clear:both"></div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="create_role()">
            Confirm
          </el-button>
        </div>
      </el-dialog>

      <el-dialog :title="'Edit a role'" :visible.sync="EditDialogFormVisible" top='2%' width="80%">
        <el-form ref="edit_role_form" :model="edit_role" label-position="left" label-width="90px" style="width: 90%; margin-left:50px;">
          <el-form-item label="Name" prop="name" >
            <el-input  v-model=edit_role.name style="width:100%;border-bottom: 1px solid black;" disabled>
  
            </el-input>
          </el-form-item>
          <el-form-item label="Description" prop="description" >
            <el-input  v-model=edit_role.description style="width:100%;border-bottom: 1px solid black;">
  
            </el-input>
          </el-form-item>
          <el-form-item label="Select privileges"  label-width="190px" >
          </el-form-item>
          <div class="role_div">
            <div class="role_panel">
              <div class="role_panel_header">
                <el-button  class="filter-item role_panel_header_button"  @click="expand_all('edit_role_tree_ref')">
                  Expand All
                </el-button>
                <el-button class="filter-item role_panel_header_button" @click="collapse_all('edit_role_tree_ref')">
                  Collapse All
                </el-button>
                <el-button class="filter-item role_panel_header_button" @click="edit_role_select_all">
                  Select All
                </el-button>
                <el-button class="filter-item role_panel_header_button" @click="edit_role_unselect_all">
                  Unselect All
                </el-button>
              </div>
              <div class="role_panel_tree" style="margin-top:1%">
                <el-tree 
                  ref="edit_role_tree_ref"
                  :data="data_tree" 
                  node-key="id"
                  :props="defaultProps" 
                  show-checkbox
                  :render-content="render_func"
                  :default-checked-keys="edit_role_default_checked_node"
                  highlight-current
                  @check="edit_role_click_tree_checkbox">
                  <template #default="{ node, data }">
                    <span @click="change_edit_role_scope_view(node)" class="custom-tree-node">
                      <span >{{ node.label }}</span>
                    </span>
                  </template>
                </el-tree>
              </div>

            </div>

            <div class="role_scope">
                <div class="role_scope_header" >
                  <template v-for="key of Object.keys(edit_role_pm_ntx_scope)">
                    <div v-if="edit_role_view_scope[key]">
                      <label class="role_scope_header_label">
                        Show :
                      </label>
                      <el-select v-model="edit_role_pm_ntx_scope[key].element.scope_select" @change="edit_role_change_ntx_checkbox_visibility(key)" style="width:15%;margin:4px 0 0 8px">
                        <el-option key="all" label="All" value="all" />
                        <el-option key="selected" label="Selected" value="selected" />
                        <el-option key="unselected" label="Unselected" value="unselected" />
                      </el-select>
                      <el-checkbox style="margin-left:20px" v-model="edit_role_pm_ntx_scope[key].checkallpc" @change="edit_role_check_all_pe(key)">Check all</el-checkbox>
                      <el-checkbox style="margin-left:20px" v-if="key=='abort_ntx_pm'">Only self-created</el-checkbox>
                      <div style="float:right;width:40%;margin-right:1%;">
                    <el-input v-model="edit_role_fuzzy_string" placeholder="Fuzzy search, eg: SE " size="large" style="width:64%;margin-right:3%;margin-top:3px"/>
                    <el-button  class="filter-item"  type="primary"  @click="edit_role_filter_checkbox(key)">
                      Search
                    </el-button>
                      </div>
                    </div>
                  </template>

                  <template>
                    <div v-if="edit_role_view_scope.create_wl" style="padding-top:10px">
                      <!-- page for edit the access for a user to use vm template -->
                      <div>
                        <!-- <label class="role_scope_header_label">
                          Show :
                        </label> -->
                        <el-checkbox style="margin-left:20px" v-model="edit_role_wl_scope.check_all"  @change="edit_role_check_all_wl_template()">Check all templates</el-checkbox>
                      </div>
                    </div>
                  </template>

                </div>

                <div class="role_scope_body" >
                  <div v-if="edit_role_view_scope.cluster">
                    cluster select scope
                  </div>
                  <template v-for="ele,key of edit_role_pm_ntx_scope">
                    <div v-if="edit_role_view_scope[key]" style="height:100%">
                        <el-tabs tab-position="right"  class="filter-tabs" v-model="ele.element.pc_tab">
                          <el-tab-pane v-for="pair in ele.checkbox"  :name="pair.pc" style="height:100%;">

                            <span slot="label"><el-checkbox style="margin-right:6px" :key="pair.pc" :ref="pair.pc" v-model="pair.checkall" @change="edit_role_select_all_pe_in_pc(key,pair.pc)" :indeterminate="pair.indeterminate"></el-checkbox>{{ pair.pc }}</span>
                            <div style="overflow-y:auto;height:490px;">
                              <!-- <el-button style="width:30%;margin-bottom: 20px;" @click = "check_all_visible_ntx_pe(key,true)" >
                                check all
                              </el-button>
                              <el-button style="width:30%" @click = "check_all_visible_ntx_pe(key,false)" >
                                uncheck all
                              </el-button> -->
                              <el-checkbox 
                                style="width:40%" 
                                @change   = "edit_role_include_future_pe(pair,key)"
                                v-model = "pair.include_future_pe"
                              >
                              Including all the new PEs in the future
                              </el-checkbox>
                              <el-checkbox-group v-model="pair.check_group" @change="check_group_change">
                              <el-checkbox 
                                style="width:40%" 
                                v-for     = "_pe in (pair.pe)" 
                                :key      = "_pe.name" 
                                :label    = "_pe.name" 
                                v-if      = "_pe.show"
                                @change   = "edit_role_change_checkbox(key)"
                                :disabled = "_pe.disabled"
                              >
                              {{_pe.name}}
                              </el-checkbox>
                            </el-checkbox-group>
                            </div>
                          </el-tab-pane>
                        </el-tabs>
                    </div>
                  </template>
                  <div v-if="edit_role_view_scope.all">
                    <div style="margin-left:20px;font-size: large;">
                      No need to select any scope, just YES or NO, thank you Sir / Ma'am.
                    </div>
                  </div>
                  <div v-if="edit_role_view_scope.create_wl">
                    <!-- page for edit the access for a user to use vm template -->
                    <div style="margin-left:20px;font-size: large;">
                      <el-checkbox-group v-model="edit_role_wl_scope.check_group">
                        <el-checkbox 
                          style="width:40%" 
                          v-for     = "_template in edit_role_wl_scope.template_list" 
                          :key      = "_template['id']" 
                          :label    = "_template['id']" 
                          @change   = "edit_role_change_wl_template_checkbox(_template)"
                        >
                        {{_template.name}}
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>

                    <!-- page for edit the access for a user to create customized vm -->
                    <div style="margin-left:20px;font-size: large;">
                      User will only be allowed to create a VM with the name matches below regular expression:
                      <el-input  v-model=edit_role_wl_scope.custom_regex style="width:90%;border: 1px solid black;" placeholder="input the regex here" @change="edit_role_update_wl_create_leaf">
                      </el-input>

                    </div>
                    <div style="margin-left:20px;font-size: large;height:8vh;">
                      <el-checkbox
                       v-model="edit_role_wl_scope.skip_sizing"
                       @change   = "edit_role_change_wl_template_skip_sizing_checkbox()"
                      >
                        User will only be allowed to skip the sizing check during VM provsioning
                      </el-checkbox>

                    </div>

                  </div>
                </div>
                <div style="clear: both;"></div>
            </div>
          </div>  
          <div style="clear:both"></div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="EditDialogFormVisible = false">
            Cancel
          </el-button>
          <el-button type="success" @click="update_role()">
            Confirm
          </el-button>
        </div>
      </el-dialog>

    </div>
  </template>
  
  <script>
  import {GetPEList, GetPCPECorrespondence, GetTemplateList} from '@/api/nutanix'
  import {GetRoleList, CreateRole, GetRole, UpdateRole, DeleteRole} from '@/api/common'
  import waves from '@/directive/waves' // waves directive
  import { parseTime } from '@/utils'
  import Pagination from '@/components/Pagination' // secondary package based on el-pagination
// import { TimeSelect } from 'element-ui'
// import { triggerRef } from 'vue'
// import { compression } from 'jszip/lib/defaults'
  const prismoptions = [
    { key: 'EU', display_name: 'SSP-EU-NTX.IKEA.COM' },
    { key: 'CN', display_name: 'SSP-CHINA-NTX.IKEA.COM' },
    { key: 'APAC', display_name: 'SSP-APAC-NTX.IKEA.COM' },
    { key: 'NA', display_name: 'SSP-NA-NTX.IKEA.COM' },
    { key: 'RU', display_name: 'SSP-RUSSIA-NTX.IKEA.COM' },
    { key: 'DT', display_name: 'SSP-DT-NTX.IKEADT.COM' },
    { key: 'PPE', display_name: 'SSP-PPE-NTX.IKEA.COM' },
  ]

  // arr to obj, such as { CN : "China", US : "USA" }
  // const calendarTypeKeyValue = prismoptions.reduce((acc, cur) => {
  //   acc[cur.key] = cur.display_name
  //   return acc
  // }, {})
  

  export default {
    name: 'Role',
    components: { Pagination },
    directives: { waves },
    filters: {
      statusFilter(status) {
        let st = status.toLowerCase();
        const statusMap = {
          'not started': 'info',
          'done': 'success',
          'error': 'danger',
          'in progress':'primary'
        }
        return statusMap[st]
      }
    },
    data() {
      const validateTime =(rule, value, callback)=>{
        if(this.temp.datatimepickerdisabled){
          callback()
        }
        let currentdate = new Date()
        let utctime =new Date( currentdate.getTime() + 60*1000*currentdate.getTimezoneOffset())
        if (value < utctime){
          callback(new Error('Schedule date must be later then now.'))
        }else{
          let currnettime = utctime.getTime()
          let scheduletime = value.getTime()
          let timediff = scheduletime-currnettime
          if(timediff/1000/60 < 5){
            callback(new Error('Schedule date is too close from now.'))
          }else{
            callback()
          }
        }
        callback()
      }
      return {
        privilege:{ //init privilege , all empty  except dashboard
          role_dashboard:{
            view:'full'
          },
          role_pm:{
            view_ntx_pm:'empty',
            view_ntx_pm_scope:'',
            create_ntx_pm:'empty',
            create_ntx_pm_scope:'',
            abort_ntx_pm:'empty',
            abort_ntx_pm_scope:'',
            delete_ntx_pm:'empty',
            delete_ntx_pm_scope:'',
            view_sli_pm:'empty',
            view_sli_pm_scope:'',
            create_sli_pm:'empty',
            create_sli_pm_scope:'',
            abort_sli_pm:'empty',
            abort_sli_pm_scope:'',
            delete_sli_pm:'empty',
            delete_sli_pm_scope:'',
          },
          role_ntx:{
            view_pc:'empty',
            view_pc_scope:'',
            add_pc:'empty',
            add_pc_scope:'',
            remove_pc:'empty',
            remove_pc_scope:'',
            view_pe:'empty',
            view_pe_scope:'',
            view_ahv:'empty',
            view_ahv_scope:'',
            view_vm:'empty',
            view_vm_scope:''
          },
          role_sli:{
            view_vc:'empty',
            view_vc_scope:'',
            view_cluster:'empty',
            view_cluster_scope:'',
            view_host:'empty',
            view_host_scope:'',
            view_vm:'empty',
            view_vm_scope:''
          },
          role_mkt:{
            view_wl                   : 'empty',
            view_wl_scope             : '',
            create_wl                 : 'empty',
            create_wl_scope           : '',    
            abort_wl                  : 'empty',     
            abort_wl_scope            : '',
            remove_wl                 : 'empty',     
            remove_wl_scope           : '',
            view_template             : 'empty',
            view_template_scope       : '',
            create_template           : 'empty',  
            create_template_scope     : '',
            edit_template             : 'empty',   
            edit_template_scope       : '',
            remove_template           : 'empty',
            remove_template_scope     : ''
          },
          role_administration:{
            view_role:'empty',
            view_role_scope:'',
            view_user:'empty',
            view_user_scope:''
          },
          role_lcm:{
            view_spp:'empty',
            view_spp_scope:'',
            view_aos:'empty',
            view_aos_scope:'',
            view_move:'empty',
            view_move_scope:'',
            view_atm:'empty',
            view_atm_scope:''
          }
        },
        pm_ntx_scope:{
          create_ntx_pm:{},
          abort_ntx_pm:{},
          delete_ntx_pm:{},
        },
        edit_role_pm_ntx_scope:{
          create_ntx_pm:{},
          abort_ntx_pm:{},
          delete_ntx_pm:{},
        },
        wl_scope:{
          check_all:false,
          check_group:[],
          template_list:[],
          custom_regex:"",
          skip_sizing:false
        },
        edit_role_wl_scope:{
          check_all:false,
          check_group:[],
          template_list:[],
          custom_regex:"",
          skip_sizing:false
        },
        fuzzy_string:'',
        edit_role_fuzzy_string:'',
        checkAll:'',
        prismtier:[
          'Production',
          'PreProduction',
          'IKEADT',
          'IKEAD2'
        ],
        create_new_role:{
          name: '',
          description:''
        },
        edit_role:{
          name: 'Test',
          description:''
        },
        current_editing_role:"",
        tableKey: 0,
        view_scope:{
          "create_ntx_pm":false,
          "abort_ntx_pm":false,
          "delete_ntx_pm":false,
          all:true,
          pe:false,
          cluster:false,
          create_wl:false
        },
        edit_role_view_scope:{
          "create_ntx_pm":false,
          "abort_ntx_pm":false,
          "delete_ntx_pm":false,
          all:true,
          pe:false,
          cluster:false,
          create_wl:false
        },
        tree:{
          current_selected_node_id:'',
          last_selected_node_id:''
        },
        edit_role_tree:{
          current_selected_node_id:'',
          last_selected_node_id:''
        },
        edit_role_default_checked_node:[1,6.1,6.2],
        list: null,
        role_list : null,
        pelist:null,
        pepccorrespondencelist:null,
        total: 0,
        listLoading: true,
        listQuery: {
          page: 1,
          limit: 30,
          cluster: '',
          prism: '',
          status: '',
          sort: '+id'
        },
        prismoptions,
        selectedrow:'',
        dialogFormVisible: false,
        EditDialogFormVisible: false,
        logdata: [],
        rules: {
          name: { required: true, message: 'Name is required', trigger: 'change' }
        },
        defaultProps : {
          children: 'children',
          label: 'label',
          disabled: 'disabled',
        },
        data_tree : this.$store.getters.role_map,

      }
    },
    created() {
      this.get_role_list()
      this.get_pe_list() 
      this.get_template_list()
      this.init_scope_list()
    },
    methods: {    
      get_pe_list() {
        GetPEList(this.$store.getters.token).then(response => {
          this.pelist = response.data
        })
      },
      get_role_list(){
        this.listLoading = true
        GetRoleList(this.$store.getters.token).then(response => {
          this.role_list = response.data
          this.total = response.data.length
          this.listLoading = false
        })
      },
      init_scope_list(){
        // generate checkedpes list 
        GetPCPECorrespondence(this.$store.getters.token).then(response => {
          this.pepccorrespondencelist = response['data']
          // for(let pair of this.pepccorrespondencelist){
          //   let _temp = {'pc':pair.pc,'pe':[],'checkall':false,'check_group':[],'pe_list':[],'indeterminate':false}
          //   // this.checkedpes[pair.pc] = []
          //   for(let _pe of pair.pe){
          //     _temp['pe'].push(
          //       {name:_pe,show:true}
          //     )
          //     _temp['pe_list'].push(
          //       _pe
          //     )
          //   }
          //   this.checkedpes.push(_temp)
          // }

          Object.keys(this.pm_ntx_scope).forEach(//loop the pm_ntx_scope, create/abort/delete, add pc/pe to it
            (_key)=>{
              this.pm_ntx_scope[_key] = {element:{scope_select:'all'},checkbox:[],checkallpc:false}
              for(let pair of this.pepccorrespondencelist){
                if(this.pm_ntx_scope[_key].element.pc_tab == undefined){this.pm_ntx_scope[_key].element.pc_tab=pair.pc}
                
                let _temp = {'pc':pair.pc,'pe':[],'checkall':false,'check_group':[],'pe_list':[],'indeterminate':false,'include_future_pe':false}
                for(let _pe of pair.pe){
                  _temp['pe'].push({name:_pe,show:true,disabled:false})
                  _temp['pe_list'].push(_pe )
                }
                this.pm_ntx_scope[_key].checkbox.push(_temp)
              }
            }
          )
          Object.keys(this.edit_role_pm_ntx_scope).forEach(//loop the pm_ntx_scope, create/abort/delete, add pc/pe to it
            (_key)=>{
              this.edit_role_pm_ntx_scope[_key] = {element:{scope_select:'all'},checkbox:[],checkallpc:false}
              for(let pair of this.pepccorrespondencelist){
                if(this.edit_role_pm_ntx_scope[_key].element.pc_tab == undefined){this.edit_role_pm_ntx_scope[_key].element.pc_tab=pair.pc}
                
                let _temp = {'pc':pair.pc,'pe':[],'checkall':false,'check_group':[],'pe_list':[],'indeterminate':false,'include_future_pe':false}
                for(let _pe of pair.pe){
                  _temp['pe'].push({name:_pe,show:true,disabled:false})
                  _temp['pe_list'].push(_pe )
                }
                this.edit_role_pm_ntx_scope[_key].checkbox.push(_temp)
              }
            }
          )
        })
      },
      get_template_list(){
        GetTemplateList(this.$store.getters.token).then(response => {
          this.wl_scope.template_list=response.data
          this.edit_role_wl_scope.template_list=response.data
        })
      },
      get_single_role(){
        //make the payload
        let payload = {
          role_id : this.selectedrow.id,
          token : this.$store.getters.token
        }
        return GetRole(payload).then(response => {
          return response.data

        }).catch(error =>{

          return false
        })
      },
      change_wl_template_checkbox(){
        this.wl_scope.check_all = this.wl_scope.check_group.length == this.wl_scope.template_list.length
        this.update_wl_create_leaf()
      },
      change_wl_template_skip_sizing_checkbox(){
        this.update_wl_create_leaf()
      },
      edit_role_change_wl_template_checkbox(){
        this.edit_role_wl_scope.check_all = (this.edit_role_wl_scope.check_group.length == this.edit_role_wl_scope.template_list.length)
        this.edit_role_update_wl_create_leaf()
      },
      edit_role_change_wl_template_skip_sizing_checkbox(){
        this.edit_role_update_wl_create_leaf()
      },
      check_all_wl_template(){
        this.wl_scope.check_group = this.wl_scope.check_all?this.wl_scope.template_list.map((_t)=>{return  _t.id}):[]
        this.update_wl_create_leaf()
      },
      edit_role_check_all_wl_template(){
        this.edit_role_wl_scope.check_group = this.edit_role_wl_scope.check_all?this.edit_role_wl_scope.template_list.map((_t)=>{return  _t.id}):[]
        this.edit_role_update_wl_create_leaf()
      },
      handle_filter() {
        this.listQuery.page = 1
      },
      sort_change(data) {
        const { prop, order } = data
        if (prop === 'id') {
          this.sort_by_id(order)
        }
      },
      sort_by_id(order) {
        if (order === 'ascending') {
          this.listQuery.sort = '+id'
        } else {
          this.listQuery.sort = '-id'
        }
        this.handle_filter()
      },
      resetTemp() {
        let localtime = new Date()
        let utctime =new Date( localtime.getTime() + 60*1000*localtime.getTimezoneOffset())
        this.temp = {
          id: undefined,
          timestamp: utctime,
          status: 'published',
          type: '',
          prism: '',
          startnow: "1" ,
          pmtype: "1" ,
          newsa: "1",
          datatimepickerdisabled: false
        }
      },
      handle_create() {
        this.resetTemp()
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      handle_delete(){
         this.$confirm('You sure??', '?!', {
            confirmButtonText: 'YES',
            cancelButtonText: 'NO',
            type: 'danger'
          }).then(()=>
          {
              let payload = {
              token: this.$store.getters.token,
              data :{
                role_id: this.selectedrow.id
              }
            }
            DeleteRole(payload).then(response => {
              this.get_role_list()
              this.$notify({
                title: 'Success',
                message: 'Role has been deleted.',
                type: 'success',
                duration: 5000
              })
            })
            .catch(e=>{
              this.$notify({
                title: 'error',
                message: 'Error orcurred,'+ e.response.data.errors.json._schema[0],
                type: 'error',
                duration: 5000
              })  
            })
          } 
          ).catch((e) => {
            this.$notify({
                title: 'error',
                message: 'Error orcurred,'+ e,
                type: 'error',
                duration: 5000
              })        
          });
      },
      handle_row_click(row,column,event){
        this.selectedrow = row
      },
      render_func(h,{node,data,store}){
        if(node.key == 1){
          //dashboard
          return h('span',{style:"font-size:18px"}, node.label)
        }
        let _n = this.data_tree.filter((role)=>{
          return role.id==node.key[0]
        })[0]
        console.log(111,node)
        // _n is the corresponding node in data_tree, we need _n.key : role_ntx role_sli etc....
        let sub_role_priv = this.current_editing_role.priv[_n.key]
        this.update_node_check_status(node, sub_role_priv)
        return h('span',
                  {
                    style:{    
                      "flex": "1",
                      "display": "flex",
                      "align-items": "center",
                      "justify-content": "space-between",
                      "font-size": "18px",
                      "padding-right": "8px",
                    }, 
                    on: {
                      click: () => this.change_edit_role_scope_view(node),
                    }
                  }, 
                  node.label
                )
      },
      update_node_check_status(node, priv){
        let checked = false
        let indeterminate = false
        if (node.childNodes.length==0){ // child node
          if(priv[node.data.key]=='empty'|| priv[node.data.key]==null||priv[node.data.key]==undefined){
            node.checked = false
            node.indeterminate = false
            return {checked:false,indeterminate:false}
          }
          if(priv[node.data.key]=='full'){
            node.checked = true
            node.indeterminate = false
            return {checked:true,indeterminate:false}
          }
          if(priv[node.data.key]=='part'){
            node.checked = true
            node.indeterminate = true
            return {checked:true,indeterminate:true}
          }
          return {checked:false,indeterminate:false}
        }
        else{
          for(let _node of node.childNodes){
            checked  = (this.update_node_check_status(_node, priv)).checked || checked
            indeterminate  = (this.update_node_check_status(_node, priv)).indeterminate || indeterminate
          }
          node.checked = checked
          node.indeterminate = indeterminate
          return {checked:checked,indeterminate:indeterminate}
        }
      },
      async handle_edit(){
        if(!this.selectedrow){
          this.$message({
              type: 'info',
              message: 'Select a role first.',
              duration: 3000
            });  
          return 
        }
      let role_detail = await this.get_single_role()
      console.log(role_detail)
      if(role_detail){
        this.current_editing_role = role_detail
        this.edit_role.name = role_detail.name
        this.edit_role.description = role_detail.description
        for (const [key, value] of Object.entries(this.edit_role_pm_ntx_scope)) {
          //handle pm ntx first
          if(role_detail.priv.role_pm[key]=='full'){
            this.edit_role_pm_ntx_scope[key].checkallpc = true
            for(const box of this.edit_role_pm_ntx_scope[key].checkbox){
              box.checkall = true
              box.include_future_pe = true
              box.check_group = box.pe_list
            }
          }
          else if(role_detail.priv.role_pm[key]=='empty' || role_detail.priv.role_pm[key]==null ){
            this.edit_role_pm_ntx_scope[key].checkallpc = false
            for(const box of this.edit_role_pm_ntx_scope[key].checkbox){
              box.checkall = false
              box.include_future_pe = false
              box.check_group = []
            }
          }
          else if(role_detail.priv.role_pm[key]=='part'){
            this.edit_role_pm_ntx_scope[key].checkallpc = false
            try{
              let _scope = JSON.parse(role_detail.priv.role_pm[key+'_scope'])
              if(_scope==null|_scope==undefined){
                throw 1
              }
            }
            catch{
              this.$message({
                type: 'error',
                message: 'Cannot convert '+key+' scope to json, but you can continue to modify the role.',
                duration: 5000
             });  
               for(const box of this.edit_role_pm_ntx_scope[key].checkbox){
                box.checkall = false
                box.include_future_pe = false
                box.check_group = []
              }
              continue
            }
            for(const box of this.edit_role_pm_ntx_scope[key].checkbox){
              //iterate the pc
              let _scope = JSON.parse(role_detail.priv.role_pm[key+'_scope'])
              if(box.pc in _scope){
                if(_scope[box.pc]=='all'){
                  box.checkall = true
                  box.include_future_pe = true
                  box.check_group = box.pe_list
                  continue
                }
                else if(_scope[box.pc].length>0){
                  box.checkall = false
                  box.include_future_pe = false
                  box.indeterminate = true
                  box.check_group = box.pe_list
                  continue
                }
              }
              box.checkall = false
              box.include_future_pe = false
              box.check_group = []
            }
          }
        }
        if(role_detail.priv.role_mkt.create_wl =='full'){
          this.edit_role_wl_scope.check_all = true
          this.edit_role_wl_scope.check_group = this.edit_role_wl_scope.template_list.map((_t)=>{
            return _t.id
          })
          this.edit_role_wl_scope.custom_regex = ".*"
          this.edit_role_wl_scope.skip_sizing = true
        }
        else if(role_detail.priv.role_mkt.create_wl == 'part'){
          try{
            let _scope = JSON.parse(role_detail.priv.role_mkt.create_wl_scope)
            console.log(_scope)
            if(_scope.template_ids=="all"){
              this.edit_role_wl_scope.check_all = true
              this.edit_role_wl_scope.check_group = this.edit_role_wl_scope.template_list.map((_t)=>{
                return _t.id
              })
            }
            else{
              this.edit_role_wl_scope.check_all = false
              this.edit_role_wl_scope.check_group = _scope.template_ids||[]
            }

            this.edit_role_wl_scope.custom_regex = _scope.custom_settings=="all"?".*":(_scope.custom_settings||"")
            console.log(_scope.skip_sizing)
            this.edit_role_wl_scope.skip_sizing =  !!_scope.skip_sizing
          }
          catch{
            this.$message({
                type: 'error',
                message: 'Cannot convert wl_create_scope to json, but you can continue to modify the role.',
                duration: 5000
             });  
          }
        }
        else if(role_detail.priv.role_mkt.create_wl =='empty' ||role_detail.priv.role_mkt.create_wl == null){
          this.edit_role_wl_scope.check_all = false
          this.edit_role_wl_scope.check_group = []
          this.edit_role_wl_scope.custom_regex = ""
        }
        // edit_role_default_checked_node
        this.EditDialogFormVisible = true
        // this.EditDialogFormVisible = false
      }
      else{
        this.$message({
              type: 'error',
              message: 'Failed to get the role details, try again later.',
              duration: 3000
            });   
        return
      }
      },
      handleUpdate(row) {
        this.temp.prism = row.prism
        this.temp.id = row.id
        this.temp.cluster = row.cluster
        this.temp.description =  row.description
        this.temp.pmtype =  row.pmtype=='poweroff'?'1':'2'
        if (row.startdate ==='startnow'){
          this.temp.timestamp  = new Date()
          this.temp.datatimepickerdisabled = true
          this.temp.startnow = '2'
        }
        else{
          let dt = new Date(row.startdate)
          this.temp.timestamp = new Date(dt.getTime() + 60*1000*dt.getTimezoneOffset())
          this.temp.datatimepickerdisabled = false
          this.temp.startnow = '1'
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      formatJson(filterVal) {
        return this.list.map(v => filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j])
          } else {
            return v[j]
          }
        }))
      },
      create_role(){
        if(!this.create_new_role.name){
          this.$notify({
                title: 'You forgot the name.',
                message: 'Name this role!',
                type: 'error',
                duration: 2000
              })
          return 
        }
        let privilege = JSON.parse(JSON.stringify(this.privilege))
        let checked_nodes = this.$refs.treeRef.getCheckedNodes().filter((n)=>{return !('children' in n)}) // only need the leaf node
        let half_checked_nodes = this.$refs.treeRef.getHalfCheckedNodes().filter((n)=>{return !('children' in n)})// node id = 1 , means node is dashboard node, it should always be full access.
        let full_checked_nodes = new Array()
        if(half_checked_nodes.length){ // if there is half checked node/nodes.
          for(let node of checked_nodes){
            if(!half_checked_nodes.find((ele)=>ele.id==node.id)){
              full_checked_nodes.push(node)
            }
          }
        }else{
          full_checked_nodes = checked_nodes// if there isn't any half checked nodes, that means all the nodes are full checked.
        }
        // loop the full checked nodes
        for(let node of full_checked_nodes){
          privilege[node['root_key']][node['key']] = 'full'
        }
        for(let node of half_checked_nodes){
          privilege[node['root_key']][node['key']] = 'part'

          // deal with ntx_pm 
          let _scope = {}
          if(node['key'].match(/_ntx_pm$/)){
            this.pm_ntx_scope[node['key']].checkbox.forEach((_s)=>{
              if(_s.include_future_pe){
                _scope[_s.pc] = 'all'
              }else{
                _scope[_s.pc] = _s.check_group
              }
            })
          }
          // deal with create_wl
          if(node['key'] == 'create_wl'){
            //custom_settings={vm_name:'regex'}; template_ids='all' or list()
            if(this.wl_scope.check_group.length > 0){
              if(this.wl_scope.check_all){
                _scope['template_ids'] = 'all'
              }else{
                _scope['template_ids'] = this.wl_scope.check_group
              }
            }
            if(this.wl_scope.custom_regex.trim()!=""){
              if(this.wl_scope.custom_regex.trim()=='.*'){
                _scope['custom_settings'] = 'all'
              }
              else{
                _scope['custom_settings'] = this.wl_scope.custom_regex.trim()
              }
            }
            _scope['skip_sizing'] = this.wl_scope.skip_sizing
          }
          privilege[node['root_key']][node['key']+'_scope'] = _scope

        }
        let payload = {
          data:{
            name:this.create_new_role.name,
            description:this.create_new_role.description,
            privilege:privilege
          },
          token: this.$store.getters.token
        }
        CreateRole(payload)
          .then(() => {
            this.$notify({
              title: 'Success',
              message: 'Role has been created.',
              type: 'success',
              duration: 5000
            })
            this.dialogFormVisible = false
            this.get_role_list()
          })
          .catch((error) => {
            this.dialogFormVisible  = false
            this.$notify({
              title: 'Error',
              message: "Failed to create role.\n\r" + error.response.data.message,
              type: 'error',
              duration: 2000
            })
          })
      },
      update_role(){
        if(!this.edit_role.name){
          this.$notify({
                title: 'You forgot the name.',
                message: 'Name this role!',
                type: 'error',
                duration: 2000
              })
          return 
        }
        let privilege = JSON.parse(JSON.stringify(this.privilege))//deepcopy, kinda a tricky way though
        let checked_nodes = this.$refs.edit_role_tree_ref.getCheckedNodes().filter((n)=>{return !('children' in n)}) // only need the leaf node
        let half_checked_nodes = this.$refs.edit_role_tree_ref.getHalfCheckedNodes().filter((n)=>{return !('children' in n)})// node id = 1 , means node is dashboard node, it should always be full access.
        let full_checked_nodes = new Array()
        if(half_checked_nodes.length){ // if there is half checked node/nodes.
          for(let node of checked_nodes){
            if(!half_checked_nodes.find((ele)=>ele.id==node.id)){
              full_checked_nodes.push(node)
            }
          }
        }else{
          full_checked_nodes = checked_nodes// if there isn't any half checked nodes, that means all the nodes are full checked.
        }
        // loop the full checked nodes
        for(let node of full_checked_nodes){
          privilege[node['root_key']][node['key']] = 'full'
        }
        for(let node of half_checked_nodes){
          privilege[node['root_key']][node['key']] = 'part'

          // deal with ntx_pm 
          let _scope = {}
          if(node['key'].match(/_ntx_pm$/)){
            this.edit_role_pm_ntx_scope[node['key']].checkbox.forEach((_s)=>{
              if(_s.include_future_pe){
                _scope[_s.pc] = 'all'
              }else{
                _scope[_s.pc] = _s.check_group
              }
            })
          }
          // deal with create_wl
          if(node['key'] == 'create_wl'){
            //custom_settings={vm_name:'regex'}; template_ids='all' or list()
            if(this.edit_role_wl_scope.check_group.length > 0){
              if(this.edit_role_wl_scope.check_all){
                _scope['template_ids'] = 'all'
              }else{
                _scope['template_ids'] = this.edit_role_wl_scope.check_group
              }
            }
            if(this.edit_role_wl_scope.custom_regex.trim()!=""){
              if(this.edit_role_wl_scope.custom_regex.trim()=='.*'){
                _scope['custom_settings'] = 'all'
              }
              else{
                _scope['custom_settings'] = this.edit_role_wl_scope.custom_regex.trim()
              }
            }
            _scope['skip_sizing'] = this.edit_role_wl_scope.skip_sizing
          }
          privilege[node['root_key']][node['key']+'_scope'] = _scope

        }
        let payload = {
          data:{
            role_id:this.selectedrow.id,
            description: this.edit_role.description,
            privilege:privilege
          },
          token: this.$store.getters.token
        }
        console.log(payload)
        UpdateRole(payload)
            .then((response) => {
              this.$notify({
                title: 'Success',
                message: 'Role has been updated.',
                type: 'success',
                duration: 5000
              })
              this.EditDialogFormVisible = false
              this.get_role_list()
            })
            .catch((error) => {
              this.EditDialogFormVisible  = false
              this.$notify({
                title: 'Error',
                message: "Failed to update role.\n\r" + error.response.data.message,
                type: 'error',
                duration: 6000
              })
            })
      },
      change_scope_view(node){//改变tree选中node 对应的scope view
        this.tree.last_selected_node_id = this.tree.current_selected_node_id
        if(node.isLeaf){ 
          let scope = node.data.scope
          if (scope == undefined){
            this.change_scope_view_data('all')
          }
          else{
            this.change_scope_view_data(scope)
          }
        }
        else{
          this.change_scope_view_data('all')
        }
        this.tree.current_selected_node_id = node.data.id
      },
      change_scope_view_data(key){
        Object.keys(this.view_scope).forEach(
          (_key)=>{
            this.view_scope[_key] = (_key ==  key)//把相应目录对应的 v-if 改成对应的 true/false, 只显示当前点击的tree node对应的scope view
          }
        )
      },
      change_edit_role_scope_view(node){//改变tree选中node 对应的scope view
        this.edit_role_tree.last_selected_node_id = this.edit_role_tree.current_selected_node_id
        if(node.isLeaf){ 
          let scope = node.data.scope
          if (scope == undefined){
            this.change_edit_role_scope_view_data('all')
          }
          else{
            this.change_edit_role_scope_view_data(scope)
          }
        }
        else{
          this.change_edit_role_scope_view_data('all')
        }
        this.edit_role_tree.current_selected_node_id = node.data.id
      },
      change_edit_role_scope_view_data(key){
        Object.keys(this.edit_role_view_scope).forEach(
          (_key)=>{
            this.edit_role_view_scope[_key] = (_key ==  key)//把相应目录对应的 v-if 改成对应的 true/false, 只显示当前点击的tree node对应的scope view
          }
        )
      },
      expand_all(treeref){
        for(let _node of this.$refs[treeref].$children){
          this.change_node_status(_node.node,true)
        }
      },
      collapse_all(treeref){
        for(let _node of this.$refs[treeref].$children){
          this.change_node_status(_node.node,false)
        }
      },
      change_node_status(node,status=false){
        node.expanded = status
        for(let _node of node.childNodes){
          this.change_node_status(_node,status)
        }
      },
      select_all(){
        this.$refs.treeRef.setCheckedNodes(
          this.$refs.treeRef.$children.map(
            (item)=>{
              return item.node.data
            }
          ),false)

          this.click_tree_checkbox(this.$refs.treeRef.getNode('4').data,'')
          this.click_tree_checkbox(this.$refs.treeRef.getNode('5').data,'')
      },
      edit_role_select_all(){
        this.$refs.edit_role_tree_ref.setCheckedNodes(
          this.$refs.edit_role_tree_ref.$children.map(
            (item)=>{
              return item.node.data
            }
          ),false)

          this.edit_role_click_tree_checkbox(this.$refs.edit_role_tree_ref.getNode('4').data,'')
          this.edit_role_click_tree_checkbox(this.$refs.edit_role_tree_ref.getNode('5').data,'')
      },
      unselect_all(){
        this.$refs.treeRef.setCheckedNodes(
          this.$refs.treeRef.$children.map(
            (item)=>{
              if(item.node.data.disabled == true){
                return item.node.data
              }
            }
          ),false)
        this.click_tree_checkbox(this.$refs.treeRef.getNode('4').data,'')
        this.click_tree_checkbox(this.$refs.treeRef.getNode('5').data,'')
      },      
      edit_role_unselect_all(){
        this.$refs.edit_role_tree_ref.setCheckedNodes(
          this.$refs.edit_role_tree_ref.$children.map(
            (item)=>{
              if(item.node.data.disabled == true){
                return item.node.data
              }
            }
          ),false)
        this.edit_role_click_tree_checkbox(this.$refs.edit_role_tree_ref.getNode('4').data,'')
        this.edit_role_click_tree_checkbox(this.$refs.edit_role_tree_ref.getNode('5').data,'')
      },
      change_ntx_checkbox_visibility(action){// 改变checkbox 可见性
        if(this.pm_ntx_scope[action].element.scope_select =='all'){
          this.pm_ntx_scope[action].checkbox.forEach(
            (cg)=>{//checkbox group
              cg.pe.forEach(//loop all the checkbox.
                (cb)=>{
                  cb.show =  true //make it visible.
                }
              )
            }
          )
          return 
        }

        let _flag
        if(this.pm_ntx_scope[action].element.scope_select == 'selected'){
          _flag = true
        }
        else{
          _flag = false
        }

        this.pm_ntx_scope[action].checkbox.forEach(
          (cg)=>{//checkbox group
            cg.pe.forEach(//loop all the checkbox.
              (cb)=>{
                  cb.show =( (cg.check_group.some(item=>item==cb.name)) == _flag )
              }
            )
          }
        )
      },//选择下拉框调整函数
      edit_role_change_ntx_checkbox_visibility(action){// 改变checkbox 可见性
        if(this.edit_role_pm_ntx_scope[action].element.scope_select =='all'){
          this.edit_role_pm_ntx_scope[action].checkbox.forEach(
            (cg)=>{//checkbox group
              cg.pe.forEach(//loop all the checkbox.
                (cb)=>{
                  cb.show =  true //make it visible.
                }
              )
            }
          )
          return 
        }

        let _flag
        if(this.edit_role_pm_ntx_scope[action].element.scope_select == 'selected'){
          _flag = true
        }
        else{
          _flag = false
        }

        this.edit_role_pm_ntx_scope[action].checkbox.forEach(
          (cg)=>{//checkbox group
            cg.pe.forEach(//loop all the checkbox.
              (cb)=>{
                  cb.show =( (cg.check_group.some(item=>item==cb.name)) == _flag )
              }
            )
          }
        )
      },//选择下拉框调整函数
      change_checkbox(action){
        if(this.scope_select!='all'){
          this.change_ntx_checkbox_visibility(action)
        }
        this.change_tab_checkbox_status(action)
        this.update_pm_ntx_leaf(action)

      },//单个checkbox选中之后 触发 的函数
      edit_role_change_checkbox(action){
        if(this.edit_role_scope_select!='all'){
          this.edit_role_change_ntx_checkbox_visibility(action)
        }
        this.edit_role_change_tab_checkbox_status(action)
        this.edit_role_update_pm_ntx_leaf(action)

      },//单个checkbox选中之后 触发 的函数
      check_all_visible_ntx_pe(action,flag){
        // check all the checkbox in tab-pane
        this.pm_ntx_scope[action].checkbox.forEach(
          (cg)=>{//loop the cb group
            if(cg.pc == this.pm_ntx_scope[action].element.pc_tab){
              cg.pe.forEach(
                (_pe)=>{
                  if(_pe.show){
                    if(flag){
                      if(!(cg.check_group.some(item=>item==_pe.name))){
                        cg.check_group.push(_pe.name)
                      }
                    }
                    else{
                      cg.check_group = cg.check_group.filter(function(item) {
                            return item != _pe.name;
                        })
                    }
                  }  
                }
              )
            }
          }
        )

        if(this.scope_select!='all'){
          this.change_ntx_checkbox_visibility(action)
        }

        this.change_tab_checkbox_status(action)

        this.update_pm_ntx_leaf(action)

      },//（取消）选中所有可见的
      check_all_pe(action){//选中当前scope所有pe
        this.pm_ntx_scope[action].checkbox.forEach(
          (cb)=>{
            cb.checkall = this.pm_ntx_scope[action].checkallpc
            this.select_all_pe_in_pc(action,cb.pc)
          }
        )

        this.update_pm_ntx_leaf(action)
      },  
      edit_role_check_all_pe(action){//选中当前scope所有pe
        this.edit_role_pm_ntx_scope[action].checkbox.forEach(
          (cb)=>{
            cb.checkall = this.edit_role_pm_ntx_scope[action].checkallpc
            this.edit_role_select_all_pe_in_pc(action,cb.pc)
          }
        )

        this.edit_role_update_pm_ntx_leaf(action)
      }, 
      filter_checkbox(action){
        let fuzzy_list = this.fuzzy_string.trim().split(/\s+/)
        if(fuzzy_list.length == 0){
          this.change_ntx_checkbox_visibility(action)
          return
        }else if(fuzzy_list.length == 1){
          if(fuzzy_list[0]==''){
            this.change_ntx_checkbox_visibility(action)
            return
          }
        }
        this.change_ntx_checkbox_visibility(action)
        this.pm_ntx_scope[action].checkbox.forEach(
          (_map)=>{
            if(_map.pc == this.pm_ntx_scope[action].element.pc_tab){// get the right pc
              _map.pe.forEach(
                (_pe)=>{
                  if(_pe.show){
                    let flag = false
                    fuzzy_list.forEach(
                      (fuzzy)=>{
                        if(_pe.name.toString().toLowerCase().search(fuzzy.toLowerCase()) != -1){
                          flag = true
                        }
                    })
                    if(!flag){
                      _pe.show=false
                    }
                  }  
                }
              )
            }
          }
        )
      },
      edit_role_filter_checkbox(action){
        let fuzzy_list = this.edit_role_fuzzy_string.trim().split(/\s+/)
        if(fuzzy_list.length == 0){
          this.edit_role_change_ntx_checkbox_visibility(action)
          return
        }else if(fuzzy_list.length == 1){
          if(fuzzy_list[0]==''){
            this.edit_role_change_ntx_checkbox_visibility(action)
            return
          }
        }
        this.edit_role_change_ntx_checkbox_visibility(action)
        this.edit_role_pm_ntx_scope[action].checkbox.forEach(
          (_map)=>{
            if(_map.pc == this.edit_role_pm_ntx_scope[action].element.pc_tab){// get the right pc
              _map.pe.forEach(
                (_pe)=>{
                  if(_pe.show){
                    let flag = false
                    fuzzy_list.forEach(
                      (fuzzy)=>{
                        if(_pe.name.toString().toLowerCase().search(fuzzy.toLowerCase()) != -1){
                          flag = true
                        }
                    })
                    if(!flag){
                      _pe.show=false
                    }
                  }  
                }
              )
            }
          }
        )
      },
      check_group_change(){
        // this.$refs[this.pc_tab].indeterminate=true
      },
      change_tab_checkbox_status(action){// 改变tab 前的checkbox的状态
        this.pm_ntx_scope[action].checkbox.forEach(
          (_map)=>{
            if(_map.pc == this.pm_ntx_scope[action].element.pc_tab){//找到对应的pc tab
              if(_map.check_group.length==0){ //nothing is checked.
                _map.indeterminate = false
                _map.checkall = false
                _map.include_future_pe = false
              }
              else{
                if(_map.check_group.length == _map.pe_list.length){
                  _map.indeterminate = false
                  _map.checkall = true
                }
                else{
                  _map.indeterminate = true
                  _map.checkall = false
                  _map.include_future_pe = false
                }
              }
            }
          }
        )

      },
      edit_role_change_tab_checkbox_status(action){// 改变tab 前的checkbox的状态
        this.edit_role_pm_ntx_scope[action].checkbox.forEach(
          (_map)=>{
            if(_map.pc == this.edit_role_pm_ntx_scope[action].element.pc_tab){//找到对应的pc tab
              if(_map.check_group.length==0){ //nothing is checked.
                _map.indeterminate = false
                _map.checkall = false
                _map.include_future_pe = false
              }
              else{
                if(_map.check_group.length == _map.pe_list.length){
                  _map.indeterminate = false
                  _map.checkall = true
                }
                else{
                  _map.indeterminate = true
                  _map.checkall = false
                  _map.include_future_pe = false
                }
              }
            }
          }
        )

      },
      select_all_pe_in_pc(action, pc_tab){
        this.pm_ntx_scope[action].checkbox.forEach(
          (cb)=>{
            if(cb.pc == pc_tab){
              cb.indeterminate = false //删除半选中状态
              if(cb.checkall){//选中全部
                cb.check_group = cb.pe_list.map((pe)=>{return pe})
                cb.include_future_pe = true
              }else{//全部 不选中
                cb.check_group = []
                cb.include_future_pe = false
              }
            }
          }
        )
        this.update_pm_ntx_leaf(action)
      },
      edit_role_select_all_pe_in_pc(action, pc_tab){
        this.edit_role_pm_ntx_scope[action].checkbox.forEach(
          (cb)=>{
            if(cb.pc == pc_tab){
              cb.indeterminate = false //删除半选中状态
              if(cb.checkall){//选中全部
                cb.check_group = cb.pe_list.map((pe)=>{return pe})
                cb.include_future_pe = true
              }else{//全部 不选中
                cb.check_group = []
                cb.include_future_pe = false
              }
            }
          }
        )
        this.edit_role_update_pm_ntx_leaf(action)
      },
      click_tree_checkbox(node,node_list){
        let node_obj = this.$refs.treeRef.getNode(node.id)
        let checked = node_obj.checked// 获取是否checked信息
        this.$refs.treeRef.setCurrentNode(node)//选中node
        let action = node.scope.toLowerCase()
        if(node.scope.match(/.*_ntx_pm/)){ // check if it's a create/abort/delete nutanix pm
          this.pm_ntx_scope[action].checkbox.forEach(
            (cb)=>{
              cb.checkall = checked
              this.select_all_pe_in_pc(action,cb.pc)
            }
          )
        }
        else if(node.scope.match(/.*create_wl/)){ // check if it's a create/abort/delete nutanix pm
          this.wl_scope.check_all = checked
          this.wl_scope.custom_regex = checked ? '.*' : ''
          this.wl_scope.skip_sizing = checked
          this.check_all_wl_template()

        }
        else if(node.id=='4.1'){// when click the pm - nutaix node: 4 children.
          node.children.forEach(
            (child)=>{
              this.click_tree_checkbox(child,node_list)
            }
          )
        }
        else if(node.id=='4'){// when click the root node of PM
          node.children[0].children.forEach(
            (child)=>{
              this.click_tree_checkbox(child,node_list)
            }
          )
        }        
        else if(node.id=='5.2'){
          node.children.forEach(
            (child)=>{
              this.click_tree_checkbox(child,node_list)
            }
          )
        }
        else if(node.id=='5'){
          node.children[1].children.forEach(
            (child)=>{
              this.click_tree_checkbox(child,node_list)
            }
          )
        }
        this.change_scope_view(this.$refs.treeRef.getNode(node.id))//需要传递node对象给函数
      },
      edit_role_click_tree_checkbox(node,node_list){
        let node_obj = this.$refs.edit_role_tree_ref.getNode(node.id)
        let checked = node_obj.checked// 获取是否checked信息
        this.$refs.edit_role_tree_ref.setCurrentNode(node)//选中node
        let action = node.scope.toLowerCase()
        if(node.scope.match(/.*_ntx_pm/)){ // check if it's a create/abort/delete nutanix pm
          this.edit_role_pm_ntx_scope[action].checkbox.forEach(
            (cb)=>{
              cb.checkall = checked
              this.edit_role_select_all_pe_in_pc(action,cb.pc)
            }
          )
        }
        else if(node.scope.match(/.*create_wl/)){ // check if it's a create/abort/delete nutanix pm
          this.edit_role_wl_scope.check_all = checked
          this.edit_role_wl_scope.custom_regex = checked ? '.*' : ''
          this.edit_role_wl_scope.skip_sizing = checked
          this.edit_role_check_all_wl_template()

        }
        else if(node.id=='4.1'){// when click the pm - nutaix node: 4 children.
          node.children.forEach(
            (child)=>{
              this.edit_role_click_tree_checkbox(child,node_list)
            }
          )
        }
        else if(node.id=='4'){// when click the root node of PM
          node.children[0].children.forEach(
            (child)=>{
              this.edit_role_click_tree_checkbox(child,node_list)
            }
          )
        }        
        else if(node.id=='5.2'){
          node.children.forEach(
            (child)=>{
              this.edit_role_click_tree_checkbox(child,node_list)
            }
          )
        }
        else if(node.id=='5'){
          node.children[1].children.forEach(
            (child)=>{
              this.edit_role_click_tree_checkbox(child,node_list)
            }
          )
        }
        this.change_edit_role_scope_view(this.$refs.edit_role_tree_ref.getNode(node.id))//需要传递node对象给函数
      },
      update_pm_ntx_leaf(action){
        let _map = {
          create_ntx_pm:'4.1.2',// node id
          abort_ntx_pm:'4.1.3',
          delete_ntx_pm:'4.1.4'
        }
        let indeterminate = false // if all pe checkbox are selected, this should be false, if at least one is not selected, then this should be true
        let _check = false
        this.pm_ntx_scope[action].checkbox.forEach(
          (cg)=>{
            if(cg.check_group.length >0){
              _check =  true
            }
            if(!cg.include_future_pe||(cg.pe.length!=cg.check_group.length)){
              indeterminate = true 
            }
            // cg.pe.length is stable, if check_group != pe , means some pe are not checked. so indeterminate should be true. 
          }
        )

        //role 的勾选框应该是半选中，只要有一个pe没选中
        this.$refs.treeRef.setChecked(_map[action], _check, true)
        if(_check){
          this.$refs.treeRef.getNode(_map[action]).indeterminate = indeterminate
        }
        else{
          this.$refs.treeRef.getNode(_map[action]).indeterminate = false // nothing checked, we should set indeterminate to false, no matter what it was before
        }

      },
      edit_role_update_pm_ntx_leaf(action){
        let _map = {
          create_ntx_pm:'4.1.2',// node id
          abort_ntx_pm:'4.1.3',
          delete_ntx_pm:'4.1.4'
        }
        let indeterminate = false // if all pe checkbox are selected, this should be false, if at least one is not selected, then this should be true
        let _check = false
        this.edit_role_pm_ntx_scope[action].checkbox.forEach(
          (cg)=>{
            if(cg.check_group.length >0){
              _check =  true
            }
            if(!cg.include_future_pe||(cg.pe.length!=cg.check_group.length)){
              indeterminate = true 
            }
            // cg.pe.length is stable, if check_group != pe , means some pe are not checked. so indeterminate should be true. 
          }
        )

        //role 的勾选框应该是半选中，只要有一个pe没选中
        this.$refs.edit_role_tree_ref.setChecked(_map[action], _check, true)
        if(_check){
          this.$refs.edit_role_tree_ref.getNode(_map[action]).indeterminate = indeterminate
        }
        else{
          this.$refs.edit_role_tree_ref.getNode(_map[action]).indeterminate = false // nothing checked, we should set indeterminate to false, no matter what it was before
        }

      },
      update_wl_create_leaf(){
        let node_id = '5.2.2'
        let check = true // true => checked/half-checked, false=> not checked
        let indeterminate = false // true => half-checked
        console.log(this.wl_scope)
        if(this.wl_scope.custom_regex.trim()==""||
            this.wl_scope.custom_regex.trim()==undefined||
            this.wl_scope.custom_regex.trim()==null){//no custom
          if(this.wl_scope.check_group.length||
             this.wl_scope.skip_sizing
          ){
            check = true
            indeterminate = true
          }else{
            check = false
            indeterminate = false
          }
        }
        else{
          if(this.wl_scope.custom_regex.trim()=='.*'){//all custom
            if(this.wl_scope.check_all&&this.wl_scope.skip_sizing){
              check = true
              indeterminate = false
            }else{
              check = true
              indeterminate = true
            }
          }else{// some custom
            check = true
            indeterminate = true
          }
        }
        this.$refs.treeRef.setChecked(node_id, check, true)
        this.$refs.treeRef.getNode(node_id).indeterminate = indeterminate
      },
      edit_role_update_wl_create_leaf(){
        let node_id = '5.2.2'
        let check = true
        let indeterminate = false
        console.log(this.edit_role_wl_scope)
        if(this.edit_role_wl_scope.custom_regex.trim()==""||
            this.edit_role_wl_scope.custom_regex.trim()==undefined||
            this.edit_role_wl_scope.custom_regex.trim()==null){//no custom
          if(this.edit_role_wl_scope.check_group.length||
             this.edit_role_wl_scope.skip_sizing
          ){
            check = true
            indeterminate = true
          }else{
            check = false
            indeterminate = false
          }
        }
        else{
          if(this.edit_role_wl_scope.custom_regex.trim()=='.*'){//all custom
            if(this.edit_role_wl_scope.check_all&&this.edit_role_wl_scope.skip_sizing){
              check = true
              indeterminate = false
            }else{
              check = true
              indeterminate = true
            }
          }else{// some custom
            check = true
            indeterminate = true
          }
        }
        this.$refs.edit_role_tree_ref.setChecked(node_id, check, true)
        this.$refs.edit_role_tree_ref.getNode(node_id).indeterminate = indeterminate
      },
      include_future_pe(pair,key){
        if(pair.check_group.length < pair.pe.length){
          this.$notify({
                title: "Cannot select!!",
                message: 'Cannot do this since not all the PEs in the PC are selected...',
                type: 'warning',
                duration: 5000
              })  
          pair.include_future_pe = false
        }
        this.update_pm_ntx_leaf(key)
      },
      edit_role_include_future_pe(pair,key){
        if(pair.check_group.length < pair.pe.length){
          this.$notify({
                title: "Cannot select!!",
                message: 'Cannot do this since not all the PEs in the PC are selected...',
                type: 'warning',
                duration: 5000
              })  
          pair.include_future_pe = false
        }
        this.edit_role_update_pm_ntx_leaf(key)
      }
    },
    beforeDestroy(){
      //
    }
  }
  </script>

  <style scoped>
   .role-table span{
       font-size: 17px
    }
    .el-tree-node__content span{
      font-size:18px
    }
    .custom-tree-node {
     flex: 1;
     display: flex;
     align-items: center;
     justify-content: space-between;
     font-size: 18px;
     padding-right: 8px;
    }


  </style>
  <style>
    .el-dialog__wrapper{
      width: 100%;
    }
    .role_div{
      border:1px black solid;
      height:600px; 
      margin-top: -1%;
    }
    .role_panel{
      width:40%;
      border-right:1px black solid;
      float:left;
      height:100%;
      
    }
    .role_panel_header{
      height:8%;
      border-bottom:1px solid black;
      width:98%;
    }
    .role_panel_header_button{
      height:30px;
      line-height:1px;
      margin:1.5% 0px 1% 15px;
      background-color:rgb(245, 240, 240)
    }
    .role_panel_tree{
      overflow-y:auto;
      height:91%;
      margin-top:1%
    }
    .role_scope{
      width:60%;
      float:right;
      height:100%
    }
    .role_scope_header{
      height:8%;
      border-bottom:1px solid black;
      width:98%;
      margin-left:2%
    }
    .role_scope_header_label{
      margin:3px;
    }
    .role_scope_body{
      overflow-y:auto;
      height:91%;
      margin-top:1%
    }
    .filter-tabs > .el-tabs__content {
      padding: 12px;
      color: #6b778c;
      font-size: 32px;
      font-weight: 600;
    }

    </style>
2025-07-31 14:15:29,148 INFO Start to run the task.
2025-07-31 14:15:29,185 INFO Starting to unlock account for PE: DSUSDC6-NXC000
2025-07-31 14:15:29,222 INFO Getting PE information
2025-07-31 14:15:29,293 INFO Found PE in warehouse: dsusdc6-nxc000.ikea.com, PC: ssp-na-wiab-ntx.ikea.com
2025-07-31 14:15:29,332 INFO Executing account unlock operation
2025-07-31 14:15:29,372 INFO Connecting to Nutanix cluster...
2025-07-31 14:15:31,369 INFO Unlocking accounts...
2025-07-31 14:15:31,370 INFO Trying to SSH to the pe dsusdc6-nxc000.
2025-07-31 14:15:31,370 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '1' try.
2025-07-31 14:15:36,734 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 14:15:36,735 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '2' try.
2025-07-31 14:15:44,147 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 14:15:44,147 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '3' try.
2025-07-31 14:15:51,506 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 14:15:51,506 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '4' try.
2025-07-31 14:15:56,843 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 14:15:56,843 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '5' try.
2025-07-31 14:16:02,155 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 14:16:02,155 ERROR We've retried for 5 times, still not connected, aborting.
2025-07-31 14:16:02,155 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '1' try.
2025-07-31 14:16:04,747 INFO SSH connected to dsusdc6-nxc000.ikea.com with SSHKEY.
2025-07-31 14:16:06,404 INFO SSH connecting to ssp-na-wiab-ntx.ikea.com, this is the '1' try.
2025-07-31 14:16:09,737 INFO SSH connected to ssp-na-wiab-ntx.ikea.com.
2025-07-31 14:16:11,259 INFO Unlocking PC account: 'admin'
2025-07-31 14:16:11,259 INFO Sending 'allssh sudo faillock --user admin --reset' to the server.
2025-07-31 14:16:16,261 INFO Successfully unlocked PC account: 'admin'
2025-07-31 14:16:16,261 INFO Unlocking PC account: 'nutanix'
2025-07-31 14:16:16,261 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.
2025-07-31 14:16:21,262 INFO Successfully unlocked PC account: 'nutanix'
2025-07-31 14:16:21,262 INFO Unlocking PC account: '1-click-nutanix'
2025-07-31 14:16:21,262 INFO Sending 'allssh sudo faillock --user 1-click-nutanix --reset' to the server.
2025-07-31 14:16:26,264 INFO Successfully unlocked PC account: '1-click-nutanix'
2025-07-31 14:16:27,192 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '1' try.
2025-07-31 14:16:34,484 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 14:16:34,486 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '2' try.
2025-07-31 14:16:41,857 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 14:16:41,857 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '3' try.
2025-07-31 14:16:49,198 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 14:16:49,198 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '4' try.
2025-07-31 14:16:56,557 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 14:16:56,557 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '5' try.
2025-07-31 14:17:04,019 WARNING Connection failed due to Username/Password invalid, please verify., retry.
2025-07-31 14:17:04,019 ERROR We've retried for 5 times, still not connected, aborting.
2025-07-31 14:17:04,019 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '1' try.
2025-07-31 14:17:04,838 WARNING Connection failed due to ['Traceback (most recent call last):\n', '  File "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\paramiko\\transport.py", line 2292, in _check_banner\n    buf = self.packetizer.readline(timeout)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\paramiko\\packet.py", line 374, in readline\n    buf += self._read_timeout(timeout)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n', '  File "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\paramiko\\packet.py", line 603, in _read_timeout\n    raise EOFError()\n', 'EOFError\n', '\nDuring handling of the above exception, another exception occurred:\n\n', 'Traceback (most recent call last):\n', '  File "c:\\Dev\\UnitPortalBackend\\business\\generic\\commonfunc.py", line 619, in connect_sshkey\n    self.ssh.connect(self.host, username=self.username, pkey=self.public_key)\n', '  File "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\paramiko\\client.py", line 451, in connect\n    t.start_client(timeout=timeout)\n', '  File "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\paramiko\\transport.py", line 722, in start_client\n    raise e\n', '  File "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\paramiko\\transport.py", line 2113, in run\n    self._check_banner()\n', '  File "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\paramiko\\transport.py", line 2296, in _check_banner\n    raise SSHException(\n', 'paramiko.ssh_exception.SSHException: Error reading SSH protocol banner\n'], retry.
2025-07-31 14:17:04,838 INFO SSH connecting to dsusdc6-nxc000.ikea.com, this is the '2' try.
2025-07-31 14:17:07,481 INFO SSH connected to dsusdc6-nxc000.ikea.com with SSHKEY.
2025-07-31 14:17:08,978 INFO Unlocking PE account: 'admin'
2025-07-31 14:17:08,979 INFO Sending 'allssh sudo faillock --user admin --reset' to the server.
2025-07-31 14:17:13,979 INFO Successfully unlocked PE account: 'admin'
2025-07-31 14:17:13,980 INFO Unlocking PE account: 'nutanix'
2025-07-31 14:17:13,980 INFO Sending 'allssh sudo faillock --user nutanix --reset' to the server.
2025-07-31 14:17:18,981 INFO Successfully unlocked PE account: 'nutanix'
2025-07-31 14:17:18,981 INFO Unlocking PE account: '1-click-nutanix'
2025-07-31 14:17:18,981 INFO Sending 'allssh sudo faillock --user 1-click-nutanix --reset' to the server.
2025-07-31 14:17:23,982 INFO Successfully unlocked PE account: '1-click-nutanix'
2025-07-31 14:17:24,020 INFO Account unlocked successfully
2025-07-31 14:17:24,149 INFO Task is in 'Done' status.

from business.generic.base_up_exception import BaseUpException


class MetroVGException(BaseUpException):
    def __init__(self, msg):
        super().__init__(msg)


class CreateProtectionRulesFailed(MetroVGException):
    def __init__(self, error_detail):
        super().__init__(f"Failed to create protection rules, error detail: {error_detail}")


class SubnetNameDuplicatedOnNtx(MetroVGException):
    def __init__(self, subnet_name):
        super().__init__(
            f"Found more than one network with name '{subnet_name}', please re-trigger after manually corrected.")

      
class CreateVolumeGroupFailed(MetroVGException):
    def __init__(self, vg_name):
        super().__init__(
            f"Failed to create volume group '{vg_name}'. No results returned.")


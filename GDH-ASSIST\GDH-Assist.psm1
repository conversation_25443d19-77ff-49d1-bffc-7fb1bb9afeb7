######################################################
######   Define Error and Warning preference    ######
######################################################
$ErrorActionPreference = 'SilentlyContinue'
$WarningPreference = 'SilentlyContinue'
$Error.Clear()

######################################################
######        Import all the PS1 scritps        ######
######################################################
# $RegistryModulePath = "HKCU:\GDHAssist\"
$Global:Init         = $HOME + "/.GDH-ASSIST"
$Global:ScrtJsonPath = $Global:Init + "/scrt.json"
$Lib                 = @(Get-ChildItem -Path "$PSScriptRoot\Lib" -Filter "*.ps1")
$Public              = @(Get-ChildItem -Path "$PSScriptRoot\Feature" -Filter "*.ps1")
$Utility             = @(Get-ChildItem -Path "$PSScriptRoot\Task" -Filter "*.ps1")
@($Lib + $Public + $Utility) | ForEach-Object {
    try {
        . $_.FullName
    } catch {
        Write-Error -Message "Failed to import function $($_.FullName): $_"
    }
}

# $Global:Secrets = Get-Content -Path $Global:ScrtJsonPath -Raw | ConvertFrom-Json
try {
    $Global:Secrets = Get-Content -Path $Global:ScrtJsonPath -Raw -ErrorAction Stop | ConvertFrom-Json
    if (!($Global:Secrets)) {
        $Global:Secrets = $(Read-Var -IsOriginal).Secrets
        Set-Content -Path $Global:ScrtJsonPath -Value ($Global:Secrets | ConvertTo-Json -Depth 10)
    }
} catch {
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The mandatory profile does not exist, let's make it."
    New-Item -Path $Global:ScrtJsonPath -ItemType File -Force
    Write-ConsoleLog -Level INFO -FunctionName $(Get-FunctionName) -Message "The profile created."
    $Global:Secrets = $(Read-Var -IsOriginal).Secrets
    Update-GstAccount
    Update-VaultToken
}

################################################################
######  Here is to select which function to be exported   ######
################################################################
# Export-ModuleMember -Function *

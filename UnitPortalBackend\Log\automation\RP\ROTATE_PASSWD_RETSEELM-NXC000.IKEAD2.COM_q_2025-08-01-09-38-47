2025-08-01 17:38:47,688 INFO Start to run the task.
2025-08-01 17:38:51,055 INFO Checking Maintenance Mode
2025-08-01 17:38:51,055 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-08-01 17:38:51,056 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-01 17:38:53,543 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 17:38:53,544 INFO SSH Executing '/home/<USER>/prism/cli/ncli host list --json=pretty'.
2025-08-01 17:38:54,421 INFO Waiting for 5 seconds for the execution.
2025-08-01 17:38:59,433 INFO stdout: b'{\n  "data" : [ {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::4",\n    "uuid" : "8a276a5a-9cd0-4702-8ed9-5699d07c192e",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH967RD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::60",\n        "diskUuid" : "ba87f2bb-2824-44e3-8240-75c32c2c4e80",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH967RD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH99N2D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::51",\n        "diskUuid" : "35ea81b9-7cfb-4924-9b45-f07a4cd6fc3f",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N2D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH7B71D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::53",\n        "diskUuid" : "65a27c83-885e-4672-be27-05971598814b",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7B71D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9726D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::61",\n        "diskUuid" : "f4390867-7871-494b-8fd2-65dfa19de512",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9726D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH9B3ED",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::58",\n        "diskUuid" : "9979a224-9d9e-42bc-a282-b1af4e2f9acd",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B3ED",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9421D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::52",\n        "diskUuid" : "01e76618-04df-4259-a761-7e0bab50887b",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9421D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH8DKKD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::56",\n        "diskUuid" : "e31d9325-c534-4c9e-a67e-65081ebff024",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8DKKD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98J3D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::57",\n        "diskUuid" : "c61322d0-febf-45eb-8293-8bb50ef57e95",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98J3D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307893",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::55",\n        "diskUuid" : "d55987bc-a972-43e0-8c49-380c1fed8ff0",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307893",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307888",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::54",\n        "diskUuid" : "514be4f9-c6dc-4f16-805f-8372704d5f1d",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307888",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7001",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "*************",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "*************"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8S",\n    "blockSerial" : "CZ20240J8S",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753432724206906,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "20000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "150000",\n      "controller_num_iops" : "69",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "264376",\n      "controller_num_write_io" : "1397",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "1473448",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "1732",\n      "controller_total_io_size_kbytes" : "12540",\n      "content_cache_hit_ppm" : "597575",\n      "controller_num_io" : "1397",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "96",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "1",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "1054",\n      "num_io" : "20",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "627",\n      "hypervisor_num_received_bytes" : "2595794846135",\n      "hypervisor_timespan_usecs" : "29964698",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "22",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "80",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "627",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "1942581858834",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "1",\n      "hypervisor_memory_usage_ppm" : "412539",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "69",\n      "total_io_time_usecs" : "1603",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "3",\n      "write_io_bandwidth_kBps" : "11",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "8",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "17",\n      "total_io_size_kbytes" : "258",\n      "io_bandwidth_kBps" : "12",\n      "content_cache_physical_memory_usage_bytes" : "2463128484",\n      "controller_timespan_usecs" : "20000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-87255468",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "850000",\n      "controller_avg_write_io_latency_usecs" : "1054",\n      "content_cache_logical_memory_usage_bytes" : "2375873016"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "6713765888",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "866742173696",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97632835753576",\n      "storage_tier.ssd.usage_bytes" : "************",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91529248142952",\n      "storage.usage_bytes" : "************",\n      "storage_tier.ssd.free_bytes" : "6103587610624"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::5",\n    "uuid" : "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH9453D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::63",\n        "diskUuid" : "55b9bef0-e64e-4b83-b56d-07424ac5a4fa",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9453D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH9B0JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::69",\n        "diskUuid" : "87299dda-79ad-4648-a18d-867837bdb061",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B0JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH947JD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::66",\n        "diskUuid" : "21accc04-9438-4b1e-a735-121e6651e2c1",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH947JD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9B2GD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::68",\n        "diskUuid" : "8fcec93c-f3a5-4a88-a18f-ad5a75a3d36e",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9B2GD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH95WYD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::64",\n        "diskUuid" : "190548f4-f43c-40fd-8e0d-c805d972bf31",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH95WYD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH9984D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::67",\n        "diskUuid" : "f73b0d65-6966-4e81-92fb-7815e0986aea",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9984D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH99N1D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::70",\n        "diskUuid" : "ebe3c979-d24f-46fb-9f2d-06b675e7f957",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99N1D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH98WLD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::65",\n        "diskUuid" : "3b10236d-18ef-48d1-b777-8f9f6b64ced8",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH98WLD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N200095",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::72",\n        "diskUuid" : "4ce56fa0-31fa-45aa-9740-39f6561ab0e2",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N200095",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307878",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::71",\n        "diskUuid" : "064c52a5-ac8a-4f7f-ae98-967f225ddb32",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307878",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7002",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********30",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********30"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8R",\n    "blockSerial" : "CZ20240J8R",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 3,\n    "bootTimeInUsecs" : 1753430337664248,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "20000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "222222",\n      "controller_num_iops" : "63",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "243721",\n      "controller_num_write_io" : "1270",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "3013264",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "1997",\n      "controller_total_io_size_kbytes" : "11980",\n      "content_cache_hit_ppm" : "558838",\n      "controller_num_io" : "1270",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "95",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "2372",\n      "num_io" : "9",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "599",\n      "hypervisor_num_received_bytes" : "2925779135134",\n      "hypervisor_timespan_usecs" : "29914648",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "1",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "1427",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "599",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "1971239245733",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "0",\n      "hypervisor_memory_usage_ppm" : "412461",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "63",\n      "total_io_time_usecs" : "12844",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "2",\n      "write_io_bandwidth_kBps" : "5",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "9",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "7",\n      "total_io_size_kbytes" : "113",\n      "io_bandwidth_kBps" : "5",\n      "content_cache_physical_memory_usage_bytes" : "1983167144",\n      "controller_timespan_usecs" : "20000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-85908872",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "777777",\n      "controller_avg_write_io_latency_usecs" : "2372",\n      "content_cache_logical_memory_usage_bytes" : "1897258272"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "6852161536",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "916494811136",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97579221243496",\n      "storage_tier.ssd.usage_bytes" : "************",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91529109747304",\n      "storage.usage_bytes" : "************",\n      "storage_tier.ssd.free_bytes" : "6050111496192"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  }, {\n    "serviceVMId" : "00062979-420c-a3d1-0112-48df37c7ce70::6",\n    "uuid" : "34ff0abd-9dee-4e7d-9481-4716d64569b5",\n    "diskHardwareConfigs" : {\n      "1" : {\n        "serialNumber" : "5PH96JDD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::41",\n        "diskUuid" : "2c172a78-3626-4861-bfff-98d6d79fef49",\n        "location" : 1,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH96JDD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "2" : {\n        "serialNumber" : "5PH995TD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::43",\n        "diskUuid" : "94de93d2-5e8f-44d7-85d7-c414177670a7",\n        "location" : 2,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH995TD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "3" : {\n        "serialNumber" : "5PH991DD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::46",\n        "diskUuid" : "4d3bffc9-fa0d-4ff3-bd95-436cf68ab516",\n        "location" : 3,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH991DD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "4" : {\n        "serialNumber" : "5PH9825D",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::45",\n        "diskUuid" : "ea324991-7bb3-49a9-84f6-7c30e1c938a5",\n        "location" : 4,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9825D",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "5" : {\n        "serialNumber" : "5PH8XYHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::48",\n        "diskUuid" : "5f14642e-c45b-4efc-b5ee-95c532192c22",\n        "location" : 5,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH8XYHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "6" : {\n        "serialNumber" : "5PH99MHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::44",\n        "diskUuid" : "5758e233-4edc-4045-8446-0625e9ba05c0",\n        "location" : 6,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH99MHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "7" : {\n        "serialNumber" : "5PH9ADHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::47",\n        "diskUuid" : "dbeb0a0e-ff11-4b98-a2b7-a45bad3407fd",\n        "location" : 7,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH9ADHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "8" : {\n        "serialNumber" : "5PH7XGHD",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::42",\n        "diskUuid" : "08dba0c0-cd98-4d75-a131-a60323ba43b5",\n        "location" : 8,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/5PH7XGHD",\n        "model" : "MB012000JWDFD",\n        "vendor" : "HPE",\n        "bootDisk" : false,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPD4",\n        "targetFirmwareVersion" : "HPD3",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "9" : {\n        "serialNumber" : "S4NDNA0N307864",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::50",\n        "diskUuid" : "39e3bde6-2802-4ad2-825c-a0d61ae9fb35",\n        "location" : 9,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307864",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      },\n      "10" : {\n        "serialNumber" : "S4NDNA0N307881",\n        "diskId" : "00062979-420c-a3d1-0112-48df37c7ce70::49",\n        "diskUuid" : "a032c48d-3866-4fd6-88af-1f6a25fe5ac3",\n        "location" : 10,\n        "bad" : false,\n        "mounted" : true,\n        "mountPath" : "/home/<USER>/data/stargate-storage/disks/S4NDNA0N307881",\n        "model" : "VK003840GWSRV",\n        "vendor" : "Not Available",\n        "bootDisk" : true,\n        "onlyBootDisk" : false,\n        "underDiagnosis" : false,\n        "currentFirmwareVersion" : "HPG4",\n        "targetFirmwareVersion" : "HPG4",\n        "canAddAsNewDisk" : false,\n        "canAddAsOldDisk" : false\n      }\n    },\n    "name" : "RETSEELM-NX7003",\n    "serviceVMExternalIP" : "***********",\n    "serviceVMExternalAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "oplogDiskPct" : 0.4,\n    "oplogDiskSize" : ************,\n    "hypervisorKey" : "***********",\n    "hypervisorAddress" : "***********",\n    "hypervisorAddressValue" : [ {\n      "ipv4" : "***********"\n    } ],\n    "hypervisorUsername" : "root",\n    "controllerVmBackplaneIp" : "***********",\n    "controllerVmBackplaneAddress" : [ {\n      "ipv4" : "***********"\n    } ],\n    "managementServerName" : "***********",\n    "ipmiAddress" : "***********31",\n    "ipmiAddressValue" : [ {\n      "ipv4" : "***********31"\n    } ],\n    "ipmiUsername" : "ADMIN",\n    "monitored" : true,\n    "position" : {\n      "ordinal" : 1,\n      "name" : "",\n      "physicalPosition" : "TL"\n    },\n    "serial" : "CZ20240J8Q",\n    "blockSerial" : "CZ20240J8Q",\n    "blockModel" : "UseLayout",\n    "blockModelName" : "HPE DX380-12 G10",\n    "hostMaintenanceModeReason" : "life_cycle_management",\n    "hypervisorState" : "kAcropolisNormal",\n    "acropolisConnectionState" : "kConnected",\n    "metadataStoreStatus" : "kNormalMode",\n    "metadataStoreStatusMessage" : "Metadata store enabled on the node",\n    "state" : "NORMAL",\n    "removalStatus" : [ "NA" ],\n    "vzoneName" : "",\n    "cpuModel" : "Intel(R) Xeon(R) Silver 4210R CPU @ 2.40GHz",\n    "numCpuCores" : 20,\n    "numCpuThreads" : 40,\n    "numCpuSockets" : 2,\n    "cpuFrequencyInHz" : 2400000000,\n    "cpuCapacityInHz" : 48000000000,\n    "memoryCapacityInBytes" : ************,\n    "hypervisorFullName" : "AHV 10.3",\n    "hypervisorType" : "kKvm",\n    "numVMs" : 4,\n    "bootTimeInUsecs" : 1753435095317913,\n    "isDegraded" : false,\n    "isSecureBooted" : false,\n    "isHardwareVirtualized" : false,\n    "rebootPending" : false,\n    "clusterUuid" : "00062979-420c-a3d1-0112-48df37c7ce70",\n    "stats" : {\n      "hypervisor_avg_io_latency_usecs" : "0",\n      "num_read_iops" : "0",\n      "hypervisor_write_io_bandwidth_kBps" : "0",\n      "timespan_usecs" : "20000000",\n      "controller_num_read_iops" : "0",\n      "read_io_ppm" : "384615",\n      "controller_num_iops" : "54",\n      "total_read_io_time_usecs" : "-1",\n      "controller_total_read_io_time_usecs" : "0",\n      "hypervisor_num_io" : "0",\n      "controller_total_transformed_usage_bytes" : "-1",\n      "hypervisor_cpu_usage_ppm" : "197289",\n      "controller_num_write_io" : "1089",\n      "avg_read_io_latency_usecs" : "-1",\n      "content_cache_logical_ssd_usage_bytes" : "0",\n      "controller_total_io_time_usecs" : "1050614",\n      "controller_total_read_io_size_kbytes" : "0",\n      "controller_num_seq_io" : "-1",\n      "controller_read_io_ppm" : "0",\n      "content_cache_num_lookups" : "413",\n      "controller_total_io_size_kbytes" : "8627",\n      "content_cache_hit_ppm" : "980629",\n      "controller_num_io" : "1089",\n      "hypervisor_avg_read_io_latency_usecs" : "0",\n      "content_cache_num_dedup_ref_count_pph" : "96",\n      "num_write_iops" : "0",\n      "controller_num_random_io" : "0",\n      "num_iops" : "0",\n      "hypervisor_num_read_io" : "0",\n      "hypervisor_total_read_io_time_usecs" : "0",\n      "controller_avg_io_latency_usecs" : "964",\n      "num_io" : "13",\n      "controller_num_read_io" : "0",\n      "hypervisor_num_write_io" : "0",\n      "controller_seq_io_ppm" : "-1",\n      "controller_read_io_bandwidth_kBps" : "0",\n      "controller_io_bandwidth_kBps" : "431",\n      "hypervisor_num_received_bytes" : "1900796515252",\n      "hypervisor_timespan_usecs" : "29328399",\n      "hypervisor_num_write_iops" : "0",\n      "total_read_io_size_kbytes" : "27",\n      "hypervisor_total_io_size_kbytes" : "0",\n      "avg_io_latency_usecs" : "1238",\n      "hypervisor_num_read_iops" : "0",\n      "content_cache_saved_ssd_usage_bytes" : "0",\n      "controller_write_io_bandwidth_kBps" : "431",\n      "controller_write_io_ppm" : "1000000",\n      "hypervisor_avg_write_io_latency_usecs" : "0",\n      "hypervisor_num_transmitted_bytes" : "3219429187860",\n      "hypervisor_total_read_io_size_kbytes" : "0",\n      "read_io_bandwidth_kBps" : "1",\n      "hypervisor_memory_usage_ppm" : "509800",\n      "hypervisor_num_iops" : "0",\n      "hypervisor_io_bandwidth_kBps" : "0",\n      "controller_num_write_iops" : "54",\n      "total_io_time_usecs" : "16103",\n      "content_cache_physical_ssd_usage_bytes" : "0",\n      "controller_random_io_ppm" : "-1",\n      "controller_avg_read_io_size_kbytes" : "0",\n      "total_transformed_usage_bytes" : "-1",\n      "avg_write_io_latency_usecs" : "-1",\n      "num_read_io" : "5",\n      "write_io_bandwidth_kBps" : "4",\n      "hypervisor_read_io_bandwidth_kBps" : "0",\n      "random_io_ppm" : "-1",\n      "total_untransformed_usage_bytes" : "-1",\n      "hypervisor_total_io_time_usecs" : "0",\n      "num_random_io" : "-1",\n      "controller_avg_write_io_size_kbytes" : "7",\n      "controller_avg_read_io_latency_usecs" : "0",\n      "num_write_io" : "8",\n      "total_io_size_kbytes" : "119",\n      "io_bandwidth_kBps" : "5",\n      "content_cache_physical_memory_usage_bytes" : "2558089816",\n      "controller_timespan_usecs" : "20000000",\n      "num_seq_io" : "-1",\n      "content_cache_saved_memory_usage_bytes" : "-87029352",\n      "seq_io_ppm" : "-1",\n      "write_io_ppm" : "615384",\n      "controller_avg_write_io_latency_usecs" : "964",\n      "content_cache_logical_memory_usage_bytes" : "2471060464"\n    },\n    "usageStats" : {\n      "storage_tier.das-sata.usage_bytes" : "5903994880",\n      "storage.capacity_bytes" : "98254680041064",\n      "storage.logical_usage_bytes" : "1060123639808",\n      "storage_tier.das-sata.capacity_bytes" : "91535961908840",\n      "storage.free_bytes" : "97425612985960",\n      "storage_tier.ssd.usage_bytes" : "823163060224",\n      "storage_tier.ssd.capacity_bytes" : "6718718132224",\n      "storage_tier.das-sata.free_bytes" : "91530057913960",\n      "storage.usage_bytes" : "829067055104",\n      "storage_tier.ssd.free_bytes" : "5895555072000"\n    },\n    "hasCsr" : false,\n    "hostNicIds" : [ ],\n    "hostType" : "HYPER_CONVERGED",\n    "keyManagementDeviceToCertificateStatus" : { },\n    "hostInMaintenanceMode" : false\n  } ],\n  "status" : 0\n}\n'
2025-08-01 17:38:59,451 INFO All good, no hosts are set as NCLI maintenance inside this cluster.
2025-08-01 17:38:59,451 INFO Trying to SSH to the pe RETSEELM-NXC000.
2025-08-01 17:38:59,451 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-01 17:39:01,921 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 17:39:01,922 INFO SSH Executing '/usr/local/nutanix/bin/acli -o json host.list'.
2025-08-01 17:39:02,749 INFO Waiting for 5 seconds for the execution.
2025-08-01 17:39:07,751 INFO stdout: b'{"data": [{"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "8a276a5a-9cd0-4702-8ed9-5699d07c192e", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "6ecba7d0-2125-48d2-b79e-3a72f16ff3b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}, {"hypervisorAddress": "***********", "hypervisorDnsName": "***********", "uuid": "34ff0abd-9dee-4e7d-9481-4716d64569b5", "node_state": "AcropolisNormal", "connected": true, "node_type": "Hyperconverged", "schedulable": true, "hypervisorName": "AHV", "cvm_ip": "***********"}], "error": null, "status": 0}\n'
2025-08-01 17:39:07,751 INFO This seems a very old AOS version...
2025-08-01 17:39:07,751 INFO This seems a very old AOS version...
2025-08-01 17:39:07,751 INFO This seems a very old AOS version...
2025-08-01 17:39:07,764 INFO All good, no hosts are set as ACLI maintenance inside this cluster.
2025-08-01 17:39:07,774 INFO Checking CVM status
2025-08-01 17:39:08,262 INFO Trying to SSH to the RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 17:39:08,262 INFO First try with username/password.
2025-08-01 17:39:08,263 INFO SSH connecting to RETSEELM-NXC000.IKEAD2.COM, this is the '1' try.
2025-08-01 17:39:10,750 INFO SSH connected to RETSEELM-NXC000.IKEAD2.COM.
2025-08-01 17:39:16,846 INFO Sending 'cluster status |grep -v UP' to the server.
2025-08-01 17:39:32,849 INFO CVM IP:*********** Status:Up
2025-08-01 17:39:32,850 INFO CVM IP:*********** Status:Up
2025-08-01 17:39:32,850 INFO CVM IP:*********** Status:Up
2025-08-01 17:39:32,850 INFO Great, all CVM status are Up
2025-08-01 17:39:32,892 INFO Calling restapi, URL: https://retseelm-nxc000.ikead2.com:9440/PrismGateway/services/rest/v1/hosts, method: GET, headers: None
2025-08-01 17:39:32,892 INFO params: None
2025-08-01 17:39:32,892 INFO User: <EMAIL>
2025-08-01 17:39:32,892 INFO payload: None
2025-08-01 17:39:32,892 INFO files: None
2025-08-01 17:39:32,892 INFO timeout: 30
2025-08-01 17:39:34,851 INFO Getting host list from retseelm-nxc000.
2025-08-01 17:39:34,852 INFO Got the host list from retseelm-nxc000.
2025-08-01 17:39:34,852 INFO Got the host list.
2025-08-01 17:39:34,852 INFO Getting vault from IKEAD2.
2025-08-01 17:39:35,583 INFO Getting Site_Pe_Nutanix.
2025-08-01 17:39:36,075 INFO Got Site_Pe_Nutanix.
2025-08-01 17:39:36,075 INFO Getting Site_Pe_Admin.
2025-08-01 17:39:36,547 INFO Got Site_Pe_Admin.
2025-08-01 17:39:36,548 INFO Getting Site_Oob.
2025-08-01 17:39:37,011 INFO Got Site_Oob.
2025-08-01 17:39:37,011 INFO Getting Site_Ahv_Nutanix.
2025-08-01 17:39:37,537 INFO Got Site_Ahv_Nutanix.
2025-08-01 17:39:37,538 INFO Getting Site_Ahv_Root.
2025-08-01 17:39:38,008 INFO Got Site_Ahv_Root.
2025-08-01 17:39:38,008 INFO Getting Site_Gw_Priv_Key.
2025-08-01 17:39:38,492 INFO Got Site_Gw_Priv_Key.
2025-08-01 17:39:38,492 INFO Getting Site_Gw_Pub_Key.
2025-08-01 17:39:38,960 INFO Got Site_Gw_Pub_Key.
2025-08-01 17:39:38,961 INFO Getting Site_Pe_Svc.
2025-08-01 17:39:39,499 INFO Got Site_Pe_Svc.
2025-08-01 17:39:39,510 INFO Checking if cluster 'RETSEELM-NXC000' exists in ssp-dhd2-ntx.ikead2.com.
2025-08-01 17:42:03,962 INFO Start reset CVM Nutanix password
2025-08-01 17:42:03,975 INFO Attempting to reset password for user 'nutanix' on cluster associated with CVM RETSEELM-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-01 17:42:03,976 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-01 17:42:06,476 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-01 17:42:06,487 INFO Unlocking user 'nutanix' on all CVMs.
2025-08-01 17:42:06,487 INFO SSH Executing 'allssh sudo faillock --user nutanix --reset'.
2025-08-01 17:42:07,358 INFO Waiting for 10 seconds for the execution.
2025-08-01 17:42:17,359 INFO stdout: b''
2025-08-01 17:42:17,373 INFO Changing password for 'nutanix' on single node RETSEELM-NXC000.ikead2.com.
2025-08-01 17:42:17,374 INFO SSH Executing '*****************************************************'.
2025-08-01 17:42:17,889 INFO Waiting for 5 seconds for the execution.
2025-08-01 17:42:22,890 INFO stdout: b'Changing password for user nutanix.\npasswd: all authentication tokens updated successfully.\n'
2025-08-01 17:42:22,903 INFO Password change command sent. Verifying new password for 'nutanix' with a new SSH connection.
2025-08-01 17:42:27,904 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-01 17:42:30,417 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-01 17:42:30,434 INFO Successfully reset and verified new password for user 'nutanix'.
2025-08-01 17:42:30,454 INFO Saving token to Vault... Username: nutanix, label: RETSEELM-NXC000/Site_Pe_Nutanix
2025-08-01 17:42:31,464 INFO Saving token completed.
2025-08-01 17:42:32,443 INFO taking a 30S extra powernap
2025-08-01 17:43:02,455 INFO Start reset admin password
2025-08-01 17:43:02,468 INFO Attempting to reset password for user 'admin' on cluster associated with CVM RETSEELM-NXC000.ikead2.com, authenticating as 'nutanix'.
2025-08-01 17:43:02,469 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-01 17:43:04,958 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-01 17:43:04,969 INFO Unlocking user 'admin' on all CVMs.
2025-08-01 17:43:04,969 INFO SSH Executing 'allssh sudo faillock --user admin --reset'.
2025-08-01 17:43:05,849 INFO Waiting for 10 seconds for the execution.
2025-08-01 17:43:15,850 INFO stdout: b''
2025-08-01 17:43:15,864 INFO Changing password for 'admin' on single node RETSEELM-NXC000.ikead2.com.
2025-08-01 17:43:15,865 INFO SSH Executing '***************************************************'.
2025-08-01 17:43:16,386 INFO Waiting for 5 seconds for the execution.
2025-08-01 17:43:21,387 INFO stdout: b'Changing password for user admin.\npasswd: all authentication tokens updated successfully.\n'
2025-08-01 17:43:21,413 INFO Password change command sent. Verifying new password for 'admin' with a new SSH connection.
2025-08-01 17:43:26,414 INFO SSH connecting to RETSEELM-NXC000.ikead2.com, this is the '1' try.
2025-08-01 17:43:28,969 INFO SSH connected to RETSEELM-NXC000.ikead2.com.
2025-08-01 17:43:28,989 INFO Successfully reset and verified new password for user 'admin'.
2025-08-01 17:43:29,000 INFO Saving token to Vault... Username: admin, label: RETSEELM-NXC000/Site_Pe_Admin
2025-08-01 17:43:30,420 INFO Saving token completed.
2025-08-01 17:43:33,754 INFO This is a central PE, start to reset PCVM password...
2025-08-01 17:43:36,145 INFO Resetting password for Site_Pc_Nutanix...
2025-08-01 17:43:44,095 INFO taking a 30S extra powernap
2025-08-01 17:44:16,544 INFO Start reset PCVM Site_Pc_Nutanix password
2025-08-01 17:44:21,965 INFO Attempting to reset password for user 'nutanix' on cluster associated with CVM ***********2, authenticating as 'nutanix'.
2025-08-01 17:44:29,915 INFO SSH connecting to ***********2, this is the '1' try.
2025-08-01 17:44:32,394 INFO SSH connected to ***********2.
2025-08-01 17:44:35,250 INFO Unlocking user 'nutanix' on all CVMs.
2025-08-01 17:44:37,008 INFO SSH Executing 'allssh sudo faillock --user nutanix --reset'.
2025-08-01 17:44:37,525 INFO Waiting for 10 seconds for the execution.
2025-08-01 17:44:47,527 INFO stdout: b''
2025-08-01 17:44:48,455 INFO Changing password for 'nutanix' on single node ***********2.
2025-08-01 17:44:49,776 INFO SSH Executing '*****************************************************'.
2025-08-01 17:44:50,300 INFO Waiting for 5 seconds for the execution.
2025-08-01 17:44:55,301 INFO stdout: b'Changing password for user nutanix.\npasswd: all authentication tokens updated successfully.\n'
2025-08-01 17:45:08,890 INFO Password change command sent. Verifying new password for 'nutanix' with a new SSH connection.
2025-08-01 17:45:17,034 INFO SSH connecting to ***********2, this is the '1' try.
2025-08-01 17:45:19,501 INFO SSH connected to ***********2.
2025-08-01 17:45:22,391 INFO Successfully reset and verified new password for user 'nutanix'.
2025-08-01 17:45:31,058 INFO Resetting password for Site_Pc_Admin...
2025-08-01 17:45:41,324 INFO taking a 30S extra powernap
2025-08-01 17:46:14,001 INFO Start reset PCVM Site_Pc_Admin password
2025-08-01 17:46:16,769 INFO Attempting to reset password for user 'admin' on cluster associated with CVM ***********2, authenticating as 'nutanix'.
2025-08-01 17:46:24,040 INFO SSH connecting to ***********2, this is the '1' try.
2025-08-01 17:46:26,547 INFO SSH connected to ***********2.
2025-08-01 17:46:30,154 INFO Unlocking user 'admin' on all CVMs.
2025-08-01 17:46:31,512 INFO SSH Executing 'allssh sudo faillock --user admin --reset'.
2025-08-01 17:46:32,036 INFO Waiting for 10 seconds for the execution.
2025-08-01 17:46:42,038 INFO stdout: b''
2025-08-01 17:46:43,679 INFO Changing password for 'admin' on single node ***********2.
2025-08-01 17:46:45,598 INFO SSH Executing '***************************************************'.
2025-08-01 17:46:46,127 INFO Waiting for 5 seconds for the execution.
2025-08-01 17:46:51,128 INFO stdout: b'Changing password for user admin.\npasswd: all authentication tokens updated successfully.\n'
2025-08-01 17:46:56,927 INFO Password change command sent. Verifying new password for 'admin' with a new SSH connection.
2025-08-01 17:47:04,013 INFO SSH connecting to ***********2, this is the '1' try.
2025-08-01 17:47:06,481 INFO SSH connected to ***********2.
2025-08-01 17:47:08,896 INFO Successfully reset and verified new password for user 'admin'.
2025-08-01 17:47:25,695 INFO Start reset 1-click-nutanix password for PE RETSEELM-NXC000
2025-08-01 17:47:56,195 INFO Calling restapi, URL: https://RETSEELM-NXC000.IKEAD2.COM:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-08-01 17:47:56,195 INFO params: None
2025-08-01 17:47:56,196 INFO User: admin
2025-08-01 17:47:56,196 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-08-01 17:47:56,196 INFO files: None
2025-08-01 17:47:56,196 INFO timeout: None
2025-08-01 17:48:04,389 INFO Calling restapi, URL: https://ssp-dhd2-ntx.ikead2.com:9440/PrismGateway/services/rest/v1/users/reset_password, method: POST, headers: None
2025-08-01 17:48:04,389 INFO params: None
2025-08-01 17:48:04,389 INFO User: admin
2025-08-01 17:48:04,390 INFO payload: {'username': '1-click-nutanix', 'password': '*****'}
2025-08-01 17:48:04,390 INFO files: None
2025-08-01 17:48:04,390 INFO timeout: None

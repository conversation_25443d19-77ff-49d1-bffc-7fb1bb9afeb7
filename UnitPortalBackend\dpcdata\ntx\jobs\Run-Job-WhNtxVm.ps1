$Global:DumpFile = New-Item -Type File `
                            -Path "C:\UnitPortalJobLogs\$(Get-Date -Format FileDate)\$($MyInvocation.MyCommand.Name.Split("v")[0])t$((Get-Date -Format FileDateTime).Split("T")[1]).log" `
                            -Force
#Check if the PS versioin is less than 7, than quit
if ($PSVersionTable.PSVersion.Major -lt 7) {
    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) The current PS version is $($PSVersionTable.PSVersion.Major), 7 or above is required, exit"
    Write-Host $Message -ForegroundColor Red
    Add-Content -Path $DumpFile -Value $Message
    Exit 0
}
function Launch-Job(){
    Begin {
        $ModuleItems = @(Get-ChildItem -Path "$PSScriptRoot\..\modules" -Filter "*.psm1")
        $ModuleItems | Foreach-Object {
            try {
                Import-Module -Name $_.VersionInfo.FileName `
                              -DisableNameChecking:$true `
                              -Force
            }
            catch {
                $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                Write-Host $Message -ForegroundColor Red
                Add-Content -Path $DumpFile -Value $Message
                Exit 0
            }
        }
        try {
            $Vars             = Load-Vars
            $DhPCs            = Select-DhWhNtxPc -Vars $Vars
            $DhPEs            = Select-DhWhNtxPe -Vars $Vars | Where-Object {$_.status -ne "Decommissioned"}
            $CollectionL      = Select-DhWhNtxVm -Vars $Vars
            $CollectionR      = [System.Collections.Concurrent.ConcurrentBag[psobject]]::new()
            $CollectionInsert = @()
            $CollectionUpdate = @()
            $LastUpdate       = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
        catch {
            Write-Console-Logs -Level ERROR -FunctionName $(Get-FunctionName) -Message "Exception occurred when launching job. Cause: $_ Exit" -DumpFile $DumpFile
            Exit 0
        }
    }
    Process {
        $DhPCs | Foreach-Object -ThrottleLimit 10 -Parallel {
            $Global:DumpFile = $using:DumpFile
            $using:ModuleItems | Foreach-Object {
                try {
                    Import-Module -Name $_.VersionInfo.FileName `
                                  -DisableNameChecking:$true `
                                  -Force
                }
                catch {
                    $Message = "[ERROR] $(Get-Date -Format "MM/dd/yyyy HH:mm:ss") $($MyInvocation.MyCommand.Name) Exception occurred when importing modules. Cause: $_ Exit"
                    Write-Host $Message -ForegroundColor Red
                    Add-Content -Path $DumpFile -Value $Message
                    Exit 0
                }
            }
            Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "We're now working on '$($_.fqdn)'" -DumpFile $using:DumpFile
            $PC         = $_
            $DictR      = $using:CollectionR
            $SvcAccount = Select-DhServiceAccount -Vars $using:Vars -Usage $PC.service_account | Select-Object -First 1
            if (!$SvcAccount) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to get service account" -DumpFile $using:DumpFile
                return
            }
            $Auth = Get-Base64Auth -Username $SvcAccount.username -PWord (Decrypt-String -Key $SvcAccount.key -IV $SvcAccount.iv -Encrypted $SvcAccount.crypted)
            if (!$Auth) {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed to generate authentication" -DumpFile $using:DumpFile
                return
            }
            if ($PrismCall1 = Rest-Prism-v1-List-Vm -Fqdn $PC.Fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "'$($PrismCall1.entities.count)' VMs are available in '$($PC.Fqdn)'" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to list VMs" -DumpFile $DumpFile
                return
            }
            if ($PrismCall2 = Rest-Prism-v3-List-Vm -Fqdn $PC.Fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "'$($PrismCall2.count)' VMs from v3 are available in '$($PC.Fqdn)'" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism v3 to list VMs" -DumpFile $using:DumpFile
            }
            if ($PrismCall3 = Rest-Prism-v1-List-Cluster -Fqdn $PC.Fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "'$($PrismCall3.entities.count)' PEs profile are available in '$($PC.Fqdn)'" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to list PEs" -DumpFile $using:DumpFile
            }
            #List all disks under the PC
            if ($PrismCall4 = Rest-Prism-v1-List-VirtualDisk -Fqdn $PC.Fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "'$($PrismCall4.entities.count)' virtual disks are available in '$($PC.Fqdn)'" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to list virtual disks" -DumpFile $using:DumpFile
            }
            #List all Subnets under the PC
            if ($PrismCall5 = Rest-Prism-v3-List-Subnet -Fqdn $PC.fqdn -Auth $Auth) {
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "'$($PrismCall5.count)' subnets are available in '$($PC.Fqdn)'" -DumpFile $using:DumpFile
            }else {
                Write-Console-Logs -Level WARN -FunctionName (Get-FunctionName) -Message "Failed when calling Prism to list subnets" -DumpFile $using:DumpFile
            }
            #Assemble the VM data models
            Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "All data are collected in '$($PC.Fqdn)', let's do some actual works" -DumpFile $using:DumpFile
            foreach ($VM in $PrismCall1.entities) {
                $VMv3        = $PrismCall2 | Where-Object {$_.metadata.uuid -eq $VM.uuid}
                $PE          = $PrismCall3.entities | Where-Object {$_.uuid -eq $VM.clusterUuid}
                $Name        = $VM.vmName.ToUpper() -replace ".IKEA.COM", ""
                $VmDisks     = ""
                $PrismCall4.entities | Where-Object {$_.attachedVmUuid -eq $VM.uuid} | ForEach-Object {
                    $VmDisks += $_.diskAddress + ":" + $([int][Math]::Ceiling($_.diskCapacityInBytes / [Math]::Pow(1024,3))) + " GiB, "
                }
                $VmDisks     = $VmDisks.TrimEnd(", ")
                $Category    = Resolve-VmCategory -Object $Name
                $OS          = $Category.OS
                $Type        = $Category.Type
                $BootType    = if ($VMv3.status.resources.boot_config.boot_type) {
                                    $VMv3.status.resources.boot_config.boot_type
                                }else {
                                    "Undefined"
                                }
                $NICs        = $VMv3.spec.resources.nic_list
                $NetworkVlan = ""
                foreach ($NIC in $NICs) {
                    $Subnet       = $PrismCall5 | Where-Object {$_.metadata.uuid -eq $NIC.subnet_reference.uuid}
                    $NetworkVlan += "" + $Subnet.status.resources.vlan_id + ", "
                }
                $NetworkVlan = $NetworkVlan.TrimEnd(", ")
                $NetworkVlan = if (!$NetworkVlan) {"NA"}
                $DhPE        = $using:DhPEs | Where-Object {$_.uuid -eq $VM.clusterUuid}
                Write-Console-Logs -Level INFO -FunctionName (Get-FunctionName) -Message "Processing the VM '$Name' payload" -DumpFile $using:DumpFile
                try {
                    $R = [PSCustomObject]@{
                        'name'         = $Name
                        'uuid'         = $VM.uuid
                        'pe_name'      = $PE.name
                        'pe_fqdn'      = $PE.name.ToLower() + "." + $PC.domain
                        'pe_uuid'      = $VM.clusterUuid
                        'host_name'    = $VM.hostName
                        'host_uuid'    = $VM.hostUuid
                        'description'  = $VM.description
                        'disk'         = $VmDisks
                        'memory'       = [int][Math]::Ceiling($VM.memoryCapacityInBytes / [Math]::Pow(1024,3))
                        'cpu_core'     = $VM.numVCpus
                        'power_state'  = $VM.powerState
                        'ip'           = $VM.ipAddresses
                        'boot_type'    = $BootType
                        'network_vlan' = $NetworkVlan
                        'is_cvm'       = if ($VM.vmName -match '-CVM') {"Y"}else {"N"}
                        'pe_id'        = $DhPE.id
                        'status'       = 'Available'
                        'os'           = $OS
                        'type'         = $Type
                        'last_update'  = $using:LastUpdate
                    }
                    $DictR.Add($R)
                }
                catch {
                    Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "Exception occurred when assembling payload for '$Name'. Cause: $_" -DumpFile $Global:DumpFile
                }
            }
        }
        if (0 -eq $CollectionR.Count) {
            Write-Console-Logs -Level WARN -FunctionName $(Get-FunctionName) -Message "There is no data need to deal with. Exit" -DumpFile $DumpFile
            Exit 0
        }
        $CollectionR | ForEach-Object {
            if ($_.uuid -notin $CollectionL.uuid) {
                $CollectionInsert += $_
            }else {
                $CollectionUpdate += $_
            }
        }
        $CollectionL | ForEach-Object {
            if ($_.uuid -notin $CollectionR.uuid) {
                $L = [PSCustomObject]@{
                    'name'         = $_.name
                    'uuid'         = $_.uuid
                    'pe_name'      = $_.pe_name
                    'pe_fqdn'      = $_.pe_fqdn
                    'pe_uuid'      = $_.pe_uuid
                    'host_name'    = 'NA'
                    'host_uuid'    = 'NA'
                    'description'  = $_.description
                    'disk'         = $_.disk
                    'memory'       = $_.memory
                    'cpu_core'     = $_.cpu_core
                    'power_state'  = 'NA'
                    'ip'           = $_.ip
                    'boot_type'    = $_.boot_type
                    'network_vlan' = $_.network_vlan
                    'is_cvm'       = $_.is_cvm
                    'pe_id'        = $_.pe_id
                    'status'       = 'Unavailable'
                    'os'           = $_.os
                    'type'         = $_.type
                    'last_update'  = $LastUpdate
                }
                $CollectionUpdate += $L
            }
        }
    }
    End {
        Write-Console-Logs -Level INFO -FunctionName $(Get-FunctionName) -Message "Sending data to the table [dh_wh_ntx_vm]" -DumpFile $DumpFile
        Insert-Table-DhWhNtxVm -Vars $Vars -Collection $CollectionInsert
        Update-Table-DhWhNtxVm-ByUuid -Vars $Vars -Collection $CollectionUpdate
    }
}
Launch-Job
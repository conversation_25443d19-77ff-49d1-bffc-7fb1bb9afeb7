<template>
    <div class="app-container" >
      <div class="left-container" >
        <div style="margin-left: 2px;padding: 28px 6px 4px; ">
          <el-row >
            <el-col :span="6"  >
              <div >
                <star-rating :rounded-corners="true" :animate="true" :inline="true" :star-size="30" :read-only="true" :show-rating="false" :rating="starnum" :increment="0.1"></star-rating>
                
              </div>
              <div><span> Satisfactory : {{ starnum }} ({{ commentnum}})</span></div>
            </el-col>
            <el-col :span="16">
              <el-switch
                v-model="commentswitch"
                active-text="All"
                inactive-text="Personal"
                @change="cs_change"
                style="margin-top: 8px;float: right; ">
              </el-switch>
              <!-- <el-button  class="filter-item"  type="primary" size="mini" @click="newcomment" style="margin-left: 26px;">
                Comment
              </el-button> -->
            </el-col>
            <el-col :span="2">
              <el-button  class="filter-item"  type="primary" size="medium" @click="newcomment" style="margin-left: 2vh;">
                Comment
              </el-button>
            </el-col>
          </el-row>
        </div>
        <el-table :key="tableKey" 
                  v-loading="listLoading" 
                  :data="current_list" 
                  border
                  stripe
                  fit 
                  highlight-current-row 
                  style="width: 100%;" 
                  @sort-change="sortChange" 
                  @row-click="handle_row_click"
                  ref='cltable'>
          <el-table-column label="Date" prop="date" align="center" min-width="10%" sortable="custom" overflow:hidden >
              <template slot-scope="{row}">
                <i class="el-icon-time"></i>
                <span>{{ row.date }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Name" prop="name" align="center" min-width="15%" sortable="custom" overflow:hidden >
              <template slot-scope="{row}">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Star" prop="star" align="center" min-width="6%" sortable="custom" >
              <template slot-scope="{row}">
                <span>{{ row.star }}</span>
              </template>
            </el-table-column>
      
            <el-table-column  label="Comments" class-name="status-col" min-width="13%" align="left"  sortable="custom" prop="comment" overflow:hidden >
              <template slot-scope="{row}">
                <!-- <span v-if="row.comment!=null"> {{ row.comment }}</span><span class="bigger_font" v-else>NA<span class="link-type el-icon-edit smaller_font" @click="handle_commentupdate('comment')">add</span></span> -->
                <!-- <span v-if="row.comment==null"> NA<span class="link-type el-icon-edit smaller_font"  @click="handle_commentupdate('solution',row.name, row.comment)">add</span></span>
                <span class="bigger_font" v-else>{{ row.comment }}</span> -->
                <span >{{ row.comment}}</span>
                
              </template>
            </el-table-column>

            <el-table-column label="Solutions" class-name="status-col" min-width="13%" align="left" sortable="custom" prop="solution" overflow:hidden >
              <template slot-scope="{row}">
                <!-- <span v-if=" row.solution=null">NA</span><span class="link-type el-icon-edit"  > Add</span> -->
                 <span v-if="row.solution!=null"> {{ row.solution }}<span class="link-type el-icon-edit smaller_font" v-if="ifpriv" @click="handle_commentupdate('solution',row.name, row.solution)">Edit</span></span><span class="bigger_font" v-else>NA<span class="link-type el-icon-edit smaller_font" v-if="ifpriv" @click="handle_commentupdate('solution',row.name, row.solution)">add</span></span>
                <!-- <span >{{ row.solution|solution_filter}}</span><span class="link-type el-icon-edit"  > Edit</span> -->
              </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="get_commentlist" />
      </div>
      <el-dialog :title="'Feedback to DH UnitPortal'" :visible.sync="dialogcomment" width="30%" :close-on-click-modal="false" class="el-dialog__body" >
        <div class="drawer-container">
          <!-- <div> -->
            <!-- <h2 class="drawer-title">Give Feedback to GDH</h2> -->
            <div :class="currenttextshow ? 'drawer-item-showtext' : 'drawer-item' ">
              <span style="font-size: 19px;">Overall, is this tool useful?</span>
              <div style="position: relative;padding:4px 10vh">
                <star-rating @update:rating="handleRatingChange" :rounded-corners="true" :animate="true" :clearable="true" :key="refreshKey" :increment=0.5 :star-size="45" :padding="10"></star-rating>
              </div>
                <div style="margin-top:6px;" v-show="currenttextshow">{{currentRatingText}}</div>
            </div>
            
          <!-- </div> -->
          <div>
            <div :class="currenttextshow? 'drawer-item-showtext' : 'drawer-item'">
                <span style="font-size: 19px;padding:4px 0">Any ideas or requests?</span>
                <el-input
                  type="textarea"
                  :rows="5"
                  resize="none"
                  maxlength="500"
                  show-word-limit
                  placeholder="Please input your comments here..."
                  v-model="comments">
                </el-input>
              </div>
          </div>
          <div>
            <!-- <div class=drawer-item> -->
            <div slot="footer" class="dialog-footer" style="margin-top: 15px;">
              <el-checkbox v-model="anonymous">anonymous</el-checkbox>
              <el-button style="position:absolute; right: 130px;" @click="dialogcomment=false">
                Cancel
              </el-button>
              <el-button type="primary" style="position:absolute; right: 20px;" @click=create_comment()>
                Submit
              </el-button>
            </div>
          </div>
        </div>
      </el-dialog>
      <el-dialog :title="titlecp" :visible.sync="dialogcommentupdate" width="30%" :close-on-click-modal="false" >
          <el-form ref="commentform" :model="commentform" label-width="120px">
            <el-form-item label="Activity name">
              <el-input v-model="commentform.name" readonly></el-input>
            </el-form-item>
            <!-- <el-form-item label="Activity name">
              <el-input v-model="commentform.star" readonly></el-input>
            </el-form-item> -->

            <el-form-item label="Activity form">
              <el-input
                type="textarea"
                :rows="6"
                maxlength="500"
                show-word-limit
                placeholder="Please input your comments here..."
                v-model="commentform.comments">
              </el-input>
            </el-form-item>
          </el-form>
           <div slot="footer" class="dialog-footer">
            <el-button @click="dialogcommentupdate = false">
              Cancel
            </el-button>
            <el-button type="primary" @click="updatecheck? update_comment('comment'):update_comment('solution')">
              Confirm
          </el-button>
          </div>
        <!-- </div> -->
      </el-dialog>
    </div>
  </template>
  
  <script>
    import waves from '@/directive/waves' // waves directive
    import { parseTime } from '@/utils'
    import Pagination from '@/components/Pagination'
    import StarRating from '@/layout/components/starrate/star-rating.vue'
    import { GetCommentList, CreatCommentRecord, UpdateCommentRecord} from '@/api/upwidgets'
    

    export default {
      name: 'UserTable',
      components: {
        StarRating,
        Pagination
        },
      directives: { waves },
      filters: {
        anonymous_filter(row){
          if(row['anonymous']=="T"){
            return "Anonymous"
          }
          else{
            return row['name']
          }
        },
        comments_filter(comment){
          if(comment==null){
            return "NA"
          }
          else{
            return comment
          }
        },
        solution_filter(solution){
          if(solution==null){
            return "NA"
          }
          else{
            return solution
          }
        },
      },
      data() {
        return {
          priv:"",
          ifpriv:false,
          ifac: true,
          titlecp:'',
          username: this.$store.getters.name,
          commentid: this.$route.query.id,
          listLoading: true,
          dialogapiVisible: false,
          value9:'',
          commentswitch:true,
          tableKey: 0,
          filtered_list: null,
          current_list: null,
          page_list: null,
          updatecheck:false,
          dialogcomment:false,
          dialogcommentupdate:false,
          comments: '',
          rating: 0,
          totalrate:0,
          anonymous: false,
          currenttextshow: false,
          refreshKey: 0,
          selectedrow: '',
          commentform: {
            name: '',
            comments: '',
            star:''
          },
          listQuery: {
            page: 1,
            limit: 20,
            cluster: '',
            prism: '',
            status: '',
            sort: '+id'
          },
          filter:{
            startdate:'',
            enddate:'',
            comment_list:[],
            selected_vhost:[],
            fuzzy_string: "",
            hide_ovc: false
          },
          lcm : '',
          aos : '',
          ntxpm : '',
          slipm : '',
          toolbox : '',
          workload : '',
          template : '',
          dashboard : '',
          ntxmovewh : '',
          automaintenance : '',
          pmnumber: '',
          maintenancenum :'',
          workloadnum :'',
          apifbgettimes:'',
          apifbcreatetimes:'',
          apifbtracerttimes: '',
          apifbget:'',
          apifbcreate:'',
          apifbtracert: '',
          commentlist:'',
          starnum: 0,
          commentnum:0,
          total: 0,
          fdget: 0,
          fdtra: 0,
          apicount:0,
          newapilist:[],
          apiutilist: [],
          tempapiutilist: [],
          currentapitasklist: [],
          tempapitasklist: [],
          startdate:'',
          enddate:'',
          apitasklist: [
            {
              name:'FeedbackGet',
              router:'/api/v1/upwidgets/feedback/get'
            },
            {
              name:'FeedbackTracert',
              router:'/api/v1/upwidgets/webpage/tracert'
            },
            {
              name: 'PM',
              router:'/api/v1/pm'
            },
            {
              name: 'Automation',
              router:'/api/v1/ntx/automation'
            },
            {
              name: 'Workload',
              router:'/api/v1/ntx/workload'
            },
          ],
          apilist: [
            {
              name: 'login',
              times:0,
              router:'/api/v1/login'
            },
            {
              name: 'workload',
              times:0,
              router:'/api/v1/ntx/workload'
            },
            {
              name: 'pm',
              times:0,
              router:'/api/v1/pm'
            },
            {
              name: 'automation',
              times:0,
              router:'/api/v1/ntx/automation'
            },
          ],
          funcinfolist: [
            {
              name: "Automation",
              totalnum: 0,
              data: {},
              func:['GetSPPTasks', 'GetAOSTaskList', 'Get_Tasklist_Move', 'Get_DSC_Tasks', 'Get_PWRotate_Tasks', 'Get_CER_Tasks', 'GetCleanupWorkloadTasks']
            },
            {
              name: 'PM',
              totalnum: 0,
              data: {},
              func:['GetSLIPMTasks', 'GetNTXPMTasks']
            },
            {
              name: 'Workload',
              totalnum: 0,
              data: {},
              fun:['GetNTXPMTasks',]
            },
          ],
        }
      },
      computed: {
        currentRatingText: {
          get() {
            if (this.rating) {
              this.currenttextshow = true
              let curtext = eval(this.rating) <3? this.rating +" stars? Anything must be mistaken?" : "Thanks, You are so nice! ";
              return curtext
            }
            }
        },
        ac: {
          get() {
            let ifac = this.$route.query.id == 'mycomments' ? false : true
            return ifac
          }
          
        },
    },
    watch: {
      $route(route) {
        // if you go to the redirect page, do not update the breadcrumbs
        if (route.query.id == 'mycomments') {
          this.ifac=false
          this.commentswitch=false
        } else {
            this.ifac = true
            this.commentswitch=true
        }
        this.filter_commentlist()
      },
    },
    
    created() {
      this.handle_commentfilter()
      this.get_commentlist()
      this.check_priv()
      // this.get_taskslist()
  
    },
    methods: {
      cs_change(val){
        this.ifac = val

        this.filter_commentlist()
      },
      check_priv(){
        this.priv = this.$store.getters.all_privilege
        this.ifpriv = this.priv.role_administration.edit_comment =='full'? true:false
      },
      handle_commentfilter() {
        this.ifac = this.$route.query.id=='mycomments'? false:true
      },
      handle_commentupdate(col,userid,content) {
        if (this.$store.getters.name != userid) {
          this.$alert("Please Edit your own comment records, thanks. ", "Alert", {
              confirmButtonText: "Confirm",
          });
          return false
        }
        this.dialogcommentupdate = true
        this.updatecheck = col == "comment" ? true : false
        this.titlecp= col == "comment" ? 'Update Comments': 'Update Solutions'
        // this.commentform.comments= col == "comment" ? this.selectedrow.comment: this.selectedrow.solution
        this.commentform.comments= content
        this.$nextTick(() => {
        this.$refs['commentform'].clearValidate()
      })
        
      },
      get_taskslist() {
        for (let f of this.funcinfolist) {
          let _this =this
          for (let ff of f['fun']) {
            // ff+'()'
            eval(ff.split(' ')+'()');
            let tt= ff+'('+_this.$store.getters.token+')'
            tt(_this.$store.getters.token).then(response => {
              // let totalrate =0
            })}
          // f['fun'].forEach(e => {
          //   console.log(_this)
          //   let token = _this.$store.getters.token
          //   console.log(token)
          //   // eval(e+'()').then(response => {
          //   eval(e)(token).then(response => {
          //     // let totalrate =0
          //     console.log("hhhhhhhhh")
          //     console.log(response.data)
              // this.commentlist = response.data
              // this.listLoading = false
              // this.commentnum = this.commentlist.length
              // response.data.forEach(e => {
              //   totalrate = totalrate +parseFloat(e.star)

              // });
              // this.starnum = parseFloat((totalrate/this.commentnum).toFixed(1))
              // this.filter_commentlist()         
          //   })
          // })
        }
      },
     
      get_commentlist() {
        GetCommentList(this.$store.getters.token).then(response => {
          let totalrate =0
          this.commentlist = response.data
          this.listLoading = false
          this.commentnum = this.commentlist.length
          response.data.forEach(e => {
            totalrate = totalrate +parseFloat(e.star)

          });
          this.starnum = parseFloat((totalrate/this.commentnum).toFixed(1))
          this.filter_commentlist()         
        })
      },
      filter_commentlist() {
          let a = this
          if (this.ifac) {
            this.filtered_list = this.commentlist
          } else {
            this.filtered_list = this.commentlist.filter(function (e) {
              if(e.if_namematched){
                return true
              }
            })
          }
          this.total = this.filtered_list.length
          let page = this.listQuery.page
          let limit = this.listQuery.limit
          let start , end
          if(page*limit>=this.total){
            start = (page-1)*limit
            end = this.total
          }
          else{
            start = (page-1)*limit
            end = page * limit
          }
          this.current_list = this.filtered_list.slice(start,end)
          this.listLoading = false
          // let all_comment_list = this.commentlist.map((obj,index)=>{return obj['date']})
          // this.filter.comment_list = this.remove_duplicate(all_comment_list)
          
      },
      create_comment() {
        let payload = {
          data: {
            "star": this.rating,
            "comment": this.comments,
            "anonymous": this.anonymous? 1:0
          },
          token: this.$store.getters.token
        }
        CreatCommentRecord(payload).then(() => {
          this.$notify({
                title: 'Success',
                message: 'Successed to create the comment.',
                type: 'success',
                duration: 2000
              })
              this.comments = ''
              this.anonymous = false
              this.currenttextshow = false
              this.dialogcomment=false
              this.get_commentlist()
              this.handleRefresh()
              
            }
        )
        .catch((error)=>{
          this.$notify({
                title: 'Error',
                message: 'Failed to create the comment...',
                type: 'error',
                duration: 2000
            })
        })
      },
      update_comment(v) {
        this.selectedrow[v] = this.commentform.comments
        let payload = {
          data: this.selectedrow,
          token: this.$store.getters.token
        }
        UpdateCommentRecord(payload).then(() => {
          this.$notify({
                title: 'Success',
                message: 'Successed to update the comment.',
                type: 'success',
                duration: 2000
            })
            // this.resetForm(commentform) 
            this.commentform.comments=''
            this.dialogcommentupdate=false
              // this.handleRefresh()
            }
        )
        .catch((error)=>{
          this.$notify({
                title: 'Error',
                message: 'Failed to update the comment...',
                type: 'error',
                duration: 2000
            })
        })
      },
      newcomment(){
        this.dialogcomment=true
      },
      handleRefresh() {
          // 点击刷新按钮，改变 refreshKey 的值触发组件的重新渲染
          this.refreshKey++
        },
      handleRatingChange(newRating) {
          this.rating = newRating
        },
      remove_duplicate(arr) {
          //remove the duplicated ones
          const newArr = []
          arr.forEach(item => {
            if (!newArr.includes(item)) {
              newArr.push(item)
            }
          })
          return newArr
        },
      handleFilter() {
        this.listQuery.page = 1
      },
      set_page(){
      // 设置当前分页的表格显示的条目， 根据 page 号和 page长度计算
      let page = this.listQuery.page
      let limit = this.listQuery.limit
      let start , end
      if(page*limit>=this.total){
        start = (page-1)*limit
        end = this.total 
      }
      else{
        start = (page-1)*limit
        end = page * limit
      }
      this.current_list = this.filtered_list.slice(start,end)
    },
    sortChange(data) {
        const { prop, order } = data
        if(order==null){
          this.sortChange({prop:'id',order:'ascending'})
          return 
        }
        let flag_num = order=="ascending" ? 1 : -1
        this.filtered_list.sort((item1,item2)=>(
          (item1[prop] > item2[prop]) ? flag_num*1 : ((item1[prop] < item2[prop]) ? flag_num*-1 : 0)
        ))
        this.set_page()
      },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+id'
      } else {
        this.listQuery.sort = '-id'
      }
      this.handleFilter()
    },
      // async handle_user_edit_dialog() {
      //   //open the edit user dialog
      //   if(!this.selectedrow){
      //     this.$message({
      //         type: 'warning',
      //         message: 'Select a user first.',
      //         duration: 5000
      //       });  
      //     return 
      //   }
      //   this.edit_user.check_group = await this.get_single_user_role()
      //   console.log("edit user checkgroup")
      //   console.log(this.edit_user.check_group)
      //   this.edit_user_dialog_form_visible = true
      //   this.$nextTick(() => {
      //     this.$refs['edituserForm'].clearValidate()
      //   })
      // },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      },
      
        handle_row_click(row,column,event){
          this.selectedrow = row
          this.commentform.name = this.selectedrow.name
          // this.commentform.comments = this.selectedrow.comment
          this.commentform.star = this.selectedrow.star
        },
        formatJson(filterVal) {
          return this.list.map(v => filterVal.map(j => {
            if (j === 'timestamp') {
              return parseTime(v[j])
            } else {
              return v[j]
            }
          }))
        },
        getSortClass: function(key) {
          const sort = this.listQuery.sort
          return sort === `+${key}` ? 'ascending' : 'descending'
        },
        
        handle_search(){

          if(this.listQuery.fuzzy.trim().length){
            let temp_list = this.all_user_list
            let fuzzy_list = this.listQuery.fuzzy.trim().split(/\s+/)
            for(let fuzzy of fuzzy_list){
              fuzzy = fuzzy.toString().toLowerCase()
              temp_list = temp_list.filter((k)=>{
                if( k.username.toString().toLowerCase().search(fuzzy)!= -1){
                  return true
                }
              })
            }
            this.filtered_list = temp_list
          }
          this.set_page()
        }
      },
      beforeDestroy(){
        clearInterval( this.intervaljob )
      }
    }
  </script>
  <style lang="scss" scoped>
  .el-dialog__body{
    padding: 15px 20px 30px 20px;
  }
  .drawer-container {
    line-height: 1.5;
    word-wrap: break-word;
    // overflow: hidden;

    .drawer-title {
      // margin-bottom: 1px;
      color: rgba(0, 0, 0, .85);
      font-size: 28px;
      line-height: 20px;
    }

    .drawer-item {
      color: rgba(0, 0, 0, .65);
      font-size: 14px;
      padding: 8px 0;
      overflow: hidden;
          
    }
    .drawer-item-showtext {
      color: rgba(0, 0, 0, .65);
      font-size: 14px;
      padding: 3px 0;
    }

    .drawer-switch {
      float: right
    }

    .custom-text {
      font-weight: bold;
      font-size: 1.9em;
      border: 1px solid #cfcfcf;
      padding-left: 10px;
      padding-right: 10px;
      border-radius: 5px;
      color: #999;
      background: #fff;
    }
    
  }
  .bigger_font {
      font-size: 16px;
    }
  .smaller_font {
      font-size: 12px;
      position:absolute;
      right:8px;
      bottom:5px;
    }
  .user-table span{
    font-size: 17px
  }

  .left-table,.right-table {
    width: 50%;
    float: left;
  }

  .el-main {
    /* background-color: #c996cc;
    color: #333; */
    /* width: 50%; */
    text-align: center;
    /* line-height: 260px; */
    border-style: solid;
    border-width: 1px;
    /* border-left-style:dotted; */
  }
  .el-row {
    margin-bottom: 20px;
  }

  .el-row:last-child {
    margin-bottom: 0;
  }

  .el-col {
    border-radius: 4px;
  }

  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }

  
  </style>
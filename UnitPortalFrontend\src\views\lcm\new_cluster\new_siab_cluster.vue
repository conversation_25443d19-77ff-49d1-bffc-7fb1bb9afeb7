<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row>
        <el-col :span="3" :offset="19">
          <el-button size="large" type="danger" style="float: right" @click="clean_up">
            Clean up
          </el-button>
        </el-col>
        <el-col :span="2">
          <el-button size="large" type="success" style="float: right" @click="clean_modal();new_cluster_dialog_form_visible = true">
            Create
          </el-button>
        </el-col>

      </el-row>
    </div>
  
    <el-table  v-loading="listLoading" 
    element-loading-text="Loading the task details..."
    :data="current_list" border fit highlight-current-row style="width: 100%;"
    ref="task_table"
    @current-change="change_current_selected_row"
    >
      <el-table-column label="ID" prop="id" sortable="custom" align="center" min-width="2%" >
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="PE (click to see details)" min-width="10%" align="center" sortable="custom" prop="pe">
        <template slot-scope="{row}">
          <span class="bigger_font" style="cursor: pointer;text-decoration: underline;color:green" @click="show_task_detail(row.id)">{{ row.pe_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="PC" class-name="status-col" min-width="8%" align="center" sortable="custom" prop="pc">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.pc_fqdn }}</span>
        </template>
      </el-table-column>

      <el-table-column label="ahv subnet" min-width="6%" align="center" sortable="custom" prop="ahv_subnet">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.ahv_subnet}}</span>
        </template>
      </el-table-column>

      <el-table-column label="Benchmark" min-width="6%" align="center" sortable="custom" prop="benchmark_id">
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.benchmark_id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Stage" class-name="status-col" min-width="5%" align="center" sortable="custom" prop="stage" >
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.stage }}</span>
        </template>
      </el-table-column>


      <el-table-column label="Status" class-name="status-col" min-width="5%" align="center" sortable="custom" prop="status" >
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusFilter">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="Creater" class-name="status-col" min-width="5%" align="center" sortable="custom" prop="creater" >
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.creater }}</span>
        </template>
      </el-table-column>

      <el-table-column label="Create date" class-name="status-col" min-width="5%" align="center" sortable="custom" prop="create_date" >
        <template slot-scope="{row}">
          <span class="bigger_font">{{ row.create_date }}</span>
        </template>
      </el-table-column>

    </el-table>

    <el-dialog 
        id="new_cluster_modal"
        width="56%"
        :visible.sync="new_cluster_dialog_form_visible" 
        :close-on-click-modal="false"
        style="height:100%;margin-top:-10vh">

        <div style="width:100%;text-align: center;font-size:30px;">Create New Cluster</div>
        <div style="border-top: 0.1px solid rgb(185, 182, 182);width:95%;height:0;margin-top:1%;margin-left: 2%;"></div>
        <div style="height: 70vh;min-height: 550px;margin-top:0.3vh;">
          <el-steps :active="active_step" align-center style="height:12%;min-height: 90px;">
            <el-step 
              v-for  = "stage in stages"
              :title = "stage.title"  
              :class = "[stage.class, {'highlight':stage.stage==showing_stage}]" 
              :description="stage.description"
              :status="stage.status"
              @click.native="change_view(stage.stage)"
              > 
              <div class=""  slot="icon" style=" border-radius: 50%;width:100%;height:100%;padding:5px;">
                <div class="stage_ongoing" style="position:absolute;top:0;bottom: 0;left: 0;right:0"  v-show="active_step==stage.stage"> </div>
                <svg-icon :class-name="stage.icon" :icon-class="stage.icon" />
              </div>
            
            </el-step>
          </el-steps>
          <div style="height:90%;">
            <div style="height:7%;width:90%;margin-top:1%;margin-left:3%;">
              <el-tag type="primary">
                {{ stages[showing_stage]['details_discription'] }}
              </el-tag>
            </div> 
            <!-- stage1 user input-->
            <div class="modal_div" v-show="showing_stage==0">
              <div class="stage_body_fixed"> 
                <el-form
                    ref="stage1_form"
                    :rules="stage1_rules"
                    :model="stage1_input"
                    label-position="left"
                  >
                  <el-form-item label="PE name" prop="pe" style="margin-top:2%">
                    <el-input  v-model="stage1_input.pe" :disabled="active_step!=0"/>
                  </el-form-item>

                  <el-form-item label="PC" prop="pc" style="margin-top:30px">
                    <el-select size="large"
                        v-model="stage1_input.pc"  collapse-tags placeholder="select the PC" style="width:100%;" :disabled="active_step!=0" >
                      <el-option v-for="item in pc_list" :key="item.fqdn" :label="item.fqdn" :value="item.fqdn" style="font-size: large;"/>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="AHV vlan gateway IP" prop="ahv_vlan_ip" style="margin-bottom: 0px;margin-top:30px">
                    <el-input  v-model="stage1_input.ahv_vlan_ip" :disabled="active_step!=0"/>
                  </el-form-item>
                  <el-alert type="success"  :closable="true" >
                    Enter a random IP from the AHV vlan
                  </el-alert>

                  <el-form-item label="Benchmark" prop="benchmark" label-width="100%" label-position="left" label-prop="benchmark" style="margin-bottom: 0px;margin-top:30px">
                      <el-select size="large" :disabled="active_step!=0"
                          v-model="stage1_input.benchmark"  collapse-tags placeholder="select the benchmark" style="width:100%;float:left" filterable>
                        <el-option 
                            v-for="item in benchmark_list" 
                            :key="item.id" 
                            :value="item.id"
                            :label="`${item.name}---oob_vlan:${item.oob_vlan}---ahv_cvm_vlan:${item.ahv_cvm_vlan}`" 
                            style="font-size: large;"
                        />
                      </el-select>
                      <!-- <el-button size="large" style="width:28%;float:right" :disabled="active_step!=0">
                        Check benchmark list
                      </el-button> -->
                      <div style="clear:both"></div>
                  </el-form-item>
                  <el-alert type="success"  :closable="true" style="margin-top:0px;">
                    select a benchmark for the cluster, it can be changed later, just make sure that the ahv/cvm and oob vlan are correct.
                  </el-alert>
                </el-form>
              </div>
              <div class="stage_footer">
                <el-button type="success" @click="launch_task()" style="float:right;margin-left:1%">
                    Launch
                  </el-button>

                  <el-button type="primary" @click="new_cluster_dialog_form_visible=false" style="float:right">
                    Close
                  </el-button>

                  <div style="clear:both"></div>
              </div>
            </div>
            <!-- stage2 detecting nodes-->
            <div class="modal_div" v-show="showing_stage==1" >
              <div class="stage_body" > 
                <el-tabs v-model="stage2_input.active_tab" style="height:88%;" @tab-click="handle_tab_click" >
                  <el-tab-pane label="logs" name="logs" style="height:100%; border:1px solid black">
                    <template #label>
                      <span class="custom-tabs-label">
                        <span>Logs</span>
                      </span>
                    </template>
                    <div id="log_box" style="width:100%;height:100%;overflow-y: auto;">
                        <el-text v-for ="log in detail_log_list"  :class="[log_class[log.severity]]">{{log.message}}<br></el-text>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane label="Detected oob" name="second" style="height:100%; ">
                    <template #label>
                      <el-badge :value="stage2_input.detected_oob.length" :type="stage2_input.detected_oob.length?'primary':'info'">
                        <span class="custom-tabs-label">
                          <span>Detected OOB</span>
                        </span>
                      </el-badge> 

                    </template>
            
                    <div  style="width:100%;height:100%;">
                        <el-table
                          ref="stage2_oob_table"
                          element-loading-text="Loading the task details..."
                          border fit highlight-current-row style="width: 100%;height:100%;overflow-y: auto;" 
                          :data="stage2_input.detected_oob"
                          >
                          <el-table-column type="selection" width="35" :selectable="()=>current_task.stage<3"/>
                          <el-table-column label="IP Address" sortable="custom" align="center" min-width="2%" >
                            <template slot-scope="{row}">
                              <span>{{ row.ip }}</span>
                            </template>
                          </el-table-column>
                        
                          <el-table-column label="SN" sortable="custom" align="center" min-width="2%" >
                            <template slot-scope="{row}">
                              <span>{{ row.sn }}</span>
                            </template>
                          </el-table-column>
                        
                        </el-table>
                    </div >
                  </el-tab-pane>
                </el-tabs>

                <div style="clear:both"></div>
              </div>
              <div class="stage_footer">

                <el-button type="success" @click="fire" style="float:right;margin-left:1%">
                  Next
                </el-button>

                <el-button type="primary" @click="new_cluster_dialog_form_visible=false" style="float:right">
                  Close
                </el-button>

                <div style="clear:both"></div>
              </div>
            </div>
            <!-- stage3 pre-check-->




            <div class="modal_div"  v-show="showing_stage>1">
              <div class="stage_body">
                <div class="stage_left_log"> 
                  <span style="font-weight: bold;font-size: 26px;">
                  LOG
                  </span>
                  <div id="log_box_step_4" style="width:100%;height:90%;box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);">
                    <div style="overflow-y: auto;max-height:100%">
                      <el-text v-for ="log in detail_log_list"  :class="[log_class[log.severity]]">{{log.message}}<br></el-text>
                    </div>
                  </div>
                </div>
                <div v-for="stage in stage3_to_9_details" class="stage_right_step" v-show="stage.stage-1==showing_stage">
                  <span style="font-weight: bold;font-size: 26px;">
                  STEPS
                  </span>
                  <div v-for="step in stage.steps" class="stage_right_step_inner">
                    <!--- v-for="step in stage3_steps" -->
                    <div style="margin-top:2%"> 
                      <svg-icon v-if="step_check_icon_showing(stage, step)"   icon-class="step_check" style="float:left;width:2em;height:2em;"/>
                      <svg-icon v-if="step_ongoing_icon_showing(stage, step)" class="step_ongoing" icon-class="step_process" style="float:left;width:2em;height:2em;"/>
                      <svg-icon v-if="step_waiting_icon_showing(stage, step)" icon-class="step_waiting" style="float:left;width:2em;height:2em;"/>
                      <svg-icon v-if="step_error_icon_showing(stage, step)"   icon-class="step_error" style="float:left;width:2em;height:2em;"/>
                      <span style="float:left;margin-left:2%;margin-top:3px;height:20px;font-size:100%;">{{ step.description }}</span>
                      <div style="clear:both"></div>
                    </div>

                  </div>
                </div>
                <div style="clear:both"> </div>
              </div>
              <div class="stage_footer">
                <el-button type="primary" @click="new_cluster_dialog_form_visible=false" style="float:right;margin-left:1%">
                  Close
                </el-button>                
                <el-button 
                  v-show="current_task.stage-1==showing_stage" 
                  type="danger" 
                  style="float:right; margin-left: 10px;" 
                  :disabled="current_task.status !== 'In Progress'"
                  @click="abortTask">
                  Abort
                </el-button>
                <el-button 
                  v-show="current_task.stage-1==showing_stage" 
                  type="warning" 
                  style="float:right" 
                  :disabled="current_task.status!='Error' && current_task.status!='Aborted'" 
                  @click="resume">
                  Retry 
                </el-button>                
                <div style="clear:both"></div>
              </div>
            </div>
          </div>

        </div>

    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves' // waves directive
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import {GetPrismList, GetBenchmarkList } from  '@/api/nutanix'
import {CreateNewRetailCluster, GetNewClusterTaskList, GetNewClusterTaskDetail, GetNewClusterLog, AbortClusterSubTask } from  '@/api/new_cluster'
export default {
  name: 'NewCluster',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
        let st = status.toLowerCase();
        const statusMap = {
          'not started': 'info',
          'done': 'success',
          'error': 'danger',
          'in progress':'primary'
        }
        return statusMap[st]
      },
  },
  data() {

    return {
      interval_log_fetcher:null,
      // varaible for single task log fetching
      selected_row:null,
      pc_list:[],
      benchmark_list:[],
      detail_log_list:[],
      log_class:{
        "ERROR" : "error_log",
        "INFO" : "info_log",
        "WARNING" : "warning_log"
      },
      current_list:[],
      stage1_input:{
        // pe:'DSSE996-NXC000',
        // pc:'ssp-apac-ntx.ikea.com',
        // ahv_vlan_ip:'***********',
        // benchmark:"Store-AP---oob_vlan:158---ahv_cvm_vlan:157",
        pe:"",
        pc:"",
        ahv_vlan_ip:"",
        benchmark:"",
      },
      stage2_input:{
        active_tab:"logs",
        detected_oob:[],
      },
      stage3_to_9_details:[
        {
          stage:3,
          steps:[
            {id:1, description:"Check if we have enough free IPs"},
            {id:2, description:"Test firewall endpoints"},
            {id:3, description:"Check if the target IPs are in use"},
            {id:4, description:"Check node interface config"},
            {id:5, description:"Check if there are any error tasks on the PC"},
            {id:6, description:"Darksite test"},
            {id:7, description:"Check foundation central api_key"},
            {id:8, description:"Check if the cluster exists"},
            {id:9, description:"Check dns and ntp"}
          ]
        },
        {
          stage:4,
          steps:[
            {id:1, description:"Create Vault labels"},
          ]
        },
        { 
          stage:5,
          steps:[
            {id:1, description:"Upgrade Foundation version on CVMs"},
          ]
        },
        {
          stage:6,
          steps:[
            {id:1, description:"Wait nodes"},
            {id:2, description:"Prepare the payload"},
            {id:3, description:"Reserve static ips on ipam"},
            {id:4, description:"Deploy the cluster"},
            {id:5, description:"Check cluster creation status"},
          ]
        },
        {
          stage:7,
          steps:[
            {id:1, description:"Reset ntx admin password"},
            {id:2, description:"Create service account"},
            {id:3, description:"Auth config"},
            {id:4, description:"Configure EULA"},
            {id:5, description:"Register cluster to PC"},
            {id:6, description:"Update PE data service IP"},
            {id:7, description:"Configure LCM darksite"},
            {id:8, description:"Configure storage container"},
          ]
        },
        {
          stage:8,
          steps:[
            {id:1, description:"Rotate password"},
          ]
        },
        {
          stage:9,
          steps:[
            {id:1, description:"Configure OOB IP Address"},
            {id:2, description:"Renew Certificates"},
            {id:3, description:"Add Host To Oneview"},
          ]
        }
      ],
      stages:[
        { title:"Stage 1", icon:"info_no_border",class:"stage_icon"    , description:"Basic info", stage:0,
          details_discription:"Stage1: User needs to enter the basic information of the cluster. ", status:""
        },
        { title:"Stage 2", icon:"skill", class:"stage_icon" , description:"Scan nodes", stage:1,
          details_discription:"Stage2: Scaning the oob vlan which we get from the benchmark, and try to discover all the nodes in this vlan, will show in the table below.", status:""
        },
        { title:"Stage 3", icon:"pre-check", class:"stage_icon"   , description:"Pre-check", stage:2,
          details_discription:"Creating new vault items if they didn't exist.", status:""
        },
        { title:"Stage 4", icon:"vault", class:"stage_icon"   , description:"Prepare vault", stage:3,
          details_discription:"Initializing vault.", status:""
        },
        { title:"Stage 5", icon:"foundation", class:"stage_icon"   , description:"Foundation", stage:4,
          details_discription:"Upgrade foundation version on CVMs.", status:""
        },
        { title:"Stage 6", icon:"deployment-unit", class:"stage_icon"   , description:"Deploy", stage:5,
          details_discription:"Deploy the cluster", status:""
        },
        { title:"Stage 7", icon:"configure", class:"stage_icon"   , description:"Configure PE", stage:6,
          details_discription:"Configure PE", status:""
        },
        { title:"Stage 8", icon:"vault", class:"stage_icon"   , description:"Update vault", stage:7,
          details_discription:"Rotate password", status:""
        },
        { title:"Stage 9", icon:"icon_node", class:"stage_icon"   , description:"Configure OOB", stage:8,
          details_discription:"Configuring OOB.", status:""
        }
      ],
      new_cluster_dialog_form_visible : false,
      new_cluster_task_log_visible: false,
      active_step: 0 ,
      showing_stage:0 ,
      current_task:0,
      listLoading:false,
      stage1_rules:{
        pe:[{required:true, message: "please input the PE name.", trigger:'blur'}],
        pc:[{required:true, message: "please select a PC.", trigger:'blur'}],
        ahv_vlan_ip:[{required:true, message: "please input an ip from ahv vlan", trigger:'blur'}],
        benchmark:[{required:true, message:"please select a benchmark."}]
      }
    }
  },
  computed: {

  },
  created() {
    this.get_pc_list(),
    this.get_benchmark_list(),
    this.get_task_list()
  },
  methods: {
    fire(){
      console.log("firing to stage3 now, no chance to regret.")
      if(this.$refs.stage2_oob_table.selection.length < 3){
        this.$notify({
                title: 'Error',
                message: "Sorry, you have to select at lease 3 nodes to start.",
                type: 'error',
                duration: 5000
              }) 
      }
      let payload ={
        "task_id"     : this.current_task.id,
        "resume"      : false,
        "pc_fqdn"     : this.current_task.pc_fqdn,
        "pe_name"     : this.current_task.pe_name,
        "stage"       : 3,
        "ahv_subnet"  : this.current_task.ahv_subnet,
        "selected_oob": this.$refs.stage2_oob_table.selection.map(row => row.ip),
        "benchmark_id": this.current_task.benchmark_id
      }
      CreateNewRetailCluster(payload).then(
        response => {
          this.$notify({
                title: 'Success',
                message: 'Task has been started.',
                type: 'success',
                duration: 5000
              }) 
          this.get_task_list()
          this.start_log_refresher(response.data.task_id)
        }
      ).catch(
        err=>{
          this.$notify({
                title: 'Error',
                message: 'error:'+err,
                type: 'error',
                duration: 5000
              }) 
        }
      )
    },
    resume(){
      let stage = this.showing_stage + 1
      let payload ={
        "task_id"     : this.current_task.id,
        "resume"      : true,
        "pc_fqdn"     : this.current_task.pc_fqdn,
        "pe_name"     : this.current_task.pe_name,
        "stage"       : stage,
        "ahv_subnet"  : this.current_task.ahv_subnet,
        "selected_oob": this.current_task.selected_oob,
        "benchmark_id": this.current_task.benchmark_id
      }
      CreateNewRetailCluster(payload).then(
        response => {
          this.$notify({
                title: 'Success',
                message: 'Task has been started.',
                type: 'success',
                duration: 5000
              }) 
          this.get_task_list()
          this.start_log_refresher(response.data.task_id)
        }
      ).catch(
        err=>{
          this.$notify({
                title: 'Error',
                message: 'error:'+err,
                type: 'error',
                duration: 5000
              }) 
        }
      )
    },
    clean_up(){
      if(!this.selected_row){
        this.$notify({
                title: 'Error',
                message: 'error: at least select one record.',
                type: 'error',
                duration: 5000
              }) 
        return
      }
      let payload ={
        "task_id"     : this.selected_row.id,
        "stage"       : 99,
        "resume"      : false,
        "pc_fqdn"     : this.selected_row.pc_fqdn,
        "pe_name"     : this.selected_row.pe_name,
        "ahv_subnet"  : this.selected_row.ahv_subnet,
        "selected_oob": this.selected_row.selected_oob,
        "benchmark_id": this.selected_row.benchmark_id
      }
      CreateNewRetailCluster(payload).then(
        response => {
          console.log(response)
          this.$notify({
                title: 'Success',
                message: 'Task has been started.',
                type: 'success',
                duration: 5000
              }) 
          this.get_task_list()
          this.start_log_refresher(response.data.task_id)
        }
      ).catch(
        err=>{
          this.$notify({
                title: 'Error',
                message: 'error:'+err,
                type: 'error',
                duration: 5000
              }) 
        }
      )
    },
    launch_task(){
      let payload = {
          "pc_fqdn": this.stage1_input.pc,
          "pe_name": this.stage1_input.pe,
          "stage": 2,
          "ahv_subnet": this.stage1_input.ahv_vlan_ip,
          "benchmark_id": this.stage1_input.benchmark
      }
      this.active_step = 1
      // move to step 1
      this.showing_stage = this.showing_stage + 1
      CreateNewRetailCluster(payload).then(
        response => {
          this.$notify({
                title: 'Success',
                message: 'Task has been created.',
                type: 'success',
                duration: 5000
              }) 
          this.get_task_list()
          this.start_log_refresher(response.data.task_id)
        }
      ).catch(
        err=>{
          this.$notify({
                title: 'Error',
                message: 'error:'+err,
                type: 'error',
                duration: 5000
              }) 
        }
      )
    },
    change_view(stage){ 
      this.showing_stage = stage
      console.log(stage)
      if(stage>0){
        // meaning there are logs needed to be load
        this.$nextTick(()=>{
          this.scroll_to_bottom("log_box")
        })
      }
    },
    clean_modal(){
      if(this.$refs.stage1_form){// if stage1_form has been rendered, clean it. otherwise, do nothing

        this.active_step = 0
        this.showing_stage = 0
        this.current_task = 0
        //set current_task to 0, to reset all the step icon to "waiting"
        this.stage1_input.pe = ''
        this.stage1_input.pc = ''
        this.stage1_input.ahv_vlan_ip = ''
        this.stage1_input.benchmark = ''
        this.$refs.stage1_form.resetFields()
        this.reset_log_box()
        this.stage2_input.detected_oob = []
        this.stages.forEach(stage=>{
          stage.status = ""
        })
      }
    },
    get_pc_list(){
      GetPrismList().then(response=>{
        this.pc_list = response.data
      })
    },
    get_benchmark_list(){
      GetBenchmarkList().then(
        response=>{
          this.benchmark_list = response.data
        }
      )
    },
    get_task_list(){
      /*
      get the task list of cluster creation
      */
      GetNewClusterTaskList("retail").then(response=>{
        this.current_list = response.data
      })
    },
    show_task_detail(id){
      this.listLoading = true
      this.reset_log_box()
      this.get_task_detail(id)
      this.new_cluster_dialog_form_visible = true
      this.listLoading = false
    },
    get_task_detail(id){
      GetNewClusterTaskDetail('retail', id).then(
        (response)=>{
          /* get the details of a task
// {
//     "id": 19,
//     "pe_name": "RETSEHBG-NXC001",
//     "pc_fqdn": "ssp-eu-ntx.ikea.com",
//     "ahv_subnet": "*************",
//     "benchmark_id": 11,
//     "creater": "q",
//     "create_date": "2024-09-30,02:17:32",
//     "stage": 2,
//     "stage_status": "Error",
//     "status": "Error",
//     "detail_log_path": "/Users/<USER>/unitportal/UnitPortalBackend/Log/cluster/CREATE_CLUSTER_RETSEHBG-NXC001_q_2024-09-30-02-17-44",
//     "scan_oob": null,
//     "selected_oob": null,
//     "cluster_uuid": null,
//     "pid": 99908,
//     "ahv_cvm_subnet_id": "61270",
//     "oob_subnet_id": null,
//     "pe_ip": null,
//     "data_service_ip": null,
//     "ahv_cvm_oob_mapping": null
// }
*/
          console.log(response)
          this.current_task = response.data
          this.active_step = this.current_task.stage - 1
          this.showing_stage = this.current_task.stage - 1
          this.stage1_input.pe = this.current_task.pe_name
          this.stage1_input.pc = this.current_task.pc_fqdn
          this.stage1_input.ahv_vlan_ip = this.current_task.ahv_subnet
          this.stage1_input.benchmark = this.current_task.benchmark_id
          this.stage2_input.detected_oob = this.current_task.scan_oob?this.current_task.scan_oob:[]
          if(this.current_task.stage>1){
            console.log('fetching log')
            this.fetch_log(this.current_task.id)
          }
          this.stage3_to_9_details.forEach(stage=>{
              if(stage.stage == 5){
                if(this.stage2_input.detected_oob){
                  stage.steps=[]
                  for(let i=0;i<this.stage2_input.detected_oob.length;i++){
                    stage.steps.push({id:i, description:"Upgrade Foundation version "+this.stage2_input.detected_oob[i].ip})
                  }
                }
              }
            })

          this.stages.forEach(stage => {
            let _stage = stage.stage + 1
            //stage.stage is 0 based, but the el-steps is 1 based
            if(_stage < this.current_task.stage) {
              stage.status = "success"
            }
            else if(_stage == this.current_task.stage) {
              if(this.current_task.status.match(/.*(Error|Aborted).*/i)) {
                stage.status = "error"
              }
              if(this.current_task.status.match(/.*(done).*/i)) {
                stage.status = "success"
              }
              if(this.current_task.status.match(/.*(in progress).*/i)) {
                stage.status = "process"
              }
            }
            else {
              stage.status = "wait"
            }
          })
          this.$nextTick(()=>{
              //select all oob
                this.stage2_input.detected_oob.forEach(oob=>{
                  this.$refs.stage2_oob_table.toggleRowSelection(oob,true)
                })
              })

          if(!this.current_task.status.match(/.*(in progress|Not Started).*/i)){
            // is task is not in progress anymore, stop the interval 
            console.log('stop the interval')
            clearInterval(this.interval_log_fetcher)
          }
        }
      ).catch(
        (err)=>{
          this.$notify({
                title: 'Error',
                message: 'error:'+err,
                type: 'error',
                duration: 5000
              }) 
        }
      )
    },
    start_log_refresher(id){
      this.get_task_detail(id)
      this.interval_log_fetcher = setInterval(
        this.get_task_detail,
        10000,
        id
      )
    },
    fetch_log(id){
      console.log(this.showing_stage)
      GetNewClusterLog('retail', id).then(
        response=>{
          this.detail_log_list = response.data
          this.$nextTick(()=>{
            this.scroll_to_bottom("log_box")
        })
        }
      ).catch(
        error=>{
          console.log(error)
        }
      )
    },
    scroll_to_bottom(element_id){
      let _d = document.getElementById(element_id)
      _d.scrollTop = _d.scrollHeight
    },
    reset_log_box(){
      this.detail_log_list=[{
        "severity":"INFO",
        "message":"Log page"
      }]
    },
    handle_tab_click(tab, event){
    },
    change_current_selected_row(n, o){
      this.selected_row = n
    },
    step_check_icon_showing(stage, step){
      if(stage.stage==5){
        return (this.current_task.stage>5)
      }// in stage5 , all steps are concurrently running and it's not easy to check which CVM was done
       // so we just check if the stage is done
      if(stage.stage==9){
        return (this.current_task.step>step.id)||(this.current_task.status=="Done")
      }
      return (this.current_task.stage>stage.stage)||
              ( (this.current_task.stage==stage.stage) && (this.current_task.step>step.id) )
    },
    step_ongoing_icon_showing(stage, step) {
      if(stage.stage==5) {
        return (this.current_task.stage==5) && (!this.current_task.status.match(/.*(Error|Done|Aborted).*/i))
      }
      return this.current_task.stage==stage.stage && 
             this.current_task.step==step.id && 
             !this.current_task.status.match(/.*(Error|Done|Aborted).*/i)
    },
    step_waiting_icon_showing(stage, step){
      return !this.current_task||
              this.current_task.stage<stage.stage||
              ((this.current_task.stage==stage.stage)&&(this.current_task.step<step.id))
    },
    step_error_icon_showing(stage, step) {
      if(stage.stage==5) {
        return (this.current_task.stage==5) && (this.current_task.status.match(/.*(Error|Aborted).*/i))
      }
      return this.current_task.stage==stage.stage && 
              this.current_task.step==step.id && 
              this.current_task.status.match(/.*(Error|Aborted).*/i)
    },
    remove_interval(){
      clearInterval(this.interval_log_fetcher)
    },
    async abortTask() {
      try {
        await AbortClusterSubTask(this.facility_type, this.current_task.id)
        this.$notify({
          title: 'Success',
          message: 'Task abort initiated successfully',
          type: 'success',
          duration: 2000
        })
      } catch (error) {
        this.$notify({
          title: 'Error',
          message: error.response.data.message || 'Failed to abort task',
          type: 'error',
          duration: 2000
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
    .bigger_font {
      font-size: 13px;
    }
</style>  
<style lang="scss" >

#new_cluster_modal{
  .el-dialog{
    width:70%
  }
  .el-dialog__header{
    padding:0px;
  }
  .el-dialog__body {
    padding: 10px 10px;
    width: 900;
    min-height:800px;
  }
}
  .stage_icon{
  cursor: pointer;
  color: black;
  &:hover {
      background-image: linear-gradient(to right, rgba(255,0,0,0), rgb(184, 240, 255),rgba(255,0,0,0));
      //color of stage backgrounds
    }
    .is-icon{
      background-color: transparent; //all icons
    }
    .el-step__main{
      .el-step__title.is-wait{
        color:#a0b8ca// color of title 
      }
      .el-step__title.is-process{
        color:#2499f2// color of title 
      }      
      .el-step__description.is-process{
        color:#2499f2// color of title 
      }
      .el-step__title.is-success{
        color:#08b856;// color of title ,
      }
      .el-step__description.is-success{
        color:#08b856// color of title 
      }
      .el-step__title{
        font-size: 21px;
      }
      .el-step__description{
        font-size: 12px;
        padding-left: 10%;
        padding-right: 10%;
      }
    }

    .el-step__head{
      
      .el-step__line{
        background-color: #9de7e0;//color of connecting lines
        margin-top:10px;
        left: 55%;
        right: -44%;
      }

    } 

    .el-step__head.is-process {
      border-color: #2499f2;
      color:#2499f2;
    }

    .el-step__head.is-wait {
      border-color: #a0b8ca;
      color:#a0b8ca;
    }

    .el-step__icon{
      width:40px;
      height:40px;

      .svg-icon{
        width: 1.8em;
        height: 1.8em;
      }
    }
  }
  .el-form-item__content{
    margin-left:0px !important;
  }

  .el-form-item--medium .el-form-item__label {// label height
    line-height: 20px;
  }

  .modal_div{
    height:93%;
    width:90%;
    margin-top:1%;
    margin-left:3%;
  }
  .modal_div_stage1{
    height:60%;
    width:60%;
    margin-top:1%;
    margin-left:20%;

  }
  .el-alert {
    padding: 0px;
    margin-top:0px;

  }
  .el-alert .el-alert__description {
    font-size: 12px;
    margin: 1px 0 0 0;
    padding: 0;
  }
  .el-alert__closebtn {
    top: 5px;
  }
  .el-form-item__error {
    position: relative;
  }

 .stage_ongoing {
      // border: 1px solid #f3f3f3;
      border-top: 7px solid #1e64d4;
      border-radius: 50%;
      display: inline-block;
      animation: spin 3s linear infinite;
  }
  .step_ongoing {
      // border: 1px solid #f3f3f3;
      display: inline-block;
      animation: spin 3s linear infinite;
  }
  .stage_body{
    height:85%;
    max-height: 85%;
  }
  .stage_body_fixed{
    min-height:85%;
  }
  .stage_footer{
    height:10%;
  }
  .stage_left_log{
    height:100%;
    max-width: 60%;
    width:60%;
    float:left
  }
  .stage_right_step{
    height:100%;
    width:40%;
    max-width: 40%;
    float:right;
    padding-left: 3%;
  }
  .stage_right_step_inner{
    // min-height:7%;
    width:100%;
  }
  .highlight{
    background-image: linear-gradient(to right, rgba(255,0,0,0), rgb(145, 245, 160),rgba(255,0,0,0));
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

.el-table__body tr.current-row > td.el-table__cell{
  background-color: #77b1e7;
}
.el-card__header{
  padding: 5px 5px;
}
.el-card__body{
  padding:0
}
.error_log{
  color:red
}
.warning_log{
  color: rgb(142, 142, 12)
}
.info_log{
  color: black
}
.el-badge__content.is-fixed {
  top:7px;
  right:4px
}

.el-tabs__content{
      height:100%
    }

    .el-table::before{
      height:0px
    }
</style>


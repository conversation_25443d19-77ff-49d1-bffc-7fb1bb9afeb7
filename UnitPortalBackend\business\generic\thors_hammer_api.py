from time import sleep

import requests
import static.SETTINGS
from business.authentication.authentication import ServiceAccount
from business.distributedhosting.nutanix.workload.workload_exceptions import ThorsDeleteComputerNotFound, \
    ThorsRegisterMachineFailed, ThorsAddProfileFailed, ThorsUpdateActivationCodeFailed, \
    ThorsExpControlAgentStatusFailed, ThorsHammerException
from business.generic.commonfunc import CommonRestCall


class ThorsHammerAPI:
    def __init__(self, sa=None, logger=None) -> None:
        self.endpoint = static.SETTINGS.THORS_HAMMER["ENDPOINT"]
        _sa = sa if sa else ServiceAccount(usage=ServiceAccount.THORS_HAMMER).get_service_account()
        self.logger = logger
        self.rest = CommonRestCall(username=_sa['username'], password=_sa['password'], logger=self.logger)

    def is_computer_existing(self, name):
        res = self.rest.call_restapi(f"{self.endpoint}/robo/get-computer/prefix={name}&client=yes")
        return res.json()["data"]

    def register_machine(self, machine_name):
        self.logger.info(f"Start to register machine {machine_name} in Thor's Hammer...")
        # Note: the machine will be added with the type "Contacts"
        res = self.rest.call_restapi(f"{self.endpoint}/robo/new-computer/fixed={machine_name}")
        if not res.json()["Success"]:
            raise ThorsRegisterMachineFailed(machine_name, res.json()['Error'])
        self.logger.info(f"Register machine {machine_name} in Thor's Hammer succeeded!")

    def post_create(self, machine_name, machine_ip):
        self.get_deployment_status(machine_name)
        self.set_ip_address(machine_name, machine_ip)
        self.init_disks(machine_name)
        self.extend_disks(machine_name)
        for _ in range(1, 11):
            res = self.control_agent_status(machine_name)
            idem_state = res.json()["data"]
            self.logger.info(f"Agent status: {idem_state}")
            if idem_state == "LISTENING":
                self.logger.info("IDEM is idle , will continue installations flow")
                return
            self.logger.info("Sleeping '60' extra seconds")
            sleep(60)
        raise ThorsHammerException(res.json()['Error'], f"Query status of machine {machine_name} in Thor's Hammer failed!")

    def update_activation_code(self, machine_name, vm_uuid):
        self.logger.info(f"Start to update activation code, machine name: {machine_name}, VM uuid: {vm_uuid}")
        res = self.rest.call_restapi(
            f"{self.endpoint}/robo/update-computer/computername={machine_name}&activationCode={vm_uuid}"
        )
        if not res.json()["Success"]:
            raise ThorsUpdateActivationCodeFailed(machine_name, vm_uuid, res.json()['Error'])
        self.logger.info("Succeeded to update activation code.")

    def add_host_profile(self, machine_name, profile_name):
        # Assign an existing profile to a machine.
        self.logger.info(f"Start to add host profile {profile_name}, machine name: {machine_name}")
        res = self.rest.call_restapi(
            f"{self.endpoint}/robo/add-profile/Profilename={profile_name}&computerName={machine_name}"
        )
        if not res.json()["Success"]:
            raise ThorsAddProfileFailed(machine_name, profile_name, res.json()['Error'])
        self.logger.info("Succeeded to add host profile.")

    def get_deployment_status(self, machine_name, profilec=None, subscription=None, retries=120, retry_interval=120):
        for i in range(1, retries + 1):
            res = self.test_deployment_finished(machine_name, profilec, subscription)
            if res.json()["Success"]:
                self.logger.info("Deployment status is success.")
                return
            if 'not found in assigned software' in res.text or 'Not everything installed yet' in res.text:
                self.logger.info(f"Waiting job status to become success...Current error: {res.json()['Error']}")
                self.logger.info(f"Looping '{i}' out of '{retries}'")
                self.logger.info(f"Sleeping '{retry_interval}' extra seconds each loop")
                sleep(retry_interval)
            else:
                self.logger.error(f"Deployment status error: {res.json()['Error']}")
                break
        raise ThorsHammerException(res.json()["Error"], 'ThorsHammer has failed to complete its work. Is this machine reachable?')

    def test_deployment_finished(self, machine_name, profilec=None, subscription=None):
        self.logger.info(f"Start to get deployment status for {machine_name} in Thor's Hammer...")
        url = f"{self.endpoint}/robo/Test-DeploymentFinished/computerName={machine_name}"
        if profilec:
            url += f"&profilec={profilec}"
        if subscription:
            url += f"&subscription={subscription}"
        res = self.rest.call_restapi(url)
        return res

    def set_ip_address(self, machine_name, machine_ip):
        self.logger.info(f"Start to set ip address, machine_name: {machine_name}, ip: {machine_ip}")
        res = self.rest.call_restapi(
            f"{self.endpoint}/robo/New-Alias/computername={machine_name}&aliasname=IPAddress1&aliasValue={machine_ip}"
        )
        if not res.json()["Success"]:
            raise Exception(f'Failed to set ip address. Error: {res.json()["Error"]}')
        self.logger.info("Succeeded to set ip address.")
        return res

    def init_disks(self, machine_name):
        self.logger.info("Start to init disks in Thor's Hammer...")
        res = self.rest.call_restapi(
            f"{self.endpoint}/robo/New-Alias/computerName={machine_name}&aliasname=AutoInitializeDisks&aliasValue=True"
        )
        if not res.json()["Success"]:
            raise Exception(f'Failed to init disks. Error: {res.json()["Error"]}')
        self.logger.info("Succeeded to init disks.")
        return res

    def extend_disks(self, machine_name):
        self.logger.info("Start to extend disks in Thor's Hammer...")
        res = self.rest.call_restapi(
            f"{self.endpoint}/robo/New-Alias/computerName={machine_name}&aliasname=AutoExpandVolumes&aliasValue=True"
        )
        if not res.json()["Success"]:
            raise Exception(f'Failed to extend disks. Error: {res.json()["Error"]}')
        self.logger.info("Succeeded to extend disks.")
        return res

    def control_agent_status(self, machine_name, status=True):
        """
        Gets the agent its status or starts it.
        status: Boolean, true to query status or false to start the agent

        success response:
        {
            "KeyValue": {
                "ComputerName": "RETCN888-NT3000",
                "IPAddress": "***********"
            },
            "data": "LISTENING",
            "Success": true,
            "Error": null
        }
        failed response:
        {
            "KeyValue": {
                "ComputerName": "RETSE995-NT8888",
                "IPAddress": null
            },
            "data": "",
            "Success": false,
            "Error": "Error pinging computer [RETSE995-NT8888], Error finding VM or IP for VM"
        }
        """
        self.logger.info("Start to control agent status in Thor's Hammer...")
        url = f"{self.endpoint}/robo/start-agent/computerName={machine_name}"
        if status:
            url += "&status=true"
        for _ in range(1, 71):
            res = self.rest.call_restapi(url)
            if res.json()["Success"]:
                return res
            self.logger.info(f"Get Control agent status not ready... {res.json()}")
            self.logger.info("Sleep '60' seconds...")
            sleep(60)
        raise ThorsExpControlAgentStatusFailed(res.json()["Error"])

    def delete_computer(self, machine_name):
        self.logger.info(f"Start to delete machine {machine_name} in Thor's Hammer...")
        res = self.rest.call_restapi(f"{self.endpoint}/robo/remove-computer/computerName={machine_name}")
        if not res.json()["Success"] and "Error finding computer" not in res.json()["Error"]:
            raise ThorsDeleteComputerNotFound(machine_name)
        self.logger.info("Succeeded to delete machine!")
        return res

    def new_profile(
        self,
        prefix,
        profile_name,
        profile_description,
    ):
        self.logger.info(f"Start to new profile {profile_name}, prefix: {prefix}")
        res = self.rest.call_restapi(
            f"{self.endpoint}/robo/new-profile/profileName={profile_name}&prefix={prefix}&profileDescriptions={profile_description}"
        )
        if not res.json()["Success"]:
            raise Exception(f'Failed to new profile. Error: {res.json()["Error"]}')
        self.logger.info(f"Succeeded to new profile{profile_name}.")
        return res

    def make_profile(self, prefix, profiletype):
        # = create profile
        # prefix: site code, e.g. POSES718;
        # profiletype: machine type, e.g. FSOL
        self.logger.info(f"Start to make profile '{profiletype}' under '{prefix}'")
        res = self.rest.call_restapi(
            f"{self.endpoint}/robo/make-profile/Profiletype={profiletype}&prefix={prefix}"
        )
        if not res.json()["Success"]:
            raise Exception(f'Failed to make profile. Error: {res.json()["Error"]}')
        self.logger.info(f"Succeeded to make profile '{profiletype}' on site '{prefix}'.")
        return res

    def remove_profile(self, machine_name, profile_name):
        self.logger.info(f"Start to remove profile '{profile_name}' for '{machine_name}'")
        res = self.rest.call_restapi(
            f"{self.endpoint}/robo/remove-profile/Profilename={profile_name}&computerName={machine_name}"
        )
        if not res.json()["Success"]:
            raise Exception(f'Failed to remove profile. Error: {res.json()["Error"]}')
        self.logger.info(f"Succeeded to remove profile '{profile_name}' on site '{machine_name}'.")
        return res

    def add_host_subscription(self, machine_name, subscription):
        self.logger.info(f"Assigning Subscription: '{subscription}' towards Machine: '{machine_name}'")
        res = self.rest.call_restapi(
            f"{self.endpoint}/robo/Add-Subscription/computerName={machine_name}&subscriptionName={subscription}"
        )
        if not res.json()["Success"]:
            raise Exception(f'Failed to add host subscription. Error: {res.json()["Error"]}')
        self.logger.info("Succeeded to add host subscription.")
        return res

    def add_user_to_admin_computer_group(self, sam_account_name, machine_name):
        self.logger.info(f"Adding '{sam_account_name}' towards the admin users inside machine '{machine_name}'")
        res = self.rest.call_restapi(
            f"{self.endpoint}/robo/Add-Toadmingroup/computername={machine_name}&Username={sam_account_name}"
        )
        try:
            if not res.json()["Success"]:
                # raise Exception(f'Failed to add user to admin computer group. Error: {res.json()["Error"]}')  # TODO
                self.logger.warning("API return failed, but maybe because the user is already in admin group.")
            self.logger.info("Succeeded to add user to admin computer group.")
        except requests.exceptions.JSONDecodeError:
            self.logger.warning("Something wrong with the output, but ignore")
        return res

from models.database import db, ma


class ModelCountryCode(db.Model):
    __tablename__ = 'dh_country_code'
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    country_code = db.Column(db.String(50))
    country_name = db.Column(db.String(255))
    retail_pc = db.Column(db.String(255))
    retail_vc = db.Column(db.String(255))
    warehouse_pc = db.Column(db.String(255))
    warehouse_vc = db.Column(db.String(255))


class ModelCountryCodeSchema(ma.Schema):
    class Meta:
        fields = ('id', 'country_code', 'country_name', 'retail_pc', 'retail_vc', 'warehouse_pc', 'warehouse_vc')

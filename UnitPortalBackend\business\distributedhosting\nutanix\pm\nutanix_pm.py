# installed modules
import copy
import logging
import re
import time
import uuid
from datetime import datetime
from flask import Flask
# local files
import static.SETTINGS as SETTING
from business.distributedhosting.nutanix.nutanix import PrismElement, NutanixOperation
from business.generic.commonfunc import create_file, test_pinging, setup_common_logger
from business.loggings.loggings import DBLogging
from models.database import db
from models.pm_models import ModelNTXPMTask
from business.generic.commonfunc import DBConfig


def start_ntx_pm(task_id):      # noqa

    logging.info(f"Nutanix PM {task_id} started.")
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = DBConfig()()
    db.init_app(app)                # db is in models.py
    app.app_context().push()        # context definition
    ######## main logic started #########
    task = ModelNTXPMTask.query.filter_by(id=task_id).first()
    if re.match(SETTING.STATUS['INPROGRESS'], task.status, re.I):
        logging.info(f"PM task :{task_id} already started.")
        return
    task.status = "In Progress"
    cluster, creater = task.cluster, task.creater
    db.session.commit()     # change the task status to inprogress
    lg = DBLogging(logdir=SETTING.NTX_LOG_PATH, taskid=task_id, logtype="NTX_PM")   # create logging instance
    try:
        ################### create dedicated log file #######################
        #                                                                   #
        #                      Step 1 : create logs                         #
        #                                                                   #
        #####################################################################
        if local_log_path := create_file(filepath=SETTING.NTX_LOG_PATH,filename=f'{cluster}_{creater}_{datetime.utcnow().strftime("%Y-%m-%d-%H-%M-%S")}'):  # noqa
            logging.info(f"Local log file created, path: {local_log_path}.")

            lg.write_pm_log(loginfo=f'Local log file created, path: {local_log_path}.')

            task.detaillogpath = local_log_path
            db.session.commit()
        else:
            raise Exception('Failed to create local log file, PM will be ended now.')
        # for function/class using, logging into the specific file
        logger = setup_common_logger(str(uuid.uuid4()), local_log_path)

        if re.match(SETTING.PM['ACTION']['off'], task.pmtype, re.I):    # it's a power off task.

            lg.write_pm_log(loginfo=f'This is a power-off action for {cluster}, starting.')

            # if not re.search("888",cluster):# just for test
            #     lg.write_pm_log(loginfo=f'The cluster you are trying to shut down is not 888, not allowed so far :D..', logseverity='info', taskid=task_id)   # noqa
            #     task = ModelNTXPMTask.query.filter_by(id=task_id).first()
            #     task.status = "Done"
            #     db.session.commit()
            #     return

            ntx_pm = NutanixOperation(pc=task.prism, pe=task.cluster, logger=logger)    # type:ignore
            #####################################################################
            #                                                                   #
            #                  Step 2 : Check if cluster exists                 #
            #                                                                   #
            #####################################################################

            lg.write_pm_log(loginfo='Check if cluster exists.')

            res, mes = ntx_pm.if_cluster_exists()
            if not res:     # if cluster not exist, then abort the pm
                raise Exception(mes)
            if isinstance(mes, list):
                if len(mes) > 1:    # the code shouldn't go here, if it's here, then something is really wrong
                    raise Exception(f'we found more than 1 cluster named {task.cluster}, abort.')
                #####################################################################
                #                         get pe info                               #
                #                         cvm/oob/ahv                               #
                #                     ip/username/password                          #
                #####################################################################

            lg.write_pm_log(loginfo='Getting cluster (cvm/ahv/oob) list and credentials .')

            res, pe_info = ntx_pm.get_pe_info()
            if not res:     # if cluster not exist, then abort the pm
                raise Exception(pe_info)
            print(pe_info)

            #####################################################################
            #                                                                   #
            #                    Step 3 : Get the VM list                       #
            #                                                                   #
            #####################################################################
            ntx_pe = PrismElement(task.cluster, logger=logger)  # type:ignore
            res, data = ntx_pe.get_vm_list()
            lg.write_pm_log(loginfo=f'Getting VM list from {task.cluster}.')
            if not res:
                raise Exception('Failed to get the VM list.')
            # Shutdown
            #####################################################################
            #                                                                   #
            #            Step 4 : Try to gracefully shutdown the VM             #
            #                                                                   #
            #####################################################################
            _flag_wait_graceful_showdown = True     # flag to indicate if we have done any graceful shutdown
            if data:     # gracefully shutting down non-cvm VMs
                if poweredon_vm := [_vm for _vm in data if (_vm['state'] != 'off' and (not _vm['is_controller_vm']))]:
                    # if there is powered on vms. gracefully shutdown.
                    lg.write_pm_log(loginfo='gracefully shutting down vm in the list.')
                    for vm in poweredon_vm:
                        lg.write_pm_log(loginfo=f'Gracefully shutting down {vm["name"]}')   # type:ignore
                        ntx_pe.set_vm_power_state(vm_uuid=vm["uuid"], transition='acpi_shutdown', vm_name=vm["name"])
                else:
                    _flag_wait_graceful_showdown = False    # NO need to wait for VMs to be shutdown.
                    lg.write_pm_log(loginfo="All non-cvm VMs are powered off, let's continue.")
            else:
                raise Exception('Failed to get the VM list, PM will be aborted.')
            #####################################################################
            #                                                                   #
            # Step 5 (if needed) : Hard shutdown the VM that remain powered on. #
            #                                                                   #
            #####################################################################
            _flag_wait_hard_showdown = False  # flag to indicate if we've done any hard shutdown
            if _flag_wait_graceful_showdown:
                _flag_wait_hard_showdown = True
                lg.write_pm_log(loginfo="Take a 3-minutes nap after gracefully shutting down the VMs.")
                # sleep 3 minutes waiting for gracefully shutdown VMs
                time.sleep(SETTING.PM["GRACEFUL_SHUTDOWN_SLEEP_TIME"])
                # Test-curfu
                res, data = ntx_pe.get_vm_list()    # query the vm again.
                if not res:
                    raise Exception('Failed to get the VM list after gracefully shutting them down.....')
                if poweredon_vm := [_vm for _vm in data if (_vm['state'] != 'off' and (not _vm['is_controller_vm']))]:
                    lg.write_pm_log(loginfo="There are VMs still powered on, start to hard shutdown.")
                    for vm in poweredon_vm:
                        lg.write_pm_log(loginfo=f'Hard shutting down {vm["name"]}')  # type:ignore
                        ntx_pe.set_vm_power_state(vm_uuid=vm["uuid"], transition='off', vm_name=vm["name"])
                        # Test-curfu
                    lg.write_pm_log(loginfo="Take a 2-minutes nap after hard shutting down the VMs.")
                    time.sleep(SETTING.PM["HARD_SHUTDOWN_SLEEP_TIME"])  # sleep 2 minutes waiting for hard shutdown VMs
                    # Test-curfu
            if _flag_wait_hard_showdown:
                res, data = ntx_pe.get_vm_list()   # query the vm the 3rd time to make sure all vms are powered off.
                if not res:
                    raise Exception('Failed to get the VM list after hard shutting them down.....')
                if poweredon_vm := [_vm for _vm in data if (_vm['state'] != 'off' and (not _vm['is_controller_vm']))]:
                    # getting vm that is not off and not cvm
                    # pass
                    raise Exception('There is non-cvm VM powered on, weird, please contact GDH to have a look.')
                    # Test-curfu
                lg.write_pm_log(loginfo="All non-cvm VMs are powered off, let's move on.")
            #####################################################################
            #                                                                   #
            #                    Step 6 : Stop the cluster                      #
            #                                                                   #
            #####################################################################
            lg.write_pm_log(loginfo=f'Stopping the cluster: {task.cluster} now.')

            res, msg = ntx_pe.shutdown_cluster(cvm=pe_info[0]['cvm_ip'],
                                               username=pe_info[0]['cvm_nutanix']['username'],
                                               password=pe_info[0]['cvm_nutanix']['password'])
            # We pass the ip of the first CVM we got to the shutdown_cluster function.

            if not res:
                raise Exception(msg)
            lg.write_pm_log(loginfo='Take a 3-minutes nap after shutting down the cluster.')
            time.sleep(SETTING.PM["CLUSTER_SHUTDOWN_SLEEP_TIME"])

            ntx_pe.get_cluster_status(cvm=pe_info[0]['cvm_ip'],
                                      username=pe_info[0]['cvm_nutanix']['username'],
                                      password=pe_info[0]['cvm_nutanix']['password'])
            # We use this function to log the status of our cluster, maybe we need it in the future ?
            #####################################################################
            #                                                                   #
            #            Step 7 : Poweroff AHV node by ssh "shutdown -s"        #
            #                                                                   #
            #####################################################################
            lg.write_pm_log(loginfo='Powering off AHV now.')

            for ahv in pe_info:
                res = ntx_pe.shutdown_host_via_ssh(hostip=ahv['ip'],
                                                   username=ahv['ahv_root']['username'],
                                                   password=ahv['ahv_root']['password'],
                                                   hostname=ahv['name'])
                if res:
                    lg.write_pm_log(loginfo='Shutdown command has been sent to AHV.')
                else:
                    lg.write_pm_log(loginfo='Error happened when sending shutdown command to AHV, no worry, we will use RedFish API instead.', logseverity='warning')  # noqa

            lg.write_pm_log(loginfo="Take a 2-minutes nap after powering off AHVs.")
            time.sleep(SETTING.PM["AHV_SHUTDOWN_SLEEP_TIME"])
            #####################################################################
            #                                                                   #
            #        Step 8(if needed) : Poweroff AHV node by redfish api       #
            #                                                                   #
            #####################################################################
            lg.write_pm_log(loginfo='Checking AHV status via redfish api.')
            i, powered_on_host_list = 0, copy.deepcopy(pe_info)
            powered_on_host_list = [_h for _h in powered_on_host_list if _h['state'] != "Off"]
            while i < SETTING.PM['HOST_STATE_LOOP_TIMES'] and powered_on_host_list:     # skip when list is empty.
                print("#########powered on AHV:", [_h['name'] for _h in powered_on_host_list])
                print("start to loop the list")
                for ahv in powered_on_host_list:
                    print("AHV", ahv['name'])
                    lg.write_pm_log(loginfo=f"Checking {ahv['name']}")
                    res, state = ntx_pe.check_host_power_state_via_redfish(
                        oobip=ahv['oob_ip'],
                        username=ahv['oob_admin']['username'],
                        password=ahv['oob_admin']['password'])
                    if not res:
                        lg.write_pm_log(loginfo=f'Got error :{state}', logseverity='error')
                    else:
                        if state == "Off":
                            lg.write_pm_log(loginfo=f"AHV {ahv['name']} is off")
                            ahv['state'] = "Off"
                        if state == "On":
                            lg.write_pm_log(loginfo=f"AHV {ahv['name']} is on, shutting down via redfish now.")
                            ntx_pe.shutdown_host_via_redfish(
                                oobip=ahv['oob_ip'],
                                username=ahv['oob_admin']['username'],
                                password=ahv['oob_admin']['password'])
                i += 1
                # refresh powered_on_host_list
                powered_on_host_list = [_h for _h in powered_on_host_list if _h['state'] != "Off"]
                if powered_on_host_list:
                    lg.write_pm_log(loginfo="Take a 2-minutes nap and check again.")
                    time.sleep(SETTING.PM["HOST_STATE_LOOP_SLEEP_TIME"])

            task = ModelNTXPMTask.query.filter_by(id=task_id).first()
            if not [_h for _h in powered_on_host_list if _h['state'] != "Off"]:  # There is no ahv remaining powered on.
                task.status = "Done"
                db.session.commit()
            else:       # There is ahv remaining powered on.
                _h = ",".join([_h['name'] for _h in powered_on_host_list])
                lg.write_pm_log(loginfo=f"{_h} still not powered off, please manually check.", logseverity='error')
                task.status = "Error"
                db.session.commit()

        if re.match(SETTING.PM['ACTION']['on'], task.pmtype, re.I):
            lg.write_pm_log(loginfo=f'This is a power-on action for {cluster}, starting.')
            ntx_pm = NutanixOperation(pc=task.prism, pe=task.cluster, logger=logger)        # type:ignore
            #####################################################################
            #                                                                   #
            #                  Step 2 : Check if cluster exists                 #
            #                                                                   #
            #####################################################################

            lg.write_pm_log(loginfo='Check if cluster exists.')

            res, mes = ntx_pm.if_cluster_exists()
            if not res:     # if cluster not exist, then abort the pm
                raise Exception(mes)
            if isinstance(mes, list):
                if len(mes) > 1:        # the code shouldn't go here, if it's here, then something is really wrong
                    raise Exception(f'we found more than 1 cluster named {task.cluster}, abort.')
            lg.write_pm_log(loginfo='Cluster exists, proceed.')
            #####################################################################
            #                         get pe info                               #
            #                         cvm/oob/ahv                               #
            #                     ip/username/password                          #
            #####################################################################

            lg.write_pm_log(loginfo='Getting cluster (cvm/ahv/oob) list and credentials .')

            res, pe_info = ntx_pm.get_pe_info(frompc=True)
            if not res:     # if cluster not exist, then abort the pm
                raise Exception(pe_info)
            #####################################################################
            #                                                                   #
            #                  Step 3 : Check if ipmi is powered on             #
            #                                                                   #
            #####################################################################
            ntx_pe = PrismElement(task.cluster, logger=logger)

            lg.write_pm_log(loginfo='Checking IPMI connection state.')
            res, oob_state = ntx_pe.check_ipmi_connection(pe_info)
            if not res:
                raise Exception(oob_state)

            lg.write_pm_log(loginfo='All IPMI are connected, proceed.')

            #####################################################################
            #                                                                   #
            #                       Step 4 : power on hosts                     #
            #                                                                   #
            #####################################################################
            lg.write_pm_log(loginfo='Checking AHV power state.')
            _flag_wait_ahv_power_on = False
            for ahv in pe_info:
                lg.write_pm_log(loginfo=f"Checking {ahv['name']} power state via redfish.")
                res, state = ntx_pe.check_host_power_state_via_redfish(
                    oobip=ahv['oob_ip'], username=ahv['oob_admin']['username'], password=ahv['oob_admin']['password'])

                if not res:
                    lg.write_pm_log(loginfo=f'Got error :{state}', logseverity='error')
                    lg.write_pm_log(loginfo='We will ping the AHV to see if it is online.')
                    if test_pinging(ahv['ip']):
                        lg.write_pm_log(loginfo='It is pinging, proceed.')
                        continue
                else:
                    if state == "On":
                        lg.write_pm_log(loginfo=f"{ahv['name']} is on.")
                        continue
                lg.write_pm_log(loginfo=f"AHV {ahv['name']} is off, power it on now. ")
                _flag_wait_ahv_power_on = True
                ntx_pe.power_on_host_via_redfish(
                    oobip=ahv['oob_ip'], username=ahv['oob_admin']['username'], password=ahv['oob_admin']['password'])
            if _flag_wait_ahv_power_on:
                lg.write_pm_log(loginfo="Take a 6-minutes nap after powered on AHV.")
                time.sleep(SETTING.PM["AHV_POWEREDON_SLEEP_TIME"])

            lg.write_pm_log(loginfo='Checking CVM connection state.',)

            res, cvm_state = ntx_pe.check_cvm_connection(pe_info)
            if not res:
                raise Exception(cvm_state)
            lg.write_pm_log(loginfo='All CVM are up.')
            if _flag_wait_ahv_power_on:
                lg.write_pm_log(loginfo="Take a 5-minutes nap after all CVM being pinging.")
                time.sleep(SETTING.PM["CVM_POWEREDON_SLEEP_TIME"])
            #####################################################################
            #                                                                   #
            #                       Step 5 : start the cluster                  #
            #                                                                   #
            #####################################################################
            i, cs = 0, "Off"
            while i < SETTING.PM["START_CLUSTER_LOOP_TIMES"]:
                ntx_pe.start_cluster(
                    pe_info[0]["cvm_ip"],
                    username=pe_info[0]['cvm_nutanix']['username'],
                    password=pe_info[0]['cvm_nutanix']['password'],)       # pass a random CVM ip is good enough
                lg.write_pm_log(
                    loginfo="Cluster start command has been sent, let's take a 2-minutes nap.", logseverity='info')
                time.sleep(SETTING.PM["CLUSTER_START_UP_SLEEP_TIME"])
                res, cluster_state = ntx_pe.get_cluster_status(pe_info[0]["cvm_ip"],
                                                               username=pe_info[0]['cvm_nutanix']['username'],
                                                               password=pe_info[0]['cvm_nutanix']['password'])
                if res:
                    if cluster_state == "On":
                        cs = "On"
                        lg.write_pm_log(loginfo="Cluster is started, proceed.")
                        break
                    if cluster_state == "Off" or cluster_state == "Unknown":
                        lg.write_pm_log(loginfo="Cluster is still not started, let's take a 2-minutes nap again.")
                        time.sleep(SETTING.PM["CLUSTER_START_UP_SLEEP_TIME"])
                else:
                    lg.write_pm_log(
                        loginfo="Got an error when checking cluster state, will take a 2-minutes nap and continue.",
                        logseverity='warning')
                    time.sleep(SETTING.PM["CLUSTER_START_UP_SLEEP_TIME"])
                i += 1
            #####################################################################
            #                                                                   #
            #                  Step 6 : start the workloads                     #
            #                                                                   #
            #####################################################################
            if cs != "On":
                # this indicates the cluster is still not up.
                raise Exception(
                    "We have been waiting for a long time, the cluster was still not running, please manually check.")

            lg.write_pm_log(loginfo="We will start the workloads now.")

            lg.write_pm_log(loginfo="Getting vm list from pe.")
            res, vm_list = ntx_pe.get_vm_list()
            if not res:
                raise Exception("Failed to get the VM list, but cluster is up, so please manually start VMs.")
            lg.write_pm_log(loginfo="Filter the vm list, we don't need cvm or poweredon vms.")

            vm_list = [vm for vm in vm_list if (not vm["is_controller_vm"] and vm['state'] != 'on')]

            lg.write_pm_log(loginfo="Let's check if there is any firewall servers(PL0001/0002), we need to start those first.")    # noqa

            firewall_list = [
                vm for vm in vm_list
                if re.match(SETTING.PM["FIRST_START_SERVER_REGEX"], vm["name"], re.I) and not vm["is_controller_vm"]
            ]

            if firewall_list:
                lg.write_pm_log(
                    loginfo=f"We have {','.join([vm['name'] for vm in firewall_list])}, will start these first.")
                for vm in firewall_list:
                    lg.write_pm_log(loginfo=f"Starting {vm['name']}.")
                    ntx_pe.set_vm_power_state(vm_uuid=vm["uuid"], transition='ON', vm_name=vm["name"])
                lg.write_pm_log(loginfo="Take a 5-minutes nap after powered on firewalls.")
                time.sleep(SETTING.PM["FIREWALL_POWEREDON_SLEEP_TIME"])
            else:
                lg.write_pm_log(loginfo="We don't have powered off firewalls, let's start the VMs.")
            other_vm_list = [
                vm for vm in vm_list
                if (not re.match(SETTING.PM["FIRST_START_SERVER_REGEX"], vm["name"], re.I)) and (not vm["is_controller_vm"])        # noqa
            ]

            for vm in other_vm_list:
                lg.write_pm_log(loginfo=f"Starting {vm['name']}.")
                ntx_pe.set_vm_power_state(vm_uuid=vm["uuid"], transition='ON', vm_name=vm["name"])
            lg.write_pm_log(loginfo="Sent 'start' command to all the VMs, PM is done.")
            task = ModelNTXPMTask.query.filter_by(id=task_id).first()
            task.status = "Done"
            db.session.commit()
    except Exception as e:
        max_length = len(str(e)) if len(str(e)) < 255 else 255
        lg.write_pm_log(loginfo=f'{str(e)[0:max_length-1]}', logseverity='error')
        task = ModelNTXPMTask.query.filter_by(id=task_id).first()
        task.status = "Error"
        db.session.commit()

import asyncio
import functools
import json
import logging
import re
import time
from datetime import datetime
from typing import Dict, <PERSON>, Tuple, Union
from uuid import uuid4

import requests

from business.distributedhosting.nutanix.nutanix import Prism_Central
from collector.collectors.base_collector import BaseCollector
from collector.collectors.modules.api_calls import APICallsVMCollector, with_app_context
from collector.collectors.modules.db_operations import DatabaseOperations
from models.database import db
from models.ntx_models import ModelRetailNutanixVM
from models.ntx_models_wh import ModelWarehouseNutanixVM

# ----------------------
# Exception Classes
# ----------------------
GLOBAL_LOGGER_VM_FILE = None  # Used to get the correct logger for functions and classes outside of VMCollector class


class AuthenticationError(Exception):
    """Exception raised when authentication fails with Prism Element."""


class DataCollectionError(Exception):
    """Exception raised when data collection fails."""


# try / catch wrapper for functions
def try_catch_wrapper(func):
    def handle_error(e, func_name, *args):
        print(f"Error in {func_name}({', '.join(map(str, args))}): {e}")
        import traceback  # pylint: disable=import-outside-toplevel

        traceback.print_exc()
        if func.__annotations__.get("return") is None:
            print(f"W: in {func_name}: No return type specified")
            return None
        if func.__annotations__.get("return") == bool:
            return False
        if func.__annotations__.get("return").__name__ == "Tuple":
            # Get the tuple type arguments from the return annotation
            tuple_args = func.__annotations__.get("return").__args__
            # Create a tuple with default values based on the type arguments
            default_values = []
            for arg_type in tuple_args:
                if arg_type == bool:
                    default_values.append(False)
                elif arg_type == str:
                    default_values.append("")
                elif arg_type == int:
                    default_values.append(0)
                elif arg_type == float:
                    default_values.append(0.0)
                elif arg_type == list:
                    default_values.append([])
                elif arg_type == dict:
                    default_values.append({})
                else:
                    default_values.append(None)
            return tuple(default_values)
        return func.__annotations__.get("return")()

    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            return handle_error(e, func.__name__, *args)

    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            return handle_error(e, func.__name__, *args)

    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    return sync_wrapper


@try_catch_wrapper
async def http_request(func, *args, **kwargs) -> dict:
    res = func(*args, **kwargs)
    if res.status_code != 200:
        raise Exception(f"HTTP request failed: {kwargs['request_url']}: {res.status_code}")
    return res.json()


# ----------------------
# Utility Functions
# ----------------------
def bytes_to_gb(bytes_value: int) -> int:
    """Convert bytes to gigabytes, rounded to the nearest integer."""
    return int(round(bytes_value / (1024**3), 0))


def text_to_json(response: Union[requests.Response, str]) -> dict:
    """Load the response text into a JSON object."""
    logger = GLOBAL_LOGGER_VM_FILE if GLOBAL_LOGGER_VM_FILE else logging.getLogger(__name__)
    content = {}
    try:
        if isinstance(response, requests.Response):
            if response.status_code != 200:
                raise RuntimeError(f"Unexpected status code: {response.status_code}")
            text = response.text
        else:
            text = response
        content = json.loads(text)
    except Exception as e:
        logger.error("ERROR: failed to convert text to json format")
        logger.error(e)

    return content


@try_catch_wrapper
def resolve_vm_category(vm_name: str) -> Tuple[str, str]:
    """Resolve VM category based on name pattern."""
    os, vm_type = "Others", "Others"

    const_cvm_pattern = re.compile(r"^NTX-.*-CVM$")
    const_pc_aos_pattern = re.compile(r".*NXP\d{3}-\d{1}")

    if const_cvm_pattern.match(vm_name.upper()):
        os = "AOS"
        vm_type = "Nutanix Controller VM"
        return os, vm_type

    if const_pc_aos_pattern.match(vm_name.upper()):
        os = "PC AOS"
        vm_type = "Prism Central VM"
        return os, vm_type

    if not '-' in vm_name:
        return os, vm_type

    sub_name = vm_name.split("-", 1)[1]
    if len(sub_name) != 6:
        return os, vm_type

    if sub_name.startswith("NT"):
        os = "Windows"
        if re.match(r"(NT0)(\d{3})", sub_name):
            vm_type = "FSOL"
        elif re.match(r"(NT1)(\d{3})", sub_name):
            vm_type = "Terminal"
        elif re.match(r"(NT2)(\d{3})", sub_name):
            vm_type = "Database"
        elif re.match(r"(NT3)(\d{3})", sub_name):
            vm_type = "Web"
        elif re.match(r"(NT4)(\d{3})", sub_name):
            vm_type = "Application"
        elif re.match(r"(NT5)(\d{3})", sub_name):
            vm_type = "Cluster"
        elif re.match(r"(NT6)(\d{3})", sub_name):
            vm_type = "Infrastructure"
        elif re.match(r"(NT7)(\d{3})", sub_name):
            vm_type = "Hypervisor"
        elif re.match(r"(NT8)(\d{3})", sub_name):
            vm_type = "Local Solution"
        elif re.match(r"(NT9)(\d{3})", sub_name):
            vm_type = "Development"
        return os, vm_type

    if sub_name.startswith("LX"):
        os = "Linux"
        if re.match(r"(LX2000)", sub_name):
            vm_type = "MHS DB"
        elif re.match(r"(LX2010)", sub_name):
            vm_type = "PIP DB"
        elif re.match(r"(LX4000)", sub_name):
            vm_type = "PBR"
        elif re.match(r"(LX4010)", sub_name):
            vm_type = "MHS"
        elif re.match(r"(LX4030)", sub_name):
            vm_type = "PIP"
        elif re.match(r"(LX600)([1-9])", sub_name):
            vm_type = "NSB"
        elif re.match(r"(LX40)([4-7])(0)", sub_name):
            vm_type = "LIP"
        return os, vm_type

    if sub_name.startswith("LC") or sub_name.startswith("PL") or sub_name.startswith("WL"):
        os = "Network Appliance"
        if re.match(r"(LC)\d{4}", sub_name):
            vm_type = "Lancom"
        elif re.match(r"(PL)\d{4}", sub_name):
            vm_type = "Palo Alto"
        elif re.match(r"(WL)\d{4}", sub_name):
            vm_type = "Wireless Controller"
        return os, vm_type

    return os, vm_type


# ----------------------
# Main Collector Class
# ----------------------


class VMCollector(BaseCollector):
    """Collector for VM data.

    This class is responsible for collecting data about VMs from Nutanix Prism Elements,
    including VM details, configuration, and status information.
    """

    def __init__(self, sa=None, facility_type="retail") -> None:
        """Initialize the VMCollector.

        Args:
            sa: Service account credentials (optional)
            facility_type: Type of facility (retail or warehouse)
        """
        super().__init__(sa, facility_type)
        global GLOBAL_LOGGER_VM_FILE
        GLOBAL_LOGGER_VM_FILE = self.logger
        self.api = APICallsVMCollector(logger=self.logger)
        DatabaseOperations(logger=self.logger) # set the correct logger 
        self.warehouse = False
        self.__reset__()

    def __reset__(self) -> None:
        """Reset collector state."""
        self.collect_result = {}
        self.prism_cache = {}

    def collect(self, warehouse: bool = False) -> None:
        """Main collection method that initiates the data collection process.

        Args:
            warehouse: Whether to collect warehouse data (default: False)
        """
        self.logger.title("Starting VMCollector for {}".format("Retail" if not warehouse else "Warehouse"))
        start_time = time.time()

        asyncio.run(self._collect(warehouse))

        elapsed_time = time.time() - start_time
        self.__reset__()
        self.logger.info("VMCollector completed in %.2f seconds", elapsed_time)

    @try_catch_wrapper
    @with_app_context
    async def _collect(self, warehouse: bool = False) -> None:
        """Internal collection method.

        Args:
            warehouse: Whether to collect warehouse data
        """
        self.warehouse = warehouse
        self.prism_cache = await self._get_prisms()

        self.collect_result = {vm["uuid"]: vm for vm in self.prism_cache["vms"] if vm}

        for key in self.collect_result.keys():
            # Reset fields
            reset_fields = {"status": "Unavailable", "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
            self.collect_result[key].update(reset_fields)
            self.collect_result[key].pop("pe_id")
            self.collect_result[key].pop("pe_name")
            self.collect_result[key].pop("pe_fqdn")
            self.collect_result[key].pop("pe_uuid")
            self.collect_result[key].pop("host_name")
            self.collect_result[key].pop("host_uuid")
            self.collect_result[key].pop("description")
            self.collect_result[key].pop("disk")
            self.collect_result[key].pop("memory")
            self.collect_result[key].pop("cpu_core")
            self.collect_result[key].pop("power_state")
            self.collect_result[key].pop("ip")
            self.collect_result[key].pop("is_cvm")
            self.collect_result[key].pop("type")
            self.collect_result[key].pop("last_update")
            self.collect_result[key].pop("boot_type")
            self.collect_result[key].pop("network_vlan")

        task_name = "pc_vm_" + str(uuid4())
        for _, pc_fqdn in self.prism_cache["pcs"]:
            self.processes.add_task(task_name, self.collect_from_prism_central, pc_fqdn)

        await self.processes.execute_asynchronous_tasks()
        _ = self.processes.get_results()[task_name]

        if self.warehouse:
            DatabaseOperations.update_db(
                self.collect_result, ModelWarehouseNutanixVM, ModelWarehouseNutanixVM.uuid, db.session
            )
        else:
            DatabaseOperations.update_db(
                self.collect_result, ModelRetailNutanixVM, ModelRetailNutanixVM.uuid, db.session
            )

    @try_catch_wrapper
    @with_app_context
    async def _get_prisms(self) -> Dict[str, List[Tuple[int, str]]]:
        """Get the list of Prisms from the database.

        Returns:
            pcs: List of Prism Centrals
            pes: List of Prism Elements
        """
        pcs = {}
        pes = {}
        if self.warehouse:
            pcs = DatabaseOperations.get_dh_warehouse_ntx_pc()
            pes = DatabaseOperations.get_dh_warehouse_ntx_pe()
            vms = DatabaseOperations.get_dh_warehouse_ntx_vm()
        else:
            pcs = DatabaseOperations.get_dh_retail_ntx_pc()
            pes = DatabaseOperations.get_dh_retail_ntx_pe()
            vms = DatabaseOperations.get_dh_retail_ntx_vm()

        return {
            "pcs": [(pcs[fqdn]["id"], fqdn) for fqdn in pcs.keys()],
            "pes": [(pe["id"], pe["name"], pe["fqdn"], pe["uuid"]) for pe in pes],
            "vms": vms,
        }

    @try_catch_wrapper
    async def collect_from_prism_central(self, pc_fqdn: str) -> None:
        """Collect data from given Prism Central.

        Args:
            pc_fqdn: FQDN of the Prism Central
        """
        # Initialize Prism Central client
        account = self.api.get_svc_account(pc_fqdn)
        pc_client = Prism_Central(
            pc_fqdn, sa={"username": account["username"], "password": account["secret"]}, logger=self.logger
        )
        pc_client.rest.retry = 1

        vm_data_v1 = await http_request(pc_client.rest.prism_get, request_url="/vms")

        # Get VM data in parallel, filtering out None results
        vm_data_v3 = await self.get_vm_data_v3(pc_client)
        vdisks = await http_request(pc_client.rest.prism_get, request_url="/virtual_disks")
        subnets = await self.get_pc_subnets(pc_client)

        # Process each VM
        for vm in vm_data_v1.get("entities", []):
            vm_uuid = vm["uuid"]
            pe_entry = next((p for p in self.prism_cache["pes"] if p[3] == vm["clusterUuid"]), (None, None, None, vm["clusterUuid"]))
            if not pe_entry:
                self.logger.error("PE not found for VM %s, clusterUuid: %s", vm_uuid, vm['clusterUuid'])
            pe_id, pe_name, pe_fqdn, pe_uuid = pe_entry

            # Resolve VM category
            vm_name = re.sub(r'\.IKEA.*\.COM$', '', vm["vmName"], flags=re.IGNORECASE).upper()
            os, vm_type = resolve_vm_category(vm_name)

            # Create VM record
            vm_record = {
                "name": vm_name,
                "uuid": vm_uuid,
                "pe_name": pe_name,
                "pe_fqdn": pe_fqdn,
                "pe_uuid": pe_uuid,
                "host_name": vm.get("hostName", "NA"),
                "host_uuid": vm.get("hostUuid", "NA"),
                "description": (vm.get("description") or "")[:254],
                "disk": self.get_vm_disk_info(vm_uuid, vdisks),
                "memory": bytes_to_gb(vm.get("memoryCapacityInBytes", 0)),
                "cpu_core": vm.get("numVCpus", 0),
                "power_state": vm.get("powerState", "NA"),
                "ip": " ".join(vm.get("ipAddresses", [])),
                "is_cvm": True if vm["controllerVm"] or "NXP00" in vm_name else False,
                "pe_id": pe_id,
                "status": "Available",
                "os": os,
                "type": vm_type,
                "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }

            # Get additional VM data from result of v3 API
            vm_v3_spec = next((v["spec"] for v in vm_data_v3 if v["metadata"]["uuid"] == vm_uuid), {})

            # Get network info
            network_vlans = []
            if vm_v3_spec and "nic_list" in vm_v3_spec.get("resources", {}):
                for nic in vm_v3_spec["resources"]["nic_list"]:
                    nic_uuid = nic.get("subnet_reference", {}).get("uuid")
                    subnet = next((s["status"] for s in subnets if s["metadata"]["uuid"] == nic_uuid), None)
                    if subnet:
                        network_vlans.append(str(subnet.get("resources", {}).get("vlan_id", "NA")))

                additional_info = {
                    "boot_type": (
                        vm_v3_spec.get("resources", {}).get("boot_config", {}).get("boot_type", "Undefined")
                        if vm_v3_spec
                        else "Undefined"
                    ),
                    "network_vlan": ", ".join(network_vlans) if network_vlans else "NA",
                }
                vm_record.update(additional_info)

            self.collect_result[vm_uuid] = vm_record


    async def v3_api_post_to_get_all(self, post_func, endpoint, kind, batch_size=500):
        result = []
        offset = 0
        got = batch_size
        while got == batch_size:
            res = await http_request(
                post_func,
                request_url=endpoint,
                version="v3",
                payload={
                    "kind": kind,
                    "length": batch_size,
                    "offset": offset,
                },
            )
            result.extend(res.get("entities", []))
            got = len(res.get("entities", []))
            offset += got
        return result

    async def get_vm_data_v3(self, pc_client):
        return await self.v3_api_post_to_get_all(pc_client.rest.prism_post, "/vms/list", "vm", 500)

    async def get_pc_subnets(self, pc_client):
        return await self.v3_api_post_to_get_all(pc_client.rest.prism_post, "/subnets/list", "subnet", 500)

    def get_vm_disk_info(self, vm_uuid: str, disk_data: dict) -> str:
        vdisks = [vdisk for vdisk in disk_data["entities"] if vdisk["attachedVmUuid"] == vm_uuid]
        return ", ".join(
            [f"{vdisk['diskAddress']}:{bytes_to_gb(vdisk['diskCapacityInBytes'])} GiB" for vdisk in vdisks]
        )

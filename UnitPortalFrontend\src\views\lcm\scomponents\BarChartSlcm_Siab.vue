<template>
  <div :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') 
import resize from './mixins/resize'
import { GetPCPECorrespondence_Lcm} from '@/api/nutanix'

const animationDuration = 6000

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '350px',
    },
    version: {
      type: String,
      default: '*******',
    },
  },
  data() {
    return {
      chart: null,
      categorylist: [],
      aosVersionList: [],
      totalversionlist: [],
      completedversionlist: []
    }
  },
  mounted() {
    this.getCorrespondence_Lcm()
    // this.$nextTick(() => {
    //   this.initChart()
    // })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    getCorrespondence_Lcm(){
      GetPCPECorrespondence_Lcm(this.$store.getters.token).then(response => {
        console.log(response)
        console.log("barchart")
        console.log(this.version)
        if (response.status == 200){
          this.pes_list = response.data.filter(item => item.pc !== "ssp-ppe-ntx.ikea.com" && item.pc !== "ssp-russia-ntx.ikea.com" && item.pc !== "ssp-dhd2-ntx.ikead2.com" && item.pc !== "ssp-dt-ntx.ikeadt.com")
          console.log(this.pes_list)
          
          if (!Array.isArray(this.pes_list)) {
            console.error("data type error");
            return;
          }
          const versionCount = {};
          this.pes_list.forEach(pcEntry => {
            if (!pcEntry.pe || !Array.isArray(pcEntry.pe)) {
              console.warn("pcEntry is missing pe property or it is not an array", pcEntry);
              return;
            }
            this.categorylist.push(pcEntry.pc.split('-')[1].toUpperCase(),);
            let aosCount = 0; 
            let totalCount = 0; 
            pcEntry.pe.forEach(countryEntry => {
              const names = countryEntry.name;
              totalCount += names.length; 
              aosCount += names.filter(item => item.aos_version === this.version).length;
            });
            this.totalversionlist.push(totalCount);
            this.completedversionlist.push(aosCount);
          });
          this.initChart()
        } else {
          console.error("Unexpected response status:", response.status);
        }
      }).catch(error => {
        console.error("Error fetching correspondence data:", error);
      });
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      
      const remainingValues = this.totalversionlist.map((total, index) => total - this.completedversionlist[index]);
      console.log(remainingValues);
      console.log(this.totalversionlist);
      console.log(this.completedversionlist);
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          top: 10,
          left: '2%',
          right: '2%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.categorylist,
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: 'Completed',
            type: 'bar',
            stack: 'progress',
            barWidth: '60%',
            data: this.completedversionlist,
            itemStyle: {
              color: '#00BFFF', 
            },
            animationDuration,
          },
          {
            name: 'Remaining',
            type: 'bar',
            stack: 'progress',
            barWidth: '60%',
            data: remainingValues,
            itemStyle: {
              color: '#D3D3D3', 
            },
            animationDuration,
          },
        ],
        xAxis: [
          {
            type: 'category',
            data: this.categorylist,
            axisTick: {
              alignWithLabel: true,
            },
          },
          {
            type: 'category',
            data: this.categorylist,
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        ],
      })
    },
  },
}
</script>

<style scoped>
.chart {
  height: 100%;
}
</style>